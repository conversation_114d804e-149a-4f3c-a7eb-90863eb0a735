package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SynchronizationReadSqlArg {

    /**
     * 上游企业id
     */
    private String upStreamTenantId;

    /**
     * 下游企业id
     */
    private String downStreamTenantId;

    /**
     * 下游路由
     */
    private RouterInfo routerInfo;

    /**
     * 统计图图表Id
     */
    private String viewId;

    /**
     * 统计图版本号
     */
    private String version;

    /**
     * 同步的action_date时间节点
     */
    private String actionDate;

    /**
     * 同步的时间节点
     */
    private Long timeStamp;

    /**
     * 上次同步时间节点
     */
    private Long lastTimeStamp;

    /**
     * 同步的hash_code
     */
    private String aggDataHashCode;

    /**
     * 经销商id
     */
    private String objectId;

    /**
     * 待同步字段
     */
    private List<String> fieldList;

    /**
     * 待同步字段上下游槽位映射
     */
    private Map<String, String> downToUpMap;

    /**
     * 批次号  todo 改成驼峰式写法
     */
    private long biSysBatchId;

    /**
     * 是否是全量同步
     */
    private boolean isFullSynchronization;

    /**
     * 策略id
     */
    private String policyId;

    public static SynchronizationReadSqlArg getSynchronizationReadSqlArg(AggDataSyncInfoDo aggDataSyncInfoDo,
                                                                         List<String> fieldList,
                                                                         Map<String, String> downToUpMap,
                                                                         String upStreamTenantId,
                                                                         RouterInfo routerInfo,
                                                                         String objectId,
                                                                         boolean isFullSynchronization,
                                                                         long currentTimeMillis,
                                                                         long biSysBatchId,
                                                                         String policyId) {
        return SynchronizationReadSqlArg.builder()
                                        .upStreamTenantId(upStreamTenantId)
                                        .downStreamTenantId(aggDataSyncInfoDo.getTenantId())
                                        .routerInfo(routerInfo)
                                        .viewId(aggDataSyncInfoDo.getViewId())
                                        .version(String.valueOf(aggDataSyncInfoDo.getViewVersion()))
                                        .aggDataHashCode("0")
                                        .timeStamp(currentTimeMillis)
                                        .lastTimeStamp(aggDataSyncInfoDo.getMaxSyncTimeStamp())
                                        .objectId(objectId)
                                        .fieldList(fieldList)
                                        .downToUpMap(downToUpMap)
                                        .biSysBatchId(biSysBatchId)
                                        .isFullSynchronization(isFullSynchronization)
                                        .policyId(policyId)
                                        .build();
    }
}
