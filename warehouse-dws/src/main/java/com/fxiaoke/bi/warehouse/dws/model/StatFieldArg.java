package com.fxiaoke.bi.warehouse.dws.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: zhaomh
 * @Description:
 * @Date: Created in 2024/4/26
 * @Modified By:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatFieldArg {
  private String tenantId;
  /**
   * uniq,sum,count
   */
  private String type;
  /**
   * 0正常,1删除
   */
  private int isDeleted;
  /**
   * downstream_agg|downstream_dim
   */
  private String aggDimType;
  /**
   * agg_downstream_data
   */
  private String objectDescribeApiName;
  /**
   * 客户schemaId:BI_5bcebcdc3060e20001e79977
   */
  private String schemaId;
  /**
   * agg_data/dim_data
   */
  private String dbObjName;
  /**
   * ObjectId
   */
  private String fieldId;
  /**
   * 下游指标名称
   */
  private String fieldName;
  /**
   * 格式化
   * count -> '0', uniq -> ''
   */
  private String formatStr;
  /**
   * ds_下游指标槽位字段
   */
  private String dbFieldName;
  /**
   * count -> Number, uniq -> AggFunc
   */
  private String fieldType;
  /**
   * 0是停用,1是启用
   */
  private int status;
  /**
   * 下游field_id
   */
  private String downstreamFieldId;
  /**
   * 下游view_id
   */
  private String downstreamViewId;
  /**
   * 下游rule_id上的is_null_action_date字段
   */
  private String downstreamIsNullActionDate;
}


