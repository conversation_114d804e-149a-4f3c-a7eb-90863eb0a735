package com.fxiaoke.bi.warehouse.core.config;

import com.github.mybatis.spring.DynamicDataSource;
import com.github.mybatis.spring.ScannerConfigurer;
import com.github.mybatis.tenant.TenantPolicy;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
public class PaasDBConfig implements ResourceLoaderAware {
  private ResourceLoader resourceLoader;

  @Bean("paasDataSource")
  @DependsOn("springUtil")
  public DataSource tenantDataSource(@Qualifier("mybatisPaasTenantPolicy") TenantPolicy tenantPolicy) {
    DynamicDataSource dataSource = new DynamicDataSource();
    dataSource.setConfigName("fs-bi-statistic-db-online");
    dataSource.setTenantPolicy(tenantPolicy);
    dataSource.setConnectionPoolDriver("hikari");
    return dataSource;
  }

  @Bean("paasSqlSessionFactoryBean")
  public SqlSessionFactoryBean sqlSessionFactoryBean(@Qualifier("paasDataSource") DataSource dataSource) throws Exception {
    SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
    sqlSessionFactoryBean.setDataSource(dataSource);
    sqlSessionFactoryBean.setTypeAliasesPackage("com.fxiaoke.bi.warehouse.dws.db.entity,com.fxiaoke.bi.warehouse.dws.transform.db.entity");
    sqlSessionFactoryBean.setConfigLocation(resourceLoader.getResource("classpath:mybatis/mybatis-paas-config.xml"));
//    ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
//    sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath*:mybatis/mapper/*.xml"));
    return sqlSessionFactoryBean;
  }

  @Bean("paasScannerConfigurer")
  public ScannerConfigurer scannerConfigurer() {
    ScannerConfigurer scannerConfigurer = new ScannerConfigurer();
    scannerConfigurer.setBasePackage("com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper");
    scannerConfigurer.setSqlSessionFactoryBeanName("paasSqlSessionFactoryBean");
    return scannerConfigurer;
  }

  @Bean("paasTxManager")
  public DataSourceTransactionManager dataSourceTransactionManager(@Qualifier("paasDataSource") DataSource dataSource) {
    DataSourceTransactionManager txManager = new DataSourceTransactionManager();
    txManager.setDataSource(dataSource);
    return txManager;
  }

  @Override
  public void setResourceLoader(ResourceLoader resourceLoader) {
    this.resourceLoader = resourceLoader;
  }
}
