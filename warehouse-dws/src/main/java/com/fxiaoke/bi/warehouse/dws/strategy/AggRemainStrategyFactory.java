package com.fxiaoke.bi.warehouse.dws.strategy;

import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/7/7
 */
@UtilityClass
public class AggRemainStrategyFactory {

  public Map<String, AggRemainDayStrategy> factoryMap = Maps.newConcurrentMap();

  public AggRemainDayStrategy get(String tenantId, String themeApiName) {
    return factoryMap.get(tenantId + "^" + themeApiName);
  }

  public void register(String key, AggRemainDayStrategy strategy) {
    factoryMap.put(key, strategy);
  }

  public String minActionDate(String tenantId, String themeApiName,String timeZone) {
    AggRemainDayStrategy strategy = factoryMap.get(tenantId + "^" + themeApiName);
    if (strategy == null) {
      return Constants.ACTION_DATE_EMPTY;
    } else {
      return strategy.minActionDate(tenantId, themeApiName,timeZone);
    }
  }

  public Map<String, String> allMinActionDate(String timeZone) {
    Map<String, String> result = Maps.newHashMap();
    factoryMap.forEach((k, v) -> {
      List<String> tenantIdAndThemeApiName = Splitter.on('^').splitToList(k);
      result.put(k, v.minActionDate(tenantIdAndThemeApiName.get(0), tenantIdAndThemeApiName.get(1),timeZone));
    });
    return result;
  }
}
