package com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "agg_data_sync_info")
public class AggDataSyncInfoDo {

    /**
     * 租户id
     */
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 统计图图表id
     */
    @Column(name = "view_id")
    private String viewId;

    /**
     * 统计图版本
     */
    @Column(name = "view_version")
    private int viewVersion;

    /**
     * 统计图stat_view_unique_key
     */
    @Column(name = "stat_view_unique_key")
    private String statViewUniqueKey;

    /**
     * 同步策略policyId
     */
    @Column(name = "policy_id")
    private String policyId;

    /**
     * 同步批次
     */
    @Column(name = "batch_num")
    private Long batchNum;

    /**
     * 同步状态
     */
    @Column(name = "status")
    private int status;

    /**
     * 企业图表的agg_data的最后一次时间
     */
    @Column(name = "max_sync_timestamp")
    private Long maxSyncTimeStamp;

    /**
     * 同步信息创建时间
     */
    @Column(name = "timestamp")
    private Date timestamp;

    /**
     * 0 没有删除 1 删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;
}
