package com.fxiaoke.bi.warehouse.core.db;

import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.common.Pair;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.mybatis.tenant.TenantContext;
import com.github.mybatis.tenant.TenantPolicy;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/3/17
 */
@Component
@Qualifier("mybatisPaasTenantPolicy")
public class MybatisPaasTenantPolicy implements TenantPolicy {
  public static FsGrayReleaseBiz gray = FsGrayRelease.getInstance("bi-statistic-online");
  @Resource
  private DbRouterClient dbRouterClient;
  public static String BIZ = "CRM";
  public static String APPLICATION = "fs-bi-paas2gp-transfer";
  public static String DIALECT = "postgresql";

  @Override
  public TenantContext get(String tenantId, boolean readOnly) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, gray.isAllow(
      "use" + "-pgbouncer", tenantId));
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (readOnly && gray.isAllow("use-db-slave", tenantId) && routerInfo.getSlaveUrl() != null) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    if (BooleanUtils.isTrue(routerInfo.getStandalone())) {
      return TenantContext.builder()
                          .id(tenantId)
                          .url(jdbcUrl)
                          .username(routerInfo.getUserName())
                          .password(routerInfo.getPassWord())
                          .schema("sch_" + tenantId)
                          .build();
    }
    return TenantContext.builder()
                        .id(tenantId)
                        .url(jdbcUrl)
                        .username(routerInfo.getUserName())
                        .password(routerInfo.getPassWord())
                        .build();
  }

  public String getPgbouncerURL(String dbURL, boolean master) {
    return dbRouterClient.convertPgBouncerUrl(BIZ, dbURL, master);
  }

  public boolean standalone(String tenantId) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, gray.isAllow(
      "use" + "-pgbouncer", tenantId));
    return null != routerInfo.getStandalone() ? routerInfo.getStandalone() : false;
  }

  /**
   * <p>获取路由路由地址</p>
   */
  public Pair<String, Boolean> getDBURLAndSchema(String tenantID) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT);
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (GrayManager.isAllowByRule("use-db-slave", tenantID) && StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    return Pair.build(jdbcUrl, null != routerInfo.getStandalone() ? routerInfo.getStandalone() : false);
  }

  /**
   * <p>获取pg bouncer url</p>
   */
  public String getPgBouncerURLByURL(String jdbcURL, boolean isMaster) {
    return dbRouterClient.convertPgBouncerUrl(BIZ, jdbcURL, isMaster);
  }

  /**
   * <p>获取路由路由地址</p>
   */
  public String getPgBouncerJdbcURL(String tenantID, boolean readOnly) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT);
    String jdbcUrl = routerInfo.getJdbcUrl();
    boolean isMaster = true;
    if (readOnly && gray.isAllow("use-db-slave", tenantID) && StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
      jdbcUrl = routerInfo.getSlaveUrl();
      isMaster = false;
    }
    if (gray.isAllow("use-pgbouncer", tenantID)) {
      jdbcUrl = getPgBouncerURLByURL(jdbcUrl, isMaster);
    }
    return jdbcUrl;
  }

  /**
   * 判断是否走从库
   *
   * @param tenantId
   * @return
   */
  public static boolean usdDbSlave(String tenantId) {
    return gray.isAllow("use-db-slave", tenantId);
  }
}
