package com.fxiaoke.bi.warehouse.dws.db.mapper;

import com.fxiaoke.bi.warehouse.common.provider.CommonProvider;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBAggInfoDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/8/6
 */
@Mapper
public interface DBAggInfoMapper extends IBatchMapper<DBAggInfoDO>, ITenant<DBAggInfoMapper> {
  @Select("SELECT * FROM db_agg_info WHERE id=#{id}")
  DBAggInfoDO findBySyncId(@Param("id") String id);

  @InsertProvider(type = CommonProvider.class, method = "batchUpsert")
  <T> int upsertDBAggInfo(@Param(CommonProvider.FKey) List<T> list,
                           @Param(CommonProvider.SKey) Set<String> primaryKey);
  @Update("""
     INSERT INTO db_agg_info (id,sync_flows,sync_batch_nums,is_deleted,version,create_time,last_modified_time,status,api_name_ei_map,inc_sys_modified_time_range)
     VALUES (#{obj.id},#{obj.syncFlows},#{obj.syncBatchNums},#{obj.isDeleted},#{obj.version},#{obj.createTime},#{obj.lastModifiedTime},#{obj.status},#{obj.apiNameEiMap},#{obj.incSysModifiedTimeRange})
     ON CONFLICT(id) DO UPDATE SET sync_flows=EXCLUDED.sync_flows,
     sync_batch_nums=EXCLUDED.sync_batch_nums,
     is_deleted=EXCLUDED.is_deleted,
     version=EXCLUDED.version,
     status=EXCLUDED.status,
     last_modified_time=EXCLUDED.last_modified_time,
     api_name_ei_map=EXCLUDED.api_name_ei_map,
     inc_sys_modified_time_range=EXCLUDED.inc_sys_modified_time_range
     where db_agg_info.version=#{oldVersion}
    """)
  int upsertDBAggInfoWithVersion(@Param("obj") DBAggInfoDO dbAggInfoDO,@Param("oldVersion") int oldVersion);

  @Select("update db_agg_info set status=#{status},version=version+1,last_modified_time=#{lastModifiedTime} where " +
    "id=#{id} and version=#{preVersion} RETURNING version")
  Integer updateDBAggInfoStatus(@Param("id") String id,
                                           @Param("status") Integer status,
                                           @Param("lastModifiedTime") long lastModifiedTime,
                                           @Param("preVersion") int version);
}
