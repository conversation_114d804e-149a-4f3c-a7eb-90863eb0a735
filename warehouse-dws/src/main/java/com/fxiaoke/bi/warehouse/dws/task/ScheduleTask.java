package com.fxiaoke.bi.warehouse.dws.task;

import com.fxiaoke.bi.warehouse.dws.service.BackgroundTaskService;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.helper.ConfigHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@EnableScheduling
@Configuration
public class ScheduleTask {
    private boolean run;

    @Resource
    private BackgroundTaskService backgroundTaskService;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
            run = config.getBool("run_explain_task", true);
            String skipProfiles = config.get("skipProfiles", "fstest-gray,foneshare-gray");
            String[] profiles = skipProfiles.split(",");
            if (StringUtils.equalsAny(ConfigHelper.getProcessInfo().getProfile(), profiles)) {
                run = false;
            }
        });
    }

    @Scheduled(cron = "0 0 2 * * ?")
    public void runExplainTask() {
        long start = System.currentTimeMillis();
        if (run) {
            try {
                boolean bOk = backgroundTaskService.explainTask();
                log.info("runExplainTask finish {}, cost:{} ms", bOk, (System.currentTimeMillis() - start));
            } catch (Exception e) {
                log.error("runExplainTask event error", e);
            }
        } else {
            log.warn("trigger runExplainTask has stop!");
        }
    }
}
