package com.fxiaoke.bi.warehouse.ods.dao;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.component.MybatisBITenantPolicy;
import com.fxiaoke.bi.warehouse.common.db.dao.DbTableSyncInfoDao;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.mq.message.DBUpdateMessage;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.SQLUtil;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.ods.args.ResetMaxModifiedTimeArg;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.bean.PGSchema;
import com.fxiaoke.bi.warehouse.ods.bean.PgSysModifiedTimeInfo;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.dao.mapper.BISystemMapper;
import com.fxiaoke.bi.warehouse.ods.dao.mapper.CommonMapper;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.mq.CalculateEventProducer;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.ResultSetMetaData;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PgCommonDao {

  @Autowired
  private BISystemMapper biSystemMapper;
  @Autowired
  private CalculateEventProducer calculateEventProducer;

  @Autowired
  private CommonMapper commonMapper;
  @Resource
  private MybatisBITenantPolicy mybatisBITenantPolicy;
  @Resource
  private DbTableSyncInfoDao dbTableSyncInfoDao;

  private final Cache<String/*tenantId*/, Boolean> schema2publicCache = Caffeine.newBuilder()
                                                                                .maximumSize(3000)
                                                                                .expireAfterWrite(1, TimeUnit.DAYS)
                                                                                .build();
  /**
   * 遍历db_sync_info 发送mq消息通知数据同步
   */
  public int triggerTransfer(int pageSize) {
    int size = 0;
    Page<DBSyncInfo> page = new Page<>(1, pageSize);
    String startId = "0";
    do {
      biSystemMapper.setTenantId("-1").pagination3(page, "id >'" + startId + "' and is_deleted=0", "id");
      if (page.size() == pageSize) {
        for (DBSyncInfo dbSyncInfo : page) {
          this.sendTransferEvent(dbSyncInfo);
          startId = dbSyncInfo.getId();
        }
        size += page.size();
        page = new Page<>(1, pageSize);
      } else {
        for (DBSyncInfo dbSyncInfo : page) {
          this.sendTransferEvent(dbSyncInfo);
        }
        size += page.size();
        startId = null;
      }
    } while (StringUtils.isNotBlank(startId));
    return size;
  }

  /**
   * 发送计算mq消息
   *
   * @param dbSyncInfo
   */
  public void sendTransferEvent(DBSyncInfo dbSyncInfo) {
    String chDBName = CHContext.getDBName(dbSyncInfo.getChDb());
    String pgDBName = CHContext.getDBName(dbSyncInfo.getPgDb());
    Long syncInterval = WarehouseConfig.customSyncInterval.getOrDefault(pgDBName, WarehouseConfig.defaultSyncInterval);
    if (dbSyncInfo.getLastSyncTime() != null &&
      dbSyncInfo.getLastSyncTime() + syncInterval > System.currentTimeMillis()) {
      log.info("this db:{} lastSyncTime is LastSyncTime:{},interval:{}",pgDBName,dbSyncInfo.getLastSyncTime(), syncInterval);
      return;
    }
    String keys = "transfer_" + pgDBName + "_" + dbSyncInfo.getPgSchema() + "_" + chDBName;
    int hash = Math.abs(pgDBName.hashCode() == Integer.MIN_VALUE ? 0 : pgDBName.hashCode());
    TransferEvent transferEvent = TransferEvent.builder()
                                               .dbURL(dbSyncInfo.getPgDb())
                                               .chDbURL(dbSyncInfo.getChDb())
                                               .schema(dbSyncInfo.getPgSchema())
                                               .build();
    String pgSchema = dbSyncInfo.getPgSchema();
    String topic = CHContext.BI_WAREHOUSE_EVENT_TOPIC;
    if ((pgSchema.startsWith("sch_") && GrayManager.isAllowByRule("use_gray_topic_ei", pgSchema.substring(4))) || GrayManager.isAllowByRule("use_gray_topic_pgdb", pgDBName)) {
      topic = CHContext.BI_WAREHOUSE_EVENT_TOPIC_GRAY;
      log.info("this dbSyncInfo is gray topic:{},dbSyncInfo:{}",topic,JSON.toJSONString(dbSyncInfo));
    }
    calculateEventProducer.sendMessage(topic, CHContext.ODS_SYNC_EVENT, keys, JSON.toJSONString(transferEvent), hash);
  }

  /**
   * 发送计算消息通知计算
   *
   * @param dbSyncInfo
   */
  public void sendCalculateEvent(DBSyncInfo dbSyncInfo, int hashCodeSalt, BIAggSyncInfoDO biAggSyncInfoDO) {
    String chBdName = CHContext.getDBName(dbSyncInfo.getChDb());
    String pgDBName = CHContext.getDBName(dbSyncInfo.getPgDb());
    String pgSchema = dbSyncInfo.getPgSchema();
    String keys = "cal_" + pgDBName + "_" + dbSyncInfo.getPgSchema() + "_" + chBdName;
    int chDbHash = Math.abs(dbSyncInfo.getChDb().hashCode() == Integer.MIN_VALUE ? 0 : dbSyncInfo.getChDb().hashCode());
    int pgDBHash = Math.abs(dbSyncInfo.getPgDb().hashCode() == Integer.MIN_VALUE ? 0 : dbSyncInfo.getPgDb().hashCode());
    int hash = chDbHash + (pgDBHash % hashCodeSalt);//再根据pgdb做一个取模过程增加一个有限的散列度
    DBUpdateMessage transferEvent = DBUpdateMessage.builder()
                                                   .id(dbSyncInfo.getId())
                                                   .pgDB(dbSyncInfo.getPgDb())
                                                   .chDB(dbSyncInfo.getChDb())
                                                   .schema(dbSyncInfo.getPgSchema())
                                                   .batchNum(dbSyncInfo.getBatchNum())
                                                   .build();
    if(biAggSyncInfoDO!=null){
      transferEvent.setAggSyncId(biAggSyncInfoDO.getId());
      transferEvent.setDsBatchNum(biAggSyncInfoDO.getBatchNum());
    }
    String topic = CHContext.BI_WAREHOUSE_EVENT_TOPIC;
    if (pgSchema.startsWith("sch_") &&
      GrayManager.isAllowByRule("use_vip_warehouse_event_topic", pgSchema.substring(4))) {
      topic = CHContext.BI_WAREHOUSE_EVENT_TOPIC_VIP;
    } else if ((pgSchema.startsWith("sch_") && GrayManager.isAllowByRule("use_gray_topic_ei", pgSchema.substring(4))) || GrayManager.isAllowByRule("use_gray_topic_pgdb", pgDBName)) {
      topic = CHContext.BI_WAREHOUSE_EVENT_TOPIC_GRAY;
    } else if (GrayManager.isAllowByRule("use_mq_dispatcher", chBdName)) {
      topic = CommonUtils.chDb2Topic(dbSyncInfo.getChDb());
    }
    calculateEventProducer.sendMessage(topic, CHContext.DWS_CALC_EVENT, keys, JSON.toJSONString(transferEvent), hash);
  }

  public int batchUpsertDbSyncInfo(List<DBSyncInfo> dbSyncInfos) {
    return biSystemMapper.setTenantId("-1")
                         .upsertSyncDBInfo(dbSyncInfos, Sets.newHashSet("ch_db", "pg_db", "pg_schema"));
  }

  /**
   * 更新状态
   * @param dbSyncInfo
   * @return
   */
  public int updateDBSyncInfoStatus(DBSyncInfo dbSyncInfo) {
    return biSystemMapper.setTenantId("-1")
                         .updateDbSyncInfoStatus(dbSyncInfo.getId(), dbSyncInfo.getStatus(), dbSyncInfo.getLastModifiedTime(), dbSyncInfo.getLastSyncTime());
  }

  /**
   * 批量删除
   *
   * @param dbSyncIds
   */
  public void batchDeletedDbSyncInfo(List<String> dbSyncIds) {
    int size = this.deleteTableSyncInfoByDbSyncId(dbSyncIds, null);
    log.info("deleteTableSyncInfoByDbSyncId by dbSyncIds size{},dbSyncIds:{}", size, JSON.toJSONString(dbSyncIds));
    int result = biSystemMapper.setTenantId("-1").deleteDbSyncInfoById(dbSyncIds.toArray(new String[0]));
    log.info("deleteDbSyncInfoById by dbSyncIds size{},dbSyncIds:{}", result, JSON.toJSONString(dbSyncIds));
  }

  public int deleteDbSyncInfoById(List<String> dbSyncIds){
    int result = biSystemMapper.setTenantId("-1").deleteDbSyncInfoById(dbSyncIds.toArray(new String[0]));
    log.info("deleteDbSyncInfoById by dbSyncIds size{},dbSyncIds:{}", result, JSON.toJSONString(dbSyncIds));
    return result;
  }

  public void upsertDbSyncInfo(DBSyncInfo dsInfo) {
    String sqlTemplate = """
      insert into db_sync_info (id, ch_db, pg_db, pg_schema, status, batch_num, create_time,
      last_modified_time, is_deleted, last_sync_time, last_merge_agg_time, last_sync_eis)
      values('%s', '%s', '%s', '%s', %d, %d, %d, %d, %d, %d, %d, '%s')
      on conflict (ch_db, pg_db, pg_schema) do update set status = EXCLUDED.status,
      batch_num=EXCLUDED.batch_num, create_time=EXCLUDED.create_time,
      last_modified_time=EXCLUDED.last_modified_time, is_deleted=EXCLUDED.is_deleted,
      last_sync_time=EXCLUDED.last_sync_time,last_merge_agg_time=EXCLUDED.last_merge_agg_time,
      last_sync_eis=EXCLUDED.last_sync_eis;
      """;
    String sql = String.format(sqlTemplate, dsInfo.getId(), dsInfo.getChDb(), dsInfo.getPgDb(), dsInfo.getPgSchema(),
      dsInfo.getStatus(), dsInfo.getBatchNum(), dsInfo.getCreateTime(), dsInfo.getLastModifiedTime(),
      dsInfo.getIsDeleted(), dsInfo.getLastSyncTime(), dsInfo.getLastMergeAggTime(), dsInfo.getLastSyncEis());
    biSystemMapper.setTenantId("-1").execCommonSQL(sql);
  }

  public int updateDbSyncInfo(DBSyncInfo dsInfo) {
    return biSystemMapper.setTenantId("-1").updateDbSyncInfo(dsInfo);
  }

  public int batchUpsertDbTableSyncInfo(List<DbTableSyncInfoDO> dbTableSyncInfos) {
    return biSystemMapper.setTenantId("-1")
                         .upsertSyncDBInfo(dbTableSyncInfos, Sets.newHashSet("db_sync_id", "table_name"));
  }

  /**
   * 批量更新db table syncInfo 数据
   * @param dbTableSyncInfos
   * @return
   */
  public int batchUpdateDbTableSyncInfo(List<DbTableSyncInfoDO> dbTableSyncInfos) {
    return biSystemMapper.setTenantId("-1").batchUpdateDbTableSyncInfo(dbTableSyncInfos);
  }

  public int updateTopologyStatusByChDbUrl(int status, String chDBURl) {
    String sql = String.format("update db_sync_info set status=%d where ch_db='%s' and is_deleted=0", status, chDBURl);
    return biSystemMapper.setTenantId("-1").execCommonSQL(sql);
  }

  public int updateTopologyStatusById(int status, List<String> ids) {
    String inIds = SQLUtil.generateInExpress(ids);
    String sql = String.format("update db_sync_info set status=%d where id %s and is_deleted=0", status, inIds);
    return biSystemMapper.setTenantId("-1").execCommonSQL(sql);
  }

  /**
   * 是否开启增量分区，一般在计算完成后开启防止漏算。
   * @param status 0、1
   * @param ids id
   * @return
   */
  @Transactional(propagation= Propagation.REQUIRED)
  public int allowIncPartition(int status, List<String> ids, String partitionName) {
    String inIds = SQLUtil.generateInExpress(ids);
    String sql = "update %s set %s=%d where id %s and is_deleted=0";
    if (Objects.equals(partitionName, WarehouseConfig.INC_PARTITION_NAME)) {
      sql = String.format(sql, "db_sync_info", "allow_inc_partition", status, inIds);
      if (status == 1) {
        sql = String.format("update db_sync_info set allow_inc_partition=%d where id %s and status in(2,3) and is_deleted=0", status, inIds);
      }
    } else if (Objects.equals(partitionName, WarehouseConfig.CAL_PARTITION_NAME)) {
      sql = String.format(sql, "db_agg_info", "allow_cal_partition", status, inIds);
      if (status == 1) {
        sql = String.format("update db_agg_info set allow_cal_partition=%d where id %s and status in(3) and is_deleted=0", status, inIds);
      }
    } else if (Objects.equals(partitionName, WarehouseConfig.PAAS2BI_FLAG)) {
      sql = String.format(sql, "db_sync_info", "allow_paas2bi_status", status, inIds);
      if (status == 1) {
        sql = String.format("update db_sync_info set allow_paas2bi_status=%d where id %s and allow_inc_partition=1 and is_deleted=0", status, inIds);
      }
    }
    return biSystemMapper.setTenantId("-1").execCommonSQL(sql);
  }

  /**
   * 更新状态
   *
   * @param status
   * @param ids
   * @param casStatus
   * @return
   */
  public int updateDbSyncInfoByIdStatusCAS(int status, List<String> ids, List<Integer> casStatus) {
    String inIds = SQLUtil.generateInExpress(ids);
    String anyStatus = casStatus.stream()
                                .filter(Objects::nonNull)
                                .map(Object::toString)
                                .collect(Collectors.joining(","));
    String sql = String.format("update db_sync_info set status=%d where id %s and status=any(array[%s]) and is_deleted=0", status, inIds, anyStatus);
    return biSystemMapper.setTenantId("-1").execCommonSQL(sql);
  }

  /**
   * 根据chDBURL和status更新merge agg time
   *
   * @param chDBURl
   * @param mergeTime
   * @return
   */
  public int updateAggMergeTime(String chDBURl, long mergeTime) {
    return biSystemMapper.setTenantId("-1")
                         .updateLastMergeAggTimeByStatus(chDBURl, mergeTime, SyncStatusEnum.EXCHANGE_AGG.getStatus());
  }

  /**
   * 重置ch同步元数据信息
   *
   * @param chDBUrl chDBurl
   */
  public void resetCHDbSyncInfo(String chDBUrl) {
    int result = biSystemMapper.setTenantId("-1").resetCHDbSyncInfo(chDBUrl);
    log.info("resetCHDbSyncInfo chDBUrl:{},size:{}", chDBUrl, result);
    List<DBSyncInfo> dbSyncInfos = biSystemMapper.setTenantId("-1").queryDBSyncInfoByChDB(chDBUrl);
    if (CollectionUtils.isNotEmpty(dbSyncInfos)) {
      for (DBSyncInfo dbSyncInfo : dbSyncInfos) {
        int result1 = biSystemMapper.setTenantId("-1").resetCHDbTableSyncInfo(dbSyncInfo.getId());
        log.info("resetCHDbSyncInfo pgDBUrl:{},chDBUrl:{},size:{}", dbSyncInfo.getPgDb(), chDBUrl, result1);
      }
    }
  }

  /**
   * @param pgDB
   * @param pgSchema
   * @return
   */
  public DBSyncInfo queryDBSyncInfo(String chDB, String pgDB, String pgSchema) {
    return biSystemMapper.setTenantId("-1").queryDBSyncInfo(chDB, pgDB, pgSchema);
  }

  /**
   * 批量发送计算消息
   *
   * @param ids
   */
  public void batchSendCalMsgByIds(List<String> ids) {
    List<DBSyncInfo> dbSyncInfos = queryDBSyncInfoById(ids);
    if (CollectionUtils.isNotEmpty(dbSyncInfos)) {
      dbSyncInfos.forEach(dbSyncInfo -> {
        this.sendCalculateEvent(dbSyncInfo, 3,null);
      });
    }
  }

  /**
   * @param ids
   * @return
   */
  public List<DBSyncInfo> queryDBSyncInfoById(List<String> ids) {
    String inSQL = SQLUtil.generateInExpress(ids);
    return biSystemMapper.setTenantId("-1").queryDBSyncInfoById(inSQL);
  }

  /**
   * @param chDB
   * @return
   */
  public List<DBSyncInfo> queryDBSyncInfoByChDB(String chDB) {
    return biSystemMapper.setTenantId("-1").queryDBSyncInfoByChDB(chDB);
  }

  /**
   * 从bi_ch_merge_task获取任务，update  returning方式来控制并发
   * @param chDB
   * @param status
   * @param lastMergeTime
   * @return
   */
  public Integer upsertAndGetCHMergeTaskStatus(String chDB, Integer status, Integer oldStatus,  long lastMergeTime) {
    return biSystemMapper.setTenantId("-1").upsertCHMergeTaskStatus(chDB, status, oldStatus, lastMergeTime);
  }

  public String getLastTimeAndStatus(String chDB) {
    return biSystemMapper.setTenantId("-1").getLastMergeTimeAndStatus(chDB);
  }

  /**
   * 批量删除表同步信息
   * @param dbSyncIds dbSyncIds
   * @param tableNames tableNames
   * @return int
   */
  public int deleteTableSyncInfoByDbSyncId(List<String> dbSyncIds, List<String> tableNames) {
    if (CollectionUtils.isEmpty(dbSyncIds)) {
      return 0;
    }
    String[] tables = null;
    if (CollectionUtils.isNotEmpty(tableNames)) {
      tables = tableNames.toArray(new String[0]);
    }
    return biSystemMapper.setTenantId("-1").deleteTableSyncInfoByDbSyncId(dbSyncIds.toArray(new String[0]), tables);
  }

  /**
   * @param dbSyncId
   * @return
   */
  public List<DbTableSyncInfoDO> queryDbTableSyncInfos(String dbSyncId, String tableName) {
    if (StringUtils.isNotBlank(tableName)) {
      DbTableSyncInfoDO dbTableSyncInfo = biSystemMapper.setTenantId("-1").queryDbTableSyncInfo(dbSyncId, tableName);
      if (dbTableSyncInfo != null) {
        return Lists.newArrayList(dbTableSyncInfo);
      }
      return null;
    }
    return biSystemMapper.setTenantId("-1").queryDbTableSyncInfos(dbSyncId);
  }

  public String getViewSqlByViewId(String tenantId, String viewId, Integer version){
    return commonMapper.setTenantId(tenantId).queryViewSqlById(tenantId, viewId, version);
  }

  public List<String> getViewsMaxVersion(String tenantId, String filterValues){
    return commonMapper.setTenantId(tenantId).queryViewsMaxVersion(tenantId, filterValues);
  }

  /**
   * 查找大于某个系统修改时间的企业id和最大系统修改时间
   *
   * @param pgSchema
   * @param fromSysModifiedTime
   * @param stopWatch
   * @return
   */
  public PgSysModifiedTimeInfo findMaxSysModifiedTimeByTable(JdbcConnection jdbcConnection,
                                                             PGSchema pgSchema,
                                                             Long fromSysModifiedTime,
                                                             StopWatch stopWatch) throws Exception {
    stopWatch.start("findMaxSysModifiedTimeByTable");
    String queryMaxModifiedSQL = pgSchema.createSQLByModifiedTime(Optional.empty(), fromSysModifiedTime);
    PgSysModifiedTimeInfo.PgSysModifiedTimeInfoBuilder builder = PgSysModifiedTimeInfo.builder();
    try {
      jdbcConnection.query(queryMaxModifiedSQL, rs -> {
        List<String> columns = Lists.newArrayList();
        ResultSetMetaData resultSetMetaData = rs.getMetaData();
        int cc = resultSetMetaData.getColumnCount();
        for (int i = 1; i <= cc; i++) {
          columns.add(resultSetMetaData.getColumnName(i));
        }
        if (rs.next()) {
          for (String column : columns) {
            Object value = rs.getObject(column);
            switch (column) {
              case "ei": {
                builder.tenantId(String.valueOf(value));
                break;
              }
              case "max_sys_modified_time": {
                if (Objects.isNull(value)) {
                  builder.maxModifiedTime(0);
                } else {
                  builder.maxModifiedTime((Long) value);
                }
                break;
              }
            }
          }
        }
      });
    } catch (Exception e) {
      log.error("findMaxSysModifiedTimeByTable error db:{},table:{}", pgSchema.getDb(), pgSchema.getName(), e);
      throw e;
    } finally {
      stopWatch.stop();
    }
    return builder.build();
  }

  /**
   * 重置db table同步元数据
   *
   * @param resetMaxModifiedTimeArg
   */
  public int updateDbTableMaxModifiedTime(ResetMaxModifiedTimeArg resetMaxModifiedTimeArg) {
    if (resetMaxModifiedTimeArg.getMaxModifiedTime() == 0L) {
      log.error("updateDbTableMaxModifiedTime maxModifiedTime is 0,{}", JSON.toJSONString(resetMaxModifiedTimeArg));
      return 0;
    }
    String idInSQL = String.format(" in ('%s')",resetMaxModifiedTimeArg.getDbSyncId());
    List<DBSyncInfo> dbSyncInfos = biSystemMapper.setTenantId("-1").queryDBSyncInfoById(idInSQL);
    DBSyncInfoBO dbSyncInfoCopy = null;
    if (CollectionUtils.isNotEmpty(dbSyncInfos)) {
      DBSyncInfo dbSyncInfo = dbSyncInfos.getFirst();
      dbSyncInfoCopy = DBSyncInfoBO.createInstanceOf(dbSyncInfo);
    }
    if (dbSyncInfoCopy == null) {
      log.error("dbSyncInfoCopy is null {}", JSON.toJSONString(resetMaxModifiedTimeArg));
      return 0;
    }
    String pgDbName = CommonUtils.getDBName(dbSyncInfoCopy.getPgDb());
    List<String> tables = resetMaxModifiedTimeArg.getTables();
    List<DbTableSyncInfoDO> dbTableSyncInfos;
    if (CollectionUtils.isNotEmpty(tables)) {
      if (GrayManager.isAllowByRule("query_from_tenant_db", String.format("%s^%s", pgDbName, dbSyncInfoCopy.getPgSchema()))) {
        dbTableSyncInfos = dbTableSyncInfoDao.queryDbTableSyncInfos(dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getPgSchema(), dbSyncInfoCopy.getId(), tables);
      } else {
        dbTableSyncInfos = biSystemMapper.setTenantId("-1")
                                         .queryDbTableSyncInfos(resetMaxModifiedTimeArg.getDbSyncId());
      }
    } else {
      if (GrayManager.isAllowByRule("query_from_tenant_db", String.format("%s^%s", pgDbName, dbSyncInfoCopy.getPgSchema()))) {
        dbTableSyncInfos = dbTableSyncInfoDao.queryDBSyncInfoBySyncId(dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getPgSchema(), dbSyncInfoCopy.getId());
      } else {
        dbTableSyncInfos = this.queryTableSyncInfoWithOutApiNameEI(dbSyncInfoCopy.getId());
      }
    }
    int result=0;
    if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
      for (DbTableSyncInfoDO dbTableSyncInfo : dbTableSyncInfos) {
        Long maxSysModifiedTime = dbTableSyncInfo.getMaxSysModifiedTime();
        if (maxSysModifiedTime == null || maxSysModifiedTime == 0L) {
          continue;
        }
        if (String.valueOf(maxSysModifiedTime).length() > 13) { //说明存储的是微秒
          maxSysModifiedTime = resetMaxModifiedTimeArg.getMaxModifiedTime() * 1000;
        } else {
          maxSysModifiedTime = resetMaxModifiedTimeArg.getMaxModifiedTime();
        }
        int size=0;
        if(Objects.equals(dbSyncInfoCopy.getStatus(), SyncStatusEnum.SYNC_ABLE.getStatus()) || Objects.equals(dbTableSyncInfo.getBatchNum(), dbSyncInfoCopy.getBatchNum())){
          size = dbTableSyncInfoDao.batchUpdateMaxSysModifiedTimeByTableName(dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getPgSchema(), dbTableSyncInfo.getId(),maxSysModifiedTime,dbTableSyncInfo.getTableName());
          if (!GrayManager.isAllowByRule("skip_update_sys_db", pgDbName)) {
            size = biSystemMapper.setTenantId("-1").updateCHDbTableMaxModifiedTime(dbTableSyncInfo.getId(), maxSysModifiedTime);
          }
        }
        log.info("updateCHDbTableMaxModifiedTime dbSyncId:{},table:{},maxModifiedTime:{},size:{}", resetMaxModifiedTimeArg.getDbSyncId(), dbTableSyncInfo.getTableName(), maxSysModifiedTime, size);
        result+=size;
      }
    }
    return result;
  }

  /**
   *  反查所有表同步信息不包含 api_name_ei_map 减少占用内存
   * @param dbSyncId db同步id
   * @return 表同步信息集合
   */
  public List<DbTableSyncInfoDO> queryTableSyncInfoWithOutApiNameEI(String dbSyncId){
    return commonMapper.setTenantId("-1").queryTableSyncInfoWithOutApiNameEI(dbSyncId);
  }

  /**
   * 反查所有表同步信息不包含转成Map类型 api_name_ei_map 减少占用内存
   * @param dbSyncId db同步id
   * @return
   */
  public Map<String, DbTableSyncInfoDO> queryDbTableSyncInfoMap(String dbSyncId) {
    List<DbTableSyncInfoDO> dbTableSyncInfos = this.queryTableSyncInfoWithOutApiNameEI(dbSyncId);
    Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = Maps.newConcurrentMap();
    if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
      dbTableSyncInfos.forEach(dbTableSyncInfo -> dbTableSyncInfoMap.put(dbTableSyncInfo.getTableName(), dbTableSyncInfo));
    }
    return dbTableSyncInfoMap;
  }
}
