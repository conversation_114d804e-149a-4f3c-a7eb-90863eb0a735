package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * 表变化引起统计图变化的关系
 */
@Slf4j
@Data
@AllArgsConstructor
public class TopologyTableMonitor {
  /**
   * 租户ID
   */
  private final String tenantId;
  /**
   * 统计图ID
   */
  private final String viewId;
  /**
   * 统计图的计算状态
   */
  private final int status;
  /**
   * 统计图的统计列
   */
  private final List<TopologyTableAggRuleMonitor> statRuleMonitorList;
  /**
   * 统计图版本
   */
  private final int version;
  /**
   * 图类型
   */
  private final int source;
  /**
   * 最近一次计算批次
   */
  private final long batchNum;
  /**
   * 统计图唯一标识
   */
  private final String statViewUniqueKey;
  /**
   * 统计规则
   */
  private final List<TopologyTableAggRule> statRuleList;
  /**
   * 是否要更新statRuleList字段信息
   */
  private final boolean isUpdateStatRule;
  /**
   * 该图是否包含下游指标,用于计算最后更新时间,
   * 如果包含下游指标则以下游指标的最后更新时间为准
   * 是否是1+N的图
   */
  private final boolean isIncludeDSRule;
  /**
   * 维度字段排序后拼接的字符串
   */
  private final String dimFieldsStr;
  /**
   * ch db名称
   */
  private final String database;
  /**
   * topology table
   */
  private final TopologyTable topologyTable;
  /**
   * 判断DB的更新事件是否引起统计图的增量计算列
   *
   * @param objectDescribeApiNameSet 变化的对象
   * @return 需要计算的列
   */
  public List<TopologyTableAggRuleMonitor> needComputeColumn(Set<String> objectDescribeApiNameSet) {
    List<TopologyTableAggRuleMonitor> result = Lists.newArrayList();
    for (TopologyTableAggRuleMonitor rptViewColumnMonitor : statRuleMonitorList) {
      if (TopologyTableStatus.Prepared.getValue() == status ||
        rptViewColumnMonitor.needCompute(objectDescribeApiNameSet)) {
        log.info("needComputeColumn tenantId:{},viewId:{},status:{}", tenantId, viewId, status);
        result.add(rptViewColumnMonitor);
      }
    }
    return result;
  }

  /**
   * 获取该图中所有指标的表集合
   * @return
   */
  public Set<String> findAllEffectTables() {
    Set<String> tableNames = Sets.newHashSet();
    if (CollectionUtils.isNotEmpty(this.statRuleMonitorList)) {
      this.statRuleMonitorList.forEach(ruleMonitor -> {
        if (ruleMonitor.getEffectTables() != null) {
          tableNames.addAll(ruleMonitor.getEffectTables());
        }
      });
    }
    return tableNames;
  }

  /**
   *
   * @return
   */
  public String[] findAllStatFields() {
    if (CollectionUtils.isNotEmpty(this.statRuleMonitorList)) {
      return this.statRuleMonitorList.stream().map(TopologyTableAggRuleMonitor::getFieldId).toArray(String[]::new);
    }
    return null;
  }

  public String toJSONString() {
    return JSON.toJSONString(this);
  }
}
