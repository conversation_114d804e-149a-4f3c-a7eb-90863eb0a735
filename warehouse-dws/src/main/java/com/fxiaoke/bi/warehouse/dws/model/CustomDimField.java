package com.fxiaoke.bi.warehouse.dws.model;

import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zzh
 * @createTime : [2024/6/28 11:38]
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomDimField {

  private String fieldId;

  private String dimensionId;

  private String dimFormula;

  private String type;

  private String dimensionConfigString;

  private boolean isSingle;

  private String fieldType;

  private PGColumnType pgColumnType;
}
