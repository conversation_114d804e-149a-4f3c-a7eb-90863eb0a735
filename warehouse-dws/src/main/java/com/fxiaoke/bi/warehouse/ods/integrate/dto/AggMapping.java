package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 上游指标对应的指标描述
 */
@Data
@Builder
@NoArgsConstructor(staticName = "empty")
@AllArgsConstructor(staticName = "of")
public class AggMapping {

    /**
     * 上游指标field_id stat_field field_id
     */
    private String upFieldId;

    /**
     * 上游指标名称 stat_field field_name
     */
    private String upFieldName;

    /**
     * 下游指标field_id stat_field field_id
     */
    private String downFieldId;

    /**
     * 下游指标field_name stat_field field_name
     */
    private String downFieldName;

    /**
     * 下游指标的槽位类型
     */
    private String aggType;
}
