package com.fxiaoke.bi.warehouse.ods.entity;

import lombok.Data;

import java.util.Map;

/**
 * 文档数据
 */
@Data
public class BizLog implements Comparable<BizLog>{
  private final Map<String, Object> values;

  public BizLog(Map<String, Object> values) {
    this.values = values;
  }

  public Object getValue(String name) {
    return values.get(name);
  }

  public void put(String name, Object value) {
    values.put(name, value);
  }

  @Override
  public int compareTo(BizLog o) {
    return 0;
  }
}