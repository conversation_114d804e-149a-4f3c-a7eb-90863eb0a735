package com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.mapper;

import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.BiDataSyncPolicyLogDo;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface BiDataSyncPolicyLogMapper extends ITenant<BiDataSyncPolicyLogMapper> {

    /**
     * 插入同步日志
     */
    @Insert("INSERT INTO bi_data_sync_policy_log(id, tenant_id, source_tenant_id, policy_id, log_type, msg, is_deleted, timestamp) VALUES (#{id}, #{tenantId}, #{sourceTenantId}, #{policyId}, #{logType}, #{msg}, #{isDeleted}, #{timestamp})")
    int insert(BiDataSyncPolicyLogDo biDataSyncPolicyLogDo);
}
