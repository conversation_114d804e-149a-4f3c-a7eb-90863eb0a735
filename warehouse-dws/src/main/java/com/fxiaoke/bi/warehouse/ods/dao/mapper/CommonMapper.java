package com.fxiaoke.bi.warehouse.ods.dao.mapper;

import com.fxiaoke.bi.warehouse.ods.bean.DBColumnType;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommonMapper extends ITenant<CommonMapper> {

  @Select("select ic.ordinal_position,ic.column_name,ic.data_type,ic.udt_name,ic.is_nullable, " +
    " ic.character_maximum_length,ic.numeric_precision,ic.numeric_scale,ic.datetime_precision," +
    " format_type(a.atttypid,a.atttypmod) as format_type,ic.column_default " +
    " from information_schema.columns ic,pg_class c, pg_attribute a ,pg_namespace pn" +
    " where ic.table_name=c.relname and ic.column_name=a.attname  AND a.attrelid = c.oid and c.relnamespace=pn.oid " +
    " and pn.nspname=#{schema} and ic.table_schema=#{schema} and ic.table_name =#{tableName} order by ic.ordinal_position asc")
  List<DBColumnType> findAllPgTypeByTable(@Param("tableName") String tableName, @Param("schema") String schema);

  @Select("""
    select b.view_sql from (
    select bmtt.view_sql,case when bmttm.version is null then 0 else bmttm.version end as version
    from bi_mt_topology_table bmtt left join bi_mt_topology_table_merge bmttm on bmtt.tenant_id=bmttm.tenant_id and bmtt.stat_view_unique_key=bmttm.id AND bmttm.is_deleted=0
     where bmtt.tenant_id=#{tenantId} and bmtt.stat_view_unique_key=#{viewId} and bmtt.is_deleted=0) as b where b.version=#{version} limit 1
    """)
  String queryViewSqlById(@Param("tenantId") String tenantId,  @Param("viewId") String viewId, @Param("version") Integer version);

  /**
   * @param tenantId
   * @param filterValues
   * @return
   */
  @Select("SELECT CASE WHEN bmtt.source in(1,2) THEN bmtt.source_id ELSE bmtt.stat_view_unique_key END || ',' || CASE WHEN bmtt.source in(1,2) THEN bmtt.version ELSE COALESCE(bmttm.version,0) END \n" +
          "FROM bi_mt_topology_table bmtt\n" +
          "LEFT JOIN bi_mt_topology_table_merge bmttm\n" +
          "     ON bmtt.stat_view_unique_key = bmttm.id AND bmtt.tenant_id=bmttm.tenant_id AND bmttm.is_deleted=0\n" +
          "WHERE (bmtt.tenant_id, CASE WHEN bmtt.source in(1,2) THEN bmtt.source_id ELSE bmtt.stat_view_unique_key END, CASE WHEN bmtt.source in(1,2) THEN bmtt.version ELSE COALESCE(bmttm.version,0) END) in (${filterValues})\n" +
          "  AND bmtt.status IN (0, 1)\n" +
          "  AND bmtt.is_deleted = 0;")
  List<String> queryViewsMaxVersion(@Param("tenantId") String tenantId, @Param("filterValues") String filterValues);

  /**
   * 反查所有表同步信息不包含 api_name_ei_map 减少占用内存
   * @param dbSyncId db同步id
   * @return 表同步信息集合
   */
  @Select("""
    SELECT id,db_sync_id,table_name,max_sys_modified_time,last_sync_time,create_time,last_modified_time,is_deleted,batch_num,status
     FROM db_table_sync_info where db_sync_id=#{db_sync_id} and is_deleted=0
    """)
  List<DbTableSyncInfoDO> queryTableSyncInfoWithOutApiNameEI(@Param("db_sync_id") String dbSyncId);
}
