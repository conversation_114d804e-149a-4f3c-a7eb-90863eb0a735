package com.fxiaoke.bi.warehouse.dws.utils;

/**
 * @Author:jief
 * @Date:2023/6/13
 */
public class InvalidRuleException extends Exception {

  public InvalidRuleException() {
    super();
  }

  public InvalidRuleException(String message) {
    super(message);
  }

  public InvalidRuleException(String message, Throwable cause) {
    super(message, cause);
  }

  public InvalidRuleException(Throwable cause) {
    super(cause);
  }

  protected InvalidRuleException(String message,
                                 Throwable cause,
                                 boolean enableSuppression,
                                 boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
  }
}
