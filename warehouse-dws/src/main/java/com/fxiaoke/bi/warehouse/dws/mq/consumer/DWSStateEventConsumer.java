package com.fxiaoke.bi.warehouse.dws.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.dws.model.AggRuleChangedEvent;
import com.fxiaoke.bi.warehouse.dws.model.DimRefreshEvent;
import com.fxiaoke.bi.warehouse.dws.mq.producer.StatViewEventProducer;
import com.fxiaoke.bi.warehouse.dws.transform.impl.OldStatViewTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/1/5
 */
@Slf4j
@Component
public class DWSStateEventConsumer implements MessageListenerConcurrently, ApplicationListener<ContextRefreshedEvent> {
  private AutoConfMQPushConsumer consumer;
  @Resource
  private OldStatViewTopologyTransformer oldStatViewTopologyTransformer;
  @Resource(name = "mybatisTenantPolicy")
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Resource
  private StatViewEventProducer statViewEventProducer;

  @PostConstruct
  public void init() {
    consumer = new AutoConfMQPushConsumer("fs-bi-warehouse", "DWSStatConsumer", this);
  }

  @Override
  public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
    Set<AggRuleChangedEvent> aggEventSet = Sets.newHashSet();
    Set<DimRefreshEvent> dimEventSet = Sets.newHashSet();
    msgs.forEach(msg -> {
      if (Objects.equals("bill_board_Error", msg.getTags())) {
        return;
      }
      String topic = msg.getTopic();
      log.info("get msg topic:{},msgId:{},tag:{},queue:{},msgBody:{}", msg.getTopic(), msg.getMsgId(), msg.getTags(), msg.getQueueId(), new String(msg.getBody(), StandardCharsets.UTF_8));
      if (Objects.equals("dim-refresh-nomon-biz", msg.getTags())) {
        DimRefreshEvent dimRefreshEvent = DimRefreshEvent.parseFromJSON(new String(msg.getBody(), StandardCharsets.UTF_8));
        if (Utils.useGrayTopic(dimRefreshEvent.getTenantId(),mybatisTenantPolicy.getPgDbNameByTenantId(dimRefreshEvent.getTenantId())) && !Objects.equals(topic, CHContext.BI_DIM_SLOW_MESSAGE_GRAY)) {
          msg.setTopic(CHContext.BI_DIM_SLOW_MESSAGE_GRAY);
          statViewEventProducer.sendMessage(msg,msg.getQueueId(),0L);
          log.info("this tenantId:{},is gray to topic:{}", dimRefreshEvent.getTenantId(),CHContext.BI_DIM_SLOW_MESSAGE_GRAY);
          return;
        }
        if (GrayManager.isAllowByRule("use_ch_agg", dimRefreshEvent.getTenantId()) ||
            GrayManager.isAllowByRule("use_ch_agg_pgdb", mybatisTenantPolicy.getPgDbNameByTenantId(dimRefreshEvent.getTenantId()))) {
          dimEventSet.add(dimRefreshEvent);
        }
      } else {
        AggRuleChangedEvent event = JSON.parseObject(StringHelper.toString(msg.getBody()), AggRuleChangedEvent.class);
        if (Utils.useGrayTopic(event.getTenantId(),mybatisTenantPolicy.getPgDbNameByTenantId(event.getTenantId())) && !Objects.equals(topic, CHContext.BI2BI_OFFLINE_GRAY)) {
          msg.setTopic(CHContext.BI2BI_OFFLINE_GRAY);
          statViewEventProducer.sendMessage(msg,msg.getQueueId(),0L);
          log.info("this tenantId:{},is gray to topic:{}", event.getTenantId(),CHContext.BI2BI_OFFLINE_GRAY);
          return;
        }
        event.setTags(msg.getTags());
        if (GrayManager.isAllowByRule("use_ch_agg", event.getTenantId()) ||
            GrayManager.isAllowByRule("use_ch_agg_pgdb", mybatisTenantPolicy.getPgDbNameByTenantId(event.getTenantId()))) {
          aggEventSet.add(event);
        }
      }
    });
    try {
      oldStatViewTopologyTransformer.batchOnChangeAggRule(aggEventSet);
    } catch (Exception e) {
      //todo 是否需要重试策略
      log.error("batchOnChangeAggRule error events:{}", JSON.toJSONString(aggEventSet), e);
    }
    try {
      oldStatViewTopologyTransformer.batchOnChangeTheme(dimEventSet);
    } catch (Exception e) {
      //todo 是否需要重试策略
      log.error("batchOnChangeTheme error dimEvent:{}", JSON.toJSONString(dimEventSet), e);
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }

  @PreDestroy
  public void destroy() {
    if (null != consumer) {
      consumer.shutdown();
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (null == event.getApplicationContext().getParent() && consumer != null) {
      consumer.start();
    }
  }
}
