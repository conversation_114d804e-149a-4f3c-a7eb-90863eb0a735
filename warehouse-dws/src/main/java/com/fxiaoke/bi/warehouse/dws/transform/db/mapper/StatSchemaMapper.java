package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatSchemaDO;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface StatSchemaMapper extends ITenant<StatSchemaMapper> {
  @Select("select * from stat_schema where tenant_id=#{tenantId} and schema_id=#{schemaId} and status=1 and is_deleted=0 ")
  Map<String,Object> findStatSchemaBySchemaId(@Param("tenantId") String tenantId, @Param("schemaId") String schemaId);

  @Select("select * from stat_schema where tenant_id=#{tenantId} and schema_id=#{schemaId} and status=1 and is_deleted=0 limit 1")
  StatSchemaDO queryStatSchemaBySchemaId(@Param("tenantId") String tenantId, @Param("schemaId") String schemaId);

  @Select("select * from stat_schema where tenant_id=#{tenantId} and schema_id=any(array[#{schemaIds}]) and status=1 and is_deleted=0")
  List<StatSchemaDO> batchQueryStatSchemaBySchemaId(@Param("tenantId") String tenantId, @Param("schemaIds") String[] schemaIds);

  @Select("select * from stat_schema where tenant_id=#{tenantId} and schema_en_name=#{schemaEnName} and status=1 and is_deleted=0 limit 1")
  StatSchemaDO findStatSchemaBySchemaName(@Param("tenantId") String tenantId, @Param("schemaEnName") String schemaEnName);

  @Select("select * from stat_schema where tenant_id=#{tenantId} and schema_en_name=#{schemaEnName} and is_deleted=0")
  List<StatSchemaDO> findAllStatSchemaBySchemaName(@Param("tenantId") String tenantId, @Param("schemaEnName") String schemaEnName);
}
