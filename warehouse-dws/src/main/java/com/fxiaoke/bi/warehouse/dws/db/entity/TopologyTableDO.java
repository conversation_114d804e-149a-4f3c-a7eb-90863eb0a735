package com.fxiaoke.bi.warehouse.dws.db.entity;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.bean.TopologyTableAggDownStream;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTable;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.github.mybatis.annotation.DynamicTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "bi_mt_topology_table")
public class TopologyTableDO {
  @Column(name = "id")
  @Id
  private String id;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "source")
  private int source;
  @Column(name = "source_id")
  private String sourceId;
  @Column(name = "api_name")
  private String apiName;
  @Column(name = "timezone")
  private String timezone;
  @Column(name = "common_dims")
  private String commonDims;
  @Column(name = "stat_list_json")
  @DynamicTypeHandler(value = "com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler.StatRuleListTypeHandler")
  private List<TopologyTableAggRule> statRuleList;
  @Column(name = "view_sql")
  private String viewSQL;
  @Column(name = "status")
  private int status;
  @Column(name = "is_deleted")
  private int isDeleted;
  @Column(name = "version")
  private int version;
  @Column(name = "created_by")
  private String createdBy;
  @Column(name = "create_time")
  private long createTime;
  @Column(name = "last_modified_time")
  private long lastModifiedTime;
  @Column(name = "stat_field_location")
  private String statFieldLocation;
  @Column(name = "uniq_field_location")
  private String uniqFieldLocation;
  @Column(name = "pg_detail_sql")
  private String pgDetailSql;
  /**
   * 所有指标受影响的对象集合
   */
  @DynamicTypeHandler(value = "com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler.AggEffectMapTypeHandler")
  @Column(name="agg_effect_api_names")
  private Map<String, Set<String>> aggEffectApiNames;
  /**
   * 计算的批次号
   */
  @Column(name="batch_num")
  private long batchNum;
  /**
   * 基于图中各个表的最新同步时间
   */
  @Column(name="latest_agg_time")
  private long latestAggTime;
  /**
   * 统计图去重标记
   */
  @Column(name="stat_view_unique_key")
  private String statViewUniqueKey;
  /**
   * 所有字段包含启用和停用的和状态{"fieldId":0}
   */
  @Column(name = "all_agg_stat_field")
  private String allAggStatField;
  /**
   * 下游图列信息
   */
  @DynamicTypeHandler(value = "com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler.AggDownStreamHandler")
  @Column(name="agg_downstream_json")
  private TopologyTableAggDownStream aggDownStream;
  @Column(name = "database_id")
  private String databaseId;


  public static TopologyTableDO from(String tenantId,
                                     TopologyTable topologyTable,
                                     int isDelete,
                                     int createdBy,
                                     String viewSQL,
                                     String pgDetailSql,
                                     int status,
                                     String timeZone,
                                     String statViewUniqKey,
                                     String databaseId) {
    return TopologyTableDO.builder()
                          .tenantId(tenantId)
                          .apiName(topologyTable.getApiName())
                          .isDeleted(isDelete)
                          .sourceId(topologyTable.getViewId())
                          .statRuleList(topologyTable.getStatRuleList())
                          .aggEffectApiNames(topologyTable.findAggEffectApiNameMapper())
                          .statFieldLocation(JSON.toJSONString(topologyTable.getStatFieldLocation()))
                          .version(0)
                          .commonDims(JSON.toJSONString(topologyTable.getCommonDimList()))
                          .createdBy(String.valueOf(createdBy))
                          .lastModifiedTime(new Date().getTime())
                          .createTime(new Date().getTime())
                          .viewSQL(viewSQL)
                          .pgDetailSql(pgDetailSql)
                          .timezone(Constants.getTimeZoneOrDefault(timeZone, Constants.DEFAULT_TIME_ZONE))
                          .status(status)
                          .uniqFieldLocation(JSON.toJSONString(topologyTable.getDimUniqFieldMapper()))
                          .source(topologyTable.getSource())
                          .statViewUniqueKey(statViewUniqKey)
                          .allAggStatField(JSON.toJSONString(topologyTable.getAllAggStatFields()))
                          .aggDownStream(topologyTable.getAggDownStream())
                          .databaseId(databaseId)
                          .build();
  }
}
