package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.facishare.bi.license.service.BaseLicenseService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DataSyncLicenseService {

    private static final String MODULE_CODE = "bi_agg_data_sync_app";

    @Autowired
    private BaseLicenseService baseLicenseService;

    public boolean checkoutDataSyncLicense(String tenantId) {
        try {
            return baseLicenseService.judgeModule(tenantId, "1000", Lists.newArrayList(DataSyncLicenseService.MODULE_CODE));
        } catch (Exception e) {
            log.error("checkoutDataSyncLicense error this tenantId is {}", tenantId, e);
            return false;
        }
    }
}
