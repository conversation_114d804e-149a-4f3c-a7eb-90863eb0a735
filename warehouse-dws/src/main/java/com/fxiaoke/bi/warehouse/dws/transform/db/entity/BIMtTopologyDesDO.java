package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
@Table(name="bi_mt_topology_describe")
@Data
public class BIMtTopologyDesDO {
  @Column(name = "topology_describe_id")
  @Id
  private String topologyDescribeId;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "source")
  private Integer source;
  @Column(name = "source_id")
  private String sourceId;
  @Column(name = "data_table")
  private String dataTable;
  @Column(name = "topology_model")
  private String topologyModel;
  @Column(name = "description")
  private String description;
  @Column(name = "timezone")
  private String timezone;
  @Column(name = "last_modified_time")
  private long lastModifiedTime;
  @Column(name = "is_deleted")
  private boolean isDeleted;
  @Column(name = "syslog")
  private String syslog;
}
