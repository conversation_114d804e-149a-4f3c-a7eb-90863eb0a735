package com.fxiaoke.bi.warehouse.dws.service;

import com.fxiaoke.common.StopWatch;
import com.fxiaoke.helper.JoinHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class CHAggDataService {
  @Resource
  private ClickHouseService clickhouseService;

  /**
   * 删除 agg_data 数据
   * @param tenantId 租户id
   * @param viewIds  图id集合
   */
  public void batchDeleteAggData(String tenantId, List<String> viewIds) {
    String in = "";
    if (CollectionUtils.isNotEmpty(viewIds)) {
      in = String.format("AND view_id IN( %s )", JoinHelper.joinSkipNullOrBlank(",", "'", viewIds));
    }
    StopWatch stopWatch = StopWatch.createStarted(tenantId+":"+System.currentTimeMillis(),tenantId + ":agg_data");
    String sql = String.format("DELETE FROM %s WHERE tenant_id='%s' %s ", "agg_data", tenantId, in);
    try {
      clickhouseService.executeSQL(tenantId, sql, 1000 * 60 * 60);
    } catch (Exception e) {
      log.error("executeSQL:{} error", sql, e);
    } finally {
      stopWatch.stop();
    }
    if (clickhouseService.checkCHTableExists(tenantId, "agg_data_swap")) {
      stopWatch.start(tenantId + ":agg_data_swap");
      sql = String.format("DELETE FROM %s WHERE tenant_id='%s' %s ", "agg_data_swap", tenantId, in);
      try {
        clickhouseService.executeSQL(tenantId, sql, 1000 * 60 * 60);
      } catch (Exception e) {
        log.error("executeSQL:{} error", sql, e);
      } finally {
        stopWatch.stop();
      }
    }
    log.info("batchDeleteAggData result:{}",stopWatch.prettyPrint());
  }
}
