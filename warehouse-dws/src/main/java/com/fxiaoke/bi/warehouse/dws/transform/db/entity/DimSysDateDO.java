package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * @Author:jief
 * @Date:2023/9/27
 */
@Data
@Table(name = "dim_sys_date")
public class DimSysDateDO {
  @Column(name = "pk")
  private String pk;
  @Column(name = "f_year")
  private Integer fYear;
  @Column(name = "f_month")
  private Integer fMonth;
  @Column(name = "f_day")
  private String fDay;
  @Column(name = "f_season")
  private String fSeason;
  @Column(name = "f_week")
  private String fWeek;
}
