package com.fxiaoke.bi.warehouse.ods.compare.dao;

import com.fxiaoke.bi.warehouse.ods.compare.arg.ColumnInfo;
import com.fxiaoke.bi.warehouse.ods.compare.util.SqlUtil;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

@Slf4j
@Repository
public class ClickhouseDataDao {

    @Resource
    private CHDataSource chDataSource;

    /**
     * 根据clickHouseDb查询所有表
     */
    public Set<String> queryTableListByDb(String chDbUrl) {
        Set<String> tableList = Sets.newHashSet();
        try {
            try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbUrl)) {
                String db = CHContext.getDBName(chDbUrl);
                jdbcConnection.query(String.format(SqlUtil.queryClickhouseTableName, db), resultSet -> {
                    while (resultSet.next()) {
                        tableList.add(resultSet.getString(1));
                    }
                });
            }
        } catch (Exception e) {
            log.error("queryTableListByDb error chDbUrl:{}", chDbUrl, e);
            throw new RuntimeException(e);
        }
        return tableList;
    }

    /**
     * 根据clickhouseDb查询表的行数
     */
    public Map<String, Long> queryTableRowsByDb(String chDbUrl) {
        Map<String, Long> tableToRowsMap = Maps.newHashMap();
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbUrl)) {
            String db = CHContext.getDBName(chDbUrl);
            jdbcConnection.query(String.format(SqlUtil.queryClickhouseTableRowsPlus, db), resultSet -> {
                while (resultSet.next()) {
                    tableToRowsMap.put(resultSet.getString(1), resultSet.getLong(2));
                }
            });
        } catch (Exception e) {
            log.error("queryTableRowsByDb error chDbUrl:{}", chDbUrl, e);
            throw new RuntimeException(e);
        }
        return tableToRowsMap;
    }

    /**
     * 公共库根据db查询,选择企业租户进行精准查询
     */
    public Map<String, Long> queryTableAccurateRowsByTenantId(String chDbUrl, String tenantId, String table) {
        Map<String, Long> tableAccurateRowsMap = Maps.newHashMap();
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbUrl)) {
            jdbcConnection.query(String.format(SqlUtil.queryClickhouseTableRowsAccurate, table, tenantId), resultSet -> {
                while (resultSet.next()) {
                    tableAccurateRowsMap.put(table, resultSet.getLong(1));
                }
            });
        } catch (Exception e) {
            log.error("queryTableAccurateRowsByTenantId error chDbUrl:{}", chDbUrl, e);
        }
        return tableAccurateRowsMap;
    }

    /**
     * 查询抽样表数量
     */
    public long querySimpleTableRowsByTenantId(String chDbUrl, Function<String, String> function, String tenantId) {
        final long[] simpleTableCount = {0};
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbUrl)) {
            jdbcConnection.query(function.apply(tenantId), resultSet -> {
                if (resultSet.next()) {
                    simpleTableCount[0] = resultSet.getLong(1);
                }
            });
            return simpleTableCount[0];
        } catch (Exception e) {
            log.error("querySimpleTableRowsByTenantId error chDbUrl:{}", chDbUrl, e);
        }
        return simpleTableCount[0];
    }


    /**
     * 根clickhouseDb查询表字段和类型
     */
    public List<ColumnInfo> queryTableColumnInfoList(String chDbUrl, String tableName) {
        List<ColumnInfo> columnInfoList = Lists.newArrayList();
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbUrl)) {
            String db = CHContext.getDBName(chDbUrl);
            jdbcConnection.query(String.format(SqlUtil.queryClickhouseTableColumns, db, tableName), resultSet -> {
                while (resultSet.next()) {
                    ColumnInfo columnInfo = new ColumnInfo(resultSet.getString(1), resultSet.getString(2));
                    columnInfoList.add(columnInfo);
                }
            });
        } catch (Exception e) {
            log.error("queryTableColumnInfoList error chDbUrl:{}", chDbUrl, e);
        }
        return columnInfoList;
    }
}
