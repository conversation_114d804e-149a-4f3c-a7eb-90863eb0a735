package com.fxiaoke.bi.warehouse.ods.compare.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-26
 * @desc ch和pg表的字段类型差异
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComparePgToChColumnResult {

    /**
     * 表名
     */
    private String tableName;

    /**
     * ch表中存在差异的字段,排除ch中额外添加的字段
     */
    private List<String> chDiffColumn;

    /**
     * pg表中存在差异的字段
     */
    private List<String> pgDiffColumn;

    /**
     * ch和pg中字段类型存在不匹配的字段，按照pg同步到ch的规则匹配
     */
    //private List<String> typeDiffColumn;
}
