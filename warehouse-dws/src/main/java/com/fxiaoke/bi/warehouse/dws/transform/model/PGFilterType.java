package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.db.er.ColumnType;
import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.util.DateTimeUtil;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.helper.StringHelper;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.util.List;
import java.util.Objects;

@Slf4j
@Getter
public enum PGFilterType {
  CONTAIN(1, "LIKE", "包含", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if (StringHelper.isNullOrBlank(value1)) {
        return null;
      }
      return columnAlias + " LIKE '%" + value1 + "%'";
    }
  }, NOTCONTAIN(2, "NOT_LIKE", "不包含", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if (StringHelper.isNullOrBlank(value1)) {
        return null;
      }
      return "(" + columnAlias + " IS NULL OR " + columnAlias + " NOT LIKE '%" + value1 + "%')";
    }
  },

  IS(3, "=", "是", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if (FieldType.TRUE_OR_FALSE.equals(fieldType) || FieldType.SELECT_ONE.equals(fieldType)) {
        String s = parseRightIn(value1, columnType, fieldType, uiType, isSingle);
        if (s == null) {
          return null;
        }
        if (s.contains("true")) {
          if (columnType == PGColumnType.Boolean) {
            return columnAlias + " is true";
          } else {
            return columnAlias + " = 'true'";
          }
        } else {
          if (columnType == PGColumnType.Boolean) {
            return columnAlias + " is false";
          } else {
            return columnAlias + " = 'false'";
          }
        }
      }
      return columnAlias + " = '" + value1 + "'";
    }
  }, NOTIS(4, "<>", "不是", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return "(" + columnAlias + " IS NULL OR " + columnAlias + " <> '" + value1 + "')";
    }
  }, BEGINWITH(5, "LIKE", "开始于", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " LIKE '" + value1 + "%'";
    }
  }, ENDWITH(6, "LIKE", "结束于", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " LIKE '%" + value1 + "'";
    }
  }, ISNULL(7, "IS", "为空", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " IS NULL";
    }
  }, ISNOTNULL(8, "IS NOT", "不为空", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " IS NOT NULL";
    }
  }, EQUAL(9, "=", "等于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int, _Decimal -> columnAlias + " = " + value1;
        default -> columnAlias + "::NUMERIC = " + value1;
      };
    }
  }, NOTEQUAL(10, "<>", "不等于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int, _Decimal -> "(" + columnAlias + " IS NULL OR " + columnAlias + " <> " + value1 + ")";
        default -> "(" + columnAlias + " IS NULL OR " + columnAlias + "::NUMERIC <> " + value1 + ")";
      };
    }
  }, GREATERTHAN(11, ">", "大于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int, _Decimal -> columnAlias + " > " + value1;
        default -> columnAlias + "::NUMERIC > " + value1;
      };
    }
  }, LOWERTHAN(12, "<", "小于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int, _Decimal -> columnAlias + " < " + value1;
        default -> columnAlias + "::NUMERIC < " + value1;
      };
    }
  }, GREATEREQUALTHAN(13, ">=", "大于等于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int, _Decimal -> columnAlias + " >= " + value1;
        default -> columnAlias + "::NUMERIC >= " + value1;
      };
    }
  }, LOWEREQUALTHAN(14, "<=", "小于等于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int, _Decimal -> columnAlias + " <= " + value1;
        default -> columnAlias + "::NUMERIC <= " + value1;
      };
    }
  }, ISNULL1(15, "IS", "为空", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " IS NULL";
    }
  }, ISNOTNULL1(16, "IS_NOT", "不为空", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " IS NOT NULL";
    }
  }, EQUAL1(17, "=", "等于", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if (PGColumnType.Date.equals(columnType)) {
        return columnAlias + "='" + Utils.castDateTimeWithTimeZone(value1, timeZone) + "'";
      }
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int, _Decimal -> columnAlias + " = " + dateString2Long(value1, timeZone);
        default -> columnAlias + "::BIGINT = " + dateString2Long(value1, timeZone);
      };
    }
  }, NOTEQUAL1(18, "!=", "不等于", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if ("object_data".equals(table) && ("create_time".equals(column) || "last_modified_time".equals(column))) {
        return columnAlias + "!='" + Utils.castDateTimeWithTimeZone(value1, timeZone) + "'";
      } else {
        return columnAlias + "::BIGINT != " + dateString2Long(value1, timeZone);
      }
    }
  }, SOONERTHAN(19, "<=", "早于", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if ("object_data".equals(table) && ("create_time".equals(column) || "last_modified_time".equals(column))) {
        return columnAlias + " < '" + Utils.castDateTimeWithTimeZone(value1, timeZone) + "'";
      } else {
        return columnAlias + "::BIGINT < " + dateString2Long(value1, timeZone);
      }
    }
  }, LATERTHAN(20, ">=", "晚于", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if ("object_data".equals(table) && ("create_time".equals(column) || "last_modified_time".equals(column))) {
        return columnAlias + ">='" + Utils.castDateTimeWithTimeZone(value1, timeZone) + "'";
      } else {
        return columnAlias + "::BIGINT >=" + dateString2Long(value1, timeZone);
      }
    }
  }, ISNULL2(21, "IS", "为空", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " IS NULL";
    }
  }, ISNOTNULL2(22, "IS_NOT", "不为空", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " IS NOT NULL";
    }
  }, OTHER(23, "DRANGE", "时间段", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if ("object_data".equals(table) && ("create_time".equals(column) || "last_modified_time".equals(column))) {
        return columnAlias + ">='" + Utils.castDateTimeWithTimeZone(value1, timeZone) + "' AND " + columnAlias +
          " <= '" + Utils.castDateTimeWithTimeZone(value2, timeZone) + "'";
      } else {
        return columnAlias + "::BIGINT>=" + dateString2Long(value1, timeZone) + " AND " + columnAlias + "::BIGINT <= " +
          dateString2Long(value2, timeZone);
      }
    }
  },// 此ID前端已经指定，不要变化
  BETWEEN(24, "BETWEEN", "介于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if (StringHelper.isNullOrBlank(value1) || StringHelper.isNullOrBlank(value2)) {
        return null;
      }
      switch (columnType) {
        case Date:
          return columnAlias + ">='" + value1 + "' AND " + columnAlias + " < '" + value2 + "'";
        default:
          return columnAlias + "::NUMERIC>=" + Utils.toNumber(value1) + " AND " + columnAlias + "::NUMERIC <= " +
            Utils.toNumber(value2);
      }
    }
  }, CUSTOM(25, "CUSTOM", "自定义", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return null;
    }
  }, IN(26, "IN", "包含", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      String right = parseRightIn(value1, columnType, fieldType, uiType, isSingle);
      if (null == right) {
        return null;
      }
      if (Constants.ALL.equals(right)) {
        return null;
      } else {
        switch (Objects.requireNonNull(columnType.trans2CHType())) {
          case _int, _Decimal, _String, _Boolean -> {
            if (isSingle) {
              if (right.contains("other")) {
                return "(" + columnAlias + " LIKE 'other%' OR " + columnAlias + " IN " + right + " ) ";
              } else {
                return columnAlias + " IN " + right;
              }
            } else {
              if (right.contains("other")) {
                return "(" + columnAlias + " LIKE '%other%' " + " OR string_to_array(NULLIF(replace(replace(trim(" +
                  columnAlias + ", '{[]}'), '|', ','), '\"', ''), ''), ',') && " + right + " ) ";
              } else {
                return "string_to_array(NULLIF(replace(replace(trim(" + columnAlias +
                  ", '{[]}'), '|', ','), '\"', ''), ''), ',') && " + right;
              }
            }
          }
          default -> {
            if (right.contains("other")) {
              return "(array_to_string(" + columnAlias + ", ',') LIKE '%other%' OR " + columnAlias + " && " + right +
                ")";
            }
            return columnAlias + " && " + right;
          }

        }
      }
    }
  }, NOTIN(27, "NOT IN", "不包含", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      String right = parseRightIn(value1, columnType, fieldType, uiType, isSingle);
      if (right == null) {
        return null;
      }
      if (Constants.ALL.equals(right)) {
        return "1!=2";
      } else {
        switch (Objects.requireNonNull(columnType.trans2CHType())) {
          case _int, _Decimal, _String, _Boolean -> {
            if (isSingle) {
              String result1 = "(" + columnAlias + " IS NULL OR ";
              //处理其他
              if (right.contains("other")) {
                result1 += " (" + columnAlias + " NOT IN " + right + " AND " + columnAlias + " NOT LIKE 'other%'))";
              } else {
                result1 += columnAlias + " NOT IN " + right + ")";
              }
              return result1;
            } else {
              String result2 = "(" + columnAlias + " IS NULL OR ";
              //处理其他, 文本不包含other todo 有可能数据'{"111","333"}'
              if (right.contains("other")) {
                result2 += "( NOT(string_to_array(NULLIF(replace(replace(trim(" + columnAlias +
                  ", '{[]}'), '|', ','), '\"', ''), ''), ',') && " + right + " ) AND " + columnAlias +
                  " NOT LIKE '%other%'))";
              } else {
                result2 += "NOT(string_to_array(NULLIF(replace(replace(trim(" + columnAlias +
                  ", '{[]}'), '|', ','), '\"', ''), ''), ',') && " + right + " ))";
              }
              return result2;
            }
          }
          default -> {
            if (right.contains("other")) {
              String result3 = "(" + columnAlias + " IS NULL OR ";
              result3 += "( NOT(" + columnAlias + " && " + right + ") AND array_to_string(" + columnAlias +
                ",',') NOT LIKE '%other%'))";
              return result3;
            } else {
              String result4 = "(" + columnAlias + " IS NULL OR ";
              result4 += "NOT(" + columnAlias + " && " + right + "))";
              return result4;
            }
          }
        }
      }
    }
  };

  private int id;
  private String operator;
  private String label;
  private String type;

  PGFilterType(int id, String operator, String label, String type) {
    this.id = id;
    this.operator = operator;
    this.label = label;
    this.type = type;
  }

  public String buildBoolSQL(String table,
                             String column,
                             String columnAlias,
                             PGColumnType columnType,
                             String fieldType,
                             boolean isSingle,
                             String value1,
                             String value2,
                             String uiType,
                             String timeZone) {
    return null;
  }

  public Long dateString2Long(String value, String timeZone) {
    if (value == null) {
      return null;
    }
    if (Utils.DATE_PATTERN_1.matcher(value).matches()) {
      return Long.parseLong(value);
    }

    if (value.length() == 10) {
      return Utils.date2Long(Utils.DATE_FORMAT_2, value, timeZone);
    }
    if (value.length() == 19) {
      return Utils.date2Long(Utils.DATE_FORMAT_1, value, timeZone);
    }
    if (value.length() >= 23) {
      ZoneOffset zoneOffset;
      if (StringUtils.isNotBlank(timeZone) && (zoneOffset = DateTimeUtil.getZoneOffsetFromDateTime(value)) != null) {
        int year = Integer.parseInt(value.substring(0, 4));
        int mm = Integer.parseInt(value.substring(5, 7));
        int dd = Integer.parseInt(value.substring(8, 10));
        int hr = Integer.parseInt(value.substring(11, 13));
        int mi = Integer.parseInt(value.substring(14, 16));
        int ss = Integer.parseInt(value.substring(17, 19));
        return LocalDateTime.of(year, mm, dd, hr, mi, ss).toInstant(zoneOffset).toEpochMilli();
      } else {
        return Utils.date2Long(Utils.DATE_FORMAT_3, value, timeZone);
      }
    }
    log.error("time format error:{}", value);
    return null;
  }

  public String parseRightIn(String value1,
                             PGColumnType columnType,
                             String fieldType,
                             String uiType,
                             boolean isSingle) {
    if (StringHelper.isNullOrBlank(value1)) {
      return null;
    }
    if (value1.startsWith("[")) {
      List<String> options = Lists.newArrayList();
      JSONArray objects = JSON.parseArray(value1);
      for (Object object : objects) {
        if (object == null) {
          continue;
        }
        JSONObject optionJSON = (JSONObject) object;
        if (FieldType.EMP_DEPT.contains(fieldType) || FieldType.UI_SELECTION.equalsIgnoreCase(uiType)) {
          options.add(String.valueOf(optionJSON.get("id")));
        } else {
          options.add(optionJSON.getString("optionCode"));
          JSONArray childList = optionJSON.getJSONArray("childList");
          if (childList != null) {
            options.addAll(getAllChildOptionCode(childList));
          }
        }
      }
      if (options.isEmpty()) {
        return null;
      }
      if (columnType.trans2CHType() == ColumnType._ARRAY || !isSingle) {
        return "'{" + JoinHelper.joinSkipNullOrBlank(',', '\"', options) + "}'";
      } else {
        return "(" + JoinHelper.joinSkipNullOrBlank(',', '\'', options) + ")";
      }
    } else {
      JSONObject filterInfo = JSON.parseObject(value1);
      Object allObj = filterInfo.get("all");
      //全部
      if (allObj instanceof JSONArray) {
        if (((JSONArray) allObj).size() > 0) {
          //全部，永远为真，不用拼条件
          return Constants.ALL;
        }
      }
      Object memObj = filterInfo.get("member");
      Object groupObj = filterInfo.get("group");
      Object optObj = MoreObjects.firstNonNull(memObj, groupObj);
      if (optObj instanceof JSONArray) {
        List<String> members = Lists.newArrayList();
        for (Object object : (JSONArray) optObj) {
          members.add(object.toString());
        }
        if (members.isEmpty()) {
          return null;
        }
        if (columnType.trans2CHType() == ColumnType._ARRAY || !isSingle) {
          return "'{" + JoinHelper.joinSkipNullOrBlank(',', '\"', members) + "}'";
        } else {
          return "(" + JoinHelper.joinSkipNullOrBlank(',', '\'', members) + ")";
        }
      }
      log.warn("opt parse failed. {}", value1);
      return Constants.ALL;
    }
  }

  public static PGFilterType parseFromId(int id) {
    switch (id) {
      case 1:
        return CONTAIN;
      case 2:
        return NOTCONTAIN;
      case 3:
        return IS;
      case 4:
        return NOTIS;
      case 5:
        return BEGINWITH;
      case 6:
        return ENDWITH;
      case 7:
        return ISNULL;
      case 8:
        return ISNOTNULL;
      case 9:
        return EQUAL;
      case 10:
        return NOTEQUAL;
      case 11:
        return GREATERTHAN;
      case 12:
        return LOWERTHAN;
      case 13:
        return GREATEREQUALTHAN;
      case 14:
        return LOWEREQUALTHAN;
      case 15:
        return ISNULL1;
      case 16:
        return ISNOTNULL1;
      case 17:
        return EQUAL1;
      case 18:
        return NOTEQUAL1;
      case 19:
        return SOONERTHAN;
      case 20:
        return LATERTHAN;
      case 21:
        return ISNULL2;
      case 22:
        return ISNOTNULL2;
      case 23:
        return OTHER;
      case 24:
        return BETWEEN;
      case 25:
        return CUSTOM;
      case 26:
        return IN;
      case 27:
        return NOTIN;
    }
    throw new IllegalArgumentException(id + " is not FilterType#id");
  }


  public List<String> getAllChildOptionCode(JSONArray childList) {
    List<String> result = Lists.newArrayList();
    childList.forEach(obj -> {
      if (null != obj) {
        JSONObject optionJSON = (JSONObject) obj;
        result.add(optionJSON.getString("optionCode"));
        JSONArray childChildList = optionJSON.getJSONArray("childList");
        if (childChildList != null) {
          result.addAll(getAllChildOptionCode(childChildList));
        }
      }
    });
    return result;
  }
}
