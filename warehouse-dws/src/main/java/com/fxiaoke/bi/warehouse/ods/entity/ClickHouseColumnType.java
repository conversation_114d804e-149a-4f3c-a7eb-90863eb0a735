package com.fxiaoke.bi.warehouse.ods.entity;

import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.context.CHDataType;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.common.Pair;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.helper.NumberHelper;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.jdbc.PgArray;
import org.postgresql.util.PGobject;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/11/2
 */
@Slf4j
public enum ClickHouseColumnType {
  /**
   * 字符串
   */
  STRING {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      if (value == null) {
        return null;
      }
      return String.valueOf(value);
    }
  },
  /**
   * int
   */
  INT {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      Integer integer = NumberHelper.parseInteger(value, " {} is not integer", value);
      return integer;
    }
  },
  /**
   * long
   */
  LONG {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      Long longValue = NumberHelper.parseLong(value, " {} is not long", value);
      if (longValue == null) {
        return null;
      }
      return longValue;
    }
  },
  /**
   * Double
   */
  DOUBLE {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      Double doubleValue = NumberHelper.parseDouble(value, " {} is not double", value);
      if (doubleValue == null) {
        return null;
      }
      if (doubleValue < (-1 * Math.pow(10, (38 - 20))) || doubleValue > (1 * Math.pow(10, (38 - 20)))) {
        log.error("double format error value:{},etlTarget:{},typeName:{}", value, etlTarget, typeName);
        return null;
      }
      return doubleValue;
    }
  }, DECIMAL {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      if (value == null) {
        return null;
      }
      BigDecimal bigDecimal = new BigDecimal(String.valueOf(value));
      double doubleValue = bigDecimal.doubleValue();
      if (doubleValue < (-1 * Math.pow(10, (38 - 20))) || doubleValue > (1 * Math.pow(10, (38 - 20)))) {
        log.error("numeric format error value:{},etlTarget:{},typeName:{}", value, etlTarget, typeName);
        return null;
      }
      return value;
    }
  }, UINT64 {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      if (value == null) {
        return null;
      }
//      BigDecimal bigDecimal = new BigDecimal(String.valueOf(value));
      return value.toString();
    }
  },
  /**
   * boolean
   */
  BOOL {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      if (value == null) {
        return null;
      }
      String stringValue = String.valueOf(value);
      return StringUtils.equalsAny(stringValue, "true", "1", "t");
    }
  },
  /**
   * 日期
   */
  DATE {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      Long longValue = NumberHelper.parseLong(value, " {} is not long", value);
      if (longValue == null) {
        return null;
      }
      if (longValue <= 0) {
        return null;
      }
      return longValue;
    }
  },
  /**
   * //clickhouse 支持将字符串数组元素转成其他类型数组元素
   */
  ARRAY {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      if (value == null) {
        return null;
      }
      if (value instanceof Collection) {
        List<String> values = ((Collection<?>) value).stream()
                                                     .map(ClickHouseColumnType::itemOfArray)
                                                     .filter(Objects::nonNull)
                                                     .collect(Collectors.toList());
        return "[" + JoinHelper.joinSkipNullOrBlank(',', '\'', values) + "]";
      } else if (value instanceof PgArray) {
        try {
          Object[] array = (Object[]) (((PgArray) value).getArray());
          if (array != null) {
            if (typeName.contains(CHDataType.STRING)) {
              List<String> valueList = Arrays.stream(array)
                                             .filter(Objects::nonNull)
                                             .map(v -> String.valueOf(v).replaceAll("'", "\\\\'"))
                                             .toList();
              if (CollectionUtils.isNotEmpty(valueList)) {
                return "[" + JoinHelper.joinSkipNullOrBlank(',', '\'', valueList) + "]";
              }
            }else{
              List<String> valueList = Arrays.stream(array)
                                             .filter(Objects::nonNull)
                                             .map(String::valueOf)
                                             .toList();
              return "[" + JoinHelper.joinSkipNullOrBlank(',', valueList) + "]";
            }
          }
        } catch (Exception e) {
          log.error("PgArray getArray error value:{}:", value, e);
        }
        //Array 字段
        String arrayString = value.toString().replaceAll("[{}]", "");
        if (typeName.contains(CHDataType.STRING)) {
          List<String> values = Splitter.on(",")
                                        .splitToList(arrayString)
                                        .stream()
                                        .map(v -> v.replaceAll("'", "\\\\'"))
                                        .toList();
          return "[" + JoinHelper.joinSkipNullOrBlank(',', '\'', values) + "]";
        } else {
          return "[" + arrayString + "]";
        }
      } else if (value instanceof PGobject) {
        //处理ltree 字段
        List<String> values = Splitter.on(".")
                                      .omitEmptyStrings()
                                      .splitToList(String.valueOf(value))
                                      .stream()
                                      .map(v -> v.replaceAll("'", "\\\\'"))
                                      .toList();
        return "[" + JoinHelper.joinSkipNullOrBlank(',', '\'', values) + "]";
      } else {
        return "['" + itemOfArray(value) + "']";
      }
    }
  },
  POINt{
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      if (value == null) {
        return null;
      }
      String v = String.valueOf(value);
      Matcher matcher = CommonUtils.geographyRex.matcher(v);
      if (matcher.matches()) {
        return Pair.of(matcher.group(1), matcher.group(3));
      }
      matcher = CommonUtils.point.matcher(v);
      if (matcher.matches()) {
        return Pair.of(matcher.group(1), matcher.group(3));
      }
      log.warn("format to point fail value:{}", v);
      return Pair.of("0","0");
    }
  },
  /**
   *
   */
  UNKNOWN {
    @Override
    public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
      if (value == null) {
        return null;
      }
      return String.valueOf(value);
    }
  };

  public Object format(Object value, CHContext.ETLTarget etlTarget, String typeName) {
    return null;
  }

  public static String itemOfArray(Object value) {
    if (value == null) {
      return null;
    }
    String result = String.valueOf(value);
    return result.replace('\'', ' ');
  }

  public static ClickHouseColumnType parseFromTypeName(String typeName) {
    if (typeName.contains("Array")) {
      return ARRAY;
    }
    if (typeName.contains("String")) {
      return STRING;
    }
    if (typeName.contains("Int32")) {
      return INT;
    }
    if (typeName.contains("Int64")) {
      return LONG;
    }
    if (typeName.contains("Int")) {
      return INT;
    }
    if(typeName.contains("Decimal")){
      return DECIMAL;
    }
    if (typeName.contains("Float") || typeName.contains("Double")){
      return DOUBLE;
    }
    if (typeName.contains("Date")) {
      return DATE;
    }
    if (typeName.contains("Boolean") || typeName.contains("Bool")) {
      return BOOL;
    }
    if(typeName.contains("Point")){
      return POINt;
    }
    return UNKNOWN;
  }

}
