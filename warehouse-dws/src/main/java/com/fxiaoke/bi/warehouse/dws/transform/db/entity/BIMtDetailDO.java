package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import com.fxiaoke.bi.warehouse.dws.transform.model.BiMtDetailRule;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "bi_mt_detail")
@Data
public class BIMtDetailDO {
  @Column(name = "detail_id")
  @Id
  private String detailId;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "topology_describe_id")
  private String topologyDescribeId;
  @Column(name = "detail_field")
  private String detailField;
  @Column(name = "field_id")
  private String fieldId;

  public BiMtDetailRule toMtDetailRule() {
    return BiMtDetailRule.builder()
                         .tenantId(tenantId)
                         .detailId(detailId)
                         .topologyDescribeId(topologyDescribeId)
                         .detailField(detailField)
                         .fieldId(fieldId)
                         .build();

  }
}
