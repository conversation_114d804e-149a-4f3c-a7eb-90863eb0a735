package com.fxiaoke.bi.warehouse.core.db.entity;

import lombok.Data;

import javax.persistence.Column;
import java.util.Objects;

/**
 * @Author: DERET
 * @Date: 2018/4/27
 * @Description: 字段表 udf_obj_field
 */
@Data
public class UdfObjFieldDO {
  public static final String  DATE_LEVEL_TYPE="Date";
  /**
   * 字段id
   */
  @Column(name = "field_id")
  private String fieldId;

  /**
   * 字段描述
   */
  @Column(name = "field_name")
  private String fieldName;

  /**
   * 字段ApiName
   */
  @Column(name = "db_field_name")
  private String dbFieldName;

  /**
   * 字段槽位
   */
  @Column(name = "field_location")
  private String fieldLocation;

  /**
   * 字段主类型
   */
  @Column(name = "field_type")
  private String fieldType;

  /**
   * 字段子类型
   */
  @Column(name = "sub_field_type")
  private String subFieldType;

  /**
   * 字段所属对象id
   */
  @Column(name = "obj_id")
  private String objId;

  /**
   * 字段所属对象ApiName
   */
  @Column(name = "db_obj_name")
  private String dbObjName;

  /**
   * 是否为主键字段
   */
  @Column(name = "is_key")
  private int isKey;

  /**
   * 字段是否显示
   */
  @Column(name = "is_show")
  private int isShow;

  /**
   * 是否索引字段
   */
  @Column(name = "is_index")
  private int isIndex;

  /**
   * 格式化字符
   */
  @Column(name = "format_str")
  private String formatStr;

  /**
   * 引用对象
   */
  @Column(name = "ref_obj_name")
  private String refObjName;

  /**
   * 引用对象主键
   */
  @Column(name = "ref_key_field")
  private String refKeyField;

  /**
   * 引用对象主属性字段
   */
  @Column(name = "ref_target_field")
  private String refTargetField;

  /**
   * 链接
   */
  @Column(name = "url_obj")
  private String urlObj;

  /**
   * 枚举
   */
  @Column(name = "enum_name")
  private String enumName;

  /**
   * 计算表达式
   */
  @Column(name = "formula")
  private String formula;

  /**
   * 计算表达式别名
   */
  @Column(name = "formula_name")
  private String formulaName;

  /**
   * ei
   */
  @Column(name = "ei")
  private int ei;

  /**
   * app_id
   */
  @Column(name = "app_id")
  private String appId;

  /**
   * 创建人
   */
  @Column(name = "creator")
  private String creator;

  /**
   * 创建时间
   */
  @Column(name = "create_time")
  private String createTime;

  /**
   * 最后修改人
   */
  @Column(name = "updator")
  private String updator;

  /**
   * 最后修改时间
   */
  @Column(name = "update_time")
  private String updateTime;

  /**
   * 是否删除
   */
  @Column(name = "is_delete")
  private int isDelete;

  /**
   * CRM类型
   */
  @Column(name = "type")
  private String originalType;

  /**
   * 是否预置字段 1: 预置字段 2: 预置自定义字段 0: 自定义字段
   */
  @Column(name = "is_pre")
  private int isPre;

  /**
   * 引用字段
   */
  @Column(name = "relation_key_field")
  private String relationKeyField;

  /**
   * ui类型
   */
  @Column(name = "ui_type")
  private String ui_type;

  /**
   * 是否为单值
   */
  @Column(name = "is_single")
  private int isSingle;

  /**
   * 类型
   */
  @Column(name = "type")
  private String type;

  @Column(name = "crm_obj_name")
  private String crmObjName;

  @Column(name = "crm_field_name")
  private String crmFieldName;

  @Column(name = "quote_type")
  private String quoteType;

  @Column(name = "return_type")
  private String returnType;

  @Column(name = "what_api_name_field")
  private String whatApiNameField;

  @Column(name = "what_id_field")
  private String whatIdField;

  @Column(name = "extend_field_api_name")
  private String extendFieldApiName;

  @Column(name = "relation_table")
  private String relationTable;

  @Column(name = "not_use_multitime_zone")
  private Boolean notUseMultitimeZone;

  @Column(name = "is_unique")
  private Boolean isUnique;
  @Column(name = "enable_multi_lang")
  private Boolean enableMultiLang;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    UdfObjFieldDO field = (UdfObjFieldDO) o;
    return ei == field.ei &&
      Objects.equals(dbFieldName, field.dbFieldName) &&
      Objects.equals(dbObjName, field.dbObjName);
  }

  public boolean isDateLevel(){
    return  Objects.equals(this.fieldType,DATE_LEVEL_TYPE);
  }

  @Override
  public int hashCode() {
    return Objects.hash(dbFieldName, dbObjName, ei);
  }

}