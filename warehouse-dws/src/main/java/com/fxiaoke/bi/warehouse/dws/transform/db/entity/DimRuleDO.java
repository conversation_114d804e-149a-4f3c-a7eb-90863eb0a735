package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "dim_rule")
@Data
public class DimRuleDO {
  @Column(name = "tenant_id")
  private String tenantId;

  @Column(name = "dim_rule_id")
  private String dimRuleId;

  @Column(name = "field_id")
  private String fieldId;

  @Column(name = "theme_api_name")
  private String themeApiName;

  @Column(name = "dim_field")
  private String dimField;

  @Column(name = "dim_field_location")
  private String dimFieldLocation;

  @Column(name = "display_name")
  private String displayName;

  @Column(name = "field_type")
  private String fieldType;

  @Column(name = "format_str")
  private String formatStr;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "creator")
  private String creator;

  @Column(name = "last_modified_time")
  private Date lastModifiedTime;

  @Column(name = "last_modifier")
  private String lastModifier;

  @Column(name = "is_deleted")
  private int isDeleted;

}
