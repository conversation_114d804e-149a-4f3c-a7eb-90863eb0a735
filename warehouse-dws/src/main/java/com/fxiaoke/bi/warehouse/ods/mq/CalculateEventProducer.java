package com.fxiaoke.bi.warehouse.ods.mq;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;

/**
 * @Author:jief
 * @Date:2023/5/24
 */
@Slf4j
@Service
public class CalculateEventProducer {
  @Getter
  private AutoConfMQProducer producer;

  @PostConstruct
  public void init() {
    producer = new AutoConfMQProducer("fs-bi-warehouse", "OBSConsumer");
  }

  /**
   * 发送消息
   * @param topic topic
   * @param tag
   * @param keys
   * @param body
   * @param hash
   */
  public void sendMessage(String topic, String tag, String keys, String body,int hash) {
    Message syncEventMsg = new Message(topic, tag, keys,body.getBytes(StandardCharsets.UTF_8));
    try {
      this.producer.send(syncEventMsg, (mqs, msg1, arg) -> {
        Integer id = (Integer) arg;
        int index = id % mqs.size();
        return mqs.get(index);
      }, hash);
    } catch (Exception e) {
      log.error("send sendTransferEvent msg fail msg:{}", body, e);
    }
  }
  /**
   * 发送mq message
   * @param messageExt
   * @param hash
   */
  public void sendMessage(MessageExt messageExt, int hash) {
    this.sendMessage(messageExt.getTopic(), messageExt.getTags(), messageExt.getKeys(), new String(messageExt.getBody(), StandardCharsets.UTF_8), hash);
  }
  @PreDestroy
  public void destroy() {
    if (producer != null) {
      producer.close();
    }
  }
}
