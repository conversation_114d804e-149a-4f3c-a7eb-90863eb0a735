package com.fxiaoke.bi.warehouse.dws.sqlgenerator;

import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * @Author:jief
 * @Date:2023/10/9
 */
@Slf4j
public class MtTagViewTable {
  /**
   * 创建表表的维度集合
   *
   * @param tenantId     租户id
   * @param standalone   是否schema隔离
   * @param themeApiName 主题对象
   * @return
   */
  public static List<DimRule> createMtTagDimRules(String tenantId,
                                                  boolean standalone,
                                                  String themeApiName,
                                                  List<DisplayField> tagFields) {
    if (CollectionUtils.isNotEmpty(tagFields)) {
      List<DimRule> tagDimRules = Lists.newArrayList();
      String idColumn = Constants.getIdName(standalone, themeApiName);
      JoinRelation joinRelation = new JoinRelation(idColumn, "object_reference", Constants.BI_MT_DATA_TAG_V,
        AggJoinType.LEFT, PGColumnType.String);
      DimFieldRule dimFieldRule = DimFieldRule.builder()
                                              .tenantId(tenantId)
                                              .fieldId("tag_range")
                                              .dbFieldName("tag_range")
                                              .dbObjName("bi_mt_data_tag_v")
                                              .column("tag_range")
                                              .joinRelation(joinRelation)
                                              .columnType(PGColumnType.ARRAY_String)
                                              .fieldType(FieldType.ARRAY)
                                              .isSingle(false)
                                              .displayType(DisplayField.DisplayType.group)
                                              .build();
      tagDimRules.add(DimRule.builder().dimFieldRule(dimFieldRule).build());
      tagFields.forEach(tagField -> {
        DimFieldRule tagFieldRule = DimFieldRule.builder()
                                                .tenantId(tenantId)
                                                .fieldId(tagField.getStatFieldId())
                                                .dbObjName(tagField.getDescribeApiName())
                                                .joinRelation(joinRelation)
                                                .columnType(PGColumnType.ARRAY_String)
                                                .fieldType(FieldType.ARRAY)
                                                .isSingle(false)
                                                .displayType(tagField.getDisPlayType())
                                                .build();
        if (Constants.TAG_FIELD_ID.equals(tagField.getStatFieldId())) {
          tagFieldRule.column = tagField.getDbFieldName();
          tagDimRules.add(DimRule.builder().dimFieldRule(tagFieldRule).build());
        } else if (Constants.TAG_GROUP_FIELD_ID.equals(tagField.getStatFieldId())) {
          tagFieldRule.column = tagField.getDbFieldName();
          tagDimRules.add(DimRule.builder().dimFieldRule(tagFieldRule).build());
        }
      });
      return tagDimRules;
    }
    return null;
  }

  /**
   * 生成标签的维度或筛选器字段
   *
   * @param fieldId
   * @param displayType
   * @return
   */
  public static Optional<DisplayField> createTagDisplayField(String fieldId, DisplayField.DisplayType displayType) {
    if (StringUtils.equalsAny(fieldId, Constants.TAG_FIELD_ID, Constants.TAG_GROUP_FIELD_ID)) {
      DisplayField subTagField = DisplayField.builder()
                                             .statFieldId(fieldId)
                                             .isSingle(0)
                                             .describeApiName("bi_mt_data_tag_v")
                                             .disPlayType(displayType)
                                             .build();
      if (Constants.TAG_FIELD_ID.equals(fieldId)) {
        subTagField.setDbFieldName("sub_tag_id_arr");
      } else if (Constants.TAG_GROUP_FIELD_ID.equals(fieldId)) {
        subTagField.setDbFieldName("tag_id_arr");
      }
      return Optional.of(subTagField);
    }
    return Optional.empty();
  }

}
