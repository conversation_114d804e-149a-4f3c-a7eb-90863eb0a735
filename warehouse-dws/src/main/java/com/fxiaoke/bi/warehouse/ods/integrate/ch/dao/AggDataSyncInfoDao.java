package com.fxiaoke.bi.warehouse.ods.integrate.ch.dao;

import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.mapper.AggDataSyncInfoMapper;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.bi.warehouse.ods.utils.InitSQL;
import com.fxiaoke.jdbc.JdbcConnection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;

@Repository("newAggDataSyncInfoDao")
@Slf4j
public class AggDataSyncInfoDao {

    @Resource
    private AggDataSyncInfoMapper aggDataSyncInfoMapper;

    @Resource
    private CHDataSource chDataSource;

    @Resource
    private CHRouterPolicy chRouterPolicy;

    /**
     * 根据企业Id，图表id，图表版本，同步批次号查询ch agg_data_sync_info信息
     */
    public AggDataSyncInfoDo queryViewAggDataSyncInfo(String tenantId, String viewId, String viewVersion, int batchNum) {
        return aggDataSyncInfoMapper.setTenantId(tenantId).queryViewAggDataSyncInfo(tenantId, viewId, viewVersion, batchNum);
    }

    /**
     * 根据企业Id，图表id查询ch agg_data_sync_info信息
     */
    public List<AggDataSyncInfoDo> queryAggDataSyncInfoByTenantIdAndViewId(String upStreamTenantId, String tenantId, String viewId) {
        return aggDataSyncInfoMapper.setTenantId(upStreamTenantId).queryAggDataSyncByTenantIdAndViewId(tenantId, viewId);
    }

    /**
     * 根据企业Id，图表id，同步策略policy_id查询ch agg_data_sync_info信息
     */
    public List<AggDataSyncInfoDo> queryAggDataSyncInfoByPolicyId(String upTenantId, String downTenantId, String viewId, String policyId) {
        return aggDataSyncInfoMapper.setTenantId(upTenantId).queryAggDataSyncByPolicyId(downTenantId, viewId, policyId);
    }

    /**
     * 根据policyIds查询agg_data_sync_info信息
     */
    public List<AggDataSyncInfoDo> queryAggDataSyncInfoByPolicyIds(String upTenantId, List<String> policyIds) {
        return aggDataSyncInfoMapper.setTenantId(upTenantId).queryAggDataSyncByPolicyIds(policyIds);
    }

    /**
     * 根据企业Id，图表id，图表版本，同步批次号更新ch agg_data_sync_info status状态
     */
    public void updateAggDataSyncInfoStatus(AggDataSyncInfoDo aggDataSyncInfoDo) {
        aggDataSyncInfoMapper.setTenantId(aggDataSyncInfoDo.getTenantId()).updateAggDataSyncInfoStatus(aggDataSyncInfoDo);
    }

    /**
     * ch中使用删除代替更新
     */
    public void updateAggDataSyncInfoStatusByInsert(String upStreamTenantId, AggDataSyncInfoDo aggDataSyncInfoDo) {
        aggDataSyncInfoMapper.setTenantId(upStreamTenantId).insertAggDataSyncInfo(aggDataSyncInfoDo);
    }

    /**
     * ch中使用删除代替更新
     */
    public void updateAggDataSyncInfoBatch(String upStreamTenantId, List<AggDataSyncInfoDo> aggDataSyncInfoDos) {
        aggDataSyncInfoMapper.setTenantId(upStreamTenantId).insertAggDataSyncInfoBatch(aggDataSyncInfoDos);
    }

    /**
     * ch更新agg_data_sync_info - 同步策略
     */
    public void updateAggDataSyncInfoStatusByPolicy(String upStreamTenantId, AggDataSyncInfoDo aggDataSyncInfoDo) {
        aggDataSyncInfoMapper.setTenantId(upStreamTenantId).insertAggDataSyncInfoByPolicyId(aggDataSyncInfoDo);
    }

    /**
     * 如果agg_data_sync_info不存在则创建
     */
    public void createAggDataSyncInfo(RouterInfo upStreamRouterInfo) {
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chRouterPolicy.getChJdbcURL(upStreamRouterInfo, true), 60000L)) {
            jdbcConnection.executeUpdate(InitSQL.agg_data_sync_info);
        } catch (SQLException e) {
            log.error("create agg_data_sync_info error:{}, {}", upStreamRouterInfo.getMasterProxyUrl(), upStreamRouterInfo.getDbName(), e);
        }
    }
}
