package com.fxiaoke.bi.warehouse.ods.rout;

import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.github.mybatis.tenant.TenantContext;
import com.github.mybatis.tenant.TenantPolicy;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/10 16:40
 */
@Slf4j
@Service("myBatisRoutePolicy")
public class MyBatisRoutePolicy implements TenantPolicy {
  @Autowired
  private DbRouterClient dbRouterClient;
  public static String BIZ = "BI";
  public static String APPLICATION = "fs-bi-custom-statistic-schedule";
  public static String DIALECT = "postgresql";

  @Override
  public TenantContext get(String tenantId, boolean readOnly) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT,
      GrayManager.isAllowByRule("use-pgbouncer", tenantId));
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (readOnly && GrayManager.isAllowByRule("use-db-slave", tenantId) &&
      StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    String schemaName = "public";
    if (routerInfo.getStandalone()) {
      schemaName = "sch_" + tenantId;
    }
    return TenantContext.builder()
                        .id(tenantId)
                        .schema(schemaName)
                        .url(jdbcUrl)
                        .username(routerInfo.getUserName())
                        .password(routerInfo.getPassWord())
                        .build();
  }

  /**
   * <p>获取router info 并且加上了灰度控件</p>
   */
  public RouterInfo getRouterInfo(String tenantID) {
    if (StringUtils.isEmpty(tenantID)) {
      return null;
    }
    RouterInfo routerInfo = null;
    try {
      routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT);
      boolean isBIStandalone = routerInfo.getStandalone();
      routerInfo.setStandalone(isBIStandalone);
    } catch (Exception e) {
      log.error("getRouterInfo error tenantID:{},errormsg:{}", tenantID, e.getMessage());
    }
    return routerInfo;
  }

  public String getPgMasterIp(String tenantId) {
    if (StringUtils.isEmpty(tenantId)) {
      return null;
    }
    String ip = null;
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT);
      String jdbcUrl = routerInfo.getJdbcUrl();
      ip = jdbcUrl.substring(18, jdbcUrl.lastIndexOf(":"));
    } catch (Exception e) {
      log.error("getPgMasterIp error tenantID:{},errormsg:{}", tenantId, e.getMessage());
    }
    return ip;
  }

  /**
   * <p>判断是否schema隔离</p>
   */
  public boolean isStandalone(String tenantID) {
    boolean isBIStandalone = false;
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT);
      isBIStandalone = routerInfo.getStandalone();
    } catch (Exception e) {
      log.error("isStandalone error tenantID:{},errormsg:{}", tenantID, e.getMessage());
    }
    return isBIStandalone;
  }

  /**
   * <p>获取路由路由地址</p>
   */
  public String getPgBouncerJdbcURL(String tenantID) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT,
      GrayManager.isAllowByRule("use-pgbouncer", tenantID));
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (GrayManager.isAllowByRule("use-db-slave", tenantID) && StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    return jdbcUrl;
  }

  /**
   * <p>获取pg bouncer url</p>
   */
  public String getPgBouncerURLByURL(String jdbcURL, boolean isMaster) {
    return dbRouterClient.convertPgBouncerUrl(BIZ, jdbcURL, isMaster);
  }

  /**
   * <p>获取从库的jdbc</p>
   *
   * @param tenantId 企业id
   * @return
   */
  public String getSlaveJdbcUrl(String tenantId) {
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT,
        GrayManager.isAllowByRule("use-pgbouncer", tenantId));
      return routerInfo.getSlaveUrl();
    } catch (Exception e) {
      log.warn("get slave jdbcUrl error tenant_id:{}", tenantId, e);
    }
    return null;
  }

  /**
   * 批量获取企业的路由信息
   *
   * @param tenantIdList
   * @return
   */
  public Map<String, RouterInfo> queryTenantChDbRouteInfo(List<String> tenantIdList) {
    if (CollectionUtils.isEmpty(tenantIdList)) {
      return Maps.newHashMap();
    }
    Map<String, RouterInfo> tenantIdToRouteInfoMap = dbRouterClient.batchQueryRouterInfo(tenantIdList, BIZ,
      APPLICATION, DIALECT);
    if (MapUtils.isEmpty(tenantIdToRouteInfoMap)) {
      return Maps.newHashMap();
    }
    return tenantIdToRouteInfoMap;
  }
}