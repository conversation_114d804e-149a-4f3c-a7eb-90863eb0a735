package com.fxiaoke.bi.warehouse.ods.entity;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;
import java.util.Set;

@Data
@AllArgsConstructor
public class BITransferPageTag {
  private String startObjectId;
  private long pageSize;
  private long realSize;
  private long maxTimeValue;
  private Map<String, Set<String>> apiNameEiMapper;

  public boolean isLastPage() {
    return realSize < pageSize;
  }

  public void putApiAndEi(String apiName, String ei) {
    if("-1".equals(ei)){
      return;
    }
    apiNameEiMapper.computeIfAbsent(apiName, key -> Sets.newHashSet()).add(ei);
  }
}
