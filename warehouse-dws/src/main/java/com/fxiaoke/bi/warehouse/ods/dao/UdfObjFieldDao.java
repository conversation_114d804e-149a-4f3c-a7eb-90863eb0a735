package com.fxiaoke.bi.warehouse.ods.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.util.ObjectConfigManager;
import com.fxiaoke.bi.warehouse.common.util.SQLUtil;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjMapper;
import com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper.PaasPgMapper;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.ChangedObjectAndFieldMessage;
import com.fxiaoke.common.Pair;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Repository
public class UdfObjFieldDao {

  @Resource
  private UdfObjFieldMapper udfObjFieldMapper;
  @Resource
  private UdfObjMapper udfObjMapper;
  @Resource
  private PaasPgMapper paasPgMapper;
  public static final String UDF_OBJECT_FIELD_CHANGE_ROOM = "udf_object_field_change_room";
  protected static LoadingCache<String, List<Integer>> UDF_OBJ_FIELD_CACHE;
  private static List<String> CONVERT_ARRAY_TYPE = List.of("select_many", "tag", "department", "employee", "department_many", "employee_many", "object_reference_many", "dimension", "out_employee");
  private volatile long udfObjFieldCacheSize;
  private volatile int durationMinutes = 30;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", iConfig -> {
      udfObjFieldCacheSize = iConfig.getLong("udfObjFieldCacheSize", 30000L);
      durationMinutes = iConfig.getInt("durationMinutes", 30);
      String convertArrayTypeStr = iConfig.get("convert_array_type", "select_many,tag,department,employee,department_many,employee_many,object_reference_many,dimension,out_employee");
      CONVERT_ARRAY_TYPE = Splitter.on(",").omitEmptyStrings().splitToList(convertArrayTypeStr);
    });
    UDF_OBJ_FIELD_CACHE = CacheBuilder.newBuilder()
                                      .maximumSize(udfObjFieldCacheSize)
                                      .refreshAfterWrite(durationMinutes, TimeUnit.MINUTES)
                                      .build(new CacheLoader<>() {
                                        @NotNull
                                        @Override
                                        public List<Integer> load(@NotNull String key) {
                                          String[] tenantIdAndDescribeApiName = key.split("\\|");
                                          String tenantId = tenantIdAndDescribeApiName[0];
                                          String describeApiName = tenantIdAndDescribeApiName[1];
                                          try {
                                            List<Integer> fieldList = paasPgMapper.setTenantId(tenantId)
                                                                                  .getFieldsByObjNameAndType(tenantId, describeApiName, SQLUtil.generateInExpress(CONVERT_ARRAY_TYPE));
                                            if (!CollectionUtils.isEmpty(fieldList)) {
                                              return fieldList;
                                            }
                                          } catch (Exception e) {
                                            log.error("array value field query paas db error! tenantId:{}, describeApiName:{}", tenantId, describeApiName, e);
                                          }
                                          return Collections.emptyList();
                                        }
                                      });
    NotifierClient.register("paas_mt_field_change_room_paas_bi_copier", (message) -> {
      log.debug("paas_mt_field_change_room_paas_bi_copier received, msg: {}", message);
      Map<String, List<String>> describeApiNameTenantIdMap = JSON.parseObject(message.getContent(), new TypeReference<>() {});
      describeApiNameTenantIdMap.forEach((tenantId, describeApiNameList) -> {
        if (!CollectionUtils.isEmpty(describeApiNameList)) {
          describeApiNameList.forEach(describeApiName -> UDF_OBJ_FIELD_CACHE.invalidate(tenantId + "|" + describeApiName));
        }
      });
    });
  }

  public String findObjNameByTable(String tenantId, String tableNameLower) {
    return udfObjMapper.setTenantId(tenantId)
                       .findObjNameByTable(Integer.parseInt(tenantId), tableNameLower.toLowerCase());
  }

  public List<Integer> getFieldsByObjNameAndType(String ei, String objName) {
    try {
      return UDF_OBJ_FIELD_CACHE.get(ei + "|" + objName);
    } catch (ExecutionException e) {
      log.error("udf_obj_field缓存加载异常：ei:{}, objName:{}", ei, objName, e);
      return null;
    }
  }

  /**
   * 查询所有udf_obj_field 数据
   *
   * @param objName    BI对象名称
   * @param ei         租户id
   * @param isShowList 字段过滤
   * @return
   */
  public List<UdfObjFieldDO> getFieldsByObjName(String objName,
                                                Integer ei,
                                                int[] isShowList,
                                                String[] dbFieldFieldNames) {
    // 开启主题时需要将主题对象的扩展对象的所有的字段查出来，加到主题对象的字段列表中
    try {
      objName = ObjectConfigManager.getPreObjName(objName);
      List<UdfObjFieldDO> fieldList;
      if (dbFieldFieldNames == null) {
        fieldList = udfObjFieldMapper.setTenantId(String.valueOf(ei))
                                     .getFieldsByObjName(objName, ObjectConfigManager.getExtendObjName(objName), ei, isShowList);
      } else {
        fieldList = udfObjFieldMapper.setTenantId(String.valueOf(ei))
                                     .getFieldsByObjNameByDbFieldName(objName, ObjectConfigManager.getExtendObjName(objName), ei, isShowList, dbFieldFieldNames);
      }
      if (CollectionUtils.isEmpty(fieldList)) {
        log.warn("getFieldsByObjName udfObjFieldList is empty,{},{}", objName, ei);
        return Lists.newArrayList();
      }
      Map<String, List<UdfObjFieldDO>> fieldGroupByEI = fieldList.stream()
                                                                 .collect(Collectors.groupingBy(field ->
                                                                   field.getDbObjName() + "$" +
                                                                   field.getDbFieldName()));
      fieldList.removeAll(fieldGroupByEI.values()
                                        .stream()
                                        .filter(fields -> fields.size() > 1)
                                        .flatMap(List::stream)
                                        .filter(field -> Objects.equals(CHContext.SYS_DEFAULT_EI, field.getEi()))
                                        .collect(Collectors.toSet()));
      if (CollectionUtils.isEmpty(fieldList)) {
        log.warn("getFieldsByObjName udfObjFieldList is empty,{},{}", objName, ei);
      }
      return fieldList;
    } catch (Exception e) {
      log.error("getFieldsByObjName error objName:{},ei:{}", objName, ei, e);
      throw new RuntimeException("fieldMapper.getFieldsByObjName error objName: " + objName + " ei: " + ei, e);
    }
  }

  public void removeCache(ChangedObjectAndFieldMessage message) {
    ChangedObjectAndFieldMessage.HandleChangedObjectArg handleChangedObjectAndFieldArg = message.getHandleChangedObjectAndFieldArg();
    String tenantId = handleChangedObjectAndFieldArg.getTenantId();
    UDF_OBJ_FIELD_CACHE.invalidate(Pair.build(Integer.parseInt(tenantId), handleChangedObjectAndFieldArg.getObjName()));
    Map<String, String> eiAndApiName = Map.of("tenantId", tenantId, "objName", handleChangedObjectAndFieldArg.getObjName());
    NotifierClient.send(UDF_OBJECT_FIELD_CHANGE_ROOM, JSON.toJSONString(eiAndApiName), tenantId);
  }

  public List<UdfObjFieldDO> getNeedFormatFieldsByObjName(int ei, String objName) {
    return udfObjFieldMapper.setTenantId(String.valueOf(ei))
                            .getFieldsByObjNameAndType(ei, objName, SQLUtil.generateInExpress(CONVERT_ARRAY_TYPE));
  }

  public UdfObjFieldDO findWhatListRelatedObjField(String tenantId, String dbObjName) {
    return udfObjFieldMapper.setTenantId(tenantId).findWhatListRelatedObjField(Integer.parseInt(tenantId), dbObjName);
  }

  public UdfObjFieldDO findObjIdFieldByObjName(String tenantId, String describeApiName) {
    return udfObjFieldMapper.setTenantId(tenantId).findObjIdFieldByObjName(Integer.parseInt(tenantId), describeApiName);
  }
}
