package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.db.dao.DbTableSyncInfoDao;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.ObjectConfigManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.ClickhouseTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.BiAggSyncInfoMapper;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBAggInfoDO;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.db.mapper.DBSyncInfoMapper;
import com.fxiaoke.bi.warehouse.dws.model.DBUpdatedEvent;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.DBTableSyncInfoMapper;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.Pair;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class DBUpdateEventDao {

  @Autowired
  private DBSyncInfoMapper dbSyncInfoMapper;
  @Autowired
  private DBTableSyncInfoMapper dbTableSyncInfoMapper;
  @Resource
  private PgDataSource pgDataSource;
  @Resource
  private ClickhouseTenantPolicy clickhouseTenantPolicy;
  @Resource(name = "mybatisTenantPolicy")
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Autowired
  private BiAggSyncInfoMapper biAggSyncInfoMapper;
  @Resource
  private DBAggInfoDAO dbAggInfoDAO;
  @Resource
  private DbTableSyncInfoDao dbTableSyncInfoDao;
  /**
   * @param id
   * @return
   */
  public DBSyncInfoDO findSyncById(String id) {
    return dbSyncInfoMapper.setTenantId("-1").findBySyncId(id);
  }

  public BIAggSyncInfoDO findBIAggSyncById(String id){
    return biAggSyncInfoMapper.setTenantId("-1").queryBiAggSyncInfo(id);
  }

  public List<DBSyncInfoDO> findSyncByIdAll() {
    return dbSyncInfoMapper.setTenantId("-1").findBySyncIdAll();
  }

  /**
   *
   * @param pgDB
   * @param schema
   * @param dbSyncId
   * @return
   */
  public List<DbTableSyncInfoDO> findByDBSyncInfoIDAndBatchNum(String pgDB,
                                                               String schema,
                                                               long batchNum,
                                                               String dbSyncId) {
    String pgDbName = CommonUtils.getDBName(pgDB);
    if (GrayManager.isAllowByRule("query_from_tenant_db", String.format("%s^%s",pgDbName,schema))) {
      return dbTableSyncInfoDao.findByDBSyncInfoID(pgDB, schema, dbSyncId, batchNum);
    }
    return dbTableSyncInfoMapper.setTenantId("-1").findByDBSyncInfoID(dbSyncId, batchNum);
  }
  /**
   * 生成DB更新事件支持分区
   * @param dbSyncInfoDO db同步信息
   * @param eventBatchNum 同步批次号
   * @param biAggSyncInfoDO 1+N 同步信息
   * @param dsBachNum 1+n 同步批次
   * @return
   */
  public DBUpdatedEvent createDbUpdateEventPlus(DBSyncInfoDO dbSyncInfoDO,
                                                Long eventBatchNum,
                                                BIAggSyncInfoDO biAggSyncInfoDO,
                                                Long dsBachNum) {
    String chDb = CommonUtils.getDBName(dbSyncInfoDO.getChDB());
    if (GrayManager.isAllowByRule("use_ch_ods_partition", chDb) && Objects.equals(dbSyncInfoDO.getAllowIncPartition(), WarehouseConfig.OPEN_INC_PARTITION)) {
      DBAggInfoDO dbAggInfoDO = dbAggInfoDAO.queryOrInitDBAggInfo(dbSyncInfoDO);
      if (dbAggInfoDO == null) {
        log.warn("dbAggInfoDO is nul id:{},chDB:{},pgDB:{}", dbSyncInfoDO.getId(), dbSyncInfoDO.getChDB(), dbSyncInfoDO.getPgDB());
        return null;
      }
      Long[] batchNums = Splitter.on(",")
                                 .splitToList(dbAggInfoDO.getSyncBatchNums())
                                 .stream()
                                 .filter(StringUtils::isNotBlank)
                                 .map(Long::parseLong)
                                 .toArray(Long[]::new);
      Arrays.sort(batchNums);
      DBUpdatedEvent result = new DBUpdatedEvent();
      result.setId(dbSyncInfoDO.getId());
      result.setStatus(dbAggInfoDO.getStatus());
      result.setAggInfoVersion(dbAggInfoDO.getVersion());
      result.setChJdbcUrl(dbSyncInfoDO.getChDB());
      result.setPgJdbcUrl(dbSyncInfoDO.getPgDB());
      result.setPgSchema(dbSyncInfoDO.getPgSchema());
      result.setLastModifiedTime(dbAggInfoDO.getLastModifiedTime());
      if (batchNums.length > 0) {
        result.setBatchNum(batchNums[batchNums.length - 1]);
      } else {
        log.warn("this aggSyncInfo batchNums is empty id:{},chDB:{},pgDB:{}", dbSyncInfoDO.getId(), dbSyncInfoDO.getChDB(), dbSyncInfoDO.getPgDB());
        result.setBatchNum(-1);
      }
      result.setAllowIncPartition(dbSyncInfoDO.getAllowIncPartition());
      result.setAllowCalPartition((dbAggInfoDO.getAllowCalPartition() == WarehouseConfig.OPEN_CAL_PARTITION ||
        GrayManager.isAllowByRule("all_cal_partition", dbSyncInfoDO.getId())) ? 1 : 0);
      result.setBatchNums(batchNums);
      result.setLastSyncEis(dbSyncInfoDO.getLastSyncEis());
      result.setSyncFlows(dbAggInfoDO.getSyncFlows());
      Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap = Maps.newHashMap();
      result.setTenantIdObjectDescribeApiNamesMap(tenantIdObjectDescribeApiNamesMap);
      Map<String, Set<String>> tableTenantIdSetMap = Maps.newHashMap();
      result.setTableTenantIdSetMap(tableTenantIdSetMap);
      String tableApiNameEiMaps = dbAggInfoDO.getApiNameEiMap();
      if (SyncStatusEnum.createFromStatus(dbAggInfoDO.getStatus()) == SyncStatusEnum.AGG_ED) {
        log.warn("dbAggInfoDO is status is AGG_ED no need to cal id:{},chDB:{},pgDB:{}", dbSyncInfoDO.getId(), dbSyncInfoDO.getChDB(), dbSyncInfoDO.getPgDB());
        return result;
      }
      if(StringUtils.isNotBlank(tableApiNameEiMaps) && !Objects.equals("{}",tableApiNameEiMaps)){
        Map<String, Map<String, Set<String>>> combineSyncFlowApiEiMaps = JSON.parseObject(tableApiNameEiMaps, new TypeReference<>() {});
        if (combineSyncFlowApiEiMaps != null) {
          combineSyncFlowApiEiMaps.forEach((tableName, objectDescribeApiNameEiMap) -> this.parseApiNameEIMaps(objectDescribeApiNameEiMap, tableName, tenantIdObjectDescribeApiNamesMap, tableTenantIdSetMap));
          //获取所有需要全量计算图的租户id merge 到增量计算的受影响租户中
          List<String> tenantIds = this.findAllCreateStatViewEis(dbSyncInfoDO.getPgDB(), dbSyncInfoDO.getPgSchema(), true);
          if (CollectionUtils.isNotEmpty(tenantIds)) {
            tenantIds.forEach(tenantId -> tenantIdObjectDescribeApiNamesMap.computeIfAbsent(tenantId, key -> Sets.newHashSet()));
          }
        }
      }
      if(StringUtils.isNotBlank(dbAggInfoDO.getIncSysModifiedTimeRange())){
       Map<String,BIPair<Long,Long>> incSysModifiedTimeRangeMap = JSON.parseObject(dbAggInfoDO.getIncSysModifiedTimeRange(), new TypeReference<>() {});
        result.setIncSysModifiedTimeRangeMap(incSysModifiedTimeRangeMap);
      }
      return result;
    }
    return this.createDbUpdateEvent(dbSyncInfoDO, eventBatchNum, biAggSyncInfoDO, dsBachNum);
  }

  public DBUpdatedEvent createDbUpdateEvent(DBSyncInfoDO dbSyncInfoDO, Long eventBatchNum,BIAggSyncInfoDO biAggSyncInfoDO,Long dsBachNum) {
    if (dbSyncInfoDO.getBatchNum().longValue() != eventBatchNum.longValue()) {
      log.warn("dbSyncInfoDO.getBatchNum() != batchNum, dbSyncInfoDO.getBatchNum()={}, batchNum={}", dbSyncInfoDO.getBatchNum(), eventBatchNum);
      return null;
    }
    DBUpdatedEvent result = new DBUpdatedEvent();
    result.setId(dbSyncInfoDO.getId());
    result.setStatus(dbSyncInfoDO.getStatus());
    result.setChJdbcUrl(dbSyncInfoDO.getChDB());
    result.setPgJdbcUrl(dbSyncInfoDO.getPgDB());
    result.setPgSchema(dbSyncInfoDO.getPgSchema());
    result.setBatchNum(eventBatchNum);
    result.setLastModifiedTime(dbSyncInfoDO.getLastModifiedTime());
    Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap = Maps.newHashMap();
    result.setTenantIdObjectDescribeApiNamesMap(tenantIdObjectDescribeApiNamesMap);
    Map<String, Set<String>> tableTenantIdSetMap = Maps.newHashMap();
    result.setTableTenantIdSetMap(tableTenantIdSetMap);
    List<DbTableSyncInfoDO> tableSyncInfoDOList = this.findByDBSyncInfoIDAndBatchNum(dbSyncInfoDO.getPgDB(),dbSyncInfoDO.getPgSchema(),eventBatchNum,dbSyncInfoDO.getId());
    if(biAggSyncInfoDO!=null && dsBachNum!=null){
      result.setAggSyncId(biAggSyncInfoDO.getId());
      List<DbTableSyncInfoDO> integrateTableSyncInfoDOList =this.findByDBSyncInfoIDAndBatchNum(dbSyncInfoDO.getPgDB(),dbSyncInfoDO.getPgSchema(),dsBachNum,biAggSyncInfoDO.getId());
      result.setDsBatchNum(dsBachNum);
      if(CollectionUtils.isNotEmpty(integrateTableSyncInfoDOList)){
        tableSyncInfoDOList.addAll(integrateTableSyncInfoDOList);
      }
    }
    for (DbTableSyncInfoDO dbTableSyncInfoDO : tableSyncInfoDOList) {
      Map<String, Set<String>> objectDescribeApiNameEiMap =
        DbTableSyncInfoDO.parseApiNameEiMap(dbTableSyncInfoDO.getApiNameEiMap());
      String tableName = dbTableSyncInfoDO.getTableName();
      if (WarehouseConfig.ch2chTablePair.containsKey(tableName)) {
        tableName = WarehouseConfig.ch2chTablePair.get(tableName);
      }
      this.parseApiNameEIMaps(objectDescribeApiNameEiMap, tableName, tenantIdObjectDescribeApiNamesMap, tableTenantIdSetMap);
    }
    //获取所有需要全量计算图的租户id merge 到增量计算的受影响租户中
    List<String> tenantIds = this.findAllCreateStatViewEis(dbSyncInfoDO.getPgDB(), dbSyncInfoDO.getPgSchema(), true);
    if (CollectionUtils.isNotEmpty(tenantIds)) {
      tenantIds.forEach(tenantId -> tenantIdObjectDescribeApiNamesMap.computeIfAbsent(tenantId, key -> Sets.newHashSet()));
    }
    return result;
  }

  private void parseApiNameEIMaps(Map<String, Set<String>> objectDescribeApiNameEiMap,
                                  String tableName,
                                  Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap,
                                  Map<String, Set<String>> tableTenantIdSetMap) {
    Set<String> tenantIdSet = Sets.newHashSet();
    objectDescribeApiNameEiMap.forEach((apiName, apiTenantIdSet) -> {
      tenantIdSet.addAll(apiTenantIdSet);
      apiTenantIdSet.forEach(tenantId -> {
        String objectApiName = tableName;
        if (StringUtils.equalsAny(tableName, "biz_account", "object_data", "object_data_downstream") || tableName.endsWith("_downstream")) {
          //tableName.endsWith("_downstream") 有点儿风险因为不确定是否有业务表也会这样命名
          objectApiName = apiName;
          if (objectApiName.endsWith("_udef")) {
            tenantIdObjectDescribeApiNamesMap.computeIfAbsent(tenantId, k -> Sets.newHashSet())
                                             .add(ObjectConfigManager.getPreObjName(objectApiName));
          }
        }
        tenantIdObjectDescribeApiNamesMap.computeIfAbsent(tenantId, k -> Sets.newHashSet()).add(objectApiName);
      });
    });
    tableTenantIdSetMap.put(tableName, tenantIdSet);
  }

  /**
   * 获取所有有全量计算图的ei
   *
   * @param jdbcURL
   * @param master
   * @return
   */
  public List<String> findAllCreateStatViewEis(String jdbcURL, String schemaName, boolean master) {
    String sql = "SELECT tenant_id FROM " + schemaName +
      ".bi_mt_topology_table where status=0 and is_deleted=0 group by tenant_id";
    List<String> tenantIds = Lists.newArrayList();
    try (JdbcConnection jdbcConnection = pgDataSource.getJdbcConnection(jdbcURL,schemaName)){
      jdbcConnection.query(sql, rs -> {
        while (rs.next()) {
          tenantIds.add(rs.getString("tenant_id"));
        }
      });
    } catch (Exception e) {
      log.error("findAllCreateStatViewEis error jdbcURL:{},isMaster:{}", jdbcURL, master, e);
    }
    return tenantIds;
  }

  public boolean updateDBStatus(String id, int status, int oldStatus) {
    return dbSyncInfoMapper.setTenantId("-1").updateDBStatus(id, status, oldStatus, new Date().getTime()) > 0;
  }

  /**
   * 根据租户id和更新得表集合获取表所有更新时间
   *
   * @param tenantId   租户id
   * @param tableNames 表集合
   * @return 最大变更时间
   */
  public long findMaxModifiedTimeByEi(String tenantId, Set<String> tableNames) {
    try {
      String chDB = clickhouseTenantPolicy.getCHJdbcURL(tenantId);
      Pair<String, Boolean> urlAndSchema = mybatisTenantPolicy.getDBURLAndSchema(tenantId);
      String schemaName = urlAndSchema.second ? "sch_" + tenantId : "public";
      //如果有下游表,则需要用下游同步信息
      Set<String> dsTableNames = tableNames.stream().filter(Constants::isDSTable).collect(Collectors.toSet());
      String dbSyncId;
      Long lastSyncTime;
      if (!dsTableNames.isEmpty()) {
        BIAggSyncInfoDO biAggSyncInfoDO = biAggSyncInfoMapper.setTenantId("-1").queryBiAggSyncInfoDB(tenantId);
        if (biAggSyncInfoDO != null) {
          long tableMaxTime = this.findMaxModifiedTime(urlAndSchema.first,schemaName,biAggSyncInfoDO.getId(), dsTableNames);
          return Math.max(tableMaxTime, biAggSyncInfoDO.getLastSyncTime());
        }
      }
      //没有下游指标则用dbSyncInfo
      DBSyncInfoDO dbSyncInfoDO = dbSyncInfoMapper.setTenantId("-1").queryDBSyncInfo(chDB, urlAndSchema.first, schemaName);
      if (dbSyncInfoDO == null) {
        log.error("dbSyncInfoDO is null tenantId:{},chDB:{},pgDB:{},schemaName:{}", tenantId, chDB, urlAndSchema.first, schemaName);
        return System.currentTimeMillis() - 15 * 60 * 1000;
      }
      dbSyncId = dbSyncInfoDO.getId();
      lastSyncTime = dbSyncInfoDO.getLastSyncTime();
      long tableMaxTime = this.findMaxModifiedTime(dbSyncInfoDO.getPgDB(),dbSyncInfoDO.getPgSchema(),dbSyncId, tableNames);
      return Math.max(tableMaxTime, lastSyncTime);
    } catch (Exception e) {
      log.error("queryDBLatestSyncTimeByEI error tenantId:{}", tenantId, e);
    }
    return System.currentTimeMillis() - 15 * 60 * 1000;
  }

  /**
   * 根据租户id和表集合获取表所有同步信息
   *
   * @param tenantId   租户id
   * @param tableNames 表集合
   * @return {@link DbTableSyncInfoDO}
   */
  public List<DbTableSyncInfoDO> findDBTableSyncInfoByEiAndTables(String tenantId, Set<String> tableNames) {
    try {
      String chDB = clickhouseTenantPolicy.getCHJdbcURL(tenantId);
      Pair<String, Boolean> urlAndSchema = mybatisTenantPolicy.getDBURLAndSchema(tenantId);
      String schemaName = urlAndSchema.second ? "sch_" + tenantId : "public";
      String pgDBName = CommonUtils.getDBName(urlAndSchema.first);
      if(GrayManager.isAllowByRule("query_from_tenant_db",String.format("%s^%s",pgDBName,schemaName))){
        DBSyncInfoDO dbSyncInfoDO = dbSyncInfoMapper.setTenantId("-1").queryDBSyncInfo(chDB, urlAndSchema.first, schemaName);
       return dbTableSyncInfoDao.queryDbTableSyncInfos(urlAndSchema.first,schemaName,dbSyncInfoDO.getId(),Lists.newArrayList(tableNames));
      }
      return dbTableSyncInfoMapper.setTenantId("-1")
                                  .queryDbTableSyncInfo(urlAndSchema.first, chDB, schemaName, tableNames.toArray(String[]::new));
    } catch (Exception e) {
      log.error("queryDBLatestSyncTimeByEI error tenantId:{}", tenantId, e);
    }
    return null;
  }

  /**
   * 计算同步的表的最大偏移时间
   * @param pgDbURL
   * @param pgSchema  schema名称
   * @param dbSyncId
   * @param tableNames 表列表
   * @return
   */
  public long findMaxModifiedTime(String pgDbURL,String pgSchema,String dbSyncId, Set<String> tableNames) {
    if (CollectionUtils.isEmpty(tableNames)) {
      return 0L;
    }
    List<DbTableSyncInfoDO> dbTableSyncInfoDOS;
    String pgDBName = CommonUtils.getDBName(pgDbURL);
    if (GrayManager.isAllowByRule("query_from_tenant_db", String.format("%s^%s",pgDBName,pgSchema))) {
      dbTableSyncInfoDOS = dbTableSyncInfoDao.queryDbTableSyncInfos(pgDbURL, pgSchema, dbSyncId, Lists.newArrayList(tableNames));
    } else {
      dbTableSyncInfoDOS = dbTableSyncInfoMapper.setTenantId("-1")
                                                .queryDbTableSyncInfoBySyncId(dbSyncId, tableNames.toArray(String[]::new));
    }
    if (CollectionUtils.isNotEmpty(dbTableSyncInfoDOS)) {
      return dbTableSyncInfoDOS.stream().map(syncInfo -> {
        if (syncInfo.getMaxSysModifiedTime() == null) {
          return syncInfo.getLastSyncTime();
        }
        if (syncInfo.getMaxSysModifiedTime().toString().length() > 13) {
          syncInfo.setMaxSysModifiedTime(syncInfo.getMaxSysModifiedTime() / 1000L);
        }
        return Math.max(syncInfo.getMaxSysModifiedTime(),syncInfo.getLastSyncTime());
      }).max(Comparator.naturalOrder()).orElse(0L);
    }
    return 0L;
  }

  /**
   * 计算同步的表的最大偏移时间兼容1+N
   *
   * @param dbSyncId
   * @param includeDSRule 是否是1+N的图
   * @return
   */
  public Long findMaxModifiedTimeN(String tenantId,
                                   boolean includeDSRule,
                                   String pgDbURL,
                                   String pgSchema,
                                   String dbSyncId,
                                   String aggSyncId,
                                   Set<String> tableNames) {
    //1+N
    if (includeDSRule) {
      Set<String> dsTable = tableNames.stream().filter(Constants::isDSTable).collect(Collectors.toSet());
      if (dsTable.isEmpty()) {
        //是1+N的图,但计算的不是1+N的指标,所以不更新计算时间
        return null;
      }
      if (dbSyncId != null) {
        return this.findMaxModifiedTime(pgDbURL, pgSchema, dbSyncId, dsTable);
      }
      log.warn("findMaxModifiedTimeN is null, tenantId:{}, dsTable:{}", tenantId, JSON.toJSONString(dsTable));
      return null;
    }
    //非1+N
    return this.findMaxModifiedTime(pgDbURL, pgSchema, dbSyncId, tableNames);
  }
}
