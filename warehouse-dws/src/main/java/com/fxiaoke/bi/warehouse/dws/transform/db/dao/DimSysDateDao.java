package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.fxiaoke.bi.warehouse.common.util.DateTimeUtil;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.DimSysDateMapper;
import com.fxiaoke.bi.warehouse.dws.transform.model.GoalRuleBO;
import com.fxiaoke.common.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2023/9/27
 */
@Slf4j
@Service
public class DimSysDateDao {

  @Autowired
  DimSysDateMapper dimSysDateMapper;

  /**
   * 生成左闭右开的时间筛选条件
   *
   * @param tenantId   租户id
   * @param goalRuleBO 目标规则
   * @return
   */
  public Pair<Long/*最小日期*/, Long/*最大日期*/> getDimSysDate(String tenantId, GoalRuleBO goalRuleBO) {
    String checkCycle = goalRuleBO.checkCyCle;
    if (StringUtils.isBlank(checkCycle)) {
      return null;
    }
    List<String> fiscalYears = goalRuleBO.getCountFiscalYear();
    if (CollectionUtils.isEmpty(fiscalYears)) {
      log.error("can not find count fiscal year tenantId:{},ruleId:{}", tenantId, goalRuleBO.getId());
    }
    List<Integer> orderFiscalYears = fiscalYears.stream()
                                                .map(Integer::parseInt)
                                                .sorted(Comparator.naturalOrder())
                                                .toList();
    switch (checkCycle) {
      case "week" -> {
        Pair<String/*最小日期*/, String/*最大日期*/> weekDays = this.findAllWeekDayByFiscalYear(tenantId,
          orderFiscalYears.get(0), orderFiscalYears.get(
          orderFiscalYears.size() - 1), Integer.parseInt(goalRuleBO.getStartWeek()));
        long startTime = DateTimeUtil.covertYYYYMMDD2Timestamp(weekDays.first, ZoneId.of(goalRuleBO.timeZone));
        long endTime = DateTimeUtil.covertYYYYMMDD2Timestamp(weekDays.second, 1L, ZoneId.of(goalRuleBO.timeZone));
        return Pair.build(startTime, endTime);
      }
      case "month" -> {
        Pair<String/*最小日期*/, String/*最大日期*/> monthDays = findAllMonthFiscalYear(tenantId, orderFiscalYears.get(0),
          orderFiscalYears.get(
          orderFiscalYears.size() - 1), Integer.parseInt(goalRuleBO.getStartMonth()));
        long startTime = DateTimeUtil.covertYYYYMMDD2Timestamp(monthDays.first, ZoneId.of(goalRuleBO.timeZone));
        long endTime = DateTimeUtil.covertYYYYMMDD2Timestamp(monthDays.second, 1L, ZoneId.of(goalRuleBO.timeZone));
        return Pair.build(startTime, endTime);
      }
      case "quarter" -> {
        Pair<String/*最小日期*/, String/*最大日期*/> quarterDays = findAllQuarterByFiscalYear(tenantId,
          orderFiscalYears.get(0), orderFiscalYears.get(
          orderFiscalYears.size() - 1), Integer.parseInt(goalRuleBO.getStartQuarter()));
        long startTime = DateTimeUtil.covertYYYYMMDD2Timestamp(quarterDays.first, ZoneId.of(goalRuleBO.timeZone));
        long endTime = DateTimeUtil.covertYYYYMMDD2Timestamp(quarterDays.second, 1L, ZoneId.of(goalRuleBO.timeZone));
        return Pair.build(startTime, endTime);
      }
      case "year" -> {
        Pair<String/*最小日期*/, String/*最大日期*/> yearDays = findFirstDayFiscalYear(tenantId, orderFiscalYears.get(0),
          orderFiscalYears.get(
          orderFiscalYears.size() - 1));
        long startTime = DateTimeUtil.covertYYYYMMDD2Timestamp(yearDays.first, ZoneId.of(goalRuleBO.timeZone));
        long endTime = DateTimeUtil.covertYYYYMMDD2Timestamp(yearDays.second, 1L, ZoneId.of(goalRuleBO.timeZone));
        return Pair.build(startTime, endTime);
      }
      default -> {
        return null;
      }
    }
  }

  Pair<String/*最小日期*/, String/*最大日期*/> findAllWeekDayByFiscalYear(String tenantId,
                                                                          int fromYear,
                                                                          int toYear,
                                                                          int fromWeek) {
    int afterToYear = toYear + 1;
    String fromWeekFormat = String.format("%sW%02d", fromYear, fromWeek);
    String toWeekFormat = String.format("%sW%02d", afterToYear, fromWeek);
    Map<String, Object> startEndDate = dimSysDateMapper.setTenantId(tenantId)
                                                       .findAllMonDayByFiscalYear(fromYear, toYear, fromWeekFormat
                                                         , toWeekFormat);
    return Pair.build(String.valueOf(startEndDate.get("mindate")), String.valueOf(startEndDate.get("maxdate")));
  }

  //查询财年每个季度的起始天
  Pair<String/*最小日期*/, String/*最大日期*/> findAllQuarterByFiscalYear(String tenantId,
                                                                          int fromYear,
                                                                          int toYear,
                                                                          int fromQuarter) {
    int afterToYear = toYear + 1;
    String fromQuarterFormat = String.format("%sQ%d", fromYear, fromQuarter);
    String toQuarterFormat = String.format("%sQ%d", afterToYear, fromQuarter);
    Map<String, Object> fromToDateMap = dimSysDateMapper.setTenantId(tenantId)
                                                        .findAllQuarterByFiscalYear(fromQuarterFormat, toQuarterFormat);
    return Pair.build(String.valueOf(fromToDateMap.get("mindate")), String.valueOf(fromToDateMap.get("maxdate")));
  }

  //查询财年起始天
  Pair<String/*最小日期*/, String/*最大日期*/> findFirstDayFiscalYear(String tenantId, int fromYear, int toYear) {
    return Pair.build(String.format("%s0101", fromYear), String.format("%s1231", toYear));
  }

  /**
   * 查询财年月得天列表
   */
  Pair<String/*最小日期*/, String/*最大日期*/> findAllMonthFiscalYear(String tenantId,
                                                                      int fromYear,
                                                                      int toYear,
                                                                      int fromMonth) {
    int afterToYear = toYear + 1;
    String fromMonthFormat = String.format("%s%02d", fromYear, fromMonth);
    String toMonthFormat = String.format("%s%02d", afterToYear, fromMonth);
    Map<String, Object> fromToDateMap = dimSysDateMapper.setTenantId(tenantId)
                                                        .findAllMonthByFiscalYear(fromMonthFormat, toMonthFormat);
    return Pair.build(String.valueOf(fromToDateMap.get("mindate")), String.valueOf(fromToDateMap.get("maxdate")));
  }
}
