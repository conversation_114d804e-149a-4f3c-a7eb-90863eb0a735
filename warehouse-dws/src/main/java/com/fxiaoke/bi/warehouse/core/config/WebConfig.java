package com.fxiaoke.bi.warehouse.core.config;

import com.fxiaoke.bi.warehouse.dws.interceptor.TraceInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @Author:jief
 * @Date:2023/9/25
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

  private final TraceInterceptor traceInterceptor;

  public WebConfig(TraceInterceptor traceInterceptor) {
    this.traceInterceptor = traceInterceptor;
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(traceInterceptor).addPathPatterns("/**");
  }
}
