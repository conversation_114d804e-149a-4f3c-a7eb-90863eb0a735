package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatSchemaDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.StatSchemaMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author:jief
 * @Date:2024/1/3
 */
@Slf4j
@Repository
public class StatSchemaDao {
  @Autowired
  private StatSchemaMapper statSchemaMapper;

  /**
   * 根据主题名称反查所有主题的数据包含删除的作废的已启用的。
   * @param tenantId 租户id
   * @param themeApiName 主题名称
   * @return
   */
  public List<StatSchemaDO> findAllStatSchemaByEnName(String tenantId, String themeApiName){
     return statSchemaMapper.setTenantId(tenantId).findAllStatSchemaBySchemaName(tenantId,themeApiName);
  }

  /**
   * 批量查询主题
   * @param tenantId 租户id
   * @param schemaIds 主题id
   * @return
   */
  public List<StatSchemaDO> batchQueryStatSchemaBySchemaId(String tenantId, List<String> schemaIds){
    return statSchemaMapper.setTenantId(tenantId).batchQueryStatSchemaBySchemaId(tenantId,schemaIds.toArray(new String[0]));
  }

}
