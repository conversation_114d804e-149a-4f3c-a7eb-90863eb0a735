package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.core.db.ClickhouseTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.GoalRuleDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.GoalRuleDetailDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper.GoalRuleDetailMapper;
import com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper.GoalRuleMapper;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.Pair;
import com.fxiaoke.helper.StringHelper;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2023/6/25
 */
@Slf4j
@Service
public class OldGoalRuleDao extends AggRuleDao {
  @Resource
  private GoalRuleMapper goalRuleMapper;
  @Resource
  private GoalRuleDetailMapper goalRuleDetailMapper;
  @Resource
  private MappingService mappingService;
  @Resource
  private UdfObjFieldMapper udfObjFieldMapper;
  @Resource
  private DimRuleDao dimRuleDao;
  @Resource
  private ClickhouseTenantPolicy chTenantPolicy;
  private volatile String goalFixDisplayField;
  /**
   * 反查goal_value 表的sql
   */
  private static final String FILTER_SQL_NO_DETAIL =
    "select check_object_id from goal_value where tenant_id = '%s' " + "and goal_rule_id = '%s' and is_deleted = 0 ";

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", iConfig -> {
      goalFixDisplayField = iConfig.get("goal_displayField", "");
    });
  }

  /**
   * 根据规则id 获取目标规则对象
   *
   * @param tenantId 租户id
   * @param id       目标规则id
   * @return
   */
  public GoalRuleDO getRuleEntityById(String tenantId, String id) {
    return goalRuleMapper.setTenantId(tenantId).getRuleEntityById(tenantId, id);
  }

  /**
   * 创建statView
   *
   * @param tenantId            租户id
   * @param goalRuleId          目标规则id
   * @param aggAliasMapper      agg指标别名cache
   * @param dimsMapper          维度字段别名cache
   * @param dimFieldAliasMapper 维度字段映射关系对象
   * @param goalValueIds        需要反查的goal_value 表的id集合用于增量查询
   * @return
   */
  public List<TopologyTable> parseGoalRuleFromMap(String tenantId,
                                                  String goalRuleId,
                                                  Map<String, AtomicInteger> aggAliasMapper,
                                                  Map<String, AtomicInteger> dimsMapper,
                                                  Map<String, String/*dim字段别名*/> dimFieldAliasMapper,
                                                  List<String> goalValueIds) {
    GoalRuleDO goalRuleDO = this.getRuleEntityById(tenantId, goalRuleId);
    if (null == goalRuleDO) {
      log.error("find goal RuleMapFromDB error tenantId:{},ruleId:{}", tenantId, goalRuleId);
      return null;
    }
    //计算别名
    Map<String, AtomicInteger> aliasMapper = Maps.newHashMap();
    //缓存列类型
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    List<Pair<JSONObject, OldGoalDimInfo>> oldGoalInfos = this.transGoal2MultiDimGoalRule(tenantId, goalRuleDO,
      cachedTableDefinitions);
    List<TopologyTable> goalStatViews = Lists.newArrayList();
    boolean standalone = this.standalone(tenantId);
    for (Pair<JSONObject, OldGoalDimInfo> infoAndInfo : oldGoalInfos) {
      JSONObject ruleInfo = infoAndInfo.first;
      OldGoalDimInfo oldGoalDimInfo = infoAndInfo.second;
      String themeApiName = ruleInfo.getString("theme_api_name");
      if ("goal_value_obj".equals(themeApiName)) {
        themeApiName = Constants.employee_api_name;
      }
      String aggApiName = ruleInfo.getString("check_object_api_name");
      String checkApiNameLookupField = ruleInfo.getString("check_apiname_lookup_field");
      String timeZone = Constants.getTimeZoneOrDefault(ruleInfo.getString("time_zone"), Constants.Asia_Shanghai);
      Map<String, String> fieldLocationMap = Maps.newHashMap();
      AggRule aggRule = createAggRule(tenantId, ruleInfo, standalone, themeApiName, oldGoalDimInfo.getViewId(),
        cachedTableDefinitions, "goal_agg");
      //初始化agg规则
      aggRule.init();
      NodeTable aggObjNodeTable = this.createAggNodeTable(standalone, aggApiName, aliasMapper);
      //获取objectId table
      NodeTable themeNodeTable = this.joinObjectIdQuoteNodeTableAndGet(tenantId, standalone, aggObjNodeTable,
        aliasMapper, themeApiName, checkApiNameLookupField, aggRule.objectIdRule, cachedTableDefinitions);
      themeNodeTable.setObjectIdTable(true);
      //构建dim
      List<String> dimConfigStringList = Lists.newArrayList();
      List<DimRule> themeDimRules = oldGoalDimInfo.getThemeDimRules();
      if (CollectionUtils.isNotEmpty(themeDimRules)) {
        this.buildDims(tenantId, standalone, themeDimRules, themeNodeTable, aliasMapper, dimFieldAliasMapper,
          dimsMapper, dimConfigStringList, fieldLocationMap);
      }
      if (oldGoalDimInfo.getSubGoalDimRule() != null) {
        if (oldGoalDimInfo.getLookupField() != null) {
          JoinRelation joinRelation = this.createRelation(tenantId, standalone, aggApiName,
            oldGoalDimInfo.getLookupField(), oldGoalDimInfo.getSubGoalObjName(), cachedTableDefinitions,
            AggJoinType.LEFT);
          if (joinRelation == null) {
            log.error("can not find master obj lookup field tenantId:{},slave apiName:{}", tenantId, themeApiName);
            return null;
          }
          NodeTable masterNodeTable = this.appendLookUpNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper,
            joinRelation);
          this.buildDims(tenantId, standalone, Lists.newArrayList(oldGoalDimInfo.getSubGoalDimRule()),
            masterNodeTable, aliasMapper, dimFieldAliasMapper, dimsMapper, dimConfigStringList, fieldLocationMap);
        } else {
          this.buildDims(tenantId, standalone, Lists.newArrayList(oldGoalDimInfo.getSubGoalDimRule()),
            aggObjNodeTable, aliasMapper, dimFieldAliasMapper, dimsMapper, dimConfigStringList, fieldLocationMap);
        }
      }
      this.joinQuoteNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.timeRule, false, false);
      fieldLocationMap.put(Constants.ACTION_DATE, Constants.ACTION_DATE);
      this.joinQuoteNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.valueRule, false, false);
      String aggColumnName = aggRule.valueRule.createAggColumnName(aggAliasMapper);
      fieldLocationMap.put(oldGoalDimInfo.getViewId(), aggColumnName);//采用goal_rule id作为完成值的映射id
      List<WhereConfig> preWhereConfigList = this.createPreWhereConfigList(tenantId, aggObjNodeTable,
        aggRule.valueRule);
      //反查goal_value 表数据
      this.addGoalValueQueryFilter(tenantId, ruleInfo, preWhereConfigList, goalRuleDO, dimConfigStringList,
        fieldLocationMap, goalValueIds);
      List<List<WhereConfig>> whereConfigList = this.dealWithWhereConfig(tenantId, standalone, aggObjNodeTable,
        aliasMapper, aggRule.whereRules, timeZone);
      TopologyTableAggRule statRule = new TopologyTableAggRule(goalRuleId, TopologyTableStatus.Prepared.getValue(),
        aggObjNodeTable, dimConfigStringList, aggRule.timeRule.createActionDateConfig(), Lists.newArrayList(
        aggRule.valueRule.createAggConfigString() + ":" +
          aggColumnName), whereConfigList, preWhereConfigList, fieldLocationMap, null,
              null, null, null, null);
      TopologyTable oldTopologyTable = new TopologyTable();
      oldTopologyTable.setViewId(oldGoalDimInfo.getViewId());
      oldTopologyTable.setTenantId(tenantId);
      oldTopologyTable.setTimezone(timeZone);
      oldTopologyTable.setApiName(themeApiName);
      oldTopologyTable.setStatRuleList(Lists.newArrayList(statRule));
      oldTopologyTable.setCommonDimList(dimConfigStringList.stream().map(dim -> dim.split(":")[2]).toList());
      statRule.setFieldLocationMap(statRule.getFieldLocationMap());
      String DBName = chTenantPolicy.getDBName(tenantId,null);
      if (StringUtils.isBlank(DBName)) {
        throw new RuntimeException(String.format("can not find DBName tenantId:%s", tenantId));
      }
      oldTopologyTable.setDatabase(DBName);
      goalStatViews.add(oldTopologyTable);
    }
    return goalStatViews;
  }

  /**
   * 增加反查goal_value表逻辑
   *
   * @param tenantId            租户id
   * @param aggRuleInfo         目标Agg计算规则
   * @param preWhereConfigList  目标规则全局筛选条件集合
   * @param goalRuleDO          目标规则对象
   * @param dimConfigStringList 维度字段配置集合
   * @param fieldLocationMap    维度和指标映射关系
   */
  public void addGoalValueQueryFilter(String tenantId,
                                      JSONObject aggRuleInfo,
                                      List<WhereConfig> preWhereConfigList,
                                      GoalRuleDO goalRuleDO,
                                      List<String> dimConfigStringList,
                                      Map<String, String> fieldLocationMap,
                                      List<String> goalValueIds) {
    String themeApiName = aggRuleInfo.getString("theme_api_name");
    if ("goal_value_obj".equalsIgnoreCase(themeApiName)) {
      themeApiName = Constants.employee_api_name;
    }
    String goalRuleId = goalRuleDO.getId();
    String checkLevelType = goalRuleDO.getCheckLevelType();
    String checkLevelFieldApiName = goalRuleDO.getCheckLevelFieldApiName();
    CheckLevelType checkLevelTypeEnum = CheckLevelType.parseFromValue(
      checkLevelType == null ? null : Integer.parseInt(checkLevelType));
    switch (checkLevelTypeEnum) {
      case type1 -> {
        if (Constants.employee_api_name.equals(themeApiName)) {
          String querySQL = String.format(FILTER_SQL_NO_DETAIL, tenantId, goalRuleId);
          querySQL = querySQL + " AND goal_type = '2'";
          WhereConfig whereConfig = createGoalValueWhereConfig(goalRuleDO, querySQL, Constants.CHECK_LEVEL_CODE_2,
            fieldLocationMap, dimConfigStringList, goalValueIds);
          preWhereConfigList.add(whereConfig);
        } else if (Constants.department_api_name.equals(themeApiName)) {
          String querySQL = String.format(FILTER_SQL_NO_DETAIL, tenantId, goalRuleId);
          querySQL = querySQL + " AND goal_type = '1'";
          WhereConfig whereConfig = createGoalValueWhereConfig(goalRuleDO, querySQL, Constants.OBJECT_ID,
            fieldLocationMap, dimConfigStringList, goalValueIds);
          preWhereConfigList.add(whereConfig);
        }
      }
      case type2 -> {
        String querySQL = String.format(FILTER_SQL_NO_DETAIL, tenantId, goalRuleId);
        querySQL = querySQL + " AND check_level_field_api_name='" + checkLevelFieldApiName + "'";
        WhereConfig whereConfig = createGoalValueWhereConfig(goalRuleDO, querySQL, Constants.OBJECT_ID,
          fieldLocationMap, dimConfigStringList, goalValueIds);
        preWhereConfigList.add(whereConfig);
      }
      case type5 -> {
        String querySQL = String.format(FILTER_SQL_NO_DETAIL, tenantId, goalRuleId);
        querySQL = querySQL + " AND check_level_field_api_name='" + checkLevelFieldApiName + "'";
        WhereConfig whereConfig = createGoalValueWhereConfig(goalRuleDO, querySQL, Constants.CHECK_LEVEL_CODE_1,
          fieldLocationMap, dimConfigStringList, goalValueIds);
        preWhereConfigList.add(whereConfig);
      }
      case type6 -> {
        String querySQL = String.format(FILTER_SQL_NO_DETAIL, tenantId, goalRuleId);
        WhereConfig whereConfig = createGoalValueWhereConfig(goalRuleDO, querySQL, Constants.CHECK_LEVEL_CODE_2,
          fieldLocationMap, dimConfigStringList, goalValueIds);
        preWhereConfigList.add(whereConfig);
      }
      default -> {
        throw new RuntimeException(String.format("can not support this checkLevelType:%s," +
          "tenantId:%s,goalRuleId:%s", checkLevelType, goalRuleDO.getTenantId(), goalRuleDO.getId()));
      }
    }
  }

  /**
   * 创建反查 goal_value 表的sql来计算目标完成值
   *
   * @param goalRuleDO          目标规则实体bean
   * @param queryGoalValueSQL   查询goal_value表的基本sql
   * @param checkLevelCodeName  考核字段别名
   * @param fieldLocationMap    槽位字段映射关系
   * @param dimConfigStringList 目标完成值维度字段配置集合
   * @param goalValueIds        目标值id集合，用于计算新增的目标值
   * @return
   */
  public WhereConfig createGoalValueWhereConfig(GoalRuleDO goalRuleDO,
                                                String queryGoalValueSQL,
                                                String checkLevelCodeName,
                                                Map<String, String> fieldLocationMap,
                                                List<String> dimConfigStringList,
                                                List<String> goalValueIds) {
    String dstColumnName = fieldLocationMap.get(checkLevelCodeName);
    if (StringUtils.isBlank(dstColumnName)) {
      throw new RuntimeException(String.format("find %s column error " +
        "tenantId:%s,goalRuleId:%s", checkLevelCodeName, goalRuleDO.getTenantId(), goalRuleDO.getId()));
    }
    Map<String/*dstColumn*/, String> dstColumnConfigMapper = dimConfigStringList.stream()
                                                                                .collect(Collectors.toMap(item -> item.split(":")[2], Function.identity()));
    String dimConfigStr = dstColumnConfigMapper.get(dstColumnName);
    if (CollectionUtils.isNotEmpty(goalValueIds)) {
      String ids = goalValueIds.stream().collect(Collectors.joining(",", "'", "'"));
      queryGoalValueSQL = queryGoalValueSQL + " AND id IN (" + ids + ")";
    }
    DimConfig dimConfig = DimConfig.parse(dimConfigStr, goalRuleDO.timeZone);
    String inSQL = String.format("%s.%s IN ( %s )", dimConfig.getTableAlias(), dimConfig.getColumnName(),
      queryGoalValueSQL);
    return WhereConfig.builder()
                      .tableAlias(dimConfig.getTableAlias())
                      .column(dimConfig.getColumnName())
                      .whereExpr(inSQL)
                      .build();
  }

  /**
   * 根据目标规则id生成 规则和维度的
   *
   * @param tenantId                 租户id
   * @param goalRuleDO               目标规则
   * @param tableColumnDefinitionMap 表元数据cache
   * @return
   */
  public List<Pair<JSONObject/*目标规则*/, OldGoalDimInfo>> transGoal2MultiDimGoalRule(String tenantId,
                                                                                       GoalRuleDO goalRuleDO,
                                                                                       Map<String, Map<String,
                                                                                         ColumnDefinition>> tableColumnDefinitionMap) {
    String goalRuleId = goalRuleDO.getId();
    String checkLevelFieldApiName = goalRuleDO.getCheckLevelFieldApiName();
    if (StringUtils.isBlank(checkLevelFieldApiName)) {
      log.error("this goal rule check level fieldApi name is null tenantId:{},ruleId:{}", tenantId, goalRuleId);
      return null;
    }
    String paasSubGoalObjApiName = goalRuleDO.getSubgoalObjectApiName();
    String paasSubGoalFieldApiName = goalRuleDO.getSubgoalFieldApiName();
    String aggRuleId = goalRuleId + "|" + Constants.NO_GOAL_DETAIL;
    if (StringUtils.isNotBlank(paasSubGoalObjApiName) && StringUtils.isNotBlank(paasSubGoalFieldApiName)) {
      List<GoalRuleDetailDO> goalRuleDetailDOList = goalRuleDetailMapper.setTenantId(tenantId)
                                                                        .queryByTenantIdAndRuleId(tenantId, goalRuleId);
      if (CollectionUtils.isNotEmpty(goalRuleDetailDOList)) {
        aggRuleId = goalRuleId + "|" + goalRuleDetailDOList.get(0).getId();
      }
    }
    List<Pair<JSONObject, OldGoalDimInfo>> oldGoalDimInfoList = Lists.newArrayList();
    Map<String, Object> aggRuleInfoMapper = this.findAggRuleMapFromDB(tenantId, aggRuleId);
    if (MapUtils.isEmpty(aggRuleInfoMapper)) {
      log.error("can not find goal agg_rule tenantId:{},ruleId:{}", tenantId, aggRuleId);
      return null;
    }
    JSONObject aggRuleInfo = new JSONObject(aggRuleInfoMapper);
    Pair<JSONObject, OldGoalDimInfo> goalDimInfoPair = this.createGoalDimInfo(tenantId, aggRuleId, goalRuleId,
      aggRuleInfo, goalRuleDO, tableColumnDefinitionMap);
    oldGoalDimInfoList.add(goalDimInfoPair);
    String checkLevelType = goalRuleDO.getCheckLevelType();
    CheckLevelType checkLevelTypeEnum = CheckLevelType.parseFromValue(
      checkLevelType == null ? null : Integer.parseInt(checkLevelType));
    //数据主属部门的话，需要计算一个部门主题的规则
    if (checkLevelTypeEnum == CheckLevelType.type1 && "data_own_department".equals(goalRuleDO.getDeptFieldApiName())) {
      aggRuleId = aggRuleId + "_dept";
      Map<String, Object> deptAggRuleInfoMapper = this.findAggRuleMapFromDB(tenantId, aggRuleId);
      if (!MapUtils.isEmpty(deptAggRuleInfoMapper)) {
        JSONObject deptAggRuleInfo = new JSONObject(deptAggRuleInfoMapper);
        Pair<JSONObject, OldGoalDimInfo> deptGoalDimInfoPair = this.createGoalDimInfo(tenantId, aggRuleId,
          goalRuleId + "_dept", deptAggRuleInfo, goalRuleDO, tableColumnDefinitionMap);
        oldGoalDimInfoList.add(deptGoalDimInfoPair);
      } else {
        log.error("can not find goal agg_rule tenantId:{},ruleId:{}", tenantId, aggRuleId);
      }
    }
    return oldGoalDimInfoList;
  }

  /**
   * 生成所有维度字段，包含子目标做为维度
   *
   * @param tenantId                 租户id
   * @param aggRuleId                agg_rule id
   * @param viewId                   goal_rule id 做为view_id
   * @param aggRuleInfo              aggRule json 对象
   * @param goalRuleDO               goal_rule 实体bean
   * @param tableColumnDefinitionMap 字段类型和表映射关系缓存
   * @return
   */
  public Pair<JSONObject, OldGoalDimInfo> createGoalDimInfo(String tenantId,
                                                            String aggRuleId,
                                                            String viewId,
                                                            JSONObject aggRuleInfo,
                                                            GoalRuleDO goalRuleDO,
                                                            Map<String, Map<String, ColumnDefinition>> tableColumnDefinitionMap) {
    String paasSubGoalObjApiName = goalRuleDO.getSubgoalObjectApiName();
    String paasSubGoalFieldApiName = goalRuleDO.getSubgoalFieldApiName();
    String themeApiName = aggRuleInfo.getString("theme_api_name");
    if (Objects.equals("goal_value_obj", themeApiName)) {
      themeApiName = "org_employee_user";
    }
    String aggApiName = aggRuleInfo.getString("check_object_api_name");
    String checkLevelType = goalRuleDO.getCheckLevelType();
    CheckLevelType checkLevelTypeEnum = CheckLevelType.parseFromValue(
      checkLevelType == null ? null : Integer.parseInt(checkLevelType));
    List<DisplayField> displayFields = this.createGoalDisplayFields(tenantId, themeApiName, checkLevelTypeEnum,
      goalRuleDO.getCheckLevelFieldApiName());
    if (aggRuleId.contains(Constants.NO_GOAL_DETAIL)) {
      List<DimRule> themeDimRules = dimRuleDao.createDimRule(tenantId, themeApiName, displayFields,
        tableColumnDefinitionMap);
      return Pair.build(aggRuleInfo, OldGoalDimInfo.builder().viewId(viewId).themeDimRules(themeDimRules).build());
    }
    String biSubGoalObjApiName = mappingService.biApiName(paasSubGoalObjApiName);
    OldGoalDimInfo.OldGoalDimInfoBuilder oldGoalDimInfoBuilder = OldGoalDimInfo.builder();
    Pair<UdfObjFieldDO, DimRule> dimRulePair = this.createSubGoalDimRule(tenantId, goalRuleDO,
      tableColumnDefinitionMap);
    if (dimRulePair != null) {
      oldGoalDimInfoBuilder.lookupField(dimRulePair.first)
                           .subGoalObjName(biSubGoalObjApiName)
                           .subGoalDimRule(dimRulePair.second);
    }
    List<DimRule> themeDimRules = dimRuleDao.createDimRule(tenantId, themeApiName, displayFields,
      tableColumnDefinitionMap);
    oldGoalDimInfoBuilder.themeDimRules(themeDimRules);
    oldGoalDimInfoBuilder.viewId(viewId);
    this.removeSubGoalFilter(tenantId, aggRuleInfo, goalRuleDO);
    return Pair.build(aggRuleInfo, oldGoalDimInfoBuilder.build());
  }

  /**
   * 排除掉wheres 条件中 子目标筛选条件
   *
   * @param tenantId
   * @param aggRuleInfo
   * @param goalRuleDO
   */
  public void removeSubGoalFilter(String tenantId, JSONObject aggRuleInfo, GoalRuleDO goalRuleDO) {
    String paasSubGoalObjApiName = goalRuleDO.getSubgoalObjectApiName();
    String paasSubGoalFieldApiName = goalRuleDO.getSubgoalFieldApiName();
    if (StringUtils.isBlank(paasSubGoalObjApiName) || StringUtils.isBlank(paasSubGoalFieldApiName)) {
      log.info("this goal have no subGoal tenantId:{},ruleId:{}", tenantId, goalRuleDO.getId());
      return;
    }
    String biSubGoalObjApiName = mappingService.biApiName(paasSubGoalObjApiName);
    String aggApiName = aggRuleInfo.getString("check_object_api_name");
    String wheresString = aggRuleInfo.getString("wheres");
    JSONArray wheresInfo = JSON.parseArray(wheresString);
    if (StringHelper.isNullOrBlank(wheresString)) {
      log.error("have subGoal but have no wheres tenantId:{},ruleId:{}", tenantId, goalRuleDO.getId());
      return;
    }
    String subGoalObjJoinFieldId = goalRuleDO.getSubgoalObjectJoinFieldId();
    if (StringUtils.isNotBlank(subGoalObjJoinFieldId)) {
      subGoalObjJoinFieldId = subGoalObjJoinFieldId.contains(Constants.whatFieldKeyWord) ?
        subGoalObjJoinFieldId.substring(0, subGoalObjJoinFieldId.indexOf(Constants.whatFieldKeyWord)) :
        subGoalObjJoinFieldId;
      for (Object whereListInfo : wheresInfo) {
        JSONArray filters = ((JSONObject) whereListInfo).getJSONArray("filters");
        JSONObject subGoalFilter = (JSONObject) filters.get(filters.size() - 1);
        //被引用对象
        String targetApiName = subGoalFilter.getString("dbObjName");
        //字段名称
        String lookupFieldName = subGoalFilter.getString("dbFieldName");
        String parentDbObjName = subGoalFilter.getString("parentDbObjName");
        String parentFieldId = subGoalFilter.getString("parentFieldId");
        if (Objects.equals(biSubGoalObjApiName, targetApiName) &&
          Objects.equals(paasSubGoalFieldApiName, lookupFieldName) && Objects.equals(aggApiName, parentDbObjName) &&
          Objects.equals(subGoalObjJoinFieldId, parentFieldId)) {
          filters.remove(subGoalFilter);
        }
      }
      aggRuleInfo.put("wheres", wheresInfo.toJSONString());
    } else {
      UdfObjFieldDO subGoalField = udfObjFieldMapper.setTenantId(tenantId)
                                                    .queryFieldByObjNameAndDbFieldName(paasSubGoalFieldApiName,
                                                      biSubGoalObjApiName,
                                                      mappingService.getApiXName(biSubGoalObjApiName),
                                                      Integer.parseInt(tenantId));
      if (subGoalField == null) {
        log.error("can not find subGoal field by apiName tenantId:{},subGoalObjApiName:{},subGoalfieldApiName:{}",
          tenantId, biSubGoalObjApiName, paasSubGoalFieldApiName);
        return;
      }
      for (Object whereListInfo : wheresInfo) {
        JSONArray filters = ((JSONObject) whereListInfo).getJSONArray("filters");
        JSONObject subGoalFilter = (JSONObject) filters.get(filters.size() - 1);
        //被引用对象
        String targetApiName = subGoalFilter.getString("dbObjName");
        //字段名称
        String dbFieldName = subGoalFilter.getString("dbFieldName");
        String fieldId = subGoalFilter.getString("fieldID");
        if (Objects.equals(biSubGoalObjApiName, targetApiName) &&
          Objects.equals(paasSubGoalFieldApiName, dbFieldName) && Objects.equals(fieldId, subGoalField.getFieldId())) {
          filters.remove(subGoalFilter);
        }
      }
      aggRuleInfo.put("wheres", wheresInfo.toJSONString());
    }
  }

  /**
   * 获取目标主题对象所有需要的维度字段
   *
   * @param tenantId               租户id
   * @param themeApiName           主题对象apiName
   * @param checkLevelTypeEnum     考核层级类型
   * @param checkLevelFieldApiName 考核维度字段
   * @return
   */
  public List<DisplayField> createGoalDisplayFields(String tenantId,
                                                    String themeApiName,
                                                    CheckLevelType checkLevelTypeEnum,
                                                    String checkLevelFieldApiName) {
    List<DisplayField> displayFields = Lists.newArrayList();
    switch (checkLevelTypeEnum) {
      case type1 -> {
        DisplayField objectIdField = this.createObjectId(tenantId, themeApiName);
        if ("org_dept".equals(themeApiName)) {
          objectIdField.setCheckLevelField(true);
          displayFields.add(objectIdField);
        }
        if ("org_employee_user".equals(themeApiName) && StringUtils.isNotBlank(checkLevelFieldApiName)) {
          displayFields.addAll(this.getCheckLevelFields(themeApiName, checkLevelFieldApiName, checkLevelTypeEnum));
        }
      }
      case type2 -> {
        DisplayField objectIdField = this.createObjectId(tenantId, themeApiName);
        objectIdField.setCheckLevelField(true);
        displayFields.add(objectIdField);
      }
      default -> {
        displayFields.addAll(this.getCheckLevelFields(themeApiName, checkLevelFieldApiName, checkLevelTypeEnum));
      }
    }
    if (StringUtils.isNotBlank(goalFixDisplayField)) {
      List<DisplayField> fixDisplayFields = JSON.parseObject(goalFixDisplayField, new TypeReference<>() {
      });
      if (CollectionUtils.isNotEmpty(fixDisplayFields)) {
        displayFields.addAll(fixDisplayFields.stream()
                                             .peek(displayField -> displayField.setDescribeApiName(themeApiName))
                                             .toList());
      }
    }
    return displayFields;
  }

  /**
   * 获取主属性字段
   *
   * @param tenantId
   * @param themeApiName
   * @return
   */
  public DisplayField createObjectId(String tenantId, String themeApiName) {
    //普通主题计算方式 包含部门主题
    String dbFieldName = Constants.getIdName(this.standalone(tenantId), themeApiName);
    if (!this.standalone(tenantId) && themeApiName.endsWith("__c")) {
      dbFieldName = "value0";
    }
    return DisplayField.builder()
                       .describeApiName(themeApiName)
                       .dbFieldName(dbFieldName)
                       .statFieldId("object_id")
                       .dstColumnName("object_id")
                       .build();
  }

  /**
   * 获取所有考核维度字段
   * 考核人员的目标完成值不用过滤goal_value表
   *
   * @param themeApiName           目标主题所属字段
   * @param checkLevelFieldApiName 考核层级字段
   * @return
   */
  public List<DisplayField> getCheckLevelFields(String themeApiName,
                                                String checkLevelFieldApiName,
                                                CheckLevelType checkLevelTypeEnum) {
    List<DisplayField> displayFields = Lists.newArrayList();
    List<String> checkLevelFields = Splitter.on("|").splitToList(checkLevelFieldApiName);
    for (int i = 0; i < checkLevelFields.size(); i++) {
      String dbFieldName = checkLevelFields.get(i);
      DisplayField displayField = DisplayField.builder()
                                              .describeApiName(themeApiName)
                                              .dbFieldName(dbFieldName)
                                              .checkLevelField(checkLevelTypeEnum != CheckLevelType.type1 &&
                                                (i + 1) == checkLevelFields.size())
                                              .statFieldId("check_level_code_" + (i + 1))
                                              .build();
      displayFields.add(displayField);
    }
    return displayFields;
  }


  /**
   * 创建子目标维度
   *
   * @param tenantId               租户id
   * @param goalRuleDO             目标规则对象
   * @param cachedTableDefinitions 字段cache
   * @return
   */
  public Pair<UdfObjFieldDO, DimRule> createSubGoalDimRule(String tenantId,
                                                           GoalRuleDO goalRuleDO,
                                                           Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    String paasSubGoalObjApiName = goalRuleDO.getSubgoalObjectApiName();
    String paasSubGoalFieldApiName = goalRuleDO.getSubgoalFieldApiName();
    if (StringUtils.isBlank(paasSubGoalObjApiName) || StringUtils.isBlank(paasSubGoalFieldApiName)) {
      log.info("this goal have no subGoal tenantId:{},ruleId:{}", tenantId, goalRuleDO.getId());
      return null;
    }
    String biSubGoalObjApiName = mappingService.biApiName(paasSubGoalObjApiName);
    UdfObjFieldDO subGoalField = udfObjFieldMapper.setTenantId(tenantId)
                                                  .queryFieldByObjNameAndDbFieldName(paasSubGoalFieldApiName,
                                                    biSubGoalObjApiName,
                                                    mappingService.getApiXName(biSubGoalObjApiName),
                                                    Integer.parseInt(tenantId));
    if (subGoalField == null) {
      log.error("can not find subGoal field by apiName tenantId:{},subGoalObjApiName:{},subGoalfieldApiName:{}",
        tenantId, biSubGoalObjApiName, paasSubGoalFieldApiName);
      return null;
    }
    String subGoalObjJoinFieldId = goalRuleDO.getSubgoalObjectJoinFieldId();
    if (StringUtils.isNotBlank(subGoalObjJoinFieldId)) {
      subGoalObjJoinFieldId = subGoalObjJoinFieldId.contains(Constants.whatFieldKeyWord) ?
        subGoalObjJoinFieldId.substring(0, subGoalObjJoinFieldId.indexOf(Constants.whatFieldKeyWord)) :
        subGoalObjJoinFieldId;
      UdfObjFieldDO checkObjLookupSubGoal = udfObjFieldMapper.setTenantId(tenantId)
                                                             .findFieldInfoByFieldId(tenantId, subGoalObjJoinFieldId);
      if (checkObjLookupSubGoal == null) {
        log.error("can not find lookup sub goal udf_obj_field tenantId:{},fieldId:{}", tenantId, subGoalObjJoinFieldId);
        return null;
      }
      DimRule dimRule = dimRuleDao.buildDimRule(tenantId, biSubGoalObjApiName, subGoalField, cachedTableDefinitions,
        paasSubGoalFieldApiName, null, DisplayField.DisplayType.group);
      return Pair.build(checkObjLookupSubGoal, dimRule);
    } else {
      DimRule dimRule = dimRuleDao.buildDimRule(tenantId, biSubGoalObjApiName, subGoalField, cachedTableDefinitions,
        paasSubGoalFieldApiName, null, DisplayField.DisplayType.group);
      return Pair.build(null, dimRule);
    }
  }

}
