package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import com.fxiaoke.bi.warehouse.dws.transform.model.BiMtDimRule;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "bi_mt_dimension")
@Data
public class BIMtDimensionDO {

  @Column(name = "dimension_id")
  private String dimensionId;

  @Column(name = "tenant_id")
  private String tenantId;

  @Column(name = "topology_describe_id")
  private String topologyDescribeId;

  @Column(name = "dimension_field")
  private String dimensionField;

  @Column(name = "field_id")
  private String fieldId;

  @Column(name = "dimension_type")
  private String dimensionType;

  @Column(name = "dimension_name")
  private String dimensionName;

  @Column(name = "dimension_config")
  private String dimensionConfig;

  @Column(name = "status")
  private Integer status;

  @Column(name = "describe_api_names")
  private String[] describeApiNames;

  @Column(name = "description")
  private String description;

  @Column(name = "disable_reason")
  private String disableReason;

  @Column(name = "create_time")
  private Long createTime;

  @Column(name = "last_modified_time")
  private Long lastModifiedTime;

  @Column(name = "sql_fragment")
  private String sqlFragment;

  @Column(name = "created_by")
  private String createdBy;

  @Column(name = "last_modified_by")
  private String lastModifiedBy;

  @Column(name = "is_deleted")
  private Short isDeleted;

  @Column(name = "describe_api_name")
  private String describeApiName;

  @Column(name = "source_dimension_id")
  private String sourceDimensionId;

  @Column(name = "type")
  private String type;

  @Column(name = "is_hide")
  private Short isHide;

  @Column(name = "relation_level")
  private Integer relationLevel;

  @Column(name = "custom_type")
  private String customType;

  @Column(name = "relation_field_id")
  private String relationFieldId;

  @Column(name = "source_describe_api_name")
  private String sourceDescribeApiName;

  @Column(name = "limit_control_tag")
  private Integer limitControlTag;

  @Column(name = "outbound_tenant_id")
  private String outboundTenantId;

  public BiMtDimRule toMultiDimRule() {
    return BiMtDimRule.builder()
                      .tenantId(tenantId)
                      .dimensionId(dimensionId)
                      .dimensionType(dimensionType)
                      .dimensionField(dimensionField)
                      .fieldId(fieldId)
                      .build();

  }
}
