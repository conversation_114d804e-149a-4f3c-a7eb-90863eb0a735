package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import com.facishare.paas.pod.dto.RouterInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncAggDataContext {

    /**
     * 上游企业路由信息
     */
    private RouterInfo upRouterInfo;

    /**
     * 下游企业路由信息
     */
    private RouterInfo downRouterInfo;

    /**
     * 同步策略policyId
     */
    private String policyId;

    /**
     * 图表viewId
     */
    private String viewId;

    /**
     * 图表statViewUniqueKey
     */
    private String statViewUniqueKey;

    /**
     * 图表版本
     */
    private int version;

    /**
     * 上下游关系objectId
     */
    private String objectId;

    /**
     * 同步的批次号
     */
    private long batchNum;

    /**
     * 同步的时间上次偏移量
     */
    private long lastTimeStamp;

    /**
     * 是否是全量同步
     */
    private boolean isFullSynchronization;

    /**
     * 同步的时间当前偏移量
     */
    private long currentTimeMillis;

    /**
     * 同步图表维度字段
     */
    private List<SyncDimContext> syncDimContextList;

    /**
     * 同步图表筛选字段
     */
    private List<SyncFilterContext> syncFilterContextList;

    /**
     * 同步图表指标字段
     */
    private Map<String, String> downToUpMap;

    /**
     * 同步的hash_code
     */
    private String hashCode;

    public String findMustUrl() {
        if (upRouterInfo != null) {
            return "jdbc:clickhouse://"+upRouterInfo.getMasterProxyUrl()+"/"+upRouterInfo.getDbName();
        }
        return null;
    }
}
