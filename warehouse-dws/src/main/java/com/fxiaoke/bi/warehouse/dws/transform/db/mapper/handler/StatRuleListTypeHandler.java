package com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class StatRuleListTypeHandler extends BaseTypeHandler<List<TopologyTableAggRule>> {
  @Override
  public void setNonNullParameter(PreparedStatement ps, int i, List<TopologyTableAggRule> parameter, JdbcType jdbcType) throws SQLException {
    ps.setString(i, JSON.toJSONString(parameter));
  }

  @Override
  public List<TopologyTableAggRule> getNullableResult(ResultSet rs, String columnName) throws SQLException {
    String st = rs.getString(columnName);
    return JSON.parseArray(st, TopologyTableAggRule.class);
  }

  @Override
  public List<TopologyTableAggRule> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
    String st = rs.getString(columnIndex);
    return JSON.parseArray(st, TopologyTableAggRule.class);
  }

  @Override
  public List<TopologyTableAggRule> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
    String st = cs.getString(columnIndex);
    return JSON.parseArray(st, TopologyTableAggRule.class);
  }
}
