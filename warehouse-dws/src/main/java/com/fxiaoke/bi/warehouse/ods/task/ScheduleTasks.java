package com.fxiaoke.bi.warehouse.ods.task;

import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.helper.ConfigHelper;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Author:jief
 * @Date:2023/5/24
 */
@Slf4j
@EnableScheduling
@Configuration
public class ScheduleTasks {
  @Resource
  private PgCommonDao pgCommonDao;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  private final AtomicBoolean run = new AtomicBoolean(true);
  private volatile int DELAY_MS = 60*1000;
  private static final int SCAN_PAGE_SIZE = 500;
  private static final String PG_CH_SYNC_BLOCK_KEY = "bi_pg_to_ch_sync_block_key";
  private JedisLock jedisLock;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
      String runStatus = config.get("pg.sync.status", "running");
      String skipProfiles = config.get("skipProfiles", "fstest-gray,foneshare-gray");
      String[] profiles = skipProfiles.split(",");
      run.set("running".equals(runStatus));
      if (StringUtils.equalsAny(ConfigHelper.getProcessInfo().getProfile(), profiles)) {
        run.set(false);
      }
      DELAY_MS = config.getInt("pg.sync.delay.ms", 60*1000);
      if(jedisLock!=null){
        jedisLock.unlock();
      }
      jedisLock = new JedisLock(jedisCmd, PG_CH_SYNC_BLOCK_KEY, DELAY_MS);
    });
  }

  /**
   * 每隔1分钟检测一次，具体是否执行还需要获取分布式锁。
   */
  @Scheduled(initialDelay = 60 * 1000, fixedRate = 1000 * 60)
  public void runScheduleFixedRate() {
    long start = System.currentTimeMillis();
    if (run.get()) {
      try {
        //在这儿没有加上 close() 就是为了保持多pod能在固定的频率执行
        if (jedisLock.tryLock()) {
          int size = this.pgCommonDao.triggerTransfer(SCAN_PAGE_SIZE);
          log.info("success triggerTransfer event size:{}, cost:{} ms", size, (System.currentTimeMillis() - start));
        }
      } catch (Exception e) {
        log.error("triggerTransfer event error", e);
      }
    } else {
      log.warn("trigger transfer task has stop!");
    }
  }

  @PreDestroy
  public void stopTask() {
    if (jedisLock != null) {
      jedisLock.unlock();
    }
    log.info("delete block key:{}", PG_CH_SYNC_BLOCK_KEY);
  }
}
