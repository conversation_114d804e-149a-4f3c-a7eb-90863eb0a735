package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 20240328
 * @desc 下游企业agg_data同步信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggDataSyncInfo {

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 统计图图表id
     */
    private String viewId;

    /**
     * 统计图版本
     */
    private int viewVersion;

    /**
     * 同步批次
     */
    private Long batchNum;

    /**
     * 同步状态
     */
    private Integer status;

    /**
     * 企业图表的agg_data的最后一次时间
     */
    private Long maxSyncTime;

    /**
     * 同步信息创建时间
     */
    private Date timestamp;

    /**
     * 0 没有删除 1 删除
     */
    private Integer isDeleted;
}
