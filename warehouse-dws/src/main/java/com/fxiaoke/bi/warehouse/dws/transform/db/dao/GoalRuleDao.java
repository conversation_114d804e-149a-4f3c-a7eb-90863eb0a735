package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.db.er.NodeJoin;
import com.fxiaoke.bi.warehouse.common.db.er.NodeOnCondition;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.common.goal.Edge;
import com.fxiaoke.bi.warehouse.common.goal.Node;
import com.fxiaoke.bi.warehouse.common.goal.OnField;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.ObjectConfigManager;
import com.fxiaoke.bi.warehouse.common.util.TopologyUtils;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import com.fxiaoke.bi.warehouse.dws.model.DimConfig;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableStatus;
import com.fxiaoke.bi.warehouse.dws.model.WhereConfig;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtTopologyDesDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.GoalRuleDetailDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper.GoalRuleDetailMapper;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.common.Pair;
import com.fxiaoke.helper.CollectionHelper;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author:jief
 * @Date:2023/5/10
 */
@Service
@Slf4j
public class GoalRuleDao extends RuleDao {
  @Autowired
  private GoalRuleDetailMapper goalRuleDetailMapper;
  @Autowired
  private DimRuleDao dimRuleDao;
  @Autowired
  private UdfObjFieldMapper udfObjFieldMapper;
  @Resource
  private MappingService mappingService;
  @Resource
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Resource
  private DimSysDateDao dimSysDateDao;
  @Resource
  private FiscalDateConvertDao fiscalDateConvertDao;
  //目标完成值需要扩展的维度
  private Set<String> extDimField = Sets.newHashSet();
  private Set<String> chAggDataFixSlot = Sets.newHashSet();

  /**
   * {@see <a href='https://oss.foneshare.cn/cms/edit/config/17121'>bi-statictic-agg</a>}
   */
  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", iConfig -> {
      String chAggFixSlot = iConfig.get("ch.agg.fix.slot", "owner,data_auth_code");
      chAggDataFixSlot = Sets.newHashSet(Splitter.on(",").splitToList(chAggFixSlot));
      String goalExtDisplayField = iConfig.get("goal_displayField", "[]");
      JSONArray extField = JSON.parseArray(goalExtDisplayField);
      Set<String> extDimFieldTmp = Sets.newHashSet();
      extField.forEach(field -> {
        JSONObject fieldObj = (JSONObject) field;
        extDimFieldTmp.add(fieldObj.getString("dbFieldName"));
      });
      extDimField = extDimFieldTmp;
    });
  }

  /**
   * 查询子目标规则
   *
   * @param tenantId
   * @param goalRuleId
   * @return
   */
  public List<GoalRuleDetailDO> findSubGoalRuleById(String tenantId, String goalRuleId, String subGoalRuleId) {
    List<GoalRuleDetailDO> goalRuleDetailDOList;
    if (StringUtils.isBlank(subGoalRuleId)) {
      goalRuleDetailDOList = goalRuleDetailMapper.setTenantId(tenantId).queryByTenantIdAndRuleId(tenantId, goalRuleId);
    } else {
//      subGoalRuleId = goalRuleId.endsWith("_dept") ? subGoalRuleId : subGoalRuleId.replace("_dept","");
      goalRuleDetailDOList = goalRuleDetailMapper.setTenantId(tenantId).queryByTenantIdAndSubRuleId(tenantId, goalRuleId, subGoalRuleId);
    }
    return goalRuleDetailDOList;
  }

  /**
   * 通过查询biMtTopologyDescribe查询子目标规则
   * 因为子目标id还有_dept结尾的，不能直接查goalRuleDetail表
   * @param tenantId
   * @param goalRuleId
   * @param subGoalRuleId 父规则|子规则
   * @return
   */
  public List<String> findSubGoalRuleIdById(String tenantId, String goalRuleId, String subGoalRuleId) {
    List<String> subGoalRuleIdList = Lists.newArrayList();
    if (StringUtils.isBlank(subGoalRuleId)) {
      subGoalRuleIdList = biMtTopologyDescribeMapper.setTenantId(tenantId).findSubGoalTopologyById(tenantId,
        goalRuleId + "|%");
    } else {
      BIMtTopologyDesDO biMtTopologyDes = this.findBiMtTopologyDes(tenantId, subGoalRuleId, 2);
      if (biMtTopologyDes != null) {
        subGoalRuleIdList.add(biMtTopologyDes.getTopologyDescribeId());
      }
    }
    return subGoalRuleIdList;
  }

  /**
   * 单个规则生成join set
   *
   * @param tenantId            租户id
   * @param biMtRule    目标规则定义
   * @param aggAliasMapper      agg 别名缓存
   * @param dimsMapper          dim 别名缓存
   * @param dimFieldAliasMapper dim别名映射
   * @return
   */
  public List<TopologyTableAggRule> parseFromMap(String tenantId,
                                                 BiMtRule biMtRule,
                                                 Map<String, AtomicInteger> aggAliasMapper,
                                                 Map<String, AtomicInteger> dimsMapper,
                                                 Map<String, String/*dim字段别名*/> dimFieldAliasMapper,
                                                 String checkLevelType,
                                                 Map<String, String> subGoalRuleDimMap) {
    //计算别名
    Map<String, AtomicInteger> aliasMapper = Maps.newHashMap();
    //缓存列类型
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    boolean standalone = mybatisTenantPolicy.standalone(tenantId);
    Map<String, String> fieldLocationMap = Maps.newHashMap();
    List<TopologyTableAggRule> statRuleList = Lists.newArrayList();
    //处理一个拓扑图中多个计算指标
    Map<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> measureRuleMaps =
      biMtRule.createMeasureRuleMapper();
    //计算不同考核对象的指标，相同考核对象的指标并且日期字段也是相同的则在一个statRule中
    for (Map.Entry<Pair<String, String>, List<BiMtMeasureRule>> measureEntry : measureRuleMaps.entrySet()) {
      Node aggNode = biMtRule.findNodeById(measureEntry.getKey().first);
      String aggApiName = mappingService.biApiName(aggNode.getDescribeApiName());
      NodeTable aggObjNodeTable = this.createAggNodeTable(standalone, aggApiName, aliasMapper);
      aggObjNodeTable.setNodeId(aggNode.getId());
      List<DimRule> dimRules = this.buildDimRule(tenantId, standalone, biMtRule);
      List<String> dimConfigStringList = Lists.newArrayList();
      Map<String, DimConfig> fieldDimConfigMapper = Maps.newHashMap();
      if (CollectionUtils.isNotEmpty(dimRules)) {
        dimRules.forEach(dimRule -> {
          this.buildBranch(biMtRule, measureEntry.getKey().first, aggObjNodeTable, aliasMapper,
            dimRule.getDimFieldRule(), cachedTableDefinitions, false);
          DimConfig dimConfig = dimRule.createDimConfig(chAggDataFixSlot, dimFieldAliasMapper, dimsMapper);
          dimConfigStringList.add(dimConfig.toConfigString());
          fieldLocationMap.put(dimRule.getDimFieldRule().fieldId, dimConfig.getDstColumnName());
          fieldDimConfigMapper.put(dimRule.getDimFieldRule().fieldId, dimConfig);
        });
      }
      TimeRule timeRule = this.buildTimeRule(tenantId, standalone, measureEntry.getKey(), biMtRule,
        cachedTableDefinitions);
      this.buildBranch(biMtRule, measureEntry.getKey().first, aggObjNodeTable, aliasMapper, timeRule,
        cachedTableDefinitions, false);
      String actionDateConfigString = timeRule.createActionDateConfig();
      //多值指标
      List<ValueRule> valueRuleList = Lists.newArrayList();
      List<String> aggConfigList = Lists.newArrayList();
      Map<String, NodeTable> valueNodeTableMap = Maps.newHashMap();
      measureEntry.getValue().forEach(multiDimMeasureRule -> {
        ValueRule valueRule = this.buildValueRule(tenantId, standalone, biMtRule, multiDimMeasureRule,
          cachedTableDefinitions);
        NodeTable valueNodeTable = this.buildBranch(biMtRule, measureEntry.getKey().first, aggObjNodeTable,
          aliasMapper, valueRule, cachedTableDefinitions, true);
        valueNodeTableMap.put(valueRule.getMeasureId(), valueNodeTable);
        valueRuleList.add(valueRule);
        String aggConfigString = valueRule.createAggConfigString();
        String aggColumnName = valueRule.createAggColumnName(aggAliasMapper);
        fieldLocationMap.put(valueRule.getMeasureId(), aggColumnName);
        aggConfigList.add(aggConfigString + ":" + aggColumnName);
      });
      //where 条件
      WhereRules whereRules = this.buildWheres(tenantId, standalone, biMtRule, cachedTableDefinitions);
      List<WhereConfig> preWhereConfigList = this.createPreWhereConfigList(tenantId, aggObjNodeTable, null);
      //增加反查goalValue
      preWhereConfigList.add(WhereConfig.builder().whereExpr(" ${queryGoalValue} ").build());
      List<List<WhereConfig>> whereConfigList = this.dealWithWhereConfig(tenantId, standalone, biMtRule,
        measureEntry.getKey().first, aggObjNodeTable, aliasMapper, whereRules, cachedTableDefinitions, true);
      String checkCyCle = biMtRule.getGoalRuleBO().checkCyCle;
      TopologyTableAggRule statRule = new TopologyTableAggRule(biMtRule.goalRuleId(),
        TopologyTableStatus.Prepared.getValue(), aggObjNodeTable, dimConfigStringList, actionDateConfigString,
        aggConfigList, whereConfigList, preWhereConfigList, fieldLocationMap,
        biMtRule.checkFieldLocationMapper(subGoalRuleDimMap, Lists.newArrayList()),null, checkLevelType,
        null, checkCyCle);
      //补充第一维度过滤,用goal_value_obj_tmp的with临时表,谓词下推
      statRule.addFilterFirstGoalValueSQL(tenantId);
      //添加财年过滤-谓词下推
      this.appendFiscalYear(tenantId, timeRule, biMtRule, statRule.getRootNodeTable(), true);
      //检测去重计数列
      this.checkUniqField(tenantId, standalone, valueRuleList, statRule, aliasMapper, aggAliasMapper, dimsMapper,
        dimFieldAliasMapper, dimRules, valueNodeTableMap);
      statRuleList.add(statRule);
      TopologyTableAggRule pgStatRule = this.buildPGStatRule(tenantId, standalone, biMtRule,
        cachedTableDefinitions, aggObjNodeTable, timeRule, measureEntry.getKey().first, aliasMapper, statRule);
      statRuleList.add(pgStatRule);
    }
    return statRuleList;
  }

  public TopologyTableAggRule buildPGStatRule(String tenantId,
                                              boolean standalone,
                                              BiMtRule biMtRule,
                                              Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                                              NodeTable aggObjNodeTable,
                                              TimeRule timeRule,
                                              String measureEntryKeyFirst,
                                              Map<String, AtomicInteger> aliasMapper,
                                              TopologyTableAggRule statRule) {
    WhereRules whereRules = this.buildWheres(tenantId, standalone, biMtRule, cachedTableDefinitions);
    List<WhereConfig> preWhereConfigList = this.createPreWhereConfigList(tenantId, aggObjNodeTable, null);
    //增加反查goalValue
    preWhereConfigList.add(WhereConfig.builder().whereExpr(" ${queryGoalValue} ").build());
    //添加财年过滤
//    if (!GrayManager.isAllowByRule("use_goal_new_calculate", tenantId)) {
//      this.appendFiscalYear(tenantId, timeRule, preWhereConfigList, biMtRule, false);
//    }
    List<List<WhereConfig>> whereConfigList = this.dealWithWhereConfig(tenantId, standalone, biMtRule,
      measureEntryKeyFirst, aggObjNodeTable, aliasMapper, whereRules, cachedTableDefinitions, false);
    List<String> dimConfigStringList = this.dealNewDetailColumn(tenantId, statRule);
    return new TopologyTableAggRule(statRule.getFieldId(), statRule.getStatus(),
            statRule.getRootNodeTable(), dimConfigStringList, timeRule.createDetailActionDateConfig(),
            statRule.getAggConfigStringList(), whereConfigList, preWhereConfigList, statRule.getFieldLocationMap(),
            statRule.getCheckFieldLocationMapper(), statRule.getUniqDimConfigMap(), statRule.getCheckLevelType(),null,
            statRule.getCheckCyCle());
  }

  /**
   * 处理新增列查询,例如生命状态
   */
  public List<String> dealNewDetailColumn(String tenantId, TopologyTableAggRule statRule) {
    List<String> newDimConfigStringList = Lists.newArrayList(statRule.getDimConfigStringList());
    String rootAlias = statRule.getRootNodeTable().getAlias();
    String rootObjApiName = statRule.getRootNodeTable().getObjectDescribeApiName();
    UdfObjFieldDO udfObjField = udfObjFieldMapper.setTenantId(tenantId).findUdfObjFieldDOByFieldName(tenantId,
            rootObjApiName, mappingService.getApiXName(rootObjApiName), "life_status");
    if (udfObjField != null) {
      String fieldLocation = udfObjField.getFieldLocation();
      String dbFieldName = Integer.parseInt(fieldLocation) > 0 ? "value" + fieldLocation : udfObjField.getDbFieldName();
      List<String> newDetailColumnList = newDimConfigStringList.stream()
              .filter(dim -> (dbFieldName.equals(dim.split(":")[1]) && rootAlias.equals(dim.split(":")[0])))
              .toList();
      if (newDetailColumnList.isEmpty()) {
        newDimConfigStringList.add(String.format("%s:%s:%s:%s", rootAlias, dbFieldName, "life_status", "_String:text"));
      }
    }
    return newDimConfigStringList;
  }

  /**
   * 添加财年筛选
   *
   * @param tenantId
   * @param timeRule
   * @param preWhereConfigList
   * @param biMtRule
   */
  public void appendFiscalYear(String tenantId,
                               TimeRule timeRule,
                               List<WhereConfig> preWhereConfigList,
                               BiMtRule biMtRule,
                               boolean isCH) {
    GoalRuleBO goalRuleBO = biMtRule.getGoalRuleBO();
    List<Integer> fiscalYears = goalRuleBO.orderByFiscalYear();
    if (CollectionUtils.isEmpty(fiscalYears)) {
      return;
    }
    Pair<Long, Long> fiscalDate = null;
    if (GrayManager.isAllowByRule("fiscal_from_paas", tenantId) && "month".equals(goalRuleBO.checkCyCle)) {
      try {
        fiscalDate = fiscalDateConvertDao.calFiscalStartAndEndDate(tenantId, biMtRule.timeZone(),
          fiscalYears.get(0), fiscalYears.get(
          fiscalYears.size() - 1), Integer.parseInt(goalRuleBO.getStartMonth()));
      } catch (Exception e) {
        log.error("calFiscalStartAndEndDate error tenantId:{},ruleId:{}", tenantId, goalRuleBO.getId());
      }
    } else {
      fiscalDate = dimSysDateDao.getDimSysDate(tenantId, goalRuleBO);
    }
    if (fiscalDate != null) {
      if (isCH) {
        String firstFiscalWhereExpr = FilterType.LATERTHAN.buildBoolSQL(timeRule.alias, timeRule.column,
                timeRule.alias + "." + timeRule.column, timeRule.columnType, FilterType.LATERTHAN.getType(),
                timeRule.isSingle, String.valueOf(fiscalDate.first), null, null,
          biMtRule.timeZone());
        preWhereConfigList.add(WhereConfig.builder()
                .whereExpr(firstFiscalWhereExpr)
                .build());
        String secondFiscalWhereExpr = FilterType.SOONERTHAN.buildBoolSQL(timeRule.alias, timeRule.column,
                timeRule.alias + "." + timeRule.column, timeRule.columnType, FilterType.SOONERTHAN.getType(),
                timeRule.isSingle, String.valueOf(fiscalDate.second), null, null,
          biMtRule.timeZone());
        preWhereConfigList.add(WhereConfig.builder()
                .whereExpr(secondFiscalWhereExpr)
                .build());
      } else {
        String firstFiscalWhereExpr = PGFilterType.LATERTHAN.buildBoolSQL(timeRule.alias, timeRule.column,
          timeRule.alias + "." + timeRule.column, timeRule.columnType, PGFilterType.LATERTHAN.getType(),
          timeRule.isSingle, String.valueOf(fiscalDate.first), null, null,
          biMtRule.timeZone());
        preWhereConfigList.add(WhereConfig.builder()
                                          .whereExpr(firstFiscalWhereExpr)
                                          .build());
        String secondFiscalWhereExpr = PGFilterType.SOONERTHAN.buildBoolSQL(timeRule.alias, timeRule.column,
          timeRule.alias + "." + timeRule.column, timeRule.columnType, PGFilterType.SOONERTHAN.getType(),
          timeRule.isSingle, String.valueOf(fiscalDate.second), null, null,
          biMtRule.timeZone());
        preWhereConfigList.add(WhereConfig.builder()
                                          .whereExpr(secondFiscalWhereExpr)
                                          .build());
      }
    }
  }

  /**
   * 添加财年筛选
   *
   * @param tenantId
   * @param timeRule
   * @param biMtRule
   */
  public void appendFiscalYear(String tenantId,
                               TimeRule timeRule,
                               BiMtRule biMtRule,
                               NodeTable rootNodeTable,
                               boolean isCH) {
    GoalRuleBO goalRuleBO = biMtRule.getGoalRuleBO();
    List<Integer> fiscalYears = goalRuleBO.orderByFiscalYear();
    if (CollectionUtils.isEmpty(fiscalYears)) {
      return;
    }
    Pair<Long, Long> fiscalDate = null;
    if (GrayManager.isAllowByRule("fiscal_from_paas", tenantId) && "month".equals(goalRuleBO.checkCyCle)) {
      try {
        fiscalDate = fiscalDateConvertDao.calFiscalStartAndEndDate(tenantId, biMtRule.timeZone(),
                fiscalYears.get(0), fiscalYears.get(
                        fiscalYears.size() - 1), Integer.parseInt(goalRuleBO.getStartMonth()));
      } catch (Exception e) {
        log.error("calFiscalStartAndEndDate error tenantId:{},ruleId:{}", tenantId, goalRuleBO.getId());
      }
    } else {
      fiscalDate = dimSysDateDao.getDimSysDate(tenantId, goalRuleBO);
    }
    NodeTable timeTable = Constants.findTableByAlias(rootNodeTable, timeRule.alias);
    if (fiscalDate != null) {
      //处理CH
      String firstFiscalWhereExpr = FilterType.LATERTHAN.buildBoolSQL(timeRule.tableName, timeRule.column,
              timeRule.column, timeRule.columnType, FilterType.LATERTHAN.getType(), timeRule.isSingle,
              String.valueOf(fiscalDate.first), null, null, biMtRule.timeZone());
      timeTable.appendSubWheres(String.format(" AND %s ", firstFiscalWhereExpr));
      String secondFiscalWhereExpr = FilterType.SOONERTHAN.buildBoolSQL(timeRule.tableName, timeRule.column,
              timeRule.column, timeRule.columnType, FilterType.SOONERTHAN.getType(), timeRule.isSingle,
              String.valueOf(fiscalDate.second), null, null, biMtRule.timeZone());
      timeTable.appendSubWheres(String.format(" AND %s ", secondFiscalWhereExpr));

      //处理PG,查看明细用
      String firstFiscalWhereExprPG = PGFilterType.LATERTHAN.buildBoolSQL(timeRule.tableName, timeRule.column,
              timeRule.column, timeRule.columnType, PGFilterType.LATERTHAN.getType(), timeRule.isSingle,
              String.valueOf(fiscalDate.first), null, null, biMtRule.timeZone());
      timeTable.appendPGSubWheres(String.format(" AND %s ", firstFiscalWhereExprPG));
      String secondFiscalWhereExprPG = PGFilterType.SOONERTHAN.buildBoolSQL(timeRule.tableName, timeRule.column,
              timeRule.column, timeRule.columnType, PGFilterType.SOONERTHAN.getType(), timeRule.isSingle,
              String.valueOf(fiscalDate.second), null, null, biMtRule.timeZone());
      timeTable.appendPGSubWheres(String.format(" AND %s ", secondFiscalWhereExprPG));
    }
  }


  /**
   * 校验是否需要去重列，以及指标计算是否由count 改为 uniqState
   *
   * @param tenantId       租户id
   * @param standalone     是否schema隔离
   * @param valueRules     agg规则
   * @param statRule       拓扑图对象
   * @param aliasMapper    dim别名计算cache
   * @param aggAliasMapper agg别名计算cache
   */
  public void checkUniqField(String tenantId,
                             boolean standalone,
                             List<ValueRule> valueRules,
                             TopologyTableAggRule statRule,
                             Map<String, AtomicInteger> aliasMapper,
                             Map<String, AtomicInteger> aggAliasMapper,
                             Map<String, AtomicInteger> dimsMapper,
                             Map<String, String/*dim字段别名*/> dimFieldAliasMapper,
                             List<DimRule> dimRules,
                             Map<String, NodeTable> valueNodeTableMap) {
    boolean isRuleUniq = statRule.hasDataExplosive(standalone, statRule.getRootNodeTable());
    boolean isDimUniq = isMultipleValue(dimRules);
    Map<String, String> tmpUniqDimConfigMap = Maps.newHashMap();
    Map<String, String> tmpTableAliasColumnNameMap = Maps.newHashMap();
    valueRules.forEach(valueRule -> {
      boolean isValueUniq = valueRule.joinRelation != null && (!GrayManager.isAllowByRule("check_uniq_field_quote_eis", tenantId) ||
              (GrayManager.isAllowByRule("check_uniq_field_quote_eis", tenantId) && !Constants.QUOTE.equals(valueRule.joinRelation.fieldType)));
      if (isRuleUniq || isDimUniq || isValueUniq) {
        switch (Objects.requireNonNull(valueRule).getAggType()) {
          case count, countdistinct -> {
            //需要改成uniqStat。先删再改增
            statRule.getAggConfigStringList().removeIf(item -> item.startsWith(valueRule.createAggConfigString()));
            valueRule.setAggType(AggType.countuniq);
            String aggColumnName = valueRule.createAggColumnName(aggAliasMapper);
            statRule.getFieldLocationMap().put(valueRule.getMeasureId(), aggColumnName);
            String aggConfigString = valueRule.createAggConfigString() + ":" + aggColumnName;
            statRule.getAggConfigStringList().add(aggConfigString);
          }
          case sum -> {
            //需要加上去重列,同一个表上的指标共用一个去重列
            NodeTable valueNodeTable = valueNodeTableMap.get(valueRule.getMeasureId());
            DimRule keyDimRule = this.createKeyDimRule(tenantId, standalone, valueNodeTable.objectDescribeApiName(),
              null);
            this.joinQuoteNodeTable(tenantId, standalone, valueNodeTable, aliasMapper, keyDimRule.getDimFieldRule(),
              true, false);
            DimConfig dimConfig = keyDimRule.createDimConfig(null, dimFieldAliasMapper, dimsMapper);
            if (tmpTableAliasColumnNameMap.containsKey(dimConfig.getTableAlias())) {
              tmpUniqDimConfigMap.put(valueRule.getMeasureId(),
                tmpTableAliasColumnNameMap.get(dimConfig.getTableAlias()));
              return;
            }
            //如果不在共享维度列中需要单独占用一列 agg_uniq_tag,其他指标也要占用这一列只不过是空串。
            if (!statRule.getDimConfigStringList().contains(dimConfig.toConfigString())) {
              dimConfig.setDstColumnName(TopologyUtils.createAggAlias(aggAliasMapper, Constants.AGG_UNIQ_TAG,
                Constants.AGG_UNIQ_TAG));
            }
            tmpUniqDimConfigMap.put(valueRule.getMeasureId(), dimConfig.toConfigString());
            tmpTableAliasColumnNameMap.put(dimConfig.getTableAlias(), dimConfig.toConfigString());
            //修改aggType 由sum 改为sum2
            statRule.getAggConfigStringList().replaceAll(item -> item.startsWith(valueRule.createAggConfigString()) ?
              item.replace(":sum:", ":sum2:") : item);
          }
        }
      }
    });
    statRule.setUniqDimConfigMap(tmpUniqDimConfigMap);
  }

  /**
   *
   */
  public List<List<WhereConfig>> dealWithWhereConfig(String tenantId,
                                                     boolean standalone,
                                                     BiMtRule biMtRule,
                                                     String rootNodeId,
                                                     NodeTable aggNodeTable,
                                                     Map<String, AtomicInteger> aliasMapper,
                                                     WhereRules whereRules,
                                                     Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                                                     boolean isCH) {
    List<List<WhereConfig>> whereConfigList = Lists.newArrayList();
    if (whereRules != null) {
      ArrayList<ArrayList<WhereRule>> whereList = whereRules.getWhereRulesList();
      for (ArrayList<WhereRule> rules : whereList) {
        List<WhereConfig> whereConfigs = Lists.newArrayList();
        rules.forEach(wr -> {
          NodeTable whereNodeTable = this.buildBranch(biMtRule, rootNodeId, aggNodeTable, aliasMapper, wr,
            cachedTableDefinitions, false);
          WhereConfig whereConfig = new WhereConfig();
          whereConfig.setTableName(wr.tableName);
          whereConfig.setTableAlias(wr.alias);
          if (isCH) {
            whereConfig.setWhereExpr(wr.whereSQL(whereNodeTable, wr.tableName, wr.alias, biMtRule.timeZone()));
          } else {
            whereConfig.setWhereExpr(wr.whereSQLPG(whereNodeTable, wr.tableName, wr.alias, biMtRule.timeZone()));
          }
          whereConfigs.add(whereConfig);
        });
        whereConfigList.add(whereConfigs);
      }
    }
    return whereConfigList;
  }

  /**
   * 查找指标或维度对象并且填充相关属性
   *
   * @param biMtRule               规则定义对象
   * @param rootNodeId             指标对象NodeId
   * @param aggObjNodeTable        根节点nodeTable
   * @param aliasMapper            别名缓存
   * @param quoteOfAgg             填充子节点或属性条件
   * @param cachedTableDefinitions
   */
  public NodeTable buildBranch(BiMtRule biMtRule,
                               String rootNodeId,
                               NodeTable aggObjNodeTable,
                               Map<String, AtomicInteger> aliasMapper,
                               QuoteOfAgg quoteOfAgg,
                               Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                               boolean forceReturnPreNode) {
    String tenantId = biMtRule.getTenantId();
    boolean standalone = biMtRule.isStandalone();
    String fromId = rootNodeId;
    String toId = quoteOfAgg.nodeId;
    NodeTable currentNodeTable = aggObjNodeTable;
    List<Edge> lookupLink = biMtRule.findLookupList(fromId, toId);
    if (CollectionUtils.isNotEmpty(lookupLink)) {
      for (Edge edge : lookupLink) {
        Node toNode = biMtRule.findNodeById(edge.getTo());
        currentNodeTable = this.computeIfAbsent(tenantId, biMtRule.isStandalone(), currentNodeTable, toNode,
          edge, aliasMapper, cachedTableDefinitions);
      }
    }
    if (currentNodeTable == null) {
      throw new RuntimeException(String.format("can not find nodeTable tenantId:%s,nodeId:%s", tenantId, toId));
    }
    if (!Objects.equals(currentNodeTable.getNodeId(), toId)) {
      log.error("tenantId:{},currentNodeTable id:{} is not equals NodeId:{}", tenantId, currentNodeTable.getNodeId(),
        toId);
    }
    return this.joinQuoteNodeTable(tenantId, standalone, currentNodeTable, aliasMapper, quoteOfAgg, true,
      forceReturnPreNode);
  }


  /**
   * 关联树剪枝
   *
   * @param tenantId      租户id
   * @param isStandalone  是否schema隔离
   * @param fromNodeTable 起始节点
   * @param toNode        添加节点
   * @param edge          节点关系
   * @param aliasMapper   别名缓存
   * @return 添加后的节点
   */
  private NodeTable computeIfAbsent(String tenantId,
                                    boolean isStandalone,
                                    NodeTable fromNodeTable,
                                    Node toNode,
                                    Edge edge,
                                    Map<String, AtomicInteger> aliasMapper,
                                    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    List<OnField> onFields = edge.getOnFields();
    if (CollectionUtils.isEmpty(onFields)) {
      log.error("edge on fields is empty tenantId:{},edge:{}", tenantId, JSON.toJSONString(edge));
      return null;
    }
    Pair<String, String> lookupFieldPair = edge.findLookupFieldPair();
    if (lookupFieldPair == null) {
      log.error("edge on fields without reference fields tenantId:{},edge:{}", tenantId, JSON.toJSONString(edge));
      return null;
    }
    NodeTable endTable = fromNodeTable;
    UdfObjFieldDO fieldInfo = udfObjFieldMapper.setTenantId(tenantId)
                                               .findUdfObjFieldDOByFieldName(tenantId,
                                                 fromNodeTable.getObjectDescribeApiName(),
                                                 mappingService.getApiXName(fromNodeTable.getObjectDescribeApiName())
                                                 , lookupFieldPair.first);
    if (fieldInfo == null) {
      log.error("can not find db field tenantId:{},dbObjName:{},dbFieldName:{}", tenantId,
        fromNodeTable.getObjectDescribeApiName(), lookupFieldPair.first);
      return null;
    }
    String targetObjName = mappingService.biApiName(toNode.getDescribeApiName());
    String lookupFieldColumn = lookupFieldPair.first;
    String fieldType = fieldInfo.getType();
    String relationTable = fieldInfo.getRelationTable();
    String refKeyField = fieldInfo.getRefKeyField();
    String refObjName = fieldInfo.getRefObjName();
    if (StringUtils.isNotBlank(refObjName) && !Objects.equals(targetObjName, refObjName)) {
      throw new RuntimeException(String.format("this two object has no relationship %s:%s:%s", tenantId,
        fromNodeTable.getObjectDescribeApiName(), targetObjName));
    }
    int objectIdFieldLocation = Integer.parseInt(fieldInfo.getFieldLocation());
    if (objectIdFieldLocation > 0) {
      lookupFieldColumn = "value" + objectIdFieldLocation;
    }
    if (!isStandalone && !fromNodeTable.getObjectDescribeApiName().endsWith("__c") &&
      Constants.slotRegex.matcher(lookupFieldColumn).matches()) {
      NodeJoin extTableJoin = this.createExtNodeTable(fromNodeTable.getObjectDescribeApiName());
      endTable = fromNodeTable.addJoinSetSingle(aliasMapper, extTableJoin, true);
      fromNodeTable.appendColumns("extend_obj_data_id","id");
    }
    //如果是what 类型的，需要继续增加join relation
    if ("group".equals(fieldType) && StringUtils.isNotBlank(relationTable)) {
      NodeTable whatNodeTable = NodeTable.of(relationTable, null, Lists.newArrayList(), Sets.newTreeSet(),
        relationTable, "id", false, null, null);
      //构建和被引用对象的join 关系
      Set<String> onFilter = Sets.newHashSet(Constants.EI_ON_CONDITIONS, Constants.RIGHT_ON_DELETED_0);
      onFilter.add("${lt}.id=" + "${rt}.source_data_id");
      NodeOnCondition nodeOnCondition = new NodeOnCondition(onFilter.toArray(new String[0]), null, null);
      NodeJoin quoteTableJoin = NodeJoin.of(com.fxiaoke.bi.warehouse.common.db.er.JoinType.LEFT_JOIN, whatNodeTable,
        nodeOnCondition);
      endTable = fromNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, true);
      endTable.appendColumns("source_data_id");
      endTable.appendColumns("is_deleted");
      lookupFieldColumn = "target_data_id";
    }
    JoinRelation joinRelation;
    if ("group".equals(fieldType) && "target_data_id".equals(lookupFieldColumn)) {
      joinRelation = JoinRelation.builder()
                                 .apiName(targetObjName)
                                 .column("target_data_id")
                                 .columnType(PGColumnType.String)
                                 .joinType(AggJoinType.LEFT)
                                 .fieldType(FieldType.TEXT)
                                 .build();
    } else {
      joinRelation = this.createRelation(tenantId, this.standalone(tenantId),
        fromNodeTable.getObjectDescribeApiName(), fieldInfo, targetObjName, cachedTableDefinitions, AggJoinType.LEFT);
      if (FieldType.GROUP.equals(fieldInfo.getType()) &&
        Constants.RELATED_OBJECT.equals(fieldInfo.getDbFieldName()) &&
        StringUtils.isNotBlank(fieldInfo.getWhatIdField()) &&
        StringUtils.isNotBlank(fieldInfo.getWhatApiNameField())) {
        String whatApiWhere = String.format(" AND %s='%s'", fieldInfo.getWhatApiNameField(), mappingService.paasApiName(joinRelation.apiName));
        endTable.appendSubWheres(whatApiWhere);
        endTable.appendPGSubWheres(whatApiWhere);
      }
    }
    NodeTable refObjNodeTable = NodeTable.of(Constants.table(targetObjName, null, isStandalone), null,
      Lists.newArrayList(), Sets.newTreeSet(), targetObjName, joinRelation.column, false, toNode.getId(), null);
    NodeJoin lookupTableJoin = Constants.onJoinPlus(tenantId, this.standalone(tenantId), joinRelation, endTable,
      refObjNodeTable, mappingService);
    return endTable.addJoinSetSingle(aliasMapper, lookupTableJoin, true);
  }

  /**
   * 生成dim Rule集合
   *
   * @param tenantId
   * @param biMtRule
   * @return
   */
  private List<DimRule> buildDimRule(String tenantId, boolean standalone, BiMtRule biMtRule) {
    List<DimRule> dimRules = Lists.newArrayList();
    Map<String/*nodeId*/, List<BiMtDimRule>> dimRuleMap = biMtRule.filterActionDateDim();
    dimRuleMap.forEach((k, v) -> {
      Node node = biMtRule.findNodeById(k);
      if (node != null) {
        String describeApiName = mappingService.biApiName(node.getDescribeApiName());
        Map<String/*dbFieldName*/, String/*fieldId*/> dbFieldNameMap = Maps.newHashMap();
        v.forEach(item -> {
          String nodeIdField = "name".equals(item.nodeIdFieldPair().second) ?
            Constants.getIdName(standalone, describeApiName) :
            item.nodeIdFieldPair().second;
          dbFieldNameMap.put(nodeIdField, item.getDimensionId());
          //增加data_auth_code维度,fieldId在封装dimRule时会用udfObjFieldDO的fieldId覆盖
          if (Constants.isIdName(standalone, describeApiName, nodeIdField)) {
            dbFieldNameMap.put("data_auth_code", item.getDimensionId() + "_data_auth_code");
          }
        });
        List<DimRule> subDimRules = dimRuleDao.createDimRule(tenantId, describeApiName, dbFieldNameMap,
          DisplayField.DisplayType.group);
        if (CollectionUtils.isNotEmpty(subDimRules)) {
          subDimRules.forEach(dimRule -> {
            dimRule.getDimFieldRule().nodeId = k;
            dimRule.getDimFieldRule().describeApiName = node.getDescribeApiName();
            dimRules.add(dimRule);
          });
        }
      }
    });
    return dimRules;
  }

  /**
   * 加载日期规则
   *
   * @param tenantId               租户id
   * @param standalone             是否独立schema
   * @param measureActionDate      指标中的日期字段
   * @param biMtRule               规则对象
   * @param cachedTableDefinitions 物理表元数据cache
   * @return 日期规则
   */
  public TimeRule buildTimeRule(String tenantId,
                                boolean standalone,
                                Pair<String/*rootNodeId*/, String/*actionDateField*/> measureActionDate,
                                BiMtRule biMtRule,
                                Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    Pair<String, String> nodeIdFieldPair = BiMtMeasureRule.createActionDateFieldPair(measureActionDate);
    Node actionDateNode = biMtRule.findNodeById(nodeIdFieldPair.first);
    String actionApiName = mappingService.biApiName(actionDateNode.getDescribeApiName());
    String dbFieldName = nodeIdFieldPair.second;
    Map<String, Object> timeField = this.findFieldInfoByFieldName(tenantId, actionApiName, dbFieldName);
    if (MapUtils.isEmpty(timeField)) {
      return null;
    }
    JSONObject timeFieldInfo = new JSONObject(timeField);
    if ("quote".equals(timeFieldInfo.getString("type"))) {
      String refObjName = mappingService.udfApiName2ApiName(timeFieldInfo.getString("ref_obj_name"));
      QuoteOfAgg timeRule = this.buildFromQuote(tenantId, standalone, actionApiName, refObjName,
        timeFieldInfo.getString("relation_key_field"), timeFieldInfo.getString("ref_target_field"),
        cachedTableDefinitions);
      return TimeRule.builder()
                     .joinRelation(timeRule.joinRelation)
                     .column(timeRule.column)
                     .columnType(timeRule.columnType)
                     .fieldType(timeRule.fieldType)
                     .isSingle(timeRule.isSingle)
                     .nodeId(nodeIdFieldPair.first)
                     .describeApiName(actionApiName)
                     .formatStr(timeRule.formatStr)
                     .build();
    } else {
      String dbColumn = timeFieldInfo.getString("db_field_name");
      //加载一下，看看是否是槽位列
      int dbFieldLocation = timeFieldInfo.getInteger("field_location");
      if (dbFieldLocation > 0) {
        dbColumn = "value" + dbFieldLocation;
      }
      PGColumnType dbColumnType;
      String dbFieldType = timeFieldInfo.getString("type");
      //员工部门的引用需要单独处理一下
      boolean isSingle = this.isSingleValue(dbFieldType, dbColumn, timeFieldInfo.getInteger("is_single"));
      if (actionApiName.equals("active_record") && "related_api_names".equalsIgnoreCase(dbColumn)) {
        dbColumnType = PGColumnType.ARRAY_String;
      } else {
        String table = actionApiName;
        if (table.endsWith("__c") && !standalone) {
          table = "object_data";
        }
        Map<String, ColumnDefinition> tableColumnDefinitions = findTableColumnDefinitions(tenantId, standalone, table
          , cachedTableDefinitions);
        ColumnDefinition definition = tableColumnDefinitions.get(dbColumn);
        if (definition == null) {
          dbColumnType = PGColumnType.String;
        } else {
          dbColumnType = definition.columnType();
        }
      }
      return TimeRule.builder()
                     .joinRelation(null)
                     .column(dbColumn)
                     .columnType(dbColumnType)
                     .fieldType(dbFieldType)
                     .isSingle(isSingle)
                     .nodeId(nodeIdFieldPair.first)
                     .describeApiName(actionApiName)
                     .formatStr(timeFieldInfo.getString("format_str"))
                     .build();
    }
  }

  /**
   * 处理指标字段
   *
   * @param tenantId               租户id
   * @param standalone             是否schema隔离
   * @param biMtRule            规则定义对象
   * @param biMtMeasureRule    多维度图指标规则对象
   * @param cachedTableDefinitions 表以及字段的临时缓存
   * @return {@link ValueRule}
   */
  public ValueRule buildValueRule(String tenantId,
                                  boolean standalone,
                                  BiMtRule biMtRule,
                                  BiMtMeasureRule biMtMeasureRule,
                                  Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    String valueFieldName = biMtMeasureRule.findValueField();
    String nodeId = biMtMeasureRule.getNodeId();
    String measureId = biMtMeasureRule.getMeasureId();
    Node aggNode = biMtRule.findNodeById(nodeId);
    String aggApiName = mappingService.biApiName(aggNode.getDescribeApiName());
    Map<String, Object> valueField = this.findFieldInfoByFieldName(tenantId, aggApiName, valueFieldName);
    //字段已经被删除
    if (null == valueField) {
      return null;
    }
    //主属性name转id
    if (Objects.equals(valueFieldName, this.getMainField(aggApiName))) {
      valueFieldName = Constants.getIdName(standalone, aggApiName);
    }
    JSONObject valueFieldInfo = new JSONObject(valueField);
    Integer fieldLocation = valueFieldInfo.getInteger("field_location");
    Boolean isUnique = valueFieldInfo.getBoolean("is_unique");
    String fieldType = valueFieldInfo.getString("type");
    if (Utils.isExtend(aggApiName, valueFieldName) && fieldLocation > 0) {
      valueFieldName = "value" + fieldLocation;
    }
    String refColumn = valueFieldInfo.getString("relation_key_field");
    //根据统计方式获取统计枚举
    String aggTypeIndex = biMtMeasureRule.getAggType();
    AggType aggType = AggType.parseFromOp(aggTypeIndex);
    if (refColumn != null) {
      String targetFieldName = valueFieldInfo.getString("ref_target_field");
      String valueApiName = valueFieldInfo.getString("ref_obj_name");
      if (valueApiName == null) {
        return null;
      }
      if (valueApiName.endsWith("_udef")) {
        valueApiName = ObjectConfigManager.getPreObjName(valueApiName);
      }
      String targetColumn = targetFieldName;
      Map<String, Object> targetField = this.findFieldInfoByFieldName(tenantId, valueApiName, targetFieldName);
      if (targetField == null) {
        return null;
      }
      JSONObject targetFieldInfo = new JSONObject(targetField);
      Integer targetFieldLocation = targetFieldInfo.getInteger("field_location");
      String targetFieldType = targetFieldInfo.getString("type");
      if (Utils.isExtend(valueApiName, targetFieldName) && targetFieldLocation > 0) {
        targetColumn = "value" + targetFieldLocation;
      }
      String table = valueApiName;
      if (table.endsWith("__c") && !standalone) {
        table = "object_data";
      }
      PGColumnType columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions,
        targetColumn, targetFieldType);
      JoinRelation valueJoinRelation = new JoinRelation(refColumn, targetFieldType, valueApiName, AggJoinType.LEFT,
        columnType);
      isUnique = targetFieldInfo.getBoolean("is_unique");
      if (aggType.equals(AggType.count) && Objects.equals(isUnique,false)) {
        aggType = AggType.countuniq;
      }
      return ValueRule.builder()
                      .joinRelation(valueJoinRelation)
                      .column(targetColumn)
                      .columnType(columnType)
                      .aggType(aggType)
                      .describeApiName(aggApiName)
                      .nodeId(nodeId)
                      .measureId(measureId)
                      .build();
    } else {
      String relationTable = valueFieldInfo.getString("relation_table");
      if ("feed_relation".equals(relationTable) && "related_object".equals(valueFieldName)) {
        JoinRelation valueJoinRelation = new JoinRelation("id", null, "feed_relation", AggJoinType.LEFT,
          PGColumnType.String);
        return ValueRule.builder()
                        .joinRelation(valueJoinRelation)
                        .column("target_data_id")
                        .columnType(PGColumnType.String)
                        .aggType(aggType)
                        .describeApiName(aggApiName)
                        .nodeId(nodeId)
                        .measureId(measureId)
                        .build();
      }
      String table = aggApiName;
      if (table.endsWith("__c") && !standalone) {
        table = "object_data";
      }
      PGColumnType columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions,
        valueFieldName, fieldType);
      if (aggType.equals(AggType.count) && Objects.equals(isUnique,false)) {
        aggType = AggType.countuniq;
      }
      return ValueRule.builder()
                      .column(valueFieldName)
                      .columnType(columnType)
                      .aggType(aggType)
                      .describeApiName(aggApiName)
                      .nodeId(nodeId)
                      .measureId(measureId)
                      .build();
    }
  }

  /**
   * @param tenantId
   * @param standalone
   * @param biMtRule
   * @param cachedTableDefinitions
   * @return
   */
  public WhereRules buildWheres(String tenantId,
                                boolean standalone,
                                BiMtRule biMtRule,
                                Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    AtomicInteger counter = new AtomicInteger(0);
    ArrayList<ArrayList<WhereRule>> list = Lists.newArrayList();
    JSONArray wheresInfo = biMtRule.goalWheres();
    if (CollectionUtils.isEmpty(wheresInfo)) {
      return null;
    }
    for (Object whereListInfo : wheresInfo) {
      ArrayList<WhereRule> whereRuleList = buildWhereRuleList(tenantId, standalone, (JSONObject) whereListInfo,
        counter, biMtRule, cachedTableDefinitions);
      if (CollectionHelper.isNotEmpty(whereRuleList)) {
        list.add(whereRuleList);
      }
    }
    if (list.isEmpty()) {
      return null;
    }
    return WhereRules.builder().whereRulesList(list).build();
  }

  /**
   * 拼where list
   *
   * @param tenantId
   * @param whereListInfo
   * @param counter
   * @return
   */
  public ArrayList<WhereRule> buildWhereRuleList(String tenantId,
                                                 boolean standalone,
                                                 JSONObject whereListInfo,
                                                 AtomicInteger counter,
                                                 BiMtRule biMtRule,
                                                 Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    ArrayList<WhereRule> result = Lists.newArrayList();
    JSONArray filters = whereListInfo.getJSONArray("filters");
    for (Object filter : filters) {
      WhereRule whereRule = buildWhereRule(tenantId, standalone, (JSONObject) filter, counter, biMtRule,
        cachedTableDefinitions);
      if (null == whereRule) {
        log.warn("where rule init failed. {}:{}", tenantId, filter);
        continue;
      }
      result.add(whereRule);
    }
    return result;
  }

  /**
   * 拼单条where规则
   *
   * @param tenantId  tenantId
   * @param whereInfo where条件
   * @param counter   计数器
   * @return WhereRule
   */
  public WhereRule buildWhereRule(String tenantId,
                                  boolean standalone,
                                  JSONObject whereInfo,
                                  AtomicInteger counter,
                                  BiMtRule biMtRule,
                                  Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    String nodeId = whereInfo.getString("nodeId");
    String filterApiName = mappingService.biApiName(biMtRule.findNodeById(nodeId).getDescribeApiName());
    //字段名称
    String dbFieldName = whereInfo.getString("dbFieldName");
    String dbObjName = whereInfo.getString("dbObjName");
    //where字段元数据类型
    //    String originalType = whereInfo.getString("originalType");
    //如果是筛选条件是属于不属于
    int operator = whereInfo.getInteger("operator");
    if (FieldType.IN_NOT_IN.contains(operator) && Objects.equals(dbFieldName, this.getMainField(dbObjName))) {
      dbFieldName = dbObjName.endsWith("__c") ? "value0" : Constants.getIdName(standalone, dbObjName);
    }
    QuoteOfAgg quoteOfAgg = this.buildQuoteOfAggIfAnyRelation(tenantId, standalone, filterApiName, dbFieldName,
      cachedTableDefinitions);
    if (quoteOfAgg == null) {
      return null;
    }
    String value1 = whereInfo.getString("value1");
    String value2 = whereInfo.getString("value2");
    String uiType = whereInfo.getString("uiType"); //指标里是type,目标里是uiType
    FilterType filterType = FilterType.parseFromId(operator);
    PGFilterType pgFilterType = PGFilterType.parseFromId(operator);
    return WhereRule.builder()
                    .id(counter.incrementAndGet())
                    .joinRelation(quoteOfAgg.joinRelation)
                    .column(quoteOfAgg.column)
                    .columnType(quoteOfAgg.columnType)
                    .fieldType(quoteOfAgg.fieldType)
                    .isSingle(quoteOfAgg.isSingle)
                    .describeApiName(filterApiName)
                    .nodeId(nodeId)
                    .filterType(filterType)
                    .pgFilterType(pgFilterType)
                    .value1(value1)
                    .value2(value2)
                    .uiType(uiType)
                    .build();
  }
}
