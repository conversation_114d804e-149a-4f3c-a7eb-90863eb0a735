package com.fxiaoke.bi.warehouse.dws.service;

import com.fxiaoke.bi.warehouse.common.util.BizAuditLog;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.BiAggSyncInfoDao;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.dws.db.dao.DbSyncInfoDao;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyTableDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTable;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableStatus;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.integrate.service.BiDataSyncPolicyService;
import com.fxiaoke.helper.Pair;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class BackgroundTaskService {

    @Resource
    private DbSyncInfoDao dbSyncInfoDao;
    @Resource
    private ClickHouseService clickHouseService;
    @Resource(name = "jedisFactory")
    private JedisCmd jedisCmd;

    @Resource
    private PgDataSource pgDataSource;

    @Resource
    private TopologyTableDao topologyTableDao;

    @Resource
    private TopologyTableService topologyTableService;
    @Resource
    private BiDataSyncPolicyService biDataSyncPolicyService;
    @Resource
    private BiAggSyncInfoDao biAggSyncInfoDao;

    private long largeTableRowsFrom;

    private long largeTableSizeFrom;

    private int explainTableCount;
    private int schemaExplainTableCount;

    private long explainTotalRows;
    private long schemaExplainTotalRows;

    private long explainSingleTableRows;
    private long schemaExplainSingleTableRows;

    private long explainNotControlRows;
    private long schemaExplainNotControlRows;

    private int aggRuleCountThreshold;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
            largeTableRowsFrom = config.getLong("large_table_rows_from_value",20000);
            largeTableSizeFrom = config.getLong("large_table_size_from_value",10240);

            explainTableCount = config.getInt("explain_table_count_threshold",3);
            explainTotalRows = config.getLong("explain_total_rows_threshold",3000000);
            explainSingleTableRows = config.getLong("explain_single_table_rows_threshold",5000000);
            explainNotControlRows = config.getLong("explain_not_control_rows_threshold",1000000);

            //独立DB的阈值组
            schemaExplainTableCount = config.getInt("explain_table_count_schema_threshold",4);
            schemaExplainTotalRows = config.getLong("explain_total_rows_schema_threshold",7000000);
            schemaExplainSingleTableRows = config.getLong("explain_single_table_rows_schema_threshold",10000000);
            schemaExplainNotControlRows = config.getLong("explain_not_control_rows_schema_threshold",3000000);

            aggRuleCountThreshold = config.getInt("explain_agg_rule_count_threshold",5);
        });
    }

    public boolean canStartExplainTask(String jdbcUrl){
        /*
        复用bi_merge_task来控制explain task的并发
        因为CH的事务机制弱，使用redis锁来控制bi_merge_task的并发读写
         */
        String sqlTemplate = """
          CREATE TABLE IF NOT EXISTS %s.bi_merge_task on cluster '{cluster}'(
              `table_name` String,
              `status` UInt8,
              `max_merged_timestamp` DateTime DEFAULT now(),
              `timestamp` DateTime DEFAULT now(),
              `is_deleted` UInt8
          )
          engine = ReplicatedReplacingMergeTree(timestamp)
          ORDER BY (table_name);
          """;
        String iSqlTemplate = """
          insert into %s.bi_merge_task(table_name, status, max_merged_timestamp, is_deleted) 
          values ('explain_db_task', 1, now(), 0);
          """;
        String chDB = Utils.parseDBNameFromJdbcUrl(jdbcUrl);
        String tableSql = String.format(sqlTemplate, chDB);
        Boolean bRet = false;
        //如果当天没有执行过explain任务，可以进行explain
        String checkSql = String.format("select status as diff from %s.bi_merge_task final where table_name = 'explain_db_task' and timestamp >= today() and is_deleted = 0 order by timestamp desc limit 1", chDB);
        String insertSql = String.format(iSqlTemplate, chDB);
        try {
            //如果table不存在，则创建
            clickHouseService.executeSQLWithJdbcUrl(jdbcUrl,tableSql,60000);
            List<Boolean> result = clickHouseService.executeQuerySQLWithJdbc(jdbcUrl, checkSql,60000L, rs -> {
                return true;
            });
            bRet = CollectionUtils.isEmpty(result);
            if(bRet){
                clickHouseService.executeSQLWithJdbcUrl(jdbcUrl, insertSql,60000);
            }
            log.info("canStartExplainTask: {}, {}",jdbcUrl, bRet);
            return bRet;
        } catch (Exception e) {
            log.error("canStartExplainTask exception:{}", chDB, e);
        }
        return false;
    }

    private List<String> getTenantsByAggData(String jdbcUrl){
        String querySql = """
          select distinct tenant_id from %s.agg_data
          order by tenant_id;
            """;
        String chDB = Utils.parseDBNameFromJdbcUrl(jdbcUrl);
        String qSql = String.format(querySql, chDB);
        try {
            List<String> eis = clickHouseService.executeQuerySQLWithJdbc(jdbcUrl, qSql,60000L, rs -> {
                try {
                    return rs.getString(1);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            });

            log.info("getTenantsByAggData count: {}, {}",eis.size(), jdbcUrl);
            return eis;
        } catch (Exception e) {
            log.error("startExplainTask exception:{}", chDB, e);
        }
        return Lists.newArrayList();
    }

    private List<String> getViewsByTenant(String jdbcUrl, String tenantId){
        String querySql = """
          select view_id from %s.agg_data where tenant_id = '%s'
          group by view_id
          order by view_id;
            """;
        String chDB = Utils.parseDBNameFromJdbcUrl(jdbcUrl);
        String qSql = String.format(querySql, chDB, tenantId);
        List<String> viewList = Lists.newArrayList();
        try {
            viewList = clickHouseService.executeQuerySQLWithJdbc(jdbcUrl, qSql,60000L, rs -> {
                try {
                    return rs.getString(1);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            });
            log.info("getViewsByTenant count: {}, {}, {}",viewList.size(), jdbcUrl, tenantId);
            return viewList;
        } catch (Exception e) {
            log.error("getViewsByTenant exception:{}", jdbcUrl, e);
        }
        return viewList;
    }

    public void explainSql(String jdbcUrl, String tenantId, String viewId,  String vSql){
        String querySql = """
            select groupArray(table) as tables, length(tables) as table_counts, sum(rows) as total_rows, groupArray(rows) as rows_array from (
              explain estimate
              %s
              );
            """;
        String qSql = String.format(querySql, vSql);
        String chDB = Utils.parseDBNameFromJdbcUrl(jdbcUrl);
        try {
            clickHouseService.executeQuerySQLWithJdbc(jdbcUrl, qSql,600000L, rs -> {
                try {
                    int table_counts = rs.getInt(2);
                    long total_rows = rs.getLong(3);
                    String tables = rs.getString(1);
                    if(StringUtils.isNotEmpty(tables)){
                        tables = tables.replace(","," ");
                    }
                    String row_list = rs.getString(4);
                    if(StringUtils.isNotEmpty(row_list)){
                        row_list = row_list.replace(","," ");
                    }
                    boolean bDirectQuery = false;

                    long notControlRows = explainNotControlRows;
                    long singleTableRows = explainSingleTableRows;
                    long totalRows = explainTotalRows;
                    int tableCount = explainTableCount;
                    if(pgDataSource.standalone(tenantId)){
                        notControlRows = schemaExplainNotControlRows;
                        singleTableRows = schemaExplainSingleTableRows;
                        totalRows = schemaExplainTotalRows;
                        tableCount = schemaExplainTableCount;
                    }

                    boolean bAllowRuleCount = true;
                    List<String> sourceIds = topologyTableService.queryViewIdByUniqueKey(tenantId, viewId);
                    if(CollectionUtils.isNotEmpty(sourceIds)) {
                        TopologyTable topologyTable = topologyTableService.findByTenantIdAndSourceId(tenantId, sourceIds.get(0));
                        if(GrayManager.isAllowByRule("allow_explain_rule_count_check", tenantId) && topologyTable != null && topologyTable.getStatRuleList().size() > aggRuleCountThreshold){
                            bAllowRuleCount = false;
                        }
                    }

                    //判断是否直接查对象表，不用预计算；指标个数小于等于5并且满足任一种情况就直查：1、total_rows少于10w；2、单表并且total_rows少于1000w；3、表个数小于等于3并且total_rows少于100w
                    if(bAllowRuleCount && ( total_rows < notControlRows || (table_counts <= 1 && total_rows <= singleTableRows) || (table_counts <= tableCount && total_rows <= totalRows) )){
                        bDirectQuery = true;
                        //如果统计图的状态不是NONeedCal，则把它更新为NONeedCal
                        if (GrayManager.isAllowByRule("explain_view_cost_db_affect", chDB)) {
                            log.info("explainSql change2NoNeed {}, {}, {}", jdbcUrl, tenantId, viewId);
                            topologyTableService.changeTopologyTable2NoNeedCalByKey(tenantId, viewId);
                        }
                    }
                    else {
                        bDirectQuery = false;
                        //如果统计图状态是NONeedCal，则把统计图状态更新预计算状态
                        if (GrayManager.isAllowByRule("explain_view_cost_db_affect", chDB)) {
                            log.info("explainSql change2Prepared {}, {}, {}", jdbcUrl, tenantId, viewId);
                            topologyTableService.changeTopologyTable2PreparedByKey(tenantId, viewId);
                        }
                    }

                    BizAuditLog auditLog = new BizAuditLog();
                    auditLog.setModule("STAT_CHART_DATA_EXPLAIN");
                    auditLog.setStartTime(System.currentTimeMillis());
                    auditLog.setFrom(chDB);
                    auditLog.setFromId(viewId);
                    auditLog.setTenantId(tenantId);
                    auditLog.setMessage(String.format("bDirectQuery:%s,table_counts:%d, total_rows:%d, tables:%s, rows:%s", bDirectQuery, table_counts, total_rows, tables, row_list));
                    auditLog.setExtra(vSql);
                    auditLog.log();
                    log.info("explainSql finish, ei {}, viewId {}, bDirectQuery:{}, table_counts:{}, total_rows:{}, tables:{}, rows:{}", tenantId, viewId, bDirectQuery, table_counts, total_rows, tables, row_list);
                    return bDirectQuery;
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            });
            log.info("explainSql finish {}, {}, {}", jdbcUrl,tenantId, viewId);
        } catch (Exception e) {
            log.warn("explainSql exception {}, {}, {}, {}, {}", jdbcUrl,tenantId, viewId, vSql,  e);
            throw new RuntimeException(e);
        }
    }


    //统计agg_data、agg_data_swap、agg_data_history的行数和存储空间占用；统计超过2亿行或者20G的大表的行数和存储空间占用；上报审计日志给监控用
    public void auditLargeTables(List<String> jdbcUrls){
        String uSqlTemplate = """
              SELECT
                  table ,
                  sum(rows) AS total_rows,
                  formatReadableSize(sum(data_uncompressed_bytes)) AS uncompressed_size,
                  formatReadableSize(sum(data_compressed_bytes)) AS compressed_size,
                  round((sum(data_compressed_bytes) / sum(data_uncompressed_bytes)) * 100, 0) AS compressed_ratio,
                  count(distinct partition) as partition_count
              FROM system.parts
              where table in ('agg_data','agg_data_swap','agg_data_history') and database = '%s'
              GROUP BY table
              union distinct
              SELECT
                  table ,
                  sum(rows) AS total_rows,
                  formatReadableSize(sum(data_uncompressed_bytes)) AS uncompressed_size,
                  formatReadableSize(sum(data_compressed_bytes)) AS compressed_size,
                  round((sum(data_compressed_bytes) / sum(data_uncompressed_bytes)) * 100, 0) AS compressed_ratio,
                  count(distinct partition) as partition_count
              FROM system.parts
              where table not in ('agg_data','agg_data_swap','agg_data_history') and database = '%s'
              GROUP BY table
              having (sum(rows) > %d or sum(data_compressed_bytes) > %d)
              order by sum(data_compressed_bytes) desc
            """;
        for (String jdbcUrl:jdbcUrls) {
            String chDB = Utils.parseDBNameFromJdbcUrl(jdbcUrl);
            String uSql = String.format(uSqlTemplate, chDB, chDB, largeTableRowsFrom * 10000, largeTableSizeFrom * 1024 * 1024);

            try {
                List<String> tables = clickHouseService.executeQuerySQLWithJdbc(jdbcUrl, uSql, 6000000L, rs -> {
                    try {
                        BizAuditLog auditLog = new BizAuditLog();
                        auditLog.setModule("STAT_CHART_DATA_COUNT");
                        auditLog.setStartTime(System.currentTimeMillis());
                        auditLog.setFrom(chDB);
                        auditLog.setFromId(rs.getString(1));
                        auditLog.setNum(rs.getLong(2));
                        auditLog.setMessage(String.format("uncompressed_size: %s, compressed_size: %s, compressed_ratio: %s%%, partition_count: %d", rs.getString(3), rs.getString(4), rs.getString(5), rs.getLong(6)));
                        auditLog.log();
                        log.info(String.format("auditLargeTables: %s, %s, %s, %s, %s, %s%%, %d", chDB, rs.getString(1), rs.getString(2), rs.getString(3), rs.getString(4), rs.getString(5), rs.getLong(6)));
                        return rs.getString(1);
                    } catch (SQLException e) {
                        throw new RuntimeException(e);
                    }
                });
                //log.info("getViewsByTenant count: {},{}, {}, {}",viewList.size(), Joiner.on(",").join(viewList), jdbcUrl, tenantId);
                log.info("auditLargeTables count: {}, {}", tables.size(), jdbcUrl);
            } catch (Exception e) {
                log.error("auditLargeTables exception:{}, {}", jdbcUrl, e);
            }
        }
    }

    private String queryViewSqlByRealTime(String ei, String viewId, String chDB) {
        List<String> sourceIds = topologyTableService.queryViewIdByUniqueKey(ei, viewId);
        if (CollectionUtils.isNotEmpty(sourceIds)) {
            TopologyTable topologyTable = topologyTableService.findByTenantIdAndSourceId(ei, sourceIds.getFirst());
            if (topologyTable != null) {
                topologyTable.setDatabase(chDB);
                String viewSQL = topologyTable.toViewSQL(-1, -1, null, null);
                if (StringUtils.isNotEmpty(viewSQL)) {
                    topologyTableDao.updateViewSqlByUniqueKey(ei, viewId, viewSQL);
                }
                log.info("real-time query view_sql  {}, ei:{}, viewId: {}, {}", chDB, ei, viewId, viewSQL);
                return viewSQL;
            }
        }
        return "";
    }

    public void explainViewsCost(List<String> jdbcList){
        try{
            long start = System.currentTimeMillis();
            ExecutorService fixedThreadPools = Executors.newFixedThreadPool(20);
            for(String jdbcUrl : jdbcList) {
                List<String> tenantIds = Lists.newArrayList();
                DBSyncInfoDO dbSyncInfoDO = dbSyncInfoDao.querySchemaDBSyncInfoByCHDB(jdbcUrl);
                if (dbSyncInfoDO != null && StringUtils.isNotBlank(dbSyncInfoDO.getPgSchema())) {
                    String tenantId = dbSyncInfoDO.getPgSchema().substring(4);
                    if (biDataSyncPolicyService.getPolicySizeByTenantId(tenantId) > 0) {
                        tenantIds.add(tenantId);
                    }
                }
                Map<String, Set<String>> downstreamEiViewMapper = biDataSyncPolicyService.findAllDownStreamView(tenantIds);
                String chDB = Utils.parseDBNameFromJdbcUrl(jdbcUrl);
                if (GrayManager.isAllowByRule("explain_view_cost_db_check", chDB)) {
                    fixedThreadPools.submit(() -> {
                        try {
                            List<String> eis;
                            if (dbSyncInfoDO != null && dbSyncInfoDO.getPgSchema().startsWith("sch_")) {
                                eis = Lists.newArrayList(dbSyncInfoDO.getPgSchema().substring(4));
                            } else {
                                eis = this.getTenantsByAggData(jdbcUrl);
                            }
                            for (String ei : eis) {
                                Set<String> downstreamViews = downstreamEiViewMapper.get(ei);
                                List<String> viewList = getViewsByTenant(jdbcUrl, ei);
                                viewList.forEach(viewId -> {
                                    if (this.skipExplainTask(ei, viewId, downstreamViews)) {
                                        return;
                                    }
                                    String viewSql = topologyTableDao.queryViewSqlByUniqueKey(ei, viewId,new int[]{0}); //暂时不支持目标
                                    //如果view_sql不包含db_name，view_sql有问题，需要使用实时接口生成一次；如果view_sql为空，使用实时接口生成一次
                                    if(StringUtils.isEmpty(viewSql) || !viewSql.contains(chDB)) {
                                        String realViewSql = queryViewSqlByRealTime(ei, viewId, chDB);
                                        if(StringUtils.isNotEmpty(realViewSql)){
                                            viewSql = realViewSql;
                                        }
                                        log.info("real-time query view_sql  {}, ei:{}, viewId: {}, {}", jdbcUrl, ei, viewId, viewSql);
                                    }
                                    if (StringUtils.isNotEmpty(viewSql)) {
                                        for(int retryCount=0; retryCount < 2; retryCount++) {
                                            try {
                                                explainSql(jdbcUrl, ei, viewId, viewSql);
                                                break;
                                            }
                                            catch (Exception ex){
                                                if (retryCount < 1) {
                                                    viewSql = queryViewSqlByRealTime(ei, viewId, chDB);
                                                } else {
                                                    throw new RuntimeException(ex);
                                                }
                                            }
                                        }
                                    }
                                    log.info("explainViewsCost finish {}, ei:{}, viewId: {}, {}", jdbcUrl, ei, viewId, viewSql == null ? "" : viewSql);
                                });
                            }
                            log.info("explainViewsCost finish jdbcUrl {}", jdbcUrl);
                        } catch (Exception ex) {
                            log.error("explainViewsCost thread exception {}", jdbcUrl, ex);
                        }
                        return 0;
                    });
                }
            }
            fixedThreadPools.shutdown();
            while(!fixedThreadPools.awaitTermination(60, TimeUnit.SECONDS)){
                log.info("explainViewsCost executing");
            }
            log.info("explainViewsCost finished, cost {}", System.currentTimeMillis()-start);
        }
        catch (Exception ex){
            log.error("explainViewsCost exception", ex);
        }
    }

    /**
     * 判断是否要跳过执行计划校验
     * @param ei
     * @param viewId
     * @param downstreamViews
     * @return
     */
    public boolean skipExplainTask(String ei, String viewId, Set<String> downstreamViews) {
        if (WarehouseConfig.skipExplain(ei, viewId)) {
            log.info("skip this ei:{},stat_view_unique_key:{} explain ", ei, viewId);
            return true;
        }
        if (CollectionUtils.isNotEmpty(downstreamViews)) {
            List<Map<String, Object>> topologyTableInfo = topologyTableDao.querySourceIdAndViewSqlById(ei, viewId);
            if (CollectionUtils.isNotEmpty(topologyTableInfo)) {
                Optional<Pair<String, Integer>> downstreamViewIdOP = topologyTableInfo.stream()
                                                                                      .map(tpInfo -> Pair.of(String.valueOf(tpInfo.get("source_id")), (int) tpInfo.get("status")))
                                                                                      .filter(viewIdStatusPair -> downstreamViews.contains(viewIdStatusPair.first))
                                                                                      .findFirst();
                if (downstreamViewIdOP.isPresent()) {
                    if (downstreamViewIdOP.get().second == TopologyTableStatus.NONeedCal.getValue()) {
                        topologyTableService.changeTopologyTable2PreparedByKey(ei, viewId);
                    }
                    log.info("skip this ei:{},stat_view_unique_key:{} explain because viewId:{} in 1+N", ei, viewId, downstreamViewIdOP.get());
                    return true;
                }
            }
        }
        return false;
    }

    public boolean explainTask(){
        List<String> chLists = Lists.newArrayList();
        try (JedisLock jedisLock = new JedisLock(jedisCmd, "bi_ch_explain_task", 1000 * 60 * 30)) {
            if (jedisLock.tryLock()) {
                for(String jdbcUrl : dbSyncInfoDao.getAllCHDbList()){
                    if(canStartExplainTask(jdbcUrl)){
                        chLists.add(jdbcUrl);
                    }
                }
            }
            else {
                log.info("explainTask can not get redis lock, wait for next time");
                return false;
            }
        }
        catch (Exception ex){
            log.error("explainTask can start exception:", ex);
            return false;
        }
        try{
            if(CollectionUtils.isNotEmpty(chLists)) {
                //统计agg_data、agg_data_swap和大表的数据量，上报统计日志
                auditLargeTables(chLists);

                explainViewsCost(chLists);
            }
            return true;
        }
        catch (Exception ex){
            log.error("explainTask error:", ex);
        }
        return false;
    }

}
