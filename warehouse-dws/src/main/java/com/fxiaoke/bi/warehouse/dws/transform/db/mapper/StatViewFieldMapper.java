package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewFieldDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface StatViewFieldMapper extends ICrudMapper<StatViewFieldDO>, IBatchMapper<StatViewFieldDO>, ITenant<StatViewFieldMapper> {
  //查询未删除的stataviewfield
  @Select("select * from stat_view_field where is_delete=0 and view_id=#{view_id} and ei=#{ei}")
  List<StatViewFieldDO> getStatField(@Param("view_id") String viewId, @Param("ei") int ei);

  //根据视图id查询所有作为维度和指标的字段
  @Select("select * from stat_view_field where (ei=#{ei} or ei=-1) and is_delete=0 and view_id=any(array[#{viewIds}])")
  List<StatViewFieldDO> getStatFieldByViewIds(@Param("ei") int ei, @Param("viewIds") String[] viewIds);

  @Select("select ei,view_id,field_id,'0' as aggr_type from stat_view_filter where (ei=-1 or ei=#{ei}) " +
    " and field_id <> '0' and is_delete=0 and view_id=any(array[#{viewIds}]) " +
    " UNION " +
    " select ei,view_id,field_id,aggr_type from stat_view_field where (ei=-1 or ei=#{ei}) " +
    " and is_delete=0 and view_id=any(array[#{viewIds}])")
  List<Map<String,Object>> findStatViewFieldList(@Param("ei") Integer ei, @Param("viewIds") String[] viewIds);

}
