package com.fxiaoke.bi.warehouse.dws.db.mapper;

import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface DBSyncInfoMapper extends ICrudMapper<DBSyncInfoDO>, ITenant<DBSyncInfoMapper> {
  @Select("SELECT * FROM db_sync_info WHERE id=#{id}")
  DBSyncInfoDO findBySyncId(@Param("id") String id);

  @Select("SELECT * FROM db_sync_info WHERE is_deleted=0")
  List<DBSyncInfoDO> findBySyncIdAll();

  @Update("UPDATE db_sync_info SET status=#{status},last_modified_time=#{lastModifiedTime} WHERE id=#{id} AND " +
    "status=#{oldStatus}")
  int updateDBStatus(@Param("id") String id,
                     @Param("status") int status,
                     @Param("oldStatus") int oldStatus,
                     @Param("lastModifiedTime") long lastModifiedTime);

  @Select("select * from db_sync_info where ch_db=#{ch_db} and pg_db=#{pg_db} and pg_schema=#{pg_schema} and " +
    "is_deleted=0")
  DBSyncInfoDO queryDBSyncInfo(@Param("ch_db") String chDb,
                               @Param("pg_db") String pgDb,
                               @Param("pg_schema") String pgSchema);

  /**
   * 根据pg db 反查db sync info 信息
   * @param pgDb pgdb
   * @param pgSchema schema
   * @return
   */
  @Select("select * from db_sync_info where pg_db=#{pg_db} and pg_schema=#{pg_schema} and is_deleted=0 limit 1")
  DBSyncInfoDO queryDBSyncInfoByPgDb(@Param("pg_db") String pgDb, @Param("pg_schema") String pgSchema);

  /**
   * 获取所有的CH库，因为db_sync_info的ch_db上有索引，所以这个sql并不会慢
   * @return
   */
  @Select("select distinct ch_db from db_sync_info where is_deleted=0 order by ch_db")
  List<String> queryChDBList();

  @Select("select * from db_sync_info where ch_db=#{ch_db} and pg_schema like 'sch\\_%' and is_deleted=0 limit 1")
  DBSyncInfoDO querySchemaDBSyncInfoByCHDB(@Param("ch_db") String chDB);
}
