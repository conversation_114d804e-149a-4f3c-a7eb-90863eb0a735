package com.fxiaoke.bi.warehouse.core.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;

/**
 * @Author:jief
 * @Date:2024/10/9
 */
@Data
public class DBMergeInfoDO {
  @Id
  @Column(name = "id")
  private String id;
  @Column(name = "ch_db")
  private String chDb;
  @Column(name = "pg_db")
  private String pgDb;//ch db名称
  @Column(name = "pg_schema")
  private String pgSchema;//pg库名称
  @Column(name = "status")
  private Integer status;
  @Column(name = "is_deleted")
  private Integer isDeleted;
  /**
   * 最后一次merge agg Time 时间
   */
  @Column(name = "last_merge_agg_time")
  private Long lastMergeAggTime;
  /**
   *是否开启增量分区同步0:否,1:是
   */
  @Column(name = "allow_inc_partition")
  private Integer allowIncPartition;
}
