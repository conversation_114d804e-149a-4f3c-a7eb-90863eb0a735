package com.fxiaoke.bi.warehouse.dws.transform.model;

/**
 * <AUTHOR>
 * @since 2022/7/27
 */
public enum CheckLevelType {

  type1(1),
  type2(2),
  type3(3),
  type4(4),
  type5(5),
  type6(6);
  public final int value;

  CheckLevelType(int value) {
    this.value = value;
  }

  public static boolean withXDimensions(CheckLevelType checkLevelType) {
    return checkLevelType != type2;
  }

  public static CheckLevelType parseFromValue(Integer value) {
    if (null == value) {
      return CheckLevelType.type1;
    }
    switch (value) {
      case 1:
        return CheckLevelType.type1;
      case 2:
        return CheckLevelType.type2;
      case 3:
        return CheckLevelType.type3;
      case 4:
        return CheckLevelType.type4;
      case 5:
        return CheckLevelType.type5;
      case 6:
        return CheckLevelType.type6;
      default:
        throw new IllegalArgumentException(value + " for CheckLevelType not valid.");
    }
  }
}
