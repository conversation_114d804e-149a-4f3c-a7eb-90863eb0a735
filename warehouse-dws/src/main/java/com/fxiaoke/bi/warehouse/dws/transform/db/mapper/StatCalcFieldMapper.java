package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatCalcFieldDO;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StatCalcFieldMapper extends ICrudMapper<StatCalcFieldDO>, ITenant<StatCalcFieldMapper> {
  @Select("select * from stat_agg_calc_field where ei=#{ei} and calc_field_id = any(array[#{calcIds}])")
  List<StatCalcFieldDO> getStatFieldByCalcId(@Param("ei") int ei, @Param("calcIds") String[] calcId);

  @Select("select ei,calc_field_id,field_name,db_obj_name,formula,field_type,is_predefined,format_str,precision " +
    " from stat_agg_calc_field where (ei=#{ei} or ei=-1) and calc_field_id=any(array[#{calcFieldIds}]) and formula is not null")
  List<StatCalcFieldDO> queryAggCalcFields(@Param("ei") Integer ei, @Param("calcFieldIds") String[] calcFields);
}
