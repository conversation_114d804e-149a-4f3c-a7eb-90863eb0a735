package com.fxiaoke.bi.warehouse.ods.controller;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.bean.ApiResult;
import com.fxiaoke.bi.warehouse.common.component.ClickHouseUtilService;
import com.fxiaoke.bi.warehouse.common.component.MybatisBITenantPolicy;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.ods.args.*;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.compare.ComparePgToChService;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChArg;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChCountResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChSampleArg;
import com.fxiaoke.bi.warehouse.ods.compare.service.ClickhouseDbCommonService;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseNodeInfo;
import com.fxiaoke.bi.warehouse.core.db.BiAggSyncInfoDao;
import com.fxiaoke.bi.warehouse.ods.integrate.service.CHDataToCHService;
import com.fxiaoke.bi.warehouse.ods.service.*;
import com.fxiaoke.bi.warehouse.ods.utils.InitSQL;
import com.github.jedis.support.JedisCmd;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class OdsController {

  @Resource
  private PgCommonDao pgCommonDao;
  @Resource
  private DBTransferService dbTransferService;
  @Resource
  private PGMetadataService pgMetadataService;
  @Resource
  private CHMetadataService chMetadataService;
  @Resource
  private CHDBService chdbService;
  @Resource
  private MergeTaskService mergeTaskService;
  @Resource
  private PgCommonService pgCommonService;
  @Resource
  private BiAggSyncInfoDao biAggSyncInfoDao;
  @Resource
  private MybatisBITenantPolicy mybatisBITenantPolicy;
  @Resource
  private ClickHouseUtilService clickHouseUtilService;
  @Resource
  private ComparePgToChService comparePgToChService;
  @Resource
  private CHDataToCHService chDataToCHService;
  @Autowired
  private DbTableSyncInfoService dbTableSyncInfoService;
  @Resource
  private ExecutePgSqlService executePgSqlService;
  @Resource
  private ClickhouseDbCommonService clickhouseDbCommonService;
  @Resource
  private DataCompareService dataCompareService;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;

  @GetMapping("/chJdbcUrl/{ei}")
  public String chJdbcUrl(@PathVariable("ei") String ei) {
    String url = chMetadataService.getCHJdbcURL(ei);
    return StringUtils.isBlank(url) ? "null" : url;
  }

  @GetMapping("/updateSyncDbStatus/{id}/{status}")
  public String updateSyncDbStatus(@PathVariable("id") String id, @PathVariable("status") int status) {
    log.info("updateSyncDbStatus json params:id:{},status:{}", id, status);
    pgCommonDao.updateTopologyStatusById(status, Lists.newArrayList(id));
    return String.format("update id:%s,status:%d,", id, status);
  }

  @GetMapping("/allowIncPartition/{id}/{status}")
  public String allowIncPartition(@PathVariable("id") String id, @PathVariable("status") int status) {
    log.info("allowIncPartition json params:id:{},status:{}", id, status);
    int result = pgCommonDao.allowIncPartition(status, Lists.newArrayList(id), WarehouseConfig.INC_PARTITION_NAME);
    return String.format("update id:%s,status:%d,result:%d,", id, status,result);
  }

  @GetMapping("/allowCalPartition/{id}/{status}")
  public String allowCalPartition(@PathVariable("id") String id, @PathVariable("status") int status) {
    log.info("allowCalPartition json params:id:{},status:{}", id, status);
    int result = pgCommonDao.allowIncPartition(status, Lists.newArrayList(id), WarehouseConfig.CAL_PARTITION_NAME);
    return String.format("update id:%s,status:%d,result:%d,", id, status,result);
  }

  @GetMapping("/allowPaas2bi/{id}/{status}")
  public String allowPaas2bi(@PathVariable("id") String id, @PathVariable("status") int status) {
    log.info("allowPaas2bi json params:id:{},status:{}", id, status);
    int result = pgCommonDao.allowIncPartition(status, Lists.newArrayList(id), WarehouseConfig.PAAS2BI_FLAG);
    return String.format("update id:%s,status:%d,result:%d,", id, status,result);
  }

  /**
   * 手动触发merge agg_data
   *
   * @param id
   * @param needSwitch
   * @return
   */
  @GetMapping("/doMergeAgg/{id}/{needSwitch}")
  public String doMergeAgg(@PathVariable("id") String id, @PathVariable("needSwitch") String needSwitch) {
    log.info("testMergeAgg json params:id:{},needSwitch:{}", id, needSwitch);
    ClickhouseNodeInfo clickhouseNodeInfo = dbTransferService.createCHNodeInfo(id);
    if (clickhouseNodeInfo == null) {
      return String.format("testMergeAgg error clickhouseNodeInfo is null id:%s,%s", id, needSwitch);
    }
    mergeTaskService.merge(clickhouseNodeInfo, dbTransferService.getMergePageSize(),
      dbTransferService.getChReadTimeOut(), BooleanUtils.toBoolean(needSwitch), false);
    return String.format("testMergeAgg finish id:%s,needSwitch:%s,", id, needSwitch);
  }

  @PostMapping("/batchUpdateSyncDbStatus")
  public String batchUpdateSyncDbStatus(@RequestBody Map<String, String> params) {
    log.info("batchUpdateSyncDbStatus json params:{}", JSON.toJSONString(params));
    String ids = params.get("ids");
    String status = params.get("status");
    if (StringUtils.isBlank(status)) {
      return "error: param status si empty!";
    }
    List<String> idList = Splitter.on(CharMatcher.anyOf(",|")).splitToList(ids);
    pgCommonDao.updateTopologyStatusById(Integer.parseInt(status), idList);
    return String.format("update id:%s,status:%s,", ids, status);
  }

  @PostMapping("/batchSendCalEvent")
  public String batchSendCalEvent(@RequestBody Map<String, String> params) {
    log.info("batchSendCalEvent json params:{}", JSON.toJSONString(params));
    String ids = params.get("ids");
    if (StringUtils.isNotBlank(ids)) {
      List<String> idList = Splitter.on(",").omitEmptyStrings().splitToList(ids);
      if (CollectionUtils.isNotEmpty(idList)) {
        pgCommonDao.batchSendCalMsgByIds(idList);
      }
    } else {
      return "ids is empty";
    }
    return "send ok";
  }

  @PostMapping("/upsertDbSyncInfo")
  public String upsertDbSyncInfo(@RequestBody List<DBSyncInfoArg> dbSyncInfoArgs) {
    log.info("upsertDbSyncInfo json params:{}", JSON.toJSONString(dbSyncInfoArgs));
    if (CollectionUtils.isNotEmpty(dbSyncInfoArgs)) {
      List<DBSyncInfo> dbSyncInfos = dbSyncInfoArgs.stream().map(DBSyncInfoArg::toDBSyncInfo).toList();
      pgCommonDao.batchUpsertDbSyncInfo(dbSyncInfos);
    }
    return "send ok";
  }

  /**
   * 批量添加上下游同步信息
   * @param biAggSyncInfoArgs
   * @return
   */
  @PostMapping("/upsertBiAggSyncInfo")
  public String upsertBiAggSyncInfo(@RequestBody List<BIAggSyncInfoArg> biAggSyncInfoArgs) {
    log.info("upsertBiAggSyncInfo json params:{}", JSON.toJSONString(biAggSyncInfoArgs));
    if (CollectionUtils.isNotEmpty(biAggSyncInfoArgs)) {
      List<BIAggSyncInfoDO> dbSyncInfos = biAggSyncInfoArgs.stream().map(BIAggSyncInfoArg::toBiAggSyncInfoDO).toList();
      biAggSyncInfoDao.saveAggSyncInfo(dbSyncInfos);
    }
    return "send ok";
  }

  /**
   * 批量删除db_sync_info
   * @param ids
   * @return
   */
  @PostMapping("/batchDeletedDbSyncInfo")
  public String batchDeletedDbSyncInfo(@RequestBody List<String> ids) {
    log.info("batchDeletedDbSyncInfo json params:{}", JSON.toJSONString(ids));
    if (CollectionUtils.isNotEmpty(ids)) {
      dbTableSyncInfoService.batchDeletedDbSyncInfo(ids);
    }
    return "deleted ok";
  }

  /**
   * 批量删除db_table_sync_info
   * 同步需要重新同步所有数据
   */
  @PostMapping("/batchDelDbTableSyncInfo")
  public String batchDelDbTableSyncInfo(@RequestBody CHPublicCreatorArg chPublicCreatorArg) {
    log.info("batchDelDbTableSyncInfo json params:{}", JSON.toJSONString(chPublicCreatorArg));
    int result = dbTableSyncInfoService.deleteTableSyncInfoByDbSyncId(chPublicCreatorArg.getDbSyncIds(), chPublicCreatorArg.getTables());
    return "deleted ok result:" + result;
  }

  /**
   * 批量添加路由
   *
   * @param params {"chDb":"","tenantIds":"1,2,3"}
   * @return
   */
  @PostMapping("/batchAddChRouter")
  public String batchAddChRouter(@RequestBody Map<String, String> params) {
    log.info("batchAddChRouter json params:{}", JSON.toJSONString(params));
    String chDb = params.get("chDb");
    String tenantIds = params.get("tenantIds");
    if (StringUtils.isBlank(chDb)) {
      return "error: dbSync chDb is empty";
    }
    if (StringUtils.isBlank(tenantIds)) {
      return "error:tenantIds is empty";
    }
    List<String> eis = Splitter.on(CharMatcher.anyOf(",|")).omitEmptyStrings().splitToList(tenantIds);
    Map<Integer, String> eiToEaMap = clickHouseUtilService.getEiToEaMap(eis);
    eis.forEach(ei -> {
      clickHouseUtilService.createChRoute(ei, chDb, mybatisBITenantPolicy.isStandalone(ei), eiToEaMap);
    });
    return "batchAddChRouter ok";
  }

  @PostMapping("/invalidateCHTablesCache")
  public String invalidateCHTablesCache(@RequestBody Map<String, String> params) {
    log.info("invalidateCHTablesCache json params:{}", JSON.toJSONString(params));
    String chDb = params.get("chDb");
    if (StringUtils.isBlank(chDb)) {
      chMetadataService.invalidateCHTablesCache(null);
    } else {
      List<String> chDBUrls = Splitter.on(CharMatcher.anyOf(",")).omitEmptyStrings().splitToList(chDb);
      if (CollectionUtils.isNotEmpty(chDBUrls)) {
        chDBUrls.forEach(chDbUrl -> chMetadataService.invalidateCHTablesCache(chDbUrl));
      }
    }
    return "invalidateCHTablesCache ok";
  }

  /**
   * 手动触发同步数据，支持按照id列表同步
   *
   * @param params
   * @return
   */
  @PostMapping("/syncDataByTable")
  public String syncDataByTable(@RequestBody Map<String, String> params) {
    log.info("syncDataByTable json params:{}", JSON.toJSONString(params));
    String ids = params.get("ids");
    String tableName = params.get("tableName");
    if (StringUtils.isBlank(tableName)) {
      return "error:tableName is empty";
    }
    if (StringUtils.isBlank(ids)) {
      return "error:ids is empty";
    }
    String tenantId = StringUtils.isBlank(params.get("tenantId")) ? "-1" : params.get("tenantId");
    String primaryKey = params.get("primaryKey");
    String partitionValue = params.getOrDefault("partition", "");
    if (StringUtils.isNotBlank(primaryKey)) {
      List<String> pks = Splitter.on(",").omitEmptyStrings().splitToList(primaryKey);
      dbTransferService.syncDataByTenantIdPrimaryKey(ids, tableName, tenantId, pks,true,partitionValue);
    } else {
      List<String> idList = Splitter.on(",").omitEmptyStrings().splitToList(ids);
      dbTransferService.syncDataByTenantId(idList, tableName, Lists.newArrayList(tenantId),partitionValue);
    }
    return "send ok";
  }

  /**
   * 更新同步表的最大修改时间
   *
   * @param resetMaxModifiedTimeArg
   * @return
   */
  @PostMapping("/updateSyncTableModifiedTime")
  public String updateSyncTableModifiedTime(@RequestBody ResetMaxModifiedTimeArg resetMaxModifiedTimeArg) {
    log.info("updateSyncTableModifiedTime json params:{}", JSON.toJSONString(resetMaxModifiedTimeArg));
    int result =pgCommonDao.updateDbTableMaxModifiedTime(resetMaxModifiedTimeArg);
    return resetMaxModifiedTimeArg.getDbSyncId()+":"+result;
  }


  @PostMapping("/createChTableSQL")
  public String createChTableSQL(@RequestBody CHPublicCreatorArg chPublicCreatorArg) {
    log.info("createChTableSQL json params:{}", JSON.toJSONString(chPublicCreatorArg));
    pgMetadataService.createCHInitSqL(chPublicCreatorArg,null);
    return "send ok";
  }

  /**
   * 重置CHDB同步信息，也就是恢复初始同步状态
   *
   * @param params
   * @return
   */
  @PostMapping("/resetChSyncInfo")
  public String resetChSyncInfo(@RequestBody Map<String, String> params) {
    log.info("resetChSyncInfo json params:{}", JSON.toJSONString(params));
    String chDBUrl = params.get("chDBUrl");
    if (StringUtils.isBlank(chDBUrl)) {
      return "error: param chDBUrl is empty!";
    }
    pgCommonDao.resetCHDbSyncInfo(chDBUrl);
    return String.format("resetChSyncInfo ok chDB:%s,", chDBUrl);
  }

  /**
   * 批量插入系统人员
   *
   * @param params
   * @return
   */
  @PostMapping("/batchAddSysUser")
  public String batchAddSysUser(@RequestBody Map<String, String> params) {
    log.info("batchAddSysUser json params:{}", JSON.toJSONString(params));
    String tenantIds = params.get("tenantIds");
    if (StringUtils.isBlank(tenantIds)) {
      return "error: param tenantIds is empty!";
    }
    List<String> tenantIdList = Splitter.on(CharMatcher.anyOf(",|")).splitToList(tenantIds);
    chdbService.batchInsertSysUser(tenantIdList, StringUtils.EMPTY);
    return "batchAddSysUser ok";
  }

  /**
   * 批量操作ch表
   * @param chPublicCreatorArg 参数
   * @param op 操作类型
   * @return
   */
  @PostMapping("/batchDDLOnCh/{op}")
  public String batchDDLOnCh(@RequestBody CHPublicCreatorArg chPublicCreatorArg, @PathVariable("op") String op) {
    log.info("batchDDLOnCh json params:{}", JSON.toJSONString(chPublicCreatorArg));
    return chMetadataService.batchDDLTableOnCh(chPublicCreatorArg, op);
  }

  @PostMapping("/createChTable")
  public String createChTable(@RequestBody Map<String, String> params) {
    log.info("createChTable params:{}", JSON.toJSONString(params));
    String tableName = params.get("tableName");
    String chDbUrl = params.get("chDbUrl");
    switch (tableName) {
      case "agg_data","agg_data_history" -> {
        chdbService.executeSQLWithJdbcUrl(chDbUrl, String.format(InitSQL.aggDataSQL,  tableName), 30 * 60 * 1000L);
      }
      case "bi_mt_data_tag_v" -> {
        chdbService.executeSQLWithJdbcUrl(chDbUrl, InitSQL.mtDataTagVSQL, 30 * 60 * 1000L);
      }
      case "bi_mt_data_tag_v2" -> {
        chdbService.executeSQLWithJdbcUrl(chDbUrl, InitSQL.mtDataTagVSQL2, 30 * 60 * 1000L);
      }
      case "agg_downstream_data"->{
        chdbService.executeSQLWithJdbcUrl(chDbUrl, InitSQL.agg_downstream_data, 30 * 60 * 1000L);
      }
      case "agg_data_sync_info"->{
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.agg_data_sync_info,30 * 60 * 1000L);
      }
      case "v_saleactionstage"->{
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.v_saleactionstage_sql,30 * 60 * 1000L);
      }
      case "goal_value_obj_snapshot"->{
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.GOAL_VALUE_OBJ_SNAPSHOT,30 * 60 * 1000L);
      }
      case "agg_log_data"->{
//        String database = chDbUrl.substring(chDbUrl.lastIndexOf("/") + 1);
//        chdbService.executeSQLWithJdbcUrl(chDbUrl, String.format(InitSQL.BI_ERP_DATA_SCREEN, database),30 * 60 * 1000L);
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.BI_ERP_DATA_SCREEN,30 * 60 * 1000L);
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA,30 * 60 * 1000L);
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V1,30 * 60 * 1000L);
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V2,30 * 60 * 1000L);
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V3,30 * 60 * 1000L);
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V4,30 * 60 * 1000L);
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V5,30 * 60 * 1000L);
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V6,30 * 60 * 1000L);
        chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V7,30 * 60 * 1000L);
      }
      case "object_data_lang"->chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.OBJECT_DATA_LANG,30 * 60 * 1000L);
      case "object_data"->chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.OBJECT_DATA,30 * 60 * 1000L);
      default -> {
        log.info("exec ch sql:{}", tableName);
        chdbService.executeSQLWithJdbcUrl(chDbUrl, tableName, 30 * 60 * 1000L);
      }
    }
    return "OK";
  }

  /**
   * 对比clickhouse隔离库和postgresql相关信息是否一致
   */
  @PostMapping("/compareChToPg")
  @ResponseBody
  public ApiResult getComparePgToChResult(@RequestBody ComparePgToChArg compareChToPgArg) {
    log.info("compareChToPg arg is {}", JSON.toJSONString(compareChToPgArg));
    try {
      ComparePgToChResult comparePgToChResult = comparePgToChService.getComparePgToChResult(compareChToPgArg);
      log.info("comparePgToChTableResult: {}", JSON.toJSONString(comparePgToChResult.getComparePgToChTableResult()));
      log.info("comparePgToChCountResultList: {}", JSON.toJSONString(comparePgToChResult.getComparePgToChCountResultList()));
      log.info("comparePgToChColumnResultList: {}", JSON.toJSONString(comparePgToChResult.getComparePgToChColumnResultList()));
      return ApiResult.success("compareChToPg finish");
      //return ApiResult.getApiResult(200, "对比完成", comparePgToChResult);
    } catch (Exception e) {
      log.error("compareChToPg error, compareChToPg arg is {}", JSON.toJSONString(compareChToPgArg), e);
      return ApiResult.error("compareChToPg fail");
    }
  }

  /**
   * 抽样对比公共库clickhouse数据库和postgresql数据库表条数
   */
  @PostMapping("/comparePgToChSampleArg")
  @ResponseBody
  public ApiResult getComparePgToChCountResult(@RequestBody ComparePgToChSampleArg comparePgToChSampleArg) {
    log.info("comparePgToChSampleArg arg is {}", JSON.toJSONString(comparePgToChSampleArg));
    try {
      List<ComparePgToChCountResult> comparePgToChCountSimpleResult = comparePgToChService.getComparePgsToChCountSimpleResult(comparePgToChSampleArg);
      log.info("comparePgToChCountSimpleResult:{}", JSON.toJSONString(comparePgToChCountSimpleResult));
      return ApiResult.success("comparePgToChSample finish");
      //return ApiResult.getApiResult(200, "对比完成", comparePgToChCountSimpleResult);
    } catch (Exception e) {
      log.error("comparePgToChSample error, comparePgToChSampleArg arg is {}", JSON.toJSON(comparePgToChSampleArg), e);
      return ApiResult.error("comparePgToChSample fail");
    }
  }
  @GetMapping("/transfer/login/{tenantId}")
  public String transferTenantLoginData(@PathVariable("tenantId") String tenantId) {
    return chDataToCHService.transferTenantLoginData(tenantId);
  }

  @GetMapping("/mergeDownstreamTableInfo/{upTenantId}/{dbSyncInfoId}")
  public String mergeDownstreamTableSyncInfo(@PathVariable("upTenantId") String upTenantId, @PathVariable("dbSyncInfoId")String dbSyncInfoId){
   dbTableSyncInfoService.mergeDownstreamTableSyncInfo(upTenantId,dbSyncInfoId);
   return "ok";
  }

  @GetMapping("/transfer/apiName/{tenantId}")
  public String transferTenantApiNameData(@PathVariable("tenantId") String tenantId) {
    return chDataToCHService.transferTenantApiNameData(tenantId);
  }

  @PostMapping("/executePgSql")
  @ResponseBody
  public String executePgSql(@RequestBody ExecutePgSqlArg executePgSqlArg) {
    log.info("executePgSql executePgSqlArg is {}", executePgSqlArg);
    try {
      return executePgSqlService.executePgSql(executePgSqlArg);
    } catch (Exception e) {
      log.error("executePgSql error, this executePgSqlArg is {}", executePgSqlArg, e);
      return "error";
    }
  }

  /**
   * 对比ClickHouse和PostgreSQL数据，当ClickHouse有而PostgreSQL中没有或已删除的数据时，
   * 在ClickHouse中标记为删除（is_deleted = -2）
   *
   * @param comparePgChDataArg 对比参数
   * @return 处理结果
   */
  @PostMapping("/compareAndMarkDeletedData")
  @ResponseBody
  public ApiResult compareAndMarkDeletedData(@RequestBody ComparePgChDataArg comparePgChDataArg) {
    log.info("compareAndMarkDeletedData arg is {}", JSON.toJSONString(comparePgChDataArg));
    try {
      String result = dataCompareService.compareAndMarkDeletedData(comparePgChDataArg);
      log.info("compareAndMarkDeletedData result: {}", result);
      return ApiResult.success(result);
    } catch (Exception e) {
      log.error("compareAndMarkDeletedData error, arg is {}", JSON.toJSONString(comparePgChDataArg), e);
      return ApiResult.error("处理失败: " + e.getMessage());
    }
  }

  /**
   * 批量修改ch表的TTL
   * @param chPublicCreatorArgs
   * @return
   */
  @PostMapping("/modifyChTableTTL")
  public String modifyChTableTTL(@RequestBody List<CHPublicCreatorArg> chPublicCreatorArgs) {
    log.info("modifyChTableTTL json params:{}", JSON.toJSONString(chPublicCreatorArgs));
    chMetadataService.batchModifyCustomChTableTTL(chPublicCreatorArgs);
    return "send ok";
  }

  @PostMapping("/repairPartition")
  public String repairPartition(@RequestBody List<CHPublicCreatorArg> chPublicCreatorArgs) {
    log.info("repairPartition json params:{}", JSON.toJSONString(chPublicCreatorArgs));
    chMetadataService.repairPartition(chPublicCreatorArgs);
    return "send ok";
  }

  @GetMapping("/checkChTableTTL")
  public String checkChTableTTL() {
    clickhouseDbCommonService.logTablesWithoutTTL();
    return "ok";
  }

  @GetMapping("/removeRedis/{key}")
  public String removeRedisByKey (@PathVariable("key") String key) {
    log.info("removeRedisByKey key:{}",key);
    jedisCmd.del(key);
    return String.format("removeRedisByKey key:%s success",key);
  }

  @PostMapping("/findAllStandaloneSchemas")
  public String findAllStandaloneSchemas(@RequestBody List<String> jdbcURLs) {
    Map<String, List<String>> result = pgMetadataService.findAllStandaloneSchemas(jdbcURLs);
    return JSON.toJSONString(result);
  }

  /**
   * 批量修改ch表的TTL
   * @param chPublicCreatorArgs
   * @return
   */
  @PostMapping("/modifyChTableSysModifiedTime")
  public String modifyChTableSysModifiedTime(@RequestBody List<CHPublicCreatorArg> chPublicCreatorArgs) {
    log.info("modifyChTableSysModifiedTime json params:{}", JSON.toJSONString(chPublicCreatorArgs));
    chMetadataService.batchModifyColumnDefault(chPublicCreatorArgs);
    return "send ok";
  }
}
