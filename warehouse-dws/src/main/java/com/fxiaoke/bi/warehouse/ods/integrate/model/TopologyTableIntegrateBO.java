package com.fxiaoke.bi.warehouse.ods.integrate.model;

import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.TopologyTableIntegrateDO;
import com.fxiaoke.bi.warehouse.common.bean.TopologyTableAggDownStream;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/4/23
 */
@Data
@Builder
public class TopologyTableIntegrateBO {
  private String tenantId;
  private String sourceId;
  private int source;
  private Map<String, String> statFieldLocation;
  private Map<String, String> uniqFieldLocation;
  private TopologyTableAggDownStream aggDownstreamJson;
  private Integer status;

  public static TopologyTableIntegrateBO from(TopologyTableIntegrateDO topologyTableIntegrateDO) {
    return TopologyTableIntegrateBO.builder()
                                   .tenantId(topologyTableIntegrateDO.getTenantId())
                                   .sourceId(topologyTableIntegrateDO.getSourceId())
                                   .source(topologyTableIntegrateDO.getSource())
                                   .statFieldLocation(topologyTableIntegrateDO.getStatFieldLocation())
                                   .uniqFieldLocation(topologyTableIntegrateDO.getUniqFieldLocation())
                                   .aggDownstreamJson(topologyTableIntegrateDO.getAggDownstreamJson())
                                   .status(topologyTableIntegrateDO.getStatus())
                                   .build();
  }
}
