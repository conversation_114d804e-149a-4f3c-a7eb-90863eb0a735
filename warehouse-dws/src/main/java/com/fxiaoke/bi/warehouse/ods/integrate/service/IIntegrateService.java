package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.ods.entity.Biz2CHConsumer;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据集成服务
 * @Author:jief
 * @Date:2024/3/31
 */
public interface IIntegrateService {
  void consumeIntegrateEvent(BIAggSyncInfoDO biAggSyncInfoDO, DBSyncInfoBO dbSyncInfoCopy, Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                             String partitionName, AtomicLong nextBatchNum);

  void transferByViewId(String tenantId,String viewId,long batchNum);

  ClickhouseTable createCHTable(String tenantId, String viewId);

  Biz2CHConsumer createCHConsumer(String tenantId,String viewId);

  List<String> findDownstreamEIs(String tenantId);
}
