package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.integrate.model.DownStreamSyncInfo;
import com.fxiaoke.bi.warehouse.ods.integrate.model.TopologyTableIntegrateBO;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.BIMtTopologyTableDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.TopologyTableIntegrateDO;
import com.fxiaoke.bi.warehouse.common.bean.TopologyTableAggDownStream;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.common.MapUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2024/4/23
 */
@Service
public class BiMtTopologyTableService {
  @Resource
  private BIMtTopologyTableDao biMtTopologyTableDao;
  private static final Set<String> filterFiledSet = Sets.newHashSet("tenant_id", "view_id", "view_version",
    "hash_code", "hash_code_without_date", "object_id", "action_date", "owner", "out_data_auth_code", "data_auth_code"
    , "out_tenant_id");

  public List<TopologyTableIntegrateBO> queryAllDownstreamTopologyTables(String tenantId) {
    List<TopologyTableIntegrateDO> topologyTableIntegrateDOS = biMtTopologyTableDao.queryAllDownstreamTopologyTables(tenantId);
    if (CollectionUtils.isNotEmpty(topologyTableIntegrateDOS)) {
      return topologyTableIntegrateDOS.stream().map(TopologyTableIntegrateBO::from).toList();
    }
    return null;
  }

  /**
   * 获取素有downstream tables
   *
   * @param topologyTableIntegrateBOS 下游统计图
   * @return
   */
  public Set<String> queryAllDownstreamTables(List<TopologyTableIntegrateBO> topologyTableIntegrateBOS) {
    Set<String> bizTables = Sets.newHashSet();
    if (CollectionUtils.isNotEmpty(topologyTableIntegrateBOS)) {
      topologyTableIntegrateBOS.forEach(r -> {
        TopologyTableAggDownStream downStreamInfo = r.getAggDownstreamJson();
        Set<String> tables = downStreamInfo.getUpTables();
        if (CollectionUtils.isNotEmpty(tables)) {
          tables.stream()
                .filter(tableName -> !Objects.equals(CHContext.AGG_DOWNSTREAM_DATA, tableName))
                .forEach(bizTables::add);
        }
      });
    }
    return bizTables;
  }

  /**
   * @param topologyTableIntegrateBOS
   * @return
   */
  public Set<TopologyTableAggDownStream.DownStreamInfo> buildDownstreamAggTables(List<TopologyTableIntegrateBO> topologyTableIntegrateBOS) {
    Set<TopologyTableAggDownStream.DownStreamInfo> downStreamInfoSet = Sets.newHashSet();
    topologyTableIntegrateBOS.forEach(topologyTableIntegrateBO -> {
      TopologyTableAggDownStream aggDownstreamJson = topologyTableIntegrateBO.getAggDownstreamJson();
      if (aggDownstreamJson != null) {
        List<TopologyTableAggDownStream.DownStreamInfo> downInfoList = aggDownstreamJson.getDownInfoList();
        if (CollectionUtils.isNotEmpty(downInfoList)) {
          downStreamInfoSet.addAll(downInfoList);
        }
      }
    });
    return downStreamInfoSet;
  }

  /**
   * @param tenantId          下游企业
   * @param downStreamInfoSet
   * @return
   */
  public Map<String, Set<String>> batchBuildClickhouseTable(String tenantId,
                                                            Set<TopologyTableAggDownStream.DownStreamInfo> downStreamInfoSet) {
    Map<String, Set<String>> viewAndFieldLocation = Maps.newHashMap();
    if (CollectionUtils.isNotEmpty(downStreamInfoSet)) {
      List<String> sourceIds = downStreamInfoSet.stream()
                                                .map(TopologyTableAggDownStream.DownStreamInfo::getDownViewId)
                                                .toList();
      List<TopologyTableIntegrateDO> fieldLocationDO = biMtTopologyTableDao.batchQueryFieldLocationByViewId(tenantId,
        sourceIds);
      if (CollectionUtils.isNotEmpty(fieldLocationDO)) {
        fieldLocationDO.forEach(fl -> {
          Map<String, String> fieldLocationMap = fl.getStatFieldLocation();
          if (!MapUtils.isNullOrEmpty(fieldLocationMap)) {
            viewAndFieldLocation.computeIfAbsent(fl.getSourceId(), key -> Sets.newHashSet())
                                .addAll(fieldLocationMap.values());
          }
        });
      }
    }
    if (org.apache.commons.collections4.MapUtils.isNotEmpty(viewAndFieldLocation)) {
      Map<String, Set<String>> map = Maps.newHashMap();
      for (Map.Entry<String, Set<String>> entry : viewAndFieldLocation.entrySet()) {
        Set<String> value = entry.getValue();
        Set<String> setValue = value.stream().filter(x -> !filterFiledSet.contains(x)).collect(Collectors.toSet());
        map.put(entry.getKey(), setValue);
      }
      return map;
    }
    return viewAndFieldLocation;
  }

  /**
   * 获取下游相关同步信息
   */
  public List<DownStreamSyncInfo> getDownsStreamSyncInfo(String tenantId, Set<TopologyTableAggDownStream.DownStreamInfo> downStreamInfoSet) {
    if (org.apache.commons.collections4.CollectionUtils.isEmpty(downStreamInfoSet) || StringUtils.isBlank(tenantId)) {
      return Lists.newArrayList();
    }
    List<String> sourceIds = downStreamInfoSet.stream().map(TopologyTableAggDownStream.DownStreamInfo::getDownViewId).toList();
    List<TopologyTableIntegrateDO> topologyTableIntegrateDOList = biMtTopologyTableDao.batchQueryFieldLocationByViewId(tenantId, sourceIds);
    if (org.apache.commons.collections4.CollectionUtils.isEmpty(topologyTableIntegrateDOList)) {
      return Lists.newArrayList();
    }
    List<DownStreamSyncInfo> downStreamSyncInfoList = Lists.newArrayList();
    for (TopologyTableIntegrateDO topologyTableIntegrateDO : topologyTableIntegrateDOList) {
      DownStreamSyncInfo downStreamSyncInfo = new DownStreamSyncInfo();
      Map<String, String> statFieldLocation = topologyTableIntegrateDO.getStatFieldLocation();
      List<String> fieldLocaltionList = statFieldLocation.values().stream().toList();
      downStreamSyncInfo.setViewId(topologyTableIntegrateDO.getSourceId());
      downStreamSyncInfo.setFieldLocaltionList(fieldLocaltionList);
      downStreamSyncInfo.setFieldIdToFieldLocaltionMap(topologyTableIntegrateDO.getStatFieldLocation());
      downStreamSyncInfo.setStat_view_unique_key(topologyTableIntegrateDO.getStatViewUniqueKey());
      if (GrayManager.isAllowByRule("combine_agg_ei", tenantId)) {
        downStreamSyncInfo.setIsStatMerge(1);
      } else {
        downStreamSyncInfo.setIsStatMerge(0);
      }
      downStreamSyncInfoList.add(downStreamSyncInfo);
    }
    return downStreamSyncInfoList;
  }
}
