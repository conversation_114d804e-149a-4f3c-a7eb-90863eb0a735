package com.fxiaoke.bi.warehouse.ods.context;

import com.fxiaoke.bi.warehouse.ods.bean.DBColumnType;
import com.fxiaoke.bi.warehouse.ods.dao.MetaDataDao;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseColumn;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.bi.warehouse.ods.utils.InitSQL;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2024/8/21
 */
@Slf4j
public enum CHStaticSQL {
  agg_data{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      return String.format(InitSQL.aggDataSQL,"agg_data");
    }
  },
  agg_data_history{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      return String.format(InitSQL.aggDataSQL,"agg_data_history");
    }
  },
  bi_mt_data_tag_v{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      return InitSQL.mtDataTagVSQL2;
    }
  },
  v_saleactionstage{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      String  v_saleactionstage_sql = """
    CREATE OR REPLACE VIEW v_saleactionstage ON CLUSTER '{cluster}' AS
    SELECT ((
                    CASE
                        WHEN sale_action_stage.stage_order < 10 THEN '0'
                        ELSE ''
                        END || toString(sale_action_stage.stage_order)) || '. ') ||
           sale_action_stage.name AS ordered_name,
           sale_action_stage.ei,
           sale_action_stage.sale_action_stage_id,
           sale_action_stage.sale_action_id,
           sale_action_stage.name,
           sale_action_stage.description,
           sale_action_stage.stage_order,
           sale_action_stage.is_timeout_remind,
           sale_action_stage.remain_days,
           sale_action_stage.is_leader_confirm,
           sale_action_stage.is_finish_by_contacts,
           sale_action_stage.contact_count,
           sale_action_stage.type,
           sale_action_stage.as_is_that_day_remind,
           sale_action_stage.as_that_remain_days,
           sale_action_stage.as_is_timeout_remind,
           sale_action_stage.as_remain_days,
           sale_action_stage.update_time,
           sale_action_stage.weight,
           sale_action_stage.stage_flag,
           toString(sale_action_stage.ei) AS tenant_id,
           sale_action_stage.bi_sys_flag,
           sale_action_stage.bi_sys_batch_id,
           sale_action_stage.bi_sys_version,
           sale_action_stage.bi_sys_is_deleted,
           %s
           toInt16(0) as is_deleted
    FROM sale_action_stage
    SETTINGS final = 1, join_use_nulls = 1;
    """;
      Optional<ClickhouseTable> clickhouseTableOP = chMetadataService.loadTableFromDB(chMetadataService.getCHJdbcURL(tenantId), "sale_action_stage");
      if (clickhouseTableOP.isPresent()) {
        if (clickhouseTableOP.get().existsPartitionKey()) {
          return String.format(v_saleactionstage_sql, "sale_action_stage.bi_sys_ods_part,");
        }
      }
      return String.format(v_saleactionstage_sql, "");
    }
  },
  goal_value_obj_snapshot{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return InitSQL.GOAL_VALUE_OBJ_SNAPSHOT;
    }
  },
  biz_account_main_data{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      String table="biz_account";
      String columns;
      if(needCreate){
        String chDBUrl = chMetadataService.getCHJdbcURL(tenantId);
        String dbName = CommonUtils.getDBName(chDBUrl);
        List<ClickhouseColumn> clickhouseColumns=  chMetadataService.loadColumnsPlus(chDBUrl,dbName,table);
        if (CollectionUtils.isEmpty(clickhouseColumns)) {
          log.error("find all pg type by table is empty tenantId:{},table:{},schemaName:{}", tenantId, table, schemaName);
          return null;
        }
        columns = clickhouseColumns.stream().map(ClickhouseColumn::getName).filter(name-> !StringUtils.equalsAny(name, CHContext.BI_SYS_BATCH_ID, CHContext.BI_SYS_IS_DELETED,
          CHContext.BI_SYS_VERSION, CHContext.BI_SYS_ODS_PART)).collect(Collectors.joining(","));
      }else{
        List<DBColumnType> dbColumnTypes = metaDataDao.findAllPgTypeByTable(tenantId, table, schemaName);
        if (CollectionUtils.isEmpty(dbColumnTypes)) {
          log.error("find all pg type by table is empty tenantId:{},table:{},schemaName:{}", tenantId, table, schemaName);
          return null;
        }
        //如果是删除的自定义槽位
        columns = dbColumnTypes.stream().map(DBColumnType::getColumnName).collect(Collectors.joining(","));
        columns+=","+CHContext.BI_SYS_FLAG;
      }
      StringBuilder chSQL = new StringBuilder(" CREATE OR REPLACE VIEW ");
      chSQL.append("biz_account_main_data");
      if (StringUtils.isNotBlank(cluster)) {
        chSQL.append(" ON CLUSTER '").append(cluster).append("'");
      }
      chSQL.append(" AS ").append("\n");
      chSQL.append("  SELECT ");
      chSQL.append(columns).append(" FROM ").append(table).append(" final ");
      chSQL.append(" WHERE ").append("object_describe_api_name = 'AccountMainDataObj'");
      return chSQL.toString();
    }
  },
  biz_user_login_online_operation_ods{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return """
        CREATE TABLE IF NOT EXISTS biz_user_login_online_operation_ods  on cluster '{cluster}'
                (
                   `id` String,
                   `name` String,
                   `tenant_id` String,
                   `login_user` String,
                   `login_user_dept` String,
                   `active_time` Int64,
                   `login_type` String,
                   `login_type_name` String,
                   `device_model` String,
                   `login_ip` String,
                   `app_name` String,
                   `last_modified_time` Int64,
                   `last_modified_by` String,
                   `create_time` Int64,
                   `created_by` String,
                   `owner` String,
                   `data_own_department` String,
                   `data_own_organization` String,
                   `out_data_own_department` String,
                   `out_data_own_organization` String,
                   `connected_enterprise` String,
                   `out_owner` String,
                   `data_create_time` DateTime64(9) DEFAULT now64(9),
                   `sys_modified_time` Int64 DEFAULT toUnixTimestamp64Micro(now64(9)),
                   `dt` String,
                   `traceid` String,
                   `bi_sys_version` DateTime DEFAULT now()
               )
               ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}')
               PARTITION BY toYYYYMM(data_create_time)
               ORDER BY (tenant_id, id)
               TTL bi_sys_version + toIntervalDay(30)
               SETTINGS index_granularity = 8192
        """;
    }
  },
  biz_user_login_online_operation{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return """
         CREATE TABLE IF NOT EXISTS biz_user_login_online_operation on cluster '{cluster}'
                 (
                    `id` String,
                    `name` String,
                    `tenant_id` String,
                    `login_user` String,
                    `login_user_dept` String,
                    `active_time` Int64,
                    `login_type` String,
                    `login_type_name` String,
                    `device_model` String,
                    `login_ip` String,
                    `app_name` String,
                    `last_modified_time` Int64,
                    `last_modified_by` String,
                    `create_time` Int64,
                    `created_by` String,
                    `owner` String,
                    `data_own_department` String,
                    `connected_enterprise` String,
                    `out_owner` String,
                    `out_tenant_id` String,
                    `data_auth_code` String,
                    `out_data_auth_code` String,
                    `data_own_organization` String,
                    `out_data_own_department` String,
                    `out_data_own_organization` String,
                    `is_deleted` Int8,
                    `bi_sys_flag` Int8,
                    `bi_sys_batch_id` Int64,
                    `bi_sys_is_deleted` UInt8,
                    `bi_sys_version` DateTime DEFAULT now(),
                    `sys_modified_time` Int64 DEFAULT toUnixTimestamp64Micro(now64(9)),
                    `bi_sys_ods_part` String DEFAULT 's',
                    `traceid` String,
                    INDEX idx_owner owner TYPE set(1000) GRANULARITY 1
                )
                ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_is_deleted)
                PARTITION BY bi_sys_ods_part
                ORDER BY (tenant_id, name, bi_sys_flag, id)
                TTL bi_sys_version + toIntervalWeek(1) WHERE bi_sys_ods_part = 'i'
                SETTINGS index_granularity = 8192;
        """;
    }
  },
  biz_user_api_name_operation_ods{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return """
        CREATE TABLE IF NOT EXISTS biz_user_api_name_operation_ods on cluster '{cluster}'
         (
            `id` String,
            `name` String,
            `tenant_id` String,
            `operator` String,
            `operator_dept` String,
            `operate_time` Int64,
            `api_name` String,
            `operate_data_id` String,
            `operate_type` String,
            `app_name` String,
            `last_modified_time` Int64,
            `last_modified_by` String,
            `create_time` Int64,
            `created_by` String,
            `data_own_department` String,
            `data_own_organization` String,
            `out_data_own_department` String,
            `out_data_own_organization` String,
            `owner_department` String,
            `owner` String,
            `connected_enterprise` String,
            `out_owner` String,
            `data_create_time` DateTime64(9) DEFAULT now64(9),
            `sys_modified_time` Int64 DEFAULT toUnixTimestamp64Micro(now64(9)),
            `dt` String,
            `flow_type` String,
            `flow_name` String,
            `traceid` String,
            `bi_sys_version` DateTime DEFAULT now()
        )
        ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}')
        PARTITION BY toYYYYMM(data_create_time)
        ORDER BY (tenant_id, id)
        TTL bi_sys_version + toIntervalDay(30)
        SETTINGS index_granularity = 8192;
        """;
    }
  },
  biz_user_api_name_operation{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return """
        CREATE TABLE IF NOT EXISTS biz_user_api_name_operation on cluster '{cluster}'
         (
            `id` String,
            `name` String,
            `tenant_id` String,
            `operator` String,
            `operator_dept` String,
            `operate_time` Int64,
            `api_name` String,
            `operate_data_id` String,
            `operate_type` String,
            `app_name` String,
            `last_modified_time` Int64,
            `last_modified_by` String,
            `create_time` Int64,
            `created_by` String,
            `data_own_department` String,
            `owner_department` String,
            `owner` String,
            `connected_enterprise` String,
            `out_owner` String,
            `out_tenant_id` String,
            `data_auth_code` String,
            `out_data_auth_code` String,
            `data_own_organization` String,
            `out_data_own_department` String,
            `out_data_own_organization` String,
            `is_deleted` Int8,
            `bi_sys_flag` Int8,
            `bi_sys_batch_id` Int64,
            `bi_sys_is_deleted` UInt8,
            `bi_sys_version` DateTime DEFAULT now(),
            `sys_modified_time` Int64 DEFAULT toUnixTimestamp64Micro(now64(9)),
            `bi_sys_ods_part` String DEFAULT 's',
            `flow_type` String,
            `flow_name` String,
            `traceid` String,
            INDEX idx_owner owner TYPE set(1000) GRANULARITY 1
        )
        ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version, bi_sys_is_deleted)
        PARTITION BY bi_sys_ods_part
        ORDER BY (tenant_id, bi_sys_flag, api_name, operate_data_id, id)
        TTL bi_sys_version + toIntervalWeek(1) WHERE bi_sys_ods_part = 'i'
        SETTINGS index_granularity = 8192;
        """;
    }
  },
  stage_runtime_new_opportunity {
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      return InitSQL.stage_runtime_new_opportunity;
    }
  },
  stage_runtime_task_new_opportunity {
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      return InitSQL.stage_runtime_task_new_opportunity;
    }
  },  bi_data_sync_policy_log {
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      return InitSQL.bi_data_sync_policy_log;
    }
  },  biz_user_bi_operation_ods {
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      return InitSQL.biz_user_bi_operation_ods;
    }
  },  biz_user_bi_operation {
    @Override
    public String buildSQL(CHMetadataService chMetadataService,MetaDataDao metaDataDao,String tenantId,String schemaName,String cluster,boolean needCreate) {
      return InitSQL.biz_user_bi_operation;
    }
  }, dim_name_data {
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return InitSQL.dimNameData;
    }
  },bi_agg_log{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return InitSQL.biAggLog;
    }
  },agg_downstream_data{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return InitSQL.agg_downstream_data;
    }
  },object_data{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return InitSQL.OBJECT_DATA;
    }
  },object_data_lang{
    @Override
    public String buildSQL(CHMetadataService chMetadataService,
                           MetaDataDao metaDataDao,
                           String tenantId,
                           String schemaName,
                           String cluster,
                           boolean needCreate) {
      return InitSQL.OBJECT_DATA_LANG;
    }
  },agg_data_sync_info{
    @Override
    public String buildSQL(CHMetadataService chMetadataService, MetaDataDao metaDataDao, String tenantId, String schemaName, String cluster,boolean needCreate){
      return InitSQL.agg_data_sync_info;
    }
  }
  ;

  public String buildSQL(CHMetadataService chMetadataService, MetaDataDao metaDataDao, String tenantId, String schemaName, String cluster,boolean needCreate){
    return null;
  }

  public static CHStaticSQL of(String tableName) {
    return switch (tableName) {
      case "agg_data" -> CHStaticSQL.agg_data;
      case "agg_data_history" -> CHStaticSQL.agg_data_history;
      case "bi_mt_data_tag_v" -> CHStaticSQL.bi_mt_data_tag_v;
      case "biz_account_main_data" -> CHStaticSQL.biz_account_main_data;
      case "v_saleactionstage" -> CHStaticSQL.v_saleactionstage;
      case "goal_value_obj_snapshot" -> CHStaticSQL.goal_value_obj_snapshot;
      case "biz_user_login_online_operation_ods" -> CHStaticSQL.biz_user_login_online_operation_ods;
      case "biz_user_login_online_operation" -> CHStaticSQL.biz_user_login_online_operation;
      case "biz_user_api_name_operation_ods" -> CHStaticSQL.biz_user_api_name_operation_ods;
      case "biz_user_api_name_operation" -> CHStaticSQL.biz_user_api_name_operation;
      case "stage_runtime_new_opportunity" -> CHStaticSQL.stage_runtime_new_opportunity;
      case "stage_runtime_task_new_opportunity" -> CHStaticSQL.stage_runtime_task_new_opportunity;
      case "bi_data_sync_policy_log" -> CHStaticSQL.bi_data_sync_policy_log;
      case "biz_user_bi_operation_ods" -> CHStaticSQL.biz_user_bi_operation_ods;
      case "biz_user_bi_operation" -> CHStaticSQL.biz_user_bi_operation;
      case "dim_name_data" -> CHStaticSQL.dim_name_data;
      case "bi_agg_log" -> CHStaticSQL.bi_agg_log;
      case "agg_downstream_data" -> CHStaticSQL.agg_downstream_data;
      case "agg_data_sync_info" -> CHStaticSQL.agg_data_sync_info;
      default -> null;
    };
  }


}
