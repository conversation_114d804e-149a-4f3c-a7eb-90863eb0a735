package com.fxiaoke.bi.warehouse.core.db;

import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.core.db.entity.BIMtDatabaseDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.BIMtDatabaseMapper;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.common.PasswordUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.github.mybatis.filter.SlowFilter;
import com.github.mybatis.tenant.TenantContext;
import com.github.mybatis.tenant.TenantPolicy;
import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * @Author:jief
 * @Date:2023/6/1
 */
@Slf4j
@Service
public class ClickhouseTenantPolicy implements TenantPolicy {
  public static final String BIZ = "BI";
  public static final String APPLICATION = "fs-bi-stat-calculate";
  public static final String DIALECT = "clickhouse";
  @Getter
  private String chProxyCalUserNamePre;
  @Getter
  private String chProxyCalPassword;
  @Getter
  private String chProxyQueryUserNamePre;
  @Getter
  private String chProxyQueryPassword;
  @Autowired
  private DbRouterClient dbRouterClient;
  @Autowired
  private BIMtDatabaseMapper biMtDatabaseMapper;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", new IniChangeListener("common") {
      @Override
      public void iniChanged(IniConfig config) {
        //写账号
        chProxyCalUserNamePre = config.get("chproxy.cal.user.name.pre");
        chProxyCalPassword = PasswordUtil.decode(config.get("chproxy.cal.paasword"));
        //只读账号
        chProxyQueryUserNamePre = config.get("chproxy.query.user.name.pre");
        chProxyQueryPassword = PasswordUtil.decode(config.get("chproxy.query.paasword"));
//      慢sql采集参数
        SlowFilter.thresholdInNanoSecond = TimeUnit.MILLISECONDS.toNanos(config.getLong("slow.threshold", 1000L));
        SlowFilter.thresholdInSize =config.getInt("slow.threshold.size", 8096);
        SlowFilter.thresholdLength = config.getLong("slow.threshold.length", 10485760L);
      }
    });
  }

  @Override
  public TenantContext get(String tenantId, boolean readOnly) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, false);
    String chDBName = routerInfo.getDbName();
    String userNameSuffix = chDBName.substring(0, chDBName.length() - 3);
    String schemaName = "public";
    return TenantContext.builder()
                        .id(tenantId)
                        .schema(schemaName)
                        .url("jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + routerInfo.getDbName())
                        .username(chProxyCalUserNamePre + userNameSuffix)
                        .password(chProxyCalPassword)
                        .build();
  }

  public RouterInfo getRouterInfo(String tenantId) {
    Preconditions.checkArgument(StringUtils.isNotBlank(tenantId), "tenantId is blank");
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT);
      boolean isBIStandalone = routerInfo.getStandalone();
      routerInfo.setStandalone(isBIStandalone);
      return routerInfo;
    } catch (Exception e) {
      log.error("getRouterInfo error tenantID:{},errormsg:{}", tenantId, e.getMessage());
    }
    return null;
  }

  public String getCHJdbcURL(String tenantId) {
    RouterInfo routerInfo = this.getRouterInfo(tenantId);
    if (routerInfo != null) {
      return "jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + routerInfo.getDbName();
    }
    return null;
  }

  /**
   * 获取db名称
   *
   * @param tenantId 租户id
   * @return
   */
  public String getDBName(String tenantId,String databaseId) {
    if(StringUtils.isNotBlank(databaseId)){
      BIMtDatabaseDO biMtDatabaseDO = biMtDatabaseMapper.setTenantId("-1").queryDatabaseById(databaseId,"-1");
      if(biMtDatabaseDO!=null){
       return biMtDatabaseDO.getDatabaseName();
      }
      biMtDatabaseDO= biMtDatabaseMapper.setTenantId(tenantId).queryDatabaseById(databaseId,tenantId);
      if(biMtDatabaseDO!=null){
        return biMtDatabaseDO.getDatabaseName();
      }
    }
    String jdbcURL = this.getCHJdbcURL(tenantId);
    if (StringUtils.isBlank(jdbcURL)) {
      return null;
    }
    return CHContext.getDBName(jdbcURL);
  }

  public String getCHJdbcURL(String tenantId, String databaseId) {
    if (StringUtils.isNotBlank(databaseId)) {
      BIMtDatabaseDO biMtDatabaseDO = biMtDatabaseMapper.setTenantId("-1").queryDatabaseById(databaseId, "-1");
      if (biMtDatabaseDO != null) {
        return "jdbc:clickhouse://" + biMtDatabaseDO.getMaster() + "/" + biMtDatabaseDO.getDatabaseName();
      }
      biMtDatabaseDO = biMtDatabaseMapper.setTenantId(tenantId).queryDatabaseById(databaseId, tenantId);
      if (biMtDatabaseDO != null) {
        return "jdbc:clickhouse://" + biMtDatabaseDO.getMaster() + "/" + biMtDatabaseDO.getDatabaseName();
      }
    }
    String jdbcURL = this.getCHJdbcURL(tenantId);
    if (StringUtils.isBlank(jdbcURL)) {
      return null;
    }
    return jdbcURL;
  }
}
