package com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.GoalRuleDO;
import com.github.mybatis.handler.list.ListTypeHandler;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudPaginationMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author:jief
 * @Date:2023/6/24
 */
@Repository
public interface GoalRuleMapper extends ICrudPaginationMapper<GoalRuleDO>, IBatchMapper<GoalRuleDO>, ITenant<GoalRuleMapper> {
  @Select(
    "select * from goal_rule where tenant_id=#{tenantId} and id=#{id} and is_deleted=0 limit 1")
  @Results(id = "findCountFiscalYears", value = {@Result(property = "countFiscalYear", column = "count_fiscal_year",
    typeHandler = ListTypeHandler.class)})
  GoalRuleDO getRuleEntityById(@Param("tenantId") String tenantId, @Param("id") String id);

  @Select("select id from goal_rule where tenant_id=#{tenantId} and status='1' and is_deleted=0")
  List<String> getAllGoalRuleIdByEi(@Param("tenantId") String tenantId);

  @Select("select * from goal_rule where tenant_id=#{tenantId} and is_delete=0 and status='1' and last_modified_time>${fromDateTime}")
  List<GoalRuleDO> batchQueryGoalRuleByEi(@Param("ei") int ei, @Param("fromDateTime") long fromDateTime);
}
