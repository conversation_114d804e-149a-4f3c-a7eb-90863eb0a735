package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Date 20240423
 * <AUTHOR>
 * @Desc 同步agg_data before
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SynchronizationBeforeAggDataArg {

    /**
     * 下游企业id
     */
    private String downStreamTenantId;

    /**
     * 下游企业路由信息
     */
    private RouterInfo routerInfo;

    /**
     * schema名称
     */
    private String dbName;

    /**
     * 统计图图表id
     */
    private String viewId;

    /**
     * 上次同步时间
     */
    private Long lastTimestamp;

    /**
     * 本次同步时间
     */
    private Long currentTimestamp;

    /**
     * 统计图版本
     */
    private int viewVersion;

    /**
     * 日期节点
     */
    private String actionDate;

    /**
     * 同步的hash_code
     */
    private String aggDataHashCode;

    public static SynchronizationBeforeAggDataArg getSynchronizationBeforeAggDataArg(AggDataSyncInfoDo aggDataSyncInfoDo, RouterInfo routerInfo, String dbName, long currentTimeMillis) {
        return SynchronizationBeforeAggDataArg.builder()
                                              .downStreamTenantId(aggDataSyncInfoDo.getTenantId())
                                              .routerInfo(routerInfo)
                                              .dbName(dbName)
                                              .viewId(aggDataSyncInfoDo.getViewId())
                                              .lastTimestamp(aggDataSyncInfoDo.getMaxSyncTimeStamp())
                                              .currentTimestamp(currentTimeMillis)
                                              .viewVersion(aggDataSyncInfoDo.getViewVersion())
                                              .aggDataHashCode("0")
                                              .build();
    }
}
