package com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.GoalRuleDetailDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author:jief
 * @Date:2023/6/30
 */
@Repository
public interface GoalRuleDetailMapper  extends ICrudMapper<GoalRuleDetailDO>, IBatchMapper<GoalRuleDetailDO>, ITenant<GoalRuleDetailMapper> {

  @Select("select * from goal_rule_detail where tenant_id = #{tenant_id} and goal_rule_id = #{goal_rule_id} and is_deleted=0 ")
  List<GoalRuleDetailDO> queryByTenantIdAndRuleId(@Param("tenant_id") String tenantId, @Param("goal_rule_id") String goalRuleId);

  @Select("select * from goal_rule_detail where tenant_id = #{tenant_id} and goal_rule_id = #{goal_rule_id} and id=#{id} and is_deleted=0 ")
  List<GoalRuleDetailDO> queryByTenantIdAndSubRuleId(@Param("tenant_id") String tenantId, @Param("goal_rule_id") String goalRuleId, @Param("id") String id);

}
