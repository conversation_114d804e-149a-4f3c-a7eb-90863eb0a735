package com.fxiaoke.bi.warehouse.ods.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/11/2
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClickhouseColumn implements Comparable<ClickhouseColumn> {
  private String name;
  private ClickHouseColumnType type;
  private int order;
  private String typeName;
  private int inSortKey;
  private String defaultKind;
  private String defaultExpression;

  @Override
  public int compareTo(ClickhouseColumn o) {
    return order - o.order;
  }

  /**
   * 判断类型是否一致
   *
   * @param o 其他列
   * @return boolean
   */
  public boolean isSameTypeWith(ClickhouseColumn o) {
    return Objects.equals(this.getType(), o.getType()) &&
      Objects.equals(this.buildDefaultExpression(), o.buildDefaultExpression());
  }

  public String buildDefaultExpression() {
    String defaultValue = "";
    if (StringUtils.isNotBlank(this.defaultExpression) && StringUtils.isNotBlank(this.defaultKind)) {
      defaultValue = String.format("%s %s", this.defaultKind, this.defaultExpression);
    }
    return defaultValue;
  }

  /**
   * 根据字段操作类型生成对应的alter 命令
   *
   * @param dbName
   * @param tableName
   * @param onCluster
   * @return
   */
  public String createAlterSQL(String alterType, String dbName, String tableName, String onCluster) {
    final String defaultExpression = this.buildDefaultExpression();
    return switch (alterType) {
      case "I" ->
        String.format("ALTER TABLE `%s`.`%s` ADD COLUMN IF NOT EXISTS `%s` %s %s", dbName, tableName, this.name, this.typeName, defaultExpression);
      case "U" ->
        String.format("ALTER TABLE `%s`.`%s` MODIFY COLUMN IF EXISTS `%s` %s %s", dbName, tableName,  this.name, this.typeName, defaultExpression);
      case "D" ->
        String.format("ALTER TABLE `%s`.`%s` DROP COLUMN IF EXISTS `%s`", dbName, tableName, this.name);
      default -> null;
    };
  }
}
