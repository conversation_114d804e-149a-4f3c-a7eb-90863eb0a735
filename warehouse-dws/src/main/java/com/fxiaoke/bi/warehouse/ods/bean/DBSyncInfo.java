package com.fxiaoke.bi.warehouse.ods.bean;

import lombok.Data;
import lombok.Getter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "db_sync_info")
@Data
@Getter
public class DBSyncInfo {
  @Id
  @Column(name = "id")
  private String id;
  @Column(name = "ch_db")
  private String chDb;
  @Column(name = "pg_db")
  private String pgDb;//ch db名称
  @Column(name = "pg_schema")
  private String pgSchema;//pg库名称
  @Column(name = "status")
  private Integer status;
  @Column(name = "batch_num")
  private Long batchNum;//批次号
  @Column(name = "last_sync_time")
  private Long lastSyncTime;    //上次同步开始的时间戳
  @Column(name = "create_time")
  private Long createTime;//上次拉取批次号
  @Column(name = "last_modified_time")
  private Long lastModifiedTime;//拉取的最大的修改时间
  @Column(name = "is_deleted")
  private Integer isDeleted;
  /**
   * 最后一次merge agg Time 时间
   */
  @Column(name = "last_merge_agg_time")
  private Long lastMergeAggTime;
  /**
   * 最近一次同步的租户id列表逗号分隔
   */
  @Column(name = "last_sync_eis")
  private String lastSyncEis;
  /**
   *是否开启增量分区同步0:否,1:是
   */
  @Column(name = "allow_inc_partition")
  private Integer allowIncPartition;
  /**
   *
   * 最近一次1+N集成时间
   */
  @Column(name = "last_integrate_time")
  private long lastIntegrateTime;
  /**
   * 是否允许paas2bi同步
   */
  @Column(name="allow_paas2bi_status")
  private int allowPaas2biStatus;
}
