package com.fxiaoke.bi.warehouse.dws.utils.sql;

import com.fxiaoke.bi.warehouse.common.db.er.AggRuleType;
import com.fxiaoke.bi.warehouse.common.db.er.SqlSelect;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.TemplateUtil;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.transform.model.AggJoinType;
import com.fxiaoke.bi.warehouse.dws.transform.model.GoalChangeType;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.Pair;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhaomh
 * @Description: 目标sql硬编码
 * @Date: Created in 2024/8/13
 * @Modified By:
 */
@Slf4j
public class GoalSqlUtils extends SQLUtils {
  /**
   * 构建目标全量计算sql
   * @param isPre 是否是预览sql,因为预览sql的with无法在查询侧作为内层sql,所以预览sql要调整with语句
   * @return
   */
  public static String buildGoalViewSql(TopologyTable statView,
                                        String viewSQL,
                                        List<SqlSelect> sqlSelectList,
                                        TopologyTableAggRule tableAggRule,
                                        boolean isView,
                                        boolean isPre,
                                        List<String> partitions) {
    Map<String, String> checkFieldDims = checkFieldDimsAll(tableAggRule);
    List<String> goalColumnList = Lists.newArrayList(checkFieldDims.keySet());
    String tenantId = statView.getTenantId();
    String checkCyCle = tableAggRule.getCheckCyCle();
    goalColumnList.sort(null);
    //如果是查看明细(statView.getSource() == 3),且是预览sql,则会给statView.getDetailSource()赋值(该字段不落库),应该用statView.getDetailSource()值
    int preSource = statView.getSource() == 3 && isPre ? statView.getDetailSource() : statView.getSource();
    String goalValueObj = createGoalValueObj(tenantId, statView.getDatabase(), statView.getViewId(), goalColumnList,
      true, checkCyCle, preSource, tableAggRule.getCheckLevelType(), isView, statView.getGoalRuleIds(), isPre, partitions);
    String goalDim = createGoalDim(goalColumnList, isView, statView.getGoalRuleIds(), tenantId, statView.getSource());
    Pair<String, String> groupingPair = buildGroupingSets(sqlSelectList, checkFieldDims, goalColumnList, tenantId);
    String groupBy = groupingPair.first;
    String dimHashCode = findDimHashCode(checkFieldDims, goalColumnList, checkCyCle, sqlSelectList, statView.getSource(), statView.getGoalDetailMatchLevel(), tenantId, groupingPair.second);
//    String havingGoalDimSQL = dimHashCode + " IN (SELECT hash_code FROM goal_dim)";
    String havingGoalDimSQL = havingGoalDimSQL(tenantId, dimHashCode, Constants.HASH_CODE, "goal_dim", goalColumnList);
    String firstDimFilter = buildFilterFirstGoalValueSQL(tenantId, "goal_value_obj_tmp", tableAggRule);
    String finalSql = String.format("WITH goal_value_obj_tmp AS (%s), goal_dim AS (%s) %s HAVING %s ", goalValueObj,
      goalDim, viewSQL, havingGoalDimSQL);
    //如果是预览sql,不能用with语句,统一换成子查询
    if (isPre) {
      StringBuilder preGoalDim = new StringBuilder("(");
      //目标查看明细,计算中可以直接走考核维度快照表
      if (statView.getSource() == 3 && statView.getVersion() > -1) {
        preGoalDim.append(buildSnapshotTmpSQL(tenantId, statView.getDatabase(), statView.getViewId(), statView.getVersion()));
      } else {
        String[] goalDimArr = goalDim.split("goal_value_obj_tmp");
        if (goalDimArr.length == 1) {
          preGoalDim.append(goalDimArr[0]).append("(").append(goalValueObj).append(") AS gvo_").append(0);
        } else {
          for (int i = 0; i < goalDimArr.length - 1; i++) {
            preGoalDim.append(goalDimArr[i]).append("(").append(goalValueObj).append(") AS gvo_").append(i);
          }
          preGoalDim.append(goalDimArr[goalDimArr.length - 1]);
        }
      }
      preGoalDim.append(") AS goal_dim");
      String preFirstDimFilter = firstDimFilter.replace("goal_value_obj_tmp", String.format("(%s) AS gov_x", goalValueObj));
      String preHavingGoalDimSQL = havingGoalDimSQL.replace("goal_dim", preGoalDim.toString());
      if (statView.getSource() == 3 && goalColumnList.size() > 1) {
        preHavingGoalDimSQL = String.format("arrayExists(x -> x IN (SELECT hash_code FROM %s), array(%s))", preGoalDim, dimHashCode);
      }
      String preFinalSql = String.format(" %s HAVING %s ", viewSQL, preHavingGoalDimSQL);
      return TemplateUtil.replace(preFinalSql,
        Map.of("queryGoalValue", "1=1", "groupBy", groupBy, "filterFirstDimSQL", preFirstDimFilter));
    }
    return TemplateUtil.replace(finalSql,
      Map.of("queryGoalValue", "1=1", "groupBy", groupBy, "filterFirstDimSQL", firstDimFilter));
  }

  public static String havingGoalDimSQL(String tenantId, String dimHashCode, String hashCodeAlias, String dimTable, List<String> goalColumnList) {
    return String.format("%s IN (SELECT %s FROM %s)", dimHashCode, hashCodeAlias, dimTable);
  }

  /**
   * 清洗goal_value_obj
   */
  public static String createGoalValueObj(String tenantId,
                                          String database,
                                          String viewId,
                                          List<String> goalColumnList,
                                          boolean isCH,
                                          String checkCyCle,
                                          int source,
                                          String checkLevelType,
                                          boolean isView,
                                          List<String> goalRuleIds,
                                          boolean isPre,
                                          List<String> partitions) {
    String goalValueObjTable = Constants.findGoalValueObjTable(checkCyCle);
    String whereMonthValue = source == 2 && "1".equals(checkLevelType)
      ? "AND check_level_field_api_name in ('main_department', 'user_id')" : "";
    String biSysFlag = isCH ? "AND bi_sys_flag = 1" : "";
    String dbTable = isCH ? String.format("%s.%s", database, goalValueObjTable) : goalValueObjTable;
    String filterRuleId = "";
    if (isView && goalRuleIds != null && goalRuleIds.size() > 1) {
      //查看明细需要支持多个目标规则
      List<String> goalRuleIdList = goalRuleIds.stream().map(ruleId -> ruleId.contains("|") ? ruleId : ruleId + "|nogoaldetail").toList();
      filterRuleId = String.format("CONCAT(goal_rule_id,'|',goal_rule_detail_id) in('%s')", String.join("','", goalRuleIdList));
    } else {
      filterRuleId = String.format("goal_rule_id='%s'", viewId.split("\\|")[0]);
      if (viewId.contains("|")) {
        filterRuleId = filterRuleId + String.format(" AND goal_rule_detail_id='%s'", viewId.split("\\|")[1]);
      }
    }
    String preWhere = "";
    if(GrayManager.isAllowByRule("use_ch_ods_partition", database) && isCH && !isView){
      preWhere= "PREWHERE bi_sys_ods_part in('s','c')";
    } else if (isCH && isPre && CollectionUtils.isNotEmpty(partitions)) {
      //查看明细预览sql,会根据统计图、目标的计算状态添加分区对齐数据
      preWhere = String.format("PREWHERE bi_sys_ods_part in ('%s')", Joiner.on("','").join(partitions));
    }
    return String.format("SELECT CONCAT(goal_rule_id,'|',goal_rule_detail_id) as rule_id, action_date, %s FROM %s %s " +
      "WHERE tenant_id = '%s' AND %s %s AND is_deleted = 0 %s AND check_dimension_value1 !=''",
      String.join(",", goalColumnList), dbTable, preWhere, tenantId, filterRuleId, biSysFlag, whereMonthValue);
  }

  /**
   * 根据goal_value_obj_tmp清洗成最终的goal_value_obj表
   */
  public static String createGoalDim(List<String> goalColumnList,
                                     boolean isView,
                                     List<String> goalRuleIds,
                                     String tenantId,
                                     int source) {
    //计算完成值新方案
    if (GrayManager.isAllowByRule("use_goal_dim_sql_v2", tenantId) &&
      (source == AggRuleType.MultiDimGoal.getRuleType() || source == AggRuleType.OldGoal.getRuleType())) {
      return createGoalDim2(goalColumnList, isView, goalRuleIds);
    }
    String goalColumnStr = String.join(",", goalColumnList);
    String selectColumn = String.format("cityHash64(action_date, %s) as hash_code, action_date, %s",
      goalColumnStr, goalColumnStr);
    //单维度
    if (goalColumnList.size() == 1) {
      return String.format("SELECT %s FROM goal_value_obj_tmp", selectColumn);
    }
    List<String> subFilterList = Lists.newArrayList();
    for (int i = 0; i < goalColumnList.size(); i++) {
      int num = 0;
      StringBuilder subFilter = new StringBuilder("(");
      List<String> subNotInList = Lists.newArrayList();
      if (isView && goalRuleIds != null && goalRuleIds.size() > 1) {
        subNotInList.add("rule_id");
      }
      subNotInList.add("action_date");
      for (String goalColumn : goalColumnList) {
        String isEqual;
        if (num <= i) {
          isEqual = "!=";
          subNotInList.add(goalColumn);
        } else {
          isEqual = "=";
        }
        subFilter.append(goalColumn).append(isEqual).append("'' AND ");
        num++;
      }
      if (i + 1 == goalColumnList.size()) {
        subFilterList.add(subFilter.substring(0, subFilter.length() - 4) + ")");
        continue;
      }
      String notEqualGoalColumn = goalColumnList.get(i+1);
      String subNotInSelect = String.format("cityHash64(%s)", String.join(",", subNotInList));
      subFilter.append(subNotInSelect)
               .append(" NOT IN (SELECT ")
               .append(subNotInSelect).append(" ")
               .append(" FROM goal_value_obj_tmp ")
               .append(" WHERE ").append(notEqualGoalColumn).append("!=''))");
      subFilterList.add(subFilter.toString());
    }
    return String.format("SELECT %s FROM goal_value_obj_tmp WHERE (%s)", selectColumn, String.join(" OR ", subFilterList));
  }

  /**
   * 根据goal_value_obj_tmp清洗成最终的goal_value_obj表
   */
  public static String createGoalDim2(List<String> goalColumnList, boolean isView, List<String> goalRuleIds) {
    String goalColumnStr = String.join(",", goalColumnList);
    //单维度
    if (goalColumnList.size() == 1) {
      return String.format("SELECT cityHash64(action_date, %s, 0) as hash_code, action_date, %s FROM goal_value_obj_tmp",
              goalColumnStr, goalColumnStr);
    }
    List<String> hashCodeList = Lists.newArrayList();
    List<String> subFilterList = Lists.newArrayList();
    for (int i = 0; i < goalColumnList.size(); i++) {
      int num = 0;
      StringBuilder subFilter = new StringBuilder("(");
      List<String> subNotInList = Lists.newArrayList();
      if (isView && goalRuleIds != null && goalRuleIds.size() > 1) {
        subNotInList.add("rule_id");
      }
      subNotInList.add("action_date");
      List<String> subEqualFilterList = Lists.newArrayList();
      for (String goalColumn : goalColumnList) {
        String isEqual;
        if (num <= i) {
          isEqual = "!=";
          subNotInList.add(goalColumn);
        } else {
          isEqual = "=";
        }
        subEqualFilterList.add(String.format("%s %s ''", goalColumn, isEqual));
//        subFilter.append(goalColumn).append(isEqual).append("'' AND ");
        num++;
      }
      String subEqualFilters = String.join(" AND ", subEqualFilterList);
      subFilter.append(subEqualFilters);
      hashCodeList.add(String.format("%s, cityHash64(action_date, %s, %d) ", subEqualFilters, String.join(",", goalColumnStr),
              (1 << (goalColumnList.size() - hashCodeList.size() - 1)) -1));
      if (i + 1 == goalColumnList.size()) {
//        subFilterList.add(subFilter.substring(0, subFilter.length() - 4) + ")");
        subFilterList.add(subFilter + ")");
        continue;
      }
      String notEqualGoalColumn = goalColumnList.get(i+1);
      String subNotInSelect = String.format("cityHash64(%s)", String.join(",", subNotInList));
      subFilter.append(" AND ")
              .append(subNotInSelect)
              .append(" NOT IN (SELECT ")
              .append(subNotInSelect).append(" ")
              .append(" FROM goal_value_obj_tmp ")
              .append(" WHERE ").append(notEqualGoalColumn).append("!=''))");
      subFilterList.add(subFilter.toString());
    }
    return String.format("SELECT multiIf(%s,cityHash64('')) AS hash_code, action_date, %s FROM goal_value_obj_tmp WHERE (%s)",
      String.join(",",hashCodeList), goalColumnStr, String.join(" OR ", subFilterList));
  }

  /**
   * 构造 group by grouping sets sql
   *
   * @param sqlSelectList
   * @return
   */
  public static Pair<String, String> buildGroupingSets(List<SqlSelect> sqlSelectList,
                                         Map<String, String> checkFieldDims,
                                         List<String> goalColumnList,
                                         String tenantId) {
    if (GrayManager.isAllowByRule("use_goal_dim_sql_v2", tenantId)) {
      return buildGroupingSets2(sqlSelectList, checkFieldDims, goalColumnList);
    }
    StringBuilder sbd = new StringBuilder(" GROUPING SETS (");
    List<String> groupByList = sqlSelectList.stream()
                                            .filter(SqlSelect::groupBy)
                                            .map(SqlSelect::alias)
                                            .collect(Collectors.toList());
    List<String> goalColumnListDesc = Lists.newArrayList(goalColumnList);
    goalColumnListDesc.sort(Collections.reverseOrder());
    for (String goalColumn : goalColumnListDesc) {
      sbd.append("(").append(String.join(",", groupByList)).append("),");
      for (Map.Entry<String, String> entry : checkFieldDims.entrySet()) {
        if (goalColumn.equals(entry.getKey())) {
          groupByList.remove(entry.getValue());
        }
      }
    }

    return Pair.build(sbd.substring(0, sbd.length() - 1) + ")", "");
  }

  /**
   * 构造 group by grouping sets sql
   *
   * @param sqlSelectList
   * @return Pair<grouping sets, grouping></>
   */
  public static Pair<String, String> buildGroupingSets2(List<SqlSelect> sqlSelectList,
                                        Map<String, String> checkFieldDims,
                                        List<String> goalColumnList) {
    StringBuilder sbd = new StringBuilder(" GROUPING SETS (");
    List<String> groupByList = sqlSelectList.stream()
            .filter(sqlSelect -> sqlSelect.groupBy() && !checkFieldDims.containsValue(sqlSelect.alias()) &&
                    !Constants.ACTION_DATE.equals(sqlSelect.alias()))
            .map(SqlSelect::alias)
            .collect(Collectors.toList());
    groupByList.add(Constants.ACTION_DATE);
    for (String goalColumn : goalColumnList) {
      groupByList.add(checkFieldDims.get(goalColumn));
    }
    //位置不能往下移动,removeLast会改变grouping
    String grouping = String.format("grouping(%s)", String.join(",", groupByList));
    for (int i = 0; i < goalColumnList.size(); i++) {
      sbd.append("(").append(String.join(",", groupByList)).append("),");
      groupByList.removeLast();
    }

    return Pair.build(sbd.substring(0, sbd.length() - 1) + ")", grouping);
  }

  /**
   * 构建having过滤设定目标值的条件
   * @param goalColumnList 是有序的考核维度12345
   * @param goalDetailMatchLevel 需要匹配到第几层维度
   * @return
   */
  public static String findDimHashCode(Map<String, String> checkFieldDims,
                                       List<String> goalColumnList,
                                       String checkCyCle,
                                       List<SqlSelect> sqlSelectList,
                                       int source,
                                       int goalDetailMatchLevel,
                                       String tenantId,
                                       String grouping) {
    List<String> dimList = Lists.newArrayList();
    if (source == 3) {
      Map<String, String> sqlSelectDefaultMap = sqlSelectList.stream()
              .filter(SqlSelect::groupBy)
              .collect(Collectors.toMap(SqlSelect::alias, SqlSelect::buildSelectSqlAliasWithDefaultWithoutAS));
      dimList.add(Constants.formatActionDate(checkCyCle, sqlSelectDefaultMap.get(Constants.ACTION_DATE)));
      for (String goalColumn : goalColumnList) {
        dimList.add(sqlSelectDefaultMap.get(checkFieldDims.get(goalColumn)));
      }
      if (goalDetailMatchLevel == 0) {goalDetailMatchLevel = 1;}
      //goalColumnList.size() > 2 的处理情况是为了模仿聚合计算的groupingset条件,即查看明细也需要把所有维度都穷举出来再去和考核维度表匹配过滤
      //dimList.size() - size == 1 保证维度处理后没有缺失,避免dimList.removeLast()报错
      int size = goalColumnList.size();
      if (size > 1 && dimList.size() - size == 1) {
        List<String> groupingSetsDimList = Lists.newArrayList();
        List<String> emptyList = Lists.newArrayList();
        for (int i = goalDetailMatchLevel; i <= size; i++) {
          List<String> newDimList = Lists.newArrayList();
          newDimList.addAll(dimList);
          newDimList.addAll(emptyList);
          groupingSetsDimList.add(String.format("cityHash64(%s)", String.join(",", newDimList)));
          dimList.removeLast();
          emptyList.add("''");
        }
        return String.join(",", groupingSetsDimList);
      }
      return String.format("cityHash64(%s)", String.join(",", dimList));
    } else {
      dimList.add(Constants.formatActionDate(checkCyCle, Constants.ACTION_DATE));
      for (String goalColumn : goalColumnList) {
        dimList.add(checkFieldDims.get(goalColumn));
      }
    }
    if (GrayManager.isAllowByRule("use_goal_dim_sql_v2", tenantId)) {
      return String.format("cityHash64(%s, %s)", String.join(",", dimList), grouping);
    }
    return String.format("cityHash64(%s)", String.join(",", dimList));
  }

  /**
   * 离线全量计算，所有设置了目标值的维度,
   * 或者增量计算当前batchNum之前的，所有目标维度
   *
   * @param tenantId 租户id
   * @return sql
   */
  public static String buildFilterFirstGoalValueSQL(String tenantId, String tableName, TopologyTableAggRule tableAggRule) {
    String firstField = checkFieldFirstDim(tableAggRule);
    if (StringUtils.isBlank(firstField)) {
      log.error("find firstField is null, tenantId:{}, goalRuleId:{}", tenantId, tableAggRule.getFieldId());
    }
    String columnName = firstField.split("\\.")[1];
    return String.format(" AND %s IN (SELECT distinct check_dimension_value1 FROM %s )", columnName, tableName);
  }

  /**
   * 获取目标值反查,只反查第一维度
   *
   * @return
   */
  public static String/*checkField*/ checkFieldFirstDim(TopologyTableAggRule tableAggRule) {
    //这里报key重复,那就是udf_obj_field重复了,大概率是这个企业停用了,一般只有刷库会有这个问题
    Map<String, String> dstNameMapper = tableAggRule.dimConfigStringList.stream()
                                                                        .collect(Collectors.toMap(dimConfig -> dimConfig.split(":")[2], dimConfig -> {
                                                                          String[] dimCol = dimConfig.split(":");
                                                                          return dimCol[0] + "." + dimCol[1];
                                                                        }));
    for (Map.Entry<String, String> entry : tableAggRule.checkFieldLocationMapper.entrySet()) {
      if (Constants.CHECK_DIMENSION_VALUE1.equals(entry.getValue())) {
        String dstName = tableAggRule.fieldLocationMap.get(entry.getKey());
        return dstNameMapper.get(dstName);
      }
    }
    return null;
  }

  /**
   * 新目标新计算,快照表版
   * @param goalChangeType
   * @return
   */
  public static List<String> buildNewCalculateGoalSQL(TopologyTable statView,
                                                      String insertFieldSQL,
                                                      String dimAfterSelectSQL,
                                                      List<SqlSelect> sqlSelectList,
                                                      Set<String> selectTableAliasSet,
                                                      List<DimConfig> dimConfigList,
                                                      ActionDateConfig actionDateConfig,
                                                      List<AggConfig> aggConfigList,
                                                      String constantWhere,
                                                      String conditionWhere,
                                                      GoalChangeType goalChangeType,
                                                      TopologyTableAggRule tableAggRule,
                                                      boolean isView,
                                                      Map<String,List<String>> tableKeys) {
    List<String> finalSqlList = Lists.newArrayList();
    String tenantId = statView.getTenantId();
    String database = statView.getDatabase();
    String viewId = statView.getViewId();
    int viewVersion = statView.getVersion();
    String checkCyCle = tableAggRule.getCheckCyCle();
    //获取维度映射关系
    Map<String, String> checkFieldDims = checkFieldDimsAll(tableAggRule);
    List<String> goalColumnList = Lists.newArrayList(checkFieldDims.keySet());
    goalColumnList.sort(null);
    //修改groupBY
    Pair<String, String> groupingPair = buildGroupingSets(sqlSelectList, checkFieldDims, goalColumnList, tenantId);
    String groupBy = groupingPair.first;
    String dimHashCode = findDimHashCode(checkFieldDims, goalColumnList, checkCyCle, sqlSelectList, statView.getSource(), 1,tenantId, groupingPair.second);
    switch (goalChangeType) {
      //目标值未变更,只计算业务增量数据
      case DATA_INC -> {
        String filterFirstDimSQL = buildFilterFirstGoalValueSQL(tenantId, "snapshot_tmp", tableAggRule);
        String snapshotTmp = buildSnapshotTmpSQL(tenantId, database, viewId, viewVersion);
        String havingGoalDimSQL = havingGoalDimSQL(tenantId, dimHashCode, Constants.HASH_CODE, "snapshot_tmp", goalColumnList);
        String incGoalSQL = String.format("INSERT INTO %s.agg_data(%s) WITH snapshot_tmp AS (%s) " +
            "SELECT %s FROM (%s and %s) AS agg_data",
          database, insertFieldSQL, snapshotTmp, insertFieldSQL, dimAfterSelectSQL, havingGoalDimSQL);
        finalSqlList.add(TemplateUtil.replace(incGoalSQL, Map.of("queryGoalValue", "1=1", "groupBy", groupBy,
          "filterFirstDimSQL", filterFirstDimSQL)));
      }
      //业务和目标值都变更了,都要计算
      case GOAL_ALL_DATA_INC -> {
        String allHashCodeTablesSQL = buildAllSnapshotHashCodeSQL(tenantId, database, viewId, viewVersion, goalColumnList,
          checkCyCle, statView.getSource(), tableAggRule.getCheckLevelType(), isView);
        //处理业务数据增量计算的sql,新老快照交集
        String incFilterFirstDimSQL = buildFilterFirstGoalValueSQL(tenantId, "inc_hash_code", tableAggRule);
        String incHashCodeTable = buildSnapshotHashCodeSQL(AggJoinType.INNER);
        String havingGoalDimSQL = havingGoalDimSQL(tenantId, dimHashCode, Constants.HASH_CODE, "inc_hash_code", goalColumnList);
        String incGoalSQL = String.format("INSERT INTO %s.agg_data(%s) WITH %s, inc_hash_code AS (%s) " +
            "SELECT %s FROM (%s and %s) AS agg_data", database, insertFieldSQL,
          allHashCodeTablesSQL, incHashCodeTable, insertFieldSQL, dimAfterSelectSQL, havingGoalDimSQL);
        finalSqlList.add(TemplateUtil.replace(incGoalSQL, Map.of("queryGoalValue", "1=1", "groupBy", groupBy,
          "filterFirstDimSQL", incFilterFirstDimSQL)));
        //处理新增目标的sql全量计算,新快照有旧快照没有的hash_code
        String allFrom = tableAggRule.buildFrom(statView, selectTableAliasSet, -1, true, null,false, tableKeys, true, false, null);
        List<SqlSelect> sqlSelectListAll = tableAggRule.buildSqlSelectList(statView, dimConfigList, actionDateConfig, aggConfigList, null, false, null, true, false,null);
        finalSqlList.add(newAddHashCodeSql(statView, insertFieldSQL, sqlSelectListAll, selectTableAliasSet, aggConfigList,
          allFrom, constantWhere, conditionWhere, allHashCodeTablesSQL, dimHashCode, groupBy, tableAggRule, goalColumnList));
        //处理删除目标的sql计算,新快照没有旧快照有的hash_code
        finalSqlList.add(delOldHashCodeSql(tenantId, database, viewId, viewVersion, allHashCodeTablesSQL, dimHashCode, aggConfigList, goalColumnList, checkFieldDims));
        //处理快照表更新的SQL
        finalSqlList.addAll(buildGoalValueObjSnapshotSQL(statView, tableAggRule, isView));
      }
      //只变更了目标值,只计算目标值变更数据
      case GOAL_ALL -> {
        String allFrom = tableAggRule.buildFrom(statView, selectTableAliasSet, -1, true, null,false, tableKeys, true, false, null);
        String allHashCodeTablesSQL = buildAllSnapshotHashCodeSQL(tenantId, database, viewId, viewVersion, goalColumnList,
          checkCyCle, statView.getSource(), tableAggRule.getCheckLevelType(), isView);
        //处理新增目标的sql全量计算,新快照有旧快照没有的hash_code
        List<SqlSelect> sqlSelectListAll = tableAggRule.buildSqlSelectList(statView, dimConfigList, actionDateConfig, aggConfigList, null, false, null, true, false,null);
        finalSqlList.add(newAddHashCodeSql(statView, insertFieldSQL, sqlSelectListAll, selectTableAliasSet, aggConfigList,
          allFrom, constantWhere, conditionWhere, allHashCodeTablesSQL, dimHashCode, groupBy, tableAggRule, goalColumnList));
        //处理删除目标的sql计算,新快照没有旧快照有的hash_code
        finalSqlList.add(delOldHashCodeSql(tenantId, database, viewId, viewVersion, allHashCodeTablesSQL, dimHashCode, aggConfigList, goalColumnList, checkFieldDims));
        //处理快照表更新的SQL
        finalSqlList.addAll(buildGoalValueObjSnapshotSQL(statView, tableAggRule, isView));
      }
    }
    return finalSqlList;
  }

  /**
   * 处理新增目标的sql全量计算,新快照有旧快照没有的hash_code
   * @return
   */
  private static String newAddHashCodeSql(TopologyTable statView,
                                          String insertFieldSQL,
                                          List<SqlSelect> sqlSelectList,
                                          Set<String> selectTableAliasSet,
                                          List<AggConfig> aggConfigList,
                                          String from,
                                          String constantWhere,
                                          String conditionWhere,
                                          String allHashCodeTablesSQL,
                                          String dimHashCode,
                                          String groupBy,
                                          TopologyTableAggRule tableAggRule,
                                          List<String> goalColumnList) {
    String afterFlagWhere = tableAggRule.buildAfterFlagWhere(statView, selectTableAliasSet);
    String finalAllSelectField = tableAggRule.buildSelect(statView, sqlSelectList, false, true, true, true,
      true, true, aggConfigList, true, true, false, null, false);
    String newFilterFirstDimSQL = GoalSqlUtils.buildFilterFirstGoalValueSQL(statView.getTenantId(), "new_hash_code", tableAggRule);
    String newHashCodeTable = buildSnapshotHashCodeSQL(AggJoinType.LEFT);
    String havingGoalDimSQL = havingGoalDimSQL(statView.getTenantId(), dimHashCode, "add_hash_code", "new_hash_code", goalColumnList);
    String newGoalSQL = String.format("INSERT INTO %s.agg_data(%s) WITH %s, new_hash_code AS (%s) " +
        "SELECT %s FROM %s WHERE %s %s %s GROUP BY ${groupBy} HAVING %s",
      statView.getDatabase(), insertFieldSQL, allHashCodeTablesSQL, newHashCodeTable, finalAllSelectField, from, constantWhere,
      conditionWhere, afterFlagWhere, havingGoalDimSQL);
    return TemplateUtil.replace(newGoalSQL, Map.of("queryGoalValue", "1=1", "groupBy", groupBy,
      "filterFirstDimSQL", newFilterFirstDimSQL));
  }

  /**
   * 处理删除目标的sql计算,新快照没有旧快照有的hash_code
   * @return
   */
  private static String delOldHashCodeSql(String tenantId,
                                          String database,
                                          String viewId,
                                          int viewVersion,
                                          String allHashCodeTablesSQL,
                                          String dimHashCode,
                                          List<AggConfig> aggConfigList,
                                          List<String> goalColumnList,
                                          Map<String, String> checkFieldDims) {
    String oldHashCodeTable = buildSnapshotHashCodeSQL(AggJoinType.RIGHT);
    StringBuilder insertSbd = new StringBuilder();
    StringBuilder selectSbd = new StringBuilder();
    for (AggConfig aggConfig : aggConfigList) {
      String dstColumnName = aggConfig.getDstColumnName();
      if (Constants.aggCountColumnRex.matcher(dstColumnName).matches()) {
        insertSbd.append(aggConfig.getDstColumnName()).append(",");
        selectSbd.append("0 AS ").append(dstColumnName).append(",");
      } else if (Constants.aggUniqColumnRex.matcher(dstColumnName).matches() ||
              Constants.aggSumColumnRex.matcher(dstColumnName).matches()) {
        insertSbd.append(dstColumnName).append("_merge").append(",");
        selectSbd.append("0 AS ").append(dstColumnName).append("_merge").append(",");
      }
    }
    String whereGoalDimSQL = havingGoalDimSQL(tenantId, dimHashCode, "del_hash_code", "old_hash_code", goalColumnList);
    if (GrayManager.isAllowByRule("use_goal_dim_sql_v2", tenantId)) {
      List<String> dimList = Lists.newArrayList();
      for (String goalColumn : goalColumnList) {
        dimList.add(checkFieldDims.get(goalColumn));
      }
      whereGoalDimSQL = buildDeleteHashCodeSQL(dimList);
    }
    return String.format("INSERT INTO %s.agg_data(tenant_id, view_id, view_version, hash_code, %s value_slot) " +
      "WITH %s, old_hash_code AS (%s) SELECT tenant_id, view_id, view_version, hash_code, %s value_slot " +
      "FROM %s.agg_data WHERE tenant_id='%s' AND view_id='%s' AND view_version=%s AND %s",
      database, insertSbd, allHashCodeTablesSQL, oldHashCodeTable, selectSbd, database, tenantId, viewId, viewVersion, whereGoalDimSQL);
  }

  private static String buildDeleteHashCodeSQL(List<String> dimList) {
    String dimStr = String.join(",", dimList);
    List<String> hashCodeList = Lists.newArrayList();
    for (int i = 0; i < dimList.size(); i++) {
      int num = 0;
      List<String> subEqualFilterList = Lists.newArrayList();
      for (String goalColumn : dimList) {
        String isEqual = num <= i ? "!=" : "=";
        subEqualFilterList.add(String.format("%s %s ''", goalColumn, isEqual));
        num++;
      }
      String subEqualFilters = String.join(" AND ", subEqualFilterList);
      hashCodeList.add(String.format("%s, cityHash64(action_date, %s, %d) ", subEqualFilters, String.join(",", dimStr),
              (1 << (dimList.size() - hashCodeList.size() - 1)) -1));
    }
    return String.format("multiIf(%s,cityHash64('')) IN (SELECT del_hash_code FROM old_hash_code)", String.join(",", hashCodeList));
  }

  /**
   * 正常增量维度
   * @param tenantId
   * @param database
   * @param ruleId
   * @param viewVersion
   * @return
   */
  private static String buildSnapshotTmpSQL(String tenantId,
                                            String database,
                                            String ruleId,
                                            int viewVersion) {
    String from = String.format("FROM %s.goal_value_obj_snapshot WHERE tenant_id='%s' AND rule_id='%s' AND view_version=%s " +
      "AND is_deleted=0", database, tenantId, ruleId, viewVersion);
    return String.format("SELECT hash_code,check_dimension_value1 %s AND batch_num IN (SELECT MAX(batch_num) %s)", from, from);
  }

  /**
   * goal_value_obj变更后,构建出变更前后两个版本的交集、新增集、删除集
   * @return
   */
  private static String buildAllSnapshotHashCodeSQL(String tenantId,
                                                    String database,
                                                    String viewId,
                                                    int viewVersion,
                                                    List<String> goalColumnList,
                                                    String checkCyCle,
                                                    int source,
                                                    String checkLevelType,
                                                    boolean isView) {
    String goalValueObj = createGoalValueObj(tenantId, database, viewId, goalColumnList, true, checkCyCle, source, checkLevelType, isView, null, false, null);
    String newSnapshot = createGoalDim(goalColumnList, isView, null, tenantId, source);
    String oldBatchNum = buildOldVersionBatchNumByGoalSql(tenantId, database, viewId, viewVersion);
    String oldSnapshot = String.format("SELECT tenant_id, rule_id, view_version, batch_num, hash_code, " +
        "check_dimension_value1 FROM %s.goal_value_obj_snapshot WHERE tenant_id='%s' AND rule_id='%s' " +
        "AND view_version=%s AND batch_num IN (SELECT del_bat_num FROM old_batch_num) AND is_deleted=0",
      database, tenantId, viewId, viewVersion);
    return String.format("goal_value_obj_tmp AS (%s), new_snapshot AS (%s), old_batch_num AS (%s), old_snapshot AS (%s)",
      goalValueObj, newSnapshot, oldBatchNum, oldSnapshot);
  }

  /**
   * goal_value_obj变更后,构建出变更前后两个版本的交集、新增集、删除集
   * @return
   */
  private static String buildSnapshotHashCodeSQL(AggJoinType joinType) {
    return switch (joinType) {
      case INNER -> "select new.hash_code, new.check_dimension_value1 from new_snapshot AS new INNER JOIN " +
        "old_snapshot AS old on new.hash_code=old.hash_code";
      case LEFT -> "select new.hash_code AS add_hash_code, new.check_dimension_value1 from new_snapshot AS new LEFT JOIN " +
        "old_snapshot AS old on new.hash_code=old.hash_code WHERE old.hash_code IS NULL OR old.hash_code = 0";
      case RIGHT -> "select old.hash_code AS del_hash_code, old.check_dimension_value1 from new_snapshot AS new RIGHT JOIN " +
        "old_snapshot AS old on new.hash_code=old.hash_code WHERE new.hash_code IS NULL OR new.hash_code = 0";
    };
  }

  /**
   * 获取目标值反查,获取槽位和维度的映射关系
   *
   * @return
   */
  private static Map<String/*check_dim*/, String/*dim槽位*/> checkFieldDimsAll(TopologyTableAggRule tableAggRule) {
    Map<String, String> inQueryFields = Maps.newHashMap();
    tableAggRule.checkFieldLocationMapper.forEach((fieldId, goalValueColumn) -> {
      inQueryFields.put(goalValueColumn, tableAggRule.fieldLocationMap.get(fieldId));
    });
    return inQueryFields;
  }

  /**
   * 查出 goal_value_obj_snapshot 最大version
   * @return
   */
  private static String buildOldVersionBatchNumByGoalSql(String tenantId,
                                                         String database,
                                                         String viewId,
                                                         int viewVersion) {
    return String.format("SELECT MAX(batch_num) AS del_bat_num FROM %s.goal_value_obj_snapshot " +
        "WHERE tenant_id='%s' AND rule_id='%s' AND view_version=%s AND batch_num<${batch_num} AND is_deleted=0",
      database, tenantId, viewId, viewVersion);
  }

  /**
   * 1.将goal_value_obj目标值维度写入快照
   * 2.删除goal_value_obj_snapshot的旧快照,保留近两个版本
   * 增量就是goal_value_obj的batch_num,即本次计算的批次号。全量写入的batch_num默认为0
   * goal_value_obj_snapshot的快照依据view_version和batch_num共同决定一个版本的快照
   * @return
   */
  public static List<String> buildGoalValueObjSnapshotSQL(TopologyTable statView, TopologyTableAggRule tableAggRule, boolean isView) {
    List<String> finalSQLList = Lists.newArrayList();
    String tenantId = statView.getTenantId();
    String database = statView.getDatabase();
    String viewId = statView.getViewId();
    int viewVersion = statView.getVersion();
    Map<String, String> checkFieldDims = checkFieldDimsAll(tableAggRule);
    List<String> goalColumnList = Lists.newArrayList(checkFieldDims.keySet());
    goalColumnList.sort(null);
    String goalValueObj = GoalSqlUtils.createGoalValueObj(tenantId, database, viewId, goalColumnList, true,
      tableAggRule.getCheckCyCle(), statView.getSource(), tableAggRule.getCheckLevelType(), isView, null, false, null);
    String goalDim = GoalSqlUtils.createGoalDim(goalColumnList, isView, null, tenantId, statView.getSource());
    String batchNum = statView.getStatus() == TopologyTableStatus.Prepared.getValue() ? "0" : "${batch_num}";
    String insertSql = String.format("INSERT INTO %s.goal_value_obj_snapshot(tenant_id,rule_id,view_version,batch_num,hash_code,check_dimension_value1,is_deleted) ", database);
    //插入新快照
    String selectAddSql = String.format("SELECT '%s' AS tenant_id, '%s' AS rule_id, %s AS view_version, %s AS batch_num, " +
      "hash_code, check_dimension_value1, 0 AS is_deleted FROM goal_dim", tenantId, viewId, viewVersion, batchNum);
    String addNewSnapshotSQL = String.format("%s WITH goal_value_obj_tmp AS (%s), goal_dim AS (%s) %s", insertSql, goalValueObj, goalDim, selectAddSql);
    finalSQLList.add(addNewSnapshotSQL);
    //插入新快照默认值，防止该规则的goal_value_obj清空后导致无法更新快照表
    String addDefaultSql = String.format("%s SELECT '%s' AS tenant_id, '%s' AS rule_id, %s AS view_version, %s AS batch_num, " +
            "cityHash64('') as hash_code, '' as check_dimension_value1, 0 AS is_deleted", insertSql, tenantId, viewId, viewVersion, batchNum);
    finalSQLList.add(addDefaultSql);
    //删除旧快照,保留近两个版本
    String deleteBatchNum = buildOldVersionBatchNumByGoalSql(tenantId, database, viewId, viewVersion);
    String selectDeleteSql = String.format("SELECT tenant_id, rule_id, view_version, batch_num, hash_code, " +
        "check_dimension_value1, 1 AS is_deleted FROM %s.goal_value_obj_snapshot WHERE tenant_id='%s' AND rule_id='%s' " +
        "AND ((view_version=%s AND batch_num < (SELECT del_bat_num FROM old_batch_num)) OR view_version<%s)",
      database, tenantId, viewId, viewVersion, viewVersion);
    String deleteOldSnapshotSQL = String.format("%s WITH old_batch_num AS (%s) %s", insertSql, deleteBatchNum, selectDeleteSql);
    finalSQLList.add(deleteOldSnapshotSQL);
    return finalSQLList;
  }
}
