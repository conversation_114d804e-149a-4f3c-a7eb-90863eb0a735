package com.fxiaoke.bi.warehouse.ods.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.Objects;
import lombok.Data;

import java.io.Serializable;

@Data
public class ChangedObjectAndFieldMessage {
  private HandleChangedObjectArg handleChangedObjectAndFieldArg;
  private String msgId;

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChangedObjectAndFieldMessage that = (ChangedObjectAndFieldMessage) o;
    return handleChangedObjectAndFieldArg.equals(that.handleChangedObjectAndFieldArg);
  }

  @Override
  public int hashCode() {
    return java.util.Objects.hash(handleChangedObjectAndFieldArg);
  }

  @Data
  public static class HandleChangedObjectArg implements Serializable {
    @JSONField(name = "objname")
    private String objName;
    @JSONField(name = "tenantid")
    private String tenantId;


    public HandleChangedObjectArg() {

    }

    @Override
    public String toString() {
      return JSON.toJSONString(this);
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      HandleChangedObjectArg that = (HandleChangedObjectArg) o;
      return Objects.equal(objName, that.objName) && Objects.equal(tenantId, that.tenantId);
    }

    @Override
    public int hashCode() {
      return Objects.hashCode(objName, tenantId);
    }
  }
}