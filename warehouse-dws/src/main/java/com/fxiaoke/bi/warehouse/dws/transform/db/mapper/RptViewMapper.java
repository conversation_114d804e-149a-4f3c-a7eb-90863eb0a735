package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.RptViewDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/04/06
 * @Description
 */
@Mapper
public interface RptViewMapper extends IBatchMapper<RptViewDO>, ITenant<RptViewMapper> {
  @Select("select view_id,view_name,ei,creator,create_time,updator,update_time,is_delete,rpt_type,data_source,time_zone " +
    " from rpt_view where (ei=#{ei} or ei=-1) and view_id=#{view_id}")
  RptViewDO queryRptView(@Param("ei") int ei, @Param("view_id") String viewId);
}
