package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtDetailDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BIMtDetailMapper extends IBatchMapper<BIMtDetailDO>, ITenant<BIMtDetailMapper> {
  @Select("select * from bi_mt_detail where tenant_id=#{tenant_id} and topology_describe_id=#{topology_describe_id}")
  List<BIMtDetailDO> findBIMtDetail(@Param("tenant_id") String tenantId, @Param("topology_describe_id") String topologyDescribeId);
}
