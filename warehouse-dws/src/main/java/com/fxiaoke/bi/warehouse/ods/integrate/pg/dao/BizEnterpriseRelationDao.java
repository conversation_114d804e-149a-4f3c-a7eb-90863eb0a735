package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao;

import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BizEnterpriseRelationDO;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper.BizEnterpriseRelationMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Date 20240412
 * <AUTHOR>
 * @Desc 互联企业查询接口
 */
@Repository
@Slf4j
public class BizEnterpriseRelationDao {

  @Resource
  private BizEnterpriseRelationMapper bizEnterpriseRelationMapper;

  /**
   * 根据上游企业查询下游企业
   */
  public List<String> queryBizEnterpriseRelation(String tenantId) {
    try {
      return bizEnterpriseRelationMapper.setTenantId(tenantId).queryBizEnterpriseRelation(tenantId);
    } catch (Exception e) {
      log.error("queryBizEnterPriseRelation error for tenantId:{}", tenantId);
      return Lists.newArrayList();
    }
  }

  /**
   * 根据上游企业查询下游企业objectId
   */
  public Map<String, String> queryBizEnterpriseRelationMap(String tenantId) {
    try {
      List<Map<String, String>> result = bizEnterpriseRelationMapper.setTenantId(tenantId)
                                                                    .queryBizEnterpriseRelationMap(tenantId);

      if (CollectionUtils.isNotEmpty(result)) {
        return result.stream().collect(Collectors.toMap(x -> x.get("ea"), y -> y.get("object_id"), (item1, item2) -> item2));
      }
    } catch (Exception e) {
      log.error("queryBizEnterPriseRelation error for tenantId:{}", tenantId);
      return Maps.newHashMap();
    }
    return Maps.newHashMap();
  }

  /**
   * 根据企业ea查询下游企业objectId
   */
  public Map<String, String> queryEaToObjectIdMap(String tenantId, List<String> eaList) {
    try {
      List<Map<String, String>> result = bizEnterpriseRelationMapper.setTenantId(tenantId).queryEaToObjectIdMap(tenantId, eaList);
      if (CollectionUtils.isEmpty(result)) {
        return Maps.newHashMap();
      }
      return result.stream().collect(Collectors.toMap(r -> r.get("ea"), r2 -> r2.get("object_id"), (item1, item2) -> item2));
    } catch (Exception e) {
      log.error("queryEaToObjectIdMap error for tenantId is {}, eaList:{}", tenantId, eaList);
      return Maps.newHashMap();
    }
  }

  /**
   * 根据下游企业的out_ei即id查询互联企业信息
   */
  public List<BizEnterpriseRelationDO> queryBizEnterpriseRelationByIds(String tenantId, List<String> ids) {
    try {
      return bizEnterpriseRelationMapper.setTenantId(tenantId).queryBizEnterpriseRelationByIds(tenantId, ids);
    } catch (Exception e) {
      log.error("queryBizEnterpriseRelationById error ids is {}, tenantId is {}", ids, tenantId);
      return Lists.newArrayList();
    }
  }

  /**
   * 根据下游企业tenant_id
   */
  public BizEnterpriseRelationDO queryBizEnterpriseRelationById(String id, String tenantId) {
    try {
      return bizEnterpriseRelationMapper.setTenantId(tenantId).queryBizEnterpriseRelationById(id, tenantId);
    } catch (Exception e) {
      log.error("queryBizEnterpriseRelationById error id is {}, tenantId is {}", id, tenantId);
      return new BizEnterpriseRelationDO();
    }
  }
}
