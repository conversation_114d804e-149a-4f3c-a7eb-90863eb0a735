package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface CustomStatisticAggFieldMapper extends ITenant<CustomStatisticAggFieldMapper> {
  Map<String, Object> findAggRuleByFieldId(@Param("tenantId") String tenantId,
                                           @Param("fieldId") String fieldId,
                                           @Param("themeApiName") String themeApiName);

  @Select("select sf.field_id, sf.field_location, sf.status, sf.agg_dim_type,sf.last_calculation_time, ar.* " +
    "from stat_schema ss " +
    "inner join stat_field sf  on ss.tenant_id = sf.tenant_id and ss.schema_id = sf.schema_id " +
    "inner join agg_rule ar on sf.tenant_id=ar.tenant_id and sf.field_id=ar.field_id " +
    "where ar.tenant_id=#{tenantId} " + "and ar.rule_id=#{ruleId} " + "and sf.status in (1,2,6) " +
    "and ss.status in (1,2,6) " + "and sf.agg_dim_type in ('agg','base_agg', 'goal_agg') " +
    "and sf.is_deleted=0 and ar.is_deleted=0 limit 1")
  Map<String,Object> getUsedRuleInfoByTenantIdAndRuleId(@Param("tenantId") String tenantId, @Param("ruleId") String ruleId);

  @Select("select\n" + "a.attname as column_name,\n" + "t.typname as type_name\n" + "from pg_namespace n\n" +
    "         inner join pg_class c ON n.oid = c.relnamespace\n" +
    "     inner join pg_attribute a on c.oid = a.attrelid and a.attnum >0\n" +
    "     inner join pg_type t on t.oid=a.atttypid\n" + "where n.nspname = #{ns}\n" + "  and c.relname = #{table}")
  List<Map> findTableColumnDefine(@Param("tenant_id") String tenantId,
                                  @Param("ns") String ns,
                                  @Param("table") String table);

  @Select("select sf.field_id,  sf.field_location, sf.status, sf.agg_dim_type, ar.* " + "from stat_schema ss " +
    "inner join  stat_field sf on ss.tenant_id = sf.tenant_id and ss.schema_id = sf.schema_id " +
    "inner join agg_rule ar on sf.tenant_id=ar.tenant_id and sf.field_id=ar.field_id " +
    "where ar.tenant_id=#{tenantId} " + "and sf.status in (1,2,6) " + "and ss.status in (1,2,6) " +
    "and sf.agg_dim_type in ('agg','base_agg', 'goal_agg') " + "and sf.is_deleted=0 and ar.is_deleted=0")
  List<Map> getUsedRuleInfoByTenantId(@Param("tenantId") String tenantId);

  @Select("select sf.field_id,  sf.field_location, sf.status, sf.agg_dim_type, ar.* " + "from stat_schema ss " +
    "inner join stat_field sf on ss.tenant_id = sf.tenant_id and ss.schema_id = sf.schema_id  " +
    "inner join agg_rule ar on sf.tenant_id=ar.tenant_id and sf.field_id=ar.field_id " +
    "where ar.tenant_id=#{tenantId} " + "and sf.status in (1,2,6) " + "and ss.status in (1,2,6) " +
    "and sf.agg_dim_type in ('agg','base_agg', 'goal_agg') " + "and ar.check_object_api_name=#{checkApiName} " +
    "and sf.is_deleted=0 and ar.is_deleted=0")
  List<Map> getUsedRuleInfoByTenantIdAndCheckApiName(@Param("tenantId") String tenantId,
                                                     @Param("checkApiName") String checkApiName);
  @Select("select check_level_type,check_level_field_api_name from goal_rule where tenant_id=#{tenantId} and id=#{goalRuleId}")
  Map<String, Object> findGoalRule(@Param("tenantId") String tenantId, @Param("goalRuleId") String goalRuleId);

  //排行榜指标在系统库中
  @Select("select sf.field_id,  sf.field_location, sf.status, sf.agg_dim_type, ar.* " + "from stat_schema ss " +
    "inner join  stat_field_gray sf on ss.tenant_id = sf.tenant_id and ss.schema_id = sf.schema_id " +
    "inner join agg_rule ar on sf.tenant_id=ar.tenant_id and sf.field_id=ar.field_id " + "where ar.tenant_id='-1' " +
    "and ar.theme_api_name='bill_board' " + "and sf.is_deleted=0 and ar.is_deleted=0 and ar.rule_id=#{ruleId} limit 1")
  Map<String,Object> getUsedBillboardRuleInfoByRuleId(@Param("ruleId") String ruleId);

  @Select("select * from stat_field where tenant_id=#{tenantId} and field_id=#{fieldId} " +
    "and object_describe_api_name='agg_downstream_data' and status in(1,2,6) " +
    "and agg_dim_type = 'downstream_agg' and is_deleted=0 limit 1")
  Map<String,Object> getUsedDownStreamRuleInfoByFieldId(@Param("tenantId") String tenantId, @Param("fieldId") String fieldId);

}
