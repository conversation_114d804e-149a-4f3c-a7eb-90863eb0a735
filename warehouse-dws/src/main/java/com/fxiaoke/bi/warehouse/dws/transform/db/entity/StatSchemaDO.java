package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * 自定义对象统计图主题表（stat_schema）
 *
 * <AUTHOR>
 * @date 2018.8.13
 */
@Table(name = "stat_schema")
@Data
public class StatSchemaDO {
  /**
   * 主题ID
   */
  @Column(name = "schema_id")
  private String schemaId;

  /**
   * 企业ID
   */
  @Column(name = "tenant_id")
  private String tenantId;

  /**
   * 主题名称
   */
  @Column(name = "schema_name")
  private String schemaName;

  /**
   * 对象名称
   */
  @Column(name = "schema_en_name")
  private String schemaEnName;

  /**
   * 主题对象展示名称
   */
  @Column(name = "schema_cn_name")
  private String schemaCnName;

  /**
   * 维度表名
   */
  @Column(name = "dim_obj_name")
  private String dimObjName;

  /**
   * 事实表名
   */
  @Column(name = "fact_obj_name")
  private String factObjName;

  /**
   * 维度表与事实表连接字段
   */
  @Column(name = "dim_fact_relation")
  private String dimFactRelation;


  @Column(name = "dim_explicit_cond")
  private String dimExplicitCond;

  /**
   * 维表隐含条件
   */
  @Column(name = "dim_hidden_cond")
  private String dimHiddenCond;

  /**
   * 默认筛选器类型
   */
  @Column(name = "option_type")
  private String optionType;

  /**
   * 默认筛选器连接表
   */
  @Column(name = "option_join_obj_name")
  private String optionJoinObjName;

  /**
   * 默认筛选器连接表连接字段
   */
  @Column(name = "option_join_obj_key")
  private String optionJoinObjKey;

  /**
   * 创建人
   */
  @Column(name = "creator")
  private String creator;

  /**
   * 创建时间
   */
  @Column(name = "create_time")
  private Date createTime;

  /**
   * 启用时间
   */
  @Column(name = "enable_time")
  private Date enableTime;

  /**
   * 描述
   */
  @Column(name = "description")
  private String description;

  /**
   * 主题状态：0:启用 1:初始化 2:停用
   */
  @Column(name = "status")
  private int status = -1;

  /**
   * 停用人
   */
  @Column(name = "unabled_by")
  private String unabledBy;

  /**
   * 停用时间
   */
  @Column(name = "unable_time")
  private Date unableTime;

  /**
   * 停用原因
   */
  @Column(name = "unable_reason")
  private String unableReason;

  /**
   * 最近修改人
   */
  @Column(name = "last_modifier")
  private String lastModifier;

  /**
   * 最近修改时间
   */
  @Column(name = "last_modified_time")
  private Date lastModifiedTime;

  /**
   * 是否删除
   */
  @Column(name = "is_deleted")
  private int isDeleted = -1;

  /**
   * 排序字段
   */
  @Column(name = "seq")
  private int seq;

  /**
   * 是否是预置主题
   */
  @Column(name = "is_pre")
  private int isPre;

  /**
   * 是否停用
   */
  @Column(name = "is_show")
  private int isShow = -1;
  /**
   * 数据源id
   */
  @Column(name="database_id")
  private String databaseId;

}
