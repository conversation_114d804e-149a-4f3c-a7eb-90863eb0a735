package com.fxiaoke.bi.warehouse.ods.etl;

import com.fxiaoke.bi.warehouse.common.arg.AggRequestContext;
import com.fxiaoke.bi.warehouse.ods.service.DBTransferService;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author:jief
 * @Date:2024/12/25
 */
public abstract class AbstractETLHandler<T> {
  @Setter
  @Getter
  private AbstractETLHandler<T> nextHandler;

  public abstract T doHandler(AggRequestContext aggRequestContext);
}
