package com.fxiaoke.bi.warehouse.dws.agg.service;

import com.fxiaoke.bi.warehouse.common.arg.AggRequestContext;
import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.bean.CHColumn;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.dws.agg.bean.AggCalResult;
import com.fxiaoke.bi.warehouse.dws.agg.bean.CHTable;
import com.fxiaoke.bi.warehouse.dws.model.DBUpdatedEvent;
import com.fxiaoke.bi.warehouse.dws.service.ClickHouseService;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.helper.Pair;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2024/8/8
 */
@Slf4j
public abstract class AbstractAggHandler<T> {
  @Resource
  private ClickHouseService clickHouseService;
  @Resource
  private CHMetadataService chMetadataService;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  @Setter
  @Getter
  private AbstractAggHandler<T> nextHandler;
  private static final ThreadFactory factory = new ThreadFactoryBuilder().setDaemon(true).setNameFormat("agg-cal-%d").build();
  public abstract T doHandler(AggRequestContext aggRequestContext);
  /**
   * 初始化before和after sql
   * @param event
   * @return
   */
  public AggCalResult prepare(DBUpdatedEvent event, StopWatch stopWatch) {
    AggCalResult aggCalResult = new AggCalResult();
    List<AggCalResult.CalPrepare> calPrepares = Lists.newArrayList();
    aggCalResult.setCalPrepares(calPrepares);
    Set<String> tableNames = event.changeTables();
    if (CollectionUtils.isEmpty(tableNames)) {
      log.warn("insertBefore return tableNames is empty chDB:{},pgDB:{},pgSchema:{}", event.getChJdbcUrl(), event.getPgJdbcUrl(), event.getPgSchema());
      return aggCalResult;
    }
    if (event.getBatchNums() == null || event.getBatchNums().length == 0) {
      log.warn("insertBefore batchNums is empty chDB:{},pgDB:{},pgSchema:{}", event.getChJdbcUrl(), event.getPgJdbcUrl(), event.getPgSchema());
      return aggCalResult;
    }
    stopWatch.start("prepare");
    try {
      Long[] batchNums = event.getBatchNums();
      Arrays.sort(batchNums);
      Map<String, List<CHColumn>> tableCHColumnsMap = Maps.newHashMap();
      if (event.getPgSchema().startsWith("sch_")) {
        List<List<String>> tablesPartitions = Lists.partition(Lists.newArrayList(tableNames), 10);
        tablesPartitions.forEach(tables -> {
          Map<String, List<CHColumn>> tableCHColumnMap1 = clickHouseService.batchLoadColumn(event.getChJdbcUrl(), CommonUtils.getDBName(event.getChJdbcUrl()), tables);
          if (!tableCHColumnMap1.isEmpty()) {
            tableCHColumnsMap.putAll(tableCHColumnMap1);
          }
        });
      }
      tableNames.forEach(table -> {
        List<CHColumn> columns = tableCHColumnsMap.getOrDefault(table, clickHouseService.loadTableCHColumn(event.getChJdbcUrl(), event.getPgSchema(), table));
        if (CollectionUtils.isEmpty(columns)) {
          log.warn("ch table columns is empty chDB:{},tableName:{}", event.getChJdbcUrl(), table);
          return;
        }
        List<String> columnNames = columns.stream().map(CHColumn::getName).toList();
        if (!columnNames.contains(WarehouseConfig.ODS_PARTITION_KEY)) {
          log.info("no deed before data chDB:{},table:{}", event.getChJdbcUrl(), table);
          AggCalResult.CalPrepare calPrepare = new AggCalResult.CalPrepare();
          calPrepare.setTableName(table);
          calPrepare.setInsertSQL(Pair.of("", ""));
          calPrepares.add(calPrepare);
          return;
        }
        List<String> sortedKeys = clickHouseService.queryCHTablePkColumns(event.getChJdbcUrl(), table);
        Optional<BIPair<String,String>> sysModifiedTimeColumn = chMetadataService.findSysModifiedTimeRange(table, columns);
        CHTable chTable = CHTable.builder().sortedKeys(sortedKeys).tableName(table).columnNames(columnNames).filterTimeColumn(sysModifiedTimeColumn.orElse(null)).build();
        AggCalResult.CalPrepare calPrepare = this.createInsertSqL(event, chTable);
        calPrepares.add(calPrepare);
      });
      return aggCalResult;
    } finally {
      stopWatch.stop("prepare");
    }
  }

  public String createSQL(String insertSQL,
                          String chDB,
                          String tableName,
                          String inEiSQL,
                          String inBatchIds,
                          String sortedKey,
                          String eiName,
                          List<String> columns,
                          Long[] batchNums,
                          boolean before,
                          String fromPartition,
                          String toPartition,
                          String pgDB,
                          BIPair<Long,Long> timeRange,
                          BIPair<String,String> filterTimeColumn) {
    if (before) {
      if (WarehouseConfig.skipBeforeTableSet.contains(tableName)) {
        return "";
      }
      String withSQL = String.format("WITH inc AS (SELECT %s FROM %s.%s PREWHERE bi_sys_ods_part='%s' WHERE %s IN(%s) AND bi_sys_batch_id in(%s))", sortedKey, chDB, tableName, fromPartition, eiName, inEiSQL, inBatchIds);
      String selectBeforeColumn = this.createSelectColumn(columns, batchNums, true, toPartition);
      String selectBeforeSQL = String.format(
        "SELECT %s FROM  %s.%s PREWHERE %s.bi_sys_ods_part='s' WHERE (%s) in inc AND " +
          "%s.bi_sys_flag=1 AND %s.bi_sys_batch_id < %d ", selectBeforeColumn, chDB, tableName, tableName, sortedKey, tableName, tableName, batchNums[batchNums.length - 1]);
      return String.format("%s\n%s\n%s\n%s", insertSQL, withSQL, selectBeforeSQL, WarehouseConfig.finalsetting);
    }
    String selectAfterColumn = this.createSelectColumn(columns, batchNums, false, toPartition);
    String whareString = String.format("%s.bi_sys_batch_id in(%s)",tableName, inBatchIds);
    if(!WarehouseConfig.allowBiPgCHTables.contains(tableName) && timeRange != null && filterTimeColumn != null){
      String filterTime = filterTimeColumn.getFirst();
      String typeName = filterTimeColumn.getSecond();
      String defaultIncFilterSQL = String.format("%s.bi_sys_batch_id=0 AND %s.%s >= %d AND %s.%s <= %d",tableName,tableName,filterTime,timeRange.getFirst(),tableName,filterTime,timeRange.getSecond());
      if(typeName.contains("string")){
        defaultIncFilterSQL = String.format("%s.bi_sys_batch_id=0 AND %s.%s >= '%s' AND %s.%s <= '%s'",tableName,tableName,filterTime,timeRange.getFirst(),tableName,filterTime,timeRange.getSecond());
      }
      if(GrayManager.isAllowByRule("skip_sync_bi_pg_to_ch", pgDB)){
        whareString = defaultIncFilterSQL;
      }else{
        whareString = String.format("(%s.bi_sys_batch_id in(%s) OR (%s))",tableName,inBatchIds,defaultIncFilterSQL);
      }
      if(Objects.equals(fromPartition,"c") && Objects.equals(toPartition,"s")){
        whareString = String.format("%s.bi_sys_batch_id = %d ",tableName, batchNums[batchNums.length - 1]);
      }
    }
    String selectAfterSQL = String.format("SELECT %s FROM %s.%s PREWHERE %s.bi_sys_ods_part='%s' WHERE %s.%s IN(%s) AND %s ", selectAfterColumn, chDB, tableName, tableName, fromPartition, tableName, eiName, inEiSQL, whareString);
    return String.format("%s\n%s\n%s", insertSQL, selectAfterSQL, WarehouseConfig.finalsetting);
  }
  /**
   * 生成插入before的sql
   * @param event
   * @param chTable
   * @return
   */
  public AggCalResult.CalPrepare createInsertSqL(DBUpdatedEvent event, CHTable chTable) {
    AggCalResult.CalPrepare calPrepare = new AggCalResult.CalPrepare();
    Set<String> lastSyncEis = event.findModifiedTenantIds();
    if (CollectionUtils.isEmpty(lastSyncEis)) {
      throw new RuntimeException(String.format("createInsertBeforeSqL lastSyncEis is empty chDB:%s,pgDB:%s",event.getChJdbcUrl(), event.getPgJdbcUrl()));
    }
    Long[] sortedBatchNums = event.sortedBatchNums();
    String chDB = CommonUtils.getDBName(event.getChJdbcUrl());
    String pgDB = CommonUtils.getDBName(event.getPgJdbcUrl());
    String tableName = chTable.getTableName();
    String inEiSQL = JoinHelper.joinSkipNullOrBlank(",", "'", lastSyncEis);
    String inBatchIds = Lists.newArrayList(sortedBatchNums).stream().map(String::valueOf).collect(Collectors.joining(","));
    String sortedKey = chTable.getSortedKeys().stream().map(key->String.format("%s.%s",tableName,key.trim())).collect(Collectors.joining(","));
    String eiName = "tenant_id";
    if (WarehouseConfig.eiTables.contains(tableName)) {
      eiName = "ei";
    }
    calPrepare.setTableName(tableName);
    String insertColumn = String.join(",", chTable.getColumnNames());
    String insertSQL = String.format("INSERT INTO %s.%s (%s) ", chDB, chTable.getTableName(), insertColumn);
    Map<String,BIPair<Long,Long>> incSysModifiedTimeRangeMap = event.getIncSysModifiedTimeRangeMap();
    BIPair<Long,Long> timeRange = incSysModifiedTimeRangeMap==null?null:incSysModifiedTimeRangeMap.get(tableName);
    BIPair<String,String> sysModifiedTimeColumn = chTable.getFilterTimeColumn();
    if (event.getAllowCalPartition() == WarehouseConfig.OPEN_CAL_PARTITION) {
      String inc2CalAfter = this.createSQL(insertSQL, chDB, tableName, inEiSQL, inBatchIds, sortedKey, eiName, chTable.getColumnNames(), sortedBatchNums, false, "i", "c",pgDB,timeRange,sysModifiedTimeColumn);
      log.info("inc2CalAfter chDb:{}, Sql:{}, sortedBatchNums:{}, timeRange:{}", chDB, inc2CalAfter, sortedBatchNums, timeRange);
      String cal2StockBefore = this.createSQL(insertSQL, chDB, tableName, inEiSQL, inBatchIds, sortedKey, eiName, chTable.getColumnNames(), sortedBatchNums, true, "c", "s",pgDB,timeRange,sysModifiedTimeColumn);
      String cal2StockAfter = this.createSQL(insertSQL, chDB, tableName, inEiSQL, inBatchIds, sortedKey, eiName, chTable.getColumnNames(), sortedBatchNums, false, "c", "s",pgDB,timeRange,sysModifiedTimeColumn);
      calPrepare.setInc2CalAfter(inc2CalAfter);
      calPrepare.setCal2StockBefore(cal2StockBefore);
      calPrepare.setCal2StockAfter(cal2StockAfter);
    } else {
      String selectBeforeColumn = this.createSelectColumn(chTable.getColumnNames(), sortedBatchNums, true,"s");
      String selectAfterColumn = this.createSelectColumn(chTable.getColumnNames(), sortedBatchNums, false,"s");
      String withSQL = String.format("WITH inc AS (SELECT %s FROM %s.%s PREWHERE bi_sys_ods_part='i' WHERE %s IN(%s) " +
        "AND bi_sys_batch_id in(%s))", sortedKey, chDB, chTable.getTableName(), eiName, inEiSQL, inBatchIds);
      String selectBeforeSQL = String.format("SELECT %s FROM  %s.%s PREWHERE %s.bi_sys_ods_part='s' WHERE (%s) in inc AND " +
        "%s.bi_sys_flag=1 AND %s.bi_sys_batch_id < %d ", selectBeforeColumn, chDB, tableName,tableName, sortedKey,tableName,tableName,sortedBatchNums[sortedBatchNums.length-1]);
      String selectAfterSQL = String.format("SELECT %s FROM %s.%s PREWHERE %s.bi_sys_ods_part='i' WHERE %s.%s IN(%s) AND " +
        "%s.bi_sys_batch_id in(%s) ", selectAfterColumn,chDB, tableName,tableName, tableName,eiName,inEiSQL,tableName,inBatchIds);
      String before = String.format("%s\n%s\n%s\n%s", insertSQL, withSQL, selectBeforeSQL, WarehouseConfig.finalsetting);
      if (WarehouseConfig.skipBeforeTableSet.contains(tableName)) {
        before = "";
      }
      String after = String.format("%s\n%s\n%s", insertSQL, selectAfterSQL, WarehouseConfig.finalsetting);
      calPrepare.setInsertSQL(Pair.of(before, after));
    }
    return calPrepare;
  }

  /**
   * 生成 select 列
   * @param columns
   * @param batchNums
   * @param before
   * @return
   */
  private String createSelectColumn(List<String> columns, Long[] batchNums, boolean before, String partitionName) {
    return columns.stream().map(column -> {
      if (!column.startsWith("bi_sys_")) {
        return column;
      }
      return switch (column) {
        case "bi_sys_flag" -> String.format("%s AS bi_sys_flag", before ? "0" : "1");
        case "bi_sys_batch_id" -> String.format("%d AS bi_sys_batch_id", batchNums[batchNums.length - 1]);
        case "bi_sys_ods_part" -> String.format("'%s' AS bi_sys_ods_part", partitionName);
        case "bi_sys_is_deleted" -> "0 AS bi_sys_is_deleted";
        case "bi_sys_version" -> before ? "now() AS bi_sys_version" : column;
        default -> column;
      };
    }).collect(Collectors.joining(","));
  }

  /**
   *
   * @param event
   * @param calPrepares
   * @param before
   */
  public void doInsertSQLs(DBUpdatedEvent event, List<AggCalResult.CalPrepare> calPrepares,int insertStatus, boolean before) {
    if (CollectionUtils.isEmpty(calPrepares)) {
      log.warn("calPrepares is empty dhDB:{},pgDB:{},batchNum:{}", event.getChJdbcUrl(), event.getPgJdbcUrl(), event.getBatchNum());
      return;
    }
    List<List<AggCalResult.CalPrepare>> calPrepareParts = Lists.partition(calPrepares, (int) Math.ceil(calPrepares.size() / WarehouseConfig.insertThreadRate));
    final ExecutorService executor = Executors.newFixedThreadPool(calPrepareParts.size(), factory);
    try {
      CompletableFuture.allOf(calPrepareParts.stream().map(key -> CompletableFuture.runAsync(() -> {
        key.forEach(calPrepare -> {
          String sql = calPrepare.findSQLByStatus(insertStatus);
          if (StringUtils.isBlank(sql)) {
            log.warn("execute insert sql is empty table:{},dhDB:{},pgDB:{},batchNum:{}", calPrepare.getTableName(), event.getChJdbcUrl(), event.getPgJdbcUrl(),event.getBatchNum());
            return;
          }
          if (!before) {
            String jedisK = String.format("ods:%s:%s:all", event.getId(), calPrepare.getTableName());
            try (JedisLock jedisLock = new JedisLock(jedisCmd, jedisK, 30 * 60 * 1000)) {
              boolean lock = jedisLock.tryLock();
              if (lock) {
                this.retryRunInsertSQL(event, sql, calPrepare);
              }else{
                log.warn("execute insert sql tryLock fail table:{},dhDB:{},pgDB:{},batchNum:{}", calPrepare.getTableName(), event.getChJdbcUrl(), event.getPgJdbcUrl(),event.getBatchNum());
              }
            } catch (Exception e) {
              throw new RuntimeException(String.format("doInsertSQLs tryLock error chDB:%s,pgDB:%s,table:%s,before:%s,batchNum:%d", event.getChJdbcUrl(), event.getPgJdbcUrl(), calPrepare.getTableName(), false,event.getBatchNum()), e);
            }
          }else{
            this.retryRunInsertSQL(event, sql, calPrepare);
          }
        });
        log.info("doInsertSQLs finish this part chDB:{},pgDB:{},before:{},batchNum:{}", event.getChJdbcUrl(), event.getPgJdbcUrl(), before, event.getBatchNum());
      }, executor)).toArray(CompletableFuture[]::new)).join();
    } finally {
      executor.shutdown();
    }
  }

  private void retryRunInsertSQL(DBUpdatedEvent event, String sql, AggCalResult.CalPrepare calPrepare) {
    for (int rt = 0; rt < WarehouseConfig.partitionCopyRetryTimes; rt++) {
      try {
        clickHouseService.executeSQLWithJdbcUrl(event.getChJdbcUrl(), sql, 1200000);
        break;
      } catch (Exception e) {
        if (rt < WarehouseConfig.partitionCopyRetryTimes - 1) {
          log.warn("execute insert sql error dhDB:{},pgDB:{},table:{},execute:{} times", event.getChJdbcUrl(), event.getPgJdbcUrl(),calPrepare.getTableName(), rt);
          Uninterruptibles.sleepUninterruptibly(Utils.calculateWaitTime(rt, 10, 25), TimeUnit.SECONDS);
        } else {
          throw new RuntimeException(String.format("execute insert sql error after 3 times dhDB:%s,pgDB:%s,table:%s", event.getChJdbcUrl(), event.getPgJdbcUrl(), calPrepare.getTableName()), e);
        }
      }
    }
  }

}
