package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.fxiaoke.bi.warehouse.common.db.er.AggRuleType;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyTableMapper;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTable;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.service.ClickHouseService;
import com.fxiaoke.bi.warehouse.dws.service.TopologyTableService;
import com.fxiaoke.bi.warehouse.dws.strategy.AggRemainStrategyFactory;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.AggDiffDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.AggRuleMapper;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;

/**
 * @Author: zhaomh
 * @Description:
 * @Date: Created in 2023/10/17
 * @Modified By:
 */

@Slf4j
@Repository
public class CompareDao {

    @Autowired
    private AggRuleMapper aggRuleMapper;
    @Autowired
    private TopologyTableMapper topologyTableMapper;
    @Resource
    private ClickHouseService clickHouseService;
    @Resource
    private TopologyTableService topologyTableService;

    /**
     * 获取当前最大版本号，如果没有获取到则证明不走统计图计算
     * @param tenantId
     * @param viewId
     * @return
     */
    public Integer findMaxVersion(String tenantId, String viewId) {
        return topologyTableMapper.setTenantId(tenantId).queryMaxVersion(tenantId, viewId);
    }

    /**
     * 获取在topology中正常计算的图
     * @param tenantId
     * @param sourceIds
     * @return
     */
    public List<String> querySourceIds(String tenantId, List<String> sourceIds) {
        return topologyTableMapper.setTenantId(tenantId).querySourceIds(tenantId, sourceIds.toArray(new String[0]));
    }

    /**
     * 执行查询
     * @param tenantId
     * @param diffSQL
     * @return
     */
    public List<AggDiffDO> executeQuerySQL(String tenantId, String diffSQL) {
        return clickHouseService.executeQuerySQL(tenantId, diffSQL, res -> {
            AggDiffDO aggDiffDO = new AggDiffDO();
            try {
                aggDiffDO.setTenantId(res.getString("tenant_id"));
                aggDiffDO.setViewId(res.getString("view_id"));
                aggDiffDO.setFieldId(res.getString("field_id"));
                aggDiffDO.setHashCode(String.valueOf(res.getBigDecimal("hash_code")));
                aggDiffDO.setObjectId(res.getString("object_id"));
                aggDiffDO.setActionDate(res.getString("action_date"));
                aggDiffDO.setCommonDim(res.getString("common_dim"));
                aggDiffDO.setFieldLocation(res.getString("field_location"));
                aggDiffDO.setAggQuota(res.getString("agg_quota"));
                aggDiffDO.setCheckQuota(res.getString("check_quota"));
                aggDiffDO.setDiffType(res.getString("diff_type"));
            } catch (SQLException e) {
                log.error("diffSQL error, tenantId:{}, diffSQL:{}", tenantId, diffSQL, e);
            }
            return aggDiffDO;
        });
    }

    /**
     * 构建对比SQL
     * @param statView
     * @param actionDateStart
     * @param actionDateEnd
     * @return
     */
    public String buildDiffSQL(TopologyTable statView,
                               String fieldId,
                               String actionDateStart,
                               String actionDateEnd,
                               int top,
                               boolean standalone) {
        if (statView.getSource() == 0) {
            //有些主题，计算结果只需要保存近N天的数据
            String minActionDate = AggRemainStrategyFactory.minActionDate(statView.getTenantId(), statView.getApiName(), statView.getTimezone());
            if (minActionDate != null && !Constants.ACTION_DATE_EMPTY.equals(minActionDate) && actionDateStart.compareTo(minActionDate) < 0) {
                actionDateStart = minActionDate;
            }
        }
        String settings = Constants.ONLY_JOIN_USE_NULLS_1;
        /*if(GrayManager.isAllowByRule("allow_deduplicated_by_arg_max", statView.getTenantId())){
            settings = Constants.JOIN_USE_NULLS_NOT_FINAL;
        }*/
        //获取指标列
        List<String> aggColumnList = this.findAggColumn(statView);
        //agg_dataSQL
        String aggDataSQL = this.buildAggDataSQL(statView, aggColumnList, actionDateStart, actionDateEnd);
        //离线计算SQL
        String checkSQL = this.buildCheckSQL(statView, actionDateStart, actionDateEnd, standalone);

        StringBuilder diffSQL = new StringBuilder();
        //查询维度列
        String dimSelectSQL = this.buildDimSQL(statView, fieldId);
        //查询指标列
        String aggSelectSQL = this.buildAggSQL(aggColumnList);
        //对比指标过滤条件
        String whereSQL = this.buildWhereSQL(aggColumnList, statView.getTenantId());
        //构建对比SQL
        diffSQL.append("with t1 as (").append(aggDataSQL).append("),")
                .append(" t2 as (").append(checkSQL).append(")")
                .append("select ")
                .append(dimSelectSQL)
                .append(aggSelectSQL)
                .append(" case when t1.hash_code is null then 'dim_less' when t2.hash_code is null then 'dim_more' else 'agg' end as diff_type")
                .append(" from t1 full join t2 ")
                .append(" on t1.hash_code = t2.hash_code")
                .append(whereSQL)
                .append(" limit ").append(top)
                .append(settings);

        return diffSQL.toString();
    }

    /**
     * 构建from table，将多个指标列union all
     * @param aggColumnList
     * @return
     */
    private String buildFromSQL(List<String> aggColumnList, String dimSelectSQL) {
        StringBuilder from = new StringBuilder(" from (");
        from.append(dimSelectSQL);
        for (String column : aggColumnList) {
            from.append(",'").append(column).append("' as field_location,");
            from.append(" t1.").append(column).append(" as agg_quota,");
            from.append(" t2.").append(column).append(" as check_quota ");
            from.append(" from t1 full join t2 on t1.hash_code = t2.hash_code ");
            from.append(" where t1.hash_code is null or t2.hash_code is null or t1.")
                    .append(column).append(" <> t2.").append(column);
            from.append(" order by action_date asc limit 200");
            from.append(" union all ");
        }
        return from.substring(0, from.length() - 11) + ") as diff_data ";
    }

    /**
     * 离线计算SQL
     * @param statView
     * @param actionDateStart
     * @param actionDateEnd
     * @return
     */
    public String buildCheckSQL(TopologyTable statView, String actionDateStart, String actionDateEnd, boolean standalone) {
        String tenantId = statView.getTenantId();
        if (GrayManager.isAllowByRule("biDataSyncPolicyGray", tenantId)) {
            topologyTableService.addDownStreamRuleWhereSql(statView);
        }
        statView.getStatRuleList().forEach(statRule -> {
            Utils.checkSubActionDate(statView.getTimezone(), statRule, actionDateStart, actionDateEnd);
            Utils.checkExtendObjDataExist(tenantId, standalone, statRule); //检测是否需要加上预设对象和扩展对象批次不一致的条件过滤
        });
        StringBuilder sb = new StringBuilder("select ");
        String aggSQL = statView.toViewSQL(-1,-1, null, topologyTableService.getTableKeys(statView));
        if (!statView.getCommonDimList().contains("object_id")){
            sb.append("'' as object_id," );
        }
        sb.append(" check_data.* from (").append(aggSQL).append(") as check_data ");
        return sb.toString();
    }

    /**
     * 获取指标列
     * @param statView
     * @return
     */
    public List<String> findAggColumn(TopologyTable statView) {
        List<String> aggColumnList = Lists.newArrayList();
        //计数去重列,不用对比去重列
//        if (statView.needAggUniqTagColumn()) {
//            aggColumnList.add(Constants.AGG_UNIQ_TAG_1);
//        }
        //指标
        statView.getStatRuleList().forEach(statRule -> {
            statRule.getAggConfigStringList().forEach(aggConfig -> {
                aggColumnList.add(aggConfig.split(":")[2]);
            });
        });
        return aggColumnList;
    }

    /**
     * 对比指标过滤条件
     * @param aggColumnList
     * @return
     */
    public String buildWhereSQL(List<String> aggColumnList, String tenantId) {
        StringBuilder sb = new StringBuilder(" where ");
        for (String quota : aggColumnList) {
            if (Constants.aggUniqColumnRex.matcher(quota).matches()) {
                sb.append(" ifNull(finalizeAggregation(t1.").append(quota).append("),0) != ifNull(finalizeAggregation(t2.").append(quota).append("),0) or ");
//            } else if (quota.startsWith(Constants.AGG_UNIQ_TAG)) {
//                sb.append(" ifNull(t1.").append(quota).append(",'') != ifNull(t2.").append(quota).append(",'') or ");
            } else {
                sb.append(" ifNull(t1.").append(quota).append(",0) != ifNull(t2.").append(quota).append(",0) or ");
            }
            if (GrayManager.isAllowByRule("sum_zero_to_null", tenantId) &&
              Constants.aggSumColumnRex.matcher(quota).matches()){
                sb.append(" t1.hash_code is null or t2.hash_code is null").append(" or ");
            }
        }

        return sb.substring(0, sb.length() - 3);
    }

    /**
     * 指标列
     * @param aggColumnList 指标列
     * @return 指标列SQL
     */
    public String buildAggSQL(List<String> aggColumnList) {
        StringBuilder sb = new StringBuilder();
        List<String> aggQuotaList = Lists.newArrayList();
        List<String> checkQuotaList = Lists.newArrayList();
        for (String quota : aggColumnList) {
            if (Constants.aggUniqColumnRex.matcher(quota).matches()) {
                aggQuotaList.add("toString(finalizeAggregation(t1." + quota + "))");
                checkQuotaList.add("toString(finalizeAggregation(t2." + quota + "))");
            } else {
                aggQuotaList.add("toString(t1." + quota + ")");
                checkQuotaList.add("toString(t2." + quota + ")");
            }
        }
        sb.append("'").append(Joiner.on("|").join(aggColumnList)).append("' as field_location, ");//槽位
        sb.append(" concat('',").append(Joiner.on(",'|',").join(aggQuotaList)).append(") as agg_quota, ");//agg
        sb.append(" concat('',").append(Joiner.on(",'|',").join(checkQuotaList)).append(") as check_quota, ");//check
        return sb.toString();
    }

    /**
     * 维度列
     * @param statView
     * @return
     */
    public String buildDimSQL(TopologyTable statView, String fieldId) {
        StringBuilder dimSQL = new StringBuilder();
        dimSQL.append("'").append(statView.getTenantId()).append("' as tenant_id,");
        dimSQL.append("'").append(statView.getViewId()).append("' as view_id,");
        dimSQL.append("'").append(fieldId == null ? "" : fieldId).append("' as field_id,");
        dimSQL.append("coalesce(t1.hash_code,t2.hash_code) as hash_code,");
        dimSQL.append("coalesce(t1.object_id,t2.object_id) as object_id,");
        dimSQL.append("coalesce(t1.action_date,t2.action_date) as action_date,");
        dimSQL.append("concat(");
        for (String commonDim : statView.getCommonDimList()) {
            if ("object_id".equals(commonDim) || "action_date".equals(commonDim)) {
                continue;
            }
            dimSQL.append("'").append(commonDim).append(":',")
                    .append("toString(coalesce(t1.").append(commonDim);
            if (commonDim.contains("string")) { //兼容boolean类型存string格式
                dimSQL.append(",toString(t2.").append(commonDim).append("))),',',");
            } else {
                dimSQL.append(",t2.").append(commonDim).append(")),',',");
            }
        }
        return dimSQL.substring(0, dimSQL.length() - 5) + ") as common_dim, ";
    }

    /**
     * agg_data 查询SQL
     * @param statView
     * @return
     */
    public String buildAggDataSQL(TopologyTable statView, List<String> aggColumn, String actionDateStart, String actionDateEnd) {
        StringBuilder sb = new StringBuilder();
        StringBuilder sb2 = new StringBuilder();
        List<String> commonDimList = statView.getCommonDimList();
        sb.append("select agg_data_tmp.* from (");
        //维度
        sb.append("select hash_code, argMax(action_date,timestamp) as action_date");
        if (!commonDimList.contains("object_id")) {
            sb.append(", argMax(object_id,timestamp) as object_id");
        }
        for (String dimColumn : commonDimList) {
            sb.append(", argMax(").append(dimColumn).append(",timestamp) as ").append(dimColumn);
        }
        //指标
        for (String quota : aggColumn) {
            if (Constants.aggUniqColumnRex.matcher(quota).matches()) {
                sb.append(", argMaxIf(")
                        .append(quota).append(",timestamp,")
                        .append(quota).append("_merge) as ")
                        .append(quota);
//                sb.append(", argMax(").append(quota).append("_merge, timestamp) as n_").append(quota).append("_merge");
//                sb2.append("isZeroOrNull(agg_data_tmp.n_").append(quota).append("_merge) AND ");
            } else if (Constants.aggSumColumnRex.matcher(quota).matches()){
                sb.append(", any_respect_nullsArgMaxIf(").append(quota).append(", timestamp, isNotNull(").append(quota).append("_merge)) as ").append(quota);
                sb.append(", argMax(").append(quota).append("_merge, timestamp) as n_").append(quota).append("_merge");
                sb2.append("isZeroOrNull(n_").append(quota).append("_merge) AND ");
//            } else if (GrayManager.isAllowByRule("count_n_merge_eis", statView.getTenantId()) &&
//              Constants.aggCountColumnRex.matcher(quota).matches()) {
//                sb.append(", argMaxIf(")
//                  .append(quota).append(",timestamp,")
//                  .append(quota).append("_merge) as ")
//                  .append(quota);
//                sb.append(", argMax(").append(quota).append("_merge, timestamp) as n_").append(quota).append("_merge");
//                sb2.append("isZeroOrNull(agg_data_tmp.n_").append(quota).append("_merge) AND ");
            } else {
                sb.append(", argMax(").append(quota).append(", timestamp) as ").append(quota);
            }
        }

        sb.append(" from ").append(statView.getDatabase()).append(".agg_data ");
        sb.append(" where tenant_id = '").append(statView.getTenantId()).append("' ");
        if ((GrayManager.isAllowByRule("combine_agg_ei", statView.getTenantId()) &&
                AggRuleType.Agg.getRuleType() == statView.getSource())) {
            sb.append(" and view_id = '").append(statView.getStatViewUniqueKey()).append("' ");
            if (GrayManager.isAllowByRule("stat_status_from_table_merge", statView.getTenantId())) {
                sb.append(" and view_version = ").append(statView.getVersion());
            } else {
                sb.append(" and view_version = 0");
            }
        } else {
            sb.append(" and view_id = '").append(statView.getViewId()).append("' ");
            sb.append(" and view_version = ").append(statView.getVersion());
//            sb.append(" and view_version = (select max(version) as version from bi_mt_topology_table where tenant_id='")
//                    .append(statView.getTenantId()).append("' and source_id='").append(statView.getViewId())
//                    .append("' and is_deleted = 0 and status = 1) ");
        }
        sb.append(" group by hash_code ) as agg_data_tmp");
        sb.append(" where 1 = 1");
        if (actionDateStart != null && !Constants.ACTION_DATE_EMPTY.equals(actionDateStart)) {
            sb.append(" and action_date >= '").append(actionDateStart).append("' ");
        }
        if (actionDateStart != null && !Constants.ACTION_DATE_NULL.equals(actionDateEnd)) {
            sb.append(" and action_date <= '").append(actionDateEnd).append("' ");
        }
        if (sb2.length() > 0) {
            sb.append(" and not(").append(sb2.substring(0, sb2.length() - 5)).append(")");
        }
        return sb.toString();
    }

}
