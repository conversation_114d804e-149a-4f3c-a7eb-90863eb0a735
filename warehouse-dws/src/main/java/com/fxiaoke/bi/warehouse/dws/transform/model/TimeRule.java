package com.fxiaoke.bi.warehouse.dws.transform.model;


import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.common.Pair;
import com.fxiaoke.helper.StringHelper;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

@SuperBuilder
public class TimeRule extends QuoteOfAgg<AggRule> {

  @Override
  public void init(AggRule aggRule) {
    //归位到AggApiOpLogListener
    if (aggRule.timeApiIsAggApi()) {
      return;
    }
    //归位到ObjectIdApiOpLogListener
    if (aggRule.timeApiIsObjectIdApi()) {
      return;
    }
  }

  public String createActionDateConfig(){
    return this.alias + "." + this.column;
  }

  public String createDetailActionDateConfig(){
    return this.alias + "." + this.column + "." + (StringUtils.isBlank(this.formatStr) ? "" : formatStr);
  }

  public String timeSelectSQL(String alias, String timeZone) {
    String columnName = column;
    if (StringHelper.containUpperChar(columnName)) {
      columnName = "\"" + column + "\"";
    }
    String atTimeZone = "";
    if (StringUtils.isNotBlank(timeZone)) {
      atTimeZone = String.format(" AT TIME ZONE '%s' ", timeZone);
    }
    switch (columnType) {
      case Date:
        return "to_char(" + alias + "." + columnName + " " + atTimeZone + ", 'yyyyMMdd')";
      case Number:
        return "to_char(to_timestamp(" + alias + "." + columnName + "/1000) " + atTimeZone + ", 'yyyyMMdd')";
      default:
        return "to_char(to_timestamp(" + alias + "." + columnName + "::BIGINT/1000) " + atTimeZone + ", 'yyyyMMdd')";
    }
  }

  public String columnAlias() {
    return "action_date";
  }

  public String timeWhereSQL(String alias, String actionDate, String timeZone) {
    String columName = column;
    if (StringHelper.containUpperChar(columName)) {
      columName = "\"" + column + "\"";
    }
    if (Constants.ACTION_DATE_EMPTY.equals(actionDate)) {
      return "AND " + alias + "." + columName + " is null";
    }
    switch (columnType) {
      case Date:
        Pair<String, String> fromTo1 = parseString(actionDate, timeZone);
        return "AND " + alias + "." + columName + " >= '" + fromTo1.first + "' AND " + alias + "." + columName +
          " < '" + fromTo1.second + "' ";
      case Number:
        Pair<Long, Long> fromTo2 = parseLong(actionDate, timeZone);
        return "AND " + alias + "." + columName + " >= " + fromTo2.first + " AND " + alias + "." + columName + " < " +
          fromTo2.second + " ";
      default:
        Pair<Long, Long> fromTo3 = parseLong(actionDate, timeZone);
        return "AND " + alias + "." + columName + " >= '" + fromTo3.first + "' AND " + alias + "." + columName +
          " < '" + fromTo3.second + "' ";
    }
  }

  //区间
  public String timeWhereSQL(String alias, Long from, Long to, String timeZone) {
    String columName = column;
    if (StringHelper.containUpperChar(columName)) {
      columName = "\"" + column + "\"";
    }

    String sql = "";
    switch (columnType) {
      case Date:
        if (null != from) {
          sql = " AND " + alias + "." + columName + " >= '" + Utils.long2DateString(from, timeZone) + "'";
        }
        if (null != to) {
          sql += " AND " + alias + "." + columName + " < '" + Utils.long2DateString(to, timeZone) + "' ";
        }
        return sql;
      case Number:
        if (null != from) {
          sql = " AND " + alias + "." + columName + " >= " + from;
        }
        if (null != to) {
          sql += " AND " + alias + "." + columName + " < " + to + " ";
        }
        return sql;
      default:
        if (null != from) {
          sql = " AND " + alias + "." + columName + "::BIGINT >= '" + from + "' ";
        }
        if (null != to) {
          sql += " AND " + alias + "." + columName + "::BIGINT < '" + to + "' ";
        }
        return sql;
    }
  }

  public Pair<Long, Long> parseLong(String actionDate, String timeZone) {
    return Utils.actionDate2Long(actionDate, timeZone);
  }

  public Pair<String, String> parseString(String actionDate, String timeZone) {
    return Utils.actionDate2String(actionDate, timeZone);
  }
}
