package com.fxiaoke.bi.warehouse.dws.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.mq.message.DBUpdateMessage;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBUpdateEventDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.service.*;
import com.fxiaoke.bi.warehouse.dws.transform.model.GoalChangeType;
import com.github.jedis.support.JedisCmd;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class DwsController {
  @Resource
  private DBUpdateEventDao dbUpdateEventDao;
  @Resource
  private DWSComputeService dwsComputeService;
  @Resource
  private StatTopologyService statTopologyService;
  @Resource
  private TopologyTableService topologyTableService;
  @Resource
  private BackgroundTaskService backgroundTaskService;
  @Resource
  private CompareService compareService;
  @Resource
  private CHAggDataService chAggDataService;
  @Resource
  private PgDataSource pgDataSource;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  @Resource
  private DWSRefreshService dwsRefreshService;

  @GetMapping("/test/{id}")
  public String edit(@PathVariable("id") String id) {
    return "hello! " + id;
  }

  /**
   * 批量修改统计图bi_mt_topology_table状态
   * @return String
   */
  @PostMapping("/resetTopologyByEi")
  public Object resetTopologyByEi(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("resetTopologyByEi json params:{}", JSON.toJSONString(statViewBatchArg));
    try {
      statTopologyService.resetTopologyByEi(statViewBatchArg);
    } catch (Exception e) {
      log.error("resetTopologyByEi error tenantId:{}", statViewBatchArg.getTenantId(), e);
      return "fail";
    }
    return "ok";
  }

  @PostMapping("/batchUpdateTopologyTableStatus")
  public String batchUpdateTopologyTableStatus(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("batchUpdateTopologyTableStatus json params:{}", JSON.toJSONString(statViewBatchArg));
    String tenantId = statViewBatchArg.getTenantId();
    if (StringUtils.isBlank(tenantId)) {
      return "fail tenantId is empty";
    }
    List<StatViewPreArg> statViewPreArgList = statViewBatchArg.getStatViewArgList();
    if (CollectionUtils.isNotEmpty(statViewPreArgList)) {
      statViewPreArgList.forEach(statViewPreArg -> {
        int updateSize = topologyTableService.updateTopologyTableStatusByUniqueKey(tenantId, statViewBatchArg.getStatus(), statViewPreArg.getSourceId(), 0, new Date().getTime(), false, null, statViewBatchArg.isIncVersion());
        if (updateSize > 0) {
          if (GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
            int uSize = topologyTableService.updateTopologyTableMergeStatusAndVersion(tenantId, statViewPreArg.getSourceId(), statViewBatchArg.getStatus(), 0L, null);
            log.info("updateTopologyTableMergeStatusAndVersion tenantId:{},uniqueKey:{},status:{},batchNum:{},uSize:{}", tenantId, statViewPreArg.getSourceId(), statViewBatchArg.getStatus(), 0L, uSize);
          }
        }
      });
    }
    return "success!";
  }

  /**
   * 批量拓扑图合并信息
   *
   * @param statViewBatchArg 参数
   * @return String
   */
  @PostMapping("/batchInitTopologyMerge")
 public String batchInitTopologyMerge(@RequestBody StatViewBatchArg statViewBatchArg){
    log.info("batchInitTopologyMerge json params:{}", JSON.toJSONString(statViewBatchArg));
    int result = topologyTableService.batchInitTopologyTableMerge(statViewBatchArg);
    return "success!"+result;
  }

  /**
   * 批量重跑租户得所有topologyTable数据，包含统计图，目标，多维度目标
   * @param statViewBatchArg 参数
   * @return
   */
  @PostMapping("/batchReCreateTopologyTable")
  public String batchReCreateTopologyTable(@RequestBody StatViewBatchArg statViewBatchArg){
    List<String> tenantIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(statViewBatchArg.getTenantId());
    tenantIds.forEach(tenantId -> {
      statTopologyService.reCreateTopologyByEI(tenantId,TopologyTableStatus.Prepared.getValue());
      log.info("batchReCreateTopologyTable tenantId:{}",tenantId);
    });
    return "ok";
  }

  /**
   * 批量刷统计图 statView
   *
   * @param statViewBatchArg 参数
   * @return String
   */
  @PostMapping("/batchCreateAggTopology")
  public String batchCreateAggTopology(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("batchCreateAggTopology json params:{}", JSON.toJSONString(statViewBatchArg));
    if (StringUtils.isBlank(statViewBatchArg.getTenantId())) {
      return "error:tenantId is empty";
    }
    List<StatViewPreArg> statViewPreArgList = statViewBatchArg.getStatViewArgList();
    if (CollectionUtils.isNotEmpty(statViewPreArgList)) {
      List<String> failViewIds = Lists.newArrayList();
      String tenantIdStr = statViewBatchArg.getTenantId();
      List<String> tenantIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(tenantIdStr);
      tenantIds.forEach(tenantId -> {
        statViewPreArgList.forEach(statViewPreArg -> {
          try {
            statTopologyService.doCreateTopology(tenantId, statViewPreArg.getSourceId(), statViewBatchArg.getSourceType(), TopologyTableStatus.Prepared.getValue());
          } catch (Exception e) {
            log.error("doCreateTopology error: tenantId:{},viewId:{}", tenantId, statViewPreArg.getSourceId(), e);
            failViewIds.add(tenantId + ":" + statViewPreArg.getSourceId());
          }
        });
      });
      if (CollectionUtils.isNotEmpty(failViewIds)) {
        return "fail:" + JSON.toJSONString(failViewIds);
      }
    } else {
      List<String> tenantIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(statViewBatchArg.getTenantId());
      tenantIds.forEach(tenantId -> {
        statTopologyService.batchCreateTopologyByEi(tenantId, statViewBatchArg.getSourceType(), statViewBatchArg.getDTFromOrDefault("1970-01-01 00:00:00"), statViewBatchArg.isAll());
        log.info("batchCreateTopologyByEi tenantId:{}",tenantId);
      });
    }
    return "finish";
  }

  /**
   * 批量重刷停用的自定义图
   * @param statViewBatchArg
   * @return
   */
  @PostMapping("/flushStopCustomStatView")
  public String flushStopCustomStatView(@RequestBody StatViewBatchArg statViewBatchArg) {
    List<String> tenantIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(statViewBatchArg.getTenantId());
    statTopologyService.flushStopCustomStatView(tenantIds);
    return "ok!";
  }

  /**
   * 批量删除计图 topology table
   *
   * @param statViewBatchArg 参数
   * @return String
   */
  @PostMapping("/batchDeleteAggTopology")
  public String batchDeleteAggTopology(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("batchDeleteAggTopology json params:{}", JSON.toJSONString(statViewBatchArg));
    List<StatViewPreArg> statViewPreArgList = statViewBatchArg.getStatViewArgList();
    List<String> viewIds = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(statViewPreArgList)) {
      viewIds.addAll(statViewPreArgList.stream().map(StatViewPreArg::getSourceId).toList());
    }
    String tenantIds = statViewBatchArg.getTenantId();
    if (StringUtils.isNotBlank(tenantIds)) {
      Splitter.on(CharMatcher.anyOf(",|")).omitEmptyStrings().splitToList(tenantIds).forEach(tenantId -> {
        topologyTableService.deleteTopologyTables(tenantId, viewIds, statViewBatchArg.getSourceType());
      });
    } else {
      return "tenantIds is empty";
    }
    return "success!";
  }

  /**
   * 批量初始化统计图状态 topology status
   *
   * @param statViewBatchArg 参数
   * @return String
   */
  @PostMapping("/batchInitStatViewStatus")
  public String batchInitAggTopologyStatus(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("batchInitAggTopologyStatus json params:{}", JSON.toJSONString(statViewBatchArg));
    int result = statTopologyService.batchInitAggStatViewStatus(statViewBatchArg);
    return "success!"+result;
  }

  @GetMapping("/statView/{tenantId}/{viewId}")
  public Object findStatView(@PathVariable("tenantId") String tenantId,
                             @PathVariable("viewId") String viewId,
                             @RequestParam(value = "monitor", defaultValue = "false") boolean monitor) {
    TopologyTable statView = topologyTableService.findByTenantIdAndSourceId(tenantId, viewId);
    if (null == statView) {
      return "not found";
    }
    if (monitor) {
      boolean standalone = pgDataSource.standalone(tenantId);
      boolean isDSCalculate = topologyTableService.downStreamAggIsCalculate(statView);
      return statView.toStatViewMonitor(null, isDSCalculate, null, GoalChangeType.GOAL_ALL_DATA_INC, topologyTableService.getTableKeys(statView),standalone);
    }
    return statView;
  }

  @PostMapping("/dbChangeEvent")
  public String dbChangeEvent(@RequestParam("id") String id) {
    log.info("dbChangeEvent id:{}", id);
    DBSyncInfoDO dbSyncInfoDO = dbUpdateEventDao.findSyncById(id);
    if (null != dbSyncInfoDO) {
      DBUpdateMessage dbUpdateMessage = DBUpdateMessage.builder()
                                                       .id(id)
                                                       .chDB(dbSyncInfoDO.getChDB())
                                                       .batchNum(dbSyncInfoDO.getBatchNum())
                                                       .pgDB(dbSyncInfoDO.getPgDB())
                                                       .schema(dbSyncInfoDO.getPgSchema())
                                                       .build();
      dwsComputeService.dbDataUpdated(dbUpdateMessage);
    }
    return "OK";
  }

  @PostMapping("/createPreViewSQL")
  public StatViewPreSQL createPreViewSQL(@RequestBody StatViewPreArg statViewPreArg) {
    log.info("createPreViewSQL json params:{}", JSON.toJSONString(statViewPreArg));
    StatViewPreSQL statViewPreSQL = statTopologyService.createStatViewPreSQL(statViewPreArg);
    log.info("createPreViewSQL result tenantId:{},sourceId:{},sourceType:{},queryDetailType:{},maxModifiedTime:{}", statViewPreArg.getTenantId(), statViewPreArg.getSourceId(), statViewPreArg.getSourceType(), statViewPreArg.getQueryDetailType(), statViewPreSQL.getMaxModifiedTime());
    return statViewPreSQL;
  }

  @PostMapping("/createBatchPreViewSQL")
  public StatViewBatchPreSQL createBatchPreViewSQL(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("createBatchPreViewSQL json params:{}", JSON.toJSONString(statViewBatchArg));
    return statTopologyService.createBatchPreViewSQL(statViewBatchArg);
  }

  @PostMapping("/createDetailViewSQL")
  public StatViewPreSQL createDetailViewSQL(@RequestBody StatViewPreArg statViewPreArg) {
    log.info("createDetailViewSQL json params:{}", JSON.toJSONString(statViewPreArg));
    return statTopologyService.createDetailViewSQL(statViewPreArg);
  }

  @PostMapping("/getLatestCalTime")
  public String getLastCalTime(@RequestBody StatViewPreArg statViewPreArg) {
    log.info("lastCalTime json params:{}", JSON.toJSONString(statViewPreArg));
    Map<String, Long> result = statTopologyService.queryDBLatestSyncTimeByEI(statViewPreArg);
    return JSON.toJSONString(result);
  }

  @PostMapping("/batchCompareViewField")
  public String batchCompareViewField(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("batchCompareViewField json params:{}", JSON.toJSONString(statViewBatchArg));
    List<Map<String, Object>> result = statTopologyService.batchCompareStatView(statViewBatchArg);
    return JSON.toJSONString(result);
  }

  @PostMapping("/checkView")
  public Object compareView(@RequestBody CheckDiffArg checkArg) {
    log.info("compareView json params:{}", JSON.toJSONString(checkArg));
    return compareService.compareView(checkArg);
  }

  @PostMapping("/checkViewTenants")
  public Object compareViewTenants(@RequestBody CheckDiffArg checkArg) {
    log.info("compareViewTenants tenantIds:{}", JSON.toJSONString(checkArg));
    return compareService.compareViewByTenantIds(checkArg);
  }

  @PostMapping("/upsertDSStatField")
  public Object upsertDSStatField(@RequestBody StatFieldArg statFieldArg) {
    log.info("upsertDSStatField statFieldArg:{}", JSON.toJSONString(statFieldArg));
    int sum = statTopologyService.upsertStatFieldByFieldId(statFieldArg);
    return "code:200, result:" + sum;
  }

  /**
   * 批量删除计图 topology table
   *
   * @param statViewBatchArg 参数
   * @return String
   */
  @PostMapping("/batchDeleteAggData")
  public String batchDeleteAggData(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("batchDeleteAggData json params:{}", JSON.toJSONString(statViewBatchArg));
    List<String> viewIds = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(statViewBatchArg.getStatViewArgList())) {
      viewIds.addAll(statViewBatchArg.getStatViewArgList().stream().map(StatViewPreArg::getSourceId).toList());
    }
    String tenantIds = statViewBatchArg.getTenantId();
    if (StringUtils.isNotBlank(tenantIds)) {
      Splitter.on(CharMatcher.anyOf(",|")).omitEmptyStrings().splitToList(tenantIds).forEach(tenantId -> {
        chAggDataService.batchDeleteAggData(tenantId, viewIds);
      });
    } else {
      return "tenantIds is empty";
    }
    return "success!";
  }

  /**
   * 批量拓扑图合并信息
   *
   * @param statViewBatchArg 参数
   * @return String
   */
  @PostMapping("/batchInitAllAggFields")
  public String batchInitAllAggFields(@RequestBody StatViewBatchArg statViewBatchArg){
    log.info("batchInitAllAggFields json params:{}", JSON.toJSONString(statViewBatchArg));
    int result = topologyTableService.batchInitAllAggFields(statViewBatchArg);
    return "success!"+result;
  }

    @PostMapping("/explainViewsCostByDB")
    public String explainViewsCostByDB(@RequestBody String chStr){
        log.info("explainViewsCostByDB param:{}", chStr);
        JSONObject jsonObject = JSONObject.parseObject(chStr);
        if(jsonObject != null){
            String chDB = jsonObject.getString("ch_db");
            List<String> chUrl = Lists.newArrayList();
            chUrl.add(chDB);
            new Thread(() -> backgroundTaskService.explainViewsCost(chUrl), "explainViewsCost-thread").start();
            log.info("explainViewsCostByDB finish");
            return "start";
        }
        return "invalid param";
    }

  /**
   * 批量修改统计图状态变更为实时查询
   * @param statViewBatchArg
   * @return
   */
  @PostMapping("/change2NoNeedCal")
  public String changeTopologyTable2NoNeedCalByKey(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("changeTopologyTable2NoNeedCalByKey params:{}",JSON.toJSONString(statViewBatchArg));
    String tenantId = statViewBatchArg.getTenantId();
    if (StringUtils.isBlank(tenantId)) {
      return "fail tenantId is empty";
    }
    List<StatViewPreArg> statViewPreArgList = statViewBatchArg.getStatViewArgList();
    if (CollectionUtils.isNotEmpty(statViewPreArgList)) {
      statViewPreArgList.forEach(statViewPreArg -> topologyTableService.changeTopologyTable2NoNeedCalByKey(tenantId, statViewPreArg.getSourceId()));
    }
    return "ok!";
  }

  /**
   * 迁移对象主题目标  老图 statView
   *
   * @param statViewBatchArg 参数
   * @return String
   */
  @PostMapping("/batchCreateGoalViewTopology")
  public String batchCreateGoalViewTopology(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("batchCreateGoalViewTopology json params:{}", JSON.toJSONString(statViewBatchArg));
    if (StringUtils.isNotBlank(statViewBatchArg.getTenantId())) {
      List<String> tenantIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(statViewBatchArg.getTenantId());
      tenantIds.forEach(tenantId -> {
        statTopologyService.batchCreateGoalViewTopologyByEI(tenantId, statViewBatchArg.getDTFromOrDefault("1970-01-01 00:00:00"), statViewBatchArg.isAll());
        log.info("batchCreateGoalViewTopology tenantId:{}",tenantId);
      });
    } else if (StringUtils.isNotBlank(statViewBatchArg.getDbSyncId())){
      List<String> dbSyncIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(statViewBatchArg.getDbSyncId());
      dbSyncIds.forEach(dbSyncId -> {
        statTopologyService.batchAllGoalViewTopology(dbSyncId, statViewBatchArg.getDTFromOrDefault("1970-01-01 00:00:00"), statViewBatchArg.isAll());
        log.info("batchCreateGoalViewTopology dbSyncId:{}",dbSyncId);
      });
    } else {
      log.error("batchCreateGoalViewTopology ERROR : 无效的参数");
      return "fail";
    }
    return "finish";
  }

    /**
     * 重刷已计算的明细主题的统计图
     *
     * @param statViewBatchArg 参数
     * @return String
     */
    @PostMapping("/batchRefreshGoalViewTopology")
    public String batchRefreshGoalViewTopology(@RequestBody StatViewBatchArg statViewBatchArg) {
        log.info("batchRefreshGoalViewTopology json params:{}", JSON.toJSONString(statViewBatchArg));
        List<StatViewPreArg> statViewPreArgList = statViewBatchArg.getStatViewArgList();
        if (CollectionUtils.isNotEmpty(statViewPreArgList)) {
            return "error";
        } else if (StringUtils.isNotBlank(statViewBatchArg.getDbSyncId())){
            List<String> dbSyncIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(statViewBatchArg.getDbSyncId());
            dbSyncIds.forEach(dbSyncId -> {
                statTopologyService.batchRefreshGoalViewTopology(dbSyncId, false, statViewBatchArg.isDel());
                log.info("batchRefreshGoalViewTopology dbSyncId:{}",dbSyncId);
            });
        } else if (statViewBatchArg.isAll()) {
            statTopologyService.batchRefreshGoalViewTopology("", true, statViewBatchArg.isDel());
        } else {
            log.error("batchRefreshGoalViewTopology ERROR : 无效的参数");
        }
        return "finish";
    }


  @PostMapping("/delRedisColumnCache")
  public String delRedisColumnCache(@RequestBody String redisKey) {
    log.info("change2NoNeedCal params:{}",redisKey);
    if (StringUtils.isBlank(redisKey)) {
      return "fail redisKey is empty";
    }
    List<String> keyList = Splitter.on(",").omitEmptyStrings().splitToList(redisKey);
    Long rows = jedisCmd.del(keyList.toArray(new String[0]));
    return "del key success num:%d".formatted(rows);
  }

  /**
   * 重刷已计算的目标规则
   *
   * @param statViewBatchArg 参数
   * @return String
   */
  @PostMapping("/batchRepairGoalRuleTopology")
  public String batchRepairGoalRuleTopology(@RequestBody StatViewBatchArg statViewBatchArg) {
    log.info("batchRepairGoalRuleTopology json params:{}", JSON.toJSONString(statViewBatchArg));
    List<StatViewPreArg> statViewPreArgList = statViewBatchArg.getStatViewArgList();
    if (CollectionUtils.isNotEmpty(statViewPreArgList)) {
      return "error";
    } else if (StringUtils.isNotBlank(statViewBatchArg.getDbSyncId())){
      List<String> dbSyncIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(statViewBatchArg.getDbSyncId());
      dbSyncIds.forEach(dbSyncId -> {
        statTopologyService.batchRepairGoalRuleTopology(dbSyncId, false);
        log.info("batchRepairGoalRuleTopology dbSyncId:{}",dbSyncId);
      });
    } else if (statViewBatchArg.isAll()) {
      statTopologyService.batchRepairGoalRuleTopology("", true);
    } else {
      log.error("batchRepairGoalRuleTopology ERROR : 无效的参数");
    }
    return "finish";
  }


  @PostMapping("/refreshStatView")
  public String refreshStatView(@RequestBody Map<String, Object> params) {
    log.info("refreshStatView RequestBody:{}", JSON.toJSONString(params));
    JSONObject paramsJson = new JSONObject(params);
    String tenantId = paramsJson.getString("tenantId");
    String viewId = paramsJson.getString("viewId");
    if(StringUtils.isBlank(tenantId) || StringUtils.isBlank(viewId)){
      return "params is blank";
    }
    Integer intervalDays = paramsJson.getInteger("intervalDays");
    Boolean delOld =paramsJson.getBoolean("delOld");
    dwsRefreshService.refreshStatView(tenantId, viewId, intervalDays==null?0:intervalDays, delOld != null && delOld);
    return "success!";
  }
}
