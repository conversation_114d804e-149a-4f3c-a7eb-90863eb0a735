package com.fxiaoke.bi.warehouse.ods.integrate.model;

/**
 * @Author:jief
 * @Date:2024/4/23
 */
public enum AggDataColumnType {
  _String {
    @Override
    public String buildSQL(String aggType, String columnName, String alias) {
      return String.format("any(%s) AS %s", columnName, alias);
    }
  }, _NUMBER {
    @Override
    public String buildSQL(String aggType, String columnName, String alias) {
      return String.format("sum(%s) AS %s", columnName, alias);
    }
  }, _UNIQ_STATE {
    @Override
    public String buildSQL(String aggType, String columnName, String alias) {
      return String.format("hex(uniqExactMergeState(%s)) AS %s", columnName, alias);
    }
  };

  public String buildSQL(String aggType, String columnName, String alias) {
    return "sum(%s)";
  }
}
