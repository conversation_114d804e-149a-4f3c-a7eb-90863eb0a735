package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.db.entity.StatFieldStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.er.*;
import com.fxiaoke.bi.warehouse.common.goal.BITopology;
import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.ObjectConfigManager;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.common.util.TopologyUtils;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.StatFieldMapper;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjMapper;
import com.fxiaoke.bi.warehouse.dws.model.QuoteTargetField;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.model.WhereConfig;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.*;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.*;
import com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper.GoalRuleMapper;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.CaseInsensitiveMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2023/4/12
 */
@Slf4j
@Service
public abstract class RuleDao {
  @Autowired
  private CustomStatisticAggFieldMapper customStatisticAggFieldMapper;
  @Autowired
  private BIMtDimensionMapper biMtDimensionMapper;
  @Autowired
  private BIMtMeasureMapper biMtMeasureMapper;
  @Autowired
  private BIMtDetailMapper biMtDetailMapper;
  @Autowired
  public BIMtTopologyDescribeMapper biMtTopologyDescribeMapper;
  @Resource
  private UdfObjFieldMapper udfObjFieldMapper;
  @Autowired
  private UdfObjMapper udfObjMapper;
  @Resource
  private MappingService mappingService;
  @Autowired
  private StatFieldMapper statFieldMapper;
  @Autowired
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Autowired
  private GoalRuleMapper goalRuleMapper;

  private Set<String> oldObjNames = Sets.newHashSet();
  private Map<String, Pair<String, String>> biObjMainPropertySndPkMapp = new CaseInsensitiveMap();
  private String biSystemTableCopySchTenantId = "1";

  /**
   * {@see <a href='https://oss.foneshare.cn/cms/edit/config/17121'>bi-statictic-agg</a>}
   */
  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("bi-statictic-agg", new IniChangeListener("agg_data2_refactor") {
      @Override
      public void iniChanged(IniConfig iniConfig) {
        //主属性db_field_name转换
        Map<String, Pair<String, String>> tempMainPropertyAndPKMap = new CaseInsensitiveMap();
        tempMainPropertyAndPKMap.putAll(Splitter.on(',')
                                                .trimResults()
                                                .omitEmptyStrings()
                                                .withKeyValueSeparator(":")
                                                .split(iniConfig.get("objMainPropertyAndPkMapp"))
                                                .entrySet()
                                                .stream()
                                                .collect(Collectors.toMap(Map.Entry::getKey,
                                                  (Function<Map.Entry<String, String>, Pair<String, String>>) orginEntry -> {
                                                  int splitIndex = orginEntry.getValue().indexOf('|');
                                                  return ImmutablePair.of(orginEntry.getValue()
                                                                                    .substring(0, splitIndex),
                                                          orginEntry.getValue().substring(splitIndex + 1));
                                                })));
        biObjMainPropertySndPkMapp = tempMainPropertyAndPKMap;
        oldObjNames = Sets.newHashSet(Splitter.on(',')
                                              .trimResults()
                                              .omitEmptyStrings()
                                              .splitToList(iniConfig.get("oldObjNames")));
        biSystemTableCopySchTenantId = iniConfig.get("biSystemTableCopySchTenantId");
      }
    });
  }


  public BIMtTopologyDesDO findBiMtTopologyDes(String tenantId, String sourceId, Integer source) {
    return biMtTopologyDescribeMapper.setTenantId(tenantId).findBiMtTopologyDes(tenantId, sourceId, source);
  }

  public BIMtTopologyDesDO findBiMtTopologyDesById(String tenantId, String id) {
    return biMtTopologyDescribeMapper.setTenantId(tenantId).findBiMtTopologyDesById(tenantId, id);
  }

  public List<String> findBiMtSourceIdsByEi(String tenantId, Integer source) {
    return biMtTopologyDescribeMapper.setTenantId(tenantId).findBiMtSourceIdsByEi(tenantId, source);
  }

  public List<String> findGoalBiMtSourceIdsByEi(String tenantId) {
    return biMtTopologyDescribeMapper.setTenantId(tenantId).findGoalBiMtSourceIdsByEi(tenantId);
  }

  public List<BIMtMeasureDO> findMeasuresByTopologyId(String tenantId, String topologyId) {
    return biMtMeasureMapper.setTenantId(tenantId).findBIMtMeasures(tenantId, topologyId);
  }

  public List<BIMtMeasureDO> findMeasuresByTopologyIdAndGoal(String tenantId, String topologyId) {
    return biMtMeasureMapper.setTenantId(tenantId).findBIMtMeasuresByGoal(tenantId, topologyId);
  }

  public List<BIMtDimensionDO> findDimsByTopologyId(String tenantId, String topologyId) {
    return biMtDimensionMapper.setTenantId(tenantId).findDimByTopologyDescribeId(tenantId, topologyId);
  }

  public List<BIMtDimensionDO> findDimsByTopologyIdAndGoal(String tenantId, String topologyId) {
    return biMtDimensionMapper.setTenantId(tenantId).findDimByTopologyDescribeIdAndGoal(tenantId, topologyId);
  }


  public List<BIMtDimensionDO> findDimsByParentGoalId(String tenantId, String topologyIds) {
    return biMtDimensionMapper.setTenantId(tenantId).findDimsByParentGoalId(tenantId, topologyIds);
  }

  public List<BIMtDetailDO> findDetailsByTopologyId(String tenantId, String topologyId) {
    return biMtDetailMapper.setTenantId(tenantId).findBIMtDetail(tenantId, topologyId);
  }

  public String findDatabaseId(String tenantId, String biApiName) {
    UdfObjDO udfObjDO = udfObjMapper.setTenantId(tenantId)
                                    .findObjNameByBiApiName(Integer.parseInt(tenantId), biApiName);
    return udfObjDO == null ? null : udfObjDO.getDatabaseId();
  }

  /**
   * 构建拓扑图
   *
   * @param tenantId 租户id
   * @param sourceId 图id
   * @param source   图类型目标，统计图，多维度目标
   */
  public BiMtRule createBiMtRule(String tenantId, String sourceId, Integer source) {
    BIMtTopologyDesDO biMtTopologyDesDO = this.findBiMtTopologyDesById(tenantId, sourceId);
    if (biMtTopologyDesDO == null) {
      log.warn("can not find topology by tenantId:{} and sourceId:{},source:{}", tenantId, sourceId, source);
      return null;
    }
    List<BIMtMeasureDO> biMtMeasureDOS;
    List<BIMtDimensionDO> biMtDimensionDOS;
    //兼容目标脏数据,子规则脏数据无法兼容
    if ((source == 1 || source == 2) && !biMtTopologyDesDO.getTopologyDescribeId().contains("|")) {
      biMtMeasureDOS = this.findMeasuresByTopologyIdAndGoal(tenantId, biMtTopologyDesDO.getTopologyDescribeId());
      biMtDimensionDOS = this.findDimsByTopologyIdAndGoal(tenantId, biMtTopologyDesDO.getTopologyDescribeId());
    } else {
      biMtMeasureDOS = this.findMeasuresByTopologyId(tenantId, biMtTopologyDesDO.getTopologyDescribeId());
      biMtDimensionDOS = this.findDimsByTopologyId(tenantId, biMtTopologyDesDO.getTopologyDescribeId());
    }
    List<BiMtMeasureRule> biMtMeasureRules = biMtMeasureDOS.stream().map(BIMtMeasureDO::createDimMeasureRule).toList();
    List<BiMtDimRule> biMtDimRules = biMtDimensionDOS.stream().map(BIMtDimensionDO::toMultiDimRule).toList();
    return BiMtRule.builder()
                   .tenantId(tenantId)
                   .topologyDescribeId(biMtTopologyDesDO.getTopologyDescribeId())
                   .sourceType(biMtTopologyDesDO.getSource())
                   .detailSourceType(biMtTopologyDesDO.getSource())
                   .standalone(this.standalone(tenantId))
                   .biTopology(BITopology.parseFromJson(biMtTopologyDesDO.getTopologyModel()))
                   .biMtDimRules(biMtDimRules)
                   .biMtMeasureRules(biMtMeasureRules)
                   .build();

  }

  /**
   * 根据fieldId反查所有aggRule
   *
   * @param tenantId 租户Id
   * @param fieldIds 指标fieldId
   * @return List
   */
  public List<StatFieldDO> findStatFieldByFieldId(String tenantId,
                                                  List<String> fieldIds,
                                                  String[] aggDimType,
                                                  int[] status) {
    String inSql = fieldIds.stream().map(field -> "'" + field + "'").collect(Collectors.joining(","));
    return statFieldMapper.setTenantId(tenantId).queryFieldBase(tenantId, inSql, aggDimType, status);
  }

  /**
   * 获取对象得主属性字段名称
   *
   * @param objName 对象apiName
   * @return
   */
  public String getMainField(String objName) {
    if (oldObjNames.contains(objName)) {
      return biObjMainPropertySndPkMapp.getOrDefault(objName, ImmutablePair.of("name", "id")).getKey();
    } else if (Constants.SCHEMA_GOAL_OBJ_NAME.equalsIgnoreCase(objName)) {
      return Constants.CHECK_OBJECT_ID;
    } else {
      return "name";
    }
  }

  public boolean standalone(String tenantId) {
    return mybatisTenantPolicy.standalone(tenantId);
  }

  public Map<String, Object> findAggRuleMapFromDB(String tenantId, String ruleId) {
    if (ruleId.contains("billboard")) {
      return customStatisticAggFieldMapper.setTenantId("-1").getUsedBillboardRuleInfoByRuleId(ruleId);
    } else {
      return customStatisticAggFieldMapper.setTenantId(tenantId).getUsedRuleInfoByTenantIdAndRuleId(tenantId, ruleId);
    }
  }

  public Map<String, Object> findStatFieldMapFromDB(String tenantId, String fieldId) {
    return customStatisticAggFieldMapper.setTenantId(tenantId).getUsedDownStreamRuleInfoByFieldId(tenantId, fieldId);
  }

  /**
   * 根据规则批量查询指标信息
   *
   * @param tenantId
   * @param ruleIds
   * @param status
   * @return
   */
  public List<StatFieldDO> batchQueryAggRuleByRuleIds(String tenantId, String[] ruleIds, int[] status) {
    return statFieldMapper.setTenantId(tenantId).batchQueryAggRuleByRuleIdAndStatus(tenantId, ruleIds, status);
  }

  /**
   * 生成对象主键的DimRule用于指标对象的去重求和列
   *
   * @param tenantId        租户id
   * @param standalone      是否schema隔离
   * @param describeApiName 对象apiName
   * @param dstColumnName   aggData的槽位列名
   * @return
   */
  public DimRule createKeyDimRule(String tenantId, boolean standalone, String describeApiName, String dstColumnName) {
    String keyName = Constants.getIdName(standalone, describeApiName);
    DimFieldRule dimFieldRule = DimFieldRule.builder()
                                            .tenantId(tenantId)
                                            .apiName(describeApiName)
                                            .dstColumnName(dstColumnName)
                                            .fieldName(keyName)
                                            .dbFieldName(keyName)
                                            .dbObjName(describeApiName)
                                            .column(keyName)
                                            .columnType(PGColumnType.String)
                                            .fieldType(FieldType.TEXT)
                                            .isSingle(true)
                                            .displayType(DisplayField.DisplayType.group)
                                            .build();
    return DimRule.builder().dimFieldRule(dimFieldRule).build();
  }

  /**
   * 生成join 关系对象默认都用left join
   *
   * @param tenantId      租户id
   * @param standalone    是否schema隔离
   * @param sourceApiName 源对象apiName
   * @param lookUpField   查找关联字段对象
   * @param joinType      join 类型
   * @return
   */
  public JoinRelation createRelation(String tenantId,
                                     boolean standalone,
                                     String sourceApiName,
                                     UdfObjFieldDO lookUpField,
                                     String targetApiName,
                                     Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                                     AggJoinType joinType) {
    Preconditions.checkNotNull(lookUpField, String.format("tenantId:%s,sourceApiName:%s,lookupField is null", tenantId, sourceApiName));
    String type = lookUpField.getType();
    switch (type) {
      case FieldType.GROUP -> {
        String relationTable = lookUpField.getRelationTable();
        if (StringUtils.isNotBlank(relationTable)) {
          return new JoinRelation(Constants.getIdName(standalone, sourceApiName), type, relationTable, joinType, PGColumnType.String);
        }
        if(Constants.RELATED_OBJECT.equals(lookUpField.getDbFieldName()) && StringUtils.isNotBlank(lookUpField.getWhatIdField())){
          return new JoinRelation(lookUpField.getWhatIdField(), type, targetApiName, joinType, PGColumnType.String);
        }
      }
      case FieldType.QUOTE -> {
        String referenceFieldName = lookUpField.getRelationKeyField();
        Map<String, Object> referenceField = null;
        if (Utils.isSlotColumn(referenceFieldName)) {
          referenceField = this.findFieldInfoByFieldLocation(tenantId, sourceApiName, Integer.parseInt(referenceFieldName.substring(5)));
        } else {
          //查找引用列
          referenceField = this.findFieldInfoByFieldName(tenantId, sourceApiName, referenceFieldName);
        }
        if (null == referenceField) {
          return null;
        }
        JSONObject referenceFieldInfo = new JSONObject(referenceField);
        String refColumn = referenceFieldName;
        Integer fieldLocation = referenceFieldInfo.getInteger("field_location");
        if (fieldLocation != null && fieldLocation > 0) {
          refColumn = "value" + fieldLocation;
        }
        String fieldType = referenceFieldInfo.getString("type");
        String table = sourceApiName;
        if (table.endsWith("__c") && !standalone) {
          table = "object_data";
        }
        String refObjName = mappingService.udfApiName2ApiName(referenceFieldInfo.getString("ref_obj_name"));
        PGColumnType columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions, refColumn, fieldType);
        return JoinRelation.builder()
                           .joinType(joinType)
                           .fieldType(fieldType)
                           .apiName(refObjName)
                           .column(refColumn)
                           .columnType(columnType)
                           .build();
      }
      default -> {
        if (StringUtils.isNotBlank(lookUpField.getRefObjName())) {
          String refColumn = lookUpField.getDbFieldName();
          int fieldLocation = Integer.parseInt(lookUpField.getFieldLocation());
          if (fieldLocation > 0) {
            refColumn = "value" + fieldLocation;
          }
          String table = sourceApiName;
          if (table.endsWith("__c") && !standalone) {
            table = "object_data";
          }
          PGColumnType columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions, refColumn, type);
          String refObjName = lookUpField.getRefObjName();
          if (StringUtils.isNotBlank(refObjName)) {
            return JoinRelation.builder()
                               .joinType(joinType)
                               .fieldType(type)
                               .apiName(refObjName)
                               .column(refColumn)
                               .columnType(columnType)
                               .build();
          }
        } else {
          //特殊处理一些关联关系,这些关联字段是固定的,无法体现在描述上,老对象
          if ("base_crmfeedrelation".equals(lookUpField.getDbObjName()) && "data_id".equals(lookUpField.getDbFieldName())) {
            return JoinRelation.builder()
                               .joinType(joinType)
                               .fieldType(type)
                               .apiName(targetApiName)
                               .column(lookUpField.getDbFieldName())
                               .columnType(PGColumnType.String)
                               .build();
          }
        }
      }
    }
    return null;
  }

  /**
   * 根据join relation 添加lookupTable
   * // todo 有可能是通过what关系字段查找关联的，需要判断 targetApiName 是否和 joinRelation 的apiName 是否一致
   *
   * @param tenantId        租户id
   * @param standalone      是否schema隔离
   * @param sourceNodeTable 源表
   * @param aliasMapper     别名cache
   * @param joinRelation    join关系
   * @return
   */
  public NodeTable appendLookUpNodeTable(String tenantId,
                                         boolean standalone,
                                         NodeTable sourceNodeTable,
                                         Map<String, AtomicInteger> aliasMapper,
                                         JoinRelation joinRelation) {
    String sourceApiName = sourceNodeTable.getObjectDescribeApiName();
    String targetApiName = joinRelation.apiName;
    String targetPreTableName = Constants.table(targetApiName, null, standalone);
    //引用字段不是扩展对象上的字段
    NodeTable quoteNodeTable = NodeTable.of(targetPreTableName, null, Lists.newArrayList(), Sets.newTreeSet(),
      targetApiName, joinRelation.column, false, null, null);
    //构建和被引用对象的join 关系
    //判断是否需要通过扩展字段查找关联被引用对象
    if (!standalone && !sourceApiName.endsWith("__c") && Constants.slotRegex.matcher(joinRelation.column).matches()) {
      //通过扩展字段查找关联引用对象。
      NodeJoin sourceExtNodeJoin = this.createExtNodeTable(sourceApiName);
      NodeTable sourceExtNodeTable = sourceNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
      NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, false, joinRelation, sourceExtNodeTable,
        quoteNodeTable, mappingService);
      return sourceExtNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, true);
    } else {
      NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation, sourceNodeTable,
        quoteNodeTable, mappingService);
      return sourceNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, true);
    }
  }

  /**
   * 创建agg表的前置过滤条件
   *
   * @param tenantId        租户id
   * @param aggObjNodeTable agg NodeTable 对象
   * @return where条件
   */
  public List<WhereConfig> createPreWhereConfigList(String tenantId, NodeTable aggObjNodeTable, ValueRule valueRule) {
    //where 条件
    List<WhereConfig> preWhereConfigList = Lists.newArrayList();
    //添加tenantId where 条件
    preWhereConfigList.add(WhereConfig.builder()
                                      .whereExpr(aggObjNodeTable.getAlias() + ".tenant_id='" + tenantId + "'")
                                      .build());
    if (Constants.isHasIsDeleted(aggObjNodeTable.getName())) {
      preWhereConfigList.add(WhereConfig.builder().whereExpr(aggObjNodeTable.getAlias() + ".is_deleted = 0").build());
      aggObjNodeTable.appendColumns("is_deleted");
    }
    switch (aggObjNodeTable.getName()) {
      //如果指标对象是自定义对象则需要加上object_describe_api_name筛选
      case Constants.OBJECT_DATA -> {
        aggObjNodeTable.appendColumns("object_describe_api_name");
      }
      case Constants.BIZ_ACCOUNT -> {
        //如果指标对象是客户对象则需要加上object_describe_api_name='AccountObj' 筛选
        if ("biz_account".equals(aggObjNodeTable.objectDescribeApiName())) {
          aggObjNodeTable.appendColumns("object_describe_api_name");
        } else if ("biz_account_main_data".equals(aggObjNodeTable.objectDescribeApiName())) {
          aggObjNodeTable.appendColumns("object_describe_api_name");
        }
      }
      case Constants.STAGE_RUNTIME -> {
        if ("stage_runtime_new_opportunity".equals(aggObjNodeTable.objectDescribeApiName())) {
          aggObjNodeTable.appendColumns("entity_id", "instance_status");
        }
      }
    }
    return preWhereConfigList;
  }

  /**
   * 预置对象join 扩展对象
   *
   * @param apiName 预设对象apiName
   */
  public NodeJoin createExtNodeTable(String apiName) {
    List<NodeColumn> nodeColumns = Lists.newArrayList();
    NodeTable objectTable = NodeTable.of(Constants.OBJECT_DATA, null, nodeColumns, Sets.newTreeSet(), apiName, "extend_obj_data_id", false, null, null);
    objectTable.appendColumns("tenant_id", "_id", "object_describe_api_name", "value0", "is_deleted");
    NodeJoin extTableJoin = new NodeJoin();
    extTableJoin.setJoinType(JoinType.LEFT_JOIN);
    extTableJoin.setTable(objectTable);
    extTableJoin.setOnCondition(Constants.getLeftJoinExtObjOnCondition(apiName, false));
    return extTableJoin;
  }

  /**
   * 扩展对象join 预置对象j
   *
   * @param apiName 预设对象apiName
   */
  public NodeJoin createPreNodeTable(String apiName) {
    List<NodeColumn> nodeColumns = Lists.newArrayList();
    NodeTable objectTable = NodeTable.of(Constants.table(apiName, null, false), null, nodeColumns, Sets.newTreeSet(), apiName, "_id", false, null, null);
    objectTable.appendColumns("extend_obj_data_id", "id", "is_deleted");
    NodeJoin extTableJoin = new NodeJoin();
    extTableJoin.setJoinType(JoinType.LEFT_JOIN);
    extTableJoin.setTable(objectTable);
    extTableJoin.setOnCondition(Constants.getLeftJoinPreObjOnCondition(apiName));
    return extTableJoin;
  }

  /**
   * 预置对象join 扩展对象
   *
   * @param apiName     预设对象apiName
   * @param quoteOfAggs 查询字段列表
   */
  public NodeJoin createExtNodeTablePlus(String apiName, List<QuoteOfAgg> quoteOfAggs, boolean isSelectInvalid) {
    List<NodeColumn> nodeColumns = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(quoteOfAggs)) {
      quoteOfAggs.forEach(quoteOfAgg -> {
        nodeColumns.add(Constants.createNodeColumn(quoteOfAgg, false));
      });
    }
    NodeTable objectTable = NodeTable.of(Constants.OBJECT_DATA, null, nodeColumns, Sets.newTreeSet(), apiName, "extend_obj_data_id", false, null, null);
    objectTable.appendColumns("_id", "object_describe_api_name", "value0", "is_deleted");
    if (isSelectInvalid) {
      objectTable.setSelectInvalid(true);
      objectTable.appendSubWheres(Constants.RIGHT_ON_DELETED_0_1);
    }
    NodeJoin extTableJoin = new NodeJoin();
    extTableJoin.setJoinType(JoinType.LEFT_JOIN);
    extTableJoin.setTable(objectTable);
    extTableJoin.setOnCondition(Constants.getLeftJoinExtObjOnCondition(apiName, isSelectInvalid));
    return extTableJoin;
  }

  /**
   * 扩展对象join 预置对象
   *
   * @param apiName     预设对象apiName
   * @param quoteOfAggs 查询字段列表
   */
  public NodeJoin createPreNodeTablePlus(String apiName, List<QuoteOfAgg> quoteOfAggs) {
    List<NodeColumn> nodeColumns = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(quoteOfAggs)) {
      quoteOfAggs.forEach(quoteOfAgg -> {
        nodeColumns.add(Constants.createNodeColumn(quoteOfAgg, false));
      });
    }
    NodeTable objectTable = NodeTable.of(Constants.table(apiName, null, false), null, nodeColumns, Sets.newTreeSet(), apiName, "_id", false, null, null);
    objectTable.appendColumns("extend_obj_data_id", "id", "is_deleted");
    NodeJoin preTableJoin = new NodeJoin();
    preTableJoin.setJoinType(JoinType.LEFT_JOIN);
    preTableJoin.setTable(objectTable);
    preTableJoin.setOnCondition(Constants.getLeftJoinPreObjOnCondition(apiName));
    return preTableJoin;
  }

  /**
   * 关联多语表
   *
   * @param apiName     预设对象apiName
   * @param quoteOfAgg 查询字段
   * @isPre
   */
  public NodeJoin createLangNodeTablePlus(String apiName, QuoteOfAgg quoteOfAgg, boolean standalone, String tenantId) {
    List<NodeColumn> nodeColumns = Lists.newArrayList(Constants.createNodeColumn(quoteOfAgg, true));
    nodeColumns.add(Constants.buildLangColumn());
    String langTableName = quoteOfAgg.tableName + Constants.LANG_SUFFIX;
    String langTableAlias = langTableName + "_" + TableAliasNaming.index(quoteOfAgg.alias);
    String lookupColumn = Constants.getIdNameByTableName(quoteOfAgg.tableName).get(0);
    NodeTable langTable = NodeTable.of(langTableName, langTableAlias, nodeColumns, Sets.newTreeSet(), apiName,
      lookupColumn, false, null, null);
    langTable.appendColumns("data_id","lang");
    langTable.appendSubWheres(" AND is_deleted = false ");
    Set<String> onFilter = Sets.newHashSet();
    onFilter.add(Constants.EI_ON_CONDITIONS);
    String objectApiName = apiName;
    switch (langTable.getName()) {
      case Constants.OBJECT_DATA_LANG -> {
        if (!apiName.endsWith("__c")) {
          objectApiName = GrayManager.isAllowByRule("rpt_lang_extend_obj_paas_eis", tenantId) ? mappingService.paasApiName(apiName) : ObjectConfigManager.getExtendObjName(apiName);
        }
        langTable.appendSubWheres(" AND object_describe_api_name ='" + objectApiName + "'");
        onFilter.add("${lt}.value0 = ${rt}.data_id");
      }
      case "biz_account_lang" -> {
        if ("biz_account".equals(apiName)) {
          langTable.appendSubWheres(" AND describe_api_name='AccountObj'");
        } else if ("biz_account_main_data".equals(apiName)) {
          langTable.appendSubWheres(" AND describe_api_name='AccountMainDataObj'");
        }
        onFilter.add("${lt}.id = ${rt}.data_id");
      }
      case "stage_runtime_lang" -> {
        if ("stage_runtime_new_opportunity".equals(apiName)) {
          langTable.appendSubWheres(" AND entity_id='NewOpportunityObj' AND instance_status = ''");
        }
        onFilter.add("${lt}.id = ${rt}.data_id");
      }
      default -> onFilter.add(String.format("${lt}.%s = ${rt}.data_id", Constants.getIdName(standalone, langTable.getObjectDescribeApiName())));
    }
    NodeJoin endTableJoin = new NodeJoin();
    endTableJoin.setJoinType(JoinType.LEFT_JOIN);
    endTableJoin.setTable(langTable);
    endTableJoin.setOnCondition(new NodeOnCondition(onFilter.toArray(new String[0]), null, null));
    return endTableJoin;
  }

  /**
   * 查找关联引用字段
   *
   * @param tenantId
   * @param standalone         是否是schema隔离
   * @param aggNodeTable
   * @param aliasMapper        表别名cache
   * @param quoteOfAgg
   * @param overrideJoinType   add join的时候是否覆盖join type，主对象和扩展对象不用判断
   * @param forceReturnPreNode 强制返回预设对象的 NodeTable对象
   */
  public NodeTable joinQuoteNodeTable(String tenantId,
                                      boolean standalone,
                                      NodeTable aggNodeTable,
                                      Map<String, AtomicInteger> aliasMapper,
                                      QuoteOfAgg quoteOfAgg,
                                      boolean overrideJoinType,
                                      boolean forceReturnPreNode) {
    String aggApiName = aggNodeTable.getObjectDescribeApiName();
    JoinRelation joinRelation = quoteOfAgg.joinRelation;
    if (joinRelation == null) {
      String aggColumn = quoteOfAgg.column;
      //判断是否是扩展字段
      if (!standalone && !aggApiName.endsWith("__c") && Constants.slotRegex.matcher(aggColumn).matches()) {
        //扩展字段需要关联扩展对象
        NodeJoin sourceExtNodeJoin = this.createExtNodeTablePlus(aggApiName, Lists.newArrayList(quoteOfAgg), false);
        NodeTable extNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
        aggNodeTable.appendColumns("extend_obj_data_id", "id");
        quoteOfAgg.tableName = extNodeTable.getName();
        quoteOfAgg.alias = extNodeTable.getAlias();
        return forceReturnPreNode ? aggNodeTable : extNodeTable;
      } else {
        //如果是预置对象字段直接加上本字段即可
        NodeColumn extColumn = Constants.createNodeColumn(quoteOfAgg, false);
        if (!aggNodeTable.getColumnList().contains(extColumn)) {
          aggNodeTable.getColumnList().add(extColumn);
        }
        quoteOfAgg.tableName = aggNodeTable.getName();
        quoteOfAgg.alias = aggNodeTable.getAlias();
        return aggNodeTable;
      }
    } else {
      //字段是查找关联其他对象的字段
      //判断被引用字段是否是扩展对象上的字段
      String targetApiName = joinRelation.apiName;
      String targetPreTableName = Constants.table(targetApiName, null, standalone);
      NodeTable quoteNodeTable;
      if (!standalone && !targetApiName.endsWith("__c") && Constants.slotRegex.matcher(quoteOfAgg.column).matches()) {
        //引用字段是扩展对象上的字段
        quoteNodeTable = NodeTable.of(targetPreTableName, null, Lists.newArrayList(), Sets.newTreeSet(), targetApiName, joinRelation.column, false, null, null);
        //构建和被引用对象的join 关系
        //判断是否需要通过扩展字段查找关联被引用对象
        if (!aggApiName.endsWith("__c") && Constants.slotRegex.matcher(joinRelation.column).matches()) {
          //通过扩展字段查找关联引用对象。
          NodeJoin sourceExtNodeJoin = this.createExtNodeTable(aggApiName);
          NodeTable sourceExtTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
          aggNodeTable.appendColumns("extend_obj_data_id", "id");
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation, sourceExtTable, quoteNodeTable, mappingService);
          quoteNodeTable = sourceExtTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
        } else {
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation, aggNodeTable, quoteNodeTable, mappingService);
          quoteNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
        }
        //join 扩展对象的扩展字段
        NodeJoin targetExtNodeJoin = this.createExtNodeTablePlus(targetApiName, Lists.newArrayList(quoteOfAgg), false);
        NodeTable targetExtNodeTable = quoteNodeTable.addJoinSetSingle(aliasMapper, targetExtNodeJoin, true);
        quoteNodeTable.appendColumns("extend_obj_data_id", "id");
        quoteOfAgg.tableName = targetExtNodeTable.getName();
        quoteOfAgg.alias = targetExtNodeTable.getAlias();
        return forceReturnPreNode ? quoteNodeTable : targetExtNodeTable;
      } else {
        //引用字段不是扩展对象上的字段
        NodeColumn extColumn = Constants.createNodeColumn(quoteOfAgg, false);
        quoteNodeTable = NodeTable.of(targetPreTableName, null, Lists.newArrayList(extColumn), Sets.newTreeSet(), targetApiName, joinRelation.column, false, null, null);
        //构建和被引用对象的join 关系
        String lookupColumn = joinRelation.column;
        //判断是否需要通过扩展字段查找关联被引用对象
        if (!standalone && !aggApiName.endsWith("__c") && Constants.slotRegex.matcher(lookupColumn).matches()) {
          //通过扩展字段查找关联引用对象。
          NodeJoin sourceExtNodeJoin = this.createExtNodeTable(aggApiName);
          NodeTable sourceExtNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
          aggNodeTable.appendColumns("extend_obj_data_id", "id");
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, false, joinRelation, sourceExtNodeTable, quoteNodeTable, mappingService);
          NodeTable quoteNodeTableTmp = sourceExtNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
          quoteOfAgg.tableName = quoteNodeTableTmp.getName();
          quoteOfAgg.alias = quoteNodeTableTmp.getAlias();
          return quoteNodeTableTmp;
        } else {
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation, aggNodeTable, quoteNodeTable, mappingService);
          NodeTable quoteNodeTableTmp = aggNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
          quoteOfAgg.tableName = quoteNodeTableTmp.getName();
          quoteOfAgg.alias = quoteNodeTableTmp.getAlias();
          return quoteNodeTableTmp;
        }
      }
    }
  }

  /**
   * 关联多语表
   *
   * @param tenantId
   * @param endNodeTable
   * @param aliasMapper        表别名cache
   * @param quoteOfAgg
   */
  public void joinLangNodeTable(String tenantId,
                                NodeTable endNodeTable,
                                Map<String, AtomicInteger> aliasMapper,
                                QuoteOfAgg quoteOfAgg,
                                boolean standalone) {
    String endApiName = endNodeTable.getObjectDescribeApiName();
    JoinRelation joinRelation = quoteOfAgg.joinRelation;
    if (joinRelation == null) {
      NodeJoin langNodeJoin = this.createLangNodeTablePlus(endApiName, quoteOfAgg, standalone, tenantId);
      endNodeTable.addJoinSetSingle(aliasMapper, langNodeJoin, true);
    } else {
      //字段是查找关联其他对象的字段暂不支持多语
      //判断被引用字段是否是扩展对象上的字段暂不支持多语
      log.warn("lookUp or quote field is not support lang, tenantId:{}, endApiName:{}, joinRelation:{}", tenantId,
        endApiName, JSON.toJSONString(joinRelation));
    }
  }

  /**
   * 通用获取字段的udf_obj_field 信息
   *
   * @param tenantId
   * @param dbObjName
   * @param dbFieldName
   * @return
   */
  public Map<String, Object> findFieldInfoByFieldName(String tenantId, String dbObjName, String dbFieldName) {
    return udfObjFieldMapper.setTenantId(tenantId)
                            .findFieldInfoByFieldName(tenantId, dbObjName, mappingService.getApiXName(dbObjName),
                              dbFieldName);
  }

  /**
   * 通用获取字段
   *
   * @param tenantId    租户id
   * @param dbObjName   BI dbObjName
   * @param dbFieldName BI 字段名称
   * @return {@link UdfObjFieldDO}
   */
  public UdfObjFieldDO findUdfObjFieldDOByFieldName(String tenantId, String dbObjName, String dbFieldName) {
    return udfObjFieldMapper.setTenantId(tenantId)
                            .findUdfObjFieldDOByFieldName(tenantId, dbObjName, mappingService.getApiXName(dbObjName),
                              dbFieldName);
  }

  /**
   * 通用槽位获取字段的udf_obj_field 信息
   *
   * @param tenantId      租户id
   * @param dbObjName     对象apiName
   * @param fieldLocation 槽位
   * @return
   */
  public Map<String, Object> findFieldInfoByFieldLocation(String tenantId, String dbObjName, int fieldLocation) {
    return udfObjFieldMapper.setTenantId(tenantId)
                            .findFieldInfoByFieldLocation(tenantId, dbObjName, mappingService.getApiXName(dbObjName),
                              fieldLocation);
  }

  public UdfObjFieldDO findUdfObjFieldByFieldLocation(String tenantId, String dbObjName, int fieldLocation) {
    return udfObjFieldMapper.setTenantId(tenantId)
            .findUdfObjFieldByFieldLocation(tenantId, dbObjName, mappingService.getApiXName(dbObjName),
                    fieldLocation);
  }

  /**
   * 创建指标表对象
   *
   * @param standalone  是否schema隔离
   * @param apiName     指标表对象apiName
   * @param aliasMapper 别名缓存
   * @return NodeTable
   */
  public NodeTable createAggNodeTable(boolean standalone, String apiName, Map<String, AtomicInteger> aliasMapper) {
    String tableName = Constants.table(apiName, null, standalone);
    String alias = TopologyUtils.createAlias(aliasMapper, tableName);
    return NodeTable.of(tableName, alias, Lists.newArrayList(), Sets.newTreeSet(), apiName, null, false, null, null);
  }

  /**
   * 没有quote字段，通过查找关联查找被引用对象的字段
   *
   * @param tenantId           租户ID
   * @param sourceApiName      引用对象
   * @param targetApiName      被引用对象
   * @param referenceFieldName 查找关联字段
   * @param targetFieldName    引用字段
   * @return
   */
  public QuoteOfAgg<Object> buildFromQuote(String tenantId,
                                           boolean standalone,
                                           String sourceApiName,
                                           String targetApiName,
                                           String referenceFieldName,
                                           String targetFieldName,
                                           Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    UdfObjFieldDO lookUpField;
    if (Utils.isSlotColumn(referenceFieldName)) {
      lookUpField = this.findUdfObjFieldByFieldLocation(tenantId, sourceApiName, Integer.parseInt(referenceFieldName.substring(5)));
    } else {
      lookUpField = this.findUdfObjFieldDOByFieldName(tenantId, sourceApiName, referenceFieldName);
    }
    JoinRelation relation = this.createRelation(tenantId, standalone, sourceApiName, lookUpField, targetApiName, cachedTableDefinitions, AggJoinType.LEFT);
    if (lookUpField != null) {
      if (FieldType.GROUP.equals(lookUpField.getType()) && "target_data_id".equals(targetFieldName)) {
        return QuoteOfAgg.builder()
                         .joinRelation(relation)
                         .column("target_data_id")
                         .columnType(PGColumnType.String)
                         .isSingle(true)
                         .fieldType(FieldType.TEXT)
                         .build();
      }
    }
    QuoteTargetField quoteTargetField = this.buildQuoteField(tenantId, standalone, targetApiName, targetFieldName,
      cachedTableDefinitions);
    if (quoteTargetField != null) {
      return QuoteOfAgg.builder()
                       .joinRelation(relation)
                       .column(quoteTargetField.column)
                       .columnType(quoteTargetField.columnType)
                       .isSingle(quoteTargetField.isSingle)
                       .fieldType(quoteTargetField.fieldType)
                       .formatStr(quoteTargetField.formatStr)
                       .build();
    }
    return null;
  }

  /**
   * 构建一个关联关系，包含引用关系，what关系，对象和字段关系
   *
   * @param tenantId               租户id
   * @param standalone             是否是schame隔离
   * @param objectApiName          对象apiName
   * @param fieldApiName           字段apiName
   * @param cachedTableDefinitions 字段类型
   * @return {@link QuoteOfAgg}
   */
  public QuoteOfAgg<Object> buildQuoteOfAggIfAnyRelation(String tenantId,
                                                         boolean standalone,
                                                         String objectApiName,
                                                         String fieldApiName,
                                                         Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    UdfObjFieldDO lookupField = udfObjFieldMapper.setTenantId(tenantId)
                                                 .findUdfObjFieldDOByFieldName(tenantId, objectApiName,
                                                   mappingService.getApiXName(objectApiName), fieldApiName);
    Preconditions.checkNotNull(lookupField, String.format("tenantId:%s,sourceApiName:%s,lookupField is null",
      tenantId, objectApiName));
    String type = lookupField.getType();
    switch (type) {
      case FieldType.GROUP -> {
        String relationTable = lookupField.getRelationTable();
        if (StringUtils.isNotBlank(relationTable)) {
          JoinRelation joinRelation = JoinRelation.builder()
                                                  .column(Constants.getIdName(standalone, objectApiName))
                                                  .apiName(relationTable)
                                                  .joinType(AggJoinType.LEFT)
                                                  .fieldType(type)
                                                  .columnType(PGColumnType.String)
                                                  .build();
          return QuoteOfAgg.builder()
                           .joinRelation(joinRelation)
                           .column("target_data_id")
                           .columnType(PGColumnType.String)
                           .isSingle(true)
                           .fieldType(FieldType.TEXT)
                           .build();
        }
      }
      case FieldType.QUOTE -> {
        String referenceFieldName = lookupField.getRelationKeyField();
        String targetFieldName = lookupField.getRefTargetField();
        Map<String, Object> referenceField = null;
        if (Utils.isSlotColumn(referenceFieldName)) {
          referenceField = this.findFieldInfoByFieldLocation(tenantId, objectApiName,
            Integer.parseInt(referenceFieldName.substring(5)));
        } else {
          //查找引用列
          referenceField = this.findFieldInfoByFieldName(tenantId, objectApiName, referenceFieldName);
        }
        if (null == referenceField) {
          return null;
        }
        JSONObject referenceFieldInfo = new JSONObject(referenceField);
        String refColumn = referenceFieldName;
        Integer fieldLocation = referenceFieldInfo.getInteger("field_location");
        if (fieldLocation != null && fieldLocation > 0) {
          refColumn = "value" + fieldLocation;
        }
        String fieldType = referenceFieldInfo.getString("type");
        String table = objectApiName;
        if (table.endsWith("__c") && !standalone) {
          table = "object_data";
        }
        String refObjName = mappingService.udfApiName2ApiName(referenceFieldInfo.getString("ref_obj_name"));
        PGColumnType columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions, refColumn,
          fieldType);
        JoinRelation joinRelation = JoinRelation.builder()
                                                .joinType(AggJoinType.LEFT)
                                                .fieldType(fieldType)
                                                .apiName(refObjName)
                                                .column(refColumn)
                                                .columnType(columnType)
                                                .build();
        QuoteTargetField quoteTargetField = this.buildQuoteField(tenantId, standalone, refObjName, targetFieldName,
          cachedTableDefinitions);
        if (quoteTargetField != null) {
          return QuoteOfAgg.builder()
                           .joinRelation(joinRelation)
                           .column(quoteTargetField.column)
                           .columnType(quoteTargetField.columnType)
                           .isSingle(quoteTargetField.isSingle)
                           .fieldType(quoteTargetField.fieldType)
                           .build();
        }
      }
      default -> {
        String refObjName = lookupField.getRefObjName();
        if (StringUtils.isNotBlank(refObjName)) {
          String refColumn = lookupField.getDbFieldName();
          int fieldLocation = Integer.parseInt(lookupField.getFieldLocation());
          if (fieldLocation > 0) {
            refColumn = "value" + fieldLocation;
          }
          String table = objectApiName;
          if (table.endsWith("__c") && !standalone) {
            table = "object_data";
          }
          PGColumnType columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions,
            refColumn, type);
          JoinRelation joinRelation = JoinRelation.builder()
                                                  .joinType(AggJoinType.LEFT)
                                                  .fieldType(type)
                                                  .apiName(refObjName)
                                                  .column(refColumn)
                                                  .columnType(columnType)
                                                  .build();
          String refKeyField = Constants.getIdName(standalone, refObjName);
          if (GrayManager.isAllowByRule("goal_where_ref_key_field_eis", tenantId) &&
            StringUtils.isNotBlank(lookupField.getRefKeyField()) && !refObjName.endsWith("__c")) {
            //描述测这个字段自定义对象都是value0,不识别是否隔离,所以自定义对象不用描述上的值
            refKeyField = lookupField.getRefKeyField();
          }
          return QuoteOfAgg.builder()
                           .joinRelation(joinRelation)
                           .column(refKeyField)
                           .columnType(PGColumnType.String)
                           .isSingle(true)
                           .fieldType(FieldType.TEXT)
                           .build();
        } else {
          QuoteTargetField quoteTargetField = this.buildQuteTargetByUdfObjField(tenantId, standalone, objectApiName,
            lookupField, cachedTableDefinitions);
          return QuoteOfAgg.builder()
                           .joinRelation(null)
                           .column(quoteTargetField.column)
                           .columnType(quoteTargetField.columnType)
                           .fieldType(quoteTargetField.fieldType)
                           .isSingle(quoteTargetField.isSingle)
                           .build();
        }
      }
    }
    return null;
  }

  /**
   * 封装字段对象
   *
   * @param tenantId               租户id
   * @param standalone             是否schema隔离
   * @param targetApiName          apiName
   * @param targetFieldName        fieldName
   * @param cachedTableDefinitions 字段类型cache
   * @return {@link QuoteTargetField}
   */
  public QuoteTargetField buildQuoteField(String tenantId,
                                          boolean standalone,
                                          String targetApiName,
                                          String targetFieldName,
                                          Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    UdfObjFieldDO quoteField = this.findUdfObjFieldDOByFieldName(tenantId, targetApiName, targetFieldName);
    if (quoteField == null) {
      return null;
    }
    return this.buildQuteTargetByUdfObjField(tenantId, standalone, targetApiName, quoteField, cachedTableDefinitions);
  }


  public QuoteTargetField buildQuteTargetByUdfObjField(String tenantId,
                                                       boolean standalone,
                                                       String apiName,
                                                       UdfObjFieldDO lookupField,
                                                       Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    String dbColumn = lookupField.getDbFieldName();
    //加载一下，看看是否是槽位列
    int dbFieldLocation = Integer.parseInt(lookupField.getFieldLocation());
    if (dbFieldLocation > 0) {
      dbColumn = "value" + dbFieldLocation;
    }
    PGColumnType dbColumnType;
    String dbFieldType = lookupField.getType();
    if (StringUtils.equalsAny(dbFieldType, "formula", "count") && GrayManager.isAllowByRule("dim_rule_return_type", tenantId)) {
      String udfFieldType = StringUtils.isNotBlank(lookupField.getReturnType()) ? lookupField.getReturnType() : lookupField.getFieldType();
      dbFieldType = StringUtils.isBlank(udfFieldType) ? FieldType.TEXT : udfFieldType.toLowerCase();
    }
    //员工部门的引用需要单独处理一下
    boolean isSingle = this.isSingleValue(dbFieldType, dbColumn, lookupField.getIsSingle());
    if (apiName.equals("active_record") && "related_api_names".equalsIgnoreCase(dbColumn)) {
      dbColumnType = PGColumnType.ARRAY_String;
    } else {
      String table = apiName;
      if (table.endsWith("__c") && !standalone) {
        table = "object_data";
      }
      if (FieldType.GROUP.equals(lookupField.getType()) && Constants.RELATED_OBJECT.equals(dbColumn) &&
        StringUtils.isNotBlank(lookupField.getWhatIdField())) {
        dbColumn = lookupField.getWhatIdField();
      }
      Map<String, ColumnDefinition> tableColumnDefinitions = findTableColumnDefinitions(tenantId, standalone, table, cachedTableDefinitions);
      ColumnDefinition definition = tableColumnDefinitions.get(dbColumn);
      if (definition == null) {
        dbColumnType = PGColumnType.String;
      } else {
        dbColumnType = definition.columnType();
      }
    }
    return QuoteTargetField.builder()
                           .fieldType(dbFieldType)
                           .columnType(dbColumnType)
                           .column(dbColumn)
                           .isSingle(isSingle)
                           .formatStr(lookupField.getFormatStr())
                           .build();
  }


  /**
   * 判断表列字段类型
   *
   * @param tenantId
   * @param standalone
   * @param table
   * @param cachedTableDefinitions
   * @param column
   * @param fieldType
   * @return
   */
  public PGColumnType findColumnType(String tenantId,
                                     boolean standalone,
                                     String table,
                                     Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                                     String column,
                                     String fieldType) {
    Map<String, ColumnDefinition> columnDefinitions = findTableColumnDefinitions(tenantId, standalone, table,
      cachedTableDefinitions);
    PGColumnType columnType;
    ColumnDefinition columnDefinition = columnDefinitions.get(column);
    boolean columnTypeFromDB = false;
    if (columnDefinition != null) {
      columnTypeFromDB = true;
      columnType = columnDefinition.columnType();
    } else {
      columnType = PGColumnType.String;
    }
    if (!columnTypeFromDB) {
      if (FieldType.NUMBER_FIELD_TYPES.contains(fieldType)) {
        columnType = PGColumnType.Number;
        if (!standalone) {
          if (Utils.isSlotColumn(column)) {
            columnType = PGColumnType.String;
          }
        }
      }
    }
    return columnType;
  }

  /**
   * 表定义
   *
   * @param tenantId
   * @param standalone
   * @param table
   * @return
   */
  public Map<String, ColumnDefinition> findTableColumnDefinitions(String tenantId,
                                                                  boolean standalone,
                                                                  String table,
                                                                  Map<String, Map<String, ColumnDefinition>> cached) {
    Map<String, ColumnDefinition> result = cached.get(table);
    if (null != result) {
      return result;
    }

    result = Maps.newHashMap();
    String ns = standalone ? "sch_" + tenantId : "public";
    List<Map> tableColumnDefine = customStatisticAggFieldMapper.setTenantId(tenantId)
                                                               .findTableColumnDefine(tenantId, ns,
                                                                 table.toLowerCase());
    //刷系统库预制报表用，只刷一次。系统库没有业务表，所以找个sch隔离企业代替
    if ("-1".equals(tenantId)) {
      tableColumnDefine = customStatisticAggFieldMapper.setTenantId(biSystemTableCopySchTenantId)
                                                       .findTableColumnDefine(biSystemTableCopySchTenantId, "sch_"+biSystemTableCopySchTenantId,
                                                          table.toLowerCase());
    }
    for (Map m : tableColumnDefine) {
      String columName = m.get("column_name").toString();
      String typeName = m.get("type_name").toString();
      result.put(columName, new ColumnDefinition(columName, typeName));
    }
    cached.put(table, result);
    return result;
  }

  public boolean isSingleValue(String fieldType, String column, Integer isSingle) {
    if (FieldType.MULTI_VALUE_FIELD_TYPES.contains(fieldType)) {
      return false;
    }
    if (FieldType.EMP_DEPT.contains(fieldType)) {
      //      if (column.startsWith("value")) {
      //        return false;
      //      }
      if (isSingle != null && isSingle == 0) {
        return false;
      }
    }
    return true;
  }

  /**
   * 增加ObjectId对象lookup字段不为空的条件
   *
   * @param statRule 拓扑图
   */
  public void addLookupNotNullFilter(TopologyTableAggRule statRule, boolean standalone) {
    WhereConfig lookupNotNullWhereConfig = this.findLookupNotNullFilter(statRule.getRootNodeTable(), standalone);
    if (lookupNotNullWhereConfig != null) {
      statRule.getPreWhereConfigList().add(lookupNotNullWhereConfig);
    }
  }

  /**
   * 增加ObjectId对象lookup字段不为空的条件
   *
   * @param nodeTable 节点表
   */
  public WhereConfig findLookupNotNullFilter(NodeTable nodeTable, boolean standalone) {
    Set<NodeJoin> joinSet = nodeTable.getJoinSet();
    if (CollectionUtils.isNotEmpty(joinSet)) {
      for (NodeJoin nodeJoin : joinSet) {
        NodeTable joinTable = nodeJoin.getTable();
        if (joinTable.isObjectIdTable() && StringUtils.isNotBlank(joinTable.getLookupColumn())) {
          return WhereConfig.builder()
                            .whereExpr(joinTable.getAlias() + "." +
                              Constants.getIdName(standalone, joinTable.getObjectDescribeApiName()) + " IS NOT NULL ")
                            .build();
        } else {
          WhereConfig config = this.findLookupNotNullFilter(joinTable, standalone);
          if (config != null) {
            return config;
          }
        }
      }
    }
    return null;
  }

  /**
   * 判断多值
   *
   * @param dimRules
   * @return
   */
  public boolean isMultipleValue(List<DimRule> dimRules) {
    if (CollectionUtils.isNotEmpty(dimRules)) {
      Optional<DimRule> multipleDimOp = dimRules.stream()
                                                .filter(dimRule ->
                                                  DisplayField.DisplayType.group == dimRule.findDisplayType() &&
                                                    dimRule.isMultipleDim())
                                                .findAny();
      return multipleDimOp.isPresent();
    }
    return false;
  }

  /**
   * 反查所有作废的dim字段
   *
   * @param tenantId 租户id
   * @param schemaId 主题id
   * @return
   */
  public List<StatFieldDO> queryUnusedDmiFields(String tenantId, String schemaId) {
    return statFieldMapper.setTenantId(tenantId)
                          .queryFieldBaseBySchemaId(tenantId, schemaId, new String[] {"dim"},
                            new int[] {StatFieldStatusEnum.unable.getCode(),
                              StatFieldStatusEnum.System_unable.getCode(), StatFieldStatusEnum.Reservation.getCode()});
  }

  public List<String> batchQueryGoalRuleByEi(String tenantId, int sourceType) {
    List<String> goalRuleIdList = goalRuleMapper.setTenantId(tenantId).getAllGoalRuleIdByEi(tenantId);
    if (CollectionUtils.isEmpty(goalRuleIdList)) {
      log.warn("batchQueryGoalRuleByEi is empty, tenantId:{}", tenantId);
      return goalRuleIdList;
    }
    return biMtTopologyDescribeMapper.setTenantId(tenantId)
            .findGoalRuleIdByEi(tenantId, goalRuleIdList.toArray(String[]::new), sourceType);
  }

  /**
   * 查询目标规则
   *
   * @param tenantId
   * @param sourceId
   * @return
   */
  public GoalRuleDO findGoalRuleById(String tenantId, String sourceId) {
    String[] goalRuleIdStr = sourceId.split("\\|");
    GoalRuleDO goalRuleDO = goalRuleMapper.setTenantId(tenantId).getRuleEntityById(tenantId, goalRuleIdStr[0]);
    if (goalRuleDO == null) {
      log.warn("can not find goalRule tenantId:{},goalRuleId:{}", tenantId, sourceId);
    }
    return goalRuleDO;
  }

}
