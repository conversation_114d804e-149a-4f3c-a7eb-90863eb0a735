package com.fxiaoke.bi.warehouse.ods.integrate.service.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.ods.bean.PgSysModifiedTimeInfo;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.Biz2CHConsumer;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.model.BIAggSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.model.TopologyTableIntegrateBO;
import com.fxiaoke.bi.warehouse.core.db.BiAggSyncInfoDao;
import com.fxiaoke.bi.warehouse.ods.integrate.service.*;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncTableStatusEnum;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.ods.service.DbTableSyncInfoService;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @Author:jief
 * @Date:2024/4/1
 */
@Slf4j
@Service
public class IntegrateServiceImpl implements IIntegrateService {
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  @Resource
  CHMetadataService chMetadataService;
  @Resource
  CHDataSource chDataSource;
  @Resource
  CHDBService chdbService;
  @Resource
  PgCommonDao pgCommonDao;
  @Resource
  BiAggSyncInfoDao biAggSyncInfoDao;
  @Resource
  BiMtTopologyTableService biMtTopologyTableService;
  @Resource
  private AggDownStreamService aggDownStreamService;
  @Resource
  private DbTableSyncInfoService dbTableSyncInfoService;

  @Resource
  private DataSyncLicenseService dataSyncLicenseService;

  @Resource
  private BiDataSyncPolicyService biDataSyncPolicyService;
  private static final Set<String> initFlag = Sets.newConcurrentHashSet();
  private long syncDelayThreshold;
  private int chReadTimeOut;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
      //同步延迟超1小时就开始报error日志过
      syncDelayThreshold = config.getInt("sync_db_delay_threshold", 1000 * 60 * 30);
      chReadTimeOut = config.getInt("ch_socket_time_out", 1000 * 60 * 30);
    });
  }

  /**
   * 1+N 支持同步和计算解耦
   * @param nextBatchNum
   * @param dbSyncInfoCopy
   * @param dbTableSyncInfoMap
   * @param partitionName
   */
  public void executeIntegrateDataPlus(AtomicLong nextBatchNum,
                                       DBSyncInfoBO dbSyncInfoCopy,
                                       Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                       String partitionName) {
    String schemaName = dbSyncInfoCopy.getPgSchema();
    if (StringUtils.isBlank(schemaName) || Objects.equals(schemaName, "public")) {
      log.warn("executeIntegrateDataPlus agg data no support this db:{},chDB:{}", dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getChDb());
      return;
    }
    if (schemaName.startsWith("sch_") && (dataSyncLicenseService.checkoutDataSyncLicense(schemaName.substring(4)) || GrayManager.isAllowByRule("agg_downstream_data_eis", schemaName.substring(4)))) {
      String tenantId = schemaName.substring(4);
      long minuteOfDay = LocalTime.now().getLong(ChronoField.MINUTE_OF_DAY);
      if ((minuteOfDay >= WarehouseConfig.integrateBeginTime && minuteOfDay <= WarehouseConfig.integrateEndTime)) {
        long lastIntegrateTime = dbSyncInfoCopy.getLastIntegrateTime();
        LocalDate lastMergeDate = new Timestamp(lastIntegrateTime).toLocalDateTime().toLocalDate();
        LocalDate localDate = LocalDate.now();
        boolean bAfter = WarehouseConfig.allowIntegrate || localDate.isAfter(lastMergeDate);
        if (bAfter) {
          try {
            this.doTranslateNew(nextBatchNum, tenantId, System.currentTimeMillis(), dbTableSyncInfoMap, partitionName, dbSyncInfoCopy);
          } catch (Exception e) {
            throw new RuntimeException(String.format("consumeIntegrateEvent error :%s", tenantId), e);
          }
        } else {
          log.info("executeIntegrateDataPlus canMerge Today:{}, lastMergeDate:{}, LocalDate:{},{},{}", bAfter, lastMergeDate, localDate, dbSyncInfoCopy.getChDb(), dbSyncInfoCopy.getPgDb());

        }
      }
    }
  }

  /**
   * 同步1+N相关的表
   * @param nextBatchNum
   * @param tenantId
   * @param start
   * @param dbTableSyncInfoMap
   * @param partitionName
   * @param dbSyncInfoCopy
   */
  public void doTranslateNew(AtomicLong nextBatchNum,
                                     String  tenantId,
                                     long start,
                                     Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                     String partitionName,
                                     DBSyncInfoBO dbSyncInfoCopy) {
    List<TopologyTableIntegrateBO> topologyTableIntegrateBOS = biMtTopologyTableService.queryAllDownstreamTopologyTables(tenantId);
    Set<String> tables = biMtTopologyTableService.queryAllDownstreamTables(topologyTableIntegrateBOS);
    if (CollectionUtils.isNotEmpty(tables)) {
      tables.forEach(tableName -> {
        String toTable = String.format(tableName + "_%s", CHContext.DOWNSTREAM);
        this.syncBizDownstreamNew(nextBatchNum, tenantId, tableName, toTable,dbTableSyncInfoMap,partitionName,dbSyncInfoCopy);
      });
    }
    if (GrayManager.isAllowByRule("biDataSyncPolicyGray", tenantId) || dataSyncLicenseService.checkoutDataSyncLicense(tenantId)) {
      biDataSyncPolicyService.synchronizeDownstreamAggDataNew(tenantId, nextBatchNum.get(), dbSyncInfoCopy,partitionName);
    } else {
      aggDownStreamService.synchronizeDownstreamAggDataNew(tenantId, nextBatchNum.get(),dbSyncInfoCopy,partitionName);
    }
    log.info("downstream sync success tenantId:{},cost:{}", tenantId, System.currentTimeMillis() - start);
  }
  /**
   * 开始同步明细表，全量或者增量同步
   *
   * @param tenantId
   * @param fromTable
   * @param toTable
   */
  public void syncBizDownstreamNew(AtomicLong nextBatchNum,
                                String tenantId,
                                String fromTable,
                                String toTable,
                                Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                String partitionName,
                                DBSyncInfoBO dbSyncInfoCopy) {
    StopWatch stopWatch = StopWatch.createStarted("transfer upstream:" + tenantId + ":" + toTable);
    String chJdbcUrl = chDataSource.findChURLByEi(tenantId);
    DbTableSyncInfoDO dbTableSyncInfo = dbTableSyncInfoMap.get(toTable);
    boolean offline = false;
    if (dbTableSyncInfo == null) {
      List<DbTableSyncInfoDO> dbTableSyncInfos = dbTableSyncInfoService.queryDbTableSyncInfosBySyncId(dbSyncInfoCopy.getPgDb(),dbSyncInfoCopy.getPgSchema(),dbSyncInfoCopy.getId(), toTable);
      if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
        dbTableSyncInfo = dbTableSyncInfos.getFirst();
      } else {
        offline = true;
        dbTableSyncInfo = DbTableSyncInfoDO.createFrom(dbSyncInfoCopy, toTable);
      }
      dbTableSyncInfoMap.put(toTable, dbTableSyncInfo);
    }
    dbTableSyncInfo.setLastSyncTime(new Date().getTime());
    //检测表
    this.checkDownstreamTable(tenantId, fromTable);
    long nextBatchNumValue = nextBatchNum.get();
    if (dbTableSyncInfo.getBatchNum() >= nextBatchNumValue) {
      log.warn("syncBizDownstreamNew this table:{} has bean synced table batchNum:{}, batchNum:{}", dbTableSyncInfo.getTableName(), dbTableSyncInfo.getBatchNum(), nextBatchNumValue);
      return;
    }
    Map<String, Set<String>> apiNameEiMapper = Maps.newHashMap();
    Optional<ClickhouseTable> fromClickhouseTableOP = chMetadataService.loadTableFromDB(chJdbcUrl, fromTable);
    ClickhouseTable fromCHTable = fromClickhouseTableOP.orElseThrow(() -> new RuntimeException(
      "loadTableFromDB error" + chJdbcUrl + "tableName:" + fromTable));
    Optional<ClickhouseTable> toClickhouseTableOP = chMetadataService.loadTableFromDB(chJdbcUrl, toTable);
    ClickhouseTable toCHTable = toClickhouseTableOP.orElseThrow(() -> new RuntimeException(
      "loadTableFromDB error" + chJdbcUrl + "tableName:" + toTable));
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chJdbcUrl, 7200000L)) {
      Long startTimestamp = dbTableSyncInfo.getMaxSysModifiedTime();
      PgSysModifiedTimeInfo sysModifiedTimeInfo = chdbService.findMaxSysModifiedTimeByTable(jdbcConnection, fromCHTable, startTimestamp, stopWatch);
      dbTableSyncInfo.setMaxSysModifiedTime(Math.max(sysModifiedTimeInfo.getMaxModifiedTime(),startTimestamp==null?0L:startTimestamp));
      Pair<Long, Long> sysTimeRange = Pair.build(startTimestamp, sysModifiedTimeInfo.getMaxModifiedTime());
      int num = 0;
      if (offline) {
        String partValue = partitionName;
        String chDBName = CommonUtils.getDBName(chJdbcUrl);
        if (GrayManager.isAllowByRule("use_ch_ods_partition", chDBName) && toCHTable.existsPartitionKey() && Objects.equals(dbSyncInfoCopy.getAllowIncPartition(), WarehouseConfig.OPEN_INC_PARTITION)) {
          partValue = WarehouseConfig.STOCK_PARTITION_NAME;
        }
        num = chdbService.insertBizData(Lists.newArrayList(tenantId), fromCHTable, toCHTable, nextBatchNumValue, sysTimeRange, stopWatch, partValue);
      } else {
        if (sysModifiedTimeInfo.getMaxModifiedTime() > (startTimestamp == null ? 0L : startTimestamp) && StringUtils.isBlank(partitionName)) {
          chdbService.insertBeforeData(tenantId, fromCHTable, toCHTable, nextBatchNumValue, sysTimeRange, stopWatch, partitionName);
        }
        num = chdbService.insertBizData(Lists.newArrayList(tenantId), fromCHTable, toCHTable, nextBatchNumValue, sysTimeRange, stopWatch, partitionName);
      }
      if (num > 0) {
        apiNameEiMapper.put(fromTable, Sets.newHashSet(tenantId));
      }
      log.info("syncBizDownstream finish tenantId:{},batchNum:{},fromTable:{},toTable:{},offline:{},num:{}", tenantId, nextBatchNumValue, fromTable, toTable, offline, num);
    } catch (Exception e) {
      log.error("syncBizDownstreamNew error tenantId:{},fromTable:{},toTable:{}", tenantId, fromTable, toTable, e);
      throw new RuntimeException(String.format("syncBizDownstream error tenantId:%s,fromTable:%s,toTable:%s", tenantId, fromTable, toCHTable), e);
    }
    dbTableSyncInfo.setApiNameEiMap(JSON.toJSONString(apiNameEiMapper));
    dbTableSyncInfo.setLastModifiedTime(new Date().getTime());
    dbTableSyncInfo.setBatchNum(nextBatchNumValue);
    dbTableSyncInfo.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
    if (offline) {
      dbTableSyncInfoService.batchUpsertDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
    } else {
      dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
    }
    stopWatch.stop();
    //大于5秒的打印一下日志
    log.info("syncBizDownstreamNew tenantId:{},fromTable:{},toTable:{},result:{}", tenantId, fromTable, toTable, stopWatch.prettyPrint());
  }

  /**
   * 执行上下游同步
   * @param dbSyncInfoCopy
   */
  public BIAggSyncInfoDO executeIntegrateData(AtomicLong nextBatchNum,
                                              DBSyncInfoBO dbSyncInfoCopy,
                                              Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                              String partitionName) {
    String schemaName = dbSyncInfoCopy.getPgSchema();
    if (StringUtils.isBlank(schemaName) || Objects.equals(schemaName, "public")) {
      log.warn("integrate agg data no support this db:{},chDB:{}", dbSyncInfoCopy.getPgDb(),dbSyncInfoCopy.getChDb());
      return null;
    }
    if (schemaName.startsWith("sch_") && (dataSyncLicenseService.checkoutDataSyncLicense(schemaName.substring(4)) || GrayManager.isAllowByRule("agg_downstream_data_eis", schemaName.substring(4)))) {
      String tenantId = schemaName.substring(4);
      BIAggSyncInfoDO biAggSyncInfoDO = biAggSyncInfoDao.queryAggSyncByEi(tenantId);
      if (biAggSyncInfoDO == null) {
        log.warn("queryAggSyncByEi result is null tenantId:{}", tenantId);
        return null;
      }
      long minuteOfDay = LocalTime.now().getLong(ChronoField.MINUTE_OF_DAY);
      if ((minuteOfDay >= WarehouseConfig.integrateBeginTime && minuteOfDay <= WarehouseConfig.integrateEndTime)) {
        long lastSyncTime = biAggSyncInfoDO.getLastSyncTime();
        LocalDate lastMergeDate = new Timestamp(lastSyncTime).toLocalDateTime().toLocalDate();
        LocalDate localDate = LocalDate.now();
        boolean bAfter = WarehouseConfig.allowIntegrate || localDate.isAfter(lastMergeDate);
        if (bAfter) {
          try {
            // 同步下游agg数据
            this.consumeIntegrateEvent(biAggSyncInfoDO,dbSyncInfoCopy,dbTableSyncInfoMap,partitionName,nextBatchNum);
            return biAggSyncInfoDO;
          } catch (Exception e) {
            throw new RuntimeException(String.format("consumeIntegrateEvent error :%s", JSON.toJSONString(biAggSyncInfoDO)),e);
          }
        }
        log.info("hasMergedToday canMerge Today:{}, lastMergeDate:{}, LocalDate:{},{},{}", bAfter, lastMergeDate, localDate, dbSyncInfoCopy.getChDb(), dbSyncInfoCopy.getPgDb());
      }
    }
    return null;
  }

  @Override
  public void consumeIntegrateEvent(@NonNull BIAggSyncInfoDO biAggSyncInfoDO,
                                    DBSyncInfoBO dbSyncInfoCopy,
                                    Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                    String partitionName,
                                    AtomicLong nextBatchNum) {
    String tenantId = biAggSyncInfoDO.getTenantId();
    String transferKey = String.format("bi:%s:%s", "integrate", tenantId);
    try (JedisLock jedisLock = new JedisLock(jedisCmd, transferKey, 1000 * 60 * 20)) {
      if (jedisLock.tryLock()) {
        SyncStatusEnum statusEnum = SyncStatusEnum.createFromStatus(biAggSyncInfoDO.getStatus());
        switch (statusEnum) {
          // 如果是 SYNC_ING 两种情况1、服务意外中断。2、redis锁超时没有同步完另外的线程抢到锁
          case SYNC_ING -> {
            //如果是服务第一次启动并且同步状态是SYNC_ING 则需要修改状态继续同步
            if (!initFlag.contains(tenantId)) {
              initFlag.add(tenantId);
              log.info("system start so continue syncing");
            } else {
              if ((System.currentTimeMillis() - biAggSyncInfoDO.getLastSyncTime()) >= this.syncDelayThreshold) {
                log.error("this integrate event:{} is SYNC_ING waiting for SYNC_ING cost:{}",
                  JSON.toJSONString(biAggSyncInfoDO), (
                  System.currentTimeMillis() - biAggSyncInfoDO.getLastModifiedTime()));
              } else {
                log.warn("this integrate event:{} is SYNC_ING please wait next time ", JSON.toJSONString(biAggSyncInfoDO));
              }
              return;
            }
          }
          case SYNC_ED, SYNC_ABLE, SYNC_ERROR -> {
            biAggSyncInfoDO.setStatus(SyncStatusEnum.SYNC_ING.getStatus());
            biAggSyncInfoDO.setLastModifiedTime(System.currentTimeMillis());
            biAggSyncInfoDao.saveAggSyncInfo(Lists.newArrayList(biAggSyncInfoDO));
            initFlag.add(tenantId);
          }
        }
        this.doTranslate(nextBatchNum, biAggSyncInfoDO,System.currentTimeMillis(),dbTableSyncInfoMap,partitionName,dbSyncInfoCopy);
        initFlag.remove(tenantId);
      } else {
        log.warn("tryLock fail {}", JSON.toJSONString(biAggSyncInfoDO));
      }
    } catch (Exception e) {
      biAggSyncInfoDO.setStatus(SyncStatusEnum.SYNC_ERROR.getStatus());
      biAggSyncInfoDO.setLastModifiedTime(System.currentTimeMillis());
      try {
        biAggSyncInfoDao.saveAggSyncInfo(Lists.newArrayList(biAggSyncInfoDO));
      } catch (Exception e2) {
        log.error("batchUpsertDbSyncInfo error status:{},event:{}", SyncStatusEnum.SYNC_ERROR.getStatus(), JSON.toJSONString(biAggSyncInfoDO), e);
        //如果状态更新失败如：数据库crash掉了，删除initFlag，保证下次可以继续同步
        initFlag.remove(biAggSyncInfoDO.getTenantId());
      }
      throw new RuntimeException("consumeIntegrateEvent error",e);
    }
  }

  @Override
  public void transferByViewId(String tenantId, String viewId, long batchNum) {

  }

  @Override
  public ClickhouseTable createCHTable(String tenantId, String viewId) {
    return null;
  }

  @Override
  public Biz2CHConsumer createCHConsumer(String tenantId, String viewId) {
    return null;
  }

  @Override
  public List<String> findDownstreamEIs(String tenantId) {
    return null;
  }


  public BIAggSyncInfoDO doTranslate(AtomicLong nextBatchNum,
                                     final BIAggSyncInfoDO biAggSyncInfoDO,
                                     long startTime,
                                     Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                     String partitionName,
                                     DBSyncInfoBO dbSyncInfoCopy) {
    long start = System.currentTimeMillis();
    BIAggSyncInfoBO biAggSyncInfoBO = BIAggSyncInfoBO.from(biAggSyncInfoDO);
    List<TopologyTableIntegrateBO> topologyTableIntegrateBOS = biMtTopologyTableService.queryAllDownstreamTopologyTables(biAggSyncInfoDO.getTenantId());
    Set<String> tables = biMtTopologyTableService.queryAllDownstreamTables(topologyTableIntegrateBOS);
    if (CollectionUtils.isNotEmpty(tables)) {
      tables.forEach(tableName -> {
        String toTable = String.format(tableName + "_%s", CHContext.DOWNSTREAM);
        this.syncBizDownstream(nextBatchNum, biAggSyncInfoDO.getTenantId(), biAggSyncInfoBO, tableName, toTable,dbTableSyncInfoMap,partitionName,dbSyncInfoCopy);
      });
    }
    if (GrayManager.isAllowByRule("biDataSyncPolicyGray", biAggSyncInfoBO.getTenantId()) || dataSyncLicenseService.checkoutDataSyncLicense(biAggSyncInfoBO.getTenantId())) {
      biDataSyncPolicyService.synchronizeDownstreamAggData(biAggSyncInfoBO, nextBatchNum.get(), dbSyncInfoCopy,partitionName);
    } else {
      aggDownStreamService.synchronizeDownstreamAggData(biAggSyncInfoBO, nextBatchNum.get(),dbSyncInfoCopy);
    }
    //执行成功后更新db状态
    biAggSyncInfoDO.setStatus(SyncStatusEnum.SYNC_ED.getStatus());
    biAggSyncInfoDO.setLastModifiedTime(System.currentTimeMillis());
    biAggSyncInfoDO.setLastSyncTime(startTime);
    biAggSyncInfoDO.setBatchNum(nextBatchNum.get());
    biAggSyncInfoDao.saveAggSyncInfo(Lists.newArrayList(biAggSyncInfoDO));
    log.info("downstream sync success :{},cost:{}", JSON.toJSONString(biAggSyncInfoDO),
      System.currentTimeMillis() - start);
    return biAggSyncInfoDO;
  }


  /**
   * 开始同步明细表，全量或者增量同步
   *
   * @param tenantId
   * @param biAggSyncInfoBO
   * @param fromTable
   * @param toTable
   */
  public void syncBizDownstream(AtomicLong nextBatchNum,
                                String tenantId,
                                BIAggSyncInfoBO biAggSyncInfoBO,
                                String fromTable,
                                String toTable,
                                Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                String partitionName,
                                DBSyncInfoBO dbSyncInfoCopy) {
    StopWatch stopWatch = StopWatch.createStarted("transfer upstream:" + tenantId + ":" + toTable);
    String chJdbcUrl = chDataSource.findChURLByEi(tenantId);
    DbTableSyncInfoDO dbTableSyncInfo = dbTableSyncInfoMap.get(toTable);
    boolean offline = false;
    if (dbTableSyncInfo == null) {
      List<DbTableSyncInfoDO> dbTableSyncInfos = dbTableSyncInfoService.queryDbTableSyncInfosBySyncId(dbSyncInfoCopy.getPgDb(),dbSyncInfoCopy.getPgSchema(),biAggSyncInfoBO.getId(), toTable);
      if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
        dbTableSyncInfo = dbTableSyncInfos.getFirst();
      } else {
        offline = true;
        dbTableSyncInfo = DbTableSyncInfoDO.createFrom(biAggSyncInfoBO, toTable);
      }
      dbTableSyncInfoMap.put(toTable, dbTableSyncInfo);
    }
    dbTableSyncInfo.setLastSyncTime(new Date().getTime());
    //检测表
    this.checkDownstreamTable(tenantId, fromTable);
    long nextBatchNumValue = nextBatchNum.get();
    if (dbTableSyncInfo.getBatchNum() >= nextBatchNumValue) {
      log.warn("this table:{} has bean synced table batchNum:{}, batchNum:{}", dbTableSyncInfo.getTableName(), dbTableSyncInfo.getBatchNum(), nextBatchNumValue);
      return;
    }
    Map<String, Set<String>> apiNameEiMapper = Maps.newHashMap();
    Optional<ClickhouseTable> fromClickhouseTableOP = chMetadataService.loadTableFromDB(chJdbcUrl, fromTable);
    ClickhouseTable fromCHTable = fromClickhouseTableOP.orElseThrow(() -> new RuntimeException(
      "loadTableFromDB error" + chJdbcUrl + "tableName:" + fromTable));
    Optional<ClickhouseTable> toClickhouseTableOP = chMetadataService.loadTableFromDB(chJdbcUrl, toTable);
    ClickhouseTable toCHTable = toClickhouseTableOP.orElseThrow(() -> new RuntimeException(
      "loadTableFromDB error" + chJdbcUrl + "tableName:" + toTable));
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chJdbcUrl, 7200000L)) {
      Long startTimestamp = dbTableSyncInfo.getMaxSysModifiedTime();
      PgSysModifiedTimeInfo sysModifiedTimeInfo = chdbService.findMaxSysModifiedTimeByTable(jdbcConnection, fromCHTable, startTimestamp, stopWatch);
      dbTableSyncInfo.setMaxSysModifiedTime(Math.max(sysModifiedTimeInfo.getMaxModifiedTime(), startTimestamp == null ? 0L : startTimestamp));
      Pair<Long, Long> sysTimeRange = Pair.build(startTimestamp, sysModifiedTimeInfo.getMaxModifiedTime());
      if (!offline && sysModifiedTimeInfo.getMaxModifiedTime() > (startTimestamp == null ? 0L : startTimestamp)) {
        chdbService.insertBeforeData(tenantId, fromCHTable, toCHTable, nextBatchNumValue, sysTimeRange, stopWatch,partitionName);
      }
      int num = chdbService.insertBizData(Lists.newArrayList(tenantId), fromCHTable, toCHTable, nextBatchNumValue, sysTimeRange, stopWatch,partitionName);
      if (num > 0) {
        apiNameEiMapper.put(fromTable, Sets.newHashSet(tenantId));
      }
      log.info("syncBizDownstream finish tenantId:{},batchNum:{},fromTable:{},toTable:{},offline:{},num:{}", tenantId, nextBatchNumValue, fromTable, toTable, offline, num);
    } catch (Exception e) {
      log.error("syncBizDownstream error tenantId:{},fromTable:{},toTable:{}", tenantId, fromTable, toTable, e);
      throw new RuntimeException(String.format("syncBizDownstream error tenantId:%s,fromTable:%s,toTable:%s", tenantId, fromTable, toCHTable), e);
    }
    dbTableSyncInfo.setApiNameEiMap(JSON.toJSONString(apiNameEiMapper));
    dbTableSyncInfo.setLastModifiedTime(new Date().getTime());
    dbTableSyncInfo.setBatchNum(nextBatchNumValue);
    dbTableSyncInfo.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
    if (offline) {
      dbTableSyncInfoService.batchUpsertDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
    } else {
      dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
    }
    stopWatch.stop();
    log.info("syncBizDownstream tenantId:{},fromTable:{},toTable:{},result:{}", tenantId, fromTable, toTable, stopWatch.prettyPrint());
  }

  /**
   * 检测虚拟指标用到得表是否存在如果不存在需要新建
   * 如果存在需要对比表结构，需要保持一致
   *
   * @param tenantId     租户id
   * @param bizTableName 业务表名称
   */
  public void checkDownstreamTable(String tenantId, String bizTableName) {
    String downstreamTableName = String.format("%s_downstream", bizTableName);
    String jdbcURL = chDataSource.findChURLByEi(tenantId);
    boolean exists = chMetadataService.checkCHTableExists(jdbcURL, downstreamTableName);
    if (exists) {
      List<String> alterSQLs = chMetadataService.compareAndUpdateTable(jdbcURL, bizTableName, downstreamTableName);
      if (!alterSQLs.isEmpty()) {
        log.warn("compareAndUpdateTable result chDB:{} newTableName:{} need exec :{}", jdbcURL, downstreamTableName, JSON.toJSONString(alterSQLs));
        chdbService.executeUpsertOnCH(jdbcURL, alterSQLs.toArray(String[]::new));
      }
    } else {
      //如果表不存在需要创建新表
      chMetadataService.createFromExistsTable(jdbcURL, bizTableName, downstreamTableName);
    }
  }
}
