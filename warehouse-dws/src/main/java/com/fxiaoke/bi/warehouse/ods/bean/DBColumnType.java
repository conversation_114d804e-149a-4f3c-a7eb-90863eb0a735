package com.fxiaoke.bi.warehouse.ods.bean;

import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.context.CHDataType;
import com.fxiaoke.bi.warehouse.ods.context.PGDataType;
import com.fxiaoke.bi.warehouse.ods.context.PaasFieldType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import java.time.ZoneId;
import java.util.regex.Matcher;

@Data
public class DBColumnType {
  @Column(name = "ordinal_position")
  private int ordinalPosition;
  @Column(name = "column_name")
  private String columnName;
  @Column(name = "data_type")
  private String dataType;
  @Column(name = "udt_name")
  private String udtName;
  @Column(name = "is_nullable")
  private String isNullable;
  @Column(name = "character_maximum_length")
  private Integer characterMaximumLength;
  @Column(name = "numeric_precision")
  private Integer numericPrecision;
  @Column(name = "numeric_scale")
  private Integer numericScale;
  @Column(name = "datetime_precision")
  private Integer datetimePrecision;
  @Column(name = "format_type")
  private String formatType;
  @Column(name = "column_default")
  private String columnDefault;

  public DBColumnType trans2CHType(ZoneId zoneId, String paasType) {
    if (this.udtName.startsWith("_")) {
      this.udtName = this.trans2CHArray(this.formatType, zoneId, paasType);
    } else {
      this.udtName = this.trans2CHPrimaryDataType(this.numericScale, zoneId, paasType);
    }
    return this;
  }

  /**
   * 基本数据类型转化
   *
   * @param zoneId
   */
  private String trans2CHPrimaryDataType(Integer numericScale, ZoneId zoneId, String paasType) {
    switch (this.udtName) {
      case PGDataType.JSON:
      case PGDataType.JSONB:
      case PGDataType.LSEQ:
      case PGDataType.PATH:
      case PGDataType.LINE:
      case PGDataType.BIT:
      case PGDataType.VARBIT:
      case PGDataType.TEXT:
      case PGDataType.VARCHAR:
      case PGDataType.BPCHAR: {
        if (PaasFieldType.LowCardinalityTypes.contains(paasType)) {
          return CHDataType.LowCardinality + "(" + CHDataType.STRING + ")";
        }
        return CHDataType.STRING;
      }
      case PGDataType.BOOL: {
        if (CHContext.NO.equalsIgnoreCase(this.getIsNullable()) || PGDataType.NOT_NULL_FIELDS.contains(this.columnName)) {
          return CHDataType.Bool;
        }
        return CHDataType.Nullable + "(" +CHDataType.Bool+ ")";
      }
      case PGDataType.INT2: {
        if (CHContext.NO.equalsIgnoreCase(this.getIsNullable()) || PGDataType.NOT_NULL_FIELDS.contains(this.columnName)) {
          return CHDataType.Int16;
        }
        return CHDataType.Nullable + "(" + CHDataType.Int16 + ")";
      }
      case PGDataType.INT4: {
        if (CHContext.NO.equalsIgnoreCase(this.getIsNullable()) || PGDataType.NOT_NULL_FIELDS.contains(this.columnName)) {
          return CHDataType.Int32;
        }
        return CHDataType.Nullable + "(" + CHDataType.Int32 + ")";
      }
      case PGDataType.INT8: {
        if (CHContext.NO.equalsIgnoreCase(this.getIsNullable()) || PGDataType.NOT_NULL_FIELDS.contains(this.columnName)) {
          return CHDataType.Int64;
        }
        return CHDataType.Nullable + "(" + CHDataType.Int64 + ")";
      }
      case PGDataType.FLOAT4:
      case PGDataType.FLOAT8:
      case PGDataType.NUMERIC: {
        if(StringUtils.equalsAny(paasType,"date","date_time")){
          return CHDataType.Int64;
        }
        if (CHContext.NO.equalsIgnoreCase(this.getIsNullable())) {
          return CHDataType.Decimal128 + "(" +
            (numericScale == null ? String.valueOf(CHContext.DEFAULT_SCALE) : numericScale) + ")";
        }
        return CHDataType.Nullable + "(" + CHDataType.Decimal128 + "(" +
          (numericScale == null ? String.valueOf(CHContext.DEFAULT_SCALE) : numericScale) + "))";
      }
      case PGDataType.LTREE: {
        return CHDataType.ARRAY + "(" + CHDataType.STRING + ")";
      }
      case PGDataType.TIMESTAMP:
      case PGDataType.TIMESTAMPTZ: {
        return CHDataType.DateTime64 + "(6,'" + zoneId.getId() + "')";
      }
      case PGDataType.DATE: {
        return CHDataType.Date32;
      }
      //gis
      case PGDataType.GEOGRAPHY:
      case PGDataType.POINT: {
        return CHDataType.Point;
      }
      default: {
        throw new RuntimeException(String.format("do not support this type:%s,column:%s", udtName, this.columnName));
      }
    }
  }

  /**
   * 数组类型转化
   */
  private String trans2CHArray(String formatType, ZoneId zoneId, String paasType) {
    if (PGDataType._NUMERIC.equalsIgnoreCase(this.udtName)) {
      Matcher matcher = CHContext.numericArrayRex.matcher(formatType);
      if (matcher.matches()) {
        String scala = matcher.group(2);
        if (StringUtils.isNotBlank(scala)) {
          this.setUdtName(PGDataType._NUMERIC.substring(1));
          String arrayT = this.trans2CHPrimaryDataType(Integer.parseInt(scala), zoneId, paasType);
          return CHDataType.ARRAY + "(" + arrayT + ")";
        }
      }
    }
    this.setUdtName(this.udtName.substring(1));
    String arrayT = this.trans2CHPrimaryDataType(CHContext.DEFAULT_SCALE, zoneId, paasType);
    return CHDataType.ARRAY + "(" + arrayT + ")";
  }

}
