package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum FilterTypeEnum {

    NULL,

    AGG("指标做数据范围", "agg_data", "base_agg");

    private String desc;

    private String dbObjName;

    private String aggDimType;

    public static FilterTypeEnum getFilterTypeEnum(FilterField filterField) {
        if (AGG.dbObjName.equals(filterField.getDbObjName()) && AGG.aggDimType.equals(filterField.getAggDimType())) {
            return AGG;
        }
        return NULL;
    }

    public static boolean isSupportFilterType(FilterTypeEnum filterTypeEnum) {
        return filterTypeEnum != NULL;
    }
}
