package com.fxiaoke.bi.warehouse.dws.agg.bean;

import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.helper.Pair;
import lombok.Data;

import java.util.List;

/**
 * @Author:jief
 * @Date:2024/8/8
 */
@Data
public class AggCalResult {
  private List<CalPrepare> calPrepares;


  @Data
  public static class CalPrepare{
    private String tableName;
    /**
     * 拷贝计算分区到存量分区
     */
    private String cal2StockAfter;
    /**
     * 基于计算分区生成before数据到存量分区
     */
    private String cal2StockBefore;
    /**
     * 增量分区拷贝after数据到计算分区
     */
    private String inc2CalAfter;

    public String findSQLByStatus(int status) {
      SyncStatusEnum syncStatusEnum = SyncStatusEnum.createFromStatus(status);
      if (SyncStatusEnum.INC_2_CAL_AFTER_ING == syncStatusEnum) {
        return this.inc2CalAfter;
      } else if (SyncStatusEnum.CAL_2_STOCK_BEFORE_ING == syncStatusEnum) {
        return this.cal2StockBefore;
      } else if (SyncStatusEnum.CAL_2_STOCK_AFTER_ING == syncStatusEnum) {
        return this.cal2StockAfter;
      }
      return "";
    }
    /**
     * 生成before和after sql
     */
    private Pair<String/*before*/, String/*after*/> insertSQL;
  }
}
