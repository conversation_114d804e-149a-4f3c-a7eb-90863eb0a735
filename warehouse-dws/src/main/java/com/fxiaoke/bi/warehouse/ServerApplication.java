package com.fxiaoke.bi.warehouse;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.mongo.MongoMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * <AUTHOR>
 * @since 2023/3/17
 */

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,
  MongoAutoConfiguration.class, MongoDataAutoConfiguration.class,
  MongoMetricsAutoConfiguration.class, TransactionAutoConfiguration.class})
public class ServerApplication extends SpringBootServletInitializer {
  public static void main(String[] args) {
    SpringApplication.run(ServerApplication.class, args);
  }

  @Override
  protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
    return application.sources(ServerApplication.class);
  }
}