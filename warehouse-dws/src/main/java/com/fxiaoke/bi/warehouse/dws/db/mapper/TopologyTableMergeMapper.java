package com.fxiaoke.bi.warehouse.dws.db.mapper;

import com.fxiaoke.bi.warehouse.common.provider.CommonProvider;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableMergeDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.provider.CASProvider;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/5/3
 */
@Mapper
public interface TopologyTableMergeMapper extends ICrudMapper<TopologyTableMergeDO>, ITenant<TopologyTableMergeMapper> {

  @InsertProvider(type = CommonProvider.class, method = "batchUpsert")
  int upsertTopologyMergeInfo(@Param(CommonProvider.FKey) List<TopologyTableMergeDO> list,
                              @Param(CommonProvider.SKey) Set<String> primaryKey);

  @UpdateProvider(type = CASProvider.class, method = "updateWithVersion")
  int upsertTopologyMergeWithVersion(@Param("version") int version,
                                     @Param("obj") TopologyTableMergeDO topologyTableMergeDO,
                                     @Param("primaryKeys") Map<String, String> pkMap);
  /**
   * 唯一约束冲突则跳过
   *
   * @param list
   * @param primaryKey
   * @return
   */
  @InsertProvider(type = CommonProvider.class, method = "batchUpsertOnConflictDoNothing")
  int insertTopologyTableMergeOnConflictDoNothing(@Param(CommonProvider.FKey) List<TopologyTableMergeDO> list,
                                          @Param(CommonProvider.SKey) Set<String> primaryKey);
  /**
   * 更新状态,同时版本应向的累加
   * @param tenantId 租户id
   * @param id  id
   * @param status 状态
   * @param lastModifiedTime  最后更新时间
   * @return
   */
//  @Update(
//    "update bi_mt_topology_table_merge set status=#{status},last_modified_time=#{lastModifiedTime}," +
//      "batch_num=#{batchNum},latest_agg_time=#{maxModifiedTime} where " +
//      "tenant_id=#{tenantId} and id=#{id}")
  int updateTopologyTableMergeStatus(@Param("tenantId") String tenantId,
                                     @Param("id") String id,
                                     @Param("status") int status,
                                     @Param("lastModifiedTime") long lastModifiedTime,
                                     @Param("batchNum") long batchNum,
                                     @Param("maxModifiedTime") Long maxModifiedTime);

  int updateTopologyTableMergeByStatus(@Param("tenantId") String tenantId,
                                       @Param("id") String id,
                                       @Param("status") int status,
                                       @Param("lastModifiedTime") long lastModifiedTime,
                                       @Param("batchNum") long batchNum,
                                       @Param("maxModifiedTime") Long maxModifiedTime);

  int updateTopologyTableMergeStatusAndVersion(@Param("tenantId") String tenantId,
                                     @Param("id") String id,
                                     @Param("status") int status,
                                     @Param("lastModifiedTime") long lastModifiedTime,
                                     @Param("batchNum") long batchNum,
                                     @Param("maxModifiedTime") Long maxModifiedTime);

  /**
   * 插入或更新 topology_table_merge 表，如果是更新的话
   * 版本增加，batch_num 设置为0
   * @param topologyTableMergeDO
   * @return
   */
  @Insert("""
    INSERT INTO bi_mt_topology_table_merge(id, tenant_id, is_deleted, version, create_time, last_modified_time,
                                           status, latest_agg_time, batch_num)
    VALUES (#{id},#{tenantId},#{isDeleted},#{version},#{createTime},#{lastModifiedTime},#{status},#{latestAggTime},#{batchNum})
    ON CONFLICT(id,tenant_id) DO UPDATE SET version=bi_mt_topology_table_merge.version + 1,
                                            is_deleted=0,
                                            status=0,
                                            last_modified_time=EXCLUDED.last_modified_time
    WHERE bi_mt_topology_table_merge.is_deleted = 1 or bi_mt_topology_table_merge.status <> #{status}
    """)
  int upsertTopologyTableMerge(TopologyTableMergeDO topologyTableMergeDO);

  @Insert("""
    INSERT INTO bi_mt_topology_table_merge(id, tenant_id, is_deleted, version, create_time, last_modified_time,
                                           status, latest_agg_time, batch_num)
    VALUES (#{id},#{tenantId},#{isDeleted},#{version},#{createTime},#{lastModifiedTime},#{status},#{latestAggTime},#{batchNum})
    ON CONFLICT(id,tenant_id) DO UPDATE SET version=bi_mt_topology_table_merge.version + 1,
                                            is_deleted=EXCLUDED.is_deleted,
                                            status=EXCLUDED.status,
                                            last_modified_time=EXCLUDED.last_modified_time,
                                            latest_agg_time=EXCLUDED.latest_agg_time,
                                            batch_num=EXCLUDED.batch_num
    """)
  int initTopologyTableMergeOrIncVersion(TopologyTableMergeDO topologyTableMergeDO);

  /**
   * 根据id查询topology table merge 后的数据
   * @param tenantId
   * @return
   */
  @Delete("DELETE FROM bi_mt_topology_table_merge where tenant_id=#{tenantId}")
  int deleteTopologyMergeByEI(@Param("tenantId") String tenantId);

  /**
   * 批量删除topology table merge
   * @param tenantId 租户id
   * @param statViewUniqueKey unique key
   * @return
   */
  int batchDeleteTopologyTableMerge(@Param("tenantId") String tenantId,
                                    @Param("uniqueKey") String[] statViewUniqueKey);

  int batchDeleteTopologyTableMergeBySource(@Param("tenantId") String tenantId,
                                            @Param("source") Integer source);
}
