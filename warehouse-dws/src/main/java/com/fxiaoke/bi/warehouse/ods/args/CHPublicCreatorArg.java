package com.fxiaoke.bi.warehouse.ods.args;

import lombok.Data;

import java.util.List;

/**
 * @Author:jief
 * @Date:2023/11/10
 */
@Data
public class CHPublicCreatorArg {
  /**
   * 数据同步id
   */
  private List<String> dbSyncIds;
  /**
   * 创建表依赖租户元数据，如果是-1则表示是公共库
   */
  private String tenantId;
  private String pgDbUrl;
  private String chDBName;
  private String chCluster;
  private String tableEngine;
  private String schemaName;
  private boolean needCreate;
  /**
   * 只处理自定对象表
   */
  private boolean onlyCustomTable;
  private List<String> tables;
}
