package com.fxiaoke.bi.warehouse.ods.entity;

import com.alibaba.fastjson.JSON;
import com.clickhouse.client.ClickHouseClient;
import com.clickhouse.client.ClickHouseNode;
import com.clickhouse.client.ClickHouseResponse;
import com.clickhouse.data.ClickHouseDataStreamFactory;
import com.clickhouse.data.ClickHouseFormat;
import com.clickhouse.data.ClickHousePipedOutputStream;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.service.CHClientService;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.fxiaoke.bi.warehouse.ods.service.CHNodeService;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.concurrent.NotThreadSafe;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
@NotThreadSafe
public class Biz2CHConsumer {
  /**
   * clickhouse table name
   */
  @Getter
  protected final String table;
  //db url
  @Getter
  private final String dbUrl;
  private final String db;
  private final CHClientService chClientService;
  private final ClickhouseTable clickhouseTable;
  private final CHNodeService chNodeService;
  private final CHDBService chdbService;
  private final List<String> orderByFields;
  /**
   * 批量存储CH阈值
   */
  private final int savePointThreshold;
  /**
   * 批量反查
   */
  private final int batchQueryBeforeSize;
  /**
   * 批次号
   */
  @Getter
  private final long batchNum;
  private BizDataStreamWriter writer;
  /**
   * 同步ch计数器
   */
  private int counter;
  private final List<BizLog> bizLogCache = Lists.newArrayList();

  private Biz2CHConsumer(CHClientService chClientService,
                         ClickhouseTable clickhouseTable,
                         int savePointThreshold,
                         long batchNum,
                         CHNodeService chNodeService,
                         CHDBService chdbService,
                         List<String> orderByFields,
                         int batchQueryBeforeSize) {
    this.table = clickhouseTable.getName();
    this.dbUrl = clickhouseTable.getDbURL();
    this.db = CHContext.getDBName(clickhouseTable.getDbURL());
    this.chClientService = chClientService;
    this.clickhouseTable = clickhouseTable;
    this.savePointThreshold = savePointThreshold;
    this.chNodeService = chNodeService;
    this.batchNum = batchNum;
    this.chdbService = chdbService;
    this.orderByFields = orderByFields;
    this.batchQueryBeforeSize = batchQueryBeforeSize;
  }


  public static Biz2CHConsumer getInstance(CHClientService chClientService,
                                           ClickhouseTable clickhouseTable,
                                           int savePointThreshold,
                                           long batchNum,
                                           CHNodeService chNodeService,
                                           CHDBService chdbService,
                                           List<String> orderByFields,
                                           int batchQueryBeforeSize) {

    return new Biz2CHConsumer(chClientService, clickhouseTable, savePointThreshold, batchNum, chNodeService,
      chdbService, orderByFields, batchQueryBeforeSize);
  }

  /**
   * 获取数据写入队列或输出流
   *
   * @param doc     数据记录对象
   * @param offline 是否是离线全量重跑
   * @throws IOException
   */
  public void queue(BizLog doc, boolean offline, boolean needBefore) throws IOException {
    if (writer == null) {
      initStreamWriter();
    }
    if (!offline && needBefore) {
      this.bizLogCache.add(doc);
      int cacheSize = this.bizLogCache.size();
      if (cacheSize >= this.batchQueryBeforeSize) {
        this.queryBeforeData(this.cacheOrderByFieldValues(this.bizLogCache));
        this.writer.batchWrite(this.bizLogCache);
        this.bizLogCache.clear();
        if ((this.counter += cacheSize) >= this.savePointThreshold) {
          this.save();
        }
      }
    } else {
      this.writer.write(doc);
      if ((++this.counter) >= this.savePointThreshold) {
        this.save();
      }
    }
  }

  /**
   * 反查CH中历史数据作为before数据
   */
  public void queryBeforeData(List<String> orderByFieldValues) {
    if (!orderByFieldValues.isEmpty()) {
      this.chdbService.insertBeforeDataPlus(clickhouseTable, batchNum, this.orderByFields, orderByFieldValues);
    }
  }

  /**
   * 保存doc中order by 字段的值到缓存中，统一批次处理
   *
   * @param docs
   */
  private List<String> cacheOrderByFieldValues(List<BizLog> docs) {
    List<String> orderByFieldValues = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(this.orderByFields)) {
      docs.forEach(doc -> {
        StringBuilder fieldValueSB = new StringBuilder();
        orderByFields.forEach(field -> {
          if (CHContext.BI_SYS_FLAG.equals(field)) {
            fieldValueSB.append(CHContext.BI_SYS_FLAG_AFTER).append("^");
            return;
          }
          Object valueObj = doc.getValue(field);
          if (valueObj == null) {
            log.error("this order by field is null tableName:{},field:{},doc:{}", this.table, field, JSON.toJSON(doc));
          }
          fieldValueSB.append(valueObj).append("^");
        });
        orderByFieldValues.add(fieldValueSB.substring(0, fieldValueSB.length() - 1));
      });
    }
    return orderByFieldValues;
  }

  /**
   * 批量写入和flush
   *
   * @return
   */
  public int save() {
    //执行一次query before data数据
    if (!this.bizLogCache.isEmpty()) {
      this.queryBeforeData(this.cacheOrderByFieldValues(this.bizLogCache));
      this.writer.batchWrite(this.bizLogCache);
      this.bizLogCache.clear();
    }
    if (writer == null) {
      return -1;
    }
    try {
      return writer.save();
    } catch (IOException e) {
      throw new RuntimeException(e);
    } finally {
      writer.close();
      writer = null;
      this.counter = 0;
    }
  }

  /**
   * 判断clickhouse writer 是否需要关闭
   */
  public void closeIfNeed() {
    if (writer == null) {
      return;
    }
    writer.close();
    writer = null;
    this.counter = 0;
  }

  /**
   * 初始化一个写入流
   *
   * @throws IOException
   */
  private void initStreamWriter() {
    try {
      //找到一个节点
      ClickHouseNode node = chNodeService.findNode(this.dbUrl);
      ClickHouseClient client = chClientService.findClient(this.dbUrl);
      String tableName = table;
      if (!tableName.contains(".")) {
        tableName = this.db + ".`" + tableName + "`";
      }
      String insertSQL = null;
      if (Objects.equals(table, "agg_downstream_data")) {
        insertSQL = clickhouseTable.createInsertSQL4AggData();
      }
      BizDataStreamWriter newStreamWriter = new BizDataStreamWriter(tableName, clickhouseTable, client, node,
        insertSQL);
      newStreamWriter.init();
      this.writer = newStreamWriter;
      this.writer.writeHeader();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  public static class BizDataStreamWriter implements AutoCloseable {
    private final String table;
    private final ClickhouseTable clickhouseTable;
    private final ClickHouseClient client;
    private final ClickHouseNode node;
    private ClickHousePipedOutputStream outputStream;
    private CompletableFuture<ClickHouseResponse> futureResponse;
    private String insertSQL;

    private int count = 0;

    public BizDataStreamWriter(String table,
                               ClickhouseTable clickhouseTable,
                               ClickHouseClient client,
                               ClickHouseNode node,
                               String insertSQL) {
      this.table = table;
      this.clickhouseTable = clickhouseTable;
      this.client = client;
      this.node = node;
      this.insertSQL = insertSQL;
    }

    /**
     * 初始化writer
     */
    public void init() {
      this.outputStream = ClickHouseDataStreamFactory.getInstance().createPipedOutputStream(client.getConfig());
      //建立一个写入流
      if (StringUtils.isNotBlank(insertSQL)) {
        this.futureResponse = client.read(node)
                                    .write()
                                    .format(ClickHouseFormat.CSVWithNames)
                                    .query(this.insertSQL)
                                    .data(outputStream.getInputStream())
                                    .execute();
      }else{
        this.futureResponse = client.read(node)
                                    .write()
                                    .table(table)
                                    .format(ClickHouseFormat.CSVWithNames)
                                    .data(outputStream.getInputStream())
                                    .execute();
      }
    }
    /**
     * 写入表头
     *
     * @throws Exception
     */
    public void writeHeader() throws Exception {
      byte[] bytes = clickhouseTable.toCSVHeader();
      outputStream.write(bytes, 0, bytes.length);
      outputStream.write('\n');
    }

    /**
     * 批量写入
     *
     * @param bizLog
     */
    public void batchWrite(List<BizLog> bizLog) {
      bizLog.forEach(this::write);
    }

    public void write(BizLog bizLog) {
      try {
        byte[] bytes = clickhouseTable.toCSVLine(bizLog, CHContext.ETLTarget.CSV);
        outputStream.write(bytes, 0, bytes.length);
        outputStream.write('\n');
        count++;
      } catch (Exception e) {
        log.error("clickhouse db:{},table:{} outputStream write error", node.getBaseUri(), table, e);
        throw new RuntimeException(e);
      }
    }

    public void writeByte(byte[] bytes) {
      try {
        outputStream.write(bytes, 0, bytes.length);
        outputStream.write('\n');
        count++;
      } catch (Exception e) {
        log.error("clickhouse db:{},table:{} outputStream write error", node.getBaseUri(), table, e);
        throw new RuntimeException(e);
      }
    }

    @SneakyThrows
    private int save() throws IOException {
      if (outputStream == null) {
        log.error("outputStream is null before save table:{}", table);
        return -1;
      }
      outputStream.flush();
      outputStream.close();
      try {
        long s = System.currentTimeMillis();
        ClickHouseResponse response = futureResponse.get();
        long cost = System.currentTimeMillis() - s;
        int insertCount = (int) response.getSummary().getWrittenRows();
        response.close();
        log.info("batch insert to {} {} items :{} items cost:{} MS", table, count, insertCount, cost);
        return insertCount;
      } catch (Exception e) {
        throw e;
      }
    }

    public void close() {
      if (outputStream != null && !outputStream.isClosed()) {
        try {
          outputStream.close();
        } catch (IOException e) {
          log.warn("output stream close failed.", e);
        }
      }
      futureResponse = null;
    }
  }
}
