package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2023/9/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatViewBaseField {
  private String tenantId;
  private String viewId;
  private String viewName;
  private String themeApiName;
  private String schemaId;
  private List<String> statViewFields;
  private List<String> statViewFilters;
  private String timeZone;

  public Set<String> allFields() {
    Set<String> fields = Sets.newHashSet();
    if (statViewFields != null) {
      fields.addAll(statViewFields);
    }
    if (statViewFilters != null) {
      fields.addAll(statViewFilters);
    }
    return fields;
  }
}
