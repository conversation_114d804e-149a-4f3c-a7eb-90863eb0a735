package com.fxiaoke.bi.warehouse.ods.service;

import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.OkHttpClient;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.ods.args.SendNotifyArg;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.dataplatform.utils.alarm.QixinAlarm;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;

/**
 * 发送消息到企信服务号
 *
 * <AUTHOR> zzh
 * @createTime : [2024/10/23 14:20]
 */
@Slf4j
@Service
public class QiXinNotifyService {

  private static OkHttpClient okHttpClient;
  private static final DateTimeFormatter dateTimeFormatter =DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
  @Resource
  private OkHttpClient injectOkHttpClient;
  @PostConstruct
  public void init() {
    okHttpClient = this.injectOkHttpClient;
  }

  private final static String TITLE = "bi-warehouse 事件延迟告警";//IgnoreI18n

  private static final String DELAY_NOTIFY_MSG = """
    ⚠️【bi-warehouse 延迟告警】
    -----------------
    【profile】:%s
    【pgDb   】:%s
    【schema 】:%s
    【chDb   】:%s
    【traceId】:%s
    【当前状态】:%s 执行时长：%s ms，开始时间：%s，已超出当前时间阈值：%s ms，请及时处理❗❗❗
    """;

  private static final String ERROR_NOTIFY_MSG = """
    ⚠️【bi-warehouse 错误告警】
    -----------------
    【profile 】:%s
    【   pgDb 】:%s
    【 schema 】:%s
    【   chDb 】:%s
    【traceId 】:%s
    【错误信息】%s❗❗❗
    """;

  public static void sendTextMessage(SyncStatusEnum statusEnum,
                                     TransferEvent transferEvent,
                                     long execTime,
                                     long start,
                                     long delayTime) {
    String pgDb = transferEvent.getDbURL();
    String schema = transferEvent.getSchema();
    String chDb = transferEvent.getChDbURL();
    String status = statusEnum.name();
    String profile = ConfigHelper.getProcessInfo().getProfile();
    Timestamp timestamp = new Timestamp(start);
    String startTime = timestamp.toLocalDateTime().format(dateTimeFormatter);
    String msgContent = DELAY_NOTIFY_MSG.formatted(profile,pgDb, schema, chDb, TraceContext.get().getTraceId(), status, execTime, startTime, delayTime);
    CompletableFuture.runAsync(() -> {
      try {
        alarm(WarehouseConfig.biWarehouseDelayMsgReceiver, WarehouseConfig.biWarehouseDelayMsgReceiverName, msgContent, pgDb);
      } catch (Exception e) {
        log.error("warehouse delay sendTextMessage error, pgDb:{}, chDb:{}, status:{}, execTime:{}", pgDb, chDb, status, execTime, e);
      }
    });
  }

  public static void alarm(String ids, String names, String msgContent, String pgDb) {
    if (GrayManager.isAllowByRule("useIndustrySendNotify", pgDb)) {
      SendNotifyArg arg = SendNotifyArg.builder()
                                       .title(TITLE)
                                       .userIds(WarehouseConfig.biWarehouseDelayMsgReceiver)
                                       .userNames(WarehouseConfig.biWarehouseDelayMsgReceiverName)
                                       .msgContent(msgContent)
                                       .build();
      okHttpClient.asyncPost(WarehouseConfig.industryAddress +
                             "/industry-ods/clickHouseData/sendNotifyToFs", new HashMap<>(), arg, "application/json");
    } else {
      QixinAlarm alarm = new QixinAlarm.Builder().addRecipientIds(ids.split(","))
                                                 .addRecipientNames(names.split(","))
                                                 .subject(TITLE)
                                                 .content(msgContent)
                                                 .build();
      alarm.send();
    }
  }

  public static void sendTextMessage(String pgDb, String schema, String chDb, String errorMsg) {
    String profile = ConfigHelper.getProcessInfo().getProfile();
    String msgContent = ERROR_NOTIFY_MSG.formatted(profile,pgDb, schema, chDb, TraceContext.get().getTraceId(), errorMsg);
    CompletableFuture.runAsync(() -> {
      try {
        alarm(WarehouseConfig.biWarehouseDelayMsgReceiver, WarehouseConfig.biWarehouseDelayMsgReceiverName, msgContent, pgDb);
      } catch (Exception e) {
        log.error("warehouse error sendTextMessage error, pgDb:{}, chDb:{}, errorMsg:{}", pgDb, chDb, errorMsg, e);
      }
    });
  }
}
