package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.DashBoardCustomizeFilterDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * @Author:jief
 * @Date:2023/7/10
 */
@Repository
public interface DashBoardCustomizeFilterMapper extends ICrudMapper<DashBoardCustomizeFilterDO>,
  IBatchMapper<DashBoardCustomizeFilterDO>, ITenant<DashBoardCustomizeFilterMapper> {

  @Select(
    "select * from dash_board_customize_filter where tenant_id=#{tenant_id} and dash_board_id=any(#{dash_board_id}) " +
      "and is_deleted=0 limit 1")
  DashBoardCustomizeFilterDO queryDBCustomFilterByDBId(@Param("tenant_id") String tenantId,
                                                       @Param("dash_board_id") String[] dashBoardId);
}
