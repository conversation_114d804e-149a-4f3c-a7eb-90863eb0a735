package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.common.util.TopologyUtils;
import com.fxiaoke.helper.StringHelper;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@SuperBuilder
public class ValueRule extends QuoteOfAgg<AggRule> {
  @Getter
  @Setter
  private AggType aggType;
  @Getter
  private String measureId;

  @Override
  public void init(AggRule aggRule) {
    //纠正唯一计数
    fixCountUniqPlus(aggRule);
  }

  public String createAggConfigString() {
    return this.alias + "." + this.column + ":" + aggType.getName().toLowerCase();
  }

  public String createAggColumnName(Map<String, AtomicInteger> aggAliasMapper) {
    return TopologyUtils.createAggAlias(aggAliasMapper,
      "agg_" + aggType.getChAggColumn().toLowerCase(), "agg_" + aggType.getChAggColumn().toLowerCase());
  }

  /**
   * 返回value所在列名，如果是驼峰，会加转义字符
   *
   * @param table
   * @return
   */
  public String valueColumn(String table) {
    if ("value0".equals(column)) {
      if (table.endsWith("__c")) {
        return "id";
      }
    }
    if (StringHelper.containUpperChar(column)) {
      return "\"" + column + "\"";
    }
    return column;
  }

  /**
   * 聚合结果是否是number
   *
   * @return
   */
  public boolean isNumber() {
    switch (aggType) {
      case sum:
      case count:
      case countdistinct:
        return true;
      case max:
      case min:
      case avg:
      case countuniq:
      case countuniq2:
      default:
        return false;
    }
  }

  public boolean isSum() {
    switch (aggType) {
      case sum:
        return true;
      case count:
      case countdistinct:
      case max:
      case min:
      case avg:
      case countuniq:
      case countuniq2:
      default:
        return false;
    }
  }

  public String aggSql(String table, String tableAlias) {
    String columnName = column;
    if ("value0".equals(column)) {
      if (table.endsWith("__c")) {
        columnName = "id";
      }
    }
    String col = StringHelper.containUpperChar(columnName) ? "\"" + columnName + "\"" : columnName;
    switch (aggType) {
      case count:
        return "COUNT(" + tableAlias + "." + col + ")";
      case sum:
        return "SUM(" + tableAlias + "." + col + "::NUMERIC)";
      case max:
        return "MAX(" + tableAlias + "." + col + ")";
      case min:
        return "MIN(" + tableAlias + "." + col + ")";
      case avg:
        return "AVG(" + tableAlias + "." + col + ")";
      case countdistinct:
        return "COUNT(DISTINCT(" + tableAlias + "." + col + "))";
      case countuniq:
        return tableAlias + "." + col;
      case countuniq2:
        return tableAlias + "." + col;
    }
    return null;
  }

  public String columnAlias() {
    if (aggType == AggType.countuniq2) {
      return "object_id";
    }
    return "quota";
  }

  public String defaultValue() {
    if (aggType == AggType.countuniq) {
      return null;
    }
    return null;
  }

  /**
   * 唯一计数修复，如果object_id对象跟agg对象重合，并且object_id列是主键列，才能使用唯一计数，其他场景有悖，SQL规则
   *
   * @param aggRule
   */
  private void fixCountUniq(AggRule aggRule) {
    if (aggType == AggType.countuniq) {
      if (aggRule.apiName.equals("biz_leads_transfer_log")) {
        if (aggRule.objectIdRule.column.equals("leads_id")) {
          return;
        }
      }
      //针对线索主题，客户查找关联线索计算客户数的情况
      if ("biz_account".equals(aggRule.objectIdTable()) && "leads_id".equals(aggRule.objectIdRule.column)) {
        if ("account_id".equals(aggRule.valueRule.column)) {
          return;
        }
      }
      //针对高级外勤查找关联客户数的情况本身就是n:1
      if ("checkins_data".equals(aggRule.valueApiName()) && "customer_id".equals(aggRule.valueRule.column)) {
        return;
      }
      if (aggRule.objectIdApiIsAggApi()) {
        //不需要group by object_id表就是agg表，并且object_id列是主键列
        String column = aggRule.objectIdRule.column;
        if ("id".equals(column) || "_id".equals(column) || "value0".equals(column)) {
          return;
        }
        //员工，部门user_id, dept_id是主键
        String objectIdTable = aggRule.objectIdTable();
        if (objectIdTable.equals("org_employee_user") || objectIdTable.equals("org_dept")) {
          if ("user_id".equals(column) || "dept_id".equals(column)) {
            return;
          }
        }
        //object_id列跟value重合
        if (aggRule.valueApiIsAggApi()) {
          if (aggRule.objectIdRule.column.equals(aggRule.valueRule.column)) {
            aggType = AggType.countuniq2;
            return;
          }
        }
      } else {
        //object_id列跟value重合
        if (aggRule.valueApiIsAggApi()) {
          if (aggRule.valueRule.column.equals(aggRule.objectIdRule.column)) {
            aggType = AggType.countuniq2;
            return;
          }

          //md只是引用的id
          if ("id".equals(aggRule.objectIdRule.column)) {
            if (aggRule.valueRule.column.equals(aggRule.objectIdRule.joinRelation.column)) {
              return;
            }
          }
        }
        //特殊处理员工部门对象
        String apiName = aggRule.objectIdRule.joinRelation.apiName;
        String referenceColumn = aggRule.objectIdRule.joinRelation.column;
        String objectIdColumn = aggRule.objectIdRule.column;
        if ("org_employee_user".equals(apiName)) {
          if ("user_id".equals(objectIdColumn)) {
            if (referenceColumn.equals(column)) {
              return;
            }
          }
        }
        if ("org_dept".equals(apiName)) {
          if ("dept_id".equals(objectIdColumn)) {
            if (referenceColumn.equals(column)) {
              return;
            }
          }
        }
      }
      aggType = AggType.count;
    }
  }

  /**
   * 唯一计数修复，如果object_id对象跟agg对象重合，并且object_id列是主键列，才能使用唯一计数，其他场景有悖，SQL规则
   *
   * @param aggRule
   */
  private void fixCountUniqPlus(AggRule aggRule) {
    if (aggType == AggType.countuniq) {
      //考核人员数量
      if (Objects.equals(aggRule.apiName, "org_employee_user") && Objects.equals(aggRule.valueRule.column, "user_id")) {
        aggType = AggType.count;
        return;
      }
      //考核部门数量
      if (Objects.equals(aggRule.apiName, "org_dept") && Objects.equals(aggRule.valueRule.column, "dept_id")) {
        aggType = AggType.count;
        return;
      }
      if (StringUtils.equalsAny(aggRule.valueRule.column, "id", "_id", "value0")) {
        aggType = AggType.count;
        return;
      }
      if (aggRule.valueRule.isUnique) {
        aggType = AggType.count;
      }
    }
    if (aggType == AggType.count) {
      if (Objects.equals(aggRule.apiName, "base_crmfeedrelation") && Objects.equals(aggRule.valueRule.column, "feed_id")) {
        aggType = AggType.countuniq;
        return;
      }
    }
  }
}
