package com.fxiaoke.bi.warehouse.ods.context;

public interface CHDataType {
  String STRING = "String";

  String Int8 = "Int8";
  String Int16 = "Int16";
  String Int32 = "Int32";
  String Int64 = "Int64";
  String Int128 = "Int128";
  String Int256 = "Int256";

  String UInt8 = "UInt8";
  String UInt16 = "UInt16";
  String UInt32 = "UInt32";
  String UInt64 = "UInt64";
  String UInt128 = "UInt128";
  String UInt256 = "UInt256";

  String Float32 = "Float32";
  String Float64 = "Float64";

  String Decimal32 = "Decimal32";
  String Decimal64 = "Decimal64";
  String Decimal128 = "Decimal128";
  String Decimal256 = "Decimal256";
  String Bool = "Bool";
  String UUID = "UUID";
  String Date = "Date";
  String Date32 = "Date32";

  String DateTime = "DateTime";//DateTime('Asia/Istanbul')
  String DateTime64 = "DateTime64";//DateTime64(3, 'Asia/Istanbul')
  String ARRAY = "Array";//Array(UInt8)
  String JSON = "JSON";
  String Enum = "Enum";
  String Tuple = "Tuple";
  //gis
  String Point = "Point";
  String Polygon = "Polygon";
  String MultiPolygon = "MultiPolygon";
  String Ring = "Ring";

  String Map = "Map";

  String IPv4 = "IPv4";
  String IPv6 = "IPv6";

  String Nullable="Nullable";

  String LowCardinality="LowCardinality";
}
