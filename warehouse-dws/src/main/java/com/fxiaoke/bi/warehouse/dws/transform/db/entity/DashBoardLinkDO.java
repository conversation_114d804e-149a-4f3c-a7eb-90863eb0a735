package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author:jief
 * @Date:2023/9/14
 */
@Data
@Table(name = "dash_board_link")
public class DashBoardLinkDO {
  @Id
  @Column(name = "link_id")
  private String linkId;

  @Column(name = "tenant_id")
  private String tenantId;

  @Column(name = "dash_board_id")
  private String dashBoardId;

  @Column(name = "owner_id")
  private String ownerId;

  @Column(name = "follows")
  private String follows;

  @Column(name = "type")
  private int type;

  @Column(name = "creator")
  private String creator;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name="last_modifier")
  private String lastModifier;

  @Column(name="last_modified_time")
  private Date lastModifiedTime;

  @Column(name = "is_deleted")
  private Integer isDeleted;
  @Column(name="follow_view_ids")
  private String [] followViewIds;
}
