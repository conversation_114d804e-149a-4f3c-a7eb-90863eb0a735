package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.core.db.ClickhouseTenantPolicy;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableMergeDO;
import com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyTableMapper;
import com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyTableMergeMapper;
import com.fxiaoke.bi.warehouse.dws.exception.VersionConflictException;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTable;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.helper.CollectionHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/3/17
 */
@Slf4j
@Repository
public class TopologyTableDao {
  @Resource
  private TopologyTableMapper topologyTableMapper;
  @Resource
  private TopologyTableMergeMapper topologyTableMergeMapper;
  @Resource
  private ClickhouseTenantPolicy clickhouseTenantPolicy;

  public List<TopologyTable> findByTenantId(String tenantId,int[] status) {
    List<TopologyTableDO> topologyTableDOList;
    if (GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
      topologyTableDOList = topologyTableMapper.setTenantId(tenantId).findCalcFromTableMergeList(tenantId,status);
      log.info("findCalcFromTableMergeList result tenantId:{},size:{}",tenantId,topologyTableDOList.size());
    } else {
      topologyTableDOList = this.findNeedCalculateList(tenantId,status);
    }
    if (CollectionHelper.isEmpty(topologyTableDOList)) {
      return Lists.newArrayList();
    }
    String databaseName = clickhouseTenantPolicy.getDBName(tenantId, null);
    if (null == databaseName) {
      log.error("databaseName is null tenantId:{}",tenantId);
      return Lists.newArrayList();
    }
    List<TopologyTable> result = new ArrayList<>();
    topologyTableDOList.forEach(it -> {
      TopologyTable topologyTable = TopologyTable.fromTopologyTableDO(it);
      if (StringUtils.isNotBlank(it.getDatabaseId())) {
        topologyTable.setDatabase(clickhouseTenantPolicy.getDBName(tenantId, it.getDatabaseId()));
      } else {
        topologyTable.setDatabase(databaseName);
      }
      result.add(topologyTable);
    });
    return result;
  }

  /**
   * 更新topology_table 状态
   *
   * @param tenantId
   * @param viewId
   * @param status
   * @param version
   * @param batchNum
   * @param maxModifiedTime
   * @return
   */
  public int updateTopologyStatusByViewId(String tenantId,
                                          String viewId,
                                          Integer status,
                                          int version,
                                          long batchNum,
                                          Long maxModifiedTime,
                                          boolean isUpdateStatRule,
                                          String statListJson) {
    return topologyTableMapper.setTenantId(tenantId)
                              .updateTopologyStatusByViewId(tenantId, viewId, status, version, batchNum,
                                maxModifiedTime, isUpdateStatRule, statListJson);
  }

  /**
   * @param tenantId
   * @param status
   * @param uniqueKey
   * @param batchNum
   * @param maxModifiedTime
   * @return
   */
  public int updateTopologyStatusByUniqueKey(String tenantId,
                                             Integer status,
                                             String uniqueKey,
                                             long batchNum,
                                             Long maxModifiedTime,
                                             boolean isUpdateStatRule,
                                             String statListJson,
                                             boolean incVersion) {
    return topologyTableMapper.setTenantId(tenantId)
                              .updateTopologyStatusByUniqueKey(tenantId, status, uniqueKey, batchNum, maxModifiedTime, isUpdateStatRule, statListJson, incVersion);
  }

  /**
   * 反查 unique key相同的拓扑图
   *
   * @param tenantId          租户id
   * @param statViewUniqueKey 图唯一标识
   * @return
   */
  public List<String> queryViewIdByUniqueKey(String tenantId, String statViewUniqueKey) {
    return topologyTableMapper.setTenantId(tenantId).queryViewIdByUniqueKey(tenantId, statViewUniqueKey);
  }

  public   List<String> queryViewIdByUniqueKeyAndStatus(String tenantId, String statViewUniqueKey, int[] status){
    return topologyTableMapper.setTenantId(tenantId).queryViewIdByUniqueKeyAndStatus(tenantId, statViewUniqueKey,status);
  }

  /**
   * 根据 sourceId 查询拓扑图查询聚合时间，影响的api
   *
   * @param tenantId 租户id
   * @param sourceId 统计图id
   * @return
   */
  public JSONObject findAggTimeAndEffectApiBySourceId(String tenantId, String sourceId) {
    Map<String, Object> maxAggTimeEffectApi = topologyTableMapper.setTenantId(tenantId)
                                                                 .findAggTimeAndEffectApiBySourceId(tenantId, sourceId);
    if (!MapUtils.isNullOrEmpty(maxAggTimeEffectApi)) {
      return new JSONObject(maxAggTimeEffectApi);
    }
    return new JSONObject();
  }

  public TopologyTable findByTenantIdAndSourceId(String tenantId, String sourceId) {
    TopologyTableDO topologyTableDO = this.queryTopologyBySourceId( tenantId, sourceId);
    if (topologyTableDO == null) {
      log.warn("findByTenantIdAndSourceId return null,tenantId:{},sourceId:{}", tenantId, sourceId);
      return null;
    }
    TopologyTable result = TopologyTable.fromTopologyTableDO(topologyTableDO);
    String databaseName = clickhouseTenantPolicy.getDBName(tenantId,topologyTableDO.getDatabaseId());
    if (databaseName == null) {
      return null;
    }
    result.setDatabase(databaseName);
    return result;
  }

  public TopologyTable findByTenantIdAndSourceIdWithMergeVersion(String tenantId, String sourceId) {
    TopologyTableDO topologyTableDO = this.findByEiAndSourceIdWithMergeVersion(tenantId, sourceId);
    if(topologyTableDO==null){
      log.warn("findByTenantIdAndSourceIdWithMergeVersion return null,tenantId:{},sourceId:{}",tenantId,sourceId);
      return null;
    }
    TopologyTable result = TopologyTable.fromTopologyTableDO(topologyTableDO);
    String databaseName = clickhouseTenantPolicy.getDBName(tenantId,topologyTableDO.getDatabaseId());
    if (databaseName == null) {
      return null;
    }
    result.setDatabase(databaseName);
    return result;
  }

  /**
   * 根据source_id 反查unique_key和版本
   *
   * @param tenantId
   * @param sourceId
   * @return
   */
  public Map<String, Object> queryUniqueKeyAndVersion(String tenantId, String sourceId) {
    return topologyTableMapper.setTenantId(tenantId).queryUniqueKeyAndVersion(tenantId, sourceId);
  }
  /**
   * 根据fieldId 查找所有用到该field的拓扑图。
   *
   * @param tenantId 租户id
   * @param status   状态
   * @param fieldIds 指标或维度id
   * @return List
   */
  public List<TopologyTableDO> findTopologyTablesByFieldId(String tenantId, int[] status, String[] fieldIds) {
    return topologyTableMapper.setTenantId(tenantId).queryTopologyTableByFieldId(tenantId, status, fieldIds);
  }

  /**
   * 根据fieldId 查找所有用到该field的拓扑图。
   *
   * @param tenantId 租户id
   * @param status   状态
   * @param fieldIds 指标或维度id
   * @return List
   */
  public List<TopologyTableDO> queryTopologyTableFromAllAgg(String tenantId, int[] status, String[] fieldIds) {
    return topologyTableMapper.setTenantId(tenantId).queryTopologyTableFromAllAgg(tenantId, status, fieldIds);
  }


  /**
   * 根据视图的id在拓扑表中查找对象的
   *
   * @param tenantId 租户id
   * @param sourceId 视图id
   * @return TopologyTableDO
   */
  public TopologyTableDO queryTopologyBySourceId(String tenantId, String sourceId) {
    return topologyTableMapper.setTenantId(tenantId).findByTenantIdAndSourceId(tenantId, sourceId);
  }

  public TopologyTableDO findByEiAndSourceIdWithMergeVersion(String tenantId, String sourceId){
    return topologyTableMapper.setTenantId(tenantId).findByEiAndSourceIdWithMergeVersion(tenantId,  sourceId);
  }

  /**
   * 根据视图的id在拓扑表中查找对象的
   *
   * @param tenantId 租户id
   * @param sourceId 视图id
   * @return TopologyTableDO
   */
  public TopologyTableDO queryTopologyBySourceIdWithDel(String tenantId, String sourceId) {
    return topologyTableMapper.setTenantId(tenantId).findByTenantIdAndSourceIdWithDel(tenantId, sourceId);
  }

  public List<TopologyTableDO> queryTopologyTableByApiName(String tenantId, int[] status, String themeApiName, int sourceType) {
    return topologyTableMapper.setTenantId(tenantId).queryTopologyTableByApiName(tenantId, themeApiName, status, sourceType);
  }

  /**
   * 更新带版本的topology 表
   *
   * @param tenantId        租户id
   * @param topologyTableDO topology bean
   * @param expectedVersion 预设的版本用于和数据库中的版本对比，不一致则说明有修改
   */
  public int saveTopologyWithVersion(String tenantId,
                                     TopologyTableDO topologyTableDO,
                                     int expectedVersion) throws VersionConflictException {
    int effectRows = topologyTableMapper.setTenantId(tenantId)
                                        .upsertTopologyWithVersion(expectedVersion, topologyTableDO);
    if (effectRows == 0) {
      throw new VersionConflictException(String.format("save error tenantId:%s,expectedVersion:%d,sourceId:%s", tenantId, expectedVersion, topologyTableDO.getSourceId()));
    }
    return effectRows;
  }

  public int saveTopologyTable(String tenantId, TopologyTableDO topologyTableDO) {
    return topologyTableMapper.setTenantId(tenantId)
                              .upsertTopologyInfo(Lists.newArrayList(topologyTableDO), Sets.newHashSet("tenant_id", "source_id"));
  }

  /**
   * 获取所有topology对象
   *
   * @param tenantId 租户id
   * @return TopologyTableDO 集合
   */
  public List<TopologyTableDO> findNeedCalculateList(String tenantId,int[] status) {
    return topologyTableMapper.setTenantId(tenantId).findNeedCalculateList(tenantId,status);
  }

  /**
   * 删除topologyTable
   * @param tenantId 租户id
   * @param sourceId 统计图id
   * @param preVersion 变更千的版本
   * @return
   */
  public int deleteTopologyTableBySourceIdWithVersion(String tenantId, String sourceId, int preVersion) {
    return topologyTableMapper.setTenantId(tenantId).updateTopologyDelWithVersion(tenantId, sourceId, 1, preVersion);
  }
  /**
   * 删除该图
   *
   * @param tenantId 租户id
   * @param sourceIds 图集合
   */
  public int batchDeleteTopologyTableBySourceId(String tenantId, String[] sourceIds, Integer sourceType) {
    try {
      return topologyTableMapper.setTenantId(tenantId).batchDeleteTopologyTableBySourceId(tenantId, sourceIds, 1, sourceType);
    } catch (Exception e) {
      log.error("batchDeleteTopologyTableBySourceId error tenantId:{}", tenantId);
    }
    return 0;
  }

  /**
   * 更新topologyTable状态 并更新版本号
   *
   * @param tenantId  租户id
   * @param sourceIds 统计图id集合
   * @param status    状态
   */
  public int updateTopologyTableStatusBySourceId(String tenantId, String[] sourceIds, int status) {
    return topologyTableMapper.setTenantId(tenantId).updateTopologyTableStatus(tenantId, sourceIds, status);
  }

  /**
   * 批量比较并更新topology table状态并更新版本号
   * @param tenantId 租户id
   * @param sourceIds 统计图sourceId集合
   * @param status 状态
   * @param preVersion
   * @return
   */
  public int updateTopologyTableStatusWithVersion(String tenantId, String sourceIds, int status, int preVersion) {
    return topologyTableMapper.setTenantId(tenantId)
                              .updateTopologyTableStatusWithVersion(tenantId, sourceIds, status, preVersion);
  }
  /**
   * 更新该图的明细sql片段
   *
   * @param tenantId
   * @param sourceId
   */
  public void updateTopologyTableDetailSqlBySourceId(String tenantId, String sourceId, String pgDetailSql) {
    topologyTableMapper.setTenantId(tenantId).updateStatViewDetailSql(tenantId, sourceId, pgDetailSql);
  }

  /**
   * 批量查询图的fieldLocation映射关系
   *
   * @param tenantId
   * @param sourceIds
   * @return
   */
  public List<Map<String, Object>> batchQueryFieldLocations(String tenantId, List<String> sourceIds) {
    return topologyTableMapper.setTenantId(tenantId)
                              .batchQueryFieldLocations(tenantId, sourceIds.toArray(new String[0]));
  }

  public int resetTopologyByEi(String tenantId, int sourceType, int status, String[] sourceIds) {
    return topologyTableMapper.setTenantId(tenantId).resetTopologyByEi(tenantId, sourceType, status, sourceIds);
  }

  /**
   *根据uniqueKey查询已启用统计图数量,排除自己
   * @param tenantId
   * @param statViewUniqueKey
   * @param selfSourceId
   * @return
   */
  public Map<String,Object> queryUsedByUniqueKeyExcludedSelf(String tenantId, String statViewUniqueKey, String selfSourceId) {
    return topologyTableMapper.setTenantId(tenantId)
                              .queryUsedByUniqueKeyExcludedSelf(tenantId, statViewUniqueKey, selfSourceId);
  }
  /**
   *根据uniqueKey查询初始化中和已启用统计图数量
   * @param tenantId
   * @param statViewUniqueKey
   * @return
   */
  public int queryTopologyByUniqueKey(String tenantId, String statViewUniqueKey){
    return topologyTableMapper.setTenantId(tenantId).queryTopologyByUniqueKey(tenantId, statViewUniqueKey);
  }

  public String queryViewSqlByUniqueKey(String tenantId, String statViewUniqueKey,int[] sources){
    return topologyTableMapper.setTenantId(tenantId).queryViewSqlById(tenantId, statViewUniqueKey,sources);
  }

  public List<Map<String, Object>> querySourceIdAndViewSqlById(String tenantId, String statViewUniqueKey) {
    return topologyTableMapper.setTenantId(tenantId).querySourceIdAndViewSqlById(tenantId, statViewUniqueKey);
  }

  public int updateViewSqlByUniqueKey(String tenantId, String uniqueKey, String viewSql){
    return topologyTableMapper.setTenantId(tenantId).updateViewSqlByUniqueKey(tenantId, uniqueKey, viewSql);
  }

  /**
   * 更新topology table merge 状态
   * @param tenantId 租户id
   * @param id id
   * @param status 状态
   */
  public int updateTopologyTableMergeStatus(String tenantId,
                                            String id,
                                            int status,
                                            long batchNum,
                                            Long maxModifiedTime) {
    return topologyTableMergeMapper.setTenantId(tenantId)
                                   .updateTopologyTableMergeStatus(tenantId, id, status, new Date().getTime(), batchNum, maxModifiedTime);
  }

  /**
   * 更新opology table merge 的最近计算时间
   * @param tenantId
   * @param id
   * @param status
   * @param batchNum
   * @param maxModifiedTime
   * @return
   */
  public int updateTopologyTableMergeByStatus(String tenantId,
                                              String id,
                                              int status,
                                              long batchNum,
                                              Long maxModifiedTime) {
    return topologyTableMergeMapper.setTenantId(tenantId)
                                   .updateTopologyTableMergeByStatus(tenantId, id, status, new Date().getTime(), batchNum, maxModifiedTime);
  }

  /**
   * 更新topology table merge 并更新版本
   * @param tenantId 租户id
   * @param id  主键
   * @param status 状态
   * @param batchNum  批次
   * @param maxModifiedTime
   * @return
   */
  public int updateTopologyTableMergeStatusAndVersion(String tenantId,
                                                      String id,
                                                      int status,
                                                      long batchNum,
                                                      Long maxModifiedTime) {
    return topologyTableMergeMapper.setTenantId(tenantId)
                                   .updateTopologyTableMergeStatusAndVersion(tenantId, id, status, new Date().getTime(), batchNum, maxModifiedTime);
  }

  /**
   * 插入或更新topology table merge 数据，更新版本
   * @param topologyTableMergeDO
   * @return
   */
  public int upsertTopologyTableMerge(TopologyTableMergeDO topologyTableMergeDO) {
    return topologyTableMergeMapper.setTenantId(topologyTableMergeDO.getTenantId())
                                   .upsertTopologyTableMerge(topologyTableMergeDO);
  }

  /**
   * upsert topologyTableMerge 存在的化更新版本
   * @param topologyTableMergeDO
   * @return
   */
  public  int initTopologyTableMergeOrIncVersion(TopologyTableMergeDO topologyTableMergeDO){
    return topologyTableMergeMapper.setTenantId(topologyTableMergeDO.getTenantId())
                                   .initTopologyTableMergeOrIncVersion(topologyTableMergeDO);
  }

  /**
   * 删除topology Table merge数据
   * @param tenantId 租户id
   * @return
   */
  public int deleteTopologyMergeByEI(String tenantId) {
    return topologyTableMergeMapper.setTenantId(tenantId).deleteTopologyMergeByEI(tenantId);
  }

  /**
   *
   * @param tenantId
   * @param list
   * @return
   */
  public int insertTopologyTableMergeOnConflictDoNothing(String tenantId, List<TopologyTableMergeDO> list) {
    return topologyTableMergeMapper.setTenantId(tenantId)
                                   .insertTopologyTableMergeOnConflictDoNothing(list, Sets.newHashSet("id", "tenant_id"));
  }

  /**
   * 批量删除topology table merge 数据
   * @param tenantId 租户id
   * @param statViewUniqueKey unique key
   * @return
   */
  public int batchDeleteTopologyTableMerge(String tenantId, String[] statViewUniqueKey) {
    return topologyTableMergeMapper.setTenantId(tenantId).batchDeleteTopologyTableMerge(tenantId, statViewUniqueKey);
  }

  /**
   * 批量删除topology table merge 数据
   * @param tenantId 租户id
   * @param sourceType 0报表1新目标2老目标3报表
   * @return
   */
  public int batchDeleteTopologyTableMergeBySource(String tenantId, Integer sourceType) {
    return topologyTableMergeMapper.setTenantId(tenantId).batchDeleteTopologyTableMergeBySource(tenantId, sourceType);
  }

  public List<Map<String, String>> queryAggRuleEffectTenantId(String tenantId) {
    return topologyTableMapper.setTenantId(tenantId).queryAggRuleEffectTenantId(tenantId);
  }

  public int updateAllAggStatField(String tenantId, String viewId, String allAggStatField) {
    return topologyTableMapper.setTenantId(tenantId).updateAllAggStatField(tenantId, viewId, allAggStatField);
  }

  /**
   * 根据目标值变更的规则id查找出正在增量计算的TopologyTable
   * @param tenantId
   * @param sourceIds
   * @return
   */
  public Map<String, TopologyTable> findIncByGoalRuleId(String tenantId, Set<String> sourceIds) {
    if (CollectionUtils.isEmpty(sourceIds)) {
      return Maps.newHashMap();
    }
    List<TopologyTableDO> topologyTableDOList = topologyTableMapper.setTenantId(tenantId)
                                                                   .findNeedCalculateListByChangeGoal(tenantId, sourceIds.toArray(new String[0]));
    if (CollectionHelper.isEmpty(topologyTableDOList)) {
      return Maps.newHashMap();
    }
    String databaseName = clickhouseTenantPolicy.getDBName(tenantId, null);
    if (StringUtils.isBlank(databaseName)) {
      return Maps.newHashMap();
    }
    Map<String, TopologyTable> result = Maps.newHashMap();
    topologyTableDOList.forEach(it -> {
      TopologyTable topologyTable = TopologyTable.fromTopologyTableDO(it);
      if (StringUtils.isNotBlank(it.getDatabaseId())) {
        topologyTable.setDatabase(clickhouseTenantPolicy.getDBName(tenantId, it.getDatabaseId()));
      } else {
        topologyTable.setDatabase(databaseName);
      }
      result.put(topologyTable.getViewId(), topologyTable);
    });
    return result;
  }

  public List<Map<String,Object>> queryRepairGoalViewTopologyByEI(String tenantId) {
    return topologyTableMapper.setTenantId(tenantId).queryNeedRepairGoalTopologyByEI(tenantId);
  }

  /**
   * 查处计算中的明细主题图
   * @param tenantId
   * @return
   */
  public List<String> queryGoalViewIdTopologyByEI(String tenantId) {
    return topologyTableMapper.setTenantId(tenantId).queryGoalViewIdTopologyByEI(tenantId);
  }

  /**
   * 按库修复有问题的规则
   * @param tenantId
   * @return
   */
  public List<String> queryGoalRuleIdTopologyByEI(String tenantId) {
    return topologyTableMapper.setTenantId(tenantId).queryGoalRuleIdTopologyByEI(tenantId);
  }
}
