package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * stat_view 实体类
 */
@Table(name = "stat_view")
@Data
public class StatViewDO {
  @Column(name = "view_id")
  private String viewId;
  @Column(name = "view_name")
  private String viewName;
  @Column(name = "category_id")
  private String categoryId;
  @Column(name = "template_id")
  private String templateId;
  @Column(name = "filters_logic")
  private String filtersLogic;
  @Column(name = "ei")
  private int ei;
  @Column(name = "description")
  private String description;
  @Column(name = "chart_user_defined")
  private int chartUserDefined;
  @Column(name = "schema_id")
  private String schemaId;
  @Column(name = "permission_type")
  private int permissionType;
  @Column(name = "creator")
  private int creator;
  @Column(name = "create_time")
  private Date createTime;
  @Column(name = "updator")
  private int updator;
  @Column(name = "update_time")
  private Date updateTime;
  @Column(name = "is_delete")
  private int isDelete;
  @Column(name = "top_num")
  private int topNum;
  @Column(name = "goal_theme_api_name")
  private String goalThemeApiName;
  @Column(name = "drill_down_path")
  private String drillDownPath;
  @Column(name = "is_show_dimension")
  private int isShowDimension;
  @Column(name = "time_zone")
  private String timeZone;

  public boolean activeInDays(int days) {
    if (updateTime == null) {
      return false;
    }
    LocalDateTime localDateTime = LocalDateTime.now();
    localDateTime = localDateTime.plusDays(-days);
    LocalDateTime updateDateTime = new Timestamp(updateTime.getTime()).toLocalDateTime();
    return localDateTime.isBefore(updateDateTime);
  }
}
