package com.fxiaoke.bi.warehouse.ods.mq;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.service.DBTransferService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * @Author:jief
 * @Date:2023/5/24
 */
@Slf4j
@Service
public class TransferEventConsumer implements ApplicationListener<ContextRefreshedEvent>, MessageListenerConcurrently {
  private AutoConfMQPushConsumer consumer;
  @Resource
  private DBTransferService dbTransferService;

  @PostConstruct
  public void init() {
    consumer = new AutoConfMQPushConsumer("fs-bi-warehouse", "OBSConsumer", this);
  }

  @Override
  public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
    for (MessageExt msg : msgs) {
      String body = new String(msg.getBody(), StandardCharsets.UTF_8);
      log.info("consumer transferEvent topic:{},msgId:{},queueId:{},body:{}", msg.getTopic(), msg.getMsgId(), msg.getQueueId(), body);
      TransferEvent transferEvent = JSON.parseObject(body, TransferEvent.class);
      String pgSchema = transferEvent.getSchema();
      String pgDBName = CommonUtils.getDBName(transferEvent.getDbURL());
      if ((pgSchema.startsWith("sch_") && GrayManager.isAllowByRule("use_gray_topic_ei", pgSchema.substring(4))) || GrayManager.isAllowByRule("use_gray_topic_pgdb", pgDBName)) {
        if (!Objects.equals(msg.getTopic(), CHContext.BI_WAREHOUSE_EVENT_TOPIC_GRAY)) {
          log.info("skip this msg is use gray topic:{},msgId:{},queueId:{},body:{}", msg.getTopic(), msg.getMsgId(), msg.getQueueId(), body);
          continue;
        }
      }
      try {
        dbTransferService.doTransfer2CH(transferEvent);
      } catch (Exception e) {
        log.error("transfer2ch error {}", JSON.toJSONString(transferEvent), e);
        //计算重试
        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
      }
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }

  @PreDestroy
  public void destroy() {
    if (null != consumer) {
      consumer.shutdown();
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (null == event.getApplicationContext().getParent()) {
      consumer.start();
    }
  }
}
