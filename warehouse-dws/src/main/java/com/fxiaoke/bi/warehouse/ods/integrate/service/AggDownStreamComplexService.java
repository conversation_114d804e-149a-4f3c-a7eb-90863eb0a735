package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.ods.entity.*;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.*;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.BIMtTopologyTableDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.StatFieldDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BiDataSyncPolicyDo;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.TopologyTableIntegrateDO;
import com.fxiaoke.bi.warehouse.ods.integrate.util.SynchronizationDataUtil;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bi.warehouse.ods.service.CHClientService;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.bi.warehouse.ods.service.CHNodeService;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fxiaoke.bi.warehouse.ods.integrate.dto.DimFieldTypeEnum.DATE_MONTH;
import static com.fxiaoke.bi.warehouse.ods.integrate.dto.DimFieldTypeEnum.REF;
import static com.fxiaoke.bi.warehouse.ods.integrate.dto.FilterTypeEnum.AGG;

@Slf4j
@Service
public class AggDownStreamComplexService {

    @Resource
    private BIMtTopologyTableDao biMtTopologyTableDao;

    @Resource
    private AggDataSyncInfoService aggDataSyncInfoService;

    @Resource
    private CHClientService chClientService;

    @Resource
    private CHNodeService chNodeService;

    @Resource
    private CHDBService chdbService;

    @Resource
    private CHDataSource chDataSource;

    @Resource
    private CHRouterPolicy chRouterPolicy;

    @Resource
    private StatFieldDao statFieldDao;

    public void syncComplexAggData(AtomicLong syncCounts,
                                   long currentTimeMillis,
                                   long nextBatchNum,
                                   String sourceTenantId,
                                   String objectId,
                                   RouterInfo targetTenantRouterInfo,
                                   BiDataSyncPolicyDo biDataSyncPolicyDo,
                                   String partitionName) {
        List<AggMappingRule> aggMappingRuleList = getAggMappingRuleList(biDataSyncPolicyDo.getAggMappingRule());
        if (CollectionUtils.isEmpty(aggMappingRuleList)) {
            return;
        }
        RouterInfo sourceTenantRouterInfo = chRouterPolicy.getRouterInfo(sourceTenantId);
        List<String> sourceIdList = aggMappingRuleList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getAggMappingList())).map(AggMappingRule::getViewId).distinct().toList();
        List<TopologyTableIntegrateDO> topologyTableIntegrateDOList = biMtTopologyTableDao.batchQuerySourceTenantTopology(sourceTenantRouterInfo.getTenantId(), sourceIdList);
        if (CollectionUtils.isEmpty(topologyTableIntegrateDOList)) {
            return;
        }
        Map<String, AggMappingRule> viewIdToAggMappingRuleMap = aggMappingRuleList.stream().collect(Collectors.toMap(AggMappingRule::getViewId, Function.identity(), (item1, item2) -> item2));
        List<StatFieldDO> virtualStatFieldList = statFieldDao.findStatFieldList(targetTenantRouterInfo.getTenantId(), sourceIdList);
        Map<String, String> statLocationMap = virtualStatFieldList.stream().collect(Collectors.toMap(StatFieldDO::getFieldId, StatFieldDO::getDbFieldName, (item1, item2) -> item2));
        topologyTableIntegrateDOList.forEach(topologyTableIntegrateDO -> {
            String viewId = topologyTableIntegrateDO.getSourceId();
            String statViewUniqueKey = topologyTableIntegrateDO.getStatViewUniqueKey();
            AggMappingRule aggMappingRule = viewIdToAggMappingRuleMap.get(topologyTableIntegrateDO.getSourceId());
            Map<String, String> statFieldLocation = topologyTableIntegrateDO.getStatFieldLocation();
            List<SyncDimContext> syncDimContext = getSyncDimContext(aggMappingRule.getDimFiledIdList(), statFieldLocation);
            List<SyncFilterContext> syncFilterContext = getSyncFilterContext(aggMappingRule.getFilterList(), statFieldLocation);
            boolean supportSync = checkSupportSync(syncDimContext, syncFilterContext);
            if (!supportSync) {
                return;
            }
            Map<String, String> syncAggMap = getSyncAggMap(aggMappingRule.getAggMappingList(), statFieldLocation, statLocationMap);
            AggDataSyncInfoDo aggDataSyncInfoDo = aggDataSyncInfoService.queryAggDataSyncInfoByPolicyId(targetTenantRouterInfo.getTenantId(), sourceTenantRouterInfo.getTenantId(), viewId, biDataSyncPolicyDo.getPolicyId(), statViewUniqueKey);
            boolean isFullSynchronization = aggDataSyncInfoDo.getBatchNum() == 0;
            SyncAggDataContext syncAggDataContext = new SyncAggDataContext(targetTenantRouterInfo, sourceTenantRouterInfo, biDataSyncPolicyDo.getPolicyId(), viewId, statViewUniqueKey, topologyTableIntegrateDO.getVersion(), objectId, nextBatchNum, aggDataSyncInfoDo.getMaxSyncTimeStamp(), isFullSynchronization, currentTimeMillis, syncDimContext, syncFilterContext, syncAggMap, "0");
            if (aggDataSyncInfoDo.getBatchNum() >= nextBatchNum) {
                return;
            }
            try {
                aggDataSyncInfoService.insertBeforeAggDownStreamData(targetTenantRouterInfo, aggDataSyncInfoDo, syncAggDataContext.getVersion(), syncAggDataContext.getStatViewUniqueKey(), syncAggDataContext.getObjectId(), syncAggDataContext.getBatchNum(), syncAggDataContext.getPolicyId(),partitionName);
                long syncNums = this.syncAggregateDataToAggDownStreamData(syncAggDataContext,partitionName);
                syncCounts.addAndGet(syncNums);
                aggDataSyncInfoDo.setViewId(syncAggDataContext.getViewId());
                aggDataSyncInfoDo.setBatchNum(nextBatchNum);
                aggDataSyncInfoDo.setMaxSyncTimeStamp(currentTimeMillis);
                aggDataSyncInfoService.updateAggDataSyncInfoByPolicyId(targetTenantRouterInfo.getTenantId(), aggDataSyncInfoDo, SyncStatusEnum.SYNC_ED);
            } catch (Exception e) {
                log.error("syncComplexAggData error this tenantId is {}, viewId is {}", sourceTenantRouterInfo.getTenantId(), viewId, e);
                throw new RuntimeException(String.format("syncComplexAggData error this tenantId is %s, viewId is %s", sourceTenantRouterInfo.getTenantId(), viewId),e);
            }
        });
    }

    public long syncAggregateDataToAggDownStreamData(SyncAggDataContext syncAggDataContext,String partitionName) {
        log.info("syncAggregateDataToAggDownStreamData this syncAggDataContext is {}", JSON.toJSONString(syncAggDataContext));
        AtomicLong nums= new AtomicLong(0L);
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chRouterPolicy.getChJdbcURL(syncAggDataContext.getDownRouterInfo(), true), 7200000L)) {
            ClickhouseTable clickhouseTable = crateDownStreamTable(syncAggDataContext,partitionName);
            Biz2CHConsumer biz2CHConsumer = getBiz2CHConsumer(syncAggDataContext, clickhouseTable);
            String sqlTemplate = this.toAggDownStreamFinalDataSql2(syncAggDataContext,partitionName);
            log.info("query sql template :{}", sqlTemplate);
            AtomicBoolean atomicBoolean = new AtomicBoolean(false);
            AtomicLong resultCount = new AtomicLong();
            try {
                do {
                    jdbcConnection.query(String.format(sqlTemplate, syncAggDataContext.getHashCode()), resultSet -> {
                        atomicBoolean.set(true);
                        resultCount.set(0);
                        while (resultSet.next()) {
                            Map<String, Object> logMap = Maps.newLinkedHashMap();
                            ResultSetMetaData metaData = resultSet.getMetaData();
                            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                                String columnName = metaData.getColumnName(i);
                                logMap.put(columnName, resultSet.getObject(columnName));
                            }
                            BizLog bizLog = new BizLog(logMap);
                            try {
                                if (syncAggDataContext.isFullSynchronization()) {
                                    biz2CHConsumer.queue(bizLog, true, false);
                                } else {
                                    biz2CHConsumer.queue(bizLog, false, StringUtils.isBlank(partitionName));
                                }
                                nums.incrementAndGet();
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                            resultCount.getAndIncrement();
                            syncAggDataContext.setHashCode(resultSet.getBigDecimal("hash_code").toString());
                        }
                    });
                    if (resultCount.get() < 1000) {
                        atomicBoolean.set(false);
                    }
                } while (atomicBoolean.get());
                biz2CHConsumer.save();
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                biz2CHConsumer.closeIfNeed();
            }
            log.info("syncAggregateDataToAggDownStreamData success, this upTenantId is {}, this downTenantId is {}, viewId is {}", syncAggDataContext.getUpRouterInfo().getTenantId(), syncAggDataContext.getDownRouterInfo().getTenantId(), syncAggDataContext.getViewId());
        } catch (SQLException e) {
            log.error("syncAggregateDataToAggDownStreamData error", e);
            throw new RuntimeException(e);
        }
        return nums.get();
    }

    public String toAggDownStreamBaseDataSql(SyncAggDataContext syncAggDataContext) {
        List<SyncDimContext> syncDimContextList = syncAggDataContext.getSyncDimContextList();
        List<SyncFilterContext> syncFilterContextList = syncAggDataContext.getSyncFilterContextList();
        Map<String, String> downToUpMap = syncAggDataContext.getDownToUpMap();
        //构建内层的select字段
        Map<String, String> selectDimColumnMap = Maps.newHashMap();
        Map<String, String> selectIncrementColumnMap = Maps.newHashMap();
        Map<String, String> selectFilterColumnMap = Maps.newHashMap();
        Map<String, String> selectAggColumnMap = Maps.newHashMap();
        Map<String, String> selectColumnMap = Maps.newHashMap();
        //内层维度字段
        syncDimContextList.forEach(syncDimContext -> {
            if (syncDimContext.getDimFieldTypeEnum() == DATE_MONTH) {
                selectColumnMap.computeIfAbsent("substring(any(ad.action_date), 1, 6)", key-> "action_date");
                selectDimColumnMap.put("substring(any(ad.action_date), 1, 6)", "action_date");
                selectIncrementColumnMap.put("substring(any(amd.action_date), 1, 6)", "action_date");
            }
            if (syncDimContext.getDimFieldTypeEnum() == REF) {
                selectColumnMap.computeIfAbsent("any(ad." + syncDimContext.getFieldLocation() + ")", key -> syncDimContext.getFieldLocation());
                selectDimColumnMap.put("any(ad." + syncDimContext.getFieldLocation() + ")", syncDimContext.getFieldLocation());
                selectIncrementColumnMap.put("any(amd." + syncDimContext.getFieldLocation() + ")", syncDimContext.getFieldLocation());
            }
        });

        syncFilterContextList.forEach(syncFilterContext -> {
            if (syncFilterContext.getFilterTypeEnum() == AGG) {
                String fieldLocation = syncFilterContext.getFieldLocation();
                if (fieldLocation.contains("agg_count")) {
                    selectColumnMap.computeIfAbsent("argMax(ad." + fieldLocation + ", ad.timestamp)", key -> fieldLocation);
                }
                if (fieldLocation.contains("agg_sum")) {
                    selectColumnMap.computeIfAbsent(
                      "any_respect_nullsArgMaxIf(ad." + fieldLocation + ", ad.timestamp,ifNull(ad." + fieldLocation + "_merge, 0))", key -> fieldLocation);
                }
                if (fieldLocation.contains("agg_uniq")) {
                    selectColumnMap.computeIfAbsent("argMaxIf(ad." + fieldLocation + ", ad.timestamp, ad." + fieldLocation + "_merge)", key -> fieldLocation);
                }
            }
        });

        //内层指标字段
        downToUpMap.forEach((key, value) -> {
            if (key.contains("agg_count")) {
                selectColumnMap.computeIfAbsent("argMax(ad." + key + ", ad.timestamp)", mapKey -> key);
            }
            if (key.contains("agg_sum")) {
                selectColumnMap.computeIfAbsent("any_respect_nullsArgMaxIf(ad." + key + ", ad.timestamp,ifNull(ad." + key + "_merge, 0))", mapKey -> key);
            }
            if (key.contains("agg_uniq")) {
                selectColumnMap.computeIfAbsent("argMaxIf(ad." + key + ", ad.timestamp, ad." + key + "_merge)", mapKey->key);
            }
        });

        //构建where查询语句
        List<String> whereColumns = Lists.newArrayList();
        whereColumns.add("ad.tenant_id = '" + syncAggDataContext.getDownRouterInfo().getTenantId() + "'");
        whereColumns.add("ad.view_id = '" + syncAggDataContext.getStatViewUniqueKey() + "'");
        whereColumns.add("view_version = " + syncAggDataContext.getVersion());

        //如果是增量需要查询受到影响的维度重新计算
        if (!syncAggDataContext.isFullSynchronization()) {
            List<String> increaseWhereColumn = Lists.newArrayList();
            syncDimContextList.forEach(syncDimContext -> {
                if (syncDimContext.getDimFieldTypeEnum() == DATE_MONTH) {
                    increaseWhereColumn.add("substring(ad.action_date, 1, 6)");
                }
                if (syncDimContext.getDimFieldTypeEnum() == REF) {
                    increaseWhereColumn.add("ad." + syncDimContext.getFieldLocation());
                }
            });
            String incrementSubSqlTemplate = """
                    select distinct %s
                    from agg_data as amd
                    where amd.tenant_id = '%s'
                      and amd.view_id = '%s'
                      and amd.view_version = %d
                      and amd.timestamp > '%s'
                      and amd.timestamp < '%s'
                    group by amd.hash_code
              """;
            String incrementColumn = selectIncrementColumnMap.entrySet().stream().map(x -> x.getKey() + " as " + x.getValue()).collect(Collectors.joining(","));
            String incrementSubSQL = String.format(incrementSubSqlTemplate, incrementColumn, syncAggDataContext.getDownRouterInfo().getTenantId(), syncAggDataContext.getStatViewUniqueKey(), syncAggDataContext.getVersion(), SynchronizationDataUtil.getTimestampFormat(syncAggDataContext.getLastTimeStamp()), SynchronizationDataUtil.getTimestampFormat(syncAggDataContext.getCurrentTimeMillis()));
            whereColumns.add(increaseWhereColumn.stream().collect(Collectors.joining(",", "(", ")")) + "in (" + incrementSubSQL + ")");
        }
        //生成内层hash_code
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("cityHash64(");
        selectDimColumnMap.keySet().forEach(selectColumn -> stringBuilder.append(selectColumn).append(","));
        stringBuilder.append("'").append(syncAggDataContext.getObjectId()).append("'");
        stringBuilder.append(")");
        selectColumnMap.putIfAbsent(stringBuilder.toString(), "hash_code");
        String selectClause = selectColumnMap.entrySet().stream().map(x -> x.getKey() + " as " + x.getValue()).collect(Collectors.joining(","));
        String whereClause = String.join(" and ", whereColumns);

        String afterSql = """
            select %s
            from agg_data as ad
            where %s
            group by ad.hash_code
          """;

        return String.format(afterSql, selectClause, whereClause);
    }

    public String toAggDownStreamFinalDataSql2(SyncAggDataContext syncAggDataContext,String partitionName) {
        String sqlTemplate = """
          select '%s'                           as tenant_id
                 ,'%s'                          as policy_id
                 ,'%s'                          as view_id
                 ,%d                            as view_version
                 ,'%s'                          as object_id
                 ,action_date                   as action_date
                 ,hash_code                     as hash_code
                 ,1                             as bi_sys_flag
                 ,%s                            as bi_sys_batch_id
                 ,%s
                 %s
          from (select concat(after2.action_date, '01')            as action_date,
                       cityHash64(action_date,'%s')                as hash_code,
                       %s
                from (
                         select after.hash_code                       as hash_code,
                                any(after.action_date)                as action_date,
                                %s
                         from (
                                  %s
                                  ) after
                         group by after.hash_code
                         having %s
                         ) after2
                group by after2.action_date) as after3
          where %s
          order by hash_code
          limit 1000
          """;
        List<String> afterSelectAggList = Lists.newArrayList();
        List<String> after2SelectAggList = Lists.newArrayList();
        List<String> after3SelectAggList = Lists.newArrayList();
        syncAggDataContext.getDownToUpMap().forEach((key, value) -> {
            if (key.contains("count")) {
                afterSelectAggList.add("sum(after." + key + ") as " + key);
                after2SelectAggList.add("sum(after2." + key + ") as " + value);
                after3SelectAggList.add(value + " as " + value);
            }
            if (key.contains("sum")) {
                afterSelectAggList.add("sum(after." + key + ") as " + key);
                after2SelectAggList.add("sum(after2." + key + ") as " + value);
                after3SelectAggList.add(value + " as " + value);
            }
            if (key.contains("uniq")) {
                afterSelectAggList.add("uniqExactMergeState(after." + key + ") as " + key);
                after2SelectAggList.add("hex(uniqExactMergeState(after2." + key + ")) as " + value);
                after3SelectAggList.add(value + " as " + value);
            }
        });
        String afterSelect = String.join(",", afterSelectAggList);
        String after2Select = String.join(",", after2SelectAggList);
        String after3Select = String.join(",", after3SelectAggList);

        List<SyncFilterContext> syncFilterContextList = syncAggDataContext.getSyncFilterContextList();
        List<String> groupByColumns = Lists.newArrayList();
        syncFilterContextList.forEach(syncFilterContext -> {
            if (syncFilterContext.getFieldLocation().contains("count")) {
                groupByColumns.add("sum(after." + syncFilterContext.getFieldLocation() +") >= " + syncFilterContext.getFilterField().getValue1());
            }
            if (syncFilterContext.getFieldLocation().contains("sum")) {
                groupByColumns.add("sum(after." + syncFilterContext.getFieldLocation() +") >= " + syncFilterContext.getFilterField().getValue1());
            }
            if (syncFilterContext.getFieldLocation().contains("uniq")) {
                groupByColumns.add("uniqExactMerge(after." + syncFilterContext.getFieldLocation() + ") >= " + syncFilterContext.getFilterField().getValue1());
            }
        });
        String groupBy = String.join(" and ", groupByColumns);
        String whereHashCode = "after3.hash_code > %s";
        String partition="";
        if(StringUtils.isNotBlank(partitionName)){
            partition = ",'"+partitionName+"' as bi_sys_ods_part";
        }
        return String.format(sqlTemplate, syncAggDataContext.getUpRouterInfo()
                                                            .getTenantId(), syncAggDataContext.getPolicyId(), syncAggDataContext.getViewId(), syncAggDataContext.getVersion(), syncAggDataContext.getObjectId(), syncAggDataContext.getBatchNum(), after3Select,partition, syncAggDataContext.getObjectId(), after2Select, afterSelect, toAggDownStreamBaseDataSql(syncAggDataContext), groupBy, whereHashCode);
    }

    public ClickhouseTable crateDownStreamTable(SyncAggDataContext syncAggDataContext,String partitionName) {
        Map<String, String> downToUpMap = syncAggDataContext.getDownToUpMap();
        Set<String> aggLocationSet = downToUpMap.keySet();
        Map<String, String> fieldToTypeMap = Maps.newHashMap();
        for (String aggLocation : aggLocationSet) {
            fieldToTypeMap.put(aggLocation, SynchronizationDataUtil.getFiledTypes(aggLocation));
        }
        List<ClickhouseColumn> columnList= Lists.newArrayList();
        columnList.add(ClickhouseColumn.builder().name("tenant_id").typeName("String").type(ClickHouseColumnType.STRING).build());
        columnList.add(ClickhouseColumn.builder().name("policy_id").typeName("String").type(ClickHouseColumnType.STRING).build());
        columnList.add(ClickhouseColumn.builder().name("view_id").typeName("String").type(ClickHouseColumnType.STRING).build());
        columnList.add(ClickhouseColumn.builder().name("view_version").typeName("UInt32").type(ClickHouseColumnType.LONG).build());
        columnList.add(ClickhouseColumn.builder().name("object_id").typeName("String").type(ClickHouseColumnType.STRING).build());
        columnList.add(ClickhouseColumn.builder().name("action_date").typeName("String").type(ClickHouseColumnType.STRING).build());
        columnList.add(ClickhouseColumn.builder().name("hash_code").typeName("UInt64").type(ClickHouseColumnType.UINT64).build());
        columnList.add(ClickhouseColumn.builder().name("bi_sys_flag").typeName("Int8").type(ClickHouseColumnType.INT).build());
        if(StringUtils.isNotBlank(partitionName)){
            columnList.add(ClickhouseColumn.builder().name("bi_sys_batch_id").typeName("Int64").type(ClickHouseColumnType.UINT64).build());
        }
        columnList.add(ClickhouseColumn.builder().name("bi_sys_ods_part").typeName("String").type(ClickHouseColumnType.STRING).build());
        for (Map.Entry<String, String> entry : fieldToTypeMap.entrySet()) {
            String columnName = downToUpMap.get(entry.getKey());
            if (StringUtils.isBlank(columnName)) {
                continue;
            }
            if (StringUtils.equals(entry.getValue(), "AggregateFunction(uniqExact, Nullable(String))")) {
                columnList.add(ClickhouseColumn.builder().name(downToUpMap.get(entry.getKey())).typeName("String").type(ClickHouseColumnType.STRING).build());
            } else {
                columnList.add(ClickhouseColumn.builder().name(downToUpMap.get(entry.getKey())).typeName(entry.getValue()).type(ClickHouseColumnType.STRING).build());
            }
        }
        return ClickhouseTable.builder()
                              .name(Constants.AGG_DOWNSTREAM_DATA)
                              .db(syncAggDataContext.getUpRouterInfo().getDbName())
                              .dbURL(syncAggDataContext.findMustUrl())
                              .columnList(columnList)
                              .build();
    }

    public Biz2CHConsumer getBiz2CHConsumer(SyncAggDataContext syncAggDataContext, ClickhouseTable clickhouseTable) {
        List<String> orderByColumns = Lists.newArrayList("tenant_id", "policy_id", "view_id", "view_version", "object_id", "hash_code");
        return Biz2CHConsumer.getInstance(chClientService, clickhouseTable, 10000, syncAggDataContext.getBatchNum(), chNodeService, chdbService, orderByColumns, 100);
    }

    private List<SyncDimContext> getSyncDimContext(List<DimField> dimFieldList, Map<String, String> statFieldLocationMap) {
        if (CollectionUtils.isEmpty(dimFieldList)) {
            return Lists.newArrayList();
        }
        List<SyncDimContext> syncDimContextList = Lists.newArrayList();
        dimFieldList.forEach(dimField -> {
            String fieldLocation = statFieldLocationMap.get(dimField.getFieldId());
            DimFieldTypeEnum dimFieldTypeEnum = DimFieldTypeEnum.getDimFieldTypeEnum(dimField);
            SyncDimContext syncDimContext = new SyncDimContext(dimField, fieldLocation, dimFieldTypeEnum);
            syncDimContextList.add(syncDimContext);
        });
        return syncDimContextList;
    }

    private List<SyncFilterContext> getSyncFilterContext(List<FilterList> filterFieldList, Map<String, String> statFieldLocationMap) {
        if (CollectionUtils.isEmpty(filterFieldList)) {
            return Lists.newArrayList();
        }
        List<SyncFilterContext> syncFilterContextList = Lists.newArrayList();
        filterFieldList.stream().flatMap(x -> x.getFilterFieldList().stream()).forEach(filterField -> {
            String fieldLocation = statFieldLocationMap.get(filterField.getFieldId());
            FilterTypeEnum filterTypeEnum = FilterTypeEnum.getFilterTypeEnum(filterField);
            SyncFilterContext syncFilterContext = new SyncFilterContext(filterField, fieldLocation, filterTypeEnum);
            syncFilterContextList.add(syncFilterContext);
        });
        return syncFilterContextList;
    }

    private Map<String, String> getSyncAggMap(List<AggMapping> aggMappingList, Map<String, String> statFieldLocation, Map<String, String> statFieldLocationMap) {
        if (CollectionUtils.isEmpty(aggMappingList) || MapUtils.isEmpty(statFieldLocation) || MapUtils.isEmpty(statFieldLocationMap)) {
            return Maps.newHashMap();
        }
        Map<String, String> downToUpMap = Maps.newHashMap();
        for (AggMapping aggMapping : aggMappingList) {
            String upFieldId = aggMapping.getUpFieldId();
            String downFieldId = aggMapping.getDownFieldId();
            String downAggFieldLocation = statFieldLocation.get(downFieldId);
            String upAggFieldLocation = statFieldLocationMap.get(upFieldId);
            downToUpMap.computeIfAbsent(downAggFieldLocation, key -> upAggFieldLocation);
        }
        return downToUpMap;
    }

    private List<AggMappingRule> getAggMappingRuleList(String aggMappingRule) {
        if (StringUtils.isBlank(aggMappingRule)) {
            return Lists.newArrayList();
        }
        try {
            return JSON.parseArray(aggMappingRule, AggMappingRule.class);
        } catch (Exception e) {
            log.error("getAggMappingRuleList error, this aggMappingRule is {}", aggMappingRule, e);
            return Lists.newArrayList();
        }
    }

    private boolean checkSupportSync(List<SyncDimContext> syncDimContext, List<SyncFilterContext> syncFilterContext) {
        if (CollectionUtils.isEmpty(syncDimContext) || CollectionUtils.isEmpty(syncFilterContext)) {
            return false;
        }
        boolean existNotSupportDim = syncDimContext.stream().allMatch(x -> DimFieldTypeEnum.isSupportDimFieldType(x.getDimFieldTypeEnum()));
        boolean existNotSupportFilter = syncFilterContext.stream().allMatch(x -> FilterTypeEnum.isSupportFilterType(x.getFilterTypeEnum()));
        return existNotSupportDim && existNotSupportFilter;
    }
}
