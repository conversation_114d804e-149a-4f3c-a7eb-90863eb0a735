package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/5/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DimRefreshEvent {
  private String tenantId;
  private String themeApiName;
  private int retry = 0;

  public static DimRefreshEvent parseFromJSON(String json) {
    return JSON.parseObject(json, DimRefreshEvent.class);
  }

  public String toJSONString() {
    return JSON.toJSONString(this);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DimRefreshEvent that = (DimRefreshEvent) o;
    return tenantId.equals(that.tenantId) && themeApiName.equals(that.themeApiName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tenantId, themeApiName);
  }
}
