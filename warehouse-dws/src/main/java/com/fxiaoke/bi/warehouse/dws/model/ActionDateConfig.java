package com.fxiaoke.bi.warehouse.dws.model;

import com.fxiaoke.bi.warehouse.common.db.er.ColumnTypeConfig;
import com.fxiaoke.bi.warehouse.common.db.er.TableAliasNaming;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/3
 */
@Getter
public class ActionDateConfig {
  private String tableName;
  private String tableAlias;
  private String columnName;
  private String formatStr;
  private ColumnTypeConfig dstColumnTypeConfig;
  private ColumnTypeConfig sourceColumnTypeConfig;
  public static ActionDateConfig parse(String config, String timezone) {
    List<String> split = Splitter.on(":").splitToList(config);
    ActionDateConfig actionDateConfig = new ActionDateConfig();
    String tableAliasAndColumnNameString = split.get(0);
    List<String> tableAliasAndColumnName = Splitter.on(".").splitToList(tableAliasAndColumnNameString);
    actionDateConfig.tableName = TableAliasNaming.tableName(tableAliasAndColumnName.get(0));
    actionDateConfig.tableAlias = tableAliasAndColumnName.get(0);
    actionDateConfig.columnName = tableAliasAndColumnName.get(1);
    actionDateConfig.dstColumnTypeConfig = new ColumnTypeConfig(ImmutableMap.of(ColumnTypeConfig._ActionDate.TIMEZONE,
      timezone));
    if (Constants.AGG_DOWNSTREAM_DATA.equals(actionDateConfig.tableName) ||
        (Constants.GOAL_VALUE_OBJ_SET.contains(actionDateConfig.tableName) &&
          Constants.ACTION_DATE.equals(actionDateConfig.columnName))) {
      //date2 -> 本身就是yyyyMMdd格式,不需要转换
      actionDateConfig.sourceColumnTypeConfig = new ColumnTypeConfig(ImmutableMap.of(
        ColumnTypeConfig._ActionDate.FIELD_TYPE, "date2"));
    } else {
      actionDateConfig.sourceColumnTypeConfig = new ColumnTypeConfig(Map.of());
    }
    return actionDateConfig;
  }

  /**
   * 明细sql需要解析formatStr
   *
   * @param config
   * @param timezone
   * @return
   */
  public static ActionDateConfig parseDetail(String config, String timezone) {
    ActionDateConfig actionDateConfig = new ActionDateConfig();
    List<String> tableAliasAndColumnName = Splitter.on(".").splitToList(config);
    actionDateConfig.tableName = TableAliasNaming.tableName(tableAliasAndColumnName.get(0));
    actionDateConfig.tableAlias = tableAliasAndColumnName.get(0);
    actionDateConfig.columnName = tableAliasAndColumnName.get(1);
    actionDateConfig.formatStr = tableAliasAndColumnName.get(2);
    actionDateConfig.dstColumnTypeConfig = new ColumnTypeConfig(ImmutableMap.of(ColumnTypeConfig._ActionDate.TIMEZONE,
      timezone));
    actionDateConfig.sourceColumnTypeConfig = new ColumnTypeConfig(Map.of());
    return actionDateConfig;
  }
}
