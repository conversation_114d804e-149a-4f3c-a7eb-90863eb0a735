package com.fxiaoke.bi.warehouse.ods.service;

import com.fxiaoke.bi.warehouse.core.db.ClickhouseTenantPolicy;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.circuit.breaker.config.ConfigParser;
import com.fxiaoke.jdbc.CircuitBreakerManager;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Service
public class CHDataSource {
  @Resource
  private CHRouterPolicy chRouterPolicy;
  @Resource
  private ClickhouseTenantPolicy clickhouseTenantPolicy;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "chDataSource", iConfig -> {
      CircuitBreakerManager.setBreakerConfig(ConfigParser.parse(iConfig.get("circuit-breaker",
        "resetMillis=60000," + "continuousFail=10,failPercent=30,maxConcurrentRequests=100,pingRetries=5")));
    });
  }

  /**
   * 创建ch jdbc connection
   *
   * @param DbUrl db url
   * @return JdbcConnection
   */
  public JdbcConnection getJdbcConnection(String DbUrl) {
    return this.getJdbcConnection(DbUrl, 3600000L);
  }

  /**
   * 创建ch jdbc connection
   *
   * @param DbUrl db url
   * @return JdbcConnection
   */
  public JdbcConnection getJdbcConnection(String DbUrl, long socketTimeOut) {
    String db = CHContext.getDBName(DbUrl);
    String userNameSuffix = db.substring(0, db.length() - 3);
    return new JdbcConnection(
      DbUrl + "?socket_timeout=" + socketTimeOut,
      clickhouseTenantPolicy.getChProxyCalUserNamePre() + userNameSuffix, clickhouseTenantPolicy.getChProxyCalPassword());
  }

  /**
   * 根据企业id获取connection
   *
   * @param tenantId 企业id
   * @return
   */
  public JdbcConnection getJdbcConnectionByTenantId(String tenantId) {
    String chURL = clickhouseTenantPolicy.getCHJdbcURL(tenantId);
    if (StringUtils.isNotBlank(chURL)) {
      return this.getJdbcConnection(chURL);
    }
    return null;
  }

  public String findChURLByEi(String tenantId) {
    return clickhouseTenantPolicy.getCHJdbcURL(tenantId);
  }


  /**
   * 创建ch jdbc connection
   *
   * @param tenantId 租户id
   * @param databaseId 外部数据源id
   * @return JdbcConnection
   */
  public JdbcConnection getJdbcConnection(String tenantId,String databaseId) {
    String DbUrl =  clickhouseTenantPolicy.getCHJdbcURL(tenantId,databaseId);
    if(StringUtils.isBlank(DbUrl)){
      log.error("getCHJdbcURL is empty tenantId:{},databaseId:{}",tenantId,databaseId);
      return null;
    }
    return this.getJdbcConnection(DbUrl);
  }

  public String findChURLByEi(String tenantId, String databaseId) {
    return clickhouseTenantPolicy.getCHJdbcURL(tenantId, databaseId);
  }

}
