package com.fxiaoke.bi.warehouse.dws.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.bean.UserCenterService;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.er.AggRuleType;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBUpdateEventDao;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyStatusDao;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyTableDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.GoalRuleDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.StatViewDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtTopologyStatusDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.GoalRuleDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewDO;
import com.fxiaoke.bi.warehouse.dws.transform.impl.NewGoalTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.impl.NewRptTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.impl.OldStatViewTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.base.CharMatcher;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class StatTopologyService {
  @Resource
  private StatViewDao statViewDao;
  @Resource
  private GoalRuleDao goalRuleDao;
  @Resource
  private OldStatViewTopologyTransformer oldStatViewTopologyTransformer;
  @Resource
  private NewGoalTopologyTransformer newGoalTopologyTransformer;
  @Resource
  private NewRptTopologyTransformer newRptTopologyTransformer;
  @Resource
  private DBUpdateEventDao dbUpdateEventDao;
  @Resource
  private TopologyTableDao topologyTableDao;
  @Resource
  private TopologyStatusDao topologyStatusDao;
  @Resource
  private PgDataSource pgDataSource;
  @Resource
  private UserCenterService userCenterService;

  /**
   * schema隔离后需要重新生成一遍topology
   *
   * @param tenantId 租户id
   * @param status 状态
   */
  public void reCreateTopologyByEI(String tenantId, Integer status) {
    List<TopologyTableDO> topologyTableDOS = topologyTableDao.findNeedCalculateList(tenantId, new int[] {TopologyTableStatus.Prepared.getValue(), TopologyTableStatus.Calculating.getValue(), TopologyTableStatus.NONeedCal.getValue()});
    if (CollectionUtils.isEmpty(topologyTableDOS)) {
      log.error("can not find topologyTables tenantId:{}", tenantId);
      return;
    }
    topologyTableDOS.forEach(topologyTableDO -> {
      try {
        this.doCreateTopology(tenantId, topologyTableDO.getSourceId(), topologyTableDO.getSource(),
          status == null ? topologyTableDO.getStatus() : status);
      } catch (Exception e) {
        log.error("doCreateTopology error tenantId{},viewId:{}", tenantId, topologyTableDO.getSourceId(), e);
      }
    });
  }
  /**
   *批量计算停用的自定义图
   * @param tenantIds 租户id集合
   */
  public void flushStopCustomStatView(List<String> tenantIds) {
    if (CollectionUtils.isNotEmpty(tenantIds)) {
      tenantIds.forEach(tenantId -> {
        List<StatViewDO> statViewDOS = statViewDao.batchQueryCustomStatViewByEi(tenantId, Constants.DEFAULT_DATETIME);
        if (CollectionUtils.isNotEmpty(statViewDOS)) {
          List<String> viewIds = statViewDOS.stream()
                                            .filter(statView -> !StringUtils.equalsAny(statView.getSchemaId(),
                                              Constants.BI_ERP_DATA_SCREEN, Constants.GOAL_VALUE_OBJ_SCHEMA_ID,
                                              Constants.MULTI_GOAL_VALUE_OBJ_SCHEMA_ID,
                                              Constants.NEW_GOAL_VALUE_OBJ_SCHEMA_ID))
                                            .map(StatViewDO::getViewId)
                                            .toList();
          if (CollectionUtils.isNotEmpty(viewIds)) {
            List<BIMtTopologyStatusDO> biMtTopologyStatusDOS = topologyStatusDao.findStopStatView(tenantId,
              viewIds.toArray(new String[0]));
            if (CollectionUtils.isNotEmpty(biMtTopologyStatusDOS)) {
              biMtTopologyStatusDOS.forEach(topologyStatus -> {
                try {
                  this.doCreateTopology(tenantId, topologyStatus.getSourceId(), AggRuleType.Agg.getRuleType(),
                    TopologyTableStatus.Prepared.getValue());
                } catch (Exception e) {
                  log.error("doCreateTopology error tenantId{},viewId:{}", tenantId, topologyStatus.getSourceId(), e);
                }
              });
            }
          }
        }
      });
    }
  }

  /**
   * 批量更新
   *
   * @param statViewBatchArg
   * @return
   */
  public int batchInitAggStatViewStatus(StatViewBatchArg statViewBatchArg) {
    if (statViewBatchArg != null && statViewBatchArg.getStatus() != null) {
      List<String> viewIds = statViewBatchArg.getStatViewArgList().stream().map(StatViewPreArg::getSourceId).toList();
      return topologyStatusDao.batchUpdateStatViewStatus(statViewBatchArg.getTenantId(), viewIds, statViewBatchArg.getStatus());
    }
    return 0;
  }

  /**
   * 批量重置Topology, 按sourceId
   *
   * @param statViewBatchArg
   */
  public void resetTopologyByEi(StatViewBatchArg statViewBatchArg) {
    Preconditions.checkArgument(StringUtils.isNotBlank(statViewBatchArg.getTenantId()), "tenantId is null");
    Preconditions.checkArgument(statViewBatchArg.getStatus() != null, "status is null");
    int sourceType = statViewBatchArg.getSourceType();
    List<StatViewPreArg> statViewPreArgList = statViewBatchArg.getStatViewArgList();
    String[] sourceIds=null;
    if (CollectionUtils.isNotEmpty(statViewPreArgList)) {
      sourceIds = statViewPreArgList.stream().map(StatViewPreArg::getSourceId).toList().toArray(new String[0]);
    }
    switch (sourceType) {
      case 0 ->{
        List<String> tenantIds = Splitter.on(CharMatcher.anyOf(",|"))
                                         .omitEmptyStrings()
                                         .splitToList(statViewBatchArg.getTenantId());
        for (String tenantId : tenantIds) {
          oldStatViewTopologyTransformer.resetTopologyByEi(tenantId, sourceType, statViewBatchArg.getStatus(),
            sourceIds);
        }
      }
      case 1, 2 -> {
        log.warn("not support ");
      }
      default -> log.error("no support this sourceType:{}, tenantId:{}", sourceType, statViewBatchArg.getTenantId());
    }
  }

  /**
   * 创建topology 按sourceId
   * 统计图/多维度目标
   *
   * @param tenantId   租户id
   * @param sourceId   图表id/目标id
   * @param sourceType 0 -> 统计图, 1 -> 目标
   * @param status     状态
   */
  public void doCreateTopology(String tenantId, String sourceId, int sourceType, int status) throws Exception {
    switch (sourceType) {
      case 0 -> oldStatViewTopologyTransformer.doCreateViewTopology(tenantId, sourceId, status);
      case 1, 2 -> newGoalTopologyTransformer.doCreateGoalTopology(tenantId, sourceId, sourceType, status);
      case 3 -> newRptTopologyTransformer.doCreateRptTopology(tenantId, sourceId, sourceType);
      default ->
        log.error("no support this sourceType:{}, tenantId:{}, sourceId:{}, status:{}", sourceType, tenantId, sourceId, status);
    }
  }

  /**
   * 批量创建Topology, 按企业
   *
   * @param tenantId     租户id
   * @param sourceType   0统计图,1目标,2老目标
   * @param fromDateTime 按照变更日期过滤
   * @param all 是否刷全部
   */
  public void batchCreateTopologyByEi(String tenantId, int sourceType, String fromDateTime, boolean all) {
    try {
      if (!userCenterService.isValidateStatus(tenantId)) {
        log.warn("this tenantId:{} is invalidate Status", tenantId);
        return;
      }
      switch (sourceType) {
        case 0 -> oldStatViewTopologyTransformer.flashStatViewByTenantId(tenantId, fromDateTime, all);
        case 1, 2 -> newGoalTopologyTransformer.flashGoalRuleByTenantId(tenantId, sourceType);
        case 3 -> newRptTopologyTransformer.flashRptViewByTenantId(tenantId, sourceType);
        case -1 -> {
          oldStatViewTopologyTransformer.flashStatViewByTenantId(tenantId, fromDateTime, all);
          newGoalTopologyTransformer.flashGoalRuleByTenantId(tenantId, 1);
          newGoalTopologyTransformer.flashGoalRuleByTenantId(tenantId, 2);
          newRptTopologyTransformer.flashRptViewByTenantId(tenantId, 3);
        }
        default ->
          log.error("no support this sourceType:{}, tenantId:{}, fromDateTime:{}", sourceType, tenantId, fromDateTime);
      }
    } catch (Exception e) {
      log.error("batchCreateTopologyByEi is error, tenantId:{}, sourceType:{}", tenantId, sourceType, e);
    }
  }

  /**
   * 迁移明细主题  只包含目标的老图
   * @param tenantId
   * @param fromDateTime
   * @param all
   */
  public void batchCreateGoalViewTopologyByEI(String tenantId, String fromDateTime, boolean all) {
    try {
      if (!userCenterService.isValidateStatus(tenantId)) {
        log.warn("this tenantId:{} is invalidate Status", tenantId);
        return;
      }
      oldStatViewTopologyTransformer.flashGoalStatViewByTenantId(tenantId, fromDateTime, all);
    } catch (Exception e) {
      log.error("batchCreateGoalViewTopologyByEI ERROR, tenantId:{} ", tenantId, e);
    }
  }

  /**
   * 按库重刷已计算的明细主题  只包含目标的老图
   * @param dbSyncId
   */
  public void batchRefreshGoalViewTopology(String dbSyncId, boolean all, boolean del) {
    oldStatViewTopologyTransformer.batchRefreshGoalViewTopology(dbSyncId, all, del);
  }

  /**
   * 按库重刷已计算的目标规则
   * @param dbSyncId
   * @param all 是否全部重刷
   */
  public void batchRepairGoalRuleTopology(String dbSyncId, boolean all) {
    newGoalTopologyTransformer.batchRepairGoalRuleTopology(dbSyncId, all);
  }

  /**
   * 生成
   *
   * @param tenantId
   * @param viewId
   * @return
   */
  public TopologyTable createViewTopologyTable(String tenantId, String viewId) {
    StatViewDO statViewDO = statViewDao.queryStatView(tenantId, viewId);
    if (statViewDO == null) {
      log.warn("can not find statViewDO tenantId:{},viewId:{}", tenantId, viewId);
      return null;
    }
    TopologyTable topologyTable = oldStatViewTopologyTransformer.createStatView(tenantId, statViewDO);
    if (topologyTable == null) {
      log.error("createStatView error tenantId:{},viewId:{}", tenantId, viewId);
      return null;
    }
    return topologyTable;
  }

  /**
   * 生成
   *
   * @param tenantId
   * @param goalId
   * @return
   */
  public TopologyTable createGoalTopologyTable(String tenantId, String goalId, int source) {
    GoalRuleDO goalRuleDO = goalRuleDao.findGoalRuleById(tenantId, goalId);
    if (goalRuleDO == null || goalRuleDO.getStatus() == 0) {
      log.warn("can not find goalRuleDO or goalRule disabled tenantId:{},goalId:{},source:{}", tenantId, goalId,
        source);
      return null;
    }
    TopologyTable topologyTable = newGoalTopologyTransformer.createTopologyTable(tenantId, goalRuleDO, source,
      null);
    if (topologyTable == null) {
      log.error("createTopologyTable error tenantId:{},goalId:{},source:{}", tenantId, goalId, source);
      return null;
    }
    return topologyTable;
  }

  /**
   * 创建预览sql
   *
   * @param statViewPreArg 获取规则的参数
   * @return StatViewPreSQL
   */
  public StatViewPreSQL createStatViewPreSQL(StatViewPreArg statViewPreArg) {
    return switch (statViewPreArg.getSourceType()) {
      case 0 -> this.oldStatViewTopologyTransformer.createStatViewPreSQL(statViewPreArg);
      case 1,2 -> this.newGoalTopologyTransformer.createStatViewPreSQL(statViewPreArg);
      case 3 -> this.newRptTopologyTransformer.createStatViewPreSQL(statViewPreArg);
      default -> {
        log.error("no support this sourceType:{}, tenantId:{}", statViewPreArg.getSourceType(), statViewPreArg.getTenantId());
        yield null;
      }
    };
  }

  /**
   * 创建目标预览sql集合,目标可以选多个一起分析
   *
   * @param statViewBatchArg 获取规则的参数
   * @return
   */
  public StatViewBatchPreSQL createBatchPreViewSQL(StatViewBatchArg statViewBatchArg) {
    String tenantId = statViewBatchArg.getTenantId();
    int sourceType = statViewBatchArg.getSourceType();
    List<StatViewPreArg> statViewArgList = statViewBatchArg.getStatViewArgList();
    if (CollectionUtils.isEmpty(statViewArgList)) {
      log.error("createBatchPreViewSQL statViewArgList is empty, {}", JSON.toJSONString(statViewArgList));
    }
    if (sourceType == 1 || sourceType == 2) {
      List<StatViewPreSQL> statViewPreSQLList = Lists.newArrayList();
      statViewArgList.forEach(statViewPreArg -> {
        StatViewPreSQL statViewPreSQL = this.newGoalTopologyTransformer.createStatViewPreSQL(statViewPreArg);
        if (statViewPreSQL == null || StringUtils.isBlank(statViewPreSQL.getPreViewSQL())) {
          log.error("no support this tenantId:{}, sourceType:{}, statViewPreArg:{}", tenantId, sourceType, JSON.toJSONString(statViewPreArg));
          return;
        }
        statViewPreSQLList.add(statViewPreSQL);
      });
      //多个规则取最早
      long minModifiedTime = statViewPreSQLList.stream()
                                               .map(StatViewPreSQL::getMaxModifiedTime)
                                               .min(Long::compareTo)
                                               .orElse(0L);
      //有一个规则抽样就都是抽样
      Integer allSampleType = statViewPreSQLList.stream().anyMatch(pre -> 1 == pre.getSampleType()) ? 1 : 0;
      return StatViewBatchPreSQL.builder()
                                .tenantId(tenantId)
                                .preViewSQLList(statViewPreSQLList)
                                .sampleType(allSampleType)
                                .minModifiedTime(minModifiedTime)
                                .build();
    } else {
      log.error("no support this tenantId:{}, sourceType:{}, statViewArgList:{}", tenantId, sourceType, JSON.toJSONString(statViewArgList));
    }
    return null;
  }

  /**
   * 创建明细sql片段
   *
   * @param statViewPreArg 获取图得
   * @return
   */
  public StatViewPreSQL createDetailViewSQL(StatViewPreArg statViewPreArg) {
    String tenantId = statViewPreArg.getTenantId();
    int sourceType = statViewPreArg.getSourceType();
    return switch (sourceType) {
      case 1, 2 -> this.newGoalTopologyTransformer.createStatViewDetailSQL(statViewPreArg);
      default -> {
        log.error("no support this sourceType:{}, tenantId:{}", sourceType, tenantId);
        yield null;
      }
    };
  }

  /**
   * 批量对比统计图是否变更字段是否变更
   *
   * @param statViewBatchArg
   * @return
   */
  public List<Map<String, Object>> batchCompareStatView(StatViewBatchArg statViewBatchArg) {
    int sourceType = statViewBatchArg.getSourceType();
    return switch (sourceType) {
      case 0 -> this.oldStatViewTopologyTransformer.batchCompareStatView(statViewBatchArg);
      default -> {
        log.error("no support this sourceType:{}, tenantId:{}", sourceType, statViewBatchArg.getTenantId());
        yield null;
      }
    };
  }

  /**
   * 按照图查询最近的更新时间
   *
   * @param statViewPreArg
   * @return
   */
  public Map<String, Long> queryDBLatestSyncTimeByEI(StatViewPreArg statViewPreArg) {
    String tenantId = statViewPreArg.getTenantId();
    try {
      JSONObject aggTimeAndEffectApi = topologyTableDao.findAggTimeAndEffectApiBySourceId(tenantId,
        statViewPreArg.getSourceId());
      String effectApi = aggTimeAndEffectApi.getOrDefault("agg_effect_api_names", "{}").toString();
      Long latestAggTime = (Long) aggTimeAndEffectApi.getOrDefault("latest_agg_time", 0L);
      Optional<Long> aggTimeByTables = calLatestAggTimeByTables(tenantId, effectApi);
      if (aggTimeByTables.isPresent()) {
        return Map.of(tenantId, Math.max(latestAggTime, aggTimeByTables.get()));
      } else if (latestAggTime > 0L) {
        return Map.of(tenantId, latestAggTime);
      }
    } catch (Exception e) {
      log.error("queryDBLatestSyncTimeByEI error tenantId:{},sourceId:{}", tenantId, statViewPreArg.getSourceId(), e);
    }
    return Map.of(tenantId, System.currentTimeMillis() - 15 * 60 * 1000);
  }

  /**
   * 根据受影响的apiName 反查受影响的表的同步信息
   *
   * @param tenantId  租户id
   * @param effectApi 受影响的apiName
   * @return {@link Long}
   */
  public Optional<Long> calLatestAggTimeByTables(String tenantId, String effectApi) {
    List<DbTableSyncInfoDO> dbTableSyncInfoDOS = this.findTableSyncInfoByEffectApi(tenantId, effectApi);
    if (CollectionUtils.isNotEmpty(dbTableSyncInfoDOS)) {
      boolean haveSyncData = dbTableSyncInfoDOS.stream().anyMatch(dbTableSyncInfoDO -> {
        String apiEiMap = dbTableSyncInfoDO.getApiNameEiMap();
        if (StringUtils.isNotBlank(apiEiMap)) {
          Map<String, Set<String>> effectApiMap = JSON.parseObject(apiEiMap, new TypeReference<>() {
          });
          return effectApiMap.entrySet().stream().anyMatch(entry -> {
            Set<String> eis = entry.getValue();
            if (CollectionUtils.isNotEmpty(eis)) {
              return eis.contains(tenantId);
            }
            return false;
          });
        }
        return false;
      });
      if (!haveSyncData) {
        return dbTableSyncInfoDOS.stream().map(DbTableSyncInfoDO::getLastSyncTime).max(Comparator.naturalOrder());
      }
    }
    return Optional.empty();
  }

  /**
   * 根据受影响的apiName 反查受影响的表的同步信息
   *
   * @param tenantId  租户id
   * @param effectApi 受影响的apiName
   * @return {@link List<DbTableSyncInfoDO>}
   */
  private List<DbTableSyncInfoDO> findTableSyncInfoByEffectApi(String tenantId, String effectApi) {
    if (StringUtils.isBlank(effectApi)) {
      return null;
    }
    Map<String, Set<String>> effectApiMap = JSON.parseObject(effectApi, new TypeReference<>() {
    });
    Map<String, Set<String>> effectTableMap = TopologyTable.transEffectApiName2Tables(effectApiMap,
      pgDataSource.standalone(tenantId));
    Set<String> effectTables = effectTableMap.entrySet().stream().flatMap(entry -> {
      if (null != entry.getValue()) {
        return entry.getValue().stream();
      }
      return Stream.empty();
    }).collect(Collectors.toSet());
    return dbUpdateEventDao.findDBTableSyncInfoByEiAndTables(tenantId, effectTables);
  }

  public int upsertStatFieldByFieldId(StatFieldArg statFieldArg) {
    return statViewDao.upsertStatFieldByFieldId(statFieldArg);
  }

  /**
   * 重刷已计算的明细主题  只包含目标的老图
   * @param dbSyncId
   */
  public void batchAllGoalViewTopology(String dbSyncId, String fromDateTime, boolean isAll) {
    try {
      DBSyncInfoDO dbSyncInfoDO = dbUpdateEventDao.findSyncById(dbSyncId);
      if (dbSyncInfoDO == null) {
        log.info("batchAllGoalViewTopology dbSyncInfoDO is null, dbSyncId:{} ", dbSyncId);
        return;
      }
      if (StringUtils.isNotBlank(dbSyncInfoDO.getLastSyncEis())) {
        String[] eis = dbSyncInfoDO.getLastSyncEis().split(",");
        for (String ei : eis) {
          batchCreateGoalViewTopologyByEI(ei, fromDateTime, isAll);
        }
      }
    } catch (RuntimeException e) {
      log.error("repairAllGoalViewTopology error dbSyncInfoId:{}", dbSyncId, e);
    }
  }
}
