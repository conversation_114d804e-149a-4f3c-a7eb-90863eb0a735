package com.fxiaoke.bi.warehouse.dws.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author:jief
 * @Date:2023/9/13
 */
@Data
public class StatViewBatchArg {
  String tenantId;
  String dbSyncId;
  String dateTimeFrom;
  List<StatViewPreArg> statViewArgList;
  int sourceType; //0 -> agg统计图, 1 -> 多维度目标, 2 -> 老目标, 3 -> 报表
  /**
   * 状态
   */
  Integer status;
  /**
   * 是否刷全部
   */
  boolean all;
  /**
   * 执行之前是否需要清理信息
   */
  boolean del;
  /**
   * 是否增加版本默认增加
   */
  boolean incVersion=true;
  public String getDTFromOrDefault(String defaultValue) {
    return StringUtils.isBlank(dateTimeFrom) ? defaultValue : dateTimeFrom;
  }
}
