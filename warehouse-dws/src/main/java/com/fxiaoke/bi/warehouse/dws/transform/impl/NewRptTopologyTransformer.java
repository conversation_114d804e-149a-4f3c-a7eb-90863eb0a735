package com.fxiaoke.bi.warehouse.dws.transform.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.db.er.AggRuleType;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.core.db.ClickhouseTenantPolicy;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.service.DbTableSyncInfoInterface;
import com.fxiaoke.bi.warehouse.dws.service.DbTableSyncInfoInterfaceImpl;
import com.fxiaoke.bi.warehouse.dws.transform.TopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.RptRuleDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.RptViewDO;
import com.fxiaoke.bi.warehouse.dws.transform.model.BiMtRule;
import com.fxiaoke.bi.warehouse.dws.transform.model.TransformContext;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.Uninterruptibles;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class NewRptTopologyTransformer extends TopologyTransformer {

  @Resource
  private RptRuleDao rptRuleDao;
  @Resource
  private ClickhouseTenantPolicy chTenantPolicy;
  @Resource
  private DbTableSyncInfoInterfaceImpl dbTableSyncInfoInterface;

  @Override
  public TopologyTableDO transform(TransformContext context) {
    return null;
  }

  @Override
  public void upsertTopologyTable(TransformContext context) {

  }

  public StatViewPreSQL createStatViewPreSQL(StatViewPreArg statViewPreArg) {
    String tenantId = statViewPreArg.getTenantId();
    int sourceType = statViewPreArg.getSourceType();
    StatViewPreArg.UserSegmentFilter userSegmentFilter = statViewPreArg.getUserSegmentFilter();
    DbTableSyncInfoInterface.FieldAggDelayInfo detailUpdateTime = null;
    List<String> partitions = null;
    BiMtRule biMtRule = rptRuleDao.buildMtRuleByRptViewDwContext(tenantId, sourceType, statViewPreArg.getRptViewDwContext());
    if (biMtRule.getDetailSourceType() == AggRuleType.Agg.getRuleType() ||
         ((biMtRule.getDetailSourceType() == AggRuleType.OldGoal.getRuleType() || biMtRule.getDetailSourceType() == AggRuleType.MultiDimGoal.getRuleType()) &&
            GrayManager.isAllowByRule("goal_rule_detail_eis", tenantId))) {
      detailUpdateTime = dbTableSyncInfoInterface.findDetailUpdateTime(tenantId, statViewPreArg.getSourceId(), biMtRule.getMeasureFieldId(), false, statViewPreArg.getTimeZone());
      log.info("createStatViewPreSQL findDetailUpdateTime, tenantId:{},viewId:{},fieldId:{},detailUpdateTime:{},statViewPreArg:{}", tenantId, statViewPreArg.getSourceId(), biMtRule.getMeasureFieldId(), detailUpdateTime, JSON.toJSONString(statViewPreArg));
      if (detailUpdateTime == null) {
        log.error("createRptViewPreSQL detailUpdateTime is null arg:{}", statViewPreArg.toJSONString());
        return StatViewPreSQL.builder().tenantId(tenantId).build();
      }
      partitions = detailUpdateTime.getPartitions();
    }
    TopologyTable topologyTable = this.createTopologyTable(tenantId, statViewPreArg.getSourceId(), sourceType,
      statViewPreArg.findTimeZone(), biMtRule, statViewPreArg.getGoalRuleActionDateMtDetailId());
    if (topologyTable==null || CollectionUtils.isEmpty(topologyTable.getStatRuleList())) {
      log.error("createRptViewPreSQL rptRuleList is empty arg:{}", statViewPreArg.toJSONString());
      return StatViewPreSQL.builder().tenantId(tenantId).build();
    }
    topologyTable.setGoalDetailMatchLevel(statViewPreArg.getGoalDetailMatchLevel());
    topologyTable.setGoalRuleIds(statViewPreArg.getGoalRuleIds());
    if (userSegmentFilter != null) {
      topologyTableService.addUserSegmentFilterSql(tenantId, topologyTable, userSegmentFilter);
    }
    Set<String> pushDownFields = Sets.newHashSet();
    if (topologyTable.getDetailSource() == AggRuleType.Agg.getRuleType()) {
      pushDownFields = this.statViewPredicatePushDown(topologyTable, statViewPreArg.getStatFilter());
    }
    TopologyTableViewMonitor viewMonitor = topologyTable.toViewMonitor(-1, -1, null, topologyTableService.getTableKeys(topologyTable), partitions);
    return StatViewPreSQL.builder()
                         .tenantId(tenantId)
                         .statFieldLocation(topologyTable.getStatFieldLocation())
                         .sampleType(0)
                         .preViewSQL(viewMonitor.getViewSQL())
                         .fieldAggDelayInfo(detailUpdateTime)
                         .pushDownFields(pushDownFields)
                         .build();
  }

  /**
   * 创建topology
   * 多维度目标
   *
   * @param tenantId 租户id
   * @param sourceId   报表id
   */
  public void doCreateRptTopology(String tenantId, String sourceId, int source) throws Exception {
    long start = System.currentTimeMillis();
    RptViewDO rptViewDO = rptRuleDao.queryRptView(tenantId, sourceId);
    if (rptViewDO == null || rptViewDO.getIsDelete() == 1) {
      topologyTableService.deleteTopologyTableBySourceId(tenantId, sourceId);
      log.info("delete this rpt_view:{}:{}", tenantId, sourceId);
      return;
    }
    String timeZone = rptViewDO.getTimeZone();
    BiMtRule biMtRule = rptRuleDao.createRptViewMtRule(tenantId, sourceId, source);
    if (biMtRule == null) {
      return;
    }
    TopologyTable topologyTable = this.createTopologyTable(tenantId, sourceId, source, timeZone, biMtRule, null);
    if (topologyTable == null) {
      log.error("createRptView null tenantId:{},viewId:{}", tenantId, sourceId);
      return;
    }
    topologyTableService.saveTopologyTable(tenantId, sourceId, TopologyTableStatus.Calculating.getValue(),
      rptViewDO.getIsDelete(), rptViewDO.getCreator(), System.currentTimeMillis(), timeZone, topologyTable, start,
      -1, -1, null);
  }

  /**
   * @param tenantId
   * @param goalActionDateId   如果是目标查看明细,需要指定action_date的id -> BiMtDetailDO.detailId
   */
  public TopologyTable createTopologyTable(String tenantId,
                                           String sourceId,
                                           int source,
                                           String timeZone,
                                           BiMtRule biMtRule,
                                           String goalActionDateId) {
    timeZone = Constants.getTimeZoneOrDefault(timeZone, Constants.DEFAULT_TIME_ZONE);
    TopologyTable topologyTable = new TopologyTable();
    final AtomicInteger suffixNum = new AtomicInteger();
    List<TopologyTableAggRule> topologyTableAggRules = rptRuleDao.parseFromMap(tenantId, biMtRule, suffixNum, goalActionDateId);
    if (topologyTableAggRules.isEmpty()) {
      log.error("createRptView topologyTableAggRules is empty tenantId:{},viewId:{}", tenantId, sourceId);
      return null;
    }
    TopologyTableAggRule topologyTableAggRule = topologyTableAggRules.get(0);
    String biApiName = topologyTableAggRule.getRootNodeTable().getObjectDescribeApiName();
    String databaseId = rptRuleDao.findDatabaseId(tenantId, biApiName);
    String dbName = "-1".equals(tenantId) ? "${dbName}" : chTenantPolicy.getDBName(tenantId, databaseId);
    if (StringUtils.isBlank(dbName)) {
      log.error("can not find DBName tenantId:{}", tenantId);
      return null;
    }
//    List<String> commonDimList = Lists.newArrayList();
//    topologyTableAggRule.getDimConfigStringList()
//                        .forEach(dimConfigString -> commonDimList.add(dimConfigString.split(":")[2]));
    topologyTable.setTenantId(tenantId);
    topologyTable.setViewId(sourceId);
    topologyTable.setStatRuleList(topologyTableAggRules);
    topologyTable.setApiName(biApiName);
    topologyTable.setCommonDimList(Lists.newArrayList());
    topologyTable.setStatFieldLocation(topologyTableAggRule.getFieldLocationMap());
    topologyTable.setTimezone(timeZone);
    topologyTable.setDatabase(dbName);
    topologyTable.setDatabaseId(databaseId);
    topologyTable.setSource(source);
    topologyTable.setDetailSource(biMtRule.getDetailSourceType());
    topologyTable.setVersion(biMtRule.getVersion());
    return topologyTable;
  }

  /**
   * 按企业刷报表规则
   * @param tenantId
   * @param sourceType
   */
  public void flashRptViewByTenantId(String tenantId, int sourceType) {
    List<String> sourceIds = rptRuleDao.findBiMtSourceIdsByEi(tenantId, sourceType);
    if (CollectionUtils.isNotEmpty(sourceIds)) {
      sourceIds.forEach(sourceId -> {
        try {
          this.doCreateRptTopology(tenantId, sourceId, sourceType);
          Uninterruptibles.sleepUninterruptibly(20, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
          log.error("rpt batchCreateTopologyByEi-->doCreateTopology error tenantId:{},sourceId:{} ",
            tenantId, sourceId, e);
        }
      });
    }
  }
}
