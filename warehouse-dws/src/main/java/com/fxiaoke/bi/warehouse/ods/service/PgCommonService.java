package com.fxiaoke.bi.warehouse.ods.service;

import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author:jief
 * @Date:2024/3/2 ch 数据同步service
 */
@Service
public class PgCommonService {
  @Resource
  private PgCommonDao pgCommonDao;

  public int deleteTableSyncInfoByDbSyncId(List<String> dbSyncIds, List<String> tableNames){
    return pgCommonDao.deleteTableSyncInfoByDbSyncId(dbSyncIds, tableNames);
  }
}
