package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ColumnDefinition {

  private String columnName;
  private String typeName;
  
  public PGColumnType columnType() {
    switch (typeName) {
      case "_int2":
        return PGColumnType.ARRAY_Int16;
      case "_int4":
        return PGColumnType.ARRAY_Int32;
      case "_int8":
        return PGColumnType.ARRAY_Int64;
      case "_text":
      case "_varchar":
        return PGColumnType.ARRAY_String;
      case "bool":
        return PGColumnType.Boolean;
      case "bpchar":
      case "cardinal_number":
      case "character_data":
        return PGColumnType.String;
      case "float4":
      case "float8":
        return PGColumnType.Number;
      case "geography":
      case "geometry":
      case "gidx":
        return PGColumnType.String;
      case "int2":
        return PGColumnType.Int2;
      case "int4":
        return PGColumnType.Int4;
      case "int8":
        return PGColumnType.Int8;
      case "ltree":
      case "ltree_gist":
        return PGColumnType.LTree;
      case "jsonb":
      case "name":
        return PGColumnType.String;
      case "numeric":
        return PGColumnType.Number;
      case "point":
      case "sql_identifier":
      case "text":
        return PGColumnType.String;
      case "timestamp":
      case "timestamptz":
        return PGColumnType.Date;
      case "varbit":
      case "varchar":
      case "xid":
        return PGColumnType.String;
      case "yes_or_no":
        return PGColumnType.Boolean;
      default:
        return PGColumnType.String;
    }
  }
}
