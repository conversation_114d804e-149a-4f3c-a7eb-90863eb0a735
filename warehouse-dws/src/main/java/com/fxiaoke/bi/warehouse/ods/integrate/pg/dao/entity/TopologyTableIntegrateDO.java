package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity;

import com.fxiaoke.bi.warehouse.common.bean.TopologyTableAggDownStream;
import com.github.mybatis.annotation.DynamicTypeHandler;
import lombok.Data;

import javax.persistence.Column;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/4/23
 */
@Data
public class TopologyTableIntegrateDO {
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "source_id")
  private String sourceId;
  @Column(name = "source")
  private int source;
  @Column(name = "version")
  private int version;
  @Column(name = "stat_view_unique_key")
  private String statViewUniqueKey;
  @Column(name = "stat_field_location")
  @DynamicTypeHandler(value = "com.fxiaoke.bi.statistic.ch.integrate.pg.dao.mapper.handler.StatFieldLocationHandler")
  private Map<String, String> statFieldLocation;
  @Column(name = "uniq_field_location")
  @DynamicTypeHandler(value = "com.fxiaoke.bi.statistic.ch.integrate.pg.dao.mapper.handler.StatFieldLocationHandler")
  private Map<String, String> uniqFieldLocation;
  @Column(name = "all_agg_stat_field")
  @DynamicTypeHandler(value = "com.fxiaoke.bi.statistic.ch.integrate.pg.dao.mapper.handler.AllAggStatFieldHandler")
  private Map<String, String > aggAggStatField;
  @Column(name = "agg_downstream_json")
  @DynamicTypeHandler(value = "com.fxiaoke.bi.statistic.ch.integrate.pg.dao.mapper.handler.AggDownstreamHandler")
  private TopologyTableAggDownStream aggDownstreamJson;
  @Column(name = "status")
  private Integer status;
}
