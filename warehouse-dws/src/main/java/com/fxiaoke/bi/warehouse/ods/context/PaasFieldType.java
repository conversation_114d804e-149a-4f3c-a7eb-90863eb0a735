package com.fxiaoke.bi.warehouse.ods.context;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * paas 元数据类型
 */
public interface PaasFieldType {
  String SELECT_ONE = "select_one";
  String COUNTRY = "country";
  String PROVINCE = "province";
  String CITY = "city";
  String EMPLOYEE = "employee";
  String SELECT_MANY = "select_many";
  String DEPARTMENT_MANY = "department_many";
  String EMPLOYEE_MANY = "employee_many";
  String DEPARTMENT = "department";
  String OBJECT_REFERENCE = "object_reference";

  Set<String> LowCardinalityTypes = Sets.newHashSet(SELECT_ONE, COUNTRY, PROVINCE, CITY, EMPLOYEE, SELECT_MANY, DEPARTMENT_MANY, EMPLOYEE_MANY, DEPARTMENT);
}
