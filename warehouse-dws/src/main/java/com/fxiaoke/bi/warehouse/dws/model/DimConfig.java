package com.fxiaoke.bi.warehouse.dws.model;

import com.fxiaoke.bi.warehouse.common.db.er.ColumnType;
import com.fxiaoke.bi.warehouse.common.db.er.ColumnTypeConfig;
import com.fxiaoke.bi.warehouse.common.db.er.TableAliasNaming;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/3
 */
@Data
public class DimConfig {
  private String tableName;
  private String tableAlias;
  private String columnName;
  private String sourceColumnTyp;
  private String dstColumnName;
  private ColumnType dstColumnType;
  private ColumnTypeConfig sourceColumnTypeConfig;
  private ColumnTypeConfig dstColumnTypeConfig;
  private String fieldType;

  public static DimConfig parse(String config, String timeZone) {
    String[] split = config.split(":");
    DimConfig dimConfig = new DimConfig();
    String tableAlias = split[0];
    dimConfig.setTableName(TableAliasNaming.tableName(tableAlias));
    dimConfig.setTableAlias(tableAlias);
    dimConfig.setColumnName(split[1]);
    dimConfig.setDstColumnName(split[2]);
    dimConfig.setDstColumnType(ColumnType.valueOf(split[3]));
    Map<String, Object> configMap = Maps.newHashMap();
    if (split.length >= 5) {
      dimConfig.setFieldType(split[4]);
      configMap.put(ColumnTypeConfig._ActionDate.FIELD_TYPE, split[4]);
    }
    if (dimConfig.getDstColumnType() == ColumnType._ARRAY) {
      String columnName = split[2];
      String[] columnNameSplit = columnName.split("_");
      if (columnNameSplit.length == 4) {
        String itemType = columnNameSplit[2];
        switch (itemType) {
          case "int16", "int32", "int64" -> configMap.put(ColumnTypeConfig._Array.ITEM_TYPE, ColumnType._int);
          default -> configMap.put(ColumnTypeConfig._Array.ITEM_TYPE, ColumnType._String);
        }
      } else {
        configMap.put(ColumnTypeConfig._Array.ITEM_TYPE, ColumnType._String);
      }
    }
    dimConfig.setSourceColumnTypeConfig(new ColumnTypeConfig(ImmutableMap.ofEntries(configMap.entrySet().toArray(Map.Entry[]::new))));
    //如果是维度的日期字段,统一用东八区,不走多时区
    String fieldType = String.valueOf(configMap.get(ColumnTypeConfig._ActionDate.FIELD_TYPE));
    String newTimeZone = "date".equalsIgnoreCase(fieldType) ? "Asia/Shanghai" : timeZone;
    dimConfig.setDstColumnTypeConfig(new ColumnTypeConfig(Map.of(ColumnTypeConfig._ActionDate.TIMEZONE, newTimeZone)));
    return dimConfig;
  }

  /**
   * 自定义维度
   */
  public static DimConfig parse(CustomDimField customDimField, String timezone) {
    String config = customDimField.getDimensionConfigString();
    DimConfig dimConfig = parse(config, timezone);
    HashMap<String, Object> dimConfigMap = new HashMap<>() {{
      put("customDimType", customDimField.getType());
      put("customDimConfig", customDimField.getDimFormula());
      put("dstColumnType", dimConfig.getDstColumnType());
      put("sourceColumnTypeConfig", dimConfig.getSourceColumnTypeConfig());
      put("dstColumnTypeConfig", dimConfig.getDstColumnTypeConfig());
      put("udfType", dimConfig.getFieldType());
      put("fieldType", dimConfig.getFieldType());
      put("isSingle", customDimField.isSingle());
      put("pgColumnType", customDimField.getPgColumnType());
      put("tableName", dimConfig.getTableName());
    }};
    dimConfig.setSourceColumnTypeConfig(new ColumnTypeConfig(dimConfigMap));
    return dimConfig;
  }

  public String toConfigString() {
    return tableAlias + ":" + columnName + ":" + dstColumnName + ":" + dstColumnType + ":" + fieldType;
  }

  //多语
  public String toLangConfigString() {
    String langTableAlias = tableName + "_lang_" + TableAliasNaming.index(tableAlias);
    String langColumnName = columnName + Constants.COLUMN_LANG_SUFFIX;
    String langDstColumnName = columnName + Constants.COLUMN_LANG_SUFFIX;
    return langTableAlias + ":" + langColumnName + ":" + langDstColumnName + ":" + dstColumnType + ":" + fieldType;
  }

  //多语lang字段,用以监听
  public String toLangColumnConfigString() {
    String langTableAlias = tableName + "_lang_" + TableAliasNaming.index(tableAlias);
    String langColumnName = Constants.LANG;
    String langDstColumnName = Constants.LANG;
    return langTableAlias + ":" + langColumnName + ":" + langDstColumnName + ":" + ColumnType._String + ":" + "text";
  }

  /**
   * CH 报表查询明细字段
   *
   * @return
   */
  public String buildSelectColumnName() {
    return tableAlias + "." + columnName + " AS " + dstColumnName;
  }

}
