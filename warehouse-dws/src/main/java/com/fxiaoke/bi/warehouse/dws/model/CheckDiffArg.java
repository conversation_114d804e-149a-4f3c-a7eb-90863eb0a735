package com.fxiaoke.bi.warehouse.dws.model;

import lombok.Data;

import java.util.List;

/**
 * @Author: zhaomh
 * @Description:
 * @Date: Created in 2023/10/17
 * @Modified By:
 */

@Data
public class CheckDiffArg {
    public static final String check1 = "full_1";
    public static final String check2 = "full_2";
    public static final String check3 = "1_2";
    private String tenantId;
    private List<String> tenantIds;
    private List<String> schemaIds;
    private List<String> viewIds;
    private List<String> commonViewIds; //与tenantIds共用
    private List<String> tenantIdsPercent; //按企业的百分比随机抽图
    private Integer sourceType; //0统计图(默认统计图),1目标
    private String dateTimeFrom;//图更新时间
    private String themeApiName;
    private String viewId;
    private String fieldId;
    private Long checkEndTime;
    private Long checkBeginTime;
    private String check = "full_1";
    private boolean onlyDiff;
    private boolean total = true;
    private boolean cache = false;
    private boolean async = false;
    private boolean stop = false;
}
