package com.fxiaoke.bi.warehouse.dws.transform.db.mapper.provider;

import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.github.mybatis.annotation.DynamicTypeHandler;
import com.github.mybatis.handler.list.ListTypeHandler;
import com.github.mybatis.handler.set.SetTypeHandler;
import com.github.mybatis.util.EntityUtil;
import com.github.mybatis.util.PersistMeta;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import java.lang.reflect.Field;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.github.mybatis.provider.CrudProvider.FIELD_LEFT;
import static com.github.mybatis.provider.CrudProvider.FIELD_RIGHT;

/**
 * @Author:jief
 * @Date:2023/7/27
 */
@Slf4j
public class TopologyProvider {

  /**
   *
   * @param version
   * @param topologyTableDO
   * @return
   */
  public String updateWithVersion(@Param("compare_version") int version, @Param("topologyTableDO") TopologyTableDO topologyTableDO) {
    PersistMeta meta = EntityUtil.getMeta(topologyTableDO.getClass());
    Iterator<Map.Entry<String, Field>> columnsEntryIter = meta.getColumns().entrySet().iterator();
    StringBuilder updateFields = new StringBuilder();
    int i = 0;
    while (columnsEntryIter.hasNext()) {
      Map.Entry<String, Field> kv = columnsEntryIter.next();
      Field field = kv.getValue();
      if (StringUtils.equalsAny(kv.getKey(), "tenant_id", "source_id")) {
        continue;
      }
      if (i++ != 0) {
        updateFields.append(',');
      }
      updateFields.append(FIELD_LEFT).append(kv.getKey()).append(FIELD_RIGHT);
      DynamicTypeHandler typeHandler = (DynamicTypeHandler) field.getAnnotation(DynamicTypeHandler.class);
      if (typeHandler == null) {
        if (field.getType().isAssignableFrom(List.class)) {
          updateFields.append(" = #{topologyTableDO.")
                      .append(field.getName())
                      .append(",typeHandler=")
                      .append(ListTypeHandler.class.getName())
                      .append("}");
        } else if (field.getType().isAssignableFrom(Set.class)) {
          updateFields.append(" = #{ topologyTableDO.")
                      .append(field.getName())
                      .append(",typeHandler=")
                      .append(SetTypeHandler.class.getName())
                      .append("}");
        } else {
          updateFields.append(" = #{topologyTableDO.").append(field.getName()).append("}");
        }
      } else {
        updateFields.append(" = #{topologyTableDO.")
                    .append(field.getName())
                    .append(",typeHandler=")
                    .append(typeHandler.value())
                    .append("}");
      }
    }
    SQL sql = new SQL();
    sql.UPDATE("bi_mt_topology_table");
    sql.SET(updateFields.toString());
    sql.WHERE("tenant_id = #{topologyTableDO.tenantId} and source_id=#{topologyTableDO.sourceId} and version=#{compare_version}");
    return sql.toString();
  }
}
