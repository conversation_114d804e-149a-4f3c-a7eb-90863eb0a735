package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.AggRuleDO;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatViewEntity {
  private String tenantId;
  private String viewId;
  private String viewName;
  private String timeZone;
  private String schemaId;
  private String themeName;
  private boolean standalone;
  /**
   * agg集合
   */
  private List<AggRuleDO> aggRuleIds;
  /**
   * 指标作为筛选条件
   */
  private List<AggRuleDO> aggRuleFilterIds;
  /**
   * 维度集合
   */
  private List<DisplayField> dimFields;
  /**
   * 标签维度字段
   */
  private List<DisplayField> tagDimFields;
  /**
   * 所有字段fieldId
   */
  private List<AggRuleBO> allAggRules;
  /**
   * 财年类型，周，月，季，年
   * 只有联合目标分析的时候才有值
   */
  private String checkCyCle;
  /**
   * 自定义维度字段
   */
  private List<DisplayField> customDimFields;
  /**
   * 数据源id可能会用到外部数据源
   */
  private String databaseId;

  /**
   * 获取所有有效字段 fieldId
   *
   * @return 有效字段集合
   */
  public Set<String> findAllFieldIds() {
    Set<String> fieldIds = Sets.newHashSet();
    if (CollectionUtils.isNotEmpty(dimFields)) {
      dimFields.forEach(dim -> fieldIds.add(dim.getStatFieldId()));
    }
    if (CollectionUtils.isNotEmpty(aggRuleIds)) {
      aggRuleIds.forEach(aggRuleDO -> fieldIds.add(aggRuleDO.getFieldId()));
    }
    if (CollectionUtils.isNotEmpty(aggRuleFilterIds)) {
      aggRuleFilterIds.forEach(aggRuleDO -> fieldIds.add(aggRuleDO.getFieldId()));
    }
    if (CollectionUtils.isNotEmpty(tagDimFields)) {
      tagDimFields.forEach(dim -> fieldIds.add(dim.getStatFieldId()));
    }
    return fieldIds;
  }

  /**
   * 获取所有字段(指标和维度字段)包含停用的和非停用的
   *
   * @return 所有字段和状态的映射
   */
  public Map<String/*fieldId*/, Integer/*1:启用;0:停用*/> findAllAggFieldsWithStatus() {
    Map<String, Integer> fieldStatusMapper = Maps.newHashMap();
    if (CollectionUtils.isNotEmpty(this.allAggRules)) {
      allAggRules.forEach(field -> {
        AggRuleDO aggRuleDO = field.getAggRuleDO();
        if (aggRuleDO != null) {
          fieldStatusMapper.computeIfAbsent(aggRuleDO.getFieldId(), key -> field.getStatFieldStatus());
        }
      });
    } return fieldStatusMapper;
  }

  /**
   * 是否包含
   *
   * @param fieldId
   * @return
   */
  public boolean containsField(String fieldId) {
    return this.findAllFieldIds().contains(fieldId);
  }

}
