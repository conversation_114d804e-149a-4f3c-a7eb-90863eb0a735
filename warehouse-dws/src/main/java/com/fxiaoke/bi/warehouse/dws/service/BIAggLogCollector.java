package com.fxiaoke.bi.warehouse.dws.service;

import com.fxiaoke.bi.warehouse.core.db.BiAggLogCHDao;
import com.fxiaoke.bi.warehouse.core.db.entity.BiAggLogDO;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Queues;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.*;

/**
 * @Author:jief
 * @Date:2024/12/11
 */
@Slf4j
@Service
public class BIAggLogCollector implements AutoCloseable{
  @Resource
  private BiAggLogCHDao biAggLogCHDao;
  ScheduledThreadPoolExecutor schedule;
  private Queue<Pair<String, BiAggLogDO>> queue = Queues.newLinkedBlockingQueue(50000);
  private long lastSendStamp;

  @PostConstruct
  public void init(){
    ThreadFactoryBuilder builder = new ThreadFactoryBuilder();
    builder.setDaemon(true);
    builder.setNameFormat("agg-log-collector-%d");
    builder.setUncaughtExceptionHandler((t, ex) -> log.error("uncaught exception in thread: {}, ", t, ex));
    ThreadFactory factory = builder.build();
    RejectedExecutionHandler policy = new ThreadPoolExecutor.DiscardOldestPolicy();
    schedule = new ScheduledThreadPoolExecutor(2, factory, policy);
    schedule.scheduleAtFixedRate(this::batchInsertAggLog, 100, 60, TimeUnit.SECONDS);
    Runtime.getRuntime().addShutdownHook(new Thread(this::close));
  }

  private void batchInsertAggLog() {
    if (!this.queue.isEmpty()) {
      Queue<Pair<String, BiAggLogDO>> old = this.queue;
      this.queue = Queues.newLinkedBlockingQueue(50000);
      Map<String, List<BiAggLogDO>> logMapper = Maps.newHashMap();
      old.forEach((it) -> logMapper.computeIfAbsent(it.first, key -> Lists.newArrayList()).add(it.second));
      if (!logMapper.isEmpty()) {
        logMapper.forEach((k, v) -> biAggLogCHDao.batchInsertAggLog(k, v));
      }
      old.clear();
    }
  }

  public void send(String jdbcURL, BiAggLogDO value) {
    if (queue.offer(Pair.of(jdbcURL, value))) {
      lastSendStamp = System.currentTimeMillis();
    } else {
      log.warn("send clickhouse offer fail jdbcURL={}, value={}", jdbcURL, value);
    }
  }

  @Override
  @SneakyThrows({InterruptedException.class})
  public void close() {
    schedule.shutdown();
    boolean terminated = schedule.awaitTermination(10, TimeUnit.SECONDS);
    log.info("ShareCrm SenderManager terminated: {}", terminated);
  }
}
