package com.fxiaoke.bi.warehouse.core.db.mapper;

import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface StatFieldMapper extends ICrudMapper<StatFieldDO>, IBatchMapper<StatFieldDO>, ITenant<StatFieldMapper> {
  @Select("select * from stat_field where tenant_id = #{tenant_id} and field_id in (${fields}) and agg_dim_type=any" +
    "(array[#{agg_dim_type}])  and is_deleted=0 and status=any(array[#{status}]) ")
  List<StatFieldDO> queryFieldBase(@Param("tenant_id") String tenantId,
                                   @Param("fields") String fieldIds,
                                   @Param("agg_dim_type") String[] aggDimType,
                                   @Param("status") int[] status);
  /**
   * 获取停用字段
   *
   * @param tenantId
   * @param schemaId
   * @return
   */
  @Select("select * from stat_field where tenant_id = #{tenant_id} and schema_id = #{schemaId} and " +
    "agg_dim_type=any(array[#{agg_dim_type}]) and status=any(array[#{status}]) and is_deleted=0 ")
  List<StatFieldDO> queryFieldBaseBySchemaId(@Param("tenant_id") String tenantId,
                                             @Param("schemaId") String schemaId,
                                             @Param("agg_dim_type") String[] aggDimType,
                                             @Param("status") int[] status);

  /**
   * 获取日期字段的槽位信息
   *
   * @param tenantId
   * @param schemaId
   * @return
   */
  @Select("select * from stat_field where tenant_id = #{tenant_id} and schema_id = #{schemaId} and agg_dim_type='dim'" +
    " and db_field_name='action_date' and db_obj_name='agg_data' and is_deleted=0 and status in(0,1,2,3) limit 1")
  StatFieldDO queryActionDateField(@Param("tenant_id") String tenantId, @Param("schemaId") String schemaId);

  /**
   * 获取object_id字段的槽位信息
   *
   * @param tenantId
   * @param themeApiName
   * @return
   */
  @Select(
    "select * from stat_field_gray where tenant_id = #{tenant_id} and object_describe_api_name = #{themeApiName} and " +
      "db_field_name=any(array[#{dbFieldNames}]) and agg_dim_type='dim'" +
      " and is_deleted=0 and status in(1,2)")
  List<StatFieldDO> querySystemStatField(@Param("tenant_id") String tenantId,
                                         @Param("themeApiName") String themeApiName,
                                         @Param("dbFieldNames") String[] dbFieldNames);

  /**
   * 获取object_id字段的槽位信息
   *
   * @param tenantId
   * @param themeApiName
   * @return
   */
  @Select(
    "select sf.* from stat_field sf,stat_schema ss where sf.tenant_id=ss.tenant_id and sf.tenant_id = #{tenant_id} " +
      " and sf.schema_id = ss.schema_id  and ss.schema_en_name=#{themeApiName} and sf.agg_dim_type='dim'" +
      " and sf.field_location='object_id' and sf.db_obj_name='dim_data' and sf.is_deleted=0 and sf.status in(1,2) " +
      " and ss.status=1 and ss.is_deleted=0 limit 1")
  StatFieldDO queryObjectIdFieldByApiName(@Param("tenant_id") String tenantId,
                                          @Param("themeApiName") String themeApiName);
  @Select("select b.* from dim_rule a,stat_field b" +
    "    where a.tenant_id = #{tenantId} and a.theme_api_name = #{themeApiName}\n" +
    "      and a.tenant_id= b.tenant_id and b.schema_id = #{schemaId}\n" +
    "      and b.db_field_name in (${dbFieldNames})  and a.is_deleted = 0\n" +
    "      and a.field_id = b.field_id and b.status in(1,2,6) AND b.agg_dim_type = 'dim'\n" +
    "      and b.is_deleted = 0")
  List<StatFieldDO> queryDimFieldByFieldName(@Param("tenantId") String tenantId,
                                             @Param("dbFieldNames") String dbFieldNames,
                                             @Param("schemaId") String schemaId,
                                             @Param("themeApiName") String themeApiName);
  @Select("""
    select b.* from stat_field b
    where b.tenant_id = #{tenantId} and b.schema_id = #{schemaId}
      and b.db_field_name in (${dbFieldNames})
      and b.status in(1,2,6) AND b.agg_dim_type = 'dim'
      and b.is_deleted = 0
    """)
  List<StatFieldDO> queryDimFieldByFieldNamePlus(@Param("tenantId") String tenantId,
                                             @Param("dbFieldNames") String dbFieldNames,
                                             @Param("schemaId") String schemaId);

  @Select("select sf.* from stat_schema ss " +
    "inner join stat_field sf  on ss.tenant_id = sf.tenant_id and ss.schema_id = sf.schema_id " +
    "inner join agg_rule ar on sf.tenant_id=ar.tenant_id and sf.field_id=ar.field_id " +
    "where ar.tenant_id=#{tenantId} and ar.rule_id=any(array[#{ruleIds}]) and sf.status=any(array[#{status}]) " +
    "and ss.status in (1,2,6) and sf.agg_dim_type in ('agg','base_agg','goal_agg') " +
    "and sf.is_deleted=0 and ar.is_deleted=0 ")
  List<StatFieldDO> batchQueryAggRuleByRuleIdAndStatus(@Param("tenantId") String tenantId,
                                                       @Param("ruleIds") String[] ruleIds,
                                                       @Param("status") int[] status);

  @Select("select sf.field_id from stat_field sf,stat_schema ss where sf.tenant_id = #{tenantId} and ss" +
    ".schema_en_name=#{themeApiName} and sf.db_field_name = #{dbFieldName}" +
    "  and sf.tenant_id=ss.tenant_id and sf.schema_id = ss.schema_id  and sf.agg_dim_type = #{aggDimType} and sf" +
    ".status in (1,2,6) and sf.is_deleted=0 and ss.is_deleted=0 limit 1")
  String queryFieldIdByFieldNameObjectNameAggDimType(@Param("tenantId")String tenantId,
                                                     @Param("themeApiName") String objectDescribeApiName,
                                                     @Param("dbFieldName") String dbFieldName,
                                                     @Param("aggDimType") String aggDimType);

  @Select("select * from stat_field where tenant_id = #{tenantId} and agg_dim_type = 'downstream_agg' " +
    "and downstream_view_id = any(array[#{downstreamViewIds}]) and status in(1,2,6) and is_deleted=0")
  List<StatFieldDO> batchQueryStatFieldByDownStreamViewIds(@Param("tenantId") String tenantId,
                                                           @Param("downstreamViewIds") String[] downstreamViewIds);

  @Insert("INSERT INTO stat_field" +
    " (tenant_id, type, is_deleted, field_order, agg_dim_type, object_describe_api_name, schema_id, db_obj_name, " +
    " field_id, is_pre, is_hide, creator, last_modified_time, is_key, create_time, is_level, ref_obj_name, is_show, " +
    " field_name, last_modifier, sub_field_type, format_str, db_field_name, field_type, status, downstream_field_id, " +
    " downstream_view_id, downstream_is_null_action_date)" +
    " VALUES(#{tenantId}, #{type}, #{isDeleted}, -1, #{aggDimType}, #{objectDescribeApiName}, #{schemaId}, #{dbObjName}, " +
    " #{fieldId}, 0, 0, '-10000', now(), 0, now(), 0, '', 1, #{fieldName}, '-10000', '', #{formatStr}, #{dbFieldName}, " +
    " #{fieldType}, #{status}, #{downstreamFieldId}, #{downstreamViewId}, #{downstreamIsNullActionDate})" +
    " ON CONFLICT (tenant_id, field_id)" +
    " DO UPDATE SET type = EXCLUDED.type, agg_dim_type = EXCLUDED.agg_dim_type, is_deleted = EXCLUDED.is_deleted," +
    " object_describe_api_name = EXCLUDED.object_describe_api_name, " +
    " schema_id = EXCLUDED.schema_id, db_obj_name = EXCLUDED.db_obj_name, field_name = EXCLUDED.field_name, " +
    " format_str = EXCLUDED.format_str, db_field_name = EXCLUDED.db_field_name, field_type = EXCLUDED.field_type, " +
    " status = EXCLUDED.status, downstream_field_id = EXCLUDED.downstream_field_id, downstream_view_id = EXCLUDED.downstream_view_id, " +
    " downstream_is_null_action_date = EXCLUDED.downstream_is_null_action_date")
  int upsertStatFieldByFieldId(@Param("tenantId") String tenantId,
                               @Param("type") String type,
                               @Param("isDeleted") int isDeleted,
                               @Param("aggDimType") String aggDimType,
                               @Param("objectDescribeApiName") String objectDescribeApiName,
                               @Param("schemaId") String schemaId,
                               @Param("dbObjName") String dbObjName,
                               @Param("fieldId") String fieldId,
                               @Param("fieldName") String fieldName,
                               @Param("formatStr") String formatStr,
                               @Param("dbFieldName") String dbFieldName,
                               @Param("fieldType") String fieldType,
                               @Param("status") int status,
                               @Param("downstreamFieldId") String downstreamFieldId,
                               @Param("downstreamViewId") String downstreamViewId,
                               @Param("downstreamIsNullActionDate") String downstreamIsNullActionDate);

  /**
   * 通过上游企业viewId查询同步虚拟指标
   */
  @Select("<script>" +
    "SELECT * FROM stat_field " +
    "WHERE tenant_id = #{tenantId} " +
    "AND agg_dim_type = 'downstream_agg' " +
    "AND downstream_view_id IN " +
    "<foreach item='viewId' index='index' collection='downstreamViewIds' open='(' separator=',' close=')'>" +
    "#{viewId}" +
    "</foreach>" +
    "</script>")
  List<StatFieldDO> findStatFieldList(@Param("tenantId") String tenantId, @Param("downstreamViewIds") List<String> downstreamViewIds);

  /**
   * 获取1端所有虚拟指标
   * @param tenantId
   * @return
   */
  @Select("<script>" +
    "select * from stat_field where tenant_id = #{tenant_id} and agg_dim_type='downstream_agg' and is_deleted=0 " +
    "<if test='fieldIds != null'>" +
    "  and field_id=any(array[#{fieldIds}])" +
    "</if>" +
    "</script>")
  List<StatFieldDO> queryDownStreamFields(@Param("tenant_id") String tenantId, @Param("fieldIds") String[] fieldIds);

  /**
   * 获取1端虚拟指标
   */
  @Select("SELECT * FROM stat_field WHERE tenant_id = #{tenantId} AND agg_dim_type = 'downstream_agg' AND is_deleted = 0")
  List<StatFieldDO> getVirtualStatFieldList(@Param("tenantId") String tenantId);
}
