package com.fxiaoke.bi.warehouse.ods.integrate.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.ods.entity.*;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.AggDataSyncInfoDao;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.BiDataSyncPolicyLogDao;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.BiDataSyncPolicyLogDo;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.*;
import com.fxiaoke.bi.warehouse.ods.integrate.service.AggDataSyncInfoService;
import com.fxiaoke.bi.warehouse.ods.integrate.util.SynchronizationDataUtil;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bi.warehouse.ods.service.CHClientService;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.bi.warehouse.ods.service.CHNodeService;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.ResultSetMetaData;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AggDataSyncInfoServiceImpl implements AggDataSyncInfoService {

    @Resource
    @Qualifier("newAggDataSyncInfoDao")
    private AggDataSyncInfoDao aggDataSyncInfoDao;

    @Resource
    private CHNodeService chNodeService;

    @Resource
    private CHClientService chClientService;

    @Resource
    private CHDataSource chDataSource;
    @Resource
    private CHRouterPolicy chRouterPolicy;
    @Resource
    private CHDBService chdbService;

    @Resource
    private BiDataSyncPolicyLogDao biDataSyncPolicyLogDao;

    private static final String incrementSubSQL="SELECT hash_code FROM agg_data WHERE tenant_id = '%s' AND view_id = '%s' AND timestamp >= '%s' AND timestamp < '%s' AND view_version = '%s'";
    /**
     * 根据agg_data_sync进行数据同步
     */
    @Override
    public long synchronizationAggDataToAggDownStreamData(String upStreamTenantId,
                                                          AggDataSyncInfoDo aggDataSyncInfoDo,
                                                          List<String> fieldList,
                                                          Map<String, String> downToUpMap,
                                                          boolean isFullSynchronization,
                                                          RouterInfo upRouterInfo,
                                                          RouterInfo downStreamRouterInfo,
                                                          String objectId,
                                                          long currentTimeMillis,
                                                          long nextBatchNum,
                                                          String viewId,
                                                          String policyId,String partitionName) {
        SynchronizationWriteArg synchronizationWriteArg = SynchronizationWriteArg.getSynchronizationWriteArg(upStreamTenantId, upRouterInfo, upRouterInfo.getDbName(), fieldList, downToUpMap, viewId, policyId);
        SynchronizationReadSqlArg synchronizationReadSqlArg = SynchronizationReadSqlArg.getSynchronizationReadSqlArg(aggDataSyncInfoDo, fieldList, downToUpMap, upStreamTenantId, downStreamRouterInfo, objectId, isFullSynchronization, currentTimeMillis, nextBatchNum, policyId);
        return synchronizationAggData(synchronizationWriteArg, synchronizationReadSqlArg,partitionName);
    }

    public ClickhouseTable createDownstreamTable(SynchronizationWriteArg writeArg, SynchronizationReadSqlArg readSqlArg,String partitionName){
        List<String> fieldList = readSqlArg.getFieldList();
        List<String> orderByColumns = Lists.newArrayList("tenant_id", "view_id", "view_version", "object_id","bi_sys_flag" ,"hash_code");
        Map<String, String> downToUpMap = readSqlArg.getDownToUpMap();
        Map<String, String> fieldToTypeMap = Maps.newHashMap();
        for (String fieldName : fieldList) {
            fieldToTypeMap.put(fieldName, SynchronizationDataUtil.getFiledTypes(fieldName));
        }
        List<ClickhouseColumn> columnList= Lists.newArrayList();
        columnList.add(ClickhouseColumn.builder().name("tenant_id").typeName("String").type(ClickHouseColumnType.STRING).build());
        if (GrayManager.isAllowByRule("biDataSyncPolicyGray", readSqlArg.getUpStreamTenantId())) {
            columnList.add(ClickhouseColumn.builder().name("policy_id").typeName("String").type(ClickHouseColumnType.STRING).build());
            orderByColumns.add(1,"policy_id");
        }
        columnList.add(ClickhouseColumn.builder().name("view_id").typeName("String").type(ClickHouseColumnType.STRING).build());
        columnList.add(ClickhouseColumn.builder().name("view_version").typeName("UInt32").type(ClickHouseColumnType.LONG).build());
        columnList.add(ClickhouseColumn.builder().name("object_id").typeName("String").type(ClickHouseColumnType.STRING).build());
        columnList.add(ClickhouseColumn.builder().name("action_date").typeName("String").type(ClickHouseColumnType.STRING).build());
        columnList.add(ClickhouseColumn.builder().name("hash_code").typeName("UInt64").type(ClickHouseColumnType.UINT64).build());
        columnList.add(ClickhouseColumn.builder().name("bi_sys_flag").typeName("Int8").type(ClickHouseColumnType.INT).build());
        columnList.add(ClickhouseColumn.builder().name("bi_sys_batch_id").typeName("Int64").type(ClickHouseColumnType.UINT64).build());
        if (StringUtils.isNotBlank(partitionName)) {
            columnList.add(ClickhouseColumn.builder()
                                           .name("bi_sys_ods_part")
                                           .typeName("String")
                                           .type(ClickHouseColumnType.STRING)
                                           .build());
        }
        for (Map.Entry<String, String> entry : fieldToTypeMap.entrySet()) {
            String columnName = downToUpMap.get(entry.getKey());
            if (StringUtils.isBlank(columnName)) {
                continue;
            }
            if (StringUtils.equals(entry.getValue(), "AggregateFunction(uniqExact, Nullable(String))")) {
                columnList.add(ClickhouseColumn.builder().name(downToUpMap.get(entry.getKey())).typeName("String").type(ClickHouseColumnType.STRING).build());
            } else {
                columnList.add(ClickhouseColumn.builder().name(downToUpMap.get(entry.getKey())).typeName(entry.getValue()).type(ClickHouseColumnType.STRING).build());
            }
        }
        return ClickhouseTable.builder()
                              .name(Constants.AGG_DOWNSTREAM_DATA)
                              .db(writeArg.getDbName())
                              .dbURL(writeArg.findMustUrl())
                              .columnList(columnList)
                              .orderByColumns(orderByColumns)
                              .build();
    }
    /**
     * 全量/增量同步下游agg_data
     */
    public long synchronizationAggData(SynchronizationWriteArg writeArg, SynchronizationReadSqlArg readSqlArg,String partitionName) {
        log.info("开始进行全量同步下游agg_data数据,writeArg:{}, readSqlArg:{}", JSON.toJSON(writeArg), JSON.toJSON(readSqlArg));
        long start= System.currentTimeMillis();
        AtomicLong nums= new AtomicLong(0L);
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chRouterPolicy.getChJdbcURL(readSqlArg.getRouterInfo(), true), 7200000L)) {
            ClickhouseTable clickhouseTable= this.createDownstreamTable(writeArg,readSqlArg,partitionName);
            Biz2CHConsumer biz2CHConsumer = Biz2CHConsumer.getInstance(chClientService, clickhouseTable, 10000, readSqlArg.getBiSysBatchId(), chNodeService, chdbService, clickhouseTable.getOrderByColumns(), 100);
            log.info("createInsertSQL4AggData sql:{}",clickhouseTable.createInsertSQL4AggData());
            //从下游agg_data全量和增量同步到agg_downstream_data
            String sqlTemplate = this.toTransferReadSqlV2(readSqlArg,partitionName);
            log.info("query sql template :{}", sqlTemplate);
            AtomicBoolean atomicBoolean = new AtomicBoolean(false);
            AtomicLong resultCount = new AtomicLong();
            try{
                do {
                    jdbcConnection.query(String.format(sqlTemplate,readSqlArg.getAggDataHashCode()), resultSet -> {
                        atomicBoolean.set(true);
                        resultCount.set(0);
                        while (resultSet.next()) {
                            Map<String, Object> logMap = Maps.newLinkedHashMap();
                            ResultSetMetaData metaData = resultSet.getMetaData();
                            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                                String columnName = metaData.getColumnName(i);
                                if (StringUtils.equals("view_id", columnName)) {
                                    logMap.put(columnName, writeArg.getViewId());
                                } else {
                                    logMap.put(columnName, resultSet.getObject(columnName));
                                }
                            }
                            BizLog bizLog = new BizLog(logMap);
                            try {
                                if (readSqlArg.isFullSynchronization()) {
                                    biz2CHConsumer.queue(bizLog,true,false);
                                } else {
                                    biz2CHConsumer.queue(bizLog,false,StringUtils.isBlank(partitionName));
                                }
                                nums.incrementAndGet();
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                            resultCount.getAndIncrement();
                            //记录同步的最后一次同步的hash_code
                            readSqlArg.setAggDataHashCode(resultSet.getBigDecimal("hash_code").toString());
                        }
                    });
                    //同步一页的数量小于同步sql一页的页数的时候说明已经同步完成了，到最后一页了
                    if (resultCount.get() < 800) {
                        atomicBoolean.set(false);
                    }
                } while (atomicBoolean.get());
                biz2CHConsumer.save();
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                biz2CHConsumer.closeIfNeed();
            }
            log.info("全量同步下游agg_data数据完成 upEI:{},downEI:{},viewId:{},batchId:{},cost:{}", readSqlArg.getUpStreamTenantId(),
              readSqlArg.getDownStreamTenantId(),writeArg.getViewId(), readSqlArg.getBiSysBatchId(), (
              System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("synchronization downStreamTenant agg_data error", e);
            throw new RuntimeException(e);
        }
        return nums.get();
    }

    /**
     * 在下游图表重新计算的的情况下，agg_downstream_data插入before数据，清空agg_downstream_data数据
     */
    @Override
    public void insertBeforeAggDownStreamData(RouterInfo upStreamTenantIdRouterInfo, AggDataSyncInfoDo aggDataSyncInfoDo, int version, String statViewUniqueKey, String objectId, long nextBatchNum, String policyId,String partitionName) {
        String dbName = upStreamTenantIdRouterInfo.getDbName();
        String tenantId = upStreamTenantIdRouterInfo.getTenantId();
        if (aggDataSyncInfoDo.getBatchNum() != 0 && (!StringUtils.equals(aggDataSyncInfoDo.getStatViewUniqueKey(), statViewUniqueKey) || !Objects.equals(aggDataSyncInfoDo.getViewVersion(), version))) {
            String insertColumn = "tenant_id, policy_id, view_id, view_version, object_id, hash_code, bi_sys_flag, bi_sys_batch_id, bi_sys_is_deleted, bi_sys_version, is_deleted,action_date";
            String selectColumn="";
            String aggDownStreamSqlTemplateSql = """
              INSERT INTO %s.agg_downstream_data (%s)
              select before.tenant_id    AS tenant_id
                   , before.policy_id    AS policy_id
                   , before.view_id      AS view_id
                   , before.view_version AS view_version
                   , before.object_id    AS object_id
                   , before.hash_code    AS hash_code
                   , %d                  AS bi_sys_flag
                   , %d                  AS bi_sys_batch_id
                   , toUInt8(0)          AS bi_sys_is_deleted
                   , now()               AS bi_sys_version
                   , %d                  AS is_deleted
                   , before.action_date  AS action_date
                   %s
              from (
                  SELECT add.tenant_id
                       , add.policy_id
                       , add.view_id
                       , add.view_version
                       , add.object_id
                       , add.hash_code
                       , add.action_date
                  FROM %s.agg_downstream_data add
                  WHERE (add.tenant_id = '%s' AND add.policy_id = '%s' AND add.view_id = '%s' AND add.view_version = %d AND add.object_id = '%s' AND add.bi_sys_flag = 1 AND add.is_deleted=0)
                  ) before
                  SETTINGS final = 1
                 , do_not_merge_across_partitions_select_final = 1
                 , optimize_move_to_prewhere_if_final = 1
                 , mutations_sync = 1;
              """;
            if (StringUtils.isNotBlank(partitionName)) {
                insertColumn = insertColumn + ",bi_sys_ods_part";
                selectColumn = ",'" + partitionName + "' AS bi_sys_ods_part";
            }
            String insertBeforeSql = String.format(aggDownStreamSqlTemplateSql, dbName,insertColumn, 0, nextBatchNum, 0,selectColumn, dbName, tenantId, policyId, aggDataSyncInfoDo.getViewId(), aggDataSyncInfoDo.getViewVersion(), objectId);
            String insertAfterSql = String.format(aggDownStreamSqlTemplateSql, dbName, insertColumn,1, nextBatchNum, -1,selectColumn, dbName, tenantId, policyId, aggDataSyncInfoDo.getViewId(), aggDataSyncInfoDo.getViewVersion(), objectId);
            try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chRouterPolicy.getChJdbcURL(upStreamTenantIdRouterInfo, true), 7200000L)) {
                if (StringUtils.isNotBlank(partitionName)) {
                    jdbcConnection.executeUpdate(insertAfterSql);
                } else {
                    jdbcConnection.executeUpdate(insertBeforeSql);
                    jdbcConnection.executeUpdate(insertAfterSql);
                }
            } catch (Exception e) {
                log.error("refreshSyncAggDownStreamData error tenantId:{}, view_id:{}, view_version:{}, object_id:{}", upStreamTenantIdRouterInfo.getTenantId(), aggDataSyncInfoDo.getViewId(), aggDataSyncInfoDo.getViewVersion(), objectId, e);
                throw new RuntimeException("refreshSyncAggDownStreamData error", e);
            }
            //更新agg_data_sync_info,使用最新的进行全量同步
            aggDataSyncInfoDo.setBatchNum(0L);
        }
    }


    /**
     * 同步before数据
     */
    public void synchronizationBeforeAggData(SynchronizationBeforeAggDataArg downStreamArg, SynchronizationInsertBeforeAggDataArg upStreamArg) {
        log.info("开始进行下游before数据,downStreamArg:{}, upStreamArg:{}", JSON.toJSON(downStreamArg), JSON.toJSON(upStreamArg));
        long start= System.currentTimeMillis();
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chRouterPolicy.getChJdbcURL(downStreamArg.getRouterInfo(), true), 7200000L);
             JdbcConnection jdbcConnectionUp = chDataSource.getJdbcConnection(chRouterPolicy.getChJdbcURL(upStreamArg.getRouterInfo(), true), 7200000L)) {
            AtomicLong hashCodeCount = new AtomicLong();
            AtomicBoolean beforeBoolean = new AtomicBoolean(false);
            String sqlTemplate = this.toTransferBeforeDownStreamAggDataSql(downStreamArg);
            log.info("query beforeSql template :{}", sqlTemplate);
            //插入before数据
            do {
                List<String> hashCodeList = Lists.newArrayList();
                jdbcConnection.query(String.format(sqlTemplate, downStreamArg.getAggDataHashCode()), hashCodeResult -> {
                    beforeBoolean.set(true);
                    hashCodeCount.set(0);
                    while (hashCodeResult.next()) {
                        hashCodeList.add(hashCodeResult.getBigDecimal("hash_code").toString());
                        downStreamArg.setAggDataHashCode(hashCodeResult.getBigDecimal("hash_code").toString());
                        hashCodeCount.getAndIncrement();
                    }
                    if (CollectionUtils.isNotEmpty(hashCodeList)) {
                        //将查询的hash_code反查agg_downstream_data，插入before数据
                        upStreamArg.setHashCodeList(hashCodeList);
                        String beforeDataSql = toTransferBeforeSql(upStreamArg);
                        jdbcConnectionUp.executeUpdate(beforeDataSql);
                    }
                });
                if (hashCodeCount.get() < 100) {
                    beforeBoolean.set(false);
                }
            } while (beforeBoolean.get());
            log.info("开始进行下游before数据完成,downStreamArg:{}, upStreamArg:{}, cost:{}", JSON.toJSON(downStreamArg), JSON.toJSON(upStreamArg), System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("synchronization before agg_data error", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据tenantId, viewId查询ch agg_data_sync_info信息,如果没有查询到就生成一个新的agg_data_sync_info,查询到了取第一个
     */
    @Override
    public AggDataSyncInfoDo queryAggDataSyncInfoByTenantIdAndViewId(RouterInfo upStreamTenantIdRouterInfo, String tenantId, String viewId) {
        //需要检测agg_data_sync_info表是否存在，如果不存在需要进行创建
        aggDataSyncInfoDao.createAggDataSyncInfo(upStreamTenantIdRouterInfo);
        List<AggDataSyncInfoDo> aggDataSyncInfoDoList = aggDataSyncInfoDao.queryAggDataSyncInfoByTenantIdAndViewId(upStreamTenantIdRouterInfo.getTenantId(), tenantId, viewId);
        if (CollectionUtils.isEmpty(aggDataSyncInfoDoList)) {
            return getAggSyncInfoDo(tenantId, viewId);
        }
        Optional<AggDataSyncInfoDo> aggDataSyncInfoDoOptional = aggDataSyncInfoDoList.stream().findFirst();
        return aggDataSyncInfoDoOptional.orElseGet(() -> getAggSyncInfoDo(tenantId, viewId));
    }

    @Override
    public AggDataSyncInfoDo queryAggDataSyncInfoByPolicyId(String upTenantId, String downTenantId, String viewId, String policyId, String statViewUniqueKey) {
        List<AggDataSyncInfoDo> aggDataSyncInfoDoList = aggDataSyncInfoDao.queryAggDataSyncInfoByPolicyId(upTenantId, downTenantId, viewId, policyId);
        if (CollectionUtils.isEmpty(aggDataSyncInfoDoList)) {
            return getAggSyncInfoDoByPolicyId(downTenantId, viewId, policyId, statViewUniqueKey);
        }
        Optional<AggDataSyncInfoDo> aggDataSyncInfoDoOptional = aggDataSyncInfoDoList.stream().findFirst();
        return aggDataSyncInfoDoOptional.orElseGet(() -> getAggSyncInfoDoByPolicyId(downTenantId, viewId, policyId, statViewUniqueKey));
    }

    @Override
    public List<AggDataSyncInfoDo> queryAggDataSyncInfoByPolicyId(String upTenantId, List<String> policyIdList) {
        if (CollectionUtils.isEmpty(policyIdList)) {
            return Lists.newArrayList();
        }
        return aggDataSyncInfoDao.queryAggDataSyncInfoByPolicyIds(upTenantId, policyIdList);
    }

    /**
     * 修改agg_data_sync_info状态根据传入的status
     */
    @Override
    public void updateAggDataSyncInfo(AggDataSyncInfoDo aggDataSyncInfoDo, SyncStatusEnum syncStatusEnum) {
        aggDataSyncInfoDo.setStatus(syncStatusEnum.getStatus());
        aggDataSyncInfoDao.updateAggDataSyncInfoStatus(aggDataSyncInfoDo);
    }

    @Override
    public void updateAggDataSyncInfoByInsert(String upStreamTenantId, AggDataSyncInfoDo aggDataSyncInfoDo, SyncStatusEnum syncStatusEnum) {
        aggDataSyncInfoDo.setTimestamp(new Date());
        aggDataSyncInfoDo.setStatus(syncStatusEnum.getStatus());
        aggDataSyncInfoDao.updateAggDataSyncInfoStatusByInsert(upStreamTenantId, aggDataSyncInfoDo);
    }

    public void updateAggDataSyncInfoBatch(String upStreamTenantId, List<AggDataSyncInfoDo> aggDataSyncInfoDos) {
        aggDataSyncInfoDos.forEach(x -> {
            x.setTimestamp(new Date());
            x.setIsDeleted(1);
        });
        aggDataSyncInfoDao.updateAggDataSyncInfoBatch(upStreamTenantId, aggDataSyncInfoDos);
    }

    @Override
    public void updateAggDataSyncInfoByPolicyId(String upStreamTenantId, AggDataSyncInfoDo aggDataSyncInfoDo, SyncStatusEnum syncStatusEnum) {
        aggDataSyncInfoDo.setTimestamp(new Date());
        aggDataSyncInfoDo.setStatus(syncStatusEnum.getStatus());
        aggDataSyncInfoDao.updateAggDataSyncInfoStatusByPolicy(upStreamTenantId, aggDataSyncInfoDo);
    }

    @Override
    public void insertBiDataSyncPolicyLog(String tenantId, String sourceTenantId, String policyId, String logType, String msg) {
        BiDataSyncPolicyLogDo biDataSyncPolicyLogDo = new BiDataSyncPolicyLogDo();
        biDataSyncPolicyLogDo.setPolicyId(policyId);
        biDataSyncPolicyLogDo.setId(ObjectId.get().toString());
        biDataSyncPolicyLogDo.setTenantId(tenantId);
        biDataSyncPolicyLogDo.setSourceTenantId(sourceTenantId);
        biDataSyncPolicyLogDo.setLogType(logType);
        biDataSyncPolicyLogDo.setMsg(msg);
        biDataSyncPolicyLogDo.setTimestamp(new Date());
        biDataSyncPolicyLogDo.setIsDeleted(0);
        biDataSyncPolicyLogDao.insertBiDataSyncPolicyLog(biDataSyncPolicyLogDo);
    }

    /**
     * 写sql，将下游agg_data数据写入agg_downStream_data
     */
    public String toTransferWriteSql(List<String> fieldList, String dbName) {
        Map<String, String> fieldToTypeMap = Maps.newHashMap();
        for (String fieldName : fieldList) {
            fieldToTypeMap.put(fieldName, SynchronizationDataUtil.getFiledTypes(fieldName));
        }
        List<String> selectIntoColumns = Lists.newArrayList("tenant_id", "view_id", "view_version", "object_id", "action_date", "hash_code", "bi_sys_flag", "bi_sys_batch_id");
        List<String> selectColumns = Lists.newArrayList("tenant_id", "view_id", "view_version", "object_id", "action_date", "hash_code", "bi_sys_flag", "bi_sys_batch_id");
        List<String> inputColumns = Lists.newArrayList("tenant_id String", "view_id String", "view_version UInt32", "object_id String", "action_date String", "hash_code UInt64", "bi_sys_flag Int8", "bi_sys_batch_id Int64");
        for (Map.Entry<String, String> entry : fieldToTypeMap.entrySet()) {
            if (StringUtils.equals(entry.getKey(), entry.getValue())) {
                continue;
            }
            String fieldName = entry.getKey();
            String fieldType = entry.getValue();
            if (StringUtils.equals(fieldType, "AggregateFunction(uniqExact, Nullable(String))")) {
                selectColumns.add("cast(unhex(" + fieldName + "),'AggregateFunction(uniqExact, Nullable(String))')");
                inputColumns.add(fieldName + " String");
            } else {
                selectColumns.add(fieldName);
                inputColumns.add(fieldName + " " + fieldType);
            }
            selectIntoColumns.add("ds_" + fieldName);
        }
        String selectIntoClause = String.join(",", selectIntoColumns);
        String selectClause = String.join(",", selectColumns);
        String inputClause = String.join(",", inputColumns);
        return String.format("INSERT INTO %s.agg_downstream_data(%s) SELECT %s FROM input('%s')", dbName, selectIntoClause, selectClause, inputClause);
    }

    /**
     * 读sql
     */
    public String toTransferReadSql(SynchronizationReadSqlArg arg) {
        List<String> fieldList = arg.getFieldList();
        Map<String, String> fieldToTypeMap = Maps.newHashMap();
        for (String fieldName : fieldList) {
            fieldToTypeMap.put(fieldName, SynchronizationDataUtil.getFiledTypes(fieldName));
        }
        //最终select字段
        List<String> selectReadColumns = Lists.newArrayList();
        selectReadColumns.add("'" + arg.getUpStreamTenantId() + "' as tenant_id");
        selectReadColumns.add("'" + arg.getViewId() + "' as view_id");
        selectReadColumns.add(arg.getVersion() + " as view_version");
        selectReadColumns.add("'" + arg.getObjectId() + "'" +  " as object_id");
        selectReadColumns.add("action_date as action_date");
        selectReadColumns.add("1 as bi_sys_flag");
        selectReadColumns.add(arg.getBiSysBatchId() + " as bi_sys_batch_id");
        selectReadColumns.add("cityHash64(action_date) as hash_code");

        //第二层的sql
        List<String> selectSecondColumns = Lists.newArrayList();
        selectSecondColumns.add("action_date as action_date");

        //内部select字段
        List<String> selectColumns = Lists.newArrayList("any(action_date) as action_date");
        for (Map.Entry<String, String> entry : fieldToTypeMap.entrySet()) {
            String fieldName = entry.getKey();
            String fieldType = entry.getValue();
            if (StringUtils.equals(fieldName, fieldType)) {
                continue;
            }
            selectReadColumns.add(fieldName + " as " + fieldName);
            if (StringUtils.equals(fieldType, "AggregateFunction(uniqExact, Nullable(String))")) {
                selectSecondColumns.add("hex(uniqExactMergeState(" + fieldName + ")) as " + fieldName);
                selectColumns.add("argMaxIf(" + fieldName + ", timestamp, " + fieldName + "_merge) as " + fieldName);
            } else {
                selectSecondColumns.add("sum(" + fieldName + ") as " + fieldName);
                selectColumns.add("argMax(" + fieldName + ", timestamp) as " + fieldName);
            }
        }

        //where查询字段
        List<String> whereColumns = Lists.newArrayList("action_date >= '%s'");

        //构建最外层select语句
        StringBuilder selectReadClause = new StringBuilder();
        for (String ReadColumn : selectReadColumns) {
            if (selectReadClause.length() > 0) {
                selectReadClause.append(",\n");
            }
            selectReadClause.append(ReadColumn);
        }

        //构建第二层select语句
        StringBuilder selectSecondClause = new StringBuilder();
        for (String secondColumn : selectSecondColumns) {
            if (selectSecondClause.length() > 0) {
                selectSecondClause.append(",\n");
            }
            selectSecondClause.append(secondColumn);
        }

        //构建内部select子句
        StringBuilder selectClause = new StringBuilder();
        for (String selectColumn : selectColumns) {
            if (selectClause.length() > 0) {
                selectClause.append(",\n");
            }
            selectClause.append(selectColumn);
        }

        //构建where子句
        StringBuilder whereClause = new StringBuilder();
        for (String condition : whereColumns) {
            if (whereClause.length() > 0) {
                whereClause.append("\n  AND ");
            }
            whereClause.append(condition);
        }

        //构建子查询语句
        String whereSubQuery = arg.isFullSynchronization() ?
          String.format("SELECT hash_code FROM agg_data WHERE tenant_id = '%s' AND view_id = '%s' AND view_version = '%s'", arg.getDownStreamTenantId(), arg.getViewId(), arg.getVersion()) :
          String.format("SELECT hash_code FROM agg_data WHERE tenant_id = '%s' AND view_id = '%s' AND timestamp >= '%s' AND timestamp < '%s' AND view_version = '%s'", arg.getDownStreamTenantId(), arg.getViewId(), SynchronizationDataUtil.getTimestampFormat(arg.getLastTimeStamp()), SynchronizationDataUtil.getTimestampFormat(arg.getTimeStamp()), arg.getVersion());

        //构建查询语句
        return String.format("SELECT %s\n" +
                             "FROM (\n" +
                                     "SELECT %s\n" +
                                     "FROM (\n" +
                                             "SELECT %s\n" +
                                             "FROM agg_data\n" +
                                             "WHERE tenant_id = '%s' and view_id = '%s' and view_version = '%s' and hash_code IN (%s)\n" +
                                             "GROUP BY hash_code\n" +
                                           ")\n" +
                            "GROUP BY action_date)\n" +
                            "WHERE %s\n" +
                            "ORDER BY action_date\n" +
                            "LIMIT 100\n" +
                            "SETTINGS max_bytes_before_external_sort = 2147483648,max_bytes_before_external_group_by = 2147483648,group_by_two_level_threshold_bytes = 1073741824,optimize_aggregation_in_order = 1;"
                            ,selectReadClause, selectSecondClause, selectClause, arg.getDownStreamTenantId(), arg.getViewId(), arg.getVersion(), whereSubQuery, whereClause);
    }

    /**
     * 读下游agg_data数据sql模版 增量/全量同步
     */
    public String toTransferReadSqlV2(SynchronizationReadSqlArg arg,String partitionName) {
        List<String> fieldList = arg.getFieldList();
        Map<String, String> downToUpMap = arg.getDownToUpMap();
        Map<String, String> fieldToTypeMap = Maps.newHashMap();
        for (String fieldName : fieldList) {
            fieldToTypeMap.put(fieldName, SynchronizationDataUtil.getFiledTypes(fieldName));
        }
        //构建外层select字段
        List<String> selectReadColumns = Lists.newArrayList();
        selectReadColumns.add("'" + arg.getUpStreamTenantId() + "' as tenant_id");
        if (GrayManager.isAllowByRule("biDataSyncPolicyGray", arg.getUpStreamTenantId())) {
            selectReadColumns.add("'" + arg.getPolicyId() + "' as policy_id");
        }
        selectReadColumns.add("'" + arg.getViewId() + "' as view_id");
        selectReadColumns.add(arg.getVersion() + " as view_version");
        selectReadColumns.add("'" + arg.getObjectId() + "'" +  " as object_id");
        selectReadColumns.add("action_date as action_date");
        selectReadColumns.add("1 as bi_sys_flag");
        selectReadColumns.add(arg.getBiSysBatchId() + " as bi_sys_batch_id");
        selectReadColumns.add("hash_code as hash_code");
        if (StringUtils.isNotBlank(partitionName)) {
            selectReadColumns.add("'"+ partitionName+"'" + " AS bi_sys_ods_part");
        }
        //构建内层的select字段
        List<String> selectColumns = Lists.newArrayList();
        selectColumns.add("hash_code as hash_code");
        selectColumns.add("any(action_date) as action_date");
        for (Map.Entry<String, String> entry : fieldToTypeMap.entrySet()) {
            String fieldName = entry.getKey();
            String fieldType = entry.getValue();
            if (StringUtils.equals(fieldName, fieldType)) {
                continue;
            }
            if (StringUtils.isBlank(downToUpMap.get(fieldName))) {
                continue;
            }
            selectReadColumns.add(fieldName + " as " + downToUpMap.get(fieldName));
            if (StringUtils.equals(fieldType, "AggregateFunction(uniqExact, Nullable(String))")) {
                selectColumns.add("hex(argMaxIf(" + fieldName + ", timestamp, " + fieldName + "_merge)) as " + fieldName);
            } else {
                selectColumns.add("argMax(" + fieldName + ", timestamp) as " + fieldName);
            }
        }

        //构建where查询语句
        List<String> whereColumns = Lists.newArrayList();
        whereColumns.add("tenant_id = '" + arg.getDownStreamTenantId() + "'");
        whereColumns.add("view_id = '" + arg.getViewId() + "'");
        whereColumns.add("view_version = " + arg.getVersion());

        //如果是全量的sql，则只需要添加时间戳就可以了,如果是增量，需要添加hash_code的条件
        if (arg.isFullSynchronization()) {
            whereColumns.add("timestamp < '" + SynchronizationDataUtil.getTimestampFormat(arg.getTimeStamp()) + "'");
        } else {
            String whereSubQuery = String.format(incrementSubSQL, arg.getDownStreamTenantId(), arg.getViewId(), SynchronizationDataUtil.getTimestampFormat(arg.getLastTimeStamp()), SynchronizationDataUtil.getTimestampFormat(arg.getTimeStamp()), arg.getVersion());
            whereColumns.add("hash_code in (" + whereSubQuery + ")");
        }

        StringBuilder selectReadClause = new StringBuilder();
        for (String ReadColumn : selectReadColumns) {
            if (selectReadClause.length() > 0) {
                selectReadClause.append(",\n");
            }
            selectReadClause.append(ReadColumn);
        }

        StringBuilder selectClause = new StringBuilder();
        for (String selectColumn : selectColumns) {
            if (selectClause.length() > 0) {
                selectClause.append(",\n");
            }
            selectClause.append(selectColumn);
        }

        StringBuilder whereClause = new StringBuilder();
        for (String condition : whereColumns) {
            if (whereClause.length() > 0) {
                whereClause.append("\n  AND ");
            }
            whereClause.append(condition);
        }

        String whereHashCode = "hash_code > %s";

        return String.format("SELECT %s\n" +
                                    "FROM (\n" +
                                            "SELECT %s\n" +
                                            "FROM agg_data\n" +
                                            "WHERE %s\n" +
                                            "GROUP BY hash_code\n" +
                                          ")\n" +
                                    "WHERE %s\n" +
                                    "ORDER BY hash_code\n" +
                                    "LIMIT 800;"
          ,selectReadClause, selectClause, whereClause, whereHashCode);
    }

    /**
     * before数据同步sql
     * fieldList 同步字段
     * batchNum 同步批次
     * viewId 统计图id
     * viewVersion 统计图版本
     * actionData agg_data日期条件
     * dbName 数据库名称
     */
    public String toTransferBeforeSql(SynchronizationInsertBeforeAggDataArg arg) {
        //同步的字段
        String selectField = arg.getFieldList().stream().map(x -> "ds_" + x).collect(Collectors.joining(","));
        String hashCodeWhere = String.join(",", arg.getHashCodeList());

        String beforeInsertSql = "INSERT INTO %s.agg_downstream_data(tenant_id,\n" +
          "                                                          view_id,\n" +
          "                                                          view_version,\n" +
          "                                                          object_id,\n" +
          "                                                          action_date,\n" +
          "                                                          hash_code,\n" +
          "                                                          %s,\n" +
          "                                                          bi_sys_batch_id," +
          "                                                          bi_sys_flag)" +
          "SELECT tenant_id,\n" +
          "       view_id,\n" + "" +
          "       view_version,\n" +
          "       object_id,\n" +
          "       action_date,\n" +
          "       hash_code,\n" +
          "       %s,\n" +
          "       %s as bi_sys_batch_id,\n" +
          "       0    as bi_sys_flag\n" +
          "FROM %s.agg_downstream_data\n" +
          "WHERE tenant_id = '%s'\n" +
          "  and view_id = '%s'\n" +
          "  and view_version = %s\n" +
          "  and (hash_code in (%s))\n" +
          "    SETTINGS\n" +
          "max_bytes_before_external_sort = 2147483648\n" +
          ", max_bytes_before_external_group_by = 2147483648\n" +
          ", group_by_two_level_threshold_bytes = 1073741824\n" +
          ", optimize_aggregation_in_order = 1;";
        return String.format(beforeInsertSql, arg.getDbName(), selectField, selectField, arg.getBatchNum(), arg.getDbName(), arg.getUpStreamTenantId(), arg.getViewId(), arg.getViewVersion(), hashCodeWhere);
    }

    /**
     * before数据查询action_date存在问题
     */
    public String toTransferBeforeDownStreamAggDataSql(SynchronizationBeforeAggDataArg arg) {
        String whereHashCode = "hash_code > %s";
        String downStreamAggDataSql =  "SELECT hash_code\n" +
                                        "FROM (\n" +
                                                 "SELECT hash_code as hash_code\n" +
                                                 "FROM %s.agg_data\n" +
                                                 "WHERE tenant_id = '%s'\n" +
                                                    "AND view_id = '%s'\n" +
                                                    "AND view_version = %s\n" +
                                                    "AND hash_code IN (SELECT hash_code\n" +
                                                                      "FROM agg_data\n" +
                                                                      "WHERE tenant_id = '%s'\n" +
                                                                            "AND view_id = '%s'\n" +
                                                                            "AND view_version = %s\n" +
                                                                            "ANd timestamp >= '%s' AND timestamp < '%s')\n" +
                                                 "GROUP BY hash_code\n" +
                                              ")\n" +
                                        "WHERE %s\n" +
                                        "ORDER BY hash_code\n" +
                                        "LIMIT 100\n" +
                                        "SETTINGS max_bytes_before_external_sort = 2147483648,max_bytes_before_external_group_by = 2147483648,group_by_two_level_threshold_bytes = 1073741824,optimize_aggregation_in_order = 1;";
        return String.format(downStreamAggDataSql, arg.getDbName(), arg.getDownStreamTenantId(), arg.getViewId(), arg.getViewVersion(), arg.getDownStreamTenantId(), arg.getViewId(), arg.getViewVersion(), SynchronizationDataUtil.getTimestampFormat(arg.getLastTimestamp()), SynchronizationDataUtil.getTimestampFormat(arg.getCurrentTimestamp()), whereHashCode);
    }


    private AggDataSyncInfoDo getAggSyncInfoDo(String tenantId, String viewId) {
        AggDataSyncInfoDo aggDataSyncInfoDo = new AggDataSyncInfoDo();
        aggDataSyncInfoDo.setTenantId(tenantId);
        aggDataSyncInfoDo.setViewId(viewId);
        aggDataSyncInfoDo.setBatchNum(0L);
        aggDataSyncInfoDo.setMaxSyncTimeStamp(0L);
        aggDataSyncInfoDo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
        aggDataSyncInfoDo.setTimestamp(new Date());
        aggDataSyncInfoDo.setIsDeleted(0);
        return aggDataSyncInfoDo;
    }

    private AggDataSyncInfoDo getAggSyncInfoDoByPolicyId(String tenantId, String viewId, String policyId, String statViewUniqueKey) {
        AggDataSyncInfoDo aggDataSyncInfoDo = new AggDataSyncInfoDo();
        aggDataSyncInfoDo.setTenantId(tenantId);
        aggDataSyncInfoDo.setViewId(viewId);
        aggDataSyncInfoDo.setPolicyId(policyId);
        aggDataSyncInfoDo.setStatViewUniqueKey(statViewUniqueKey);
        aggDataSyncInfoDo.setBatchNum(0L);
        aggDataSyncInfoDo.setMaxSyncTimeStamp(0L);
        aggDataSyncInfoDo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
        aggDataSyncInfoDo.setTimestamp(new Date());
        aggDataSyncInfoDo.setIsDeleted(0);
        return aggDataSyncInfoDo;
    }
}
