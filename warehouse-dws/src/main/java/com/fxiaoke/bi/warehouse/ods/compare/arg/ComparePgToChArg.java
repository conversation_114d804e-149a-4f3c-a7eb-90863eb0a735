package com.fxiaoke.bi.warehouse.ods.compare.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-07-26
 * @desc 比较pg库和ch库差异入参
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComparePgToChArg {

    /**
     * schema名称
     */
    private String pgSchema;

    /**
     * clickhouse jdbc url
     */
    private String chJdbcUrl;

    /**
     * postgresql jdbc url
     */
    private String pgJdbcUrl;

    /**
     * 差异的允许的范围
     */
    private int threshold;
}
