package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;


@Table(name = "stat_view_field")
@Data
public class StatViewFieldDO {

  @Column(name = "view_field_id")
  private String viewFieldId;

  @Column(name = "view_id")
  private String viewId;

  @Column(name = "field_id")
  private String fieldId;

  @Column(name = "order_type")
  private int orderType;

  @Column(name = "aggr_type")
  private String aggrType;

  @Column(name = "axis_type")
  private int axisType;

  @Column(name = "ei")
  private int ei;

  @Column(name = "is_visible")
  private int isVisible;

  @Column(name = "seq")
  private int seq;

  @Column(name = "legend_name")
  private String legendName;

  @Column(name = "creator")
  private int creator;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "updator")
  private int updator;

  @Column(name = "update_time")
  private Date updateTime;

  @Column(name = "is_delete")
  private int isDelete;

  @Column(name = "parent_id")
  private String parentId;

  @Column(name = "ratio_type")
  private String ratioType = "0";

  @Column(name = "cell_width")
  private int cellWidth;
}
