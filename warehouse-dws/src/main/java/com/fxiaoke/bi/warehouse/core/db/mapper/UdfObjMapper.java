package com.fxiaoke.bi.warehouse.core.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.UdfObjDO;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * @Author:jief
 * @Date:2023/5/13
 */
@Repository
public interface UdfObjMapper extends ITenant<UdfObjMapper> {

  @Select("select obj_name from udf_obj where ei=${ei} and lower(obj_name)=#{tableNameLower} limit 1")
  String findObjNameByTable(@Param("ei") int ei,@Param("tableNameLower") String tableNameLower);
  @Select("select * from udf_obj where ei=${ei} and obj_name=#{obj_name} limit 1")
  UdfObjDO findObjNameByBiApiName(@Param("ei") int ei, @Param("obj_name") String biApiName);
}
