package com.fxiaoke.bi.warehouse.dws.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.bi.warehouse.common.db.er.AggRuleType;
import com.fxiaoke.bi.warehouse.common.mq.message.ViewChangeMessage;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.TraceUtils;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.dws.exception.ParseRuleException;
import com.fxiaoke.bi.warehouse.dws.exception.VersionConflictException;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableStatus;
import com.fxiaoke.bi.warehouse.dws.service.ClickHouseService;
import com.fxiaoke.bi.warehouse.dws.service.StatTopologyService;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.integrate.service.BiDataSyncPolicyService;
import com.fxiaoke.bi.warehouse.ods.mq.CalculateEventProducer;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Uninterruptibles;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author:jief
 * @Date:2023/5/25
 */
@Slf4j
@Component
public class DWSViewChangeConsumer implements MessageListenerOrderly, ApplicationListener<ContextRefreshedEvent> {
  @Resource
  private StatTopologyService statTopologyService;
  @Resource
  private EIEAConverter eieaConverter;
  @Resource
  private ClickHouseService clickHouseService;
  @Resource
  private CalculateEventProducer calculateEventProducer;
  @Resource(name = "mybatisTenantPolicy")
  private MybatisTenantPolicy mybatisTenantPolicy;
  private AutoConfMQPushConsumer consumer;
  @Resource
  private BiDataSyncPolicyService biDataSyncPolicyService;

  @PostConstruct
  public void init() {
    consumer = new AutoConfMQPushConsumer("fs-bi-warehouse", "DWSViewConsumer", this);
  }

  @Override
  public ConsumeOrderlyStatus consumeMessage(List<MessageExt> list, ConsumeOrderlyContext consumeOrderlyContext) {
    try {
      list.forEach(messageExt -> {
        for (int i = 0; i < 10; i++) {
          try {
            String msg = new String(messageExt.getBody(), Charsets.UTF_8);
            log.info("DWSViewChangeConsumer consumeMessage msg:{}", msg);
            ViewChangeMessage viewChangeMessage = ViewChangeMessage.parseFromMsg(messageExt);
            String tenantId = viewChangeMessage.getTenantId();
            int sourceType = viewChangeMessage.getSourceType() == null ? -1 : viewChangeMessage.getSourceType();
            String topic = messageExt.getTopic();
            TraceUtils.createTrace(tenantId, eieaConverter, messageExt.getMsgId(), null);
            if (Utils.useGrayTopic(tenantId, mybatisTenantPolicy.getPgDbNameByTenantId(tenantId)) && !Objects.equals(topic, CHContext.BI_WAREHOUSE_EVENT_TOPIC_GRAY)) {
              messageExt.setTopic(CHContext.BI_WAREHOUSE_EVENT_TOPIC_GRAY);
              calculateEventProducer.sendMessage(messageExt, messageExt.getQueueId());
              log.info("send this ei:{} msg to topic:{}", tenantId, CHContext.BI_WAREHOUSE_EVENT_TOPIC_GRAY);
              break;
            }
            if (!GrayManager.isAllowByRule("use_ch_agg", tenantId) && !GrayManager.isAllowByRule("use_ch_agg_pgdb", mybatisTenantPolicy.getPgDbNameByTenantId(tenantId))) {
              log.info("consumeMessage dws_view_event ei:{} no allow msg:{}", tenantId,msg);
              break;
            }
            String tag = messageExt.getTags();
            switch (tag) {
              case "dws_tenant_event" -> statTopologyService.reCreateTopologyByEI(tenantId,null);
              case "dws_view_event" -> {
                //检查并添加路由信息
                clickHouseService.checkAndAddChRouter(Lists.newArrayList(tenantId));
                if (StringUtils.isBlank(viewChangeMessage.getSourceId())) {
                  log.info("begin batchCreateTopologyByEi :{} all:true", tenantId);
                  statTopologyService.batchCreateTopologyByEi(tenantId, sourceType, Constants.DEFAULT_DATETIME, true);
                } else {
                  statTopologyService.doCreateTopology(tenantId, viewChangeMessage.getSourceId(), viewChangeMessage.getSourceType(), TopologyTableStatus.Prepared.getValue());
                }
              }
              case "dws_downstream_policy" -> {
                biDataSyncPolicyService.checkDataSyncPolicyViewStatus(tenantId, viewChangeMessage.getPolicyId());
              }
              default -> log.error("no support this tag:{},msg:{}", tag, JSON.toJSONString(viewChangeMessage));
            }
            break;
          } catch (ParseRuleException e0) {
            //这个异常不做重试
            log.error("consumeMessage error msg:{}", new String(messageExt.getBody(), Charsets.UTF_8), e0);
            break;
          } catch (VersionConflictException e1) {
            log.warn("consumeMessage error msg:{}", new String(messageExt.getBody(), Charsets.UTF_8), e1);
            Uninterruptibles.sleepUninterruptibly(10, TimeUnit.SECONDS);
          } catch (Exception e) {
            log.error("consumeMessage error msg:{}", new String(messageExt.getBody(), Charsets.UTF_8), e);
            Uninterruptibles.sleepUninterruptibly(10, TimeUnit.SECONDS);
          } finally {
            TraceUtils.removeContext();
          }
        }
      });
      return ConsumeOrderlyStatus.SUCCESS;
    } catch (Exception e) {
      return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
    }
  }

  @PreDestroy
  public void destroy() {
    if (null != consumer) {
      consumer.shutdown();
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (null == event.getApplicationContext().getParent() && consumer != null) {
      consumer.start();
    }
  }
}
