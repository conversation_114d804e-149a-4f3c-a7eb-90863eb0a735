package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.collect.Sets;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * @Author:jief
 * @Date:2023/4/12
 */
@SuperBuilder
public class DimFieldRule extends QuoteOfAgg<DimRule> {
  public String tenantId;
  public String apiName;
  public String fieldId;
  public String fieldName;
  //数仓槽位
  public String fieldLocation;
  public String dbFieldName;
  public String dbObjName;
  /**
   * 预设的维度列名别名
   */
  public String dstColumnName;
  public DisplayField.DisplayType displayType;
  /**
   * 自定义字段清洗规则
   */
  public String dimFormula;
  /**
   * 自定义维度Id
   */
  public String dimensionId;
  /**
   * 自定义维度类型
   */
  public String customType;
  /**
   * 分组配置
   */
  public String dimensionConfig;
  /**
   * dbFieldName相同，fieldId可能有多个，都存到这里，只记录一次DimFieldRule
   */
  public Set<String> sameFieldIds;

  @Override
  public void init(DimRule dimRule) {
    super.init(dimRule);
  }

  public DimFieldRule copy(String fieldId,
                           String dstColumnName,
                           String fieldType,
                           String column,
                           boolean enableMultiLang,
                           Set<String> sameFieldIds) {
    return  DimFieldRule.builder()
                        .tenantId(tenantId)
                        .apiName(apiName)
                        .fieldId(fieldId)
                        .dstColumnName(dstColumnName)
                        .fieldName(fieldName)
                        .dbFieldName(dbFieldName)
                        .dbObjName(dbObjName)
                        .column(column)
                        .joinRelation(joinRelation)
                        .columnType(columnType)
                        .fieldType(fieldType)
                        .isSingle(isSingle)
                        .displayType(displayType)
                        .nodeId(nodeId)
                        .describeApiName(describeApiName)
                        .enableMultiLang(enableMultiLang)
                        .originalType(originalType)
                        .sameFieldIds(sameFieldIds)
                        .build();
  }

  /**
   * 构建node  id列,报表用
   * @param tenantId
   * @param describeApiName
   * @param biApiName
   * @param nodeId
   * @param standalone
   * @return
   */
  public static DimFieldRule buildIdDimFieldRule(String tenantId,
                                          String describeApiName,
                                          String biApiName,
                                          String nodeId,
                                          boolean standalone) {
    String id = Constants.getIdName(standalone, biApiName);
    return  DimFieldRule.builder()
            .tenantId(tenantId)
            .apiName(biApiName)
            .fieldId(nodeId)
            .dbFieldName(id)
            .dbObjName(biApiName)
            .column(id)
            .columnType(PGColumnType.String)
            .fieldType(FieldType.TEXT)
            .isSingle(true)
            .displayType(DisplayField.DisplayType.group)
            .nodeId(nodeId)
            .describeApiName(describeApiName)
            .enableMultiLang(false)
            .originalType(FieldType.TEXT)
            .sameFieldIds(Sets.newHashSet(nodeId))
            .build();
  }
}
