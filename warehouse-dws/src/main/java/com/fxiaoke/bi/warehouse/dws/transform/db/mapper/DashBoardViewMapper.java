package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.DashBoardViewDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author:jief
 * @Date:2023/7/10
 */
@Repository
public interface DashBoardViewMapper extends ICrudMapper<DashBoardViewDO>, IBatchMapper<DashBoardViewDO>, ITenant<DashBoardViewMapper> {
  /**
   * 根据viewid反查所有用到这个图的驾驶舱
   * @param tenantId
   * @param viewId
   * @return
   */
  @Select("select * from dash_board_view where tenant_id=#{tenant_id} and view_id=#{viewId} and is_deleted=0")
  List<DashBoardViewDO> queryDashBoardViewsByViewId(@Param("tenant_id") String tenantId,
                                                    @Param("viewId") String viewId);
}
