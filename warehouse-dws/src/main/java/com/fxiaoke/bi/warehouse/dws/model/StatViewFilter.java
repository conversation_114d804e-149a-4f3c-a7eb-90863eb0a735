package com.fxiaoke.bi.warehouse.dws.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatViewFilter {
  private String fieldId;
  private String operator;
  private String dateRangeID;
  private String value1;
  private String value2;
  private List<String> ratioDateValue1List; // 同环比 日期 value1
  private List<String> ratioDateValue2List; // 同环比 日期 value2
  private int onlyFilter; // 是否只作为数据范围
}
