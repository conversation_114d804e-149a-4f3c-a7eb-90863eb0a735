package com.fxiaoke.bi.warehouse.ods.integrate.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncTableStatusEnum;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.SQLUtil;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.ods.bean.PgSysModifiedTimeInfo;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.service.CHDataToCHService;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.DbTableSyncInfoService;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Slf4j
public class CHDataToCHServiceImpl implements CHDataToCHService {
  @Resource
  CHDataSource chDataSource;
  @Resource
  CHDBService chdbService;
  @Resource
  CHMetadataService chMetadataService;
  @Resource
  DbTableSyncInfoService dbTableSyncInfoService;

  @Resource
  private CHRouterPolicy chRouterPolicy;

  @Override
  public void transferOperationDataToCH(AtomicLong nextBatchNum,
                                        DBSyncInfoBO dbSyncInfoCopy,
                                        List<String> tenantIdList,
                                        Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                        String partitionValue) {
    String chDBName = CommonUtils.getDBName(dbSyncInfoCopy.getChDb());
    if (!GrayManager.isAllowByRule("allow_transfer_operation", chDBName)) {
      log.info("transferOperationDataToCH skip this chDB:{}", chDBName);
      return;
    }
    StopWatch stopWatch = StopWatch.createStarted("ch2ch_copy");
    log.info("transferOperationDataToCH nextBatchNum:{},pgDB:{},chDB:{}", nextBatchNum, dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getChDb());
    Map<String, String> ch2chTablePairs = WarehouseConfig.ch2chTablePair;
    for (Map.Entry<String, String> tablePair : ch2chTablePairs.entrySet()) {
      DbTableSyncInfoDO dbTableSyncInfo = dbTableSyncInfoMap.get(tablePair.getKey());
      boolean offline = false;
      if (dbTableSyncInfo == null) {
        List<DbTableSyncInfoDO> dbTableSyncInfos = dbTableSyncInfoService.queryDbTableSyncInfos(dbSyncInfoCopy, tablePair.getKey());
        if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
          dbTableSyncInfo = dbTableSyncInfos.getFirst();
        } else {
          offline = true;
          dbTableSyncInfo = DbTableSyncInfoDO.createFrom(dbSyncInfoCopy, tablePair.getKey());
        }
        dbTableSyncInfoMap.put(tablePair.getKey(), dbTableSyncInfo);
      }
      dbTableSyncInfo.setLastSyncTime(new Date().getTime());
      long nextBatchNumValue = nextBatchNum.get();
      if (dbTableSyncInfo.getBatchNum() >= nextBatchNumValue) {
        log.warn("this table:{} has bean synced table batchNum:{}, batchNum:{}", dbTableSyncInfo.getTableName(), dbTableSyncInfo.getBatchNum(), nextBatchNumValue);
        return;
      }
      Map<String, Set<String>> apiNameEiMapper = Maps.newHashMap();
      Optional<ClickhouseTable> fromClickhouseTableOP = chMetadataService.loadTableFromDB(dbSyncInfoCopy.getChDb(), tablePair.getKey());
      ClickhouseTable fromCHTable = fromClickhouseTableOP.orElseThrow(() -> new RuntimeException(String.format("loadTableFromDB error pgDB:%s,chDB:%s,tableName:%s", dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getChDb(), tablePair.getKey())));
      Optional<ClickhouseTable> toClickhouseTableOP = chMetadataService.loadTableFromDB(dbSyncInfoCopy.getChDb(), tablePair.getValue());
      ClickhouseTable toCHTable = toClickhouseTableOP.orElseThrow(() -> new RuntimeException(String.format("loadTableFromDB error pgDB:%s,CHDB:%s,tableName::%s", dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getChDb(), tablePair.getValue())));
      String partValue =
        GrayManager.isAllowByRule("use_ch_ods_partition", chDBName) && toClickhouseTableOP.get().existsPartitionKey() ?
          partitionValue :
          null;
      try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(dbSyncInfoCopy.getChDb(), 7200000L)) {
        Long startTimestamp = dbTableSyncInfo.getMaxSysModifiedTime();
        PgSysModifiedTimeInfo sysModifiedTimeInfo = chdbService.findMaxSysModifiedTimeByTable(jdbcConnection, fromCHTable, startTimestamp, stopWatch);
        dbTableSyncInfo.setMaxSysModifiedTime(Math.max(sysModifiedTimeInfo.getMaxModifiedTime(), startTimestamp == null ? 0L : startTimestamp));
        Pair<Long, Long> sysTimeRange = Pair.build(startTimestamp, sysModifiedTimeInfo.getMaxModifiedTime());
        int num = chdbService.insertBizData(tenantIdList, fromCHTable, toCHTable, nextBatchNumValue, sysTimeRange, stopWatch, partValue);
        if (num > 0) {
          List<String> modifiedTenantIds = SQLUtil.executeQuerySQLWithJdbc(jdbcConnection, toCHTable.queryEisByBatchNum(tenantIdList, nextBatchNumValue), r -> {
            try {
              return String.valueOf(r.getObject(1));
            } catch (Exception e) {
              throw new RuntimeException(e);
            }
          });
          if (CollectionUtils.isEmpty(modifiedTenantIds)) {
            log.warn("query change tenantIds from pgDB:{},chDB:{},table:{},batchNum:{}", dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getChDb(), toCHTable.getName(), nextBatchNumValue);
          } else {
            apiNameEiMapper.put(tablePair.getValue(), Sets.newHashSet(modifiedTenantIds));
          }
        }
        log.info("syncBizDownstream finish,pgDB{},chDB:{},batchNum:{},fromTable:{},toTable:{},offline:{},num:{}", dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getChDb(), nextBatchNumValue, tablePair.getKey(), tablePair.getValue(), offline, num);
      } catch (Exception e) {
        log.error("transferOperationDataToCH error pgDB{},chDB:{},fromTable:{},toTable:{}", dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getChDb(), tablePair.getKey(), tablePair.getValue(), e);
        throw new RuntimeException(String.format("syncBizDownstream error fromTable:%s,toTable:%s", tablePair.getKey(), tablePair.getValue()), e);
      }
      dbTableSyncInfo.setApiNameEiMap(JSON.toJSONString(apiNameEiMapper));
      dbTableSyncInfo.setLastModifiedTime(new Date().getTime());
      dbTableSyncInfo.setBatchNum(nextBatchNumValue);
      dbTableSyncInfo.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
      if (offline) {
        dbTableSyncInfoService.batchUpsertDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
      } else {
        dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
      }
      stopWatch.stop();
      log.info("transferOperationDataToCH finish pgDB{},chDB:{},fromTable:{},toTable:{},result:{}", dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getChDb(), tablePair.getKey(), tablePair.getValue(), stopWatch.prettyPrint());
    }
  }

  @Override
  public String transferTenantLoginData(String tenantId) {
    RouterInfo routerInfo = chRouterPolicy.getRouterInfo(tenantId);
    //查出最小的sys_modified_time
    String sqlMinTime =
      "select min(sys_modified_time) minTime from biz_user_login_online_operation_ods where tenant_id = '" + tenantId +
        "'";
    AtomicReference<String> minTime = new AtomicReference<>("");
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chRouterPolicy.getCHJdbcURL(tenantId))) {
      jdbcConnection.query(String.format(sqlMinTime, routerInfo.getDbName()), r -> {
        while (r.next()) {
          minTime.set(r.getString("minTime"));
        }
      });
    } catch (SQLException e) {
      log.error("transferTenantLoginData sqlMinTime error tenantId:{}", tenantId, e);
      throw new RuntimeException(e);
    }
    String newsql = """
        INSERT INTO biz_user_login_online_operation
        (id, name, tenant_id, login_user, login_user_dept, active_time, login_type, login_type_name, device_model, login_ip,
         app_name, last_modified_time, last_modified_by, create_time, created_by, owner, data_own_department,
         connected_enterprise, out_owner, out_data_own_department,sys_modified_time,traceid,is_deleted,bi_sys_flag, bi_sys_batch_id,bi_sys_is_deleted,bi_sys_version)
        select id,
               name,
               tenant_id,
               login_person,
               data_own_department,
               login_time,
               login_way,
               login_way,
               login_browser,
               login_ip,
               app_id,
               last_modified_time,
               last_modified_by,
               create_time,
               created_by,
               owner,
               data_own_department,
               out_tenant_id as out_tenant_id,
               out_owner      as connected_operator,
               out_data_own_department as connected_dept,
               sys_modified_time,
               '' as traceid,
               0 as is_deleted,
               1 as bi_sys_flag,
               0 as bi_sys_batch_id,
               0 as bi_sys_is_deleted,
               now()
        from mt_login_log where tenant_id='%s'
      """;
    String sql = String.format(newsql, tenantId);
    if (StringUtils.isNotEmpty(minTime.get()) && Long.parseLong(minTime.get()) > 0L) {
      sql += " and sys_modified_time <= " + minTime.get();
    }
    log.info("transferTenantLoginData sql:{}", sql);
    chdbService.executeSQLWithJdbcUrl(chRouterPolicy.getCHJdbcURL(tenantId), sql, 6000 * 1000L);

    return "ok";
  }

  @Override
  public String transferTenantApiNameData(String tenantId) {
    RouterInfo routerInfo = chRouterPolicy.getRouterInfo(tenantId);
    //查出最小的sys_modified_time
    String sqlMinTime =
      "select min(sys_modified_time) minTime from biz_user_api_name_operation_ods where tenant_id = '" + tenantId + "'";
    AtomicReference<String> minTime = new AtomicReference<>("");
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chRouterPolicy.getCHJdbcURL(tenantId))) {
      jdbcConnection.query(String.format(sqlMinTime, routerInfo.getDbName()), r -> {
        while (r.next()) {
          minTime.set(r.getString("minTime"));
        }
      });
    } catch (SQLException e) {
      log.error("transferTenantApiNameData sqlMinTime error tenantId:{}", tenantId, e);
      throw new RuntimeException(e);
    }
    String newSQL = """
      INSERT INTO biz_user_api_name_operation (id, name, tenant_id, operator, operator_dept, operate_time, api_name,
                                                   operate_data_id, operate_type,
                                                   app_name, last_modified_time, last_modified_by, create_time, created_by,
                                                   data_own_department, owner_department, owner, connected_enterprise,
                                                   out_owner, out_data_own_department,flow_type, flow_name, traceid,
                                                   sys_modified_time,is_deleted,bi_sys_flag,bi_sys_batch_id,bi_sys_is_deleted)
      select id,
             name,
             tenant_id,
             operator,
             operation_dept,
             operation_time,
             operation_object,
             operation_data,
             CASE
                 WHEN operation = 'choose' THEN 'Choose_button_default'
                 WHEN operation = 'allocate' THEN 'Allocate_button_default'
                 WHEN operation = 'move' THEN 'Move_button_default'
                 WHEN operation = 'return' THEN 'Return_button_default'
                 WHEN operation = '10' THEN 'TakeBack_button_default'
                 WHEN operation = '1' THEN 'Add_button_default'
                 WHEN operation = '2' THEN 'Edit_button_default'
                 WHEN operation = '6' THEN 'ChangeOwner_button_default'
                 WHEN operation = '3' THEN 'Abolish_button_default'
                 WHEN operation = 'Lock' THEN 'Lock_button_default'
                 WHEN operation = 'unlock' THEN 'Unlock_button_default'
                 ELSE '' END                         AS operation,
             app_id,
             last_modified_time,
             last_modified_by,
             create_time,
             created_by,
             data_own_department,
             operation_dept as owner_department,
             operator as owner,
             out_tenant_id as connected_enterprise,
             out_owner as connected_operator,
             out_data_own_department as connected_dept,
             '' as flow_type,
             '' as flow_name,
             '' as traceid,
             sys_modified_time,
             0 as is_deleted,
             1 as bi_sys_flag,
             0 as bi_sys_batch_id,
             0 as bi_sys_is_deleted
      from mt_operation_log
      where tenant_id = '%s'
      """;
    String sql = String.format(newSQL, tenantId);
    if (StringUtils.isNotEmpty(minTime.get()) && Long.parseLong(minTime.get()) > 0L) {
      sql += " and sys_modified_time <= " + minTime.get();
    }
    log.info("transferTenantApiNameData sql:{}", sql);
    chdbService.executeSQLWithJdbcUrl(chRouterPolicy.getCHJdbcURL(tenantId), sql, 6000 * 1000L);
    return "ok";
  }
}