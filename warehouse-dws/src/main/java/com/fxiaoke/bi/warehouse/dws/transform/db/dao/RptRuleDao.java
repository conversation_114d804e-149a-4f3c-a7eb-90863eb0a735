package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.metadata.context.dto.dw.RptViewDwContext;
import com.fxiaoke.bi.warehouse.common.db.er.*;
import com.fxiaoke.bi.warehouse.common.goal.*;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyTableMapper;
import com.fxiaoke.bi.warehouse.dws.model.DimConfig;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableStatus;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.*;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.*;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.Pair;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2023/5/10
 */
@Service
@Slf4j
public class RptRuleDao extends RuleDao {
  @Autowired
  private DimRuleDao dimRuleDao;
  @Autowired
  private UdfObjFieldMapper udfObjFieldMapper;
  @Autowired
  private RptViewMapper rptViewMapper;
  @Resource
  private MappingService mappingService;
  @Resource
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Autowired
  private TopologyTableMapper topologyTableMapper;

  /**
   * {@see <a href='https://oss.foneshare.cn/cms/edit/config/17121'>bi-statictic-agg</a>}
   */
  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", iConfig -> {
    });
  }

  public RptViewDO queryRptView(String tenantId, String viewId) {
    RptViewDO rptViewDO = rptViewMapper.setTenantId("-1").queryRptView(Integer.parseInt(tenantId), viewId);
    if (rptViewDO == null) {
      log.error("can not find rptView tenantId:{},viewId:{}", tenantId, viewId);
      return null;
    }
    return rptViewDO;
  }

  /**
   * 构建拓扑图 MtDetail只有报表有
   *
   * @param tenantId 租户id
   * @param sourceId 图id
   * @param source   图类型目标，统计图，多维度目标
   */
  public BiMtRule createRptViewMtRule(String tenantId, String sourceId, Integer source) {
    BiMtRule biMtRule = this.createBiMtRule(tenantId, sourceId, source);
    if (biMtRule == null) {
      return null;
    }
    List<BiMtDetailRule> biMtDetailRules = this.findDetailsByTopologyId(tenantId, biMtRule.getTopologyDescribeId())
                                               .stream()
                                               .map(BIMtDetailDO::toMtDetailRule)
                                               .toList();
    biMtRule.setBiMtDetailRules(biMtDetailRules);
    return biMtRule;
  }

  /**
   * 根据入参构建MtRule
   * @param tenantId
   * @param sourceType
   * @param rptViewDwContext
   * @return
   */
  public BiMtRule buildMtRuleByRptViewDwContext(String tenantId,
                                                int sourceType,
                                                RptViewDwContext rptViewDwContext) {
    String topologyDescribeId = rptViewDwContext.getBiMtTopologyDescribeDO().topologyDescribeId;
    String topologyModel = rptViewDwContext.getBiMtTopologyDescribeDO().topologyModel;
    int detailSourceType = rptViewDwContext.getBiMtTopologyDescribeDO().source;
    Integer version = -1;
    //控制是否通过维度快照表过滤数据
    if (GrayManager.isAllowByRule("user_goal_value_obj_snapshot_eis", tenantId) && (detailSourceType == 1 || detailSourceType == 2)){
      version = topologyTableMapper.setTenantId(tenantId).queryMaxVersion(tenantId, topologyDescribeId);
    }
    List<BiMtDimRule> biMtDimRules = rptViewDwContext.getBiMtDimensionDOList()
                                                     .stream()
                                                     .map(dimRule -> BiMtDimRule.builder()
                                                                                .tenantId(tenantId)
                                                                                .dimensionId(topologyDescribeId)
                                                                                .dimensionId(dimRule.dimensionId)
                                                                                .dimensionField(dimRule.dimensionField)
                                                                                .fieldId(dimRule.fieldId)
                                                                                .build()).toList();
    List<BiMtMeasureRule> biMtMeasureRules = rptViewDwContext.getBiMtMeasureDOList()
                                                 .stream()
                                                 .map(aggRule -> BiMtMeasureRule.builder()
                                                                                .tenantId(tenantId)
                                                                                .topologyDescribeId(topologyDescribeId)
                                                                                .measureId(aggRule.measureId)
                                                                                .name(aggRule.name)
                                                                                .aggType(aggRule.aggType)
                                                                                .description(aggRule.description)
                                                                                .nodeId(aggRule.nodeId)
                                                                                .actionDateField(aggRule.actionDateField)
                                                                                .status(aggRule.status)
                                                                                .filters(aggRule.filters)
                                                                                .aggExpression(aggRule.aggExpression)
                                                                                .build()).toList();
    List<BiMtDetailRule> biMtDetailRules = rptViewDwContext.getBiMtDetailDOList()
                                                           .stream()
                                                           .map(detRule -> BiMtDetailRule.builder()
                                                                                         .tenantId(tenantId)
                                                                                         .topologyDescribeId(topologyDescribeId)
                                                                                         .detailId(detRule.getDetailId())
                                                                                         .detailField(detRule.getDetailField())
                                                                                         .fieldId(detRule.getFieldId())
                                                                                         .build()).toList();

    return BiMtRule.builder()
                   .tenantId(tenantId)
                   .topologyDescribeId(topologyDescribeId)
                   .sourceType(sourceType)
                   .detailSourceType(detailSourceType)
                   .version(version == null ? -1 : version)
                   .standalone(this.standalone(tenantId))
                   .biTopology(BITopology.parseFromJson(topologyModel))
                   .biMtDimRules(biMtDimRules)
                   .biMtMeasureRules(biMtMeasureRules)
                   .biMtDetailRules(biMtDetailRules)
                   .measureFieldId(detailSourceType == 0 ? rptViewDwContext.getMeasureFieldId() : topologyDescribeId)
                   .build();
  }

  /**
   * 报表只有图粒度,没有规则粒度,把图当规则来解析
   *
   * @param tenantId            租户id
   * @param biMtRule            规则定义
   * @return
   */
  public List<TopologyTableAggRule> parseFromMap(String tenantId,
                                                 BiMtRule biMtRule,
                                                 AtomicInteger suffixNum,
                                                 String goalActionDateId) {
    //计算表别名
    Map<String, AtomicInteger> aliasMapper = Maps.newHashMap();
    //缓存列类型
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    boolean standalone = mybatisTenantPolicy.standalone(tenantId);
    Map<String, String> fieldLocationMap = Maps.newHashMap();
    Node rootNode = biMtRule.findRootNode();
    if (rootNode == null) {
      throw new RuntimeException(String.format("can not find rootNode tenantId:%s, sourceId:%s, sourceType:%d",
        tenantId, biMtRule.getTopologyDescribeId(), biMtRule.getSourceType()));
    }
    String rootApiName = mappingService.biApiName(rootNode.getDescribeApiName());
    NodeTable rootNodeTable = this.createAggNodeTable(standalone, rootApiName, aliasMapper);
    rootNodeTable.setNodeId(rootNode.getId());
    //dim、detail、agg都整合到一起生成join关系
    List<DimRule> fieldRules = GrayManager.isAllowByRule("use_dim_rule_combined_eis", tenantId) ?
            this.buildDimRule2(tenantId, standalone, biMtRule, goalActionDateId) :
            this.buildDimRule(tenantId, standalone, biMtRule, goalActionDateId);
    Map<String, BiMtMeasureRule> aggRuleMap = Maps.newHashMap();
    if (CollectionUtils.isNotEmpty(biMtRule.getBiMtMeasureRules())) {
      biMtRule.getBiMtMeasureRules().forEach(measure -> {
        aggRuleMap.put(measure.getMeasureId(), measure);
      });
    }
    List<String> dimConfigStringList = Lists.newArrayList();
    List<String> aggConfigStringList = Lists.newArrayList();
    Map<String, NodeTable> nodeTableMap = Maps.newHashMap();
    String actionDateConfigString = null;
    if (CollectionUtils.isNotEmpty(fieldRules)) {
      for (DimRule fieldRule : fieldRules) {
        this.buildBranch(biMtRule, rootNodeTable.getNodeId(), rootNodeTable, aliasMapper,
                fieldRule.getDimFieldRule(), cachedTableDefinitions, nodeTableMap, false);
        DimConfig fieldConfig = fieldRule.createNewDimConfig(suffixNum);
        String fieldId = fieldRule.getDimFieldRule().fieldId;
        //aggConfigStringList不用于生成sql,只用于判定是否生成默认值
//        if (aggRuleMap.containsKey(fieldId)) {
//          aggConfigStringList.add(fieldConfig.getTableAlias() + "." + fieldConfig.getColumnName() + ":" +
//            aggRuleMap.get(fieldId).getAggType().toLowerCase() + ":" + fieldConfig.getDstColumnName() );
//        }
        if (Constants.ACTION_DATE.equals(fieldConfig.getDstColumnName())) {
          actionDateConfigString = String.format("%s.%s", fieldConfig.getTableAlias(), fieldConfig.getColumnName());
        } else {
          dimConfigStringList.add(fieldConfig.toConfigString());
          if (fieldRule.getDimFieldRule().enableMultiLang && GrayManager.isAllowByRule("rpt_enable_multi_lang_eis", tenantId)) {
            dimConfigStringList.add(fieldConfig.toLangConfigString());
          }
        }
        if (GrayManager.isAllowByRule("use_dim_rule_combined_eis", tenantId)) {
          fieldRule.getDimFieldRule().sameFieldIds.forEach(mtId -> fieldLocationMap.put(mtId, fieldConfig.getDstColumnName()));
        } else {
          fieldLocationMap.put(fieldId, fieldConfig.getDstColumnName());
        }
      }
    }
    rootNodeTable.appendSubWheres( " ${RootSubWhere}");
    if (Constants.isHasIsDeleted(rootNodeTable.getName())) {
      rootNodeTable.appendSubWheres( " AND is_deleted = 0");
    }
    if (!GrayManager.isAllowByRule("bi_sys_flag_view", tenantId)) {
      rootNodeTable.appendSubWheres(Constants.RPT_PRE_SUB_WHERE);
      Constants.appendSubWhereCommonSQL(rootNodeTable.getJoinSet(), Constants.RPT_PRE_SUB_WHERE);
    }
    if (GrayManager.isAllowByRule("support_predicate_pushDown",tenantId) &&
      biMtRule.getDetailSourceType() == AggRuleType.Agg.getRuleType() &&
      WarehouseConfig.orderByTableColumns.containsKey(rootApiName)) {
      rootNodeTable.setOrderByColumns(WarehouseConfig.orderByTableColumns.get(rootApiName));
    }
    //是否是目标查看明细
    GoalRuleDO goalRuleDO = this.checkGoalRuleDetail(tenantId, biMtRule);
    String checkCyCle = "";
    Map<String, String> checkFieldLocationMapper = null;
    if (goalRuleDO != null) {
      GoalRuleBO goalRuleBO = goalRuleDO.createGoalRuleBO();
      biMtRule.setGoalRuleBO(goalRuleBO);
      checkCyCle = goalRuleBO.checkCyCle;
      checkFieldLocationMapper = this.findGoalCheckFieldLocationMapper(tenantId, biMtRule, fieldRules);
    }
    TopologyTableAggRule statRule = new TopologyTableAggRule(biMtRule.getTopologyDescribeId(),
      TopologyTableStatus.Calculating.getValue(), rootNodeTable, dimConfigStringList, actionDateConfigString,
      aggConfigStringList, null, null, fieldLocationMap, checkFieldLocationMapper,
      null, null, null, checkCyCle);
    return Lists.newArrayList(statRule);
  }

  /**
   * 查找指标或维度对象并且填充相关属性
   *
   * @param biMtRule               报表规则
   * @param rootNodeId             指标对象NodeId
   * @param rootNodeTable        根节点nodeTable
   * @param aliasMapper            别名缓存
   * @param quoteOfAgg             填充子节点或属性条件
   * @param cachedTableDefinitions
   */
  public NodeTable buildBranch(BiMtRule biMtRule,
                               String rootNodeId,
                               NodeTable rootNodeTable,
                               Map<String, AtomicInteger> aliasMapper,
                               QuoteOfAgg quoteOfAgg,
                               Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                               Map<String, NodeTable> nodeTableMap,
                               boolean forceReturnPreNode) {
    String tenantId = biMtRule.getTenantId();
    boolean standalone = biMtRule.isStandalone();
    String fromId = rootNodeId;
    String toId = quoteOfAgg.nodeId;
    NodeTable currentNodeTable = rootNodeTable;
    List<Edge> lookupLink = biMtRule.findLookupList(fromId, toId);
    if (CollectionUtils.isNotEmpty(lookupLink)) {
      for (Edge edge : lookupLink) {
        Node toNode = biMtRule.findNodeById(edge.getTo());
        currentNodeTable = this.computeIfAbsent(tenantId, biMtRule.isStandalone(), currentNodeTable, toNode,
          edge, aliasMapper, cachedTableDefinitions, nodeTableMap);
      }
    }
    if (currentNodeTable == null) {
      throw new RuntimeException(String.format("can not find nodeTable tenantId:%s,nodeId:%s", tenantId, toId));
    }
    if (!Objects.equals(currentNodeTable.getNodeId(), toId)) {
      log.warn("tenantId:{}, topologyDescribeId:{}, currentNodeTable id:{} is not equals NodeId:{}, column:{}", tenantId,
         biMtRule.getTopologyDescribeId(), currentNodeTable.getNodeId(), toId, quoteOfAgg.column);
    }
    NodeTable endNodeTable = this.joinQuoteNodeTableRpt(tenantId, standalone, currentNodeTable, aliasMapper, quoteOfAgg, true,
      forceReturnPreNode, biMtRule.getDetailSourceType());
    //join多语表
    if (quoteOfAgg.enableMultiLang && GrayManager.isAllowByRule("rpt_enable_multi_lang_eis", tenantId)) {
      this.joinLangNodeTable(tenantId, endNodeTable, aliasMapper, quoteOfAgg, standalone);
    }
    return endNodeTable;
  }

  /**
   * 关联树剪枝
   *
   * @param tenantId      租户id
   * @param isStandalone  是否schema隔离
   * @param fromNodeTable 起始节点
   * @param toNode        添加节点
   * @param edge          节点关系
   * @param aliasMapper   别名缓存
   * @return 添加后的节点
   */
  private NodeTable computeIfAbsent(String tenantId,
                                    boolean isStandalone,
                                    NodeTable fromNodeTable,
                                    Node toNode,
                                    Edge edge,
                                    Map<String, AtomicInteger> aliasMapper,
                                    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                                    Map<String, NodeTable> nodeTableMap) {
    List<OnField> onFields = edge.getOnFields();
    if (CollectionUtils.isEmpty(onFields)) {
      log.error("edge on fields is empty tenantId:{},edge:{}", tenantId, JSON.toJSONString(edge));
      return null;
    }
    Pair<String, String> lookupFieldPair = edge.findLookupFieldPair();
    if (lookupFieldPair == null || StringUtils.isAnyBlank(lookupFieldPair.first, lookupFieldPair.second)) {
      log.error("edge on fields without reference fields tenantId:{},edge:{}", tenantId, JSON.toJSONString(edge));
      return null;
    }
    if (edge.isLookUp()) {
      return leftNodeTable(tenantId, isStandalone, fromNodeTable, toNode, aliasMapper, cachedTableDefinitions,
        lookupFieldPair.first);
    } else {
      NodeTable toNodeTable = nodeTableMap.computeIfAbsent(toNode.getId(), key -> {
        NodeTable newNodeTable;
        String toNodeApiName = mappingService.biApiName(toNode.getDescribeApiName());
        if (!isStandalone && !toNodeApiName.endsWith("__c") && lookupFieldPair.second.endsWith("__c")) {
          newNodeTable = NodeTable.of(Constants.OBJECT_DATA, null, Lists.newArrayList(), Sets.newTreeSet(), toNodeApiName, null, false, null, null);
        } else {
          newNodeTable = this.createAggNodeTable(isStandalone, toNodeApiName, aliasMapper);
        }
        newNodeTable.setNodeId(toNode.getId());
        return newNodeTable;
      });
      return rightNodeTable(tenantId, isStandalone, fromNodeTable, toNodeTable, aliasMapper, cachedTableDefinitions,
        lookupFieldPair.second);
    }
  }

  /**
   * 正向查找关联  lt.外键=rt.主键
   * @param tenantId
   * @param isStandalone
   * @param fromNodeTable
   * @param toNode
   * @param aliasMapper
   * @param cachedTableDefinitions
   * @param lookupFieldName
   * @return
   */
  private NodeTable leftNodeTable(String tenantId,
                                  boolean isStandalone,
                                  NodeTable fromNodeTable,
                                  Node toNode,
                                  Map<String, AtomicInteger> aliasMapper,
                                  Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                                  String lookupFieldName) {
    NodeTable endTable = fromNodeTable;
    UdfObjFieldDO fieldInfo = udfObjFieldMapper.setTenantId(tenantId)
                                               .findUdfObjFieldDOByFieldName(tenantId,
                                                 fromNodeTable.getObjectDescribeApiName(),
                                                 mappingService.getApiXName(fromNodeTable.getObjectDescribeApiName())
                                                 , lookupFieldName);
    if (fieldInfo == null) {
      log.error("can not find db field tenantId:{},dbObjName:{},dbFieldName:{}", tenantId,
        fromNodeTable.getObjectDescribeApiName(), lookupFieldName);
      return null;
    }
    String targetObjName = mappingService.biApiName(toNode.getDescribeApiName());
    String lookupFieldColumn = lookupFieldName;
    String fieldType = fieldInfo.getType();
    String relationTable = fieldInfo.getRelationTable();
    String refKeyField = fieldInfo.getRefKeyField();
    String refObjName = fieldInfo.getRefObjName();
    if (StringUtils.isNotBlank(refObjName) && !Objects.equals(targetObjName, refObjName)) {
      throw new RuntimeException(String.format("this two object has no relationship %s:%s:%s", tenantId,
        fromNodeTable.getObjectDescribeApiName(), targetObjName));
    }
    int objectIdFieldLocation = Integer.parseInt(fieldInfo.getFieldLocation());
    if (objectIdFieldLocation > 0) {
      lookupFieldColumn = "value" + objectIdFieldLocation;
    }
    if (!isStandalone && !fromNodeTable.getObjectDescribeApiName().endsWith("__c") &&
      Constants.slotRegex.matcher(lookupFieldColumn).matches()) {
      NodeJoin extTableJoin = this.createExtNodeTable(fromNodeTable.getObjectDescribeApiName());
      endTable = fromNodeTable.addJoinSetSingle(aliasMapper, extTableJoin, true);
      fromNodeTable.appendColumns("extend_obj_data_id","id");
    } else if (!isStandalone && !fromNodeTable.getObjectDescribeApiName().endsWith("__c") &&
      !Constants.slotRegex.matcher(lookupFieldColumn).matches() && Constants.OBJECT_DATA.equals(fromNodeTable.getName())) {
      NodeJoin preTableJoin = this.createPreNodeTable(fromNodeTable.getObjectDescribeApiName());
      endTable = fromNodeTable.addJoinSetSingle(aliasMapper, preTableJoin, true);
      fromNodeTable.appendColumns("tenant_id", "_id", "object_describe_api_name", "value0", "is_deleted");
    }
    //如果是what 类型的，需要继续增加join relation
    if ("group".equals(fieldType) && StringUtils.isNotBlank(relationTable)) {
      NodeTable whatNodeTable = NodeTable.of(relationTable, null, Lists.newArrayList(), Sets.newTreeSet(),
        relationTable, "id", false, null, null);
      //构建和被引用对象的join 关系
      Set<String> onFilter = Sets.newHashSet(Constants.EI_ON_CONDITIONS, Constants.RIGHT_ON_DELETED_0);
      onFilter.add("${lt}.id=" + "${rt}.source_data_id");
      NodeOnCondition nodeOnCondition = new NodeOnCondition(onFilter.toArray(new String[0]), null, null);
      NodeJoin quoteTableJoin = NodeJoin.of(com.fxiaoke.bi.warehouse.common.db.er.JoinType.LEFT_JOIN, whatNodeTable,
        nodeOnCondition);
      endTable = fromNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, true);
      endTable.appendColumns("source_data_id");
      endTable.appendColumns("is_deleted");
      lookupFieldColumn = "target_data_id";
    }
    NodeTable refObjNodeTable = NodeTable.of(Constants.table(targetObjName, null, isStandalone), null,
      Lists.newArrayList(), Sets.newTreeSet(), targetObjName, lookupFieldColumn, false, toNode.getId(), null);
    JoinRelation joinRelation;
    if ("group".equals(fieldType) && "target_data_id".equals(lookupFieldColumn)) {
      joinRelation = JoinRelation.builder()
                                 .apiName(targetObjName)
                                 .column("target_data_id")
                                 .columnType(PGColumnType.String)
                                 .joinType(AggJoinType.LEFT)
                                 .fieldType(FieldType.TEXT)
                                 .build();
    } else {
      joinRelation = this.createRelation(tenantId, this.standalone(tenantId),
        fromNodeTable.getObjectDescribeApiName(), fieldInfo, targetObjName, cachedTableDefinitions, AggJoinType.LEFT);
      if (FieldType.GROUP.equals(fieldInfo.getType()) &&
        Constants.RELATED_OBJECT.equals(fieldInfo.getDbFieldName()) &&
        StringUtils.isNotBlank(fieldInfo.getWhatIdField()) &&
        StringUtils.isNotBlank(fieldInfo.getWhatApiNameField())) {
        refObjNodeTable.setLookupColumn(fieldInfo.getWhatIdField());
        String whatApiWhere = String.format(" AND %s='%s'", fieldInfo.getWhatApiNameField(), mappingService.paasApiName(joinRelation.apiName));
        endTable.appendSubWheres(whatApiWhere);
      }
    }
    NodeJoin lookupTableJoin = Constants.onJoinPlus(tenantId, this.standalone(tenantId), joinRelation, endTable,
      refObjNodeTable, mappingService);
    return endTable.addJoinSetSingle(aliasMapper, lookupTableJoin, true);
  }

  /**
   * 反向查找关联  rt.外键=lt.主键
   * @param tenantId
   * @param isStandalone
   * @param fromNodeTable
   * @param toNodeTable
   * @param aliasMapper
   * @param cachedTableDefinitions
   * @param lookupFieldName
   * @return
   */
  private NodeTable rightNodeTable(String tenantId,
                                   boolean isStandalone,
                                   NodeTable fromNodeTable,
                                   NodeTable toNodeTable,
                                   Map<String, AtomicInteger> aliasMapper,
                                   Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                                   String lookupFieldName) {
    //from join end,  from如果是扩展对象,需要补上预置对象,toNode对应quoteOfAgg,所以始终返回toNode,用以生成quote,
    NodeTable starTable = fromNodeTable;
    NodeTable endTable = toNodeTable;
    UdfObjFieldDO fieldInfo = udfObjFieldMapper.setTenantId(tenantId)
                                               .findUdfObjFieldDOByFieldName(tenantId,
                                                 endTable.getObjectDescribeApiName(),
                                                 mappingService.getApiXName(endTable.getObjectDescribeApiName()),
                                                 lookupFieldName);
    if (fieldInfo == null) {
      log.error("can not find db field tenantId:{},dbObjName:{},dbFieldName:{}", tenantId,
        endTable.getObjectDescribeApiName(), lookupFieldName);
      return null;
    }
    String targetObjName = mappingService.biApiName(endTable.getObjectDescribeApiName());
    String lookupFieldColumn = lookupFieldName;
    String fieldType = fieldInfo.getType();
    String relationTable = fieldInfo.getRelationTable();
    String refKeyField = fieldInfo.getRefKeyField();
    String refObjName = fieldInfo.getRefObjName();
    if (StringUtils.isNotBlank(refObjName) && !Objects.equals(fromNodeTable.getObjectDescribeApiName(), refObjName)) {
      throw new RuntimeException(String.format("this two object has no relationship %s:%s:%s", tenantId,
        fromNodeTable.getObjectDescribeApiName(), targetObjName));
    }
    int objectIdFieldLocation = Integer.parseInt(fieldInfo.getFieldLocation());
    if (objectIdFieldLocation > 0) {
      lookupFieldColumn = "value" + objectIdFieldLocation;
    }
    //from如果是扩展对象,需要补上预置对象
    if (!isStandalone && !fromNodeTable.getObjectDescribeApiName().endsWith("__c") &&
      Constants.OBJECT_DATA.equals(fromNodeTable.getName())) {
      NodeJoin preTableJoin = this.createPreNodeTable(fromNodeTable.getObjectDescribeApiName());
      starTable = fromNodeTable.addJoinSetSingle(aliasMapper, preTableJoin, true);
      fromNodeTable.appendColumns("_id", "object_describe_api_name", "value0", "is_deleted");
    }
    //如果是what 类型的，需要继续增加join relation
    if ("group".equals(fieldType) && StringUtils.isNotBlank(relationTable)) {
      String whatLookupFieldColumn = Constants.getIdName(isStandalone, fromNodeTable.getObjectDescribeApiName());
      NodeTable whatNodeTable = NodeTable.of(relationTable, null, Lists.newArrayList(), Sets.newTreeSet(),
        relationTable, whatLookupFieldColumn, false, null, null);
      //构建和被引用对象的join 关系
      Set<String> onFilter = Sets.newHashSet(Constants.EI_ON_CONDITIONS, Constants.RIGHT_ON_DELETED_0);
      onFilter.add("${lt}.source_data_id=" + "${rt}.id");
      NodeOnCondition nodeOnCondition = new NodeOnCondition(onFilter.toArray(new String[0]), null, null);
      NodeJoin quoteTableJoin = NodeJoin.of(com.fxiaoke.bi.warehouse.common.db.er.JoinType.LEFT_JOIN, toNodeTable,
        nodeOnCondition);
      whatNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, true);
      whatNodeTable.appendColumns("source_data_id");
      whatNodeTable.appendColumns("is_deleted");
      lookupFieldColumn = "target_data_id";
      endTable.appendColumns("is_deleted");
      endTable = whatNodeTable;
    }
    JoinRelation joinRelation;
    if ("group".equals(fieldType) && "target_data_id".equals(lookupFieldColumn)) {
      joinRelation = JoinRelation.builder()
                                 .apiName(starTable.getObjectDescribeApiName())
                                 .column("target_data_id")
                                 .columnType(PGColumnType.String)
                                 .joinType(AggJoinType.LEFT)
                                 .fieldType(FieldType.TEXT)
                                 .build();
    } else {
      //joinRelation.apiName也要用反的,因为joinRelation.referentColumn(standalone, tenantId)获取的主键是写死的
      joinRelation = this.createRelation(tenantId, this.standalone(tenantId),
        targetObjName, fieldInfo, starTable.getObjectDescribeApiName(), cachedTableDefinitions, AggJoinType.LEFT);
      if (FieldType.GROUP.equals(fieldInfo.getType()) &&
        Constants.RELATED_OBJECT.equals(fieldInfo.getDbFieldName()) &&
        StringUtils.isNotBlank(fieldInfo.getWhatIdField()) &&
        StringUtils.isNotBlank(fieldInfo.getWhatApiNameField())) {
        lookupFieldColumn = fieldInfo.getWhatIdField();
        String whatApiWhere = String.format(" AND %s='%s'", fieldInfo.getWhatApiNameField(), mappingService.paasApiName(joinRelation.apiName));
        endTable.appendSubWheres(whatApiWhere);
      }
    }
    endTable.appendColumns(lookupFieldColumn);
    NodeJoin lookupTableJoin = Constants.onJoinPlus(tenantId, this.standalone(tenantId), joinRelation, starTable,
      endTable, mappingService);
    //反转on条件,表结构依然是lt.$lookup,right=rt.id, right join（被查找关联）需要改成rt.$lookup=lt.id
    NodeOnCondition onCondition = lookupTableJoin.getOnCondition();
    List<String> conditions = Lists.newArrayList(onCondition.getEqualPairs());
    conditions.replaceAll(Constants::reverseLtRt);
    //这种情况有两个join条件,且描述上给的信息不对,暂时先写死
    if (Constants.STAGE_RUNTIME.equals(starTable.getName()) && Constants.STAGE_TASK.equals(endTable.getName()) && "stage_id".equals(joinRelation.column)) {
      conditions.add("${lt}.object_id = ${rt}.object_data_id");
      starTable.appendColumns("object_id");
      endTable.appendColumns("object_data_id");
    }
    onCondition.setEqualPairs(conditions.toArray(String[]::new));
    //去除右表子查询on条件字段
    String referentColumn = joinRelation.referentColumn(isStandalone, tenantId);
    if (!referentColumn.equals(joinRelation.column)) {
      endTable.getFilterColumns().remove(referentColumn);
    }
    //添加正确的lookup字段
    endTable.setLookupColumn(referentColumn);
    starTable.addJoinSetSingle(aliasMapper, lookupTableJoin, true);
    return toNodeTable;
  }

  /**
   * 生成dim Rule集合
   * @dimRuleMap mtId-> 不是fieldId,是 dimensionId,detailId,measureId。CH用的是新元数据MT这一天,所以dimRule.fieldId改用为这几个ID
   * @param tenantId
   * @param biMtRule
   * @return
   */
  private List<DimRule> buildDimRule(String tenantId, boolean standalone, BiMtRule biMtRule, String goalActionDateId) {
    List<DimRule> dimRules = Lists.newArrayList();
    Map<String/*nodeId*/, Multimap<String/*dbFieldName*/, String/*mtId*/>> dimRuleMap = biMtRule.findDbFieldNames();
    dimRuleMap.forEach((k, v) -> {
      Node node = biMtRule.findNodeById(k);
      if (node != null) {
        String describeApiName = mappingService.biApiName(node.getDescribeApiName());
        //负责人主属部门转负责人
        if (v.containsKey(Constants.OWNER_DEPARTMENT)) {
          v.get(Constants.OWNER_DEPARTMENT).forEach(mtId -> v.put(Constants.OWNER, mtId + "_owner"));
        }
        //员工对象main_department就是负责人主属部门
        if (Constants.employee_api_name.equals(describeApiName) && v.containsKey(Constants.MAIN_DEPARTMENT)) {
          v.get(Constants.MAIN_DEPARTMENT).forEach(mtId -> v.put(Constants.OWNER, mtId + "_owner"));
        }
        Map<String, Collection<String>> dbFieldNameMtIdListMap = v.asMap();
        Map<String, String> dbFieldNameMap = Maps.newHashMap();
        dbFieldNameMtIdListMap.forEach((dbFieldName, mtIdList) ->
          dbFieldNameMap.put(dbFieldName, mtIdList.stream().findFirst().orElse("")));
        List<DimRule> subDimRules = dimRuleDao.createDimRule(tenantId, describeApiName, dbFieldNameMap,
          DisplayField.DisplayType.group);
        if (CollectionUtils.isNotEmpty(subDimRules)) {
          subDimRules.forEach(dimRule -> {
            DimFieldRule dimFieldRule = dimRule.getDimFieldRule();
            dimFieldRule.nodeId = k;
            dimFieldRule.describeApiName = node.getDescribeApiName();
            //此字段在pg中是numeric,ch中是string,等依赖数据类型切到CH后,此if即可去掉
            if ("base_crmfeedrelation".equals(dimFieldRule.dbObjName) && "feed_id".equals(dimFieldRule.dbFieldName)) {
              dimFieldRule.columnType = PGColumnType.String;
            }
            Collection<String> mtIdList = dbFieldNameMtIdListMap.get(dimFieldRule.dbFieldName);
            //如果是目标查看明细需要固定action_date列做考核维度过滤,所以需要补充生成一个action_date列
            if (mtIdList.contains(goalActionDateId)) {
              DimFieldRule copy = dimFieldRule.copy(goalActionDateId + "_" + Constants.ACTION_DATE, Constants.ACTION_DATE,
                dimFieldRule.fieldType, dimFieldRule.column, dimFieldRule.enableMultiLang, dimFieldRule.sameFieldIds);
              dimRules.add(DimRule.builder().dimFieldRule(copy).build());
            }
            //同一个字段可以同时出现在dim/detail/agg中,dimRule是通过dbFieldName查的,只会查出一个描述,所以如果同时出现了需要对应copy一份,方便槽位映射
            mtIdList.forEach(mtId -> {
              //主属性name需要同时返回id,兼容引用主属性
              if ("name".equals(dimFieldRule.column)) {
                String column = dimFieldRule.joinRelation == null ?
                        Constants.getIdName(standalone, dimFieldRule.apiName) :
                        Constants.getIdName(standalone, dimFieldRule.joinRelation.apiName);
                DimFieldRule copy = dimFieldRule.copy(mtId + Constants._ID, dimFieldRule.dstColumnName, dimFieldRule.fieldType,
                  column, false, dimFieldRule.sameFieldIds);
                dimRules.add(DimRule.builder().dimFieldRule(copy).build());
              }
              if (dimFieldRule.fieldId.equals(mtId)) {
                dimRules.add(dimRule);
              } else {
                DimFieldRule copy = dimFieldRule.copy(mtId, dimFieldRule.dstColumnName, dimFieldRule.fieldType, dimFieldRule.column,
                  dimFieldRule.enableMultiLang, dimFieldRule.sameFieldIds);
                dimRules.add(DimRule.builder().dimFieldRule(copy).build());
              }
            });
          });
        }
      }
    });
    //为每个对象补充主属性
    biMtRule.getBiTopology().getNodes().forEach(node -> {
      String describeApiName = node.getDescribeApiName();
      String biApiName = mappingService.biApiName(describeApiName);
      DimFieldRule dimFieldRule = DimFieldRule.buildIdDimFieldRule(tenantId, describeApiName, biApiName, node.getId(),
              standalone);
      dimRules.add(DimRule.builder().dimFieldRule(dimFieldRule).build());
    });
    return dimRules;
  }

  /**
   * 生成dim Rule集合
   * @dimRuleMap mtId-> 不是fieldId,是 dimensionId,detailId,measureId。CH用的是新元数据MT这一天,所以dimRule.fieldId改用为这几个ID
   * @param tenantId
   * @param biMtRule
   * @return
   */
  private List<DimRule> buildDimRule2(String tenantId, boolean standalone, BiMtRule biMtRule, String goalActionDateId) {
    List<DimRule> dimRules = Lists.newArrayList();
    Map<String/*nodeId*/, Multimap<String/*dbFieldName*/, String/*mtId*/>> dimRuleMapBak = biMtRule.findDbFieldNames();
    Map<String/*nodeId*/, Map<String/*dbFieldName*/, Set<String/*mtId*/>>> dimRuleMap = Maps.newHashMap();
    dimRuleMapBak.forEach((k, v) -> {
      Map<String/*dbFieldName*/, Set<String/*mtId*/>> dbFieldNameMtIdMap = Maps.newHashMap();
      v.asMap().forEach((dbFieldName, mtIdList) -> {
        dbFieldNameMtIdMap.computeIfAbsent(dbFieldName, k1 -> Sets.newHashSet()).addAll(mtIdList);
      });
      dimRuleMap.put(k, dbFieldNameMtIdMap);
    });
    biMtRule.getBiTopology().getNodes().forEach(node -> {
      Map<String, Set<String>> dbFieldMap = dimRuleMap.get(node.getId());
      String describeApiName = node.getDescribeApiName();
      String biApiName = mappingService.biApiName(describeApiName);
      //为每个对象补充主属性
      DimFieldRule dimIDRule = DimFieldRule.buildIdDimFieldRule(tenantId, describeApiName, biApiName, node.getId(), standalone);
      dimRules.add(DimRule.builder().dimFieldRule(dimIDRule).build());
      if (MapUtils.isEmpty(dbFieldMap)) {
        return;
      }
      Set<String> idMtIdSet = dbFieldMap.remove(Constants.getIdName(standalone, biApiName));
      if (CollectionUtils.isNotEmpty(idMtIdSet)) {
        dimIDRule.sameFieldIds.addAll(idMtIdSet);
      }
      //负责人主属部门转负责人
      if (dbFieldMap.containsKey(Constants.OWNER_DEPARTMENT)) {
        dbFieldMap.get(Constants.OWNER_DEPARTMENT).forEach(mtId -> dbFieldMap.computeIfAbsent(Constants.OWNER, k1 -> Sets.newHashSet()).add(mtId + "_owner"));
      }
      //员工对象main_department就是负责人主属部门
      if (Constants.employee_api_name.equals(biApiName) && dbFieldMap.containsKey(Constants.MAIN_DEPARTMENT)) {
        dbFieldMap.get(Constants.MAIN_DEPARTMENT).forEach(mtId -> dbFieldMap.computeIfAbsent(Constants.OWNER, k1 -> Sets.newHashSet()).add(mtId + "_owner"));
      }
      Map<String, String> dbFieldNameMap = Maps.newHashMap();
      dbFieldMap.forEach((dbFieldName, mtIdList) -> dbFieldNameMap.put(dbFieldName, mtIdList.stream().findFirst().orElse("")));
      List<DimRule> subDimRules = dimRuleDao.createDimRule(tenantId, biApiName, dbFieldNameMap, DisplayField.DisplayType.group);
      if (CollectionUtils.isEmpty(subDimRules)) {
        log.warn("tenantId:{} sourceId:{} subDimRules is empty", tenantId, biMtRule.getTopologyDescribeId());
        return;
      }
      subDimRules.forEach(dimRule -> {
        DimFieldRule dimFieldRule = dimRule.getDimFieldRule();
        dimFieldRule.nodeId = node.getId();
        dimFieldRule.describeApiName = node.getDescribeApiName();
        //此字段在pg中是numeric,ch中是string,等依赖数据类型切到CH后,此if即可去掉
        if ("base_crmfeedrelation".equals(dimFieldRule.dbObjName) && "feed_id".equals(dimFieldRule.dbFieldName)) {
          dimFieldRule.columnType = PGColumnType.String;
        }
        Collection<String> mtIdList = dbFieldMap.get(dimFieldRule.dbFieldName);
        //同一个字段可以同时出现在dim/detail/agg中,dimRule是通过dbFieldName查的,只会查出一个描述,所以如果同时出现了需要对应copy一份,方便槽位映射
        dimFieldRule.sameFieldIds.addAll(mtIdList);
        dimRules.add(dimRule);
        //如果是目标查看明细需要固定action_date列做考核维度过滤,所以需要补充生成一个action_date列
        if (mtIdList.contains(goalActionDateId)) {
          String fieldId = goalActionDateId + "_" + Constants.ACTION_DATE;
          DimFieldRule copy = dimFieldRule.copy(fieldId, Constants.ACTION_DATE, dimFieldRule.fieldType, dimFieldRule.column,
            dimFieldRule.enableMultiLang, Sets.newHashSet(fieldId));
          dimRules.add(DimRule.builder().dimFieldRule(copy).build());
        }
        //主属性name需要同时返回id,兼容引用主属性
        if ("name".equals(dimFieldRule.column)) {
          List<String> nameToIdList = mtIdList.stream().map(mtId -> mtId + Constants._ID).toList();
          if (dimFieldRule.joinRelation == null) {
            dimIDRule.sameFieldIds.addAll(nameToIdList);
          } else {
            DimFieldRule copy = dimFieldRule.copy(dimFieldRule.fieldId + Constants._ID, dimFieldRule.dstColumnName,
              dimFieldRule.fieldType, Constants.getIdName(standalone, dimFieldRule.joinRelation.apiName), false, Sets.newHashSet(nameToIdList));
            dimRules.add(DimRule.builder().dimFieldRule(copy).build());
          }
        }
        //date、datetime、timestamp、time类型需要同时返回date和time
        if (StringUtils.equalsAny(dimFieldRule.fieldType, "date", "date_time", "time")) {
          List<String> dateToTimeList = mtIdList.stream().map(mtId -> mtId + Constants._TS).toList();
          DimFieldRule copy = dimFieldRule.copy(dimFieldRule.fieldId + Constants._TS, dimFieldRule.dstColumnName,
            FieldType.NUMBER, dimFieldRule.column, dimFieldRule.enableMultiLang, Sets.newHashSet(dateToTimeList));
          dimRules.add(DimRule.builder().dimFieldRule(copy).build());
        }
      });
    });
    return dimRules;
  }

  /**
   * 判断是否是目标查看明细
   * @param tenantId
   * @param biMtRule
   * @return
   */
  public GoalRuleDO checkGoalRuleDetail(String tenantId, BiMtRule biMtRule) {
    int detailSourceType = biMtRule.getDetailSourceType();
    if (detailSourceType == 1 || detailSourceType == 2) {
      GoalRuleDO goalRuleDO = this.findGoalRuleById(tenantId, biMtRule.getTopologyDescribeId());
      if (goalRuleDO == null || goalRuleDO.getStatus() == 0) {
        return null;
      }
      return goalRuleDO;
    }
    return null;
  }

  /**
   * 如果是目标查看明细则需要获取考核维度映射关系,sql需要拼上考核维度过滤
   * @param tenantId
   * @param biMtRule
   * @return
   */
  public Map<String, String> findGoalCheckFieldLocationMapper(String tenantId, BiMtRule biMtRule, List<DimRule> fieldRules) {
    List<String/*name的mtId*/> nameMtIdList = Lists.newArrayList();
    fieldRules.forEach(dimRule -> {
      if ("name".equals(dimRule.getDimFieldRule().column)) {
        nameMtIdList.add(dimRule.getDimFieldRule().fieldId);
      }
    });
    //从计算完成值的规则上拿到对应的映射关系，再映射到查看明细的规则上来
    Map<String, String> goalRuleDimMap = this.findDimsByParentGoalId(tenantId, biMtRule.getTopologyDescribeId().split("\\|")[0])
              .stream()
              .collect(Collectors.toMap(BIMtDimensionDO::getDimensionId, BIMtDimensionDO::getDimensionField));
    Map<String, String> checkFieldMap = biMtRule.checkFieldLocationMapper(goalRuleDimMap, nameMtIdList);
    if (checkFieldMap.containsKey(null)) {
      log.error("rpt to goal detail, check dims null, tenantId:{}, sourceId:{}", tenantId, biMtRule.getTopologyDescribeId());
      return Collections.emptyMap();
    }
    return checkFieldMap;
  }

  /**
   * 查找关联引用字段
   *
   * @param tenantId
   * @param standalone         是否是schema隔离
   * @param aggNodeTable
   * @param aliasMapper        表别名cache
   * @param quoteOfAgg
   * @param overrideJoinType   add join的时候是否覆盖join type，主对象和扩展对象不用判断
   * @param forceReturnPreNode 强制返回预设对象的 NodeTable对象
   */
  public NodeTable joinQuoteNodeTableRpt(String tenantId,
                                         boolean standalone,
                                         NodeTable aggNodeTable,
                                         Map<String, AtomicInteger> aliasMapper,
                                         QuoteOfAgg quoteOfAgg,
                                         boolean overrideJoinType,
                                         boolean forceReturnPreNode,
                                         int detailSourceType) {
    String aggApiName = aggNodeTable.getObjectDescribeApiName();
    JoinRelation joinRelation = quoteOfAgg.joinRelation;
    if (joinRelation == null) {
      String aggColumn = quoteOfAgg.column;
      //判断是否是扩展字段
      if (!standalone && !aggApiName.endsWith("__c") && Constants.slotRegex.matcher(aggColumn).matches() &&
              !Constants.OBJECT_DATA.equals(aggNodeTable.getName())) {
        //扩展字段需要关联扩展对象
        NodeJoin sourceExtNodeJoin = this.createExtNodeTablePlus(aggApiName, Lists.newArrayList(quoteOfAgg), false);
        NodeTable extNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
        aggNodeTable.appendColumns("extend_obj_data_id", "id");
        quoteOfAgg.tableName = extNodeTable.getName();
        quoteOfAgg.alias = extNodeTable.getAlias();
        return forceReturnPreNode ? aggNodeTable : extNodeTable;
      } else if (!standalone && !aggApiName.endsWith("__c") && !Constants.slotRegex.matcher(aggColumn).matches() &&
              Constants.OBJECT_DATA.equals(aggNodeTable.getName())) {
        //预置字段需要关联预置对象
        NodeJoin sourcePreNodeJoin = this.createPreNodeTablePlus(aggApiName, Lists.newArrayList(quoteOfAgg));
        NodeTable preNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourcePreNodeJoin, true);
        aggNodeTable.appendColumns("_id", "object_describe_api_name", "value0", "is_deleted");
        quoteOfAgg.tableName = preNodeTable.getName();
        quoteOfAgg.alias = preNodeTable.getAlias();
        return forceReturnPreNode ? aggNodeTable : preNodeTable;
      } else {
        //如果是预置对象字段直接加上本字段即可
        NodeColumn extColumn = Constants.createNodeColumn(quoteOfAgg, false);
        if (!aggNodeTable.getColumnList().contains(extColumn)) {
          aggNodeTable.getColumnList().add(extColumn);
        }
        quoteOfAgg.tableName = aggNodeTable.getName();
        quoteOfAgg.alias = aggNodeTable.getAlias();
        return aggNodeTable;
      }
    } else {
      boolean isSelectInvalid = GrayManager.isAllowByRule("rpt_select_quote_invalid_eis", tenantId) && detailSourceType == 3 && "quote".equals(quoteOfAgg.originalType);
      //字段是查找关联其他对象的字段
      //判断被引用字段是否是扩展对象上的字段
      String targetApiName = joinRelation.apiName;
      String targetPreTableName = Constants.table(targetApiName, null, standalone);
      NodeTable quoteNodeTable;
      if (!standalone && !targetApiName.endsWith("__c") && Constants.slotRegex.matcher(quoteOfAgg.column).matches()) {
        //引用字段是扩展对象上的字段
        quoteNodeTable = NodeTable.of(targetPreTableName, null, Lists.newArrayList(), Sets.newTreeSet(), targetApiName, joinRelation.column, false, null, null);
        quoteNodeTable.setSelectInvalid(isSelectInvalid);
        //构建和被引用对象的join 关系
        //判断是否需要通过扩展字段查找关联被引用对象
        if (!aggApiName.endsWith("__c") && Constants.slotRegex.matcher(joinRelation.column).matches() &&
                !Constants.OBJECT_DATA.equals(aggNodeTable.getName())) {
          //通过扩展字段查找关联引用对象。
          NodeJoin sourceExtNodeJoin = this.createExtNodeTable(aggApiName);
          NodeTable sourceExtTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
          aggNodeTable.appendColumns("extend_obj_data_id", "id");
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation, sourceExtTable, quoteNodeTable, mappingService);
          quoteNodeTable = sourceExtTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
        } else if (!aggApiName.endsWith("__c") && !Constants.slotRegex.matcher(joinRelation.column).matches() &&
                Constants.OBJECT_DATA.equals(aggNodeTable.getName())) {
          //预置字段需要关联预置对象
          NodeJoin sourcePreNodeJoin = this.createPreNodeTable(aggApiName);
          NodeTable sourcePreTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourcePreNodeJoin, true);
          aggNodeTable.appendColumns("_id", "object_describe_api_name", "value0", "is_deleted");
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation, sourcePreTable, quoteNodeTable, mappingService);
          quoteNodeTable = sourcePreTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
        } else {
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation, aggNodeTable, quoteNodeTable, mappingService);
          quoteNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
        }
        //join 扩展对象的扩展字段
        NodeJoin targetExtNodeJoin = this.createExtNodeTablePlus(targetApiName, Lists.newArrayList(quoteOfAgg), isSelectInvalid);
        NodeTable targetExtNodeTable = quoteNodeTable.addJoinSetSingle(aliasMapper, targetExtNodeJoin, true);
        quoteNodeTable.appendColumns("extend_obj_data_id", "id");
        quoteOfAgg.tableName = targetExtNodeTable.getName();
        quoteOfAgg.alias = targetExtNodeTable.getAlias();
        return forceReturnPreNode ? quoteNodeTable : targetExtNodeTable;
      } else {
        //引用字段不是扩展对象上的字段
        NodeColumn extColumn = Constants.createNodeColumn(quoteOfAgg, false);
        quoteNodeTable = NodeTable.of(targetPreTableName, null, Lists.newArrayList(extColumn), Sets.newTreeSet(), targetApiName, joinRelation.column, false, null, null);
        quoteNodeTable.setSelectInvalid(isSelectInvalid);
        //构建和被引用对象的join 关系
        String lookupColumn = joinRelation.column;
        //判断是否需要通过扩展字段查找关联被引用对象
        if (!standalone && !aggApiName.endsWith("__c") && Constants.slotRegex.matcher(lookupColumn).matches() &&
                !Constants.OBJECT_DATA.equals(aggNodeTable.getName())) {
          //通过扩展字段查找关联引用对象。
          NodeJoin sourceExtNodeJoin = this.createExtNodeTable(aggApiName);
          NodeTable sourceExtNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
          aggNodeTable.appendColumns("extend_obj_data_id", "id");
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, false, joinRelation, sourceExtNodeTable, quoteNodeTable, mappingService);
          NodeTable quoteNodeTableTmp = sourceExtNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
          quoteOfAgg.tableName = quoteNodeTableTmp.getName();
          quoteOfAgg.alias = quoteNodeTableTmp.getAlias();
          return quoteNodeTableTmp;
        } else if (!standalone && !aggApiName.endsWith("__c") && !Constants.slotRegex.matcher(lookupColumn).matches() &&
                Constants.OBJECT_DATA.equals(aggNodeTable.getName())) {
          //预置字段需要关联预置对象
          NodeJoin sourcePreNodeJoin = this.createPreNodeTable(aggApiName);
          NodeTable sourcePreNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourcePreNodeJoin, true);
          aggNodeTable.appendColumns("_id", "object_describe_api_name", "value0", "is_deleted");
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, false, joinRelation, sourcePreNodeTable, quoteNodeTable, mappingService);
          NodeTable quoteNodeTableTmp = sourcePreNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
          quoteOfAgg.tableName = quoteNodeTableTmp.getName();
          quoteOfAgg.alias = quoteNodeTableTmp.getAlias();
          return quoteNodeTableTmp;
        } else {
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation, aggNodeTable, quoteNodeTable, mappingService);
          NodeTable quoteNodeTableTmp = aggNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, overrideJoinType);
          quoteOfAgg.tableName = quoteNodeTableTmp.getName();
          quoteOfAgg.alias = quoteNodeTableTmp.getAlias();
          return quoteNodeTableTmp;
        }
      }
    }
  }
}
