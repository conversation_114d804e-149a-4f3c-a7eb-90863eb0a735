package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by wang<PERSON><PERSON><PERSON> on 2018/8/7.
 */
@Data
@Table(name = "stat_agg_calc_field")
public class StatCalcFieldDO {
  @Column(name = "calc_field_id")
  private String calcFieldId;
  @Column(name = "field_name")
  private String fieldName;
  @Column(name = "db_obj_name")
  private String dbObjName;
  @Column(name = "formula")
  private String formula;
  @Column(name = "field_type")
  private String fieldType;
  @Column(name = "is_predefined")
  private int isPredefined;
  @Column(name = "ei")
  private int ei;
  @Column(name = "creator")
  private int creator;
  @Column(name = "format_str")
  private String formatStr;
  @Column(name = "precision")
  private Integer precision;
  @Column(name = "create_time")
  private Date createTime;
  @Column(name = "updator")
  private int updator;
  @Column(name = "update_time")
  private Date updateTime;
  @Column(name = "ratio_type")
  private int ratioType;

  /**
   * 将[]中的fieldId 提取出来
   *
   * @return 去重后的fieldId
   */
  public List<String> findAllFormulaFields() {
    if (StringUtils.isBlank(formula)) {
      return null;
    }
    Set<String> formulaFields = new HashSet<>();
    StringBuilder fieldIdSB = new StringBuilder();
    char[] formulaChars = formula.toCharArray();
    int flag = 0;
    for (char currentChar : formulaChars) {
      if (currentChar == 91) {
        flag = 1;
        continue;
      }
      if (currentChar == 93) {
        flag = 0;
        String fieldId = fieldIdSB.toString();
        formulaFields.add(getFormulaAggFieldId(fieldId));
        fieldIdSB.delete(0, fieldIdSB.length());
        continue;
      }
      if (flag == 1) {
        fieldIdSB.append(currentChar);
      }
    }
    return Lists.newArrayList(formulaFields);
  }

  /**
   * 获取计算指标[子指标]的fieldId
   * field_aggrType_ratioType
   * BI_1234_2_120 => BI_1234
   * BI_1234_2     => BI_1234
   */
  private String getFormulaAggFieldId(String fieldStr){
    int lastedIndexOf = fieldStr.lastIndexOf("_");
    /** 是同环比 */
    if ((fieldStr.length() - lastedIndexOf) == 4) {
      fieldStr = fieldStr.substring(0, lastedIndexOf);
    }
    int index = fieldStr.lastIndexOf("_");
    return fieldStr.substring(0, index);
  }

}
