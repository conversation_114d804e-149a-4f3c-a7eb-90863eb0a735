package com.fxiaoke.bi.warehouse.dws.utils;

import com.facishare.converter.EIEAConverter;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.er.ColumnTypeConfig;
import com.fxiaoke.bi.warehouse.common.db.er.NodeColumn;
import com.fxiaoke.bi.warehouse.common.db.er.NodeJoin;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.util.DateTimeUtil;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.dws.model.ActionDateConfig;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.model.WhereConfig;
import com.fxiaoke.bi.warehouse.dws.strategy.AggRemainStrategyFactory;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.service.QiXinNotifyService;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.crypto.HexHash;
import com.fxiaoke.helper.TraceHelper;
import com.github.trace.TraceContext;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.NonNull;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Consumer;
import java.util.regex.Pattern;

@Slf4j
@UtilityClass
public class Utils {

  public static final long ONE_DAY_MILLS = 24L * 60L * 60L * 1000;
  //代表年月日
  private static final Pattern p2 = Pattern.compile("^\\d{4}\\-\\d{2}-\\d{2}.*$");
  //代表毫秒值
  private static final Pattern p3 = Pattern.compile("^\\-?\\d+$");

  private static final Pattern SLOT_PATTERN = Pattern.compile("^value\\d+$");

  public static final Pattern DATE_PATTERN_1 = Pattern.compile("^\\d+$");

  public static final Pattern DATE_PATTERN_2 = Pattern.compile(
    "^\\d{4}-[01]\\d-[0-3]\\d" + "(\\s[0-2]\\d:[0-5]\\d:[0-5]\\d(\\.\\d+)?)?$");

  /**
   * 带时区
   */
  public static final Pattern DATE_PATTERN_4 = Pattern.compile(
    "^\\d{4}-[01]\\d-[0-3]\\d\\s[0-2]\\d:[0-5]\\d:[0-5]\\d" + "\\.\\d{6}[\\+|\\-]\\d{2}$");
  public static final String DATE_FORMAT_1 = "yyyy-MM-dd HH:mm:ss";
  public static final String DATE_FORMAT_2 = "yyyy-MM-dd";
  public static final String DATE_FORMAT_3 = "yyyy-MM-dd HH:mm:ss.SSS";
  public static final String DATE_FORMAT_4 = "yyyy-MM-dd HH:mm:ss.SSSSSSXXX";
  public static final Pattern NUMBER_PATTERN = Pattern.compile("^\\d+(.\\d+)?$");
  public static String filter = "(%s.extend_obj_data_id = '' or %s._id > '0' or  %s.extend_obj_data_id is null)";
  public static String USER_SEGMENT_FILTER_SQL= " AND ei %s (SELECT DISTINCT arrayJoin(segment_values) " +
    "FROM %s.bi_user_segment WHERE tenant_id='%s' AND id in ('%s') AND is_deleted=0) ";
  /**
   * 支持合并计算是个综合配置
   * *|1^BI_xxxxx|1
   */
  public static volatile Set<String> supportCombineCal = Sets.newHashSet();

  /**
   * 检测是否支持统计图合并，目标不能合并
   *
   * @param tenantId 租户id
   * @param viewId   统计图id
   * @return boolean
   */
  public static boolean supportCombineCal(String tenantId, String viewId) {
    return supportCombineCal.contains("*") || supportCombineCal.contains(tenantId) ||
      supportCombineCal.contains(tenantId + "^" + viewId);
  }

  public String formatActionDate(Long stamp) {
    if (stamp == null) {
      return null;
    }
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    return sdf.format(new Date(stamp));
  }

  public String formatActionDateTz(Date date, String timeZone) {
    if (StringUtils.isNotBlank(timeZone)) {
      return DateTimeUtil.toChar(date.getTime(), ZoneId.of(timeZone), DateTimeUtil.formatterYYYYMMdd);
    }
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    return sdf.format(date);
  }

  public Set<String> stringsToArray(Collection<String> values) {
    Set<String> result = Sets.newHashSet();
    if (values == null) {
      return result;
    }
    values.forEach(v -> {
      if (v != null) {
        String vs = CharMatcher.anyOf("\"'[]{}").removeFrom(v);
        List<String> vsl = Splitter.on(CharMatcher.anyOf(",|")).trimResults().omitEmptyStrings().splitToList(vs);
        result.addAll(vsl);
      }
    });
    return result;
  }

  public boolean isCommonObjectTable(String tableName) {
    return StringUtils.equalsIgnoreCase(tableName, "object_data") || StringUtils.equalsIgnoreCase(tableName, "object_data_downstream");
  }

  public boolean isObjectDataLangTable(String tableName) {
    return StringUtils.equalsIgnoreCase(tableName, "object_data_lang");
  }

  public boolean isSlotColumn(String column) {
    if (!column.startsWith("value")) {
      return false;
    }
    return SLOT_PATTERN.matcher(column).matches();
  }

  public int fieldLocation(String column) {
    return Integer.parseInt(column.substring(5));
  }

  public Pair<Long, Long> actionDate2Long(String actionDate, String timeZone) {

    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    if (StringUtils.isNotBlank(timeZone)) {
      sdf.setTimeZone(TimeZone.getTimeZone(ZoneId.of(timeZone)));
    }
    try {
      Date date = sdf.parse(actionDate);
      long zeroMills = date.getTime();
      return new Pair<>(zeroMills, zeroMills + ONE_DAY_MILLS);
    } catch (ParseException e) {
      TraceHelper.trace("{} action date format error.", actionDate);
      throw new RuntimeException("actionDate format error");
    }
  }

  public Date actionDate2Date(String actionDate) {
    if (actionDate == null) {
      return null;
    }
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    try {
      return sdf.parse(actionDate);
    } catch (ParseException e) {
      TraceHelper.trace("{} action date format error.", actionDate);
      throw new RuntimeException("actionDate format error");
    }
  }

  public Long date2Long(String dateFormat, String dateString, String timeZone) {
    SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
    if (StringUtils.isNotBlank(timeZone)) {
      sdf.setTimeZone(TimeZone.getTimeZone(ZoneId.of(timeZone)));
    }
    try {
      return sdf.parse(dateString).getTime();
    } catch (ParseException e) {
      log.error("{} not valid dateformat", dateString, e);
    }
    return null;
  }

  public boolean isExtend(@NonNull String apiName, @NonNull String fieldApiName) {
    if (fieldApiName.endsWith("__c")) {
      return true;
    }
    if (apiName.endsWith("__c")) {
      if ("partner_id".equals(fieldApiName)) {
        return true;
      }
      if ("life_status".equals(fieldApiName)) {
        return true;
      }
    }
    return false;
  }

  public List<String> stringToArray(String value) {
    if (value == null) {
      return Lists.newArrayList();
    }
    List<String> result = Lists.newArrayList();
    String r1 = CharMatcher.anyOf("{} '\"[]").removeFrom(value);
    return Splitter.on(CharMatcher.anyOf(",|")).trimResults().omitEmptyStrings().splitToList(r1);
  }

  /**
   * 获取从现在计算的一个时间点
   *
   * @param days
   * @param hours
   * @return
   */
  public Date fromNow(int days, int hours, String timeZone) {
    if (StringUtils.isNotBlank(timeZone)) {
      LocalDateTime plus = LocalDateTime.now(ZoneId.of(timeZone))
                                        .plus(days, ChronoUnit.DAYS)
                                        .with(ChronoField.HOUR_OF_DAY, hours)
                                        .with(ChronoField.MINUTE_OF_HOUR, 0L)
                                        .with(ChronoField.SECOND_OF_MINUTE, 0)
                                        .with(ChronoField.MILLI_OF_SECOND, 0);
      //系统时区计算日晷是否需要支持多时区
      return Date.from(plus.atZone(ZoneId.of(timeZone)).toInstant());
    } else {
      LocalDateTime plus = LocalDateTime.now()
                                        .plus(days, ChronoUnit.DAYS)
                                        .with(ChronoField.HOUR_OF_DAY, hours)
                                        .with(ChronoField.MINUTE_OF_HOUR, 0L)
                                        .with(ChronoField.SECOND_OF_MINUTE, 0)
                                        .with(ChronoField.MILLI_OF_SECOND, 0);
      return Date.from(plus.atZone(ZoneId.systemDefault()).toInstant());
    }
  }

  private static final String traceFormat = "E-E.%s.%s-%s";

  /**
   * 根据ei 反查 ea
   *
   * @param eieaConverter
   * @param ei
   * @return
   */
  public String enterpriseIdToAccount(EIEAConverter eieaConverter, String ei) {
    try {
      return eieaConverter.enterpriseIdToAccount(Integer.parseInt(ei));
    } catch (Exception e) {
      log.error(" enterpriseIdToAccount error tenantId:{}", ei, e);
    }
    return null;
  }

  /**
   * 给日期数据加上时区
   *
   * @param value
   * @param timeZone
   * @return
   */
  public String castDateTimeWithTimeZone(String value, String timeZone) {
    //    throw new RuntimeException("castDateTimeWithTimeZone need to init");
    if (DATE_PATTERN_1.matcher(value).matches()) {
      return DateTimeUtil.toChar(Long.parseLong(value), ZoneId.of(timeZone), DateTimeUtil.dateTimeFormatterWithZone);
    }
    if (DateTimeUtil.dateTimeWithZoneRex.matcher(value).matches()) {
      return value;
    }
    if (DateTimeUtil.dateTimeWithOutZoneRex.matcher(value).matches() && StringUtils.isNotBlank(timeZone)) {
      return value + " " + DateTimeUtil.timeZoneOffset(timeZone).getId();
    }
    return value;
  }

  /**
   * 生成trace
   *
   * @param tenantId tenantId
   * @param traceId  traceId
   */
  public void createTrace(String tenantId, String ea, String traceId) {
    TraceContext.get().setEi(tenantId);
    TraceContext.get().setUid(tenantId + "." + "-1000");
    TraceContext.get().setEa(ea);
    TraceContext.get().setTraceId(String.format(traceFormat, ea, "-1000", traceId));
  }

  public long getTime(String value, String format) {
    SimpleDateFormat sdf = new SimpleDateFormat(format);
    Date date = null;
    try {
      date = sdf.parse(value);
      return date.getTime();
    } catch (ParseException e) {
      return -1L;
    }
  }

  public String long2DateString(long mills, String timeZone) {
    throw new RuntimeException("actionDate2String is not init ");
  }

  /**
   * 生成的日期格式需要带上时区 yyyy-MM-dd HH:mm:ss Z
   *
   * @param actionDate
   * @param timeZone
   * @return
   */
  public Pair<String, String> actionDate2String(String actionDate, String timeZone) {
    throw new RuntimeException("actionDate2String is not init ");
  }

  public String toNumber(String value) {
    if (NUMBER_PATTERN.matcher(value).matches()) {
      return value;
    }
    if (DATE_PATTERN_2.matcher(value).matches()) {
      if (value.length() == 23) {
        return String.valueOf(getTime(value, DATE_FORMAT_3));
      } else if (value.length() == 19) {
        return String.valueOf(getTime(value, DATE_FORMAT_1));
      } else if (value.length() == 10) {
        return String.valueOf(getTime(value, DATE_FORMAT_2));
      }
    }
    return value;
  }

  /**
   * 从jdbcUrl中解析出数据库名
   *
   * @param jdbcUrl
   * @return
   */
  public String parseDBNameFromJdbcUrl(String jdbcUrl) {
    String url = jdbcUrl;
    int idx = url.indexOf('?');
    if (idx > 0) {
      url = url.substring(0, idx);
    }
    int from = url.lastIndexOf('/');
    return url.substring(from + 1);
  }

  /**
   * 生成md5
   *
   * @param var
   * @return
   */
  public String md5(String var) {
    return HexHash.md5(var);
  }

  /**
   * 生成md5
   *
   * @param var
   * @return
   */
  public String md5(StringBuilder var) {
    return HexHash.md5(var);
  }

  public static String getDBName(String dbURL) {
    String db;
    int index = dbURL.lastIndexOf("/");
    if (dbURL.contains("?")) {
      db = dbURL.substring(index + 1, dbURL.indexOf("?"));
    } else {
      db = dbURL.substring(index + 1);
    }
    if (StringUtils.isNotBlank(db)) {
      return db;
    }
    return "default";
  }

  private static final ThreadLocal<Random> RANDOM = ThreadLocal.withInitial(Random::new);
  /**
   * Return a value which is <code>time</code> increasing exponentially as a
   * function of <code>retries</code>, +/- 0%-50% of that value, chosen
   * randomly.
   *
   * @param time the base amount of time to work with
   * @param retries the number of retries that have so occurred so far
   * @param cap value at which to cap the base sleep time
   * @return an amount of time to sleep
   */
  public static long calculateWaitTime(int retries, long time, long cap) {
    long baseTime = Math.min(time * (1L << retries), cap);
    return (long) (baseTime * (RANDOM.get().nextDouble() + 0.5));
  }

  /**
   * 按百分比随机抽取图
   * @param data 数据集
   * @param per 百分比
   * @return
   */
  public static List<String> getRandomPercentSub(List<String> data, int per) {
    Collections.shuffle(data);
    int size = (int) Math.ceil(data.size() * per / 100.0);
    return data.subList(0, size);
  }

  /**
   * 验证是否转发到灰度topic
   * @param tenantId
   * @param pgDBName
   * @return
   */
  public static boolean useGrayTopic(String tenantId, String pgDBName) {
    return GrayManager.isAllowByRule("use_gray_topic_ei", tenantId) ||
      GrayManager.isAllowByRule("use_gray_topic_pgdb", pgDBName);
  }

  /**
   * 检测是否包含预设对象和扩展对象join
   * 主要解决预设对象扩展对象批次不一致的情况导致
   * @param tenantId
   * @param standalone
   * @param statRule
   */
  public static void checkExtendObjDataExist(String tenantId, boolean standalone, TopologyTableAggRule statRule) {
    if (standalone) {
      return;
    }
    Set<String> preWhereConfigSet = Sets.newHashSet();
    if (CollectionUtils.isNotEmpty(statRule.getPreWhereConfigList())) {
      statRule.getPreWhereConfigList().forEach(whereConfig -> {
        if (StringUtils.isNotBlank(whereConfig.getWhereExpr())) {
          preWhereConfigSet.add(whereConfig.getWhereExpr());
        }
      });
    }
    Queue<NodeTable> queue = new LinkedBlockingQueue<>();
    queue.add(statRule.getRootNodeTable());
    while (!queue.isEmpty()) {
      NodeTable first = queue.poll();
      Set<NodeJoin> nodeJoins = first.getJoinSet();
      if (CollectionUtils.isNotEmpty(nodeJoins)) {
        nodeJoins.forEach(nodeJoin -> {
          NodeTable subNodeTable = nodeJoin.getTable();
          if (Objects.equals("object_data", subNodeTable.getName()) &&
            !subNodeTable.getObjectDescribeApiName().endsWith("__c")) { //如果是扩展对象
            String whereExpr = String.format(filter, first.getAlias(), subNodeTable.getAlias(), first.getAlias());
            if (!preWhereConfigSet.contains(whereExpr)) {
              statRule.getPreWhereConfigList().add(WhereConfig.builder().whereExpr(whereExpr).build());
            }
          }
          queue.add(subNodeTable);
        });
      }
    }
  }

  public static Long dateString2Long(String value, String timeZone) {
    if (value == null) {
      return null;
    }
    if (Utils.DATE_PATTERN_1.matcher(value).matches()) {
      return Long.parseLong(value);
    }
    if (value.length() == 10) {
      return Utils.date2Long(Utils.DATE_FORMAT_2, value, timeZone);
    }
    if (value.length() == 19) {
      return Utils.date2Long(Utils.DATE_FORMAT_1, value, timeZone);
    }
    if (value.length() == 23) {
      return Utils.date2Long(Utils.DATE_FORMAT_1, value, timeZone);
    }
    return null;
  }

  // 时间段是日期格式需要包含该天
  public static Long dateAddDay(String value, Long datetime) {
    if (value == null || Utils.DATE_PATTERN_1.matcher(value).matches() || datetime == null) {
      return datetime;
    }
    if (value.length() == 10) {
      return datetime + (24 * 60 * 60 * 1000) - 1;
    }
    return datetime;
  }

  public Long actionDate2Stamp(String actionDate, String timeZone) {
    if (actionDate == null) {
      return null;
    }
    try {
      if (StringUtils.isNotBlank(timeZone)) {
        return DateTimeUtil.covertYYYYMMDD2Timestamp(actionDate, ZoneId.of(timeZone));
      } else {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.parse(actionDate).getTime();
      }
    } catch (ParseException e) {
      TraceHelper.trace("{} action date format error.", actionDate);
      throw new RuntimeException("actionDate format error");
    }
  }

  /**
   * 有些企业主题只计算近N天数据
   * @param tenantId
   * @param themeApiName
   * @param timeZone
   * @param statRule
   */
  public void checkMinActionDate(String tenantId, String themeApiName, String timeZone, int source, TopologyTableAggRule statRule) {
    //只处理统计图
    if (source == 0 && statRule.getActionDateConfigString() != null) {
      //有些主题，计算结果只需要保存近N天的数据
      String minActionDate = AggRemainStrategyFactory.minActionDate(tenantId, themeApiName, timeZone);
      if (minActionDate == null || Constants.ACTION_DATE_EMPTY.equals(minActionDate)) {
        return;
      }
      String minFrom = String.valueOf(Utils.actionDate2Stamp(minActionDate,timeZone));
      ActionDateConfig actionDateConfig = ActionDateConfig.parse(statRule.getActionDateConfigString(), timeZone);
      NodeTable timeTable = Constants.findTableByAlias(statRule.getRootNodeTable(),
        actionDateConfig.getTableAlias());
      timeTable.getColumnList().forEach(nodeColumn -> {
        String columnName = nodeColumn.getName();
        if (actionDateConfig.getColumnName().equals(columnName)) {
          String fromWhereExpr;
          if (Constants.OLD_STAT_VIEW_GOAL_RULE_IDS.contains(statRule.getFieldId())) {
            fromWhereExpr = columnName + " >= '" + minActionDate + "'";
          } else {
            fromWhereExpr = switch (Objects.requireNonNull(nodeColumn.getType())) {
              case _int -> columnName + " >= " + dateString2Long(minFrom, timeZone);
              case _Decimal -> columnName + " >= toDecimal128(" + dateString2Long(minFrom, timeZone) + ",20)";
              default -> "toInt64OrNull(" + columnName + ") >= toInt64(" + dateString2Long(minFrom, timeZone) + ")";
            };
          }
          timeTable.appendSubWheres(String.format(" AND %s ", fromWhereExpr));
        }
      });
    }
  }

  /**
   * 将action_date条件内移到子查询
   * @param timeZone
   * @param statRule
   */
  public void checkSubActionDate(String timeZone,
                                 TopologyTableAggRule statRule,
                                 String actionDateStart,
                                 String actionDateEnd) {
    if ((actionDateStart == null || Constants.ACTION_DATE_EMPTY.equals(actionDateStart)) &&
      (actionDateEnd == null || Constants.ACTION_DATE_NULL.equals(actionDateEnd))) {
      return;
    }
    ActionDateConfig actionDateConfig = ActionDateConfig.parse(statRule.getActionDateConfigString(), timeZone);
    NodeTable timeTable = Constants.findTableByAlias(statRule.getRootNodeTable(),
      actionDateConfig.getTableAlias());
    for (NodeColumn nodeColumn : timeTable.getColumnList()) {
      String columnName = nodeColumn.getName();
      if (actionDateConfig.getColumnName().equals(columnName)) {
        String fieldType = actionDateConfig.getSourceColumnTypeConfig().getOrDefault(ColumnTypeConfig._ActionDate.FIELD_TYPE, "");
        if (actionDateStart != null && !Constants.ACTION_DATE_EMPTY.equals(actionDateStart)) {
          String startWhereExpr = columnName + " >= '" + actionDateStart + "'";
          if (!"date2".equals(fieldType)) {
            String start = String.valueOf(Utils.actionDate2Stamp(actionDateStart,timeZone));
            startWhereExpr = switch (Objects.requireNonNull(nodeColumn.getType())) {
              case _int -> columnName + " >= " + dateString2Long(start, timeZone);
              case _Decimal -> columnName + " >= toDecimal128(" + dateString2Long(start, timeZone) + ",20)";
              default -> "toInt64OrNull(" + columnName + ") >= toInt64(" + dateString2Long(start, timeZone) + ")";
            };
          }
          timeTable.appendSubWheres(String.format(" AND %s ", startWhereExpr));
        }
        if (actionDateEnd != null && !Constants.ACTION_DATE_NULL.equals(actionDateEnd)) {
          String endWhereExpr = columnName + " <= '" + actionDateEnd + "'";
          if (!"date2".equals(fieldType)) {
            String end = String.valueOf(Utils.actionDate2Stamp(actionDateEnd,timeZone) + 24 * 60 * 60 * 1000);
            endWhereExpr = switch (Objects.requireNonNull(nodeColumn.getType())) {
              case _int -> columnName + " < " + Utils.dateString2Long(end, timeZone);
              case _Decimal -> columnName + " < toDecimal128(" + Utils.dateString2Long(end, timeZone) + ",20)";
              default -> "toInt64OrNull(" + columnName + ") < toInt64(" + Utils.dateString2Long(end, timeZone) + ")";
            };
          }
          timeTable.appendSubWheres(String.format(" AND %s ", endWhereExpr));
        }
      }
    }
  }

  private static final ConcurrentHashMap<String, Long> overTimeMapper = new ConcurrentHashMap<>();

  public static void checkOverTime(SyncStatusEnum statusEnum,
                                   TransferEvent transferEvent,
                                   String timeKey,
                                   long startTime,
                                   long delayTimeThreshold,
                                   String errorMsg,
                                   Consumer<Long> handler) {
    long delayTime = overTimeMapper.computeIfAbsent(timeKey, key -> delayTimeThreshold);
    long currentDelayTime = System.currentTimeMillis() - startTime;
    if (currentDelayTime >= delayTime) {
      log.error("{}: is delay:{} ms", errorMsg, currentDelayTime);
      overTimeMapper.put(timeKey, delayTime + (30 * 60 * 1000L));
      QiXinNotifyService.sendTextMessage(statusEnum, transferEvent, currentDelayTime, startTime, delayTime);
      if(handler!=null){
        handler.accept(currentDelayTime);
      }
    } else {
      log.warn("{}: is delay:{} ms", errorMsg, currentDelayTime);
    }
  }

  /**
   * 批量清除
   * @param timeKeys
   */
  public static void resetDelayTime(List<String> timeKeys) {
    if (CollectionUtils.isNotEmpty(timeKeys)) {
      timeKeys.forEach(overTimeMapper::remove);
    }
  }
}
