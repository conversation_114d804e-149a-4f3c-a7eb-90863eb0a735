package com.fxiaoke.bi.warehouse.ods.service;

import com.clickhouse.client.ClickHouseClient;
import com.clickhouse.client.ClickHouseClientBuilder;
import com.clickhouse.client.ClickHouseCredentials;
import com.clickhouse.client.config.ClickHouseClientOption;
import com.clickhouse.client.config.ClickHouseDefaults;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.common.PasswordUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class CHClientService {

  private String user;
  private String password;
  private final ConcurrentHashMap<String, ClickHouseClient> clientConcurrentHashMap = new ConcurrentHashMap<>();
  private volatile int chClientSocketTimeout = 500 * 1000;
  private volatile int writeBufferSize = 40960;
  private volatile int readBufferSize = 40960;
  static {
    System.setProperty("default_max_threads", "50");
  }

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", new IniChangeListener("common") {
      @Override
      public void iniChanged(IniConfig iniConfig) {
        String userTmp = iniConfig.get("chproxy.cal.user.name.pre");
        String passwordTmp = PasswordUtil.decode(iniConfig.get("chproxy.cal.paasword"));
        if (!Objects.equals(user, userTmp) || !Objects.equals(password, passwordTmp)) {
          user = userTmp;
          password = passwordTmp;
          clientConcurrentHashMap.clear();
        }
        chClientSocketTimeout = iniConfig.getInt("ch_client_socket_timeout",500*1000);
        writeBufferSize = iniConfig.getInt("write_buffer_size",20480);
        readBufferSize = iniConfig.getInt("read_buffer_size",20480);
      }
    });
  }

  /**
   * 获取clickhouse client 存储在缓冲区中
   *
   * @param jdbcURL ch jdbc url
   * @return ClickHouseClient
   */
  public ClickHouseClient findClient(String jdbcURL) {
    return clientConcurrentHashMap.computeIfAbsent(jdbcURL, key -> {
      String dbName = CHContext.getDBName(jdbcURL);
      String userNameSuffix = dbName.substring(0, dbName.length() - 3);
      ClickHouseClientBuilder builder = ClickHouseClient.builder();
      builder.defaultCredentials(ClickHouseCredentials.fromUserAndPassword(user + userNameSuffix, password));
      builder.option(ClickHouseDefaults.MAX_THREADS, 50);
      builder.option(ClickHouseClientOption.SOCKET_TIMEOUT, chClientSocketTimeout);
      builder.option(ClickHouseClientOption.WRITE_BUFFER_SIZE, writeBufferSize);
      builder.option(ClickHouseClientOption.READ_BUFFER_SIZE, readBufferSize);
      return builder.build();
    });
  }
}
