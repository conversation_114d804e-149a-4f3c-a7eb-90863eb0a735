package com.fxiaoke.bi.warehouse.dws.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/4/6
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WhereConfig {
  private String tableName;
  private String tableAlias;
  private String column;
  private String operator;
  private String value1;
  private String value2;
  /**
   * 筛选条件类型
   */
  private int filterType;
  /**
   * 对象apiName
   */
  private String objectApiName;
  /**
   * 筛选sql表达式
   */
  private String whereExpr;

  public String toSqlExpr() {
    return whereExpr;
  }
}
