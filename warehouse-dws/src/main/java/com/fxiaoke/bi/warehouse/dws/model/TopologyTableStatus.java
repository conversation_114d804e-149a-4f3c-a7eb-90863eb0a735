package com.fxiaoke.bi.warehouse.dws.model;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum TopologyTableStatus {
  /**
   * 出错跳过
   */
  failSkip(-2),
  /**
   * 不用了
   */
  UnUsed(-1),
  /**
   * 准备做全量
   */
  Prepared(0),
  /**
   * 增量计算中
   */
  Calculating(1),
  /**
   * 不用计算
   */
  NONeedCal(2);

  private final int value;

  TopologyTableStatus(int value) {
    this.value = value;
  }

  public static TopologyTableStatus from(int value) {
    switch (value) {
      case -2 -> {
        return failSkip;
      }
      case -1 -> {
        return UnUsed;
      }
      case 0 -> {
        return Prepared;
      }
      case 1 -> {
        return Calculating;
      }
      case 2 -> {
        return NONeedCal;
      }
      default -> throw new RuntimeException("no support this enum value");
    }
  }

  public static int[] allStatus() {
    return Arrays.stream(values()).mapToInt(TopologyTableStatus::getValue).toArray();
  }

  public static int[] needCalcStatus() {
    return new int[] {TopologyTableStatus.Prepared.getValue(), TopologyTableStatus.Calculating.getValue(), TopologyTableStatus.failSkip.getValue(), TopologyTableStatus.NONeedCal.getValue()};
  }
}
