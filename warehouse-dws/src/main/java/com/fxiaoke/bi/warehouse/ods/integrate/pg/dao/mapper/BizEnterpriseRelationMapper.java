package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper;

import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BizEnterpriseRelationDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/4/7
 */
@Mapper
public interface BizEnterpriseRelationMapper extends IBatchMapper<BizEnterpriseRelationDO>, ITenant<BizEnterpriseRelationMapper> {

  /**
   * 获取企业上下游企业
   */
  @Select("select enterprise_account from biz_enterprise_relation where tenant_id=#{tenantId} and is_deleted=0 and " +
    "enterprise_account is not null and mapper_account_id=any(select id from biz_account where tenant_id=#{tenantId} " +
    "and record_type in('dealer__c','mollercular_company__c'))")
  List<String> queryBizEnterpriseRelation(@Param("tenantId") String tenantId);

  /**
   * 查询企业ObjectId
   */
  @Select("SELECT enterprise_account AS ea, mapper_account_id AS object_id FROM biz_enterprise_relation WHERE tenant_id=#{tenantId} AND is_deleted = 0 AND enterprise_account IS NOT NULL AND mapper_account_id = ANY (SELECT id FROM biz_account WHERE tenant_id=#{tenantId} AND record_type IN ('dealer__c', 'mollercular_company__c'))")
  List<Map<String, String>> queryBizEnterpriseRelationMap(@Param("tenantId") String tenantId);

  /**
   * 根据下游企业ea查询objectId
   */
  @Select("<script>" +
    "SELECT enterprise_account AS ea, mapper_account_id AS object_id " +
    "FROM biz_enterprise_relation " +
    "WHERE tenant_id = #{tenantId} " +
    "AND enterprise_account IN " +
    "<foreach item='item' index='index' collection='enterpriseAccounts' open='(' separator=',' close=')'>" +
    "#{item}" +
    "</foreach>" +
    "AND is_deleted = 0" +
    "</script>")
  List<Map<String, String>> queryEaToObjectIdMap(@Param("tenantId") String tenantId, @Param("enterpriseAccounts") List<String> enterpriseAccounts);

  /**
   * 根据下游企业查询
   */
  @Select("<script>" +
    "SELECT id, " +
    "       enterprise_account AS enterpriseAccount, " +
    "       mapper_account_id, " +
    "       name, " +
    "       upstream_short_name AS upstreamShortName, " +
    "       register_type AS registerType " +
    "FROM biz_enterprise_relation " +
    "WHERE tenant_id = #{tenantId} " +
    "AND id IN " +
    "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'>" +
    "#{item}" +
    "</foreach>" +
    "AND is_deleted = 0" +
    "</script>")
  List<BizEnterpriseRelationDO> queryBizEnterpriseRelationByIds(@Param("tenantId") String tenantId, @Param("ids") List<String> ids);

  /**
   * 根据下游企业的id进行查询
   */
  @Select("select * from biz_enterprise_relation where tenant_id = #{tenantId} and id = #{id} and is_deleted = 0")
  BizEnterpriseRelationDO queryBizEnterpriseRelationById(@Param("id") String id, @Param("tenantId") String tenantId);
}
