package com.fxiaoke.bi.warehouse.dws.db.mapper;

import com.fxiaoke.bi.warehouse.common.provider.CommonProvider;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler.AggDownStreamHandler;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler.AggEffectMapTypeHandler;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler.StatRuleListTypeHandler;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.provider.TopologyProvider;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface TopologyTableMapper extends ICrudMapper<TopologyTableDO>, ITenant<TopologyTableMapper> {

  /**
   * 查询所有topology table 数据，排除了view_sql大文本的字段，联查table_merge表查询 version 和 status
   */
  @Select("""
      SELECT  bmtt.id
             ,bmtt.tenant_id
             ,bmtt.source_id
             ,bmtt.api_name
             ,bmtt.timezone
             ,bmtt.common_dims
             ,bmtt.stat_list_json
             ,bmtt.is_deleted
             ,CASE WHEN bmtt.source in(1,2) THEN bmtt.version ELSE COALESCE(bmttm.version,0) END AS version
             ,bmtt.created_by
             ,bmtt.create_time
             ,bmtt.last_modified_time
             ,COALESCE(bmttm.status,bmtt.status) AS status
             ,bmtt.stat_field_location
             ,bmtt.uniq_field_location
             ,bmtt.source
             ,bmtt.sys_modified_time
             ,bmtt.agg_effect_api_names
             ,COALESCE(bmttm.batch_num,bmtt.batch_num) AS batch_num
             ,bmtt.stat_view_unique_key
             ,bmtt.agg_downstream_json
             ,bmtt.database_id
      FROM bi_mt_topology_table bmtt
      LEFT JOIN bi_mt_topology_table_merge bmttm
      ON stat_view_unique_key = bmttm.id AND bmtt.tenant_id = bmttm.tenant_id AND bmttm.is_deleted=0
      WHERE bmtt.tenant_id = #{tenantId}
      AND bmtt.source in(0,1,2)
      AND bmtt.status=any(array[#{status}])
      AND bmtt.is_deleted = 0
    """)
  @ResultMap("findNeedCalculateList")
  List<TopologyTableDO> findCalcFromTableMergeList(@Param("tenantId") String tenantId, @Param("status") int[] status);

  @Select("""
    SELECT  bmtt.id
             ,bmtt.tenant_id
             ,bmtt.source_id
             ,bmtt.api_name
             ,bmtt.timezone
             ,bmtt.common_dims
             ,bmtt.stat_list_json
             ,bmtt.is_deleted
             ,CASE WHEN bmtt.source in(1,2) THEN bmtt.version ELSE COALESCE(bmttm.version,0) END AS version
             ,bmtt.created_by
             ,bmtt.create_time
             ,bmtt.last_modified_time
             ,COALESCE(bmttm.status,bmtt.status) AS status
             ,bmtt.stat_field_location
             ,bmtt.uniq_field_location
             ,bmtt.source
             ,bmtt.sys_modified_time
             ,bmtt.agg_effect_api_names
             ,COALESCE(bmttm.batch_num,bmtt.batch_num) AS batch_num
             ,bmtt.stat_view_unique_key
             ,bmtt.agg_downstream_json
             ,bmtt.database_id
      FROM bi_mt_topology_table bmtt
      LEFT JOIN bi_mt_topology_table_merge bmttm
      ON stat_view_unique_key = bmttm.id AND bmtt.tenant_id = bmttm.tenant_id AND bmttm.is_deleted=0
      WHERE bmtt.tenant_id = #{tenantId}
      AND bmtt.source_id=#{sourceId}
      AND bmtt.is_deleted = 0 limit 1
    """)
  @Results(id = "findByEiAndSourceIdWithMergeVersion", value = {@Result(property = "statRuleList", column = "stat_list_json",
    typeHandler = StatRuleListTypeHandler.class), @Result(column = "agg_effect_api_names", property =
    "aggEffectApiNames", typeHandler = AggEffectMapTypeHandler.class), @Result(column = "agg_downstream_json",
    property = "aggDownStream", typeHandler = AggDownStreamHandler.class)})
  TopologyTableDO findByEiAndSourceIdWithMergeVersion(@Param("tenantId") String tenantId, @Param("sourceId") String sourceId);

  @Select("""
    SELECT id,
           tenant_id,
           source_id,
           api_name,
           timezone,
           common_dims,
           stat_list_json,
           is_deleted,
           version,
           created_by,
           create_time,
           last_modified_time,
           status,
           stat_field_location,
           uniq_field_location,
           source,
           sys_modified_time,
           agg_effect_api_names,
           batch_num,
           stat_view_unique_key,
           agg_downstream_json,
           database_id
    FROM bi_mt_topology_table
    WHERE tenant_id = #{tenantId}
      AND source in(0,1,2)
      AND status=any(array[#{status}])
      AND is_deleted = 0
    """)
  @Results(id = "findNeedCalculateList", value = {@Result(property = "statRuleList", column = "stat_list_json",
    typeHandler = StatRuleListTypeHandler.class), @Result(column = "agg_effect_api_names", property =
    "aggEffectApiNames", typeHandler = AggEffectMapTypeHandler.class), @Result(column = "agg_downstream_json",
    property = "aggDownStream", typeHandler = AggDownStreamHandler.class)})
  List<TopologyTableDO> findNeedCalculateList(@Param("tenantId") String tenantId, @Param("status") int[] status);

  @Select("""
    SELECT id,
           tenant_id,
           source_id,
           api_name,
           timezone,
           common_dims,
           stat_list_json,
           is_deleted,
           version,
           created_by,
           create_time,
           last_modified_time,
           status,
           stat_field_location,
           uniq_field_location,
           source,
           sys_modified_time,
           agg_effect_api_names,
           batch_num,
           stat_view_unique_key,
           agg_downstream_json,
           database_id
    FROM bi_mt_topology_table
    WHERE tenant_id = #{tenantId}
      AND source_id = any(array[#{sourceIds}])
      AND source in(1,2)
      AND status = 1
      AND is_deleted = 0
    """)
  @Results(id = "findNeedCalculateListByChangeGoal", value = {@Result(property = "statRuleList", column = "stat_list_json",
          typeHandler = StatRuleListTypeHandler.class), @Result(column = "agg_effect_api_names", property =
          "aggEffectApiNames", typeHandler = AggEffectMapTypeHandler.class), @Result(column = "agg_downstream_json",
          property = "aggDownStream", typeHandler = AggDownStreamHandler.class)})
  List<TopologyTableDO> findNeedCalculateListByChangeGoal(@Param("tenantId") String tenantId, @Param("sourceIds") String[] sourceIds);

  /**
   * 根据source_id 反查unique_key和版本
   * @param tenantId 租户id
   * @param sourceId 图id
   * @return
   */
  @Select("select stat_view_unique_key,version from bi_mt_topology_table where tenant_id=#{tenantId} and source_id=#{sourceId} limit 1")
  Map<String,Object> queryUniqueKeyAndVersion(@Param("tenantId") String tenantId,
                                              @Param("sourceId") String sourceId);

  @InsertProvider(type = CommonProvider.class, method = "batchUpsert")
  int upsertTopologyInfo(@Param(CommonProvider.FKey) List<TopologyTableDO> list,
                         @Param(CommonProvider.SKey) Set<String> primaryKey);

  @UpdateProvider(type = TopologyProvider.class, method = "updateWithVersion")
  int upsertTopologyWithVersion(@Param("compare_version") int version,
                                @Param("topologyTableDO") TopologyTableDO topologyTableDO);

  /**
   * 根据sourceId 更新topology 状态和最新的同步时间
   *
   * @param tenantId
   * @param viewId
   * @param status
   * @param version
   * @param batchNum
   * @param maxModifiedTime
   * @return
   */
  int updateTopologyStatusByViewId(@Param("tenantId") String tenantId,
                                   @Param("viewId") String viewId,
                                   @Param("status") Integer status,
                                   @Param("version") int version,
                                   @Param("batchNum") long batchNum,
                                   @Param("maxModifiedTime") Long maxModifiedTime,
                                   @Param("isUpdateStatRule") boolean isUpdateStatRule,
                                   @Param("statListJson") String statListJson);

  /**
   * 根据unique key 更新topology 状态和最新的同步时间
   *
   * @param tenantId
   * @param status
   * @param uniqueKey
   * @param batchNum
   * @param maxModifiedTime
   * @return
   */
  int updateTopologyStatusByUniqueKey(@Param("tenantId") String tenantId,
                                      @Param("status") Integer status,
                                      @Param("uniqueKey") String uniqueKey,
                                      @Param("batchNum") long batchNum,
                                      @Param("maxModifiedTime") Long maxModifiedTime,
                                      @Param("isUpdateStatRule") boolean isUpdateStatRule,
                                      @Param("statListJson") String statListJson,
                                      @Param("incVersion") boolean incVersion);

  @Update("update bi_mt_topology_table set status=#{status},version=version+1 where tenant_id=#{tenantId} and source_id=any" +
    "(array[#{viewIds}])")
  int updateTopologyTableStatus(@Param("tenantId") String tenantId,
                                @Param("viewIds") String[] viewIds,
                                @Param("status") int status);
  @Update("update bi_mt_topology_table set status=#{status},version=version+1 where tenant_id=#{tenantId} and " +
    "source_id=#{viewId} and version=#{pVersion}")
  int updateTopologyTableStatusWithVersion(@Param("tenantId") String tenantId,
                                           @Param("viewId") String viewId,
                                           @Param("status") int status,
                                           @Param("pVersion") int preVersion);
  /**
   * 修改删除状态
   * @param tenantId 租户id
   * @param viewId 图id
   * @param isDeleted 删除状态
   * @return
   */
  @Update(
    "update bi_mt_topology_table set is_deleted=#{isDeleted} where tenant_id=#{tenantId} and source_id=#{viewId}" +
      " and version=#{pVersion}")
  int updateTopologyDelWithVersion(@Param("tenantId") String tenantId,
                                   @Param("viewId") String viewId,
                                   @Param("isDeleted") int isDeleted,
                                   @Param("pVersion") int preVersion);

  int batchDeleteTopologyTableBySourceId(@Param("tenantId") String tenantId,
                                         @Param("viewIds") String[] viewIds,
                                         @Param("isDeleted") int isDeleted,
                                         @Param("source") Integer source);

  @Update("update bi_mt_topology_table set pg_detail_sql=#{pgDetailSql} where tenant_id=#{tenantId} and source_id=#{viewId}")
  int updateStatViewDetailSql(@Param("tenantId") String tenantId,
                               @Param("viewId") String viewId,
                               @Param("pgDetailSql") String pgDetailSql);

  @Select("SELECT source_id,agg_effect_api_names FROM bi_mt_topology_table where tenant_id=#{tenantId} and is_deleted=0 and source=0")
  List<Map<String,String>> queryAggRuleEffectTenantId(@Param("tenantId") String tenantId);

  @Update("update bi_mt_topology_table set all_agg_stat_field=#{allAggStatField} where tenant_id=#{tenantId} and source_id=#{viewId}")
  int updateAllAggStatField(@Param("tenantId") String tenantId,
                            @Param("viewId") String viewId,
                            @Param("allAggStatField") String allAggStatField);

  @Select("SELECT * FROM bi_mt_topology_table where tenant_id=#{tenantId} and source_id=#{sourceId} and is_deleted=0 " +
    "limit 1")
  @Results(id = "findByTenantIdAndSourceId", value = {@Result(property = "statRuleList", column = "stat_list_json",
    typeHandler = StatRuleListTypeHandler.class), @Result(column = "agg_effect_api_names", property =
    "aggEffectApiNames", typeHandler = AggEffectMapTypeHandler.class), @Result(column = "agg_downstream_json",
    property = "aggDownStream", typeHandler = AggDownStreamHandler.class)})
  TopologyTableDO findByTenantIdAndSourceId(@Param("tenantId") String tenantId, @Param("sourceId") String sourceId);

  @Select(
    "SELECT latest_agg_time,agg_effect_api_names,batch_num FROM bi_mt_topology_table where tenant_id=#{tenantId} and " +
      "source_id=#{sourceId} " +
      "and is_deleted=0 limit 1")
  Map<String, Object> findAggTimeAndEffectApiBySourceId(@Param("tenantId") String tenantId,
                                                        @Param("sourceId") String sourceId);

  /**
   * 根据sourceId获取topologyTable，忽略业务状态和删除状态
   *
   * @param tenantId 租户id
   * @param sourceId 图id
   * @return TopologyTableDO
   */
  @Select("SELECT * FROM bi_mt_topology_table where tenant_id=#{tenantId} and source_id=#{sourceId} limit 1")
  @Results(id = "findByTenantIdAndSourceIdWithDel", value = {@Result(property = "statRuleList", column =
    "stat_list_json", typeHandler = StatRuleListTypeHandler.class), @Result(column = "agg_effect_api_names",
    property = "aggEffectApiNames", typeHandler = AggEffectMapTypeHandler.class), @Result(column = "agg_downstream_json",
    property = "aggDownStream", typeHandler = AggDownStreamHandler.class)})
  TopologyTableDO findByTenantIdAndSourceIdWithDel(@Param("tenantId") String tenantId,
                                                   @Param("sourceId") String sourceId);

  /**
   * 根据字段查找用到该字段的图集合
   *
   * @param tenantId 租户id
   * @param status
   * @param fieldIds 指标或维度id
   * @return 统计图集合
   */
  @Select("SELECT bmtt.* FROM bi_mt_topology_table bmtt,jsonb_object_keys(stat_field_location::jsonb) as fieldId " +
    "where bmtt.tenant_id=#{tenantId} and bmtt.status=any(array[#{status}]) and fieldId=any(array[#{fieldIds}]) and " +
    "is_deleted=0")
  @Results(id = "queryTopologyTableByFieldId", value = {@Result(property = "statRuleList", column = "stat_list_json",
    typeHandler = StatRuleListTypeHandler.class), @Result(column = "agg_effect_api_names", property =
    "aggEffectApiNames", typeHandler = AggEffectMapTypeHandler.class), @Result(column = "agg_downstream_json",
    property = "aggDownStream", typeHandler = AggDownStreamHandler.class)})
  List<TopologyTableDO> queryTopologyTableByFieldId(@Param("tenantId") String tenantId,
                                                    @Param("status") int[] status,
                                                    @Param("fieldIds") String[] fieldIds);


  @Select("SELECT bmtt.* FROM bi_mt_topology_table bmtt,jsonb_object_keys(all_agg_stat_field::jsonb) as fieldId " +
    "where bmtt.tenant_id=#{tenantId} and bmtt.status=any(array[#{status}]) and fieldId=any(array[#{fieldIds}]) and " +
    "bmtt.is_deleted=0 and bmtt.source=0")
  @Results(id = "queryTopologyTableFromAllAgg", value = {@Result(property = "statRuleList", column = "stat_list_json",
    typeHandler = StatRuleListTypeHandler.class), @Result(column = "agg_effect_api_names", property =
    "aggEffectApiNames", typeHandler = AggEffectMapTypeHandler.class), @Result(column = "agg_downstream_json",
    property = "aggDownStream", typeHandler = AggDownStreamHandler.class)})
  List<TopologyTableDO> queryTopologyTableFromAllAgg(@Param("tenantId") String tenantId,
                                                    @Param("status") int[] status,
                                                    @Param("fieldIds") String[] fieldIds);

  /**
   * 根据api_name 反查该主题的所有统计图
   *
   * @param tenantId     租户id
   * @param themeApiName 主题apiName
   * @return 统计图集合
   */
  @Select(
    "SELECT * FROM bi_mt_topology_table where tenant_id=#{tenantId} and api_name=#{apiName} and status=any" +
      "(array[#{status}])" +
      " and is_deleted=0 and source=#{source} ")
  @Results(id = "queryTopologyTableByApiName", value = {@Result(property = "statRuleList", column = "stat_list_json",
    typeHandler = StatRuleListTypeHandler.class), @Result(column = "agg_effect_api_names", property =
    "aggEffectApiNames", typeHandler = AggEffectMapTypeHandler.class), @Result(column = "agg_downstream_json",
    property = "aggDownStream", typeHandler = AggDownStreamHandler.class)})
  List<TopologyTableDO> queryTopologyTableByApiName(@Param("tenantId") String tenantId,
                                                    @Param("apiName") String themeApiName,
                                                    @Param("status") int[] status,
                                                    @Param("source") int sourceType);

  @Select("SELECT tenant_id,source_id,stat_field_location,stat_view_unique_key,status FROM bi_mt_topology_table where tenant_id=#{tenantId} and " +
    "source_id=any(array[#{sourceIds}]) and status in (0,1,2) and is_deleted=0")
  List<Map<String, Object>> batchQueryFieldLocations(@Param("tenantId") String tenantId,
                                                     @Param("sourceIds") String[] sourceIds);

  @Select("select max(version) as version from bi_mt_topology_table where tenant_id=#{tenantId} and " +
    "source_id=#{sourceId} and is_deleted = 0 and status = 1 ")
  Integer queryMaxVersion(@Param("tenantId") String tenantId, @Param("sourceId") String sourceId);

  /**
   * 指标对比，只对比已启用的
   * @param tenantId
   * @param sourceIds
   * @return
   */
  @Select("select source_id from bi_mt_topology_table where tenant_id=#{tenantId} and " +
    "source_id=any(array[#{sourceIds}]) and is_deleted = 0 and status = 1 ")
  List<String> querySourceIds(@Param("tenantId") String tenantId, @Param("sourceIds") String[] sourceIds);

  /**
   * 根据sourceId更新topology table 状态
   *
   * @param tenantId   租户id
   * @param sourceType 0 -> agg, 1 -> 多维度目标
   * @param status     状态
   * @param sourceIds  统计图id集合
   * @return
   */
  int resetTopologyByEi(@Param("tenantId") String tenantId,
                        @Param("source") int sourceType,
                        @Param("status") int status,
                        @Param("sourceIds") String[] sourceIds);

  @Select("SELECT source_id FROM bi_mt_topology_table where tenant_id=#{tenantId} and " +
    "stat_view_unique_key=#{uniqueKey} and is_deleted=0")
  List<String> queryViewIdByUniqueKey(@Param("tenantId") String tenantId, @Param("uniqueKey") String statViewUniqueKey);

  @Select("SELECT source_id FROM bi_mt_topology_table where tenant_id=#{tenantId} and " +
    "stat_view_unique_key=#{uniqueKey} and status=any(array[#{status}]) and is_deleted=0")
  List<String> queryViewIdByUniqueKeyAndStatus(@Param("tenantId") String tenantId,
                                               @Param("uniqueKey") String statViewUniqueKey,
                                               @Param("status") int[] status);

  /**
   * 根据uniqueKey查询已启用统计图数量,排除自己
   *
   * @param tenantId 租户id
   * @param statViewUniqueKey 去重key
   * @return
   */
  @Select("select status from bi_mt_topology_table where tenant_id=#{tenantId} and " +
    "stat_view_unique_key=#{uniqueKey} and source_id <> #{sourceId} and is_deleted = 0 and status in (1,2) limit 1")
  Map<String,Object> queryUsedByUniqueKeyExcludedSelf(@Param("tenantId") String tenantId, @Param("uniqueKey") String statViewUniqueKey,@Param("sourceId") String sourceId);

  /**
   * 根据uniqueKey查询初始化中或已启用的统计图数量,排除自己
   *
   * @param tenantId 租户id
   * @param statViewUniqueKey 去重key
   * @return
   */
  @Select("select count(1) as size from bi_mt_topology_table where tenant_id=#{tenantId} and " +
    "stat_view_unique_key=#{uniqueKey} and is_deleted = 0 and status in (0,1,2) ")
  int queryTopologyByUniqueKey(@Param("tenantId") String tenantId, @Param("uniqueKey") String statViewUniqueKey);

  @Select("select view_sql from bi_mt_topology_table where tenant_id=#{tenantId} and stat_view_unique_key=#{viewId} and source=any(array[#{sources}]) and is_deleted=0 and status in (0,1,2) limit 1")
  String queryViewSqlById(@Param("tenantId") String tenantId,  @Param("viewId") String viewId, @Param("sources") int []source);

  @Update("update bi_mt_topology_table set view_sql = #{viewSql} where tenant_id=#{tenantId} and stat_view_unique_key = #{uniqueKey} and is_deleted=0 and status in (0,1,2)")
  int updateViewSqlByUniqueKey(@Param("tenantId") String tenantId, @Param("uniqueKey") String uniqueKey, @Param("viewSql") String viewSql);

  @Select("select source_id,status from bi_mt_topology_table where tenant_id=#{tenantId} and stat_view_unique_key=#{viewId} and is_deleted=0 and status in (0,1,2)")
  List<Map<String,Object>> querySourceIdAndViewSqlById(@Param("tenantId") String tenantId,  @Param("viewId") String viewId);

  @Select("SELECT tenant_id, source_id FROM bi_mt_topology_table WHERE tenant_id=#{tenantId} and source = 0 AND is_deleted = 0 AND status IN (0, 1, 2)  AND " +
    "EXISTS (SELECT 1 FROM jsonb_each_text(stat_field_location::jsonb) AS kv WHERE kv.key IN ('BI_f1763fde9d8c8be29674150b86ce5e9f','BI_f1763fde9d8c8be29674150b86ce5e9f_dept','BI_5d9ff3331b9ad40001873713','BI_5d9ff3331b9ad40001873778'))")
  List<Map<String,Object>> queryNeedRepairGoalTopologyByEI(@Param("tenantId") String tenantId);

  @Select("select source_id from bi_mt_topology_table where tenant_id=#{tenantId} and source =0 and status in(0,1,2) and is_deleted=0 " +
    "and view_sql like '%goal_value_obj%'")
  List<String> queryGoalViewIdTopologyByEI(@Param("tenantId") String tenantId);

  @Select("select source_id from bi_mt_topology_table where tenant_id=#{tenantId} and source in(1,2) and status in(0,1,2) and is_deleted=0 ")
  List<String> queryGoalRuleIdTopologyByEI(@Param("tenantId") String tenantId);
}