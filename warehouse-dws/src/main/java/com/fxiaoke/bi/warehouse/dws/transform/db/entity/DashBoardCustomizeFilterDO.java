package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author:jief
 * @Date:2023/7/10
 */
@Table(name = "dash_board_customize_filter")
@Data
public class DashBoardCustomizeFilterDO {

  @Id
  @Column(name = "config_id")
  private String configId;

  /**
   * 租户id
   */
  @Column(name = "tenant_id")
  private String tenantId;

  /**
   * 驾驶舱id
   */
  @Column(name = "dash_board_id")
  private String dashBoardId;

  /**
   * 用户ID
   */
  @Column(name = "user_id")
  private String userId;

  /**
   * 筛选器组
   */
  @Column(name = "filter_group")
  private String filterGroup;

  /**
   * 筛选器名称
   */
  @Column(name = "filter_name")
  private String filterName;

  /**
   * 操作符
   */
  @Column(name = "operator")
  private int operator;

  /**
   * 筛选值1
   */
  @Column(name = "filter_value1")
  private String filterValue1;

  /**
   * 筛选值2
   */
  @Column(name = "filter_value2")
  private String filterValue2;

  /**
   * 筛选器范围(驾驶舱图表及筛选字段数据)
   */
  @Column(name = "filter_range")
  private String filterRange;

  /**
   * 驾驶舱配置类型（‘global’：驾驶舱级别配置 ‘personal’：个人级别配置）
   */
  @Column(name = "config_type")
  private String configType;

  @Column(name = "creator")
  private String creator;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "last_modifier")
  private String lastModifier;

  @Column(name = "last_modified_time")
  private Date lastModifiedTime;

  @Column(name = "is_deleted")
  private int isDeleted;
}
