package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper;

import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler.AggDownStreamHandler;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler.AggEffectMapTypeHandler;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler.StatRuleListTypeHandler;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.TopologyTableIntegrateDO;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper.handler.AggDownstreamHandler;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper.handler.AllAggStatFieldHandler;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper.handler.StatFieldLocationHandler;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @Author:jief
 * @Date:2024/4/15
 */
@Mapper
public interface BIMtTopologyTableMapper extends ITenant<BIMtTopologyTableMapper> {
  /**
   * 获取上游企业的，下游agg数据指标信息
   *
   * @param tenantId
   * @return
   */
  @Select("select tenant_id,source_id,source,agg_downstream_json,status from bi_mt_topology_table " +
    " where tenant_id=#{tenantId} and source=0 and agg_downstream_json is not null")
  @Results(id = "queryIntegrateFromTopology", value = {@Result(property = "aggDownstreamJson", column =
    "agg_downstream_json", typeHandler = AggDownstreamHandler.class)})
  List<TopologyTableIntegrateDO> queryIntegrateFromTopology(@Param("tenantId") String tenantId);

  /**
   * 获取下游的拓扑图字段槽位信息
   *
   * @param tenantId
   * @param sourceIds
   * @return
   */
  @Select(
    "select tenant_id,source_id,source,version,stat_view_unique_key,stat_field_location,uniq_field_location,status from bi_mt_topology_table " +
      " where tenant_id=#{tenantId} and source_id=any(array[#{sourceIds}]) and source=0")
  @Results(id = "queryFieldLocationFromTopology", value = {@Result(property = "statFieldLocation", column =
    "stat_field_location", typeHandler = StatFieldLocationHandler.class), @Result(column = "stat_field_location",
    property = "statFieldLocation", typeHandler = StatFieldLocationHandler.class)})
  List<TopologyTableIntegrateDO> queryFieldLocationFromTopology(@Param("tenantId") String tenantId,
                                                                @Param("sourceIds") String[] sourceIds);

  /**
   * 获取下游的图合并多版本拓扑图
   */
  @Select("""
      SELECT  bmtt.tenant_id AS tenant_id
             ,bmtt.source_id AS source_id
             ,CASE WHEN bmtt.source in(1,2) THEN bmtt.version ELSE COALESCE(bmttm.version,0) END AS version
             ,COALESCE(bmttm.status,bmtt.status) AS status
             ,bmtt.stat_field_location AS stat_field_location
             ,bmtt.uniq_field_location AS uniq_field_location
             ,bmtt.source AS source
             ,bmtt.stat_view_unique_key AS stat_view_unique_key
             ,bmtt.all_agg_stat_field AS all_agg_stat_field
      FROM bi_mt_topology_table bmtt
      LEFT JOIN bi_mt_topology_table_merge bmttm
      ON stat_view_unique_key = bmttm.id AND bmtt.tenant_id = bmttm.tenant_id AND bmttm.is_deleted=0
      WHERE bmtt.tenant_id = #{tenantId}
      AND bmtt.source_id=any(array[#{sourceIds}])
      AND bmtt.is_deleted = 0
    """)
  @Results(id = "querySourceTenantTopology", value = {@Result(property = "statFieldLocation", column =
    "stat_field_location", typeHandler = StatFieldLocationHandler.class), @Result(column = "stat_field_location",
    property = "statFieldLocation", typeHandler = StatFieldLocationHandler.class), @Result(column = "all_agg_stat_field",
    property = "aggAggStatField", typeHandler = AllAggStatFieldHandler.class)})
  List<TopologyTableIntegrateDO> querySourceTenantTopology(@Param("tenantId") String tenantId, @Param("sourceIds") String[] sourceIds);

  @Results(id = "findByTenantIdAndSourceId", value = {@Result(property = "statRuleList", column = "stat_list_json",
    typeHandler = StatRuleListTypeHandler.class), @Result(column = "agg_effect_api_names", property =
    "aggEffectApiNames", typeHandler = AggEffectMapTypeHandler.class), @Result(column = "agg_downstream_json",
    property = "aggDownStream", typeHandler = AggDownStreamHandler.class)})
  @Select("select * from bi_mt_topology_table where tenant_id = #{tenant_id} and source_id = #{view_id} and is_deleted = 0 limit 1")
  TopologyTableDO queryTopologyTable(@Param("tenant_id") String tenantId, @Param("view_id") String viewId);
}
