package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.DimSysDateDO;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * @Author:jief
 * @Date:2023/9/27
 */
@Repository
public interface DimSysDateMapper extends ITenant<DimSysDateMapper> {
  //查询财年每个周的周一日期
  @Select(
    "select min(pk) as minDate, max(pk) as maxDate from dim_sys_date where f_year between #{fromYear} and #{toYear} " +
      " and f_week >= #{fromWeek}  and f_week < #{toWeek} and ei = -1 ")
  Map<String, Object> findAllMonDayByFiscalYear(@Param("fromYear") Integer fromYear,
                                                @Param("toYear") Integer toYear,
                                                @Param("fromWeek") String fromWeek,
                                                @Param("toWeek") String toWeek);

  //查询财年每个月的起始天
  @Select("select min(pk) as minDate, max(pk) as maxDate from dim_sys_date where " +
    "      f_month >= cast(#{fromMonth} as integer) and f_month < cast(#{toMonth} as integer) and ei = -1 ")
  Map<String, Object> findAllMonthByFiscalYear(@Param("fromMonth") String fromMonth, @Param("toMonth") String toMonth);

  //查询财年每个季度的起始天
  @Select("select min(pk) as minDate, max(pk) as maxDate from dim_sys_date where " +
    " f_season >= #{fromQuarter} and f_season < #{toQuarter} and ei = -1  ")
  Map<String, Object> findAllQuarterByFiscalYear(@Param("fromQuarter") String fromQuarter,
                                                @Param("toQuarter") String toQuarter);

  //查找某一天日期信息
  @Select("select pk,f_year,f_month,f_day,f_season,f_week from dim_sys_date where pk=#{pk} and ei = -1")
  DimSysDateDO findDayOfYear(@Param("pk") String pk);

}
