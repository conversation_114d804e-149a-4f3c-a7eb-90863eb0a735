package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewPropertyDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StatViewPropertyMapper extends ICrudMapper<StatViewPropertyDO>, IBatchMapper<StatViewPropertyDO>, ITenant<StatViewPropertyMapper> {
  @Select("select * from stat_view_property where view_id ${view_id} and ei=#{ei} and is_delete=0")
  List<StatViewPropertyDO> queryStatViewPropertyByIds(@Param("view_id") String viewId, @Param("ei") int ei);

  //根据view id 反查是否有agg 指标字段
  @Select("select * from stat_view_property where ei = #{ei} and view_id = any(#{viewIds}) and chart_property_id=any(array['88','92']) and is_delete = 0")
  List<StatViewPropertyDO> findAggFieldFromPropertyByVids(@Param("ei") Integer ei, @Param("viewIds") String[] viewIds);
}
