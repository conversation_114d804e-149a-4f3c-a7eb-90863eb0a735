package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Date;

@Data
@Table(name = "stat_view_filter")
public class StatViewFilterDO {
  @Column(name = "filter_id")
  private String filterId;
  @Column(name = "view_id")
  private String viewId;
  @Column(name = "field_id")
  private String fieldId;
  @Column(name = "parent_id")
  private String parentId;
  @Column(name = "display_num")
  private Integer displayNum;
  @Column(name = "operator")
  private Integer operator;
  @Column(name = "value1")
  private String value1;
  @Column(name = "value2")
  private String value2;
  @Column(name = "is_default")
  private int isDefault;
  @Column(name = "date_range_id")
  private String dateRangeId;
  @Column(name = "ei")
  private Integer ei;
  @Column(name = "filter_type")
  private Integer filterType;
  @Column(name = "option_id")
  private String optionId;
  @Column(name = "is_lock")
  private int isLock;
  @Column(name = "creator")
  private int creator;
  @Column(name = "create_time")
  private Date createTime;
  @Column(name = "updator")
  private int updator;
  @Column(name = "update_time")
  private Date updateTime;
  @Column(name = "is_delete")
  private int isDelete;
}
