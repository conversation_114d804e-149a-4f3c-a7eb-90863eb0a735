package com.fxiaoke.bi.warehouse.ods.bean;

import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.context.PGDataType;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.Builder;
import lombok.Data;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;

/**
 * 数据库表的schema信息
 */
@Data
@Builder
public class PGSchema {
  private String name;
  private String db;
  private String eiName;
  private String eiType;
  private List<String> columns;
  private List<String> primaryKeys;
  private Map<String, String> timeColumns;
  private Map<String,String> pointColumns;
  public Pair<String, String> schemaAndTableName() {
    String schemaName = "public";
    String tableName = this.name;
    int pos = name.indexOf('.');
    if (pos != -1) {
      schemaName = this.name.substring(0, pos);
      tableName = this.name.substring(pos + 1);
    }
    return Pair.build(schemaName, tableName);
  }

  public String findPrimaryKeyId() {
    if (primaryKeys == null) {
      return null;
    }
    if (primaryKeys.contains("id")) {
      return "id";
    }
    if (primaryKeys.contains("_id")) {
      return "_id";
    }
    String tableName = this.schemaAndTableName().second;
    switch (tableName) {
      case "feed_relation" -> {
        return "relation_id";
      }
      case "dim_sys_date", "dim_sys_area_gray" -> {
        return "pk";
      }
      case "mt_tag" -> {
        return "tag_id";
      }
      default -> {
        if (tableName.endsWith("_relation") && primaryKeys.contains("relation_id")) {
          return "relation_id";
        }
        List<String> tmpPk = Lists.newArrayList(primaryKeys);
        Optional<String> pkColumn = tmpPk.stream()
                                         .filter(pk -> !StringUtils.equalsAny(pk, "ei", "tenant_id"))
                                         .findFirst();
        return pkColumn.orElse(null);
      }
    }
  }

  /**
   * 判断该表是否有删除字段列名 is_deleted
   *
   * @return
   */
  public boolean existsDeletedField() {
    return columns.contains("is_deleted");
  }

  /**
   * 获取此表的时间字段用于过滤变更范围数据
   *
   * @return 字段名称和类型对
   */
  public Pair<String/*column*/, String/*type*/> findFilterTimeColumn() {
    if (MapUtils.isNullOrEmpty(this.timeColumns)) {
      return null;
    }
    if (this.timeColumns.containsKey(CHContext.SYS_MODIFIED_TIME)) {
      return Pair.build(CHContext.SYS_MODIFIED_TIME, this.timeColumns.get(CHContext.SYS_MODIFIED_TIME));
    } else if (this.timeColumns.containsKey(CHContext.LAST_MODIFIED_TIME)) {
      return Pair.build(CHContext.LAST_MODIFIED_TIME, this.timeColumns.get(CHContext.LAST_MODIFIED_TIME));
    } else if (this.timeColumns.containsKey(CHContext.CREATE_TIME)) {
      return Pair.build(CHContext.CREATE_TIME, this.timeColumns.get(CHContext.CREATE_TIME));
    }else if (this.timeColumns.containsKey(CHContext.CREATE_DATE)) {
      return Pair.build(CHContext.CREATE_DATE, this.timeColumns.get(CHContext.CREATE_DATE));
    }
    return null;
  }

  /**
   * 创建查询sql
   *
   * @param orderType
   * @param sysTimeRange 采用左开右闭(a,b]
   * @param batchSize
   * @return
   */
  public String createQuerySQL(List<String> tenantId, String orderType, Pair<Long, Long> sysTimeRange, int batchSize) {
    if (this.findPrimaryKeyId() == null) {
      throw new RuntimeException(MessageFormat.format("can not find id for order by table:{0}", name));
    }
    Pair<String, String> timeColumnType = this.findFilterTimeColumn();
    if (timeColumnType == null) {
      throw new RuntimeException(MessageFormat.format("can not find time field for filter table:{0}", name));
    }
    SQL sql = new SQL();
    StringBuilder wheres = new StringBuilder();
    if (tenantId.size() == 1) {
      wheres.append(eiName).append(" = ").append("'").append(tenantId.get(0)).append("'");
    } else if (tenantId.size() > 1) {
      String eiSQL = tenantId.stream().map(ei -> "'" + ei + "'").collect(Collectors.joining(","));
      wheres.append(eiName).append(" = ").append("any(array[").append(eiSQL).append("])");
    }
    if (sysTimeRange != null) {
      wheres.append(" ").append("AND").append(" ");
      wheres.append(timeColumnType.first).append(" > ");
      if ("string".equals(timeColumnType.second)) {
        wheres.append("'").append(sysTimeRange.first).append("'");
      } else {
        wheres.append(sysTimeRange.first);
      }
      wheres.append(" ").append("AND").append(" ");
      wheres.append(timeColumnType.first).append(" <= ");
      if ("string".equals(timeColumnType.second)) {
        wheres.append("'").append(sysTimeRange.second).append("'");
      } else {
        wheres.append(sysTimeRange.second);
      }
    }
    wheres.append(" ").append("AND").append(" ");
    wheres.append(this.findPrimaryKeyId()).append(" > ").append("'%s'");
    String orderBy = this.findPrimaryKeyId();
    if (tenantId.size() > 0 &&
      GrayManager.isAllowByRule("specialOrder", String.format("%s^%s", tenantId.get(0), this.schemaAndTableName().second))) {
      orderBy = this.findPrimaryKeyId() + " || ''";
    }
    sql.SELECT(this.createSelectSQL()).FROM(this.name).WHERE(wheres.toString()).ORDER_BY(orderBy + " " + orderType).LIMIT(batchSize);
    return sql.toString();
  }

  /**
   * 生成 select列名称
   * @return
   */
  private String createSelectSQL() {
    if (!this.pointColumns.isEmpty()) {
      return this.columns.stream().map(name -> {
        String pointType = this.pointColumns.get(name);
        if (Objects.equals(pointType, PGDataType.GEOGRAPHY)) {
          return String.format("ST_AsText(%s) AS %s", name, name);
        }
        return name;
      }).collect(Collectors.joining(","));
    }
    return "*";
  }
  /**
   * 根据主键创建查询sql
   * @param tenantId 租户id
   * @param orderType 排序类型
   * @param primaryKeys 主键集合
   * @param batchSize 批次大小
   * @return
   */
  public String createQuerySQLByPrimaryKey(List<String> tenantId, String orderType,List<String> primaryKeys, int batchSize) {
    if (this.findPrimaryKeyId() == null) {
      throw new RuntimeException(MessageFormat.format("can not find id for order by table:{0}", name));
    }
    String inSQL = primaryKeys.stream().map(pk -> "'" + pk + "'").collect(Collectors.joining(","));
    SQL sql = new SQL();
    StringBuilder wheres = new StringBuilder();
    if (tenantId.size() == 1) {
      wheres.append(eiName).append(" = ").append("'").append(tenantId.get(0)).append("'");
      wheres.append(" ").append("AND").append(" ");
    } else if (tenantId.size() > 1) {
      String eiSQL = tenantId.stream().map(ei -> "'" + ei + "'").collect(Collectors.joining(","));
      wheres.append(eiName).append(" = ").append("any(array[").append(eiSQL).append("])");
      wheres.append(" ").append("AND").append(" ");
    }
    wheres.append(this.findPrimaryKeyId()).append(" in (").append(inSQL).append(")");
    sql.SELECT(this.createSelectSQL())
       .FROM(this.name)
       .WHERE(wheres.toString())
       .ORDER_BY(this.findPrimaryKeyId() + " " + orderType)
       .LIMIT(batchSize);
    return sql.toString();
  }

  /**
   * 获取该表按照租户分组获取最大的变更时间
   *
   * @param tenantId         租户id
   * @param fromModifiedTime 其实变更时间
   * @return
   */
  public String createSQLByEiAndModifiedTime(String tenantId, long fromModifiedTime) {
    Pair<String, String> timeColumnType = this.findFilterTimeColumn();
    if (timeColumnType == null) {
      throw new RuntimeException(MessageFormat.format("can not find time field for filter table:{}", name));
    }
    SQL sql = new SQL();
    StringBuilder wheres = new StringBuilder(" 1=1 ");
    if (StringUtils.isNotBlank(tenantId)) {
      wheres.append(" ").append("AND").append(" ");
      wheres.append(eiName).append(" = ").append("'").append(tenantId).append("'");
    }
    wheres.append(" ").append("AND").append(" ");
    wheres.append(timeColumnType.first).append(" > ").append(fromModifiedTime);

    sql.SELECT(eiName + " as ei ,MAX(" + timeColumnType.first + ") as max_sys_modified_time")
       .FROM(this.name)
       .WHERE(wheres.toString())
       .GROUP_BY(eiName);
    return sql.toString();
  }

  /**
   * 获取该表按照租户分组获取最大的变更时间
   *
   * @param fromModifiedTime 其实变更时间
   * @return
   */
  public String createSQLByModifiedTime(Optional<String> tenantIdOP, Long fromModifiedTime) {
    Pair<String, String> timeColumnType = this.findFilterTimeColumn();
    if (timeColumnType == null) {
      throw new RuntimeException(MessageFormat.format("can not find time field for filter table:{}", name));
    }
    SQL sql = new SQL();
    StringBuilder wheres = new StringBuilder(" 1=1 ");
    if (tenantIdOP.isPresent()) {
      wheres.append(" ").append("AND").append(" ");
      wheres.append(eiName).append(" = ").append("'").append(tenantIdOP.get()).append("'");
    }
    StringBuilder select = new StringBuilder(timeColumnType.first + " AS max_sys_modified_time");
    if (tenantIdOP.isPresent()) {
      select.append(",").append(eiName).append(" AS ei");
    }
    if (fromModifiedTime != null) {
      wheres.append(" AND ");
      wheres.append(timeColumnType.first).append(" > ").append(fromModifiedTime);
    }
    sql.SELECT(select.toString())
       .FROM(this.name)
       .WHERE(wheres.toString())
       .ORDER_BY(timeColumnType.first + " DESC ")
       .LIMIT(1);
    return sql.toString();
  }


  public String createIndex() {
    Pair<String, String> timePair = findFilterTimeColumn();
    if (timePair == null) {
      return null;
    }
    if (StringUtils.isBlank(findPrimaryKeyId())) {
      return null;
    }
    Pair<String, String> schemaTablePair = schemaAndTableName();
    return "CREATE INDEX concurrently IF NOT EXISTS " + schemaTablePair.second + "_ei_id_smt" + " on " + name + "(" +
      eiName + "," + findPrimaryKeyId() + "," + timePair.first + " desc );";
  }

  /**
   * 生成CH order by 字段集合
   *
   * @return List
   */
  public List<String> createOrderByList() {
    List<String> orderByFields = Lists.newArrayList();
    Set<String> useCols = Sets.newHashSet();
    //定义order by
    if (StringUtils.isNotBlank(this.getEiName())) {
      orderByFields.add(this.getEiName());
      useCols.add(this.getEiName());
    }
    if (StringUtils.equalsAny(this.findTableName(), "object_data", "biz_account")) {
      orderByFields.add("object_describe_api_name");
    }
    //order by 中增加 bi_sys_flag 字段防止合并掉业务数据。
    orderByFields.add(CHContext.BI_SYS_FLAG);
    String idName = this.findPrimaryKeyId();
    if (StringUtils.isNotBlank(idName)) {
      orderByFields.add(idName);
      useCols.add(idName);
    }
    //添加其他联合主键字段作为order by字段
    this.getPrimaryKeys().stream().filter(field -> !useCols.contains(field)).forEach(orderByFields::add);
    if(StringUtils.equalsAny(this.findTableName(),"dt_auth_simple","dt_auth_out_simple")){
      orderByFields.remove("id");
    }
    return orderByFields;
  }

  /**
   * 获取表名
   * @return
   */
  public String findTableName(){
    String tableName = this.name;
    int pos = name.indexOf('.');
    if (pos != -1) {
      tableName = this.name.substring(pos + 1);
    }
    return tableName;
  }

  /**
   * 创建比较sql
   * @param tenantId 租户id
   * @param ids 主键id集合
   * @return
   */
  public String createPgCompareSQl(String tenantId,List<String> ids){
    String comparesql="SELECT id FROM (SELECT unnest(array[%s]) as id ) AS source_ids WHERE id NOT IN ( SELECT %s FROM %s WHERE %s )";
    String inIdSQL=ids.stream().map(id->"'"+id+"'").collect(Collectors.joining(","));
    String whereSQL=eiName+"='"+tenantId+"' and "+findPrimaryKeyId()+" in ("+inIdSQL+")";
    return MessageFormat.format(comparesql,inIdSQL,findPrimaryKeyId(),name,whereSQL);
  }
}
