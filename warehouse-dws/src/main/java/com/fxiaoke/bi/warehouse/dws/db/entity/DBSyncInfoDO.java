package com.fxiaoke.bi.warehouse.dws.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "db_sync_info")
public class DBSyncInfoDO {
  @Id
  private String id;
  private String chDB;
  private String pgDB;
  private String pgSchema;
  private int status;
  private Long batchNum;
  private Long createTime;
  private long lastModifiedTime;
  private int isDeleted;
  private Long lastSyncTime;
  private String lastSyncEis;
  /**
   * 是否开启增量分区同步0:否,1:是
   */
  private Integer allowIncPartition;
  @Column(name = "allow_paas2bi_status")
  private int allowPaas2BiStatus;
}
