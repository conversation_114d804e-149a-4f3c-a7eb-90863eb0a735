package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Map;

@Builder
public class WhereRules {

  /**
   * 有序集合，才能保证初始化的归一
   */
  @Getter
  @Setter
  private ArrayList<ArrayList<WhereRule>> whereRulesList;

  /**
   * 表的对象名称，方便提前过滤
   */
  public Map<String, String> listenTableApiNameMap;

  public void init(AggRule aggRule) {
    if (null == whereRulesList) {
      return;
    }
    whereRulesList.forEach(wl -> {
      wl.forEach(w -> {
        w.init(aggRule);
        //某些表只监听固定的描述变更
        String table = aggRule.whereTable(w);
        if (table.equals("base_crmfeedrelation")) {
          if ("object_describe_api_name".equals(w.column)) {
            initAndPut("base_crmfeedrelation", w.getValue1());
          }
        }
      });
    });
  }

  public void initAndPut(String table, String apiName) {
    if (listenTableApiNameMap == null) {
      listenTableApiNameMap = Maps.newHashMap();
    }
    listenTableApiNameMap.put(table, apiName);
  }
}
