package com.fxiaoke.bi.warehouse.ods.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.component.MybatisBITenantPolicy;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.bson.types.ObjectId;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * @Author:jief
 * @Date:2023/6/14
 */
@Slf4j
@Service
public class RouterChangeConsumer implements ApplicationListener<ContextRefreshedEvent>, MessageListenerOrderly {
  private AutoConfMQPushConsumer consumer;
  @Resource(name = "cHRouterPolicy")
  private CHRouterPolicy chRouterPolicy;
  @Resource
  private MybatisBITenantPolicy mybatisBITenantPolicy;
  @Resource
  private PgCommonDao pgCommonDao;
  @Resource
  private CHMetadataService chMetadataService;

  @PostConstruct
  public void init() {
    consumer = new AutoConfMQPushConsumer("fs-bi-warehouse", "routerChangeConsumer", this);
  }

  /**
   * 延迟消费路由变更，判断ch路由是否存在
   * //todo 如果是路由变更，需要发消息通知重刷规则描述
   *
   * @param msgs    msgs.size() >= 1<br> DefaultMQPushConsumer.consumeMessageBatchMaxSize=1,you can modify here
   * @param context
   * @return
   */
  @Override
  public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
    for (MessageExt msg : msgs) {
      try {
        String tags = msg.getTags();
        String body = new String(msg.getBody(), StandardCharsets.UTF_8);
        JSONObject jsonObject = JSON.parseObject(body);
        String tenantId = null;
        switch (tags) {
          case "routeChange" -> {
            log.info("process routeChange:{}", body);
            String biz = jsonObject.getString("biz");
            if (MybatisBITenantPolicy.BIZ.equalsIgnoreCase(biz)) {
              tenantId = jsonObject.getString("tenantId");
            }
          }
          case "tenantRegister" -> {
            log.info("process tenantRegister:{}", body);
            tenantId = jsonObject.getString("tenantId");
          }
          default -> {
            log.error("not support this tag {}", body);
          }
        }
        if (StringUtils.isBlank(tenantId)) {
          log.error("can not find tenantId :{}", body);
        }
        this.saveDBSyncInfo(tenantId);
      } catch (Exception e) {
        log.error("RouterChangeConsumer error {}", msg, e);
        //计算重试
        return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
      }
    }
    return ConsumeOrderlyStatus.SUCCESS;
  }

  public void saveDBSyncInfo(String tenantId) {
    String chUrl = chRouterPolicy.getCHJdbcURL(tenantId);
    log.info("tenantId:{},chUrl:{}", tenantId, chUrl);
    RouterInfo routerInfo = mybatisBITenantPolicy.getRouterInfo(tenantId);
    if (StringUtils.isNotBlank(chUrl) && routerInfo != null) {
      String pgUrl = routerInfo.getJdbcUrl();
      String schemaName = (routerInfo.getStandalone() != null && routerInfo.getStandalone()) ?
        "sch_" + tenantId :
        "public";
      log.info("tenantId:{},pgUrl:{},schema:{}", tenantId, pgUrl, schemaName);
      DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo(chUrl, pgUrl, schemaName);
      if (dbSyncInfo == null) {
        if (!chMetadataService.checkCHDBExists(chUrl)) {
          chMetadataService.createDatabase(chUrl, null, null);
        }
        dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId(ObjectId.get().toString());
        dbSyncInfo.setChDb(chUrl);
        dbSyncInfo.setPgDb(pgUrl);
        dbSyncInfo.setPgSchema(schemaName);
        dbSyncInfo.setBatchNum(0L);
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
        dbSyncInfo.setCreateTime(new Date().getTime());
        dbSyncInfo.setIsDeleted(0);
        dbSyncInfo.setLastModifiedTime(new Date().getTime());
        pgCommonDao.batchUpsertDbSyncInfo(Lists.newArrayList(dbSyncInfo));
      }
    }
  }

  @PreDestroy
  public void destroy() {
    if (null != consumer) {
      consumer.shutdown();
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (null == event.getApplicationContext().getParent()) {
//      consumer.start();
    }
  }
}
