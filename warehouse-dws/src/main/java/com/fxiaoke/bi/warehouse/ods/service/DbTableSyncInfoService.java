package com.fxiaoke.bi.warehouse.ods.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.db.dao.DbTableSyncInfoDao;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.core.db.BiAggSyncInfoDao;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class DbTableSyncInfoService {

  @Resource
  private PgCommonDao pgCommonDao;
  @Resource
  private DbTableSyncInfoDao dbTableSyncInfoDao;
  @Resource
  private BiAggSyncInfoDao biAggSyncInfoDao;

  /**
   * 返回所有需要同步的db_table_sync_info
   * @param dbSyncInfo
   * @return
   */

  public Map<String, DbTableSyncInfoDO> queryDbTableSyncInfoMap(DBSyncInfo dbSyncInfo) {
    String pgDbName = CommonUtils.getDBName(dbSyncInfo.getPgDb());
    if (GrayManager.isAllowByRule("query_from_tenant_db", String.format("%s^%s",pgDbName,dbSyncInfo.getPgSchema()))) {
      return this.queryDbTableSyncInfoMap(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getId());
    }
    return pgCommonDao.queryDbTableSyncInfoMap(dbSyncInfo.getId());
  }

  public Map<String, DbTableSyncInfoDO> queryDbTableSyncInfoMap(String pgDbURL, String pgSchema, String dbSyncId) {
    return dbTableSyncInfoDao.queryDbTableSyncInfoMap(pgDbURL, pgSchema, dbSyncId);
  }

  public List<DbTableSyncInfoDO> queryDbTableSyncInfos(DBSyncInfoBO dbSyncInfoCopy, String tableName) {
    String pgDbName = CommonUtils.getDBName(dbSyncInfoCopy.getPgDb());
    if (GrayManager.isAllowByRule("query_from_tenant_db",String.format("%s^%s",pgDbName,dbSyncInfoCopy.getPgSchema()))) {
      return dbTableSyncInfoDao.queryDbTableSyncInfos(dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getPgSchema(), dbSyncInfoCopy.getId(), Lists.newArrayList(tableName));
    }
    return pgCommonDao.queryDbTableSyncInfos(dbSyncInfoCopy.getId(), tableName);
  }

  public List<DbTableSyncInfoDO> queryDbTableSyncInfosBySyncId(String pgDbURL,
                                                               String pgSchema,
                                                               String dbSyncId,
                                                               String tableName) {
    String pgDbName = CommonUtils.getDBName(pgDbURL);
    if (GrayManager.isAllowByRule("query_from_tenant_db", String.format("%s^%s",pgDbName,pgSchema))) {
      return dbTableSyncInfoDao.queryDbTableSyncInfos(pgDbURL, pgSchema, dbSyncId, Lists.newArrayList(tableName));
    }
    return pgCommonDao.queryDbTableSyncInfos(dbSyncId, tableName);
  }

  public int batchUpsertDbTableSyncInfo(DBSyncInfoBO dbSyncInfoCopy,List<DbTableSyncInfoDO> dbTableSyncInfos) {
    String pgDbName = CommonUtils.getDBName(dbSyncInfoCopy.getPgDb());
    int result = dbTableSyncInfoDao.batchUpsertDbTableSyncInfo(dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getPgSchema(), dbTableSyncInfos);
    if (!GrayManager.isAllowByRule("skip_update_sys_db", pgDbName)) {
      return pgCommonDao.batchUpsertDbTableSyncInfo(dbTableSyncInfos);
    }
    return result;
  }

  /**
   * 批量更新db table syncInfo 数据
   * @param dbTableSyncInfos
   * @return
   */
  public int batchUpdateDbTableSyncInfo(DBSyncInfoBO dbSyncInfoCopy, List<DbTableSyncInfoDO> dbTableSyncInfos) {
    String pgDbName = CommonUtils.getDBName(dbSyncInfoCopy.getPgDb());
    int result = dbTableSyncInfoDao.batchUpsertDbTableSyncInfo(dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getPgSchema(), dbTableSyncInfos);
    if (!GrayManager.isAllowByRule("skip_update_sys_db", pgDbName)) {
      return pgCommonDao.batchUpdateDbTableSyncInfo(dbTableSyncInfos);
    }
    return result;
  }

  /**
   *
   * @param dbSyncIds
   * @param tableNames
   * @return
   */
  public int deleteTableSyncInfoByDbSyncId(List<String> dbSyncIds, List<String> tableNames) {
    AtomicInteger result = new AtomicInteger(0);
    AtomicInteger sysResult = new AtomicInteger(0);
    List<DBSyncInfo> dbSyncInfos = pgCommonDao.queryDBSyncInfoById(dbSyncIds);
    if (CollectionUtils.isNotEmpty(dbSyncInfos)) {
      dbSyncInfos.forEach(dbSyncInfo -> {
        String pgDbName = CommonUtils.getDBName(dbSyncInfo.getPgDb());
        int size = dbTableSyncInfoDao.batchDeleteBySyncId(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getId(), tableNames);
        log.info("batchDeleteBySyncId pgDB:{},pgSchema:{},chDB:{},dbSyncId:{},tableNames:{},delSize:{}", dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getChDb(), dbSyncInfo.getId(), JSON.toJSON(tableNames), size);
        result.addAndGet(size);
        if (!GrayManager.isAllowByRule("skip_update_sys_db", pgDbName)) {
          int sysSize = pgCommonDao.deleteTableSyncInfoByDbSyncId(Lists.newArrayList(dbSyncInfo.getId()), tableNames);
          log.info("deleteTableSyncInfoByDbSyncId dbSyncId:{},tableNames:{},sysSize:{}", dbSyncInfo.getId(), JSON.toJSON(tableNames), sysSize);
          sysResult.addAndGet(sysSize);
        }
      });
    }
    return result.get();
  }

  /**
   * 批量删除dbSyncInfo
   * @param dbSyncIds
   */
  public void batchDeletedDbSyncInfo(List<String> dbSyncIds) {
    List<DBSyncInfo> dbSyncInfos = pgCommonDao.queryDBSyncInfoById(dbSyncIds);
    if (CollectionUtils.isNotEmpty(dbSyncInfos)) {
      dbSyncInfos.forEach(dbSyncInfo -> {
        int delSize = dbTableSyncInfoDao.batchDeleteBySyncId(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getId(), null);
        log.info("batchDeleteBySyncId pgDB:{},pgSchema:{},chDB:{},dbSyncId:{},delSize:{}", dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getChDb(), dbSyncInfo.getId(), delSize);
        String pgDbName = CommonUtils.getDBName(dbSyncInfo.getPgDb());
        if (!GrayManager.isAllowByRule("skip_update_sys_db", pgDbName)) {
          int sysSize = pgCommonDao.deleteTableSyncInfoByDbSyncId(Lists.newArrayList(dbSyncInfo.getId()), null);
          log.info("deleteTableSyncInfoByDbSyncId dbSyncId:{},sysSize:{}", dbSyncInfo.getId(), sysSize);
        }
        int delDbSize = pgCommonDao.deleteDbSyncInfoById(Lists.newArrayList(dbSyncInfo.getId()));
        log.info("deleteTableSyncInfoByDbSyncId dbSyncId:{},delDbSize:{}", dbSyncInfo.getId(), delDbSize);
      });
    }
  }

  /**
   * 合并下游表信息
   * @param upTenantId 上游企业id
   */
  public void mergeDownstreamTableSyncInfo(String upTenantId, String dbSyncInfoId) {
    List<DBSyncInfo> dbSyncInfos = pgCommonDao.queryDBSyncInfoById(Lists.newArrayList(dbSyncInfoId));
    if (CollectionUtils.isEmpty(dbSyncInfos)) {
      log.error("can not find dbSyncInfo by id :{}", dbSyncInfoId);
      return;
    }
    BIAggSyncInfoDO biAggSyncInfoDO = biAggSyncInfoDao.queryAggSyncByEi(upTenantId);
    if (biAggSyncInfoDO != null) {
      DBSyncInfo dbSyncInfo = dbSyncInfos.getFirst();
      String pgDbName = CommonUtils.getDBName(dbSyncInfo.getPgDb());
      List<DbTableSyncInfoDO> dbTableSyncInfoDOS;
      if (GrayManager.isAllowByRule("query_from_tenant_db", String.format("%s^%s", pgDbName, dbSyncInfo.getPgSchema()))) {
        dbTableSyncInfoDOS = dbTableSyncInfoDao.queryDbTableSyncInfos(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), biAggSyncInfoDO.getId(), null);
      } else {
        dbTableSyncInfoDOS = pgCommonDao.queryDbTableSyncInfos(biAggSyncInfoDO.getId(), null);
      }
      if (CollectionUtils.isNotEmpty(dbTableSyncInfoDOS)) {
        List<DbTableSyncInfoDO> newDbTableSyncInfoDOS = dbTableSyncInfoDOS.stream()
                                                                          .peek(dbTableSyncInfoDO -> dbTableSyncInfoDO.setDbSyncId(dbSyncInfo.getId()))
                                                                          .toList();
        dbTableSyncInfoDao.batchUpdateDbTableSyncInfo(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), newDbTableSyncInfoDOS);
        if (!GrayManager.isAllowByRule("skip_update_sys_db", pgDbName)) {
          pgCommonDao.batchUpdateDbTableSyncInfo(newDbTableSyncInfoDOS);
        }
      }
    }
  }

}
