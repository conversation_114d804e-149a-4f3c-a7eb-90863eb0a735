package com.fxiaoke.bi.warehouse.core.db;

import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.BiAggSyncInfoMapper;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author:jief
 * @Date:2024/4/5
 */
@Service
public class BiAggSyncInfoDao {

  @Resource
  private BiAggSyncInfoMapper biAggSyncInfoMapper;

  /**
   * 反查agg sync 信息
   * @param tenantId
   * @return
   */
  public BIAggSyncInfoDO queryAggSyncByEi(String tenantId) {
    return biAggSyncInfoMapper.setTenantId("-1").queryAggSyncInfoByEi(tenantId);
  }

  /**
   * upsert agg同步信息
   * @param biAggSyncInfoDO
   * @return
   */
  public int saveAggSyncInfo(List<BIAggSyncInfoDO> biAggSyncInfoDO) {
    return biAggSyncInfoMapper.setTenantId("-1")
                              .upsertSyncDBInfo(biAggSyncInfoDO, Sets.newHashSet("id", "tenant_id"
                              ));
  }

  /**
   * 查询所有AggSyncInfo得数据
   * @return
   */
  public List<BIAggSyncInfoDO> queryAllAggSyncInfo() {
    return biAggSyncInfoMapper.setTenantId("-1").queryAllAggSyncInfo();
  }

  public void updateBiAggSyncInfo(BIAggSyncInfoDO biAggSyncInfoDO) {
    biAggSyncInfoMapper.setTenantId("-1").updateBiAggSyncInfo(biAggSyncInfoDO);
  }
}
