package com.fxiaoke.bi.warehouse.core.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Author:jief
 * @Date:2024/4/5
 */
@Data
@Table(name = "bi_agg_sync_info")
public class BIAggSyncInfoDO {
  @Id
  @Column(name = "id")
  private String id;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "downstream_eis")
  private String downstreamEis;
  @Column(name = "status")
  private Integer status;
  @Column(name = "batch_num")
  private Long batchNum;
  @Column(name = "last_sync_time")
  private long lastSyncTime;
  @Column(name = "is_deleted")
  private int isDeleted;
  @Column(name = "version")
  private int version;
  @Column(name = "create_time")
  private long createTime;
  @Column(name = "last_modified_time")
  private long lastModifiedTime;
}
