package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * @Author:jief
 * @Date:2023/9/9
 */
@Repository
public interface BIMtTopologyStatusMapper extends ITenant<BIMtTopologyStatusMapper> {

  @Update("update bi_mt_topology_status set status=#{status} where tenant_id=#{tenantId} and source_id=#{sourceId}")
  int updateStatusBySourceId(@Param("tenantId") String tenantId,
                             @Param("sourceId") String sourceId,
                             @Param("status") Integer status);

  @Insert("INSERT INTO bi_mt_topology_status (id,tenant_id,source_id, create_time, last_modified_time,status,visit_count,last_visit_time) VALUES " +
    "(#{id},#{tenant_id}, #{source_id}, #{create_time}, #{last_modified_time}, #{status},0,#{create_time})\n" +
    "ON CONFLICT(tenant_id,source_id) DO UPDATE SET status=EXCLUDED.status, last_modified_time=EXCLUDED" +
    ".last_modified_time")
  int upsertStatusBySourceId(@Param("id") String id,
                             @Param("tenant_id") String tenantId,
                             @Param("source_id") String sourceId,
                             @Param("create_time") long createTime,
                             @Param("last_modified_time") long lastModifiedTime,
                             @Param("status") Integer status);
}
