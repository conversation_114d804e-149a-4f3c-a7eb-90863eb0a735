package com.fxiaoke.bi.warehouse.dws.utils;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2023/12/20
 */
public class StatViewContext {
  private static final ThreadLocal<StatViewContext> statViewContextThreadLocal = new ThreadLocal<>();
  @JsonIgnore
  @JSONField(
    serialize = false
  )
  private String currentThreadName;
  /**
   * 字段udfObjFieldDO对象
   */
  private Map<String, UdfObjFieldDO> udfObjFieldDOMap=new HashMap<>();
  public static boolean exist() {
    return statViewContextThreadLocal.get() != null;
  }

  public static StatViewContext get() {
    StatViewContext context = statViewContextThreadLocal.get();
    if (context == null) {
      context = new StatViewContext();
      statViewContextThreadLocal.set(context);
    }
    return context;
  }

  public static void remove() {
    statViewContextThreadLocal.remove();
  }

  public static void _set(StatViewContext c) {
    statViewContextThreadLocal.set(c);
  }

}
