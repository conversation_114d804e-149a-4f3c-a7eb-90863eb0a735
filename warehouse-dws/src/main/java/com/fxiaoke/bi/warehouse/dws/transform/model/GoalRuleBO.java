package com.fxiaoke.bi.warehouse.dws.transform.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;

/**
 * @Author:jief
 * @Date:2023/9/2
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoalRuleBO {
  public String tenantId;
  public String id;
  public String name;
  public String description;
  public List<String> countFiscalYear;
  public String startMonth;
  public String startWeek;
  public String startQuarter;
  public int status;
  public String defineType;
  public String objectDescribeApiName;
  public String checkLevelType;
  public int version;
  public Object isDeleted;
  public String themeApiName;
  /**
   * 财年类型，周，月，季，年
   */
  public String checkCyCle;
  private String deptFieldApiName;
  public String timeZone;
  private List<String> checkMeasureFields;
  /**
   * 多维度目标考核维度字段
   */
  private String checkDimensionFields;

  /**
   * 从小到大排序财年
   * @return 财年列表
   */
  public List<Integer> orderByFiscalYear() {
    if (CollectionUtils.isEmpty(countFiscalYear)) {
      return null;
    }
    return countFiscalYear.stream().map(Integer::parseInt).sorted(Comparator.naturalOrder()).toList();
  }
}
