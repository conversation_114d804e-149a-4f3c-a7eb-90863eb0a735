package com.fxiaoke.bi.warehouse.ods.dao;

import com.fxiaoke.bi.warehouse.common.component.BIPgDataSource;
import com.fxiaoke.bi.warehouse.ods.bean.DBColumnType;
import com.fxiaoke.bi.warehouse.ods.dao.mapper.CommonMapper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

@Slf4j
@Service
public class MetaDataDao {
  @Autowired
  private CommonMapper commonMapper;
  @Resource
  private BIPgDataSource biPgDataSource;

  private static final String queryAllPgTableSQL = "select ic.table_name from information_schema.columns ic " +
    " where ic.column_name='sys_modified_time' and ic.table_schema=? and ic.table_name not like '%\\_\\_del\\_%' group by ic.table_name";

  private static final String queryUdefPgTableSQL = "select ic.table_name from information_schema.columns ic where ic.column_name='sys_modified_time' and ic.table_schema=? and ic.table_name not like '%\\_\\_del\\_%' and ic.table_name like '%__c' group by ic.table_name";

  private static final String QUERY_ALL_PG_TABLE_SQL = "SELECT table_name FROM information_schema.tables WHERE table_schema = ? AND table_type = 'BASE TABLE' AND table_name NOT LIKE '%\\_\\_del\\_%'";
  private static final Pattern customTableRegx = Pattern.compile("^object_[0-9a-zA-Z]+__c$");

  public List<DBColumnType> findAllPgTypeByTable(String tenantId, String tableName, String schema) {
    return commonMapper.setTenantId(tenantId).findAllPgTypeByTable(tableName, schema);
  }

  /**
   * 全量表 排除黑名单表。
   */
  public List<String> findAllPgTableExcludeStatTable(String dbURL, String schemaName) {
    List<String> tableNameSet = Lists.newArrayList();
    try (JdbcConnection jdbcConnection = this.biPgDataSource.getConnectionByPgbouncerURL(dbURL,schemaName)) {
      jdbcConnection.prepareQuery(QUERY_ALL_PG_TABLE_SQL, preparedStatement -> preparedStatement.setString(1, schemaName), resultSet -> {
        while (resultSet.next()) {
          String tableName = resultSet.getString(1);
          tableNameSet.add(tableName);
        }
      });
    } catch (SQLException e) {
      log.error("find schemas from this db error jdbcURL:{},schemaName:{}", dbURL, schemaName, e);
    }
    return tableNameSet;
  }

  /**
   * 返回所有包含 sys_modified_time 表
   * @param dbURL
   * @param schemaName
   * @return
   */
  public List<String> findAllPgTableWithSmt(String dbURL, String schemaName) {
    List<String> tableNameSet = Lists.newArrayList();
    try (JdbcConnection jdbcConnection = this.biPgDataSource.getConnectionByPgbouncerURL(dbURL,schemaName)) {
      jdbcConnection.prepareQuery(queryAllPgTableSQL, preparedStatement -> {
        preparedStatement.setString(1, schemaName);
      }, resultSet -> {
        while (resultSet.next()) {
          String tableName = resultSet.getString(1);
          if (Objects.equals(schemaName, "public") && customTableRegx.matcher(tableName).matches()) {
            log.warn("findAllPgTableWithSmt custom obj dbURL:{},schemaName:{},tableName:{}", dbURL, schemaName, tableName);
            continue;
          }
          tableNameSet.add(tableName);
        }
      });
    } catch (Exception e) {
      log.error("find schemas from this db error jdbcURL:{},schemaName:{}", dbURL, schemaName, e);
      throw new RuntimeException(e);
    }
    return tableNameSet;
  }

}
