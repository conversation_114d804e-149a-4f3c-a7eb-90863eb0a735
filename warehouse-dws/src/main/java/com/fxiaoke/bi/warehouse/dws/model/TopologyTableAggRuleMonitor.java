package com.fxiaoke.bi.warehouse.dws.model;

import com.fxiaoke.bi.warehouse.common.util.TemplateUtil;
import com.fxiaoke.helper.CollectionHelper;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 表数据变化引起统计图的聚合列变化的关系
 *
 * <AUTHOR>
 * @since 2023/3/9
 */
@Data
@AllArgsConstructor
public class TopologyTableAggRuleMonitor {
  /**
   * 规则Id
   */
  private final String fieldId;
  /**
   * 跟节点表,用于下游指标判断,如果是agg_downstream_data则是下游指标
   */
  private final String rootTableName;
  /**
   * 哪些表的变化会引起增量变化
   */
  private final Set<String> monitorObjectDescribeApiNameSet;
  /**
   * 增加,或者全量的sql模版
   */
  private final List<String> computeSQL;
  /**
   * 受影响的表的列表
   */
  private final Set<String> effectTables;

  /**
   * 最右侧的大表的limit offset信息，分批执行用
   */
  private final Map<String, String> mostRightLargeTableLimits;

  /**
   * 指标列名称；同一个指标可能在一个图中重复出现，value_slot不同
   */
  private final String valueSlot;

  /**
   * 指标计算时，insert语句涉及的column列表
   */
  private final List<String> insertFields;

  /**
   * 生成增量或者全量的sql
   *
   * @param batchNum
   * @return
   */
  public List<String> computeSQL(long batchNum) {
    Map<String, Object> params = ImmutableMap.of(SQLPlaceHolder.batch_num, batchNum);
   return computeSQL.stream().map(sql->TemplateUtil.replace(sql, params)).toList();
  }

  /**
   * 生成增量或者全量的sql
   * @param params
   * @return
   */
  public List<String> computeSQL(@Nonnull Map<String, Object> params ) {
    return computeSQL.stream().map(sql->TemplateUtil.replace(sql, params)).toList();
  }
  /**
   * 判断DB的更新事件是否引起统计图的增量计算列
   *
   * @param changedObjectDescribeApiNameSet 变化的对象
   * @return 是否需要计算
   */
  public boolean needCompute(Set<String> changedObjectDescribeApiNameSet) {
    boolean result = false;
    /**
     *保证大小写一致，因为schema隔离的自定义table name都是小写，但是apiName不全是小写。
     *非schema隔离的租户没有问题
     */
    return CollectionHelper.hasIntersection(monitorObjectDescribeApiNameSet, changedObjectDescribeApiNameSet);
  }
}
