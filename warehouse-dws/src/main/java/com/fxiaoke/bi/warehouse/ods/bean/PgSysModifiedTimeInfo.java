package com.fxiaoke.bi.warehouse.ods.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 保存每个表的变更时间，以及变更时间为空的数量
 * 变更时间的最大值最小值
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PgSysModifiedTimeInfo {
  private String tenantId;
//  private Long nullCount;
//  private Long minModifiedTime;
  private long maxModifiedTime;
}
