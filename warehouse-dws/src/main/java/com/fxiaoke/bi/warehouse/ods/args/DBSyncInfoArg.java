package com.fxiaoke.bi.warehouse.ods.args;

import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

import java.util.Date;
import java.util.Objects;

/**
 * @Author:jief
 * @Date:2023/11/9
 */
@Data
public class DBSyncInfoArg {
  private String id;
  private String chDb;
  private String pgDb;//ch db名称
  private String pgSchema;//pg库名称
  private Integer status;//table名称
  private Long batchNum;//批次号
  private Long lastSyncTime;    //上次同步开始的时间戳
  private Long createTime;//上次拉取批次号
  private Long lastModifiedTime;//拉取的最大的修改时间
  private Integer isDeleted;

  private Long lastMergeAggTime;
  /**
   * 最近一次同步的租户id列表逗号分隔
   */
  private String lastSyncEis;
  private int allowIncPartition;

  public DBSyncInfo toDBSyncInfo() {
    DBSyncInfo dbSyncInfo = new DBSyncInfo();
    if (StringUtils.isBlank(this.id)) {
      dbSyncInfo.setId(ObjectId.get().toString());
    } else {
      dbSyncInfo.setId(this.id);
    }
    if (StringUtils.isBlank(this.chDb) || StringUtils.isBlank(this.pgDb)) {
      throw new RuntimeException("ch DB or pgDB is empty!");
    } else {
      dbSyncInfo.setChDb(this.chDb);
      dbSyncInfo.setPgDb(this.pgDb);
    }
    if (StringUtils.isBlank(this.pgSchema)) {
      dbSyncInfo.setPgSchema("public");
    } else {
      dbSyncInfo.setPgSchema(this.pgSchema);
    }
    dbSyncInfo.setCreateTime(Objects.requireNonNullElseGet(this.createTime, () -> new Date().getTime()));
    if (this.status == null) {
      dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
    } else {
      dbSyncInfo.setStatus(this.status);
    }
    dbSyncInfo.setIsDeleted(Objects.requireNonNullElse(this.isDeleted, 0));
    dbSyncInfo.setBatchNum(Objects.requireNonNullElse(this.batchNum, 0L));
    dbSyncInfo.setLastSyncTime(this.lastSyncTime);
    dbSyncInfo.setLastMergeAggTime(this.lastMergeAggTime);
    dbSyncInfo.setLastSyncEis(this.lastSyncEis);
    dbSyncInfo.setLastModifiedTime(new Date().getTime());
    dbSyncInfo.setAllowIncPartition(this.allowIncPartition);
    return dbSyncInfo;
  }
}
