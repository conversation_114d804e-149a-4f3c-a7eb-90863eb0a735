package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.db.dao.DbSyncInfoFlowDao;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.entity.DbSyncInfoFlowDO;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBAggInfoDO;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.db.mapper.DBAggInfoMapper;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author:jief
 * @Date:2024/8/6
 */
@Slf4j
@Service
public class DBAggInfoDAO {
  @Autowired
  private DBAggInfoMapper dbAggInfoMapper;
  @Resource
 private DbSyncInfoFlowDao dbSyncInfoFlowDao;

  /**
   * 查询或初始化DBAggInfo 非线程安全
   * @param dbSyncInfoDO
   * @return
   */
  public DBAggInfoDO queryOrInitDBAggInfo(DBSyncInfoDO dbSyncInfoDO) {
    DBAggInfoDO dbAggInfoDO = this.findBySyncId(dbSyncInfoDO.getId());
    if (dbAggInfoDO == null || SyncStatusEnum.createFromStatus(dbAggInfoDO.getStatus()) == SyncStatusEnum.AGG_ED) {
      int oldVersion = dbAggInfoDO == null ? 0 : dbAggInfoDO.getVersion();
      List<DbSyncInfoFlowDO> dbSyncInfoFlowDOS = Lists.newArrayList();
      if (CHContext.shouldMerge(dbSyncInfoDO.getChDB())) {
        DBAggInfoDO dbAggInfoDO1 = this.buildDBAggInfo(dbSyncInfoDO.getId(), dbSyncInfoFlowDOS, SyncStatusEnum.EXCHANGE_AGG.getStatus());
        int result = this.upsertDBAggInfoWithVersion(dbAggInfoDO1, oldVersion);
        if (result > 0) {
          log.info("queryOrInitDBAggInfo dbAggInfo status is EXCHANGE_AGG pgDB:{},chDB:{}", dbSyncInfoDO.getPgDB(), dbSyncInfoDO.getChDB());
          return dbAggInfoDO1;
        }
      } else {
        dbSyncInfoFlowDOS = dbSyncInfoFlowDao.queryDbSyncFlow(dbSyncInfoDO.getPgDB(), dbSyncInfoDO.getPgSchema(), dbSyncInfoDO.getId(), SyncStatusEnum.SYNC_ED.getStatus(), true, WarehouseConfig.querySyncFlowLimit);
        if (CollectionUtils.isNotEmpty(dbSyncInfoFlowDOS)) {
          DBAggInfoDO dbAggInfoDO1 = this.buildDBAggInfo(dbSyncInfoDO.getId(), dbSyncInfoFlowDOS, SyncStatusEnum.SYNC_ED.getStatus());
          if (dbAggInfoDO != null) {
            dbAggInfoDO1.setAllowCalPartition(dbAggInfoDO.getAllowCalPartition());
            dbAggInfoDO1.setCreateTime(dbAggInfoDO.getCreateTime());
          }
          if (GrayManager.isAllowByRule("all_cal_partition", dbSyncInfoDO.getId())) {
            dbAggInfoDO1.setAllowCalPartition(WarehouseConfig.OPEN_CAL_PARTITION);
          }
          int result = this.upsertDBAggInfoWithVersion(dbAggInfoDO1, oldVersion);
          if (result > 0) {
            log.info("queryOrInitDBAggInfo dbAggInfo status is SyncStatusEnum.SYNC_ED  pgDB:{},chDB:{}", dbSyncInfoDO.getPgDB(), dbSyncInfoDO.getChDB());
            return dbAggInfoDO1;
          }
        } else {
          return null;
        }
      }
    }
    return dbAggInfoDO;
  }

  public DBAggInfoDO buildDBAggInfo(String dbSyncId, List<DbSyncInfoFlowDO> dbSyncInfoFlowDO,int status) {
    DBAggInfoDO dbAggInfoDO = new DBAggInfoDO();
    dbAggInfoDO.setId(dbSyncId);
    dbAggInfoDO.setStatus(status);
    dbAggInfoDO.setVersion(0);
    dbAggInfoDO.setCreateTime(new Date().getTime());
    dbAggInfoDO.setLastModifiedTime(new Date().getTime());
    Map<String, Map<String, Set<String>>> apiNameEIMap = Maps.newHashMap();
    Map<String,BIPair<Long,Long>> incSysModifiedTimeRangeMap=Maps.newHashMap();
    List<String> syncFlowIds = Lists.newArrayList();
    List<String> syncFlowBatchNums = Lists.newArrayList();
    dbSyncInfoFlowDO.forEach(df -> {
      syncFlowIds.add(df.getId());
      Long[] batchNums = df.getBatchNums();
      if (batchNums != null) {
        syncFlowBatchNums.addAll(Arrays.stream(df.getBatchNums()).map(String::valueOf).toList());
      }
      if (StringUtils.isNotBlank(df.getApiNameEiMap()) && !Objects.equals("{}", df.getApiNameEiMap())) {
        Map<String, Map<String, Set<String>>> tableApiEiMap = JSON.parseObject(df.getApiNameEiMap(),
          new TypeReference<>() {
        });
        if (tableApiEiMap != null) {
          tableApiEiMap.forEach((k, v) -> apiNameEIMap.compute(k, (key, value) -> {
            if (value == null) {
              return v;
            } else {
              v.forEach((k1, v1) -> {
                value.computeIfAbsent(k1, k2 -> new HashSet<>()).addAll(v1);
              });
              return value;
            }
          }));
        }
      }
      if (StringUtils.isNotBlank(df.getIncSysModifiedTimeRange()) && !Objects.equals("{}", df.getIncSysModifiedTimeRange())) {
        Map<String, BIPair<Long,Long>> dfIncSysModifiedTimeRangeMap = JSON.parseObject(df.getIncSysModifiedTimeRange(),new TypeReference<>() {});
        // log.info("JSON.parseObject dfIncSysModifiedTimeRangeMap:{}", JSON.toJSONString(dfIncSysModifiedTimeRangeMap));
        dfIncSysModifiedTimeRangeMap.forEach((tableName,range)->{
          incSysModifiedTimeRangeMap.compute(tableName, (k, v) -> {
            long incSysModifiedTimeFrom = range.getFirst();
            long incSysModifiedTimeTo = range.getSecond();
            if (v != null) {
              return BIPair.of(Math.min(v.first, incSysModifiedTimeFrom), Math.max(v.second, incSysModifiedTimeTo));
            } else {
              return BIPair.of(incSysModifiedTimeFrom, incSysModifiedTimeTo);
            }
          });
        });
      }
    });
    dbAggInfoDO.setSyncBatchNums(String.join(",", syncFlowBatchNums));
    dbAggInfoDO.setSyncFlows(String.join(",", syncFlowIds));
    dbAggInfoDO.setApiNameEiMap(JSON.toJSONString(apiNameEIMap));
    dbAggInfoDO.setIncSysModifiedTimeRange(JSON.toJSONString(incSysModifiedTimeRangeMap));
    return dbAggInfoDO;
  }

  public int upsertDBAggInfo(DBAggInfoDO dbAggInfoDO) {
    return dbAggInfoMapper.setTenantId("-1").upsertDBAggInfo(Lists.newArrayList(dbAggInfoDO), Sets.newHashSet("id"));
  }
  /**
   * upsert 版本++
   * @param dbAggInfoDO
   * @return
   */
  public int upsertDBAggInfoWithVersion(DBAggInfoDO dbAggInfoDO,int oldVersion){
    return dbAggInfoMapper.setTenantId("-1").upsertDBAggInfoWithVersion(dbAggInfoDO,oldVersion);
  }
  public Integer updateDBAggInfoStatus(String dbSyncId, int status, int preVersion) {
    return dbAggInfoMapper.setTenantId("-1").updateDBAggInfoStatus(dbSyncId, status, new Date().getTime(), preVersion);
  }

  public DBAggInfoDO findBySyncId(String dbSyncId) {
    return dbAggInfoMapper.setTenantId("-1").findBySyncId(dbSyncId);
  }

}
