package com.fxiaoke.bi.warehouse.ods.entity;

import com.fxiaoke.bi.warehouse.common.db.entity.SyncInfo;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2023/10/31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DBSyncInfoBO extends SyncInfo {
  private String chDb;
  private String pgDb;//ch db名称
  private String pgSchema;//pg库名称
  /**
   * 最后一次merge agg Time 时间
   */
  private Long lastMergeAggTime;
  /**
   * 最近一次同步的租户id列表逗号分隔
   */
  private Set<String> lastSyncEis;
  /**
   *是否开启增量分区同步0:否,1:是
   */
  private Integer allowIncPartition;
  private long lastIntegrateTime;
  /**
   * 是否开启paas2bi同步0:否,1:是
   */
  private int allowPaas2biStatus;

  public static DBSyncInfoBO createInstanceOf(DBSyncInfo dbSyncInfo) {
    DBSyncInfoBO dbSyncInfoBO = new DBSyncInfoBO();
    dbSyncInfoBO.setId(dbSyncInfo.getId());
    dbSyncInfoBO.setChDb(dbSyncInfo.getChDb());
    dbSyncInfoBO.setPgDb(dbSyncInfo.getPgDb());
    dbSyncInfoBO.setPgSchema(dbSyncInfo.getPgSchema());
    dbSyncInfoBO.setStatus(dbSyncInfo.getStatus());
    dbSyncInfoBO.setBatchNum(dbSyncInfo.getBatchNum());
    dbSyncInfoBO.setLastSyncTime(dbSyncInfo.getLastSyncTime());
    dbSyncInfoBO.setCreateTime(dbSyncInfo.getCreateTime());
    dbSyncInfoBO.setLastModifiedTime(dbSyncInfo.getLastModifiedTime());
    dbSyncInfoBO.setIsDeleted(dbSyncInfo.getIsDeleted());
    dbSyncInfoBO.setLastMergeAggTime(dbSyncInfo.getLastMergeAggTime());
    dbSyncInfoBO.setAllowIncPartition(dbSyncInfo.getAllowIncPartition());
    if (StringUtils.isNotBlank(dbSyncInfo.getLastSyncEis())) {
      dbSyncInfoBO.setLastSyncEis(Sets.newHashSet(Splitter.on(",").splitToList(dbSyncInfo.getLastSyncEis())));
    }
    dbSyncInfoBO.setLastIntegrateTime(dbSyncInfo.getLastIntegrateTime());
    dbSyncInfoBO.setAllowPaas2biStatus(dbSyncInfo.getAllowPaas2biStatus());
    return dbSyncInfoBO;
  }

  /**
   * 判断该租户是否是新增租户，新租户需要同步全量数据
   *  排除掉-1
   * @param tenantId 租户id
   * @return boolean
   */
  public boolean isNewTenant(String tenantId){
    if(Objects.equals("-1",tenantId)){
      return false;
    }
    return CollectionUtils.isEmpty(this.lastSyncEis) || !this.lastSyncEis.contains(tenantId);
  }

  public String findTenantIdOrDefault(String defaultValue) {
    if (pgSchema.startsWith("sch_")) {
      return pgSchema.substring(4);
    }
    return defaultValue;
  }

}
