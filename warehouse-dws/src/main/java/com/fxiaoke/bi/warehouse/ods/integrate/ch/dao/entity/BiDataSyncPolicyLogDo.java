package com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "bi_data_sync_policy_log")
public class BiDataSyncPolicyLogDo {

    @Column(name = "id")
    private String id;

    @Column(name = "tenant_id")
    private String tenantId;

    @Column(name = "source_tenant_id")
    private String sourceTenantId;

    @Column(name = "policy_id")
    private String policyId;

    @Column(name = "log_type")
    private String logType;

    @Column(name = "msg")
    private String msg;

    @Column(name = "timestamp")
    private Date timestamp;

    @Column(name = "is_deleted")
    private int isDeleted;
}
