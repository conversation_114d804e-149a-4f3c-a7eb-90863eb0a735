package com.fxiaoke.bi.warehouse.ods.compare;

import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChArg;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChColumnResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChCountResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChDelBizData;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChSampleArg;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChTableResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChTriggerResult;

import java.util.List;
import java.util.Set;

public interface ComparePgToChService {

    ComparePgToChResult getComparePgToChResult(ComparePgToChArg comparePgToChArg);

    ComparePgToChTableResult getComparePgToChTableResult(Set<String> pgTableSet, Set<String> chTableSet, Set<String> commonTableSet);

    ComparePgToChTriggerResult getComparePgToChTriggerResult(ComparePgToChArg comparePgToChArg);

    List<ComparePgToChCountResult> getComparePgToChCountResult(ComparePgToChArg comparePgToChArg, Set<String> commonTableSet);

    List<ComparePgToChColumnResult> getComparePgToChColumnResult(ComparePgToChArg comparePgToChArg, Set<String> commonTableSet);

    List<ComparePgToChCountResult> getComparePgsToChCountSimpleResult(ComparePgToChSampleArg comparePgToChSampleArg);

    ComparePgToChDelBizData getComparePgToChDelBizData(ComparePgToChArg comparePgToChArg, Set<String> commonTableSet,List<String> tenantIdList);
}
