package com.fxiaoke.bi.warehouse.ods.utils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

public class ResourcePathUtil {
  public static String getResourcePath() {
    try {
      String path = Thread.currentThread().getContextClassLoader().getResource("").getPath();

      // URL 解码
      path = URLDecoder.decode(path, StandardCharsets.UTF_8);

      // 处理 Windows 路径
      if (path.startsWith("/") && System.getProperty("os.name").toLowerCase().contains("win")) {
        path = path.substring(1);
      }

      // 统一路径分隔符
      path = path.replace("\\", "/");

      return path;
    } catch (Exception e) {
      throw new RuntimeException("Failed to get resources path", e);
    }
  }

  public static InputStream getResourceAsStream(String fileName) {
    return Thread.currentThread().getContextClassLoader().getResourceAsStream(fileName);
  }

  public static URL getResource(String fileName) {
    return Thread.currentThread().getContextClassLoader().getResource(fileName);
  }

  public static File getResourceFile(String fileName) throws IOException {
    URL url = getResource(fileName);
    if (url != null) {
      return new File(url.getFile());
    }
    throw new FileNotFoundException("Resource not found: " + fileName);
  }
}
