package com.fxiaoke.bi.warehouse.ods.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.db.entity.MergeTaskStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.util.BizAuditLog;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.core.db.AggMergeDao;
import com.fxiaoke.bi.warehouse.core.db.entity.DBMergeInfoDO;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseNodeInfo;
import com.fxiaoke.common.Holder;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.Uninterruptibles;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class MergeTaskService {
  @Resource
  private CHDataSource chDataSource;
  @Resource
  private CHMetadataService chMetadataService;
  @Resource
  private PgCommonDao pgCommonDao;
  @Resource
  private AggMergeDao aggMergeDao;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;

  private Long maxBytesBeforeExternalSort;
  private Long maxBytesBeforeExternalGroupBy;
  private Long groupByTwoLevelThresholdBytes;
  private Integer optimizeAggregationInOrder;
  private Integer parallelSize;
  private Integer mergeTaskToleranceDays;
  private Boolean bAllowExchange;
  private Boolean bAllowMovePartition;
  private Integer mergeMaxInsertThreads;
  private Integer mergeMaxThreads;
  private Boolean bAllowDropPartition;
  private Integer mergeCheckDiffValue;

  private Boolean bAllowQuantityCheck;
  private Boolean bAllowMergeCheck;
  private Boolean bAllowMergeHaving;
  private long checkCountMaxValue;

  private int explainTableCount;

  private long explainTotalRows;

  private long explainSingleTableRows;

  private long explainNotControlRows;

  private long mergeDiff;

  private int dropPartitionBefore;
  private long mergeBeginTime;
  private long mergeEndTime;
  private long mergePerTotalSize;
  private long mergePerTotalSizeGray;
  private long mergeTaskTimeout;
  private int viewVersionFilterThrottle;

  public MergeTaskService() {
  }

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
      maxBytesBeforeExternalSort = config.getLong("max_bytes_before_external_sort", 1073741824);
      maxBytesBeforeExternalGroupBy = config.getLong("max_bytes_before_external_group_by", 1073741824);
      groupByTwoLevelThresholdBytes = config.getLong("group_by_two_level_threshold_bytes", 1073741824);
      optimizeAggregationInOrder = config.getInt("optimize_aggregation_in_order", 1);
      parallelSize = config.getInt("merge_parallel_size", 5);
      mergeTaskToleranceDays = config.getInt("merge_task_error_tolerance_day",2);
      bAllowExchange = config.getBool("allow_merge_exchange",false);
      bAllowMovePartition = config.getBool("allow_merge_move_partition",false);
      bAllowDropPartition = config.getBool("allow_merge_drop_partition",false);
      bAllowQuantityCheck = config.getBool("allow_merge_quantity_count_check", true);
      bAllowMergeCheck = config.getBool("allow_merge_count_check", true);
      mergeCheckDiffValue = config.getInt("merge_check_value_diff", 30);
      checkCountMaxValue = config.getLong("merge_check_count_max_value", 1000000000);
      explainTableCount = config.getInt("explain_table_count_threshold",3);
      explainTotalRows = config.getLong("explain_total_rows_threshold",1000000);
      explainSingleTableRows = config.getLong("explain_single_table_rows_threshold",10000000);
      explainNotControlRows = config.getLong("explain_not_control_rows_threshold",100000);
      mergeDiff = config.getLong("agg_data_merge_diff",100000000);
      dropPartitionBefore = config.getInt("drop_agg_swap_partition_before",15);
      mergeMaxInsertThreads = config.getInt("merge_max_insert_threads",2);
      mergeMaxThreads = config.getInt("merge_max_threads",8);
      mergeBeginTime = LocalTime.parse(config.get("mergeBeginTime", "01:00"), DateTimeFormatter.ofPattern("HH:mm")).getLong(ChronoField.MINUTE_OF_DAY);
      mergeEndTime = LocalTime.parse(config.get("mergeEndTime", "06:00"), DateTimeFormatter.ofPattern("HH:mm")).getLong(ChronoField.MINUTE_OF_DAY);
      bAllowMergeHaving = config.getBool("allow_merge_having", true);
      mergePerTotalSize = config.getInt("agg_data_new_merge_per_total_size", 1000000000);
      mergePerTotalSizeGray = config.getInt("agg_data_new_merge_per_total_size_gray", 300000000);
      mergeTaskTimeout = config.getInt("merge_task_execute_time_out", 36000);
      viewVersionFilterThrottle = config.getInt("merge_view_version_filter_throttle", 3000);
    });
  }

  /***
   * 新merge机制：基于partition原地merge agg_data；每次找到最后一个分区，把其中包含的所有hash_code在agg_data全表范围执行group by之后插入到最新分区；
   * 每次执行merge的时候排除当前分区那些已经被merge的hash_code，断点继续执行；
   * 检测是否被merge的规则：一个hash_code，如果其他分区中有value_slot='all'的相同hash_code，并且timestamp更大，则这个hash_code被merge过了
   * @param clickhouseNodeInfo
   * @param pageSize
   * @param chReadTimeOut
   * @return
   */
  public boolean newMerge(ClickhouseNodeInfo clickhouseNodeInfo, int pageSize, int chReadTimeOut) {
    //因为CH没有事务机制，使用Redis来控制对ch的bi_merge_task的并发写入
    String lockKey = String.format("BI_MERGE_%s", clickhouseNodeInfo.getJdbcUrl());
    try (JedisLock jedisLock = new JedisLock(jedisCmd, lockKey, 1000 * 60 * 20)) {
      if (jedisLock.tryLock()) {
        if (!startNewMergeTask(clickhouseNodeInfo)) {
          return false;
        }
        log.info("new merge begin churl:{}", clickhouseNodeInfo.getJdbcUrl());

        //如果agg_data_history不存在则创建
        createAggHistoryTable(clickhouseNodeInfo);
      }
      else {
        log.info("new merge get redis lock failed,churl:{}", clickhouseNodeInfo.getJdbcUrl());
        return false;
      }
    }
    catch (Exception e) {
      log.error("new merge start task exception,churl:{}", clickhouseNodeInfo.getJdbcUrl(), e);
      return false;
    }
    long start = System.currentTimeMillis();
    long beforeCount = getAggDistinctCount(clickhouseNodeInfo);
    log.info("new merge begin churl:{}, agg distinct count:{}", clickhouseNodeInfo.getJdbcUrl(), beforeCount);
    String partition = this.newMergeAggDataTable(clickhouseNodeInfo, pageSize, chReadTimeOut);
    long afterCount = getAggDistinctCount(clickhouseNodeInfo);
    long moveCount = -1;
    if (bAllowMovePartition && bAllowMergeCheck && !StringUtils.isEmpty(partition) && beforeCount == afterCount && afterCount >= 0) {
      log.info("new merge begin move partition chURL:{},{}", partition, clickhouseNodeInfo.getJdbcUrl());
      boolean bOk = this.movePartitionAggData(clickhouseNodeInfo, partition);
      moveCount = getAggDistinctCount(clickhouseNodeInfo);
      log.info("new merge finish move partition {}, {}, chURL:{}", bOk, partition, clickhouseNodeInfo.getJdbcUrl());
    }
    if (bAllowDropPartition) {
      log.info("new merge begin drop partition chURL:{}", clickhouseNodeInfo.getJdbcUrl());
      boolean bOk = this.dropPartitionAggDataSwap(clickhouseNodeInfo);
      log.info("new merge finish drop partition {}, chURL:{}", bOk, clickhouseNodeInfo.getJdbcUrl());
    }
    //设置全量merge任务完成状态
    if(!finishNewMergeTask(clickhouseNodeInfo)){
      log.info("new merge finish failed, {}, {}", clickhouseNodeInfo.getDatabase(), partition);
      return false;
    }
    log.info("new merge finished chURL:{}, {}, cost:{} ms; agg distinct count: before {}, after {}, move {}",
            clickhouseNodeInfo.getJdbcUrl(), partition, (System.currentTimeMillis() - start), beforeCount, afterCount, moveCount);

    return true;
  }

  private List<String> getTenantsByAggData(ClickhouseNodeInfo clickhouseNodeInfo){
    String querySql = """
          select distinct tenant_id from %s.agg_data
          order by tenant_id;
            """;
    String qSql = String.format(querySql, clickhouseNodeInfo.getDatabase());
    List<String> eis = new ArrayList<>();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(qSql, rs -> {
        while (rs.next()) {
          eis.add(rs.getString(1));
        }
      });
    } catch (Exception e) {
      log.error("getTenantsByAggData exception {}", e);
    }
    return eis;
  }

  private Map<String, Integer> getViewsByTenant(ClickhouseNodeInfo clickhouseNodeInfo, String tenant_id){
    String querySql = """
          select view_id, max(view_version) as version from %s.agg_data where tenant_id = '%s'
          group by view_id
          order by view_id;
            """;
    String qSql = String.format(querySql, clickhouseNodeInfo.getDatabase(), tenant_id);
    Map<String, Integer> viewMaps = new HashMap<>();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(qSql, rs -> {
        while (rs.next()) {
          viewMaps.put(rs.getString(1), rs.getInt(2));
        }
      });
    } catch (Exception e) {
      log.error("getViewsByTenant exception {}", e);
    }
    return viewMaps;
  }

  private void explainSql(ClickhouseNodeInfo clickhouseNodeInfo, String tenant_id, String viewId,  String vSql){
    String querySql = """
            select groupArray(table) as tables, length(tables) as table_counts, sum(rows) as total_rows, groupArray(rows) as rows_array from (
              explain estimate
              %s
              );
            """;
    String qSql = String.format(querySql, vSql);
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(qSql, rs -> {
        if (rs.next()) {
          int table_counts = rs.getInt(2);
          long total_rows = rs.getLong(3);
          String tables = rs.getString(1);
          String row_list = rs.getString(4);
          boolean bDirectQuery = false;
          //判断是否直接查对象表，不用预计算；满足任一种情况就直查：1、total_rows少于10w；2、单表并且total_rows少于1000w；3、表个数小于等于3并且total_rows少于100w
          if(total_rows < explainNotControlRows || (table_counts <= 1 && total_rows <= explainSingleTableRows) || (table_counts <= explainTableCount && total_rows <= explainTotalRows) ){
            bDirectQuery = true;
          }

          BizAuditLog auditLog = new BizAuditLog();
          auditLog.setModule("STAT_CHART_DATA_EXPLAIN");
          auditLog.setStartTime(System.currentTimeMillis());
          auditLog.setFrom(clickhouseNodeInfo.getDatabase());
          auditLog.setFromId(viewId);
          auditLog.setTenantId(tenant_id);
          auditLog.setMessage(String.format("bDirectQuery:%s,table_counts:%d, total_rows:%d, tables:%s, rows:%s", bDirectQuery, table_counts, total_rows, tables, row_list));
          auditLog.setExtra(vSql);
          auditLog.log();
          log.info("explainSql finish, ei {}, viewId {}, bDirectQuery:{}, table_counts:{}, total_rows:{}, tables:{}, rows:{}", tenant_id, viewId, bDirectQuery, table_counts, total_rows, tables, row_list);
        }
      });
    } catch (Exception e) {
      log.error("explainSql exception {}, ei {}, viewId: {}, {}, {}", clickhouseNodeInfo.getJdbcUrl(), tenant_id, viewId, e, qSql);
    }
  }

  public void explainViewsCost(ClickhouseNodeInfo clickhouseNodeInfo){
    try{
      ExecutorService fixedThreadPools = Executors.newFixedThreadPool(1);
      fixedThreadPools.submit( () -> {
        try{
          List<String> eis = getTenantsByAggData(clickhouseNodeInfo);
          for(String ei : eis){
            Map<String, Integer> viewMaps = getViewsByTenant(clickhouseNodeInfo, ei);
            viewMaps.forEach((viewId, version) ->{
              String viewSql = pgCommonDao.getViewSqlByViewId(ei, viewId, version);
              if(StringUtils.isNotEmpty(viewSql)) {
                //todo 版本问题导致sql可能有语法错误
                explainSql(clickhouseNodeInfo, ei, viewId, viewSql);
              }
              log.info("explainViewsCost finish {}, ei:{}, viewId: {}, version: {}, {}", clickhouseNodeInfo.getJdbcUrl(), ei, viewId, version, viewSql==null?"":viewSql);
            });
          }
        }
        catch (Exception ex){
          log.error("explainViewsCost thread exception {}", clickhouseNodeInfo.getJdbcUrl(), ex);
        }
        return 0;
      });
      fixedThreadPools.shutdown();
    }
    catch (Exception ex){
      log.error("explainViewsCost exception {}, {}", clickhouseNodeInfo.getJdbcUrl(), ex);
    }
  }

  private void releaseMergeLock(ClickhouseNodeInfo clickhouseNodeInfo, boolean bIncrement){
    String lastTimeAndStatus = pgCommonDao.getLastTimeAndStatus(clickhouseNodeInfo.getJdbcUrl());
    if(StringUtils.isNotEmpty(lastTimeAndStatus)){
      List<String> list = Splitter.on("|").splitToList(lastTimeAndStatus);
      //如果锁的时间超过了一个小时，发生了pod异常重启等问题，释放锁
      long lastTime = Long.parseLong(list.get(0));
      int lastStatus = Integer.parseInt(list.get(1));
      if( list.size()>=2 && (lastTime == MergeTaskStatusEnum.MERGING.getStatus()) && (new Date().getTime() - lastStatus  > 3600000) ){
        Integer mStatus = pgCommonDao.upsertAndGetCHMergeTaskStatus(clickhouseNodeInfo.getJdbcUrl(), MergeTaskStatusEnum.MERGED.getStatus(), MergeTaskStatusEnum.MERGING.getStatus(), new Date().getTime());
        log.info("merge bIncrement {}, lock time is too long ,release it, {}, {}, {}, {}", bIncrement, clickhouseNodeInfo.getDatabase(), list.get(0), list.get(1), mStatus==null?-1:mStatus);
      }
    }
  }


  public boolean merge(ClickhouseNodeInfo clickhouseNodeInfo, int pageSize, int chReadTimeOut, boolean needSwitch, boolean bIncrement) {
    //因为CH没有事务机制，使用Redis来控制对ch的bi_merge_task的并发写入
    String lockKey = String.format("BI_MERGE_%s", clickhouseNodeInfo.getJdbcUrl());
    if (!bIncrement) {
      ///全量merge任务
      try (JedisLock jedisLock = new JedisLock(jedisCmd, lockKey, 1000 * 60 * 20)) {
        if (jedisLock.tryLock()) {
          if (!canStartMergeAllTask(clickhouseNodeInfo)) {
            return false;
          }
          //设置全量merge任务启动状态
          if (!startMergeTaskAll(clickhouseNodeInfo)) {
            log.info("merge all start failed, {}", clickhouseNodeInfo.getDatabase());
            return false;
          }
          log.info("merge all begin churl:{}", clickhouseNodeInfo.getJdbcUrl());
          //全量merge历史数据的时候需要建表
          boolean bNewPartitionKey = false;
          if (GrayManager.isAllowByRule("merge_add_sys_time_to_agg_data_db", clickhouseNodeInfo.getDatabase())) {
            bNewPartitionKey = true;
          }
          createOrReplaceAggSwapTable(clickhouseNodeInfo, bNewPartitionKey);
        }
        else{
          log.info("merge all cannot get redis lock,churl:{}", clickhouseNodeInfo.getJdbcUrl());
          return false;
        }
      }
      catch (Exception e) {
        log.error("merge all start task exception,churl:{}", clickhouseNodeInfo.getJdbcUrl(), e);
        return false;
      }
      //全量merge不需要等待执行结果；增量merge需要等待执行结果;
      ExecutorService fixedThreadPools = Executors.newFixedThreadPool(1);
      fixedThreadPools.submit( () -> {
        long start = System.currentTimeMillis();
        try{
          long count = mergeAggDataTable(clickhouseNodeInfo, pageSize, chReadTimeOut, false);
          //设置全量merge任务完成状态
          if(count > 0) {
            if (!finishMergeTaskAll(clickhouseNodeInfo)) {
              log.info("merge all finish failed, {}", clickhouseNodeInfo.getDatabase());
              return false;
            }
          }
          log.info("merge all finished chURL:{},cost:{} ms", clickhouseNodeInfo.getJdbcUrl(), (System.currentTimeMillis() - start));
          return count;
        }
        catch (Exception ex){
          log.error("merge all error chURL:{}", clickhouseNodeInfo.getJdbcUrl(), ex);
        }
        return 0;
      });
      fixedThreadPools.shutdown();
    } else {
      try (JedisLock jedisLock = new JedisLock(jedisCmd, lockKey, 1000 * 60 * 20)) {
        if (jedisLock.tryLock()) {
          if (!canStartMergeIncrementTask(clickhouseNodeInfo)) {
            return false;
          }
          //设置增量merge任务启动状态
          if (!startMergeIncrementTask(clickhouseNodeInfo)) {
            log.info("merge increment start failed, {}", clickhouseNodeInfo.getDatabase());
            return false;
          }
          if (!chMetadataService.checkCHTableExists(clickhouseNodeInfo.getJdbcUrl(), "agg_data_swap")) {
            log.info("merge increment agg_data_swap not exists, {}", clickhouseNodeInfo.getDatabase());
            return false;
          }
        }
        else {
          log.info("merge increment cannot get redis lock,churl:{}", clickhouseNodeInfo.getJdbcUrl());
          return false;
        }
      }
      catch (Exception e) {
        log.error("merge increment start task exception,churl:{}", clickhouseNodeInfo.getJdbcUrl(), e);
        return false;
      }

      //用explain方式评估各个view的cost
      /*if(GrayManager.isAllowByRule("explain_view_cost_db", CHContext.getDBName(clickhouseNodeInfo.getJdbcUrl()))){
        explainViewsCost(clickhouseNodeInfo);
      }*/

      log.info("merge increment begin churl:{}", clickhouseNodeInfo.getJdbcUrl());
      long start = System.currentTimeMillis();
      //开始增量merge任务
      long count = mergeAggDataTable(clickhouseNodeInfo, pageSize, chReadTimeOut, true);
      checkMergeResult(clickhouseNodeInfo);
      if (count != -1) {
        if (needSwitch && bAllowExchange) {
          log.info("merge increment begin exchange table name chURL:{}", clickhouseNodeInfo.getJdbcUrl());
          boolean bOk = this.swapAggDataAggDataSwap(clickhouseNodeInfo);
          //exchange之后，不立即删除agg_data_swap表，如果merge有问题，可以手动紧急恢复
          //this.dropAggSwapTable(clickhouseNodeInfo);
          log.info("merge increment finish exchange table name {}, chURL:{}", bOk, clickhouseNodeInfo.getJdbcUrl());

        }
        //设置增量任务完成状态
        if (!finishMergeTaskIncrement(clickhouseNodeInfo)) {
          log.info("merge increment finish failed, {}", clickhouseNodeInfo.getDatabase());
          return false;
        }
        log.info("merge increment finished chURL:{},cost:{} ms", clickhouseNodeInfo.getJdbcUrl(), (System.currentTimeMillis() - start));
      }
    }
    return true;
  }

  public void dropAndCreateAggSwapTable(ClickhouseNodeInfo clickhouseNodeInfo, boolean bNewPartitionKey){
    String sql = queryCreateAggDataTableSQL(clickhouseNodeInfo);
    sql = sql.replace(
            "CREATE TABLE " + clickhouseNodeInfo.getDatabase() + ".agg_data",
            "CREATE TABLE " + clickhouseNodeInfo.getDatabase() + ".agg_data_swap on cluster '" +
                    clickhouseNodeInfo.getCluster() + "'");
    if (!sql.contains("agg_data_swap")) {
      throw new RuntimeException("create table agg_data_swap failed.");
    }
    //添加sys_modified_time和对应的partitionKey
    if(bNewPartitionKey && !sql.contains("sys_modified_time")){
      sql = sql.replace("PARTITION BY cityHash64(tenant_id, view_id, view_version) % 10", "PARTITION BY toYYYYMMDD(sys_modified_time)");
      if(sql.contains("`timestamp` DateTime64(9) DEFAULT now64(9)")){
        sql = sql.replace("`timestamp` DateTime64(9) DEFAULT now64(9),", "`timestamp` DateTime64(9) DEFAULT now64(9), `sys_modified_time` DateTime64(9) DEFAULT now64(9),");
      }
      else {
        sql = sql.replace("`timestamp` DateTime DEFAULT now(),", "`timestamp` DateTime64(9) DEFAULT now64(9), `sys_modified_time` DateTime64(9) DEFAULT now64(9),");
      }
    }

    String dropSql = String.format("DROP TABLE IF EXISTS %s.agg_data_swap on cluster '%s'", clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getCluster());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
      connection.executeUpdate(dropSql);
      connection.executeUpdate(sql);
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }
  public void createOrReplaceAggSwapTable(ClickhouseNodeInfo clickhouseNodeInfo, boolean bNewPartitionKey) {
    //CH有个安全机制，超过max_table_size_to_drop大小（默认50G）的表不能执行drop和truncate，需要特别处理；replace也受此限制
    String sql = queryCreateAggDataTableSQL(clickhouseNodeInfo);
    sql = sql.replace(
      "CREATE TABLE " + clickhouseNodeInfo.getDatabase() + ".agg_data",
      "CREATE OR REPLACE TABLE " + clickhouseNodeInfo.getDatabase() + ".agg_data_swap on cluster '" +
        clickhouseNodeInfo.getCluster() + "'");
    if (!sql.contains("agg_data_swap")) {
      throw new RuntimeException("create table agg_data_swap failed.");
    }
    //添加sys_modified_time和对应的partitionKey
    if(bNewPartitionKey && !sql.contains("sys_modified_time")){
      sql = sql.replace("PARTITION BY cityHash64(tenant_id, view_id, view_version) % 10", "PARTITION BY toYYYYMMDD(sys_modified_time)");
      if(sql.contains("`timestamp` DateTime64(9) DEFAULT now64(9)")){
        sql = sql.replace("`timestamp` DateTime64(9) DEFAULT now64(9),", "`timestamp` DateTime64(9) DEFAULT now64(9), `sys_modified_time` DateTime64(9) DEFAULT now64(9),");
      }
      else {
        sql = sql.replace("`timestamp` DateTime DEFAULT now(),", "`timestamp` DateTime64(9) DEFAULT now64(9), `sys_modified_time` DateTime64(9) DEFAULT now64(9),");
      }
    }

    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
      connection.executeUpdate(sql);
    } catch (SQLException e) {
      //有的linux内核没有支持RENAME_EXCHANGE，不能使用exchange，需要重新执行一次drop and create
      if(e.getMessage().contains("RENAME EXCHANGE is not supported")){
        log.warn("createOrReplaceAggSwapTable exception {}", clickhouseNodeInfo.getDatabase(),e);
        dropAndCreateAggSwapTable(clickhouseNodeInfo, bNewPartitionKey);
      }
      else {
        throw new RuntimeException(e);
      }
    }
  }

  public void createAggHistoryTable(ClickhouseNodeInfo clickhouseNodeInfo) {
    String sql = queryCreateAggDataTableSQL(clickhouseNodeInfo);
    String replaceSql = "CREATE TABLE IF NOT EXISTS ";
    sql = sql.replace(
            "CREATE TABLE " + clickhouseNodeInfo.getDatabase() + ".agg_data",
            replaceSql + clickhouseNodeInfo.getDatabase() + ".agg_data_history on cluster '" +
                    clickhouseNodeInfo.getCluster() + "'");
    if (!sql.contains("agg_data_history")) {
      throw new RuntimeException("create table agg_data_history failed.");
    }

    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
      connection.executeUpdate(sql);
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  public boolean hasAggCalculatingInProgress(ClickhouseNodeInfo clickhouseNodeInfo){
    String pSql = """
            select query_id
            from system.processes
            where current_database = '%s'
              and query_kind = 'Insert'
              and query ilike '%%agg_data%%'
              and query not ilike '%%agg_data_swap%%'
            limit 1;
            """;
    String procSql = String.format(pSql,  clickhouseNodeInfo.getDatabase());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      AtomicBoolean hasProc = new AtomicBoolean(false);

      //判断是否有AGG进程正在执行，为了防止AGG执行间隙查不到，执行10次
      for(int i=0; i<10; i++) {
        connection.query(procSql, rs -> {
          if (rs.next()) {
            hasProc.set(true);
          }
        });
        if(hasProc.get())
          break;
        Uninterruptibles.sleepUninterruptibly(1, TimeUnit.SECONDS);
      }
      log.info("hasAggCalculatingInProgress status: {}, {} ",hasProc.get(), clickhouseNodeInfo.getDatabase());
      return hasProc.get();
    } catch (SQLException e) {
      log.error("hasAggCalculatingInProgress exception:{}, {}", e, clickhouseNodeInfo.getDatabase());
    }
    return false;
  }

  public boolean hasNewMergeTaskInProgress(ClickhouseNodeInfo clickhouseNodeInfo){
    String pSql = """
            select query_id
            from system.processes
            where current_database = '%s'
              and query_kind = 'Insert'
              and query ilike '%%agg_data%%'
              and query ilike '%%_partition_id%%'
            limit 1;
            """;
    String procSql = String.format(pSql,  clickhouseNodeInfo.getDatabase());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      AtomicBoolean hasProc = new AtomicBoolean(false);

      //判断是否有merge进程正在执行，为了防止merge task执行间隙查不到，执行10次
      for(int i=0; i<10; i++) {
        connection.query(procSql, rs -> {
          if (rs.next()) {
            hasProc.set(true);
          }
        });
        if(hasProc.get())
          break;
        Uninterruptibles.sleepUninterruptibly(2, TimeUnit.SECONDS);
      }

      log.info("hasNewMergeTaskInProgress status: {}, {}",hasProc.get(), clickhouseNodeInfo.getDatabase());
      return hasProc.get();
    } catch (SQLException e) {
      log.error("hasNewMergeTaskInProgress exception:{}, {}", e, clickhouseNodeInfo.getDatabase());
    }
    return true;
  }

  public boolean hasMergeTaskInProgress(ClickhouseNodeInfo clickhouseNodeInfo, boolean bCheckLog){
    String pSql = """
            select query_id
            from system.processes
            where current_database = '%s'
              and query_kind = 'Insert'
              and query ilike '%%agg_data_swap%%'
            limit 1;
            """;
    String lSql = """
            SELECT query_id
            FROM system.query_log
            WHERE query ilike '%%agg_data_swap%%' and query_kind = 'Insert' AND type in ('QueryFinish', 'ExceptionWhileProcessing') and event_time > (now() - toIntervalHour(2))
            and current_database = '%s'
            ORDER BY event_time desc
            limit 1;
            """;
    String procSql = String.format(pSql,  clickhouseNodeInfo.getDatabase());
    String qlSql = String.format(lSql, clickhouseNodeInfo.getDatabase());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      AtomicBoolean hasProc = new AtomicBoolean(false);
      AtomicBoolean hasLog = new AtomicBoolean(false);

      //判断是否有merge进程正在执行，为了防止merge task执行间隙查不到，执行10次
      for(int i=0; i<10; i++) {
        connection.query(procSql, rs -> {
          if (rs.next()) {
            hasProc.set(true);
          }
        });
        if(hasProc.get())
          break;
        Uninterruptibles.sleepUninterruptibly(2, TimeUnit.SECONDS);
      }
      //判断2小时内query_log是否有merge记录
      connection.query(qlSql, rs -> {
        if (rs.next()) {
          hasLog.set(true);
        }
      });

      boolean hasTask = hasProc.get();
      if(bCheckLog){
        hasTask = hasProc.get() || hasLog.get();
      }
      log.info("hasMergeTaskInProgress status: {}, {}, bCheckLog:{}",hasTask, clickhouseNodeInfo.getDatabase(), bCheckLog);
      return hasTask;
    } catch (SQLException e) {
      log.error("hasMergeTaskInProgress exception:{}, {}, bCheckLog:{}", e, clickhouseNodeInfo.getDatabase(), bCheckLog);
    }
    return true;
  }

  public boolean startNewMergeTask(ClickhouseNodeInfo clickhouseNodeInfo){
    //如果agg_data还是旧的结构，不执行new merge任务
    if(!tablePropertyCheck(clickhouseNodeInfo, "agg_data", "sys_modified_time", "toYYYYMMDD(sys_modified_time)"))
      return false;
    if(hasNewMergeTaskInProgress(clickhouseNodeInfo))
      return false;
    if(GrayManager.isAllowByRule("merge_count_compare_check", clickhouseNodeInfo.getDatabase()) && !isNeedMerge(clickhouseNodeInfo)){
      return false;
    }
    if(!partitionCountCheck(clickhouseNodeInfo)){
      return false;
    }
    /*
    bi_merge_task记录进行中的全量merge任务
    max_merged_timestamp表示全量merge的最大timestamp，从这个时间之后的进行增量merge
    is_deleted：0正常，1删除
    status记录任务状态：0全量已完成，1全量进行中，2增量进行中（没有增量已完成，当增量完成时任务删除，置is_deleted=1）
    新merge也使用bi_merge_task来控制并发：table_name='agg_data_new',status只有0和1两种状态
     */
    String sqlTemplate = """
          CREATE TABLE IF NOT EXISTS %s.bi_merge_task on cluster '{cluster}'(
              `table_name` String,
              `status` UInt8,
              `max_merged_timestamp` DateTime DEFAULT now(),
              `timestamp` DateTime DEFAULT now(),
              `is_deleted` UInt8
          )
          engine = ReplicatedReplacingMergeTree(timestamp)
          ORDER BY (table_name);
          """;
    String tableSql = String.format(sqlTemplate, clickhouseNodeInfo.getDatabase());
    String checkSql = String.format("select status, dateDiff('second', timestamp, now()) as diff from %s.bi_merge_task final where table_name = 'agg_data_new' and is_deleted = 0 order by timestamp desc limit 1",
            clickhouseNodeInfo.getDatabase());

    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      //如果table不存在，则创建
      connection.executeUpdate(tableSql);

      AtomicBoolean canStart = new AtomicBoolean(false);
      //可以进行merge的条件：CH中无merge process的前提下，【status是已完成】，或者【status是进行中，并且任务是10小时前开始（task执行中异常中断了）】
      connection.query(checkSql, rs -> {
        if (rs.next()) {
          int status = rs.getInt(1);
          int diff = rs.getInt(2);
          if(status == 0 || (status == 1 && diff > mergeTaskTimeout)){
            log.info("startNewMergeTask merge task exists, status:{}, time diff:{},{}", status, diff, clickhouseNodeInfo.getDatabase());
            canStart.set(true);
          }
        }
        else{
          canStart.set(true);
        }
      });
      log.info("startNewMergeTask check: {}, {}",canStart.get(), clickhouseNodeInfo.getDatabase());

      if(canStart.get()){
        String uSqlTemplate = """
          insert into %s.bi_merge_task(table_name, status, is_deleted) 
          values('agg_data_new',1, 0);
          """;
        String uSql = String.format(uSqlTemplate, clickhouseNodeInfo.getDatabase());
        return connection.executeUpdate(uSql) > 0;
      }
    } catch (SQLException e) {
      log.error("startNewMergeTask exception:{}", clickhouseNodeInfo.getDatabase(), e);
    }
    return false;
  }

  public boolean canStartMergeAllTask(ClickhouseNodeInfo clickhouseNodeInfo){
    //如果CH有进行中merge process，不能开始新的任务
    boolean bCheckLog = true;
    if(GrayManager.isAllowByRule("merge_all_not_check_agg_log", clickhouseNodeInfo.getDatabase())){
      bCheckLog = false;
    }
    if(hasMergeTaskInProgress(clickhouseNodeInfo, bCheckLog))
      return false;
    if(!GrayManager.isAllowByRule("disable_merge_count_compare_check", clickhouseNodeInfo.getDatabase()) && !isNeedMerge(clickhouseNodeInfo)){
      return false;
    }
    /*
    bi_merge_task记录进行中的全量merge任务
    max_merged_timestamp表示全量merge的最大timestamp，从这个时间之后的进行增量merge
    is_deleted：0正常，1删除
    status记录任务状态：0全量已完成，1全量进行中，2增量进行中（没有增量已完成，当增量完成时任务删除，置is_deleted=1）
     */
    String sqlTemplate = """
          CREATE TABLE IF NOT EXISTS %s.bi_merge_task on cluster '{cluster}'(
              `table_name` String,
              `status` UInt8,
              `max_merged_timestamp` DateTime DEFAULT now(),
              `timestamp` DateTime DEFAULT now(),
              `is_deleted` UInt8
          )
          engine = ReplicatedReplacingMergeTree(timestamp)
          ORDER BY (table_name);
          """;
    String tableSql = String.format(sqlTemplate, clickhouseNodeInfo.getDatabase());
    String checkSql = String.format("select status, dateDiff('second', timestamp, now()) as diff from %s.bi_merge_task final where table_name = 'agg_data' and timestamp >= now() - INTERVAL %d DAY and is_deleted = 0 order by timestamp desc limit 1",
            clickhouseNodeInfo.getDatabase(), mergeTaskToleranceDays);

    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      //如果table不存在，则创建
      connection.executeUpdate(tableSql);

      AtomicBoolean canStart = new AtomicBoolean(false);
      //可以进行全量merge的条件：CH中无merge process的前提下，【timestamp在2天内无任务(2天是兼容异常task)】或者【status是全量进行中，并且任务是2小时前开始（task执行中异常中断了）】
      connection.query(checkSql, rs -> {
        if (rs.next()) {
          int status = rs.getInt(1);
          int diff = rs.getInt(2);
          if(status == 1 && diff > 7200){
            log.info("canStartMergeAllTask merge all task exists, status:{}, time diff:{},{}", status, diff, clickhouseNodeInfo.getDatabase());
            canStart.set(true);
          }
        }
        else{
          canStart.set(true);
        }
      });
      log.info("canStartMergeAllTask check: {}, {}",canStart.get(), clickhouseNodeInfo.getDatabase());
      return canStart.get();
    } catch (SQLException e) {
      log.error("canStartMergeAllTask exception:{}", clickhouseNodeInfo.getDatabase(), e);
    }
    return false;
  }

  public boolean isMergeAllTaskRunning(ClickhouseNodeInfo clickhouseNodeInfo){
    //CH是否有进行中merge process
    if(!hasMergeTaskInProgress(clickhouseNodeInfo, false))
      return false;

    /**
     String sqlTemplate = """
     CREATE TABLE IF NOT EXISTS %s.bi_merge_task on cluster '{cluster}'(
     `table_name` String,
     `status` UInt8,
     `max_merged_timestamp` DateTime DEFAULT now(),
     `timestamp` DateTime DEFAULT now(),
     `is_deleted` UInt8
     )
     engine = ReplicatedReplacingMergeTree(timestamp)
     ORDER BY (table_name);
     """;
     String tableSql = String.format(sqlTemplate, clickhouseNodeInfo.getDatabase());
     */
    String checkSql = String.format("select status, dateDiff('second', timestamp, now()) as diff from %s.bi_merge_task final where table_name = 'agg_data' and is_deleted = 0 order by timestamp desc limit 1",
            clickhouseNodeInfo.getDatabase());
    AtomicBoolean isRunning = new AtomicBoolean(false);
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      //如果task表不存在，补建一下
      //connection.executeUpdate(tableSql);

      connection.query(checkSql, rs -> {
        if (rs.next()) {
          int status = rs.getInt(1);
          if (status == 1) {
            isRunning.set(true);
          }
        }
      });
      log.info("isMergeAllTaskRunning check:{}, {}", isRunning, clickhouseNodeInfo.getDatabase());
      return isRunning.get();
    } catch (SQLException e) {
      log.error("isMergeAllTaskRunning exception", e);
    }
    return false;
  }

  public boolean canStartMergeIncrementTask(ClickhouseNodeInfo clickhouseNodeInfo){
    //如果CH有进行中merge process，不能开始新的任务
    if(hasMergeTaskInProgress(clickhouseNodeInfo, false))
      return false;

    String sqlTemplate = """
          CREATE TABLE IF NOT EXISTS %s.bi_merge_task on cluster '{cluster}'(
              `table_name` String,
              `status` UInt8,
              `max_merged_timestamp` DateTime DEFAULT now(),
              `timestamp` DateTime DEFAULT now(),
              `is_deleted` UInt8
          )
          engine = ReplicatedReplacingMergeTree(timestamp)
          ORDER BY (table_name);
          """;
    String tableSql = String.format(sqlTemplate, clickhouseNodeInfo.getDatabase());

    String checkSql = String.format("select status, dateDiff('second', timestamp, now()) as diff from %s.bi_merge_task final where table_name = 'agg_data' and is_deleted = 0 order by timestamp desc limit 1",
            clickhouseNodeInfo.getDatabase());
    AtomicBoolean canStart = new AtomicBoolean(false);
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      //如果task表不存在，补建一下
      connection.executeUpdate(tableSql);

      //可以进行增量merge的条件：CH中无merge process的前提下，【bi_merge_task中的task的status=0(场景：全量merge执行完毕)】或者【status是增量进行中，并且任务是2小时前开始（task执行中异常中断了）】
      connection.query(checkSql, rs -> {
        if (rs.next()) {
          int status = rs.getInt(1);
          int diff = rs.getInt(2);
          if (status == 2 && diff > 7200) {
            //增量merge任务存在：一个redis lock周期内任务没有执行完，或者任务异常中断
            log.warn("canStartMergeIncrementTask increment task exists, status:{}, time diff: {}, {}", status, diff, clickhouseNodeInfo.getDatabase());
            canStart.set(true);
          }
          else if (status == 0) {
            canStart.set(true);
          }
        }
      });
      log.info("canStartMergeIncrementTask check:{}, {}", canStart, clickhouseNodeInfo.getDatabase());
      return canStart.get();
    } catch (SQLException e) {
      log.error("canStartMergeIncrementTask exception", e);
    }
    return false;
  }

  public long getMergeTaskBeginTime(ClickhouseNodeInfo clickhouseNodeInfo){
    String querySql = """
          select toUnixTimestamp(min(timestamp))*1000 as min_timestamp from %s.bi_merge_task final 
          where table_name='agg_data' and status in (2) and is_deleted = 0;
            """;
    if(GrayManager.isAllowByRule("merge_agg_in_situation_db", clickhouseNodeInfo.getDatabase())){
      querySql = """
          select toUnixTimestamp(min(max_merged_timestamp))*1000 as min_timestamp from %s.bi_merge_task final 
          where table_name='agg_data_new' and status in (1) and is_deleted = 0;
            """;
    }
    AtomicReference<Long> timeStamp = new AtomicReference<>(0L);
    String qSql = String.format(querySql, clickhouseNodeInfo.getDatabase());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(qSql, rs -> {
        if (rs.next()) {
          timeStamp.set(rs.getLong(1));
        }
      });
    } catch (SQLException e) {
      log.error("getMergeTaskBeginTime exception", e);
    }
    return timeStamp.get();
  }

  public boolean startMergeIncrementTask(ClickhouseNodeInfo clickhouseNodeInfo){
    String uSqlTemplate = """
          insert into %s.bi_merge_task(table_name, status, max_merged_timestamp, is_deleted) 
          select table_name, 2, max_merged_timestamp, 0 from %s.bi_merge_task final 
          where table_name='agg_data' and status in (0, 2) and is_deleted = 0
          order by timestamp desc 
          limit 1;
          
          """;
    String querySql = """
          select toString(max_merged_timestamp) from %s.bi_merge_task final 
          where table_name='agg_data' and status in (0, 2) and is_deleted = 0
          order by timestamp desc 
          limit 1;
            """;
    boolean canStart = false;
    AtomicReference<String> maxMergedTime = new AtomicReference<>("");
    String uSql = String.format(uSqlTemplate, clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getDatabase());
    String qSql = String.format(querySql, clickhouseNodeInfo.getDatabase());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(qSql, rs -> {
        if (rs.next()) {
          maxMergedTime.set(rs.getString(1));
        }
      });
      if(connection.executeUpdate(uSql) > 0){
        canStart = true;
      }
    } catch (SQLException e) {
      log.error("startMergeTaskAll exception", e);
    }
    BizAuditLog auditLog = new BizAuditLog();
    auditLog.setModule("STAT_CHART_DATA_MERGE");
    auditLog.setStartTime(System.currentTimeMillis());
    auditLog.setFrom(clickhouseNodeInfo.getDatabase());
    auditLog.setFromId("start_merge_increment_task");
    auditLog.setMessage(String.format("start merge increment task, mergeTimeStamp: %s", maxMergedTime.get()));
    auditLog.setExtra(maxMergedTime.get());
    auditLog.log();

    return canStart;
  }

  public boolean startMergeTaskAll(ClickhouseNodeInfo clickhouseNodeInfo){
    String uSqlTemplate = """
          insert into %s.bi_merge_task(table_name, status, is_deleted) 
          values('agg_data',1, 0);
          """;
    String uSql = String.format(uSqlTemplate, clickhouseNodeInfo.getDatabase());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      if(connection.executeUpdate(uSql) > 0)
        return true;
      else
        return false;
    } catch (SQLException e) {
      log.error("startMergeTaskAll exception", e);
      return false;
    }
  }

  public boolean finishNewMergeTask(ClickhouseNodeInfo clickhouseNodeInfo){
    //以增代改，status置为0，max_merged_timestamp保持不变，记录的是merge task的开始时间
    String uSqlTemplate = """
          insert into %s.bi_merge_task(table_name, status, max_merged_timestamp, is_deleted) 
          select table_name, 0, max_merged_timestamp, 0 from %s.bi_merge_task final 
          where table_name='agg_data_new' and status = 1 and is_deleted = 0
          order by timestamp desc 
          limit 1;
          """;
    boolean bOk = false;
    AtomicReference<String> maxMergedTime = new AtomicReference<>("");
    String uSql = String.format(uSqlTemplate, clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getDatabase());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      if(connection.executeUpdate(uSql) > 0){
        bOk = true;
      }
    } catch (SQLException e) {
      log.error("finishNewMergeTask exception", e);
    }
    BizAuditLog auditLog = new BizAuditLog();
    auditLog.setModule("STAT_CHART_DATA_MERGE");
    auditLog.setStartTime(System.currentTimeMillis());
    auditLog.setFrom(clickhouseNodeInfo.getDatabase());
    auditLog.setFromId("finish_new_merge_task");
    auditLog.setMessage(String.format("finish newMerge task"));
    auditLog.setExtra(maxMergedTime.get());
    auditLog.log();

    return bOk;
  }
  public boolean finishMergeTaskAll(ClickhouseNodeInfo clickhouseNodeInfo){
    //以增代改，status置为0，max_merged_timestamp保持不变（增量merge的依赖max_merged_timestamp的值）
    String uSqlTemplate = """
          insert into %s.bi_merge_task(table_name, status, max_merged_timestamp, is_deleted) 
          select table_name, 0, max_merged_timestamp, 0 from %s.bi_merge_task final 
          where table_name='agg_data' and status = 1 and is_deleted = 0
          order by timestamp desc 
          limit 1;
          """;
    String querySql = """
          select toString(max_merged_timestamp) from %s.bi_merge_task final 
          where table_name='agg_data' and status = 1 and is_deleted = 0
          order by timestamp desc 
          limit 1;
            """;
    boolean canStart = false;
    AtomicReference<String> maxMergedTime = new AtomicReference<>("");
    String uSql = String.format(uSqlTemplate, clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getDatabase());
    String qSql = String.format(querySql, clickhouseNodeInfo.getDatabase());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(qSql, rs -> {
        if (rs.next()) {
          maxMergedTime.set(rs.getString(1));
        }
      });
      if(connection.executeUpdate(uSql) > 0){
        canStart = true;
      }
    } catch (SQLException e) {
      log.error("finishMergeTaskAll exception", e);
    }
    BizAuditLog auditLog = new BizAuditLog();
    auditLog.setModule("STAT_CHART_DATA_MERGE");
    auditLog.setStartTime(System.currentTimeMillis());
    auditLog.setFrom(clickhouseNodeInfo.getDatabase());
    auditLog.setFromId("finish_merge_all_task");
    auditLog.setMessage(String.format("finish merge all task, mergeTimeStamp: %s", maxMergedTime.get()));
    auditLog.setExtra(maxMergedTime.get());
    auditLog.log();

    return canStart;
  }

  public boolean finishMergeTaskIncrement(ClickhouseNodeInfo clickhouseNodeInfo){
    String uSqlTemplate = """
          insert into %s.bi_merge_task(table_name, status, max_merged_timestamp, is_deleted) 
          select table_name, 0, max_merged_timestamp, 1 from %s.bi_merge_task final 
          where table_name='agg_data' and status = 2 and is_deleted = 0
          order by timestamp desc 
          limit 1;
          """;
    String uSql = String.format(uSqlTemplate, clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getDatabase());
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      if(connection.executeUpdate(uSql)>0)
        return true;
      else
        return false;
    } catch (SQLException e) {
      log.error("finishMergeTaskIncrement exception", e);
      return false;
    }
  }

  public boolean checkPartitionQuantileCount(ClickhouseNodeInfo clickhouseNodeInfo, List<String> withSqlList, String totalWithSql, String whereSql){
    if(CollectionUtils.isEmpty(withSqlList))
      return false;

    String settingStrTemplate = """
              SETTINGS max_bytes_before_external_sort = %d,
                      max_bytes_before_external_group_by = %d,
                      group_by_two_level_threshold_bytes = %d,
                      optimize_aggregation_in_order = 1,
                      max_threads = %d
            """;
    String settingStr = String.format(settingStrTemplate, maxBytesBeforeExternalSort,
            maxBytesBeforeExternalGroupBy, groupByTwoLevelThresholdBytes, mergeMaxThreads);
    String totalCountSql = String.format("%s select count() as cnt from agg_data where 1=1 %s %s", totalWithSql, whereSql, settingStr);
    String checkSql = String.format("select (%s)", totalCountSql);
    List<String> countSQLs = Lists.newArrayList();
    for (String withSql : withSqlList) {
      String countSql = String.format("(%s select count() as cnt from agg_data where 1=1 %s)",withSql, whereSql);
      countSQLs.add(countSql);
      checkSql = String.format("%s - %s", checkSql, countSql);
    }
    checkSql = String.format("%s as diff", checkSql);

    AtomicLong totalCount = new AtomicLong(-1);
    AtomicLong sumCount = new AtomicLong(0);
    log.info("checkPartitionQuantileCount:{}, {}",checkSql, clickhouseNodeInfo.getJdbcUrl());
    //出错的时候重试5次
    for (int retryCount = 0; retryCount < 5; retryCount++) {
      try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 600000L)) {
        connection.query(totalCountSql, rs -> {
          if (rs.next()) {
            totalCount.set(rs.getLong(1));
          }
        });
        log.info("checkPartitionQuantileCount totalCountSql: {}, {}, {}", totalCountSql, totalCount.get(), clickhouseNodeInfo.getJdbcUrl());

        for(String countSql : countSQLs) {
          connection.query(countSql, rs -> {
            if (rs.next()) {
              sumCount.addAndGet(rs.getLong(1));
            }
          });
          log.info("checkPartitionQuantileCount countSql: {}, accumulation count: {}, {}", countSql, sumCount.get(), clickhouseNodeInfo.getJdbcUrl());
        }
        break;
      } catch (SQLException e) {
        log.error("checkPartitionQuantileCount exception:{}", clickhouseNodeInfo.getJdbcUrl(), e);
        BizAuditLog auditLog = new BizAuditLog();
        auditLog.setModule("STAT_CHART_DATA_MERGE");
        auditLog.setStartTime(System.currentTimeMillis());
        auditLog.setFrom(clickhouseNodeInfo.getDatabase());
        auditLog.setFromId("merge_task_partition_quantile_check");
        auditLog.setNum(0);
        auditLog.setExtra(checkSql);
        auditLog.setMessage(String.format("checkPartitionQuantileCount exception:%s,%s", clickhouseNodeInfo.getJdbcUrl(), e));
        auditLog.log();
        if (retryCount < 4) {
          Uninterruptibles.sleepUninterruptibly(120, TimeUnit.SECONDS);
        } else {
          return false;
        }
      }
    }
    log.info("checkPartitionQuantileCount diff: {}, {}", totalCount.get() - sumCount.get(), clickhouseNodeInfo.getJdbcUrl());
    BizAuditLog auditLog = new BizAuditLog();
    auditLog.setModule("STAT_CHART_DATA_MERGE");
    auditLog.setStartTime(System.currentTimeMillis());
    auditLog.setFrom(clickhouseNodeInfo.getDatabase());
    auditLog.setFromId("merge_task_partition_quantile_check");
    auditLog.setNum(0);
    auditLog.setExtra(checkSql);
    auditLog.setMessage(String.format("merge_task_partition_quantile_check, diff: %d", totalCount.get() - sumCount.get()));
    auditLog.log();
    //如果agg_data的分区的totalCount超过10亿，那么判断标准是：差异在千分之一以内，否则判断标准是：差异是0
    if(totalCount.get() >= checkCountMaxValue)
      return  totalCount.get() > (Math.abs(totalCount.get()-sumCount.get()) * 1000);
    else
      return (totalCount.get() == sumCount.get());
  }

  /**
   * //判断各个子句count和总count是否一致
   * @param clickhouseNodeInfo
   * @param sqlList
   * @param tsSql
   * @return
   */
  public boolean checkQuantileCount(ClickhouseNodeInfo clickhouseNodeInfo, List<String> sqlList, String tsSql){
    if(CollectionUtils.isEmpty(sqlList))
      return false;

    //卡住一个固定的timestamp，防止新写入数据导致的count细微差异问题
    String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Timestamp(System.currentTimeMillis() - mergeCheckDiffValue * 1000));
    String totalCountSql = String.format("select count() as cnt from agg_data where timestamp <='%s' and %s", timestamp, tsSql);
    String checkSql = String.format("select (%s)", totalCountSql);
    List<String> countSQLs = Lists.newArrayList();
    for (String whereSql : sqlList) {
      String countSql = String.format("(select count() as cnt from agg_data where timestamp <='%s' %s)", timestamp, whereSql);
      countSQLs.add(countSql);
      checkSql = String.format("%s - %s", checkSql, countSql);
    }
    checkSql = String.format("%s as diff", checkSql);

    AtomicLong totalCount = new AtomicLong(-1);
    AtomicLong sumCount = new AtomicLong(0);
    log.info("checkQuantileCount:{}, {}",checkSql, clickhouseNodeInfo.getJdbcUrl());
    //出错的时候重试5次
    for (int retryCount = 0; retryCount < 5; retryCount++) {
      totalCount.set(-1);
      sumCount.set(0);
      try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 600000L)) {
        connection.query(totalCountSql, rs -> {
          if (rs.next()) {
            totalCount.set(rs.getLong(1));
          }
        });
        log.info("totalCountSql: {}, {}, {}", totalCountSql, totalCount.get(), clickhouseNodeInfo.getJdbcUrl());

        for(String countSql : countSQLs) {
          connection.query(countSql, rs -> {
            if (rs.next()) {
              sumCount.addAndGet(rs.getLong(1));
            }
          });
          log.info("countSql: {}, accumulation count: {}, {}", countSql, sumCount.get(), clickhouseNodeInfo.getJdbcUrl());
        }
        break;
      } catch (SQLException e) {
        log.error("checkQuantileCount exception:{}", clickhouseNodeInfo.getJdbcUrl(), e);
        BizAuditLog auditLog = new BizAuditLog();
        auditLog.setModule("STAT_CHART_DATA_MERGE");
        auditLog.setStartTime(System.currentTimeMillis());
        auditLog.setFrom(clickhouseNodeInfo.getDatabase());
        auditLog.setFromId("merge_task_quantile_check");
        auditLog.setNum(0);
        auditLog.setExtra(checkSql);
        auditLog.setMessage(String.format("checkQuantileCount exception:%s,%s", clickhouseNodeInfo.getJdbcUrl(), e));
        auditLog.log();
        if (retryCount < 4) {
          Uninterruptibles.sleepUninterruptibly(120, TimeUnit.SECONDS);
        } else {
          return false;
        }
      }
    }
    log.info("checkQuantileCount diff: {}, {}", totalCount.get() - sumCount.get(), clickhouseNodeInfo.getJdbcUrl());
    BizAuditLog auditLog = new BizAuditLog();
    auditLog.setModule("STAT_CHART_DATA_MERGE");
    auditLog.setStartTime(System.currentTimeMillis());
    auditLog.setFrom(clickhouseNodeInfo.getDatabase());
    auditLog.setFromId("merge_task_quantile_check");
    auditLog.setNum(0);
    auditLog.setExtra(checkSql);
    auditLog.setMessage(String.format("merge_task_quantile_check, diff: %d", totalCount.get() - sumCount.get()));
    auditLog.log();
    //如果agg_data的totalCount超过10亿，那么判断标准是：差异在千分之一以内，否则判断标准是：差异是0
    if(totalCount.get() >= checkCountMaxValue)
      return  totalCount.get() > (Math.abs(totalCount.get()-sumCount.get()) * 1000);
    else
      return (totalCount.get() == sumCount.get());
  }
  /**
   * 把这个方法单独提炼出来，方便单元测试各种场景
   * @param clickhouseNodeInfo
   * @param size
   * @param pageSize
   * @return
   */
  public List<String> createQuantileSqlList(ClickhouseNodeInfo clickhouseNodeInfo, long size, long pageSize, String tsSql, boolean bIncrement){
    List<String> whereList = new ArrayList<>();
    List<String> sqlList = new ArrayList<>();
    List<BigDecimal> quantileList = CHContext.calQuantiles(size, pageSize);
    //获取quantile的时候，不加timestamp过滤，因为增量数据比较少的时候，quantile给出的分位值可能是NaN
    List<String> quantileCodeList = chMetadataService.queryTableQuantile(clickhouseNodeInfo.getJdbcUrl(),
            quantileList, "agg_data", " 1=1 ");
    // 当只有一个元素时循环不执行，在循环后的语句拼接 hash_code > 0
    for (int i = 0; i < quantileCodeList.size() - 1; i++) {
      String from = quantileCodeList.get(i);
      String end = quantileCodeList.get(i + 1);
      String addWhereSql = String.format("AND (hash_code > %s AND hash_code <= %s) AND %s ", from, end, tsSql);
      String sql = this.createMergeSQL(clickhouseNodeInfo, addWhereSql, bIncrement, false, "");
      sqlList.add(sql);
      whereList.add(addWhereSql);
    }
    // 最后一个查最后剩余的数据
    String addWhereSql = "AND (hash_code > %s) AND %s ".formatted(quantileCodeList.get(quantileCodeList.size() - 1), tsSql);
    whereList.add(addWhereSql);
    String sql = this.createMergeSQL(clickhouseNodeInfo, addWhereSql, bIncrement, false, "");
    sqlList.add(sql);
    if(bAllowQuantityCheck && !checkQuantileCount(clickhouseNodeInfo, whereList, tsSql))
      return new ArrayList<>();
    return sqlList;
  }

  public String getMinPartition(ClickhouseNodeInfo clickhouseNodeInfo, String tableName){
    String querySql = String.format("select partition from system.parts where database = '%s' and table = '%s' and active = 1 order by partition limit 1",
            clickhouseNodeInfo.getDatabase(), tableName);
    AtomicReference<String> partition = new AtomicReference<>("");
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(querySql, rs -> {
        if (rs.next()) {
          partition.set(rs.getString(1));
        }
      });
    } catch (SQLException e) {
      log.error("getMinPartition exception", e);
    }
    return partition.get();
  }

  public long getTablePartitionSize(ClickhouseNodeInfo clickhouseNodeInfo, String tableName, String partition){
    String querySql = String.format("select count() from %s.%s where _partition_id='%s' ",
            clickhouseNodeInfo.getDatabase(), tableName, partition);
    AtomicReference<Long> count = new AtomicReference<>(0L);
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(querySql, rs -> {
        if (rs.next()) {
          count.set(rs.getLong(1));
        }
      });
    } catch (SQLException e) {
      log.error("getTablePartitionSize exception", e);
    }
    return count.get();
  }

  private List<String> getAggTenants(ClickhouseNodeInfo clickhouseNodeInfo, String partition){
    String querySql = String.format("select distinct tenant_id from %s.agg_data where _partition_id='%s' order by tenant_id ", clickhouseNodeInfo.getDatabase(), partition);
    List<String> eiList = Lists.newArrayList();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 600000L)) {
      connection.query(querySql, rs -> {
        while (rs.next()) {
          eiList.add(rs.getString(1));
        }
      });
    } catch (SQLException e) {
      log.error("getAggTenants exception", e);
    }
    return eiList;
  }

  private String getAggViewsList(ClickhouseNodeInfo clickhouseNodeInfo, String partition, String tenantId){
    //每次最多处理3000个不同的view_id，防止merge sql超长
    String querySql = String.format("""
                    select distinct tenant_id,
                        view_id,
                        view_version
                    from %s.agg_data
                    where tenant_id = '%s'
                        and _partition_id = '%s'
                        and (tenant_id, view_id) in (
                            select distinct tenant_id,
                                view_id
                            from %s.agg_data
                            where tenant_id = '%s'
                                and _partition_id = '%s'
                            order by tenant_id,
                                view_id
                            limit %d
                        )
                    order by tenant_id,
                        view_id,
                        view_version
                    """,
            clickhouseNodeInfo.getDatabase(), tenantId, partition, clickhouseNodeInfo.getDatabase(), tenantId, partition, viewVersionFilterThrottle);
    List<String> viewList = Lists.newArrayList();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 600000L)) {
      connection.query(querySql, rs -> {
        while (rs.next()) {
          viewList.add(String.format("('%s', '%s', %s)",rs.getString(1),rs.getString(2),rs.getString(3)));
        }
      });
    } catch (SQLException e) {
      log.error("getAggViewsList exception", e);
    }
    String viewsStr = String.format("values%s", Joiner.on(",").join(viewList));
    log.info("getAggViewsList {},{}",clickhouseNodeInfo.getDatabase(), viewsStr);
    return viewsStr;
  }
  public boolean newMergeResultCheck(ClickhouseNodeInfo clickhouseNodeInfo, String partition){
    String beforeTemplate = """
           select count() from (
             select tenant_id,
                 view_id,
                 view_version,
                 hash_code
             from %s.agg_data
             where _partition_id = '%s'
              group by tenant_id,
                  view_id,
                  view_version,
                  hash_code
            )
            """;
    String afterTemplate = """
            select count() from (
            select tenant_id,
                view_id,
                view_version,
                hash_code
            from %s.agg_data
            where _partition_id > '%s'
              and (
                  tenant_id,
                  view_id,
                  view_version,
                  hash_code,
                  value_slot
              ) in (
                  select tenant_id,
                     view_id,
                     view_version,
                     hash_code,
                     'all'
                 from %s.agg_data
                 where _partition_id = '%s'
              )
            group by tenant_id,
                view_id,
                view_version,
                hash_code
            )
            """;
    AtomicLong beforeCount = new AtomicLong(-1);
    AtomicLong afterCount = new AtomicLong(-1);
    String beforeSql = String.format(beforeTemplate, clickhouseNodeInfo.getDatabase(), partition);
    String afterSql = String.format(afterTemplate, clickhouseNodeInfo.getDatabase(), partition, clickhouseNodeInfo.getDatabase(), partition);
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
      connection.query(beforeSql, rs -> {
        if (rs.next()) {
          beforeCount.set(rs.getLong(1));
        }
      });
      connection.query(afterSql, rs -> {
        if (rs.next()) {
          afterCount.set(rs.getLong(1));
        }
      });
      log.info("newMergeResultCheck, beforeCount:{}, afterCount, {},{},{}", beforeCount.get(), afterCount.get(), clickhouseNodeInfo.getDatabase(),partition);
    }
    catch (Exception ex){
      log.error("newMergeResultCheck exception:{}", clickhouseNodeInfo.getJdbcUrl(), ex);
    }
    return beforeCount.get() >= 0 && afterCount.get() >= 0 && beforeCount.get() == afterCount.get();
  }

  public String getViewVersionFilter(ClickhouseNodeInfo clickhouseNodeInfo, String partition, Set<String> viewIds){
    try {
      Set<String> viewsList = Sets.newHashSet();
      Set<String> views = Sets.newHashSet();
      for (String tenantId : getAggTenants(clickhouseNodeInfo, partition)) {
        String filterValues = getAggViewsList(clickhouseNodeInfo, partition, tenantId);
        for (String viewVersion : pgCommonDao.getViewsMaxVersion(tenantId, filterValues)) {
          List<String> vv = Splitter.on(",").splitToList(viewVersion);
          viewsList.add(String.format("('%s','%s',%s)", tenantId, vv.get(0), vv.get(1)));
          views.add(String.format("('%s','%s')", tenantId, vv.get(0)));
        }
        if(viewsList.size() >= viewVersionFilterThrottle){
          viewIds.addAll(views);
          break;
        }
      }
      return Joiner.on(",").join(viewsList);
    }
    catch (Exception ex){
      log.error("getViewVersionFilter exception:{}", clickhouseNodeInfo.getJdbcUrl(), ex);
    }
    return "";
  }

  private String getMergeToPartition(ClickhouseNodeInfo clickhouseNodeInfo, String tableName){
    String sqlTemplate = """
            SELECT partition, rows
            FROM (
                SELECT
                    partition,
                    sum(total_rows) OVER (ORDER BY partition ASC) AS rows
                FROM (
                    SELECT
                        partition,
                        sum(rows) AS total_rows
                    FROM system.parts
                    WHERE table = '%s'
                        AND database = '%s'
                        AND active = 1
                    GROUP BY partition
                )
            )
            WHERE rows <= %d
            ORDER BY rows DESC
            LIMIT 1
            """;
    String sqlStr = String.format(sqlTemplate, tableName, clickhouseNodeInfo.getDatabase(), GrayManager.isAllowByRule("allow_merge_partition_range_filter_gray_db", clickhouseNodeInfo.getDatabase()) ? mergePerTotalSizeGray : mergePerTotalSize);
    AtomicReference<String> partition = new AtomicReference<>("");
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 600000L)) {
      connection.query(sqlStr, rs -> {
        if (rs.next()) {
          partition.set(rs.getString(1));
        }
      });
    } catch (SQLException e) {
      log.error("getMergeToPartition exception", e);
    }
    return partition.get();
  }

  public List<String> createPartitionQuantileSqlList(ClickhouseNodeInfo clickhouseNodeInfo, long pageSize, String partition){
    List<String> whereWithList = new ArrayList<>();
    List<String> sqlList = new ArrayList<>();
    List<BigDecimal> quantileList = CHContext.calQuantiles(getTablePartitionSize(clickhouseNodeInfo, "agg_data", partition), pageSize);
    String versionFilter = "";
    String versionMaxVersionWith = "";
    //是否开启图的最高版本过滤
    if(GrayManager.isAllowByRule("allow_merge_agg_version_check_db", clickhouseNodeInfo.getDatabase())){
      Set<String> viewIds = Sets.newHashSet();
      String vFilter = getViewVersionFilter(clickhouseNodeInfo, partition, viewIds);
      if(StringUtils.isNotEmpty(vFilter)) {
        if(CollectionUtils.isNotEmpty(viewIds)) {
          versionMaxVersionWith = String.format("""
                          view_max_version as(select * from values(%s)),
                          view_filter_list as(select * from values(%s)),""",
                  vFilter, Joiner.on(",").join(viewIds));
          //每次最多处理 2*viewVersionFilterThrottle 个不同的tenant_id, view_id，防止merge sql超长
          versionFilter = " AND ((tenant_id, view_id, view_version) IN (select * from view_max_version) OR (tenant_id, view_id) NOT IN (select * from view_filter_list)) ";
        }
        else {
          versionMaxVersionWith = String.format("view_max_version as(select * from values(%s)),", vFilter);
          versionFilter = " AND (tenant_id, view_id, view_version) IN (select * from view_max_version) ";
        }
      }
    }
    /**这个where条件分两大部分：
     * 1、使用quantity后生成的hash_code条件来圈选参与merge的数据范围，此时不拼partition条件，同一个hash_code所有分区的数据都参与merge；分区过的时候每次最多merge10亿行，保证内存不超额使用
     * 2、not in 条件是排除已经merge过了的数据；一个hash_code，如果在其他分区中有value_slot='all'的相同hash_code，并且timestamp更大，则这个hash_code被merge过了
     */
    String whereSqlWithTemplate = """
            with %s
            current_data as (
                select tenant_id,
                    view_id,
                    view_version,
                    hash_code,
                    toUnixTimestamp(max(timestamp)) as ts
                from %s.agg_data
                where %s
                    AND _partition_id = '%s'
                group by tenant_id,
                    view_id,
                    view_version,
                    hash_code
            ),
            merged_data as (
                select tenant_id,
                    view_id,
                    view_version,
                    hash_code,
                    toUnixTimestamp(max(timestamp)) as ts
                from %s.agg_data
                where %s
                    AND
                    (
                        tenant_id,
                        view_id,
                        view_version,
                        hash_code,
                        value_slot
                    ) in (
                        select tenant_id,
                            view_id,
                            view_version,
                            hash_code,
                            'all'
                        from current_data
                    )
                    AND _partition_id > '%s'
                group by tenant_id,
                    view_id,
                    view_version,
                    hash_code
            )
            """;
    String parRangeFilter = "";
    if(GrayManager.isAllowByRule("allow_merge_partition_range_filter_db", clickhouseNodeInfo.getDatabase())){
      String lastPartition = getMergeToPartition(clickhouseNodeInfo, "agg_data");
      //每次merge最多10亿条；如果第一个分区就超过了10亿条，则只merge第一个分区
      if(StringUtils.isNotEmpty(lastPartition)){
        parRangeFilter = String.format(" AND _partition_id >= '%s' AND _partition_id <= '%s' ", partition, lastPartition);
      }
      else {
        parRangeFilter = String.format(" AND _partition_id = '%s' ", partition);
      }
    }
    String whereSql = String.format("""
            AND (
                tenant_id,
                view_id,
                view_version,
                hash_code
            ) in (
            select c.tenant_id,
                  c.view_id,
                  c.view_version,
                  c.hash_code
              from current_data as c
                  left join merged_data as m on c.tenant_id = m.tenant_id
                  and c.view_id = m.view_id
                  and c.view_version = m.view_version
                  and c.hash_code = m.hash_code
              where c.ts > coalesce(m.ts, 0)
            )
            %s
            """, parRangeFilter);
    String chDbName = clickhouseNodeInfo.getDatabase();
    List<String> quantileCodeList = chMetadataService.queryTableQuantile(clickhouseNodeInfo.getJdbcUrl(),
            quantileList, "agg_data", String.format(" _partition_id = '%s' ", partition));
    // 当只有一个元素时循环不执行，在循环后的语句拼接 hash_code > 0
    for (int i = 0; i < quantileCodeList.size() - 1; i++) {
      String from = quantileCodeList.get(i);
      String end = quantileCodeList.get(i + 1);
      String hashSql = String.format(" (hash_code > %s AND hash_code <= %s) %s ", from, end, versionFilter);
      String whereWithSql = String.format(whereSqlWithTemplate, versionMaxVersionWith, chDbName, hashSql, partition, chDbName, hashSql, partition);
      String sql = this.createMergeSQL(clickhouseNodeInfo, whereSql, false, true, whereWithSql);
      sqlList.add(sql);
      whereWithList.add(whereWithSql);
    }
    // 最后一个查最后剩余的数据
    String hashSql = " (hash_code > %s) %s ".formatted(quantileCodeList.get(quantileCodeList.size() - 1), versionFilter);
    String whereWithSql = String.format(whereSqlWithTemplate, versionMaxVersionWith, chDbName, hashSql, partition, chDbName, hashSql, partition);
    whereWithList.add(whereWithSql);
    String sql = this.createMergeSQL(clickhouseNodeInfo, whereSql, false, true, whereWithSql);
    sqlList.add(sql);

    //基于分区的新merge机制，在执行move partition之前会做count校验，所以不用再在这里做校验

    /*if(bAllowQuantityCheck && !checkPartitionQuantileCount(clickhouseNodeInfo, whereWithList,
                    String.format(whereSqlWithTemplate, chDbName, " 1=1 ",  partition, chDbName, partition), whereSql))
      return new ArrayList<>();
    */
    return sqlList;
  }

  /***
   * 新的merge机制，按照分区直接在agg_data上原地merge
   * @param clickhouseNodeInfo
   * @param pageSize
   * @param chReadTimeOut
   * @return  merge成功的分区名称
   */
  public String newMergeAggDataTable(ClickhouseNodeInfo clickhouseNodeInfo, int pageSize, int chReadTimeOut) {
    if(!tablePropertyCheck(clickhouseNodeInfo, "agg_data", "sys_modified_time", "toYYYYMMDD(sys_modified_time)"))
      return "";
    //如果CH中存在merge process，不执行任务
    if(hasNewMergeTaskInProgress(clickhouseNodeInfo))
      return "";

    String partition = getMinPartition(clickhouseNodeInfo, "agg_data");
    List<String> sqlList = createPartitionQuantileSqlList(clickhouseNodeInfo, pageSize, partition);
    if (CollectionUtils.isEmpty(sqlList)) {
      throw new RuntimeException("newMergeAggDataTable create mergeSQL error:" + JSON.toJSONString(clickhouseNodeInfo));
    }
    log.info("newMergeAggDataTable total {}, {}", sqlList.size(), clickhouseNodeInfo.getDatabase());
    log.info("newMergeAggDataTable all sql: {}, {}", clickhouseNodeInfo.getDatabase(), sqlList);
    long begin = System.currentTimeMillis();
    AtomicLong totalCount = new AtomicLong(0);
    //任务编号
    AtomicLong num = new AtomicLong(0);
    ExecutorService fixedThreadPools = Executors.newFixedThreadPool(parallelSize);
    List<Future<Integer>> taskFuture = new ArrayList<>();
    for (String sql : sqlList) {
      taskFuture.add(fixedThreadPools.submit( () -> {
        num.incrementAndGet();
        int count=0;
        int retryTimes = 10;
        for(int retryCount = 0; retryCount < retryTimes; retryCount++) {
          try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), chReadTimeOut)) {
            long start = System.currentTimeMillis();
            //执行merge的时候，如果db_sync_info.status不是exchange_agg，则表示已经发生了异常，不需要再继续执行merge了
            boolean bShouldMerge = shouldExchangeTable(clickhouseNodeInfo, true);
            if(bShouldMerge) {
              count = connection.executeUpdate(sql);
              log.info("new merge task{}, count: {}, cost: {}, sql: {}", num.get(), count, System.currentTimeMillis() - start, sql);
              BizAuditLog auditLog = new BizAuditLog();
              auditLog.setModule("STAT_CHART_DATA_MERGE");
              auditLog.setStartTime(System.currentTimeMillis());
              auditLog.setFrom(clickhouseNodeInfo.getDatabase());
              auditLog.setFromId(String.format("merge_task_new_%s", num.get()));
              auditLog.setCost(System.currentTimeMillis() - start);
              auditLog.setNum(num.get());
              auditLog.setMessage(String.format("new merge task%d, count: %d", num.get(), count));
              auditLog.setExtra(sql);
              auditLog.log();
            }
            else {
              log.info("new merge task{} should not execute merge:{}, sql: {}", num.get(), clickhouseNodeInfo.getDatabase(), sql);
              count = -2;
            }
            break;
          } catch (Exception e) {
            log.warn("new merge task{}, {}, exception:{},  sql: {}", num.get(), JSON.toJSONString(clickhouseNodeInfo), e, sql);
            BizAuditLog auditLog = new BizAuditLog();
            auditLog.setModule("STAT_CHART_DATA_MERGE");
            auditLog.setStartTime(System.currentTimeMillis());
            auditLog.setFrom(clickhouseNodeInfo.getDatabase());
            auditLog.setFromId(String.format("merge_task_new_%s", num.get()));
            auditLog.setNum(num.get());
            auditLog.setMessage(String.format("new merge task%d", num.get()));
            auditLog.setError(String.format("new merge task%d exception: %s", num.get(), e));
            auditLog.setExtra(sql);
            auditLog.log();
            if(retryCount < retryTimes-1){
              Uninterruptibles.sleepUninterruptibly(300, TimeUnit.SECONDS);
            }
            else{
              throw e;
            }
          }
        }
        return count;
      }));
    }
    boolean bSuccessAll = true;
    try {
      for (Future<Integer> task : taskFuture) {
        Integer rCount = task.get();
        if(rCount == -2){
          bSuccessAll = false;
          log.info("new merge tasks not all success: {}, partition:{}",clickhouseNodeInfo.getDatabase(), partition);
        }
        totalCount.addAndGet(rCount);
      }
      log.info("new merge tasks execute all: {}, total_count: {}, total_cost:{}, partition:{}",clickhouseNodeInfo.getDatabase(), totalCount.get(), System.currentTimeMillis() - begin, partition);
      BizAuditLog auditLog = new BizAuditLog();
      auditLog.setModule("STAT_CHART_DATA_MERGE");
      auditLog.setCost(System.currentTimeMillis() - begin);
      auditLog.setStartTime(System.currentTimeMillis());
      auditLog.setFrom(clickhouseNodeInfo.getDatabase());
      auditLog.setFromId(String.format("merge_task_new_9999"));
      auditLog.setNum(9999);
      auditLog.setMessage(String.format("new merge tasks all , total_count: %d, partition: %s" , totalCount.get(), partition));
      //auditLog.setExtra(sqlList);
      auditLog.log();
      fixedThreadPools.shutdown();
    }
    catch(Exception ex){
      log.warn("new merge tasks all, {}, exception", JSON.toJSONString(clickhouseNodeInfo), ex);
      BizAuditLog auditLog = new BizAuditLog();
      auditLog.setModule("STAT_CHART_DATA_MERGE");
      auditLog.setStartTime(System.currentTimeMillis());
      auditLog.setFrom(clickhouseNodeInfo.getDatabase());
      auditLog.setFromId(String.format("merge_task_new_9999"));
      auditLog.setNum(9999);
      auditLog.setError(String.format("new merge tasks all, exception: %s", ex));
      auditLog.log();
      fixedThreadPools.shutdown();
      throw new RuntimeException(ex);
    }
    if(!bSuccessAll)
      return "";
    else
      return partition;
  }

  /**
   * 对agg_data表的数据进行merge
   */
  public long mergeAggDataTable(ClickhouseNodeInfo clickhouseNodeInfo, int pageSize, int chReadTimeOut, boolean bIncrement) {
    //如果CH中存在merge process，不执行任务
    if(hasMergeTaskInProgress(clickhouseNodeInfo, false))
      return -1;

    List<String> sqlList = new ArrayList<>();
    Map<String, Long> tableSizeMap = chMetadataService.queryTableSize(clickhouseNodeInfo.getJdbcUrl(),
      Lists.newArrayList("agg_data"));
    if (tableSizeMap != null && tableSizeMap.get("agg_data") != null) {
      String tsSql = " 1=1 ";
      if(bIncrement){
        tsSql = String.format(" timestamp >= (select max_merged_timestamp from %s.bi_merge_task final where table_name = 'agg_data' " +
                "and status = 2 and is_deleted = 0 order by timestamp desc limit 1) ", clickhouseNodeInfo.getDatabase());
      }
      //根据分位数生成sql集合
      sqlList = createQuantileSqlList(clickhouseNodeInfo, tableSizeMap.get("agg_data"), pageSize , tsSql, bIncrement);
    }
    if (CollectionUtils.isEmpty(sqlList)) {
      throw new RuntimeException("mergeAggDataTable create mergeSQL error:" + JSON.toJSONString(clickhouseNodeInfo));
    }

    log.info("x:{}, total {}, {}, all sql:, {}", bIncrement, sqlList.size(), clickhouseNodeInfo.getDatabase(), sqlList);

    long begin = System.currentTimeMillis();
    AtomicLong totalCount = new AtomicLong(0);
    //任务编号
    AtomicLong num = new AtomicLong(0);
    ExecutorService fixedThreadPools = Executors.newFixedThreadPool(parallelSize);
    List<Future<Integer>> taskFuture = new ArrayList<>();
    for (String sql : sqlList) {
      taskFuture.add(fixedThreadPools.submit( () -> {
        num.incrementAndGet();
        int count=0;
        int retryTimes = 5;
        for(int retryCount = 0; retryCount < retryTimes; retryCount++) {
          try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), chReadTimeOut)) {
            long start = System.currentTimeMillis();
            //增量merge的时候，如果db_sync_info.status不是exchange_agg，则表示已经发生了异常，不需要再继续执行merge了
            boolean bShouldMerge = shouldExchangeTable(clickhouseNodeInfo, true);
            if(bIncrement && bShouldMerge || !bIncrement) {
              count = connection.executeUpdate(sql);
              log.info("merge task{} bIncrement:{}, bShouldMerge:{}, {}, count: {}, cost: {}, sql: {}", num.get(), bIncrement, bShouldMerge, clickhouseNodeInfo.getDatabase(), count, System.currentTimeMillis() - start, sql);
              BizAuditLog auditLog = new BizAuditLog();
              auditLog.setModule("STAT_CHART_DATA_MERGE");
              auditLog.setStartTime(System.currentTimeMillis());
              auditLog.setFrom(clickhouseNodeInfo.getDatabase());
              auditLog.setFromId(String.format("merge_task_%d_%s", num.get(), bIncrement ? "i" : "a"));
              auditLog.setCost(System.currentTimeMillis() - start);
              auditLog.setNum(num.get());
              auditLog.setMessage(String.format("merge task%d bIncrement:%s, count: %d", num.get(), bIncrement, count));
              auditLog.setExtra(sql);
              auditLog.log();
            }
            else {
              log.info("merge task{} increment should not execute merge:{}, sql: {}", num.get(), clickhouseNodeInfo.getDatabase(), sql);
              count = -2;
            }
            break;
          } catch (Exception e) {
            log.warn("merge task{}, {}, exception:{},  sql: {}", num.get(), JSON.toJSONString(clickhouseNodeInfo), e, sql);
            BizAuditLog auditLog = new BizAuditLog();
            auditLog.setModule("STAT_CHART_DATA_MERGE");
            auditLog.setStartTime(System.currentTimeMillis());
            auditLog.setFrom(clickhouseNodeInfo.getDatabase());
            auditLog.setFromId(String.format("merge_task_%d_%s", num.get(), bIncrement?"i":"a"));
            auditLog.setNum(num.get());
            auditLog.setMessage(String.format("merge task%d bIncrement:%s", num.get(), bIncrement));
            auditLog.setError(String.format("merge task%d exception: %s", num.get(), e));
            auditLog.setExtra(sql);
            auditLog.log();
            if(retryCount < retryTimes-1){
              Uninterruptibles.sleepUninterruptibly(300, TimeUnit.SECONDS);
            }
            else{
              throw e;
            }
          }
        }
        return count;
      }));
    }
    boolean bSuccessAll = true;
    try {
      for (Future<Integer> task : taskFuture) {
        Integer rCount = task.get();
        if(rCount == -2){
          bSuccessAll = false;
          log.info("merge tasks not all success, bIncrement {}, {}", bIncrement, clickhouseNodeInfo.getDatabase());
        }
        totalCount.addAndGet(rCount);
      }
      log.info("merge tasks execute successfully all:  bIncrement:{}, {}, total_count: {}, total_cost:{}", bIncrement,clickhouseNodeInfo.getDatabase(), totalCount.get(), System.currentTimeMillis() - begin);
      BizAuditLog auditLog = new BizAuditLog();
      auditLog.setModule("STAT_CHART_DATA_MERGE");
      auditLog.setCost(System.currentTimeMillis() - begin);
      auditLog.setStartTime(System.currentTimeMillis());
      auditLog.setFrom(clickhouseNodeInfo.getDatabase());
      auditLog.setFromId(String.format("merge_task_9999_%s", bIncrement?"i":"a"));
      auditLog.setNum(9999);
      auditLog.setMessage(String.format("merge tasks all bIncrement:%s, total_count: %d", bIncrement , totalCount.get()));
      //auditLog.setExtra(sqlList);
      auditLog.log();
      fixedThreadPools.shutdown();
    }
    catch(Exception ex){
      log.warn("merge tasks all bIncrement:{},  {}, exception:", bIncrement, JSON.toJSONString(clickhouseNodeInfo), ex);
      BizAuditLog auditLog = new BizAuditLog();
      auditLog.setModule("STAT_CHART_DATA_MERGE");
      auditLog.setStartTime(System.currentTimeMillis());
      auditLog.setFrom(clickhouseNodeInfo.getDatabase());
      auditLog.setFromId(String.format("merge_task_9999_%s", bIncrement?"i":"a"));
      auditLog.setNum(9999);
      auditLog.setError(String.format("merge tasks all bIncrement:%s, exception: %s", bIncrement, ex));
      auditLog.log();
      fixedThreadPools.shutdown();
      throw new RuntimeException(ex);
    }
    if(!bSuccessAll)
      return -1;
    else
      return totalCount.get();
  }

  /**
   * 生成merge sql
   *
   * @param clickhouseNodeInfo clickhouse node
   * @return sql
   */
  public String createMergeSQL(ClickhouseNodeInfo clickhouseNodeInfo, String quantileSql, boolean bIncrement, boolean bPartMerge, String withSql) {
    List<String> columns = Lists.newArrayList();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
      connection.query(
        "select name from system.columns where database='" + clickhouseNodeInfo.getDatabase() + "' " + "and" +
          " table ='agg_data' order by position ", rs -> {
          while (rs.next()) {
            columns.add(rs.getString(1));
          }
        });
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
    StringBuilder sb1 = new StringBuilder();
    StringBuilder sb2 = new StringBuilder(1024);
    StringBuilder sb3 = new StringBuilder();
    for (String column : columns) {
      if (StringUtils.equalsAny(column, "value_slot", "timestamp", "is_deleted", "sys_modified_time", "rule_id", "batch_num")) {
        //do nothing
      } else if (StringUtils.equalsAny(column, "tenant_id", "view_id", "view_version", "hash_code")) {
        sb1.append(column).append(",");
        sb2.append(column).append(",");
      } else if (column.startsWith("agg_uniq_tag_")) {
        sb1.append(column).append(",");
        sb2.append("any(").append(column).append("),");
      } else if (column.startsWith("agg_count_")) {
        sb1.append(column).append(",");
        sb2.append("argMax(").append(column).append(", timestamp) AS n_").append(column).append(",");
        sb3.append("isZeroOrNull(n_").append(column).append(") AND ");
      } else if (column.startsWith("agg_sum_") && !column.endsWith("_merge")) {
        sb1.append(column).append(",");
        if (GrayManager.isAllowByRule("sum_zero_to_null_ch_db", clickhouseNodeInfo.getDatabase())) {
          sb2.append("any_respect_nullsArgMaxIf(").append(column).append(", timestamp, isNotNull(").append(column).append("_merge)) AS n_").append(column).append(",");
        } else {
          sb2.append("argMax(").append(column).append(", timestamp) AS n_").append(column).append(",");
        }
        sb3.append("isZeroOrNull(n_").append(column).append("_merge").append(") AND ");
      } else if (column.startsWith("agg_sum_") && column.endsWith("_merge")) {
        sb1.append(column).append(",");
        sb2.append("argMax(").append(column).append(", timestamp) AS n_").append(column).append(",");
      } else if (column.startsWith("agg_uniq_") && !column.endsWith("_merge")) {
        sb1.append(column).append(",");
        sb2.append("argMaxIf(").append(column).append(", timestamp, ").append(column).append("_merge) AS n_").append(column).append(",");
        sb3.append("finalizeAggregation(n_").append(column).append(")=0 AND ");
      } else if (column.startsWith("agg_uniq_") && column.endsWith("_merge")) {
        sb1.append(column).append(",");
        sb2.append("argMaxIf(").append(column).append(", timestamp, ").append(column).append(") AS n_").append(column).append(",");;
      } else {
        sb1.append(column).append(",");
        sb2.append("any(").append(column).append("),");
      }
    }
    if (StringUtils.isBlank(quantileSql)) {
      quantileSql = "";
    }
    String havingSql = "";
    String valueSlot = "'all_inc',";
    if (!bIncrement) {
//      if(GrayManager.isAllowByRule("agg_merge_use_having_chdb",clickhouseNodeInfo.getDatabase())){
        havingSql = "HAVING not(" + sb3.substring(0, sb3.length() - 5) + ")";
//      }
      valueSlot = "'all',";
    }

    //基于swap的原merge机制，先判断agg_data_swap是否包含sys_modified_time
    String mergeTableName = "agg_data_swap";
    String sysMdColumn = "sys_modified_time,";
    String sysMdTime = "now64(9),";
    if(!bPartMerge && !tablePropertyCheck(clickhouseNodeInfo, "agg_data_swap", "sys_modified_time", "toYYYYMMDD(sys_modified_time)")){
      sysMdColumn = "";
      sysMdTime = "";
    }
    //基于partition的新merge机制
    if(bPartMerge){
      mergeTableName = "agg_data";
      sysMdColumn = "sys_modified_time,";
      sysMdTime = "now64(9),";
      if(!bAllowMergeHaving){
        havingSql = "";
      }
    }
    String maxInsertThreadSetting = "";
    if(GrayManager.isAllowByRule("allow_merge_max_insert_thread", clickhouseNodeInfo.getDatabase())){
      maxInsertThreadSetting = String.format(", max_insert_threads = %d ", mergeMaxInsertThreads);
    }
    String maxThreadSetting = "";
    if(GrayManager.isAllowByRule("allow_merge_max_thread", clickhouseNodeInfo.getDatabase())){
      maxThreadSetting = String.format(", max_threads = %d ", mergeMaxThreads);
    }
    
    return "INSERT INTO " + mergeTableName + "(" +
      sb1 + """
      value_slot,
      timestamp,
      """ + sysMdColumn + """
      is_deleted
      )
      """ + withSql + """
      SELECT
      """ + sb2 + valueSlot + """
      max(timestamp),
      """ + sysMdTime + """
      0
      """ + " FROM " + clickhouseNodeInfo.getDatabase() + """
      .agg_data
      WHERE 1=1 \s
      """ + quantileSql + """
      GROUP BY tenant_id,
          view_id,
          view_version,
          hash_code
      """ + havingSql + """
      SETTINGS max_bytes_before_external_sort=""" + maxBytesBeforeExternalSort.toString() + "," + """
      max_bytes_before_external_group_by=""" + maxBytesBeforeExternalGroupBy.toString() + "," + """
      group_by_two_level_threshold_bytes=""" + groupByTwoLevelThresholdBytes.toString()  +
            maxInsertThreadSetting + maxThreadSetting + "," + """
      optimize_aggregation_in_order=""" + optimizeAggregationInOrder.toString() + ";";
  }


  /**
   * 查看原表agg_data建表语句
   *
   * @param clickhouseNodeInfo db info
   * @return 原表agg_data建表语句
   */
  public String queryCreateAggDataTableSQL(ClickhouseNodeInfo clickhouseNodeInfo) {
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
      Holder<String> resultHolder = new Holder<>();
      connection.query("show create table agg_data", rs -> {
        if (rs.next()) {
          resultHolder.set(rs.getString(1));
        }
      });
      return resultHolder.get();
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  public long getAggDistinctCount(ClickhouseNodeInfo clickhouseNodeInfo) {
    String aggDataSql = """
              select count()
              from (
                      select tenant_id,
                          view_id,
                          view_version,
                          hash_code
                      from %s.agg_data
                      group by tenant_id,
                          view_id,
                          view_version,
                          hash_code
                  )
                  SETTINGS max_bytes_before_external_sort=%d,
                        max_bytes_before_external_group_by=%d,
                        group_by_two_level_threshold_bytes=%d,
                        optimize_aggregation_in_order=1;
            """;
    String aggSql = String.format(aggDataSql, clickhouseNodeInfo.getDatabase(), maxBytesBeforeExternalSort,
            maxBytesBeforeExternalGroupBy, groupByTwoLevelThresholdBytes);
    Holder<Long> aggCount = new Holder<>();
    for(int retryCount=0; retryCount < 5; retryCount++) {
      try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 12000000L)) {
        connection.query(aggSql, rs -> {
          if (rs.next()) {
            aggCount.set(rs.getLong(1));
          }
        });
        log.info("getAggDistinctCount, agg_data:{}", aggCount.getOrDefault((long) -1));
        BizAuditLog auditLog = new BizAuditLog();
        auditLog.setModule("STAT_CHART_DATA_MERGE");
        auditLog.setStartTime(System.currentTimeMillis());
        auditLog.setFrom(clickhouseNodeInfo.getDatabase());
        auditLog.setFromId("merge_get_agg_count");
        auditLog.setMessage(String.format("merge get agg distinct count:%d", aggCount.getOrDefault((long) -1)));
        auditLog.log();
        return aggCount.getOrDefault((long) -1);
      } catch (Exception ex) {
        log.error("getAggDistinctCount exception:{}, {}", clickhouseNodeInfo.getJdbcUrl(), ex);
        if (retryCount < 4) {
          Uninterruptibles.sleepUninterruptibly(120, TimeUnit.SECONDS);
        }
      }
    }
    return -1;
  }

  public boolean checkMergeResult(ClickhouseNodeInfo clickhouseNodeInfo) {
    String aggDataSql = """
              select count()
              from (
                      select tenant_id,
                          view_id,
                          view_version,
                          hash_code
                      from %s.agg_data
                      group by tenant_id,
                          view_id,
                          view_version,
                          hash_code
                  )
            """;
    String aggDataSwapSql = """
              select count()
              from (
                      select tenant_id,
                          view_id,
                          view_version,
                          hash_code
                      from %s.agg_data_swap
                      group by tenant_id,
                          view_id,
                          view_version,
                          hash_code
                  )
            """;
    String aggSql = String.format(aggDataSql, clickhouseNodeInfo.getDatabase());
    String swapSql = String.format(aggDataSwapSql, clickhouseNodeInfo.getDatabase());
    Holder<Long> aggCount = new Holder<>();
    Holder<Long> swapCount = new Holder<>();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 300000L)) {
      connection.query(aggSql, rs -> {
        if (rs.next()) {
          aggCount.set(rs.getLong(1));
        }
      });
      connection.query(swapSql, rs -> {
        if (rs.next()) {
          swapCount.set(rs.getLong(1));
        }
      });
      /*if(aggCount.getOrDefault((long) -1) > 0 && swapCount.getOrDefault((long) -1) > 0 && aggCount.getOrDefault((long) -1) - swapCount.getOrDefault((long) -1) > 10000){
        return true;
      }*/
      log.info("checkMergeResult, agg_data:{}, agg_data_swap:{}", aggCount.getOrDefault((long) -1), swapCount.getOrDefault((long) -1));
      BizAuditLog auditLog = new BizAuditLog();
      auditLog.setModule("STAT_CHART_DATA_MERGE");
      auditLog.setStartTime(System.currentTimeMillis());
      auditLog.setFrom(clickhouseNodeInfo.getDatabase());
      auditLog.setFromId("merge_task_check");
      auditLog.setMessage(String.format("merge check agg_data:%d, agg_data_swap:%d",  aggCount.getOrDefault((long) -1), swapCount.getOrDefault((long) -1)));
      auditLog.log();
      return true;
    }
    catch (Exception ex){
      log.error("checkMergeResult exception:{}, {}", clickhouseNodeInfo.getJdbcUrl(), ex);
    }
    return false;
  }

  public boolean tablePropertyCheck(ClickhouseNodeInfo clickhouseNodeInfo, String tableName, String columnName, String partitionKey) {
    String querySql = String.format("select name from system.columns where database = '%s' and table = '%s' and name = '%s' limit 1;", clickhouseNodeInfo.getDatabase(), tableName, columnName);
    Holder<Boolean> bColumn = new Holder<>();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 600000L)) {
      connection.query(querySql, rs -> {
        if (rs.next()) {
          bColumn.set(true);
        }
        else {
          bColumn.set(false);
        }
      });
      Holder<Boolean> bPartition = new Holder<>();
      if(StringUtils.isNotEmpty(partitionKey)){
        querySql = String.format("select partition_key from system.tables where database = '%s' and name = '%s' and partition_key = '%s' limit 1;", clickhouseNodeInfo.getDatabase(), tableName, partitionKey);
        connection.query(querySql, rs -> {
          if (rs.next()) {
            bPartition.set(true);
          }
          else {
            bPartition.set(false);
          }
        });
      }
      else {
        bPartition.set(true);
      }
      log.info("tablePropertyCheck db:{}, table:{}, column:{}, partition:{}, bColumn: {}, bPartition:{}", clickhouseNodeInfo.getDatabase(),tableName, columnName, partitionKey, bColumn.get(), bPartition.get());
      return bColumn.get() && bPartition.get();
    }
    catch (Exception ex){
      log.error("tablePropertyCheck exception:{}", clickhouseNodeInfo.getJdbcUrl(), ex);
    }
    return false;
  }

  public boolean partitionCountCheck(ClickhouseNodeInfo clickhouseNodeInfo) {
    String diffSql = """
              select count() as cnt from (
              select partition
              from system.parts
              where database = '%s'
                and table = 'agg_data'
                and active = 1
              group by partition
                );
            """;
    String aggSql = String.format(diffSql, clickhouseNodeInfo.getDatabase(),clickhouseNodeInfo.getDatabase());
    Holder<Long> diffCount = new Holder<>();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 600000L)) {
      connection.query(aggSql, rs -> {
        if (rs.next()) {
          diffCount.set(rs.getLong(1));
        }
      });
      boolean bMerge = diffCount.getOrDefault((long)-1) >= 2;
      log.info("partitionCountCheck, partition count:{}, bMerge:{}", diffCount.getOrDefault((long) -1), bMerge);
      BizAuditLog auditLog = new BizAuditLog();
      auditLog.setModule("STAT_CHART_DATA_MERGE");
      auditLog.setStartTime(System.currentTimeMillis());
      auditLog.setFrom(clickhouseNodeInfo.getDatabase());
      auditLog.setFromId("merge_task_partition_check");
      auditLog.setExtra(Thread.currentThread().getName());
      auditLog.setMessage(String.format("partition check count:%d, bMerge:%s",  diffCount.getOrDefault((long) -1), bMerge));
      auditLog.log();
      return bMerge;
    }
    catch (Exception ex){
      log.error("partitionCountCheck exception:{}, {}", clickhouseNodeInfo.getJdbcUrl(), ex);
    }
    return false;
  }
  public boolean isNeedMerge(ClickhouseNodeInfo clickhouseNodeInfo) {
    String diffSql = """
              select (
                       select count()
                       from %s.agg_data
                   ) - (
                       select uniq(
                               tenant_id,
                               view_id,
                               view_version,
                               hash_code
                           )
                       from %s.agg_data
                   ) as diff
            """;
    String aggSql = String.format(diffSql, clickhouseNodeInfo.getDatabase(),clickhouseNodeInfo.getDatabase());
    Holder<Long> diffCount = new Holder<>();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 600000L)) {
      connection.query(aggSql, rs -> {
        if (rs.next()) {
          diffCount.set(rs.getLong(1));
        }
      });
      boolean bMerge = diffCount.getOrDefault((long) -1) >= mergeDiff;
      log.info("bNeedMerge, agg_data merge diff:{}, bMerge:{}", diffCount.getOrDefault((long) -1), bMerge);
      BizAuditLog auditLog = new BizAuditLog();
      auditLog.setModule("STAT_CHART_DATA_MERGE");
      auditLog.setStartTime(System.currentTimeMillis());
      auditLog.setFrom(clickhouseNodeInfo.getDatabase());
      auditLog.setFromId("merge_task_diff_check");
      auditLog.setMessage(String.format("merge diff check agg_data diff:%d, bMerge:%s",  diffCount.getOrDefault((long) -1), bMerge));
      auditLog.log();
      return bMerge;
    }
    catch (Exception ex){
      log.error("bNeedMerge exception:{}, {}", clickhouseNodeInfo.getJdbcUrl(), ex);
    }
    return true;
  }

  /**
   * agg_data_swap 替换 agg_data
   *
   * @param clickhouseNodeInfo clickhouse连接信息
   */
  public boolean swapAggDataAggDataSwap(ClickhouseNodeInfo clickhouseNodeInfo) {
    for(int retryCount = 0; retryCount<3; retryCount++) {
      try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
        String sql = "exchange tables agg_data_swap and agg_data on cluster '" + clickhouseNodeInfo.getCluster() + "';";

        BizAuditLog auditLog = new BizAuditLog();
        auditLog.setModule("STAT_CHART_DATA_MERGE");
        auditLog.setStartTime(System.currentTimeMillis());
        auditLog.setFrom(clickhouseNodeInfo.getDatabase());
        auditLog.setFromId("merge_task_exchange");
        if (this.shouldExchangeTable(clickhouseNodeInfo, true)) {
          connection.executeUpdate(sql);
          auditLog.setMessage(String.format("merge exchange agg_data and agg_data_swap %s", "success"));
        } else {
          auditLog.setMessage(String.format("merge exchange agg_data and agg_data_swap %s", "fail"));
        }
        auditLog.setExtra(sql);
        auditLog.log();
        break;
      }
      catch (SQLException e) {
        if(e.getMessage().contains("RENAME EXCHANGE is not supported")){
          return swapAggDataAggDataSwapByRename(clickhouseNodeInfo);
        }
        else {
          log.error("merge exchange exception:{}", e);
          BizAuditLog auditLog = new BizAuditLog();
          auditLog.setModule("STAT_CHART_DATA_MERGE");
          auditLog.setStartTime(System.currentTimeMillis());
          auditLog.setFrom(clickhouseNodeInfo.getDatabase());
          auditLog.setFromId("merge_task_exchange");
          auditLog.setMessage(String.format("merge exchange agg_data and agg_data_swap"));
          auditLog.setError(String.format("merge exchange exception: %s", e));
          auditLog.log();
          if (retryCount < 2) {
            Uninterruptibles.sleepUninterruptibly(120, TimeUnit.SECONDS);
          } else {
            return false;
          }
        }
      }
    }
    return true;
  }

  public String getTableUUID(ClickhouseNodeInfo clickhouseNodeInfo, String tableName){
    String querySql = """
        select uuid from system.tables
        where database = '%s' and name = '%s'
          """;
    String qSql = String.format(querySql, clickhouseNodeInfo.getDatabase(), tableName);
    AtomicReference<String> uuid = new AtomicReference<>();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(qSql, rs -> {
        if (rs.next()) {
          uuid.set(rs.getString(1));
        }
      });
    } catch (Exception e) {
      log.error("getTableUUID exception {}", e);
    }
    return uuid.get();
  }

  public String getTableNameByUUID(ClickhouseNodeInfo clickhouseNodeInfo, String uuid){
    String querySql = """
        select name from system.tables
        where database = '%s' and uuid = '%s'
          """;
    String qSql = String.format(querySql, clickhouseNodeInfo.getDatabase(), uuid);
    AtomicReference<String> tableName = new AtomicReference<>();
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 60000L)) {
      connection.query(qSql, rs -> {
        if (rs.next()) {
          tableName.set(rs.getString(1));
        }
      });
    } catch (Exception e) {
      log.error("getTableUUID exception {}", e);
    }
    return tableName.get();
  }

  //有的linux内核没有支持RENAME_EXCHANGE，不能使用exchange，需要重新执行rename tables，需要保证原子性
  public boolean swapAggDataAggDataSwapByRename(ClickhouseNodeInfo clickhouseNodeInfo) {
    String aggUUID = getTableUUID(clickhouseNodeInfo, "agg_data");
    String aggSwapUUID = getTableUUID(clickhouseNodeInfo, "agg_data_swap");
    if(StringUtils.isEmpty(aggUUID) || StringUtils.isEmpty(aggSwapUUID)){
      log.error("swapAggDataAggDataSwapByRename agg_data or agg_data_swap UUID is null");
      return false;
    }

    boolean bRet = true;
    for(int retryCount = 0; retryCount<3; retryCount++) {
      try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
        //只能执行一次，清理历史表，否则重试的时候就把agg_data清理掉了
        /*String aggSwap1UUID = getTableUUID(clickhouseNodeInfo, "agg_data_swap1");
        if(StringUtils.isNotEmpty(aggSwap1UUID) && !StringUtils.equals(aggUUID, aggSwap1UUID) && !StringUtils.equals(aggSwapUUID, aggSwap1UUID)) {
          connection.executeUpdate(String.format("drop table if exists %s.agg_data_swap1 on cluster '%s'",
                  clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getCluster()));
        }*/

        BizAuditLog auditLog = new BizAuditLog();
        auditLog.setModule("STAT_CHART_DATA_MERGE");
        auditLog.setStartTime(System.currentTimeMillis());
        auditLog.setFrom(clickhouseNodeInfo.getDatabase());
        auditLog.setFromId("merge_task_rename");
        if (this.shouldExchangeTable(clickhouseNodeInfo, true)) {
          //第一步
          try {
            String sql = String.format("rename table %s.agg_data to %s.agg_data_swap1 on cluster '%s'", clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getCluster());
            connection.executeUpdate(sql);
          }
          catch (Exception ex){
            log.error("merge rename agg_data to agg_data_swap1 exception:{}", ex);
            throw ex;
          }

          //第二步
          try {
            String sql = String.format("rename table %s.agg_data_swap to %s.agg_data on cluster '%s'", clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getCluster());
            connection.executeUpdate(sql);
          }
          catch (Exception ex){
            log.error("merge rename agg_data_swap to agg_data exception:{}", ex);
            //回滚第一步操作
            connection.executeUpdate(String.format("rename table %s.agg_data_swap1 to %s.agg_data on cluster '%s'", clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getCluster()));
            throw ex;
          }

          //第三步
          try {
            String sql = String.format("rename table %s.agg_data_swap1 to %s.agg_data_swap on cluster '%s'", clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getCluster());
            connection.executeUpdate(sql);
          }
          catch (Exception ex){
            log.error("merge rename agg_data_swap1 to agg_data_swap exception:{}", ex);
            //不回滚前2步操作
          }

          auditLog.setMessage(String.format("merge rename agg_data and agg_data_swap success"));
        } else {
          auditLog.setMessage(String.format("merge rename agg_data and agg_data_swap fail"));
        }
        auditLog.log();
        break;
      } catch (SQLException e) {
        log.error("merge rename exception:{}", e);
        if (retryCount < 2) {
          Uninterruptibles.sleepUninterruptibly(120, TimeUnit.SECONDS);
        } else {
          bRet = false;
        }
      }
    }
    //检测agg_data是否存在，如果不存在需要一直尝试回滚100次
    boolean bRetry;
    String aggUUIDNowName = getTableNameByUUID(clickhouseNodeInfo, aggUUID);
    String aggUUIDNow = getTableUUID(clickhouseNodeInfo, "agg_data");
    if (StringUtils.isEmpty(aggUUIDNow)) {
      int i = 0;
      do {
        try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
          connection.executeUpdate(String.format("rename table %s.%s to %s.agg_data on cluster '%s'", clickhouseNodeInfo.getDatabase(), aggUUIDNowName, clickhouseNodeInfo.getDatabase(), clickhouseNodeInfo.getCluster()));
          bRetry = true;
        } catch (Exception ex) {
          bRetry = false;
          i++;
          log.error("merge rename retry {} to agg_data exception:{}", aggUUIDNowName, ex);
          Uninterruptibles.sleepUninterruptibly(120, TimeUnit.SECONDS);
        }
      }
      while (!bRetry && i < 100);
    }

    return bRet;
  }

  private boolean movePartitionAggData(ClickhouseNodeInfo clickhouseNodeInfo, String partition) {
    if(StringUtils.isEmpty(partition))
      return false;

    if(!tablePropertyCheck(clickhouseNodeInfo, "agg_data", "sys_modified_time", "toYYYYMMDD(sys_modified_time)"))
      return false;

    //输出结果对比，因为merge的时候会删除空维度，因此两个结果可能不一致
    newMergeResultCheck(clickhouseNodeInfo, partition);

    for(int retryCount = 0; retryCount<3; retryCount++) {
      try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
        String moveSql = String.format("alter table %s.agg_data on cluster '{cluster}' move partition %s to table %s.agg_data_history; ",
                clickhouseNodeInfo.getDatabase(), partition, clickhouseNodeInfo.getDatabase());

        BizAuditLog auditLog = new BizAuditLog();
        auditLog.setModule("STAT_CHART_DATA_MERGE");
        auditLog.setStartTime(System.currentTimeMillis());
        auditLog.setFrom(clickhouseNodeInfo.getDatabase());
        auditLog.setFromId("merge_task_move_partition");
        if (this.shouldExchangeTable(clickhouseNodeInfo, true)) {
          connection.executeUpdate(moveSql);
          auditLog.setMessage("merge move partition from agg_data to agg_data_history success");
        } else {
          auditLog.setMessage("merge move partition from agg_data to agg_data_history fail");
        }
        auditLog.setExtra(moveSql);
        auditLog.log();
        break;
      }
      catch (SQLException e) {
        log.error("merge move partition exception:{}", e);
        BizAuditLog auditLog = new BizAuditLog();
        auditLog.setModule("STAT_CHART_DATA_MERGE");
        auditLog.setStartTime(System.currentTimeMillis());
        auditLog.setFrom(clickhouseNodeInfo.getDatabase());
        auditLog.setFromId("merge_task_move_partition");
        auditLog.setMessage(String.format("merge move partition from agg_data to agg_data_history"));
        auditLog.setError(String.format("merge move partition exception: %s", e));
        auditLog.log();
        if(retryCount < 2){
          Uninterruptibles.sleepUninterruptibly(120, TimeUnit.SECONDS);
        }
        else{
          return false;
        }
      }
    }
    return true;
  }

  private boolean dropPartitionAggDataSwap(ClickhouseNodeInfo clickhouseNodeInfo) {
    for(int retryCount = 0; retryCount<3; retryCount++) {
      try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
        //删除七天前merge的分区
        String querySql = String.format("select partition from system.parts where table='agg_data_history' and database='%s' group by partition having toDate(max(max_time)) < now() - INTERVAL %d DAY order by partition;",
                clickhouseNodeInfo.getDatabase(), dropPartitionBefore);

        List<String> sqlList = Lists.newArrayList();
        connection.query(querySql, rs -> {
          while (rs.next()) {
            sqlList.add(String.format("alter table %s.agg_data_history on cluster '{cluster}' drop partition %s settings max_partition_size_to_drop=0; ",
                    clickhouseNodeInfo.getDatabase(), rs.getString(1)));
          }
        });

        for(String sql : sqlList){
          connection.executeUpdate(sql);
          BizAuditLog auditLog = new BizAuditLog();
          auditLog.setModule("STAT_CHART_DATA_MERGE");
          auditLog.setStartTime(System.currentTimeMillis());
          auditLog.setFrom(clickhouseNodeInfo.getDatabase());
          auditLog.setFromId("merge_task_drop_partition");
          auditLog.setMessage("merge drop partition from agg_data_history");
          auditLog.setExtra(sql);
          auditLog.log();
        }
        break;
      }
      catch (SQLException e) {
        log.error("merge drop partition exception", e);
        BizAuditLog auditLog = new BizAuditLog();
        auditLog.setModule("STAT_CHART_DATA_MERGE");
        auditLog.setStartTime(System.currentTimeMillis());
        auditLog.setFrom(clickhouseNodeInfo.getDatabase());
        auditLog.setFromId("merge_task_drop_partition");
        auditLog.setMessage(String.format("merge drop partition from agg_data_history"));
        auditLog.setError(String.format("merge drop partition exception: %s", e));
        auditLog.log();
        if(retryCount < 2){
          Uninterruptibles.sleepUninterruptibly(120, TimeUnit.SECONDS);
        }
        else{
          return false;
        }
      }
    }
    return true;
  }

  /**
   * 检查当前 db_sync_info 状态来判断是否可以exchange table
   * @param clickhouseNodeInfo clickhouseNodeInfo
   * @return boolean
   */
  public boolean shouldExchangeTable(ClickhouseNodeInfo clickhouseNodeInfo, boolean bCheckTime){
    if(bCheckTime){
      long minuteOfDay = LocalTime.now().getLong(ChronoField.MINUTE_OF_DAY);
      if (minuteOfDay < this.mergeBeginTime || minuteOfDay > this.mergeEndTime){
        return false;
      }
    }
    List<DBMergeInfoDO> dbSyncInfos = aggMergeDao.queryDBMergeInfoByChUrl(clickhouseNodeInfo.getJdbcUrl());
    if (CollectionUtils.isEmpty(dbSyncInfos)) {
      log.warn("queryDBMergeInfoByChUrl dbSyncInfos is empty chDB:{}", clickhouseNodeInfo.getJdbcUrl());
      return true;
    }
    Optional<Integer> statusOP = dbSyncInfos.stream()
                                            .map(DBMergeInfoDO::getStatus)
                                            .filter(status -> !Objects.equals(status, SyncStatusEnum.EXCHANGE_AGG.getStatus()))
                                            .findFirst();
    return statusOP.isEmpty();
  }

  /**
   * 创建agg_data_swap表
   *
   * @param clickhouseNodeInfo
   */
  public void dropAggSwapTable(ClickhouseNodeInfo clickhouseNodeInfo) {
    try (JdbcConnection connection = chDataSource.getJdbcConnection(clickhouseNodeInfo.getJdbcUrl(), 6000000L)) {
      connection.executeUpdate(String.format("drop table agg_data_swap on cluster '%s'",
        clickhouseNodeInfo.getCluster()));
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }
}
