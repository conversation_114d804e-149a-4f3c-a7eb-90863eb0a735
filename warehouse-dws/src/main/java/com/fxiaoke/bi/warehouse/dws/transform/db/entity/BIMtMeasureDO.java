package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import com.fxiaoke.bi.warehouse.dws.transform.model.BiMtMeasureRule;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "bi_mt_measure")
@Data
public class BIMtMeasureDO {
  @Column(name = "measure_id")
  @Id
  private String measureId;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "name")
  private String name;
  @Column(name = "topology_describe_id")
  private String topologyDescribeId;
  @Column(name = "agg_expression")
  private String aggExpression;
  @Column(name = "agg_type")
  private String aggType;
  @Column(name = "field_location")
  private String fieldLocation;
  @Column(name = "filters")
  private String filters;
  @Column(name = "status")
  private Integer status;
  @Column(name = "disable_reason")
  private String disableReason;
  @Column(name = "description")
  private String description;
  @Column(name = "last_modified_time")
  private long lastModifiedTime;
  @Column(name = "is_deleted")
  private boolean isDeleted;
  @Column(name = "syslog")
  private String syslog;
  @Column(name = "node_id")
  private String nodeId;
  @Column(name = "action_date_field")
  private String actionDateField;

  public BiMtMeasureRule createDimMeasureRule() {
    return BiMtMeasureRule.builder()
                          .tenantId(tenantId)
                          .fieldLocation(fieldLocation)
                          .measureId(measureId)
                          .name(name)
                          .topologyDescribeId(topologyDescribeId)
                          .aggType(aggType)
                          .description(description)
                          .syslog(syslog)
                          .nodeId(nodeId)
                          .actionDateField(actionDateField)
                          .status(status)
                          .isDeleted(isDeleted)
                          .lastModifiedTime(lastModifiedTime)
                          .filters(filters)
                          .aggExpression(aggExpression)
                          .build();
  }
}
