package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.common.db.er.ColumnType;
import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.TopologyUtils;
import com.fxiaoke.bi.warehouse.dws.model.DimConfig;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DimRule {
  private DimFieldRule dimFieldRule;

  public boolean isMultipleDim() {
    return dimFieldRule != null && !dimFieldRule.isSingle;
  }

  public DisplayField.DisplayType findDisplayType() {
    if (dimFieldRule != null) {
      return dimFieldRule.displayType;
    }
    return null;
  }

  /**
   * 生成 dimconfig 供生成sql使用
   *
   * @param chAggDataFixSlot
   * @param dimFieldAliasMapper
   * @param dimsMapper
   * @return
   */
  public DimConfig createDimConfig(Set<String> chAggDataFixSlot,
                                   Map<String, String/*dim字段别名*/> dimFieldAliasMapper,
                                   Map<String, AtomicInteger> dimsMapper) {
    String columnName = dimFieldRule.column;
    String alias = dimFieldRule.alias;
    PGColumnType columnType = dimFieldRule.columnType;
    String chType = columnType.trans2CHType().name();
    String distTypeName = columnType.trans2CHType().name();
    String dstDimColumnName;
    if (StringUtils.isNotBlank(dimFieldRule.dstColumnName)) {
      dstDimColumnName = dimFieldRule.dstColumnName;
    } else if (StringUtils.isNotBlank(dimFieldRule.customType)) {
      String dimensionId = dimFieldRule.dimensionId;
      String itemTypeAlias = columnType.findItemTypeAlias();
      String newChType;
      String aggColumnPrefix;
      if ("select_many".equals(dimFieldRule.fieldType)) {
        newChType = itemTypeAlias;
        aggColumnPrefix = "dim_%s";
      } else {
        newChType = ColumnType._String.name();
        aggColumnPrefix = "dim%s";
      }
      distTypeName = ColumnType._CustomDim.name();
      String dimAlias = TopologyUtils.createDimAlias(dimsMapper, newChType, aggColumnPrefix.formatted(newChType.toLowerCase()));
      dstDimColumnName = dimFieldAliasMapper.computeIfAbsent(dimensionId, key -> dimAlias);
    } else {
      String fieldId = dimFieldRule.fieldId;
      ColumnType distType = columnType.createDimDistType(dimFieldRule.fieldType, dimFieldRule.isSingle);
      switch (distType) {
        case _ARRAY: {
          String itemType = columnType.findItemTypeAlias();
          dstDimColumnName = dimFieldAliasMapper.computeIfAbsent(fieldId, key -> TopologyUtils.createDimAlias(dimsMapper, itemType, "dim_%s".formatted(itemType)));
          distTypeName = ColumnType._ARRAY.name();
          break;
        }
        case _ActionDate: {
          String newChType = ColumnType._String.name();
          distTypeName = ColumnType._ActionDate.name();
          dstDimColumnName = dimFieldAliasMapper.computeIfAbsent(fieldId, key -> TopologyUtils.createDimAlias(dimsMapper, newChType, "dim%s".formatted(newChType.toLowerCase())));
          break;
        }
        case _String: {
          if (Objects.equals(chType, ColumnType._Boolean.name())) {
            String newChType = ColumnType._String.name();
            dstDimColumnName = dimFieldAliasMapper.computeIfAbsent(fieldId, key -> TopologyUtils.createDimAlias(dimsMapper, newChType, "dim%s".formatted(newChType.toLowerCase())));
            distTypeName = ColumnType._String.name();
            break;
          }
        }
        default: {
          dstDimColumnName = dimFieldAliasMapper.computeIfAbsent(fieldId, key -> TopologyUtils.createDimAlias(dimsMapper, chType, "dim%s".formatted(chType.toLowerCase())));
          break;
        }
      }
    }
    DimConfig dimConfig = new DimConfig();
    dimConfig.setTableName(dimFieldRule.tableName);
    dimConfig.setTableAlias(alias);
    dimConfig.setColumnName(columnName);
    dimConfig.setDstColumnName(dstDimColumnName);
    dimConfig.setDstColumnType(ColumnType.valueOf(distTypeName));
    dimConfig.setFieldType(dimFieldRule.fieldType);
    return dimConfig;
  }

  /**
   * 生成 dimconfig 供生成sql使用 列-新别名版
   *
   * @param suffixNum 后缀编号
   * @return
   */
  public DimConfig createNewDimConfig(AtomicInteger suffixNum) {
    String columnName = dimFieldRule.column;
    String alias = dimFieldRule.alias;
    PGColumnType columnType = dimFieldRule.columnType;
    String distTypeName = columnType.trans2CHType().name();
    String dstDimColumnName = StringUtils.isNotBlank(dimFieldRule.dstColumnName) ? dimFieldRule.dstColumnName :
            String.format("%s_%s_%d",alias, columnName, suffixNum.getAndIncrement());
    ColumnType distType = columnType.createDimDistType(dimFieldRule.fieldType, dimFieldRule.isSingle);
    switch (distType) {
      case _ARRAY: {
        distTypeName = ColumnType._ARRAY.name();
        break;
      }
      case _ActionDate: {
        distTypeName = ColumnType._ActionDate.name();
        break;
      }
      case _String: {
        if (StringUtils.equalsAny(distTypeName, ColumnType._Boolean.name(), ColumnType._int.name())) {
          distTypeName=ColumnType._String.name();
        }
        break;
      }
      case _Decimal: {
        distTypeName = ColumnType._Decimal.name();
        break;
      }
    }
    if (GrayManager.isAllowByRule("use_dim_rule_combined_eis", dimFieldRule.tenantId) && dimFieldRule.fieldId.endsWith(Constants._TS)) {
      distTypeName = ColumnType._int.name();
    }
    DimConfig dimConfig = new DimConfig();
    dimConfig.setTableAlias(alias);
    dimConfig.setTableName(dimFieldRule.tableName);
    dimConfig.setColumnName(columnName);
    dimConfig.setDstColumnName(dstDimColumnName);
    dimConfig.setDstColumnType(ColumnType.valueOf(distTypeName));
    dimConfig.setFieldType(dimFieldRule.fieldType);
    return dimConfig;
  }
}
