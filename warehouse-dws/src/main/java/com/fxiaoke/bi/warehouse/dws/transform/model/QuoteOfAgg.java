package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import lombok.experimental.SuperBuilder;

/**
 * value对象的查找关联
 */
@SuperBuilder
public class QuoteOfAgg<T> {
  /**
   * 查找关系
   */
  public final JoinRelation joinRelation;
  /**
   * 关联属性
   */
  public String column;
  /**
   * 被引用列的数据库列类型
   */
  public PGColumnType columnType;
  /**
   * 被引用列的元数据字段类型
   */
  public final String fieldType;
  /**
   * 是否多值
   */
  public final boolean isSingle;
  /**
   * 是否是唯一值
   */
  public final boolean isUnique;
  /**
   * source 表名称
   */
  public String tableName;
  /**
   * source table别名
   */
  public String alias;
  /**
   * 对象api name
   */
  public String describeApiName;
  /**
   * topology graph 顶点id
   */
  public String nodeId;
  /**
   * 日期格式化
   */
  public String formatStr;
  /**
   * 是否开启多语
   */
  public boolean enableMultiLang;
  /**
   * 是否是引用字段
   */
  public String originalType;
  /**
   * 初始化
   */
  public void init(T aggRule) {

  }
}
