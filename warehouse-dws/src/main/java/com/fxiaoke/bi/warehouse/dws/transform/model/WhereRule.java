package com.fxiaoke.bi.warehouse.dws.transform.model;


import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.helper.StringHelper;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;

@SuperBuilder
public class WhereRule extends QuoteOfAgg<AggRule> {

  @Getter
  public int id;
  @Getter
  private final FilterType filterType;
  @Getter
  private final PGFilterType pgFilterType;
  @Getter
  private final String value1;
  @Getter
  private final String value2;
  @Getter
  private final String uiType;

  public String whereSQL(NodeTable whereNodeTable,String table, String tableAlias, String timeZone) {
    //兼容what_list类型的where
    String columnN = column;
    if ("active_record".equals(table)) {
      if ("related_object".equals(columnN)) {
        table = "feed_relation";
        tableAlias = "object_id_table";
        columnN = "target_data_id";
      }
    }
    String columnName = columnN;
    if ("value0".equals(columnName)) {
      if (table.endsWith("__c")) {
        columnName = "id";
      }
    }
    String col = columnName;//StringHelper.containUpperChar(columnName) ? "\"" + columnName + "\"" : columnName;
    whereNodeTable.appendColumns(col);
    String columnAlias = StringUtils.isNoneBlank(tableAlias) ? tableAlias + "." + col : col;
    return filterType.buildBoolSQL(table, col, columnAlias, columnType, fieldType, isSingle, value1, value2, uiType, timeZone);
  }

  public String whereSQLPG(NodeTable whereNodeTable,String table, String tableAlias, String timeZone) {
    //兼容what_list类型的where
    String columnN = column;
    if ("active_record".equals(table)) {
      if ("related_object".equals(columnN)) {
        table = "feed_relation";
        tableAlias = "object_id_table";
        columnN = "target_data_id";
      }
    }
    String columnName = columnN;
    if ("value0".equals(columnName)) {
      if (table.endsWith("__c")) {
        columnName = "id";
      }
    }
    String col = StringHelper.containUpperChar(columnName) ? "\"" + columnName + "\"" : columnName;
    whereNodeTable.appendColumns(col);
    String columnAlias = StringUtils.isNoneBlank(tableAlias) ? tableAlias + "." + col : col;
//    String columnAlias = tableAlias + "." + col;
    return pgFilterType.buildBoolSQL(table, col, columnAlias, columnType, fieldType, isSingle, value1, value2, uiType, timeZone);
  }


  /**
   * select field sql
   *
   * @param table
   * @param tableAlias
   * @return
   */
  public String joinOnSQL(String table, String tableAlias) {
    String columnName = joinRelation.column;
    if (StringHelper.containUpperChar(columnName)) {
      columnName = "\"" + columnName + "\"";
    }
    //员工部门数组
    if (Utils.isSlotColumn(joinRelation.column)) {
      if (FieldType.EMP_DEPT.contains(joinRelation.fieldType)) {
        return " any(string_to_array(NULLIF(replace(replace(trim(" + tableAlias + "." + columnName +
          ", '{[]}'), '|', ','), '\"', ''), ''), ','))";
      }
    }
    if ("approval_task".equals(table)) {
      if ("candidate_ids".equals(columnName) || "dealed_persons".equals(columnName)) {
        return " any(" + tableAlias + "." + columnName + ") ";
      }
    }
    //处理数组
    if (joinRelation.columnType == PGColumnType.ARRAY_String) {
      return " any(" + tableAlias + "." + columnName + ") ";
    }
    //处理字符串
    return tableAlias + "." + columnName;
  }

  @Override
  public void init(AggRule aggRule) {
    //纠正一些特殊配置
    fixRule(aggRule);
    //归位到AggApiOpLogListener
    if (aggRule.whereApiIsAggApi(this)) {
      return;
    }
    //归位到ObjectIdApiOpLogListener
    if (joinRelation.equals(aggRule.objectIdRule.joinRelation)) {
      return;
    }
    //归位到TimeApiOpLogListener
    if (joinRelation.equals(aggRule.timeRule.joinRelation)) {
      return;
    }
    //归位到ValueApiOpLogListener
    if (joinRelation.equals(aggRule.valueRule.joinRelation)) {
      return;
    }
    //归位到排位靠前的WhereApiOpLogListener
    boolean mathOtherWhere = false;
    bb:
    for (ArrayList<WhereRule> wl : aggRule.whereRules.getWhereRulesList()) {
      for (WhereRule w : wl) {
        if (w == this) {
          break bb;
        }
        if (joinRelation.equals(w.joinRelation)) {
          mathOtherWhere = true;
          break bb;
        }
      }
    }
  }

  private void fixRule(AggRule aggRule) {
    if ("timeout_time".equals(column)) {
      if ("approval_task".equals(aggRule.whereApiName(this))) {
        this.columnType = PGColumnType.String;
      }
    }
  }
}
