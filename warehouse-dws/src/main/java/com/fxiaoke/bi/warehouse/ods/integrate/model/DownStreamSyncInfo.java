package com.fxiaoke.bi.warehouse.ods.integrate.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 下游图表同步相关信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DownStreamSyncInfo {

    /**
     * 图表id
     */
    private String viewId;

    /**
     * 统计图版本
     */
    private int version;

    /**
     * 图合并的key
     */
    private String stat_view_unique_key;

    /**
     * 下游图表待同步的指标槽位信息
     */
    private List<String> fieldLocaltionList;

    /**
     * 下游图表fieldToFieldLocation
     */
    private Map<String, String> fieldIdToFieldLocaltionMap;

    /**
     * 是否进行图合并,0不是图合并，1是图合并
     */
    private int isStatMerge;
}
