package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtMeasureDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BIMtMeasureMapper extends IBatchMapper<BIMtMeasureDO>, ITenant<BIMtMeasureMapper> {
  @Select("select * from bi_mt_measure where tenant_id=#{tenant_id} and topology_describe_id=#{topology_describe_id}")
  List<BIMtMeasureDO> findBIMtMeasures(@Param("tenant_id") String tenantId, @Param("topology_describe_id") String topologyDescribeId);

  @Select("select * from bi_mt_measure where tenant_id=#{tenant_id} and topology_describe_id=#{topology_describe_id} and " +
  " measure_id in (select unnest(check_measure_fields) from goal_rule where tenant_id = #{tenant_id} and id = #{topology_describe_id})")
  List<BIMtMeasureDO> findBIMtMeasuresByGoal(@Param("tenant_id") String tenantId, @Param("topology_describe_id") String topologyDescribeId);
}
