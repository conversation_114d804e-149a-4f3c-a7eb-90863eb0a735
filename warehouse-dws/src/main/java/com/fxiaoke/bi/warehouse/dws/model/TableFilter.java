package com.fxiaoke.bi.warehouse.dws.model;


import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.apache.commons.collections.CollectionUtils;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor  
public class TableFilter {
 private String tableAlias;
 private List<Pair<DimConfig,StatViewFilter>> dimFieldFilterList;
 private List<Pair<ActionDateConfig,StatViewFilter>> actionDateFilters;

 public void addDimFieldFilter(DimConfig dimConfig,StatViewFilter filter){
  if(dimFieldFilterList==null){
    dimFieldFilterList = Lists.newArrayList();
  }
  dimFieldFilterList.add(Pair.build(dimConfig,filter));
 }

 public void addActionDateFilter(ActionDateConfig actionDateConfig,StatViewFilter filter){
  if(actionDateFilters==null){
   actionDateFilters = Lists.newArrayList();
  }
  actionDateFilters.add(Pair.build(actionDateConfig,filter));
 }

 public void batchAddActionDateFilter(List<Pair<ActionDateConfig,StatViewFilter>> others){
  if(actionDateFilters==null){
   actionDateFilters = Lists.newArrayList();
  }
  actionDateFilters.addAll(others);
 }


 public Set<String> getPushDownFields(){
  Set<String> pushDownFields = Sets.newHashSet();
  if(CollectionUtils.isNotEmpty(this.dimFieldFilterList)){
    this.dimFieldFilterList.forEach(pair->pushDownFields.add(pair.second.getFieldId()));
  }
  if(this.actionDateFilters!=null){
   this.actionDateFilters.forEach(pair->pushDownFields.add(pair.second.getFieldId()));
  }
  return pushDownFields;
 }
 
}
