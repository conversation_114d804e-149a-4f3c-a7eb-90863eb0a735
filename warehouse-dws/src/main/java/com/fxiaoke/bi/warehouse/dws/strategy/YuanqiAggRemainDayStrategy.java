package com.fxiaoke.bi.warehouse.dws.strategy;

import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 元气特殊
 * <AUTHOR>
 * @since 2022/7/7
 */
@Component
public class YuanqiAggRemainDayStrategy implements AggRemainDayStrategy {

//  @Resource
//  private DataMapper dataMapper;

  private long updated = 0;
  private long interval = 10L * 60L * 1000L;

  private String minaActionDate;

//  @PostConstruct 根据产品的需要暂时停用给元气的动态获取日期直接写死31天在配置中心
  public void init() {
    AggRemainStrategyFactory.register("735454^checkins_data", this);
  }

  /**
   *  只给元气森林 跑数据因此暂时不用时区
   * @param tenantId
   * @param themeApiName
   * @param timeZone
   * @return
   */
  @Override
  public String minActionDate(String tenantId, String themeApiName,String timeZone) {
    if (updated + interval < System.currentTimeMillis()) {
      refresh();
    }
    return minaActionDate;
  }

  public void refresh() {
    //@Select("select min(value1) from fmcg_visit_route where tenant_id='735454' and is_deleted=0")
//    Long stamp = dataMapper.setTenantId("735454").findYuanqiMinDate();
//    if (stamp == null) {
//      minaActionDate = Constants.ACTION_DATE_EMPTY;
//    } else {
//      minaActionDate = Utils.formatActionDateTz(new Date(stamp),null);
//    }
//    updated = System.currentTimeMillis();
  }
}
