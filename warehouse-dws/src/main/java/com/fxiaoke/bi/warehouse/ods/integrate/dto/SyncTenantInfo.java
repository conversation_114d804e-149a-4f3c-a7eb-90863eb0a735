package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import com.facishare.paas.pod.dto.RouterInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业同步信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncTenantInfo {

    /**
     * 企业tenant_id
     */
    private String tenantId;

    /**
     * 企业object_id
     */
    private String objectId;

    /**
     * 企业名称
     */
    private String tenantName;

    /**
     * 目标企业的ch路由信息
     */
    private RouterInfo targetTenantRouterInfo;

    /**
     * 数据源企业ch路由信息
     */
    private RouterInfo sourceTenantRouteInfo;

    /**
     * 需要同步的指标图表
     */
    private AggMappingRule aggMappingRule;

    public static SyncTenantInfo getSyncTenantInfo(String tenantId, String tenantName, RouterInfo sourceTenantRouteInfo, RouterInfo targetTenantRouterInfo) {
        return SyncTenantInfo.builder()
                             .tenantId(tenantId)
                             .tenantName(tenantName)
                             .sourceTenantRouteInfo(sourceTenantRouteInfo)
                             .targetTenantRouterInfo(targetTenantRouterInfo)
                             .build();
    }
}
