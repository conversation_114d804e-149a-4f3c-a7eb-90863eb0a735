package com.fxiaoke.bi.warehouse.ods.compare.impl;

import com.fxiaoke.bi.warehouse.ods.compare.ComparePgToChService;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ColumnInfo;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChArg;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChColumnResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChCountResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChDelBizData;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChSampleArg;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChTableResult;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ComparePgToChTriggerResult;
import com.fxiaoke.bi.warehouse.ods.compare.dao.ClickhouseDataDao;
import com.fxiaoke.bi.warehouse.ods.compare.dao.PostgresqlDataDao;
import com.fxiaoke.bi.warehouse.ods.compare.dto.DbEnum;
import com.fxiaoke.bi.warehouse.ods.compare.util.CompareUtil;
import com.fxiaoke.bi.warehouse.ods.compare.util.SqlUtil;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.service.PGMetadataService;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-07-26
 * @desc 比对ch库和pg库差异
 */
@Slf4j
@Service
public class ComparePgToChServiceImpl implements ComparePgToChService {

    @Resource
    private PGMetadataService pgMetadataService;

    @Resource
    private ClickhouseDataDao clickhouseDataDao;

    @Resource
    private PostgresqlDataDao postgresqlDataDao;


    @Override
    public ComparePgToChResult getComparePgToChResult(ComparePgToChArg comparePgToChArg) {
        String pgSchema = comparePgToChArg.getPgSchema();
        String pgDbUrl = comparePgToChArg.getPgJdbcUrl();
        String chDbUrl = comparePgToChArg.getChJdbcUrl();
        DbEnum dbEnum = DbEnum.getDbEnum(pgSchema);
        /** 查询pg库中已存在的表 */
        Set<String> pgTableSet = Sets.newHashSet(pgMetadataService.findAllPgTableExcludeStatTable(new TransferEvent(chDbUrl, pgDbUrl, pgSchema)));
        Set<String> chTableSet = clickhouseDataDao.queryTableListByDb(chDbUrl);
        //chTableSet.addAll(CompareUtil.SYNC_TABLE_WHITES_SET);
        //chTableSet.removeAll(CompareUtil.SYNC_TABLE_BLACK_SET);
        Set<String> commonTableSet = Sets.newHashSet(pgTableSet);
        commonTableSet.retainAll(chTableSet);
        ComparePgToChTableResult comparePgToChTableResult = getComparePgToChTableResult(pgTableSet, chTableSet, commonTableSet);
        ComparePgToChTriggerResult comparePgToChTriggerResult = getComparePgToChTriggerResult(comparePgToChArg);
        List<ComparePgToChCountResult> comparePgToChCountResult = Objects.equals(dbEnum, DbEnum.SCHEMA_ENUM) ? getComparePgToChCountResult(comparePgToChArg, commonTableSet) : Lists.newArrayList();
        List<ComparePgToChColumnResult> comparePgToChColumnResult = getComparePgToChColumnResult(comparePgToChArg, commonTableSet);
        return new ComparePgToChResult(comparePgToChTableResult, comparePgToChTriggerResult, comparePgToChCountResult, comparePgToChColumnResult);
    }

    @Override
    public ComparePgToChTableResult getComparePgToChTableResult(Set<String> pgTableSet, Set<String> chTableSet, Set<String> commonTableSet) {
        Set<String> onlyInPgTableSet = Sets.newHashSet(pgTableSet);
        Set<String> onlyInChTableSet = Sets.newHashSet(chTableSet);
        Set<String> commonElementSet = Sets.newHashSet(commonTableSet);
        onlyInPgTableSet.removeAll(commonElementSet);
        onlyInChTableSet.removeAll(commonElementSet);
        List<String> chDiffTableList = onlyInChTableSet.stream().toList();
        List<String> pgDiffTableList = onlyInPgTableSet.stream().toList();
        return new ComparePgToChTableResult(chDiffTableList, pgDiffTableList);
    }

    @Override
    public ComparePgToChTriggerResult getComparePgToChTriggerResult(ComparePgToChArg comparePgToChArg) {
        String pgSchema = comparePgToChArg.getPgSchema();
        String pgDbUrl = comparePgToChArg.getPgJdbcUrl();
        String chDbUrl = comparePgToChArg.getChJdbcUrl();
        Set<String> neededToSyncTableList = pgMetadataService.findNeededToSyncTables(new TransferEvent(chDbUrl, pgDbUrl, pgSchema));
        List<String> notTriggerPgTableList = postgresqlDataDao.queryNotTriggerPgTableList(pgDbUrl, pgSchema, neededToSyncTableList);
        return new ComparePgToChTriggerResult(neededToSyncTableList.size(), notTriggerPgTableList);
    }

    @Override
    public List<ComparePgToChCountResult> getComparePgToChCountResult(ComparePgToChArg comparePgToChArg, Set<String> commonTableSet) {
        String pgSchema = comparePgToChArg.getPgSchema();
        String pgDbUrl = comparePgToChArg.getPgJdbcUrl();
        String chDbUrl = comparePgToChArg.getChJdbcUrl();
        int threshold = comparePgToChArg.getThreshold();
        List<ComparePgToChCountResult> comparePgToChCountResultList = Lists.newArrayList();
        Map<String, Long> pgTableToRowsMap = postgresqlDataDao.queryTableRowsByDb(pgDbUrl, pgSchema, commonTableSet);
        Map<String, Long> chTableToRowsMap = clickhouseDataDao.queryTableRowsByDb(chDbUrl);
        commonTableSet.forEach(table -> {
            Long pgCount = pgTableToRowsMap.getOrDefault(table, 0L);
            Long chCount = chTableToRowsMap.getOrDefault(table, 0L);
            if (!CompareUtil.isApproximatelyEqual(pgCount, chCount, threshold)) {
                ComparePgToChCountResult comparePgToChCountResult = new ComparePgToChCountResult(table, chCount, pgCount);
                comparePgToChCountResultList.add(comparePgToChCountResult);
            }
        });
        return comparePgToChCountResultList;
    }


    @Override
    public List<ComparePgToChColumnResult> getComparePgToChColumnResult(ComparePgToChArg comparePgToChArg, Set<String> commonTableSet) {
        String pgSchema = comparePgToChArg.getPgSchema();
        String pgDbUrl = comparePgToChArg.getPgJdbcUrl();
        String chDbUrl = comparePgToChArg.getChJdbcUrl();
        List<ComparePgToChColumnResult> comparePgToChCountResultList = Collections.synchronizedList(Lists.newArrayList());
        CountDownLatch latch = new CountDownLatch(commonTableSet.size());
        ExecutorService executorService = Executors.newFixedThreadPool(20);
        commonTableSet.forEach(table -> executorService.execute(() -> {
            try {
                List<ColumnInfo> pgColumnInfoList = postgresqlDataDao.queryTableColumnInfoList(pgDbUrl, pgSchema, table);
                List<ColumnInfo> chColumnInfoList = clickhouseDataDao.queryTableColumnInfoList(chDbUrl, table);
                List<String> pgDiffColumn = CompareUtil.pgGreaterThanCh(pgColumnInfoList, chColumnInfoList);
                List<String> chDiffColumn = CompareUtil.chGreaterThanPg(pgColumnInfoList, chColumnInfoList);
                if (CollectionUtils.isNotEmpty(pgDiffColumn) || CollectionUtils.isNotEmpty(chDiffColumn)) {
                    ComparePgToChColumnResult comparePgToChColumnResult = new ComparePgToChColumnResult(table, chDiffColumn, pgDiffColumn);
                    comparePgToChCountResultList.add(comparePgToChColumnResult);
                }
            } catch (Exception e) {
                log.error("error comparing table: " + table, e);
            } finally {
                latch.countDown();
            }
        }));

        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Main thread interrupted while waiting for tasks to complete", e);
        } finally {
            executorService.shutdown();
        }
        return comparePgToChCountResultList;
    }

    @Override
    public List<ComparePgToChCountResult> getComparePgsToChCountSimpleResult(ComparePgToChSampleArg comparePgToChSampleArg) {
        String chJdbcUrl = comparePgToChSampleArg.getChJdbcUrl();
        String pgJdbcUrl = comparePgToChSampleArg.getPgJdbcUrl();
        String tenantId = comparePgToChSampleArg.getTenantId();
        String schemaName = comparePgToChSampleArg.getPgSchema();
        int threshold = comparePgToChSampleArg.getThreshold();
        List<ComparePgToChCountResult> comparePgToChCountResultList = Lists.newArrayList();
        List<String> simpleTableList = Lists.newArrayList("biz_account", "org_employee_user", "org_dept", "dim_sys_date", "dim_sys_area_gray");
        Map<String, Function<String, String>> chMap = Map.of("biz_account", x -> String.format(SqlUtil.queryChBizAccountByTenantId, x),
                                                          "org_employee_user", x -> String.format(SqlUtil.queryChOrgEmployeeUserByTenantId, x),
                                                          "org_dept", x -> String.format(SqlUtil.queryChOrgDeptByTenantId, x),
                                                          "dim_sys_date", x -> String.format(SqlUtil.queryChDimSysDateByTenantId, x),
                                                          "dim_sys_area_gray", x -> String.format(SqlUtil.queryChDimSysAreaGrayByTenantId, x));

        Map<String, Function<String, String>> pgMap = Map.of("biz_account", x -> String.format(SqlUtil.queryPgBizAccountByTenantId, x),
                                                          "org_employee_user", x -> String.format(SqlUtil.queryPgOrgEmployeeUserByTenantId, x),
                                                          "org_dept", x -> String.format(SqlUtil.queryPgOrgDeptByTenantId, x),
                                                          "dim_sys_date", x -> String.format(SqlUtil.queryPgDimSysDateByTenantId, x),
                                                          "dim_sys_area_gray", x -> String.format(SqlUtil.queryPgDimSysAreaGrayByTenantId, x));

        for (String table : simpleTableList) {
            long pgCount = clickhouseDataDao.querySimpleTableRowsByTenantId(chJdbcUrl, pgMap.get(table), tenantId);
            long chCount = postgresqlDataDao.querySimpleTableRowsByTenantId(pgJdbcUrl, schemaName, chMap.get(table), tenantId);
            ComparePgToChCountResult comparePgToChCountResult = new ComparePgToChCountResult(table, pgCount, chCount);
            comparePgToChCountResultList.add(comparePgToChCountResult);
        }
        return comparePgToChCountResultList;
    }

    @Override
    public ComparePgToChDelBizData getComparePgToChDelBizData(ComparePgToChArg comparePgToChArg, Set<String> commonTableSet,List<String> tenantIdList) {
        String pgSchema = comparePgToChArg.getPgSchema();
        String pgDbUrl = comparePgToChArg.getPgJdbcUrl();
        String chDbUrl = comparePgToChArg.getChJdbcUrl();
        int threshold = comparePgToChArg.getThreshold();
        Map<String/*table*/, Map<String/*apiName*/, Pair<String/*ei*/,Integer/*delCount*/>>> result = Maps.newHashMap();
        for(String tableName : commonTableSet){
        
        }
        return null;
    }
}
