package com.fxiaoke.bi.warehouse.ods.integrate.util;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

public class SynchronizationDataUtil {

    /**
     * agg_data中类型与字段映射map
     */
    public static final Map<String, List<String>> typeToFieldListMap = ImmutableMap.<String, List<String>>builder()
                                                                                   .put("String", Lists.newArrayList("dim_string", "agg_uniq_tag", "tenant_id", "view_id", "object_id", "owner", "create_time", "last_modified_time", "data_auth_code", "out_tenant_id", "out_data_auth_code", "value_slot"))
                                                                                   .put("Array(String)", Lists.newArrayList("dim_array_string"))
                                                                                   .put("UInt64", Lists.newArrayList("bi_sys_batch_id"))
                                                                                   .put("Decimal(38, 20)", Lists.newArrayList("dim_decimal", "agg_sum"))
                                                                                   .put("Bool", Lists.newArrayList("dim_boolean"))
                                                                                   .put("UInt8", Lists.newArrayList("agg_sum_merge", "agg_uniq_merge", "bi_sys_flag", "is_deleted"))
                                                                                   .put("AggregateFunction(uniqExact, Nullable(String))", Lists.newArrayList("agg_uniq"))
                                                                                   .put("Nullable(Int64)", Lists.newArrayList("agg_count", "dim_int"))
                                                                                   .build();

    /**
     * 判断agg_data字段类型
     */
    public static String getFiledTypes(String fieldName) {
        for (Map.Entry<String, List<String>> entry : typeToFieldListMap.entrySet()) {
            if (entry.getValue().stream().anyMatch(fieldName::contains)) {
                return entry.getKey();
            }
        }
        return fieldName;
    }

    /**
     * 根据同步时间戳转化成为日期 1704038400 -> 2024-01-01 00:00:00
     */
    public static String getTimestampFormat(Long timestamp) {
        try {
            Instant instant = Instant.ofEpochMilli(timestamp);
            LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return dateTime.format(formatter);
        } catch (Exception e) {
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return now.format(formatter);
        }
    }
}
