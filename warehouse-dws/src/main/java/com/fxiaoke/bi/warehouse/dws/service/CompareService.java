package com.fxiaoke.bi.warehouse.dws.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyTableDao;
import com.fxiaoke.bi.warehouse.dws.model.CheckDiffArg;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTable;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.CompareDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.GoalRuleDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.StatViewDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.AggDiffDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewDO;
import com.fxiaoke.bi.warehouse.dws.transform.impl.OldStatViewTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.model.StatViewEntity;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: zhaomh
 * @Description: agg_data数据对比
 * @Date: Created in 2023/10/17
 * @Modified By:
 */

@Slf4j
@Service
public class CompareService {

    @Resource
    public CompareDao compareDao;
    @Resource
    public StatViewDao statViewDao;
    @Resource
    public GoalRuleDao goalRuleDao;
    @Resource
    public TopologyTableDao topologyTableDao;
    @Resource
    private PgDataSource pgDataSource;

    public Map<String, Object> compareView(CheckDiffArg checkArg) {
        List<AggDiffDO> result = Lists.newArrayList();
        String tenantId = checkArg.getTenantId();
        String viewId = checkArg.getViewId();
        String argFieldId = checkArg.getFieldId();
        try {
            boolean standalone = pgDataSource.isStandalone(tenantId);
            Integer maxVersion = compareDao.findMaxVersion(tenantId, viewId);
            if (maxVersion == null) {
                return of( 204,  "the view no run agg",  Collections.emptyList());
            }
//            TopologyTable statView = oldStatViewTopologyTransformer.createTopologyTable(tenantId, statViewEntity);
            TopologyTable statView = topologyTableDao.findByTenantIdAndSourceIdWithMergeVersion(tenantId, viewId);
            if (statView == null) {
                return of( 204, "not find the view",  Collections.emptyList());
            }
            List<TopologyTableAggRule> statRuleList = statView.getStatRuleList();
            if (CollectionUtils.isEmpty(statRuleList)) {
                log.warn("statRuleList is empty, tenantId:{}, viewId:{}", tenantId, viewId);
                return of( 204,  "not find the rule of view",  Collections.emptyList());
            }
            List<TopologyTableAggRule> statRuleListCopy = Lists.newArrayList(statRuleList);
            if (StringUtils.isNotBlank(argFieldId)) {
                statRuleListCopy = statRuleList.stream().filter(statRule ->
                        StringUtils.equals(statRule.getFieldId(), argFieldId)).toList();
            }
            if (statRuleListCopy.isEmpty()) {
                log.warn("statRuleList is empty, tenantId:{}, viewId:{}, fieldId:{}", tenantId, viewId, argFieldId);
                return of( 204,  "not find statRule",  Collections.emptyList());
            }
            String actionDateStart = checkArg.getCheckBeginTime() == null ? "00000000" : Utils.formatActionDate(checkArg.getCheckBeginTime());
            String actionDateEnd = checkArg.getCheckEndTime() == null ? "99999999" : Utils.formatActionDate(checkArg.getCheckEndTime());
//            statView.setStartActionDate(checkArg.getCheckBeginTime() == null ? null : actionDateStart);
//            statView.setEndActionDate(checkArg.getCheckEndTime() == null ? null : actionDateEnd);
            //每个规则单独计算
            for (TopologyTableAggRule statRule : statRuleListCopy) {
                statView.setStatRuleList(Lists.newArrayList(statRule));
                String fieldId = statRule.getFieldId();
                String diffSQL = compareDao.buildDiffSQL(statView, fieldId, actionDateStart, actionDateEnd, 1000, standalone);
                log.info("tenantId:{}, fieldId:{}, diffSQL:{}", tenantId, fieldId, diffSQL);
                List<AggDiffDO> aggDiffDOS = compareDao.executeQuerySQL(tenantId, diffSQL);
                if (aggDiffDOS.isEmpty()) {
                    String fieldLocation = MapUtils.isNotEmpty(statView.getStatFieldLocation()) ?
                      statView.getStatFieldLocation().getOrDefault(fieldId,"") : "";
                    aggDiffDOS.add(AggDiffDO.builder()
                            .tenantId(tenantId)
                            .viewId(viewId)
                            .fieldId(fieldId)
                            .hashCode("")
                            .objectId("ALL")
                            .actionDate("ALL")
                            .commonDim("")
                            .aggQuota("0")
                            .checkQuota("0")
                            .fieldLocation(fieldLocation)
                            .diffType("normal")
                            .build());
                }
                result.addAll(aggDiffDOS);
                if (result.size() > 1000) {
                    break;
                }
            }
            return of( 200,  "成功",  result);
        } catch (RuntimeException e) {
            log.error("该图对比计算异常, tenantId:{}, viewId:{}, fieldId:{} ", tenantId, viewId, argFieldId, e);
            return of( 500,  "compare error",  result);
        }
    }

    private static Map<String, Object> of(int errCode, String errMessage, Object result) {
        return Map.of("errCode", errCode, "errMessage", errMessage, "result", result);
    }

    /**
     * 获取指定指标的统计图
     *
     * @param tenantId
     * @param viewId
     * @return
     */
    public StatViewEntity findStatViewByViewId(String tenantId, String viewId) {
        StatViewDO statViewDO = statViewDao.queryStatView(tenantId, viewId);
        if (statViewDO == null) {
            log.warn("can not find statView tenantId:{},viewId:{}", tenantId, viewId);
            return null;
        }
        StatViewEntity statViewEntity = statViewDao.findStatViewById(tenantId, statViewDO);
        if (statViewEntity == null) {
            log.warn("StatViewEntity is empty,  tenantId:{}, viewId:{}", tenantId, viewId);
            return null;
        }
        return statViewEntity;
    }

    /**
     * 自动化扫图接口
     * @param checkArg
     * @return
     */
    public Map<String, Object> compareViewByTenantIds(CheckDiffArg checkArg) {
        try {
            List<String> tenantIds = checkArg.getTenantIds();
            List<String> schemaIds = checkArg.getSchemaIds();
            List<String> viewIdsArg = checkArg.getViewIds();
            int sourceType = checkArg.getSourceType() == null ? 0 : checkArg.getSourceType();
            String fromDateTime = StringUtils.isBlank(checkArg.getDateTimeFrom()) ? "1970-01-01 00:00:00" : checkArg.getDateTimeFrom();
            String actionDateStart = checkArg.getCheckBeginTime() == null ? "00000000" : Utils.formatActionDate(checkArg.getCheckBeginTime());
            String actionDateEnd = checkArg.getCheckEndTime() == null ? "99999999" : Utils.formatActionDate(checkArg.getCheckEndTime());
            Map<String, List<String>> tenantIdViewIds = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(checkArg.getCommonViewIds()) && CollectionUtils.isNotEmpty(tenantIds)) {
                tenantIds.forEach(tenantId -> tenantIdViewIds.put(tenantId, checkArg.getCommonViewIds()));
            } else if (CollectionUtils.isNotEmpty(viewIdsArg)) {
                viewIdsArg.forEach(viewIdStr -> {
                    if (viewIdStr.contains(":")) {
                        String tenantId = viewIdStr.split(":")[0];
                        List<String> viewIdList = Lists.newArrayList(viewIdStr.split(":")[1].split(","));
                        if (CollectionUtils.isEmpty(viewIdList)) {
                            return;
                        }
                        tenantIdViewIds.put(tenantId, viewIdList);
                    }
                });
            } else if (CollectionUtils.isNotEmpty(schemaIds)) {
                schemaIds.forEach(eiSchemaIdStr -> {
                    if (eiSchemaIdStr.contains(":")) {
                        String tenantId = eiSchemaIdStr.split(":")[0];
                        String schemaIdStr = eiSchemaIdStr.split(":")[1];
                        List<String> schemaIdList = Lists.newArrayList(schemaIdStr.split(","));
                        if (CollectionUtils.isEmpty(schemaIdList)) {
                            return;
                        }
                        List<String> viewIds = Lists.newArrayList();
                        if (schemaIdStr.contains("^")) {
                            String schemaId = schemaIdStr.split("\\^")[0];
                            int percent = Integer.parseInt(schemaIdStr.split("\\^")[1]);
                            List<String> allViewIds = this.findStatViewByTenantId(tenantId, Lists.newArrayList(schemaId), fromDateTime, sourceType);
                            viewIds.addAll(Utils.getRandomPercentSub(allViewIds, percent));
                        } else {
                            viewIds.addAll(this.findStatViewByTenantId(tenantId, schemaIdList, fromDateTime, sourceType));
                        }
                        tenantIdViewIds.put(tenantId, viewIds);
                    }
                });
            } else if (CollectionUtils.isNotEmpty(checkArg.getTenantIdsPercent())) {
                checkArg.getTenantIdsPercent()
                        .forEach(tenantIdPer -> {
                    String tenantId = tenantIdPer.split("\\^")[0];
                    int percent = Integer.parseInt(tenantIdPer.split("\\^")[1]);
                    List<String> viewIds = this.findStatViewByTenantId(tenantId, null, fromDateTime, sourceType);
                    tenantIdViewIds.put(tenantId, Utils.getRandomPercentSub(viewIds, percent));
                });
            } else if (CollectionUtils.isNotEmpty(tenantIds)) {
                tenantIds.forEach(tenantId -> {
                    List<String> viewIds = this.findStatViewByTenantId(tenantId, null, fromDateTime, sourceType);
                    tenantIdViewIds.put(tenantId, viewIds);
                });
            }
            List<Map<String, Object>> diffTenantViewIds = Lists.newArrayList();
            if (tenantIdViewIds.isEmpty()) {
                Map<String, Object> diffMap = Map.of("tenantId", "","total", 0,
                  "success", 0, "successList", "",
                  "error", 0, "errorList", "",
                  "fail", 0, "failList", "");
                diffTenantViewIds.add(diffMap);
                return of(500, "tenantId is empty", diffTenantViewIds);
            }
            for (Map.Entry<String, List<String>> entry : tenantIdViewIds.entrySet()) {
                String tenantId = entry.getKey();
                List<String> viewIds = entry.getValue();
                List<String> diffViewIds = Lists.newArrayList(); //数据错误的图
                List<String> errorViewIds = Lists.newArrayList(); //对比sql异常错误的图
                List<String> sameViewIds = Lists.newArrayList(); //正确的图
                boolean standalone = pgDataSource.isStandalone(tenantId);
                for (String viewId : viewIds) {
                    try {
                        String diffViewId = this.findDiffViewId(tenantId, viewId, actionDateStart, actionDateEnd, standalone);
                        if (StringUtils.isNotBlank(diffViewId)) {
                            diffViewIds.add(diffViewId);
                        } else {
                            sameViewIds.add(viewId);
                        }
                    } catch (RuntimeException e) {
                        errorViewIds.add(viewId);
                    }
                }
                Map<String, Object> diffMap = Map.of("tenantId", tenantId, "total", viewIds.size(),
                  "success", sameViewIds.size(), "successList", sameViewIds,
                  "error", diffViewIds.size(), "errorList", diffViewIds,
                  "fail", errorViewIds.size(), "failList", errorViewIds);
                diffTenantViewIds.add(diffMap);
            }
            return of(200, "success", diffTenantViewIds);
        } catch (RuntimeException e) {
            log.warn("对比计算异常, CheckDiffArg:{}", JSON.toJSONString(checkArg), e);
            return of(500, "compare agg error", Collections.emptyList());
        }
    }

    /**
     * 对比，记录有问题的viewId
     * @param
     * @return
     */
    public String findDiffViewId(String tenantId, String viewId, String actionDateStart, String actionDateEnd, boolean standalone) {
//        TopologyTable statView = oldStatViewTopologyTransformer.createTopologyTable(tenantId, statViewEntity);
        TopologyTable statView = topologyTableDao.findByTenantIdAndSourceIdWithMergeVersion(tenantId, viewId);
        if (CollectionUtils.isEmpty(statView.getStatRuleList()) || statView.getStatus() != 1) {
            return null;
        }
//        statView.setStartActionDate("00000000".equals(actionDateStart) ? null : actionDateStart);
//        statView.setEndActionDate("99999999".equals(actionDateEnd) ? null : actionDateEnd);
        List<TopologyTableAggRule> statRuleListCopy = Lists.newArrayList(statView.getStatRuleList());
        for (TopologyTableAggRule statRule : statRuleListCopy) {
            statView.setStatRuleList(Lists.newArrayList(statRule));
            String fieldId = statRule.getFieldId();
            String diffSQL = compareDao.buildDiffSQL(statView, fieldId, actionDateStart, actionDateEnd, 1, standalone);
//            log.info("tenantId:{}, fieldId:{}, diffSQL:{}", tenantId, fieldId, diffSQL);
            List<AggDiffDO> aggDiffDOS = compareDao.executeQuerySQL(tenantId, diffSQL);
            if (!aggDiffDOS.isEmpty()) {
                return viewId;
            }
        }
        return null;
    }

    /**
     * 查图，只查生成过topology的图，并封装统计图信息
     * @param tenantId
     * @return
     */
    public List<String> findStatViewByTenantId(String tenantId,
                                               List<String> schemaIdList,
                                               String fromDateTime,
                                               int sourceType) {
        if (sourceType == 1) {
            List<String> sourceIds = goalRuleDao.findGoalBiMtSourceIdsByEi(tenantId);
            if (sourceIds != null) {
                return sourceIds;
            }
        } else {
            List<StatViewDO> statViewDOList;
            if (CollectionUtils.isEmpty(schemaIdList)) {
                statViewDOList = statViewDao.batchQueryStatViewByEi(tenantId, fromDateTime);
            } else {
                statViewDOList = statViewDao.batchQueryStatViewByEiSchemaId(tenantId, schemaIdList, fromDateTime);
            }
            //只计算在topology_table中正常计算的图
            if (CollectionUtils.isNotEmpty(statViewDOList)) {
                //去除目标主题的图
                List<String> allViewIds = statViewDOList.stream()
                                                        .filter(v -> !Constants.GOAL_VALUE_OBJ_SCHEMA_ID.equals(v.getSchemaId()))
                                                        .map(StatViewDO::getViewId).toList();
                //只计算在topology_table中正常计算的图
                List<String> activeViewIds = compareDao.querySourceIds(tenantId, allViewIds);
                //去除包含目标联合分析的图
                List<String> goalViewIds = statViewDao.queryViewIdsGoalFields(tenantId, activeViewIds);
                activeViewIds.removeAll(goalViewIds);
                return activeViewIds;
            }
        }
        return Lists.newArrayList();
    }

}
