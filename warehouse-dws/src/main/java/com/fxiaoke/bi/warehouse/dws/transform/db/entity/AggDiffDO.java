package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: zhaomh
 * @Description: 对比结果
 * @Date: Created in 2023/10/19
 * @Modified By:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggDiffDO {
    /**
     * 企业ID
     */
    private String tenantId;
    /**
     * 统计图ID
     */
    private String viewId;
    /**
     * fieldID
     */
    private String fieldId;
    /**
     * hash_code
     */
    private String hashCode;
    /**
     * object_id
     */
    private String objectId;
    /**
     * action_date
     */
    private String actionDate;
    /**
     * 维度列集合
     */
    private String commonDim;
    /**
     * 指标列槽位集合
     */
    private String fieldLocation;
    /**
     * agg_data指标列结果集合
     */
    private String aggQuota;
    /**
     * 重算指标列结果集合
     */
    private String checkQuota;
    /**
     * diff类型：
     *  dim_less维度缺少
     *  dim_more维度多余
     *  agg指标结果不对
     *  normal正常
     */
    private String diffType;
}
