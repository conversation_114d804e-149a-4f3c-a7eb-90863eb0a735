package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author:jief
 * @Date:2023/7/10
 */
@Data
@Table(name = "dash_board_view")
public class DashBoardViewDO {

  @Column(name = "dash_board_view_id")
  private String dashBoardViewId;

  @Column(name = "dash_board_id")
  private String dashBoardId;

  @Column(name = "tenant_id")
  private String tenantId;

  @Column(name = "is_predefined")
  private Integer isPredefined;

  @Column(name = "view_id")
  private String viewId;

  @Column(name = "title")
  private String title;

  @Column(name = "view_type")
  private String viewType;

  @Column(name = "coordinate_x")
  private Integer coordinateX;

  @Column(name = "coordinate_y")
  private Integer coordinateY;

  @Column(name = "height")
  private Integer height;

  @Column(name = "width")
  private Integer width;

  @Column(name = "source")
  private Integer source;

  @Column(name = "creator")
  private String creator;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "last_modifier")
  private String lastModifier;

  @Column(name = "last_modified_time")
  private Date lastModifiedTime;

  @Column(name = "is_deleted")
  private Integer isDeleted;
}
