package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author:jief
 * @Date:2023/7/10
 */
@Table(name = "dash_board_global_filter_config")
@Data
public class DashBoardGlobalFilterConfigDO {

  @Id
  @Column(name = "config_id")
  private String configId;

  @Column(name = "tenant_id")
  private String tenantId;

  @Column(name = "dash_board_id")
  private String dashBoardId;

  @Column(name = "date_range_id")
  private String dateRangeId;

  @Column(name = "date_value1")
  private String dateValue1;

  @Column(name = "date_value2")
  private String dateValue2;

  @Column(name = "emp_value")
  private String empValue;

  @Column(name = "is_obs_data_filter")
  private int isObsDataFilter;

  @Column(name = "is_obs_emp_filter")
  private int isObsEmpFilter;

  @Column(name = "creator")
  private String creator;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "last_modifier")
  private String lastModifier;

  @Column(name = "last_modified_time")
  private Date lastModifiedTime;

  @Column(name = "is_deleted")
  private int isDeleted;

  @Column(name = "user_id")
  private String userId;

  @Column(name = "goal_rule_value")
  private String goalRuleValue;

  @Column(name = "sub_goal_rule_value")
  private String subGoalRuleValue;

  @Column(name = "local_date_filter")
  private String localDateFilter;

  @Column(name = "local_emp_filter")
  private String localEmpFilter;

  @Column(name = "filter_type")
  private String filterType;

  /**
   * 驾驶舱配置类型（‘global’：驾驶舱级别配置 ‘personal’：个人级别配置）
   */
  @Column(name = "config_type")
  private String configType;
}
