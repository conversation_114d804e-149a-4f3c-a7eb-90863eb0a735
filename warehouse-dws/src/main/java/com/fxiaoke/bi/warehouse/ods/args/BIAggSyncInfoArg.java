package com.fxiaoke.bi.warehouse.ods.args;

import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import lombok.Data;

/**
 * @Author:jief
 * @Date:2024/4/28
 */
@Data
public class BIAggSyncInfoArg {
  private String id;
  private String tenantId;
  private String downstreamEis;
  private Integer status;
  private Long batchNum;
  private long lastSyncTime;
  private int isDeleted;
  private int version;
  private long createTime;
  private long lastModifiedTime;

  public BIAggSyncInfoDO toBiAggSyncInfoDO() {
    BIAggSyncInfoDO biAggSyncInfoDO = new BIAggSyncInfoDO();
    biAggSyncInfoDO.setId(id);
    biAggSyncInfoDO.setTenantId(tenantId);
    biAggSyncInfoDO.setDownstreamEis(downstreamEis);
    biAggSyncInfoDO.setStatus(status);
    biAggSyncInfoDO.setBatchNum(batchNum);
    biAggSyncInfoDO.setLastSyncTime(lastSyncTime);
    biAggSyncInfoDO.setIsDeleted(isDeleted);
    biAggSyncInfoDO.setVersion(version);
    biAggSyncInfoDO.setCreateTime(createTime);
    biAggSyncInfoDO.setLastModifiedTime(lastModifiedTime);
    return biAggSyncInfoDO;
  }
}
