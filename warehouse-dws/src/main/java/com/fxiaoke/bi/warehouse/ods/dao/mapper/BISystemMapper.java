package com.fxiaoke.bi.warehouse.ods.dao.mapper;

import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.provider.CommonProvider;
import com.fxiaoke.bi.warehouse.core.db.entity.DBMergeInfoDO;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.github.mybatis.mapper.ICrudPaginationMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public interface BISystemMapper extends ICrudPaginationMapper<DBSyncInfo>, ITenant<BISystemMapper> {

  @Update("${sql}")
  int execCommonSQL(@Param("sql") String sql);
  /**
   * upsert 系统库
   *
   * @param list
   * @param primaryKey
   * @param <T>
   * @return
   */
  @InsertProvider(type = CommonProvider.class, method = "batchUpsert")
  <T> int upsertSyncDBInfo(@Param(CommonProvider.FKey) List<T> list,
                           @Param(CommonProvider.SKey) Set<String> primaryKey);

  @Insert("<script>" +
          " insert into db_sync_info (id, ch_db, pg_db, pg_schema, status, batch_num, create_time, last_modified_time, is_deleted, last_sync_time, last_merge_agg_time, last_sync_eis)" +
          " values " +
          " <foreach collection = \"list\" item=\"item\" separator=\",\">" +
          " (#{item.id}, #{item.chDb}, #{item.pgDb}, #{item.pgSchema}, #{item.status}, #{item.batchNum},#{item.createTime},#{item.lastModifiedTime},#{item.isDeleted},#{item.lastSyncTime},#{item.lastMergeAggTime},#{item.lastSyncEis})" +
          " </foreach>" +
          "on conflict (ch_db, pg_db, pg_schema) do update set status = EXCLUDED.status, batch_num=EXCLUDED.batch_num, create_time=EXCLUDED.create_time, last_modified_time=EXCLUDED.last_modified_time, is_deleted=EXCLUDED.is_deleted, last_sync_time=EXCLUDED.last_sync_time,last_merge_agg_time=EXCLUDED.last_merge_agg_time, last_sync_eis=EXCLUDED.last_sync_eis" +
          "</script>")
  int upsertSyncDBInfos(List<DBSyncInfo> dbSyncInfos);

  @Select("select * from db_sync_info where ch_db=#{ch_db} and pg_db=#{pg_db} and pg_schema=#{pg_schema} and " +
    "is_deleted=0")
  DBSyncInfo queryDBSyncInfo(@Param("ch_db") String chDb,
                             @Param("pg_db") String pgDb,
                             @Param("pg_schema") String pgSchema);

  @Select("select * from db_sync_info where id ${idInSql} and is_deleted=0")
  List<DBSyncInfo> queryDBSyncInfoById(@Param("idInSql") String idInSql);

  @Select("select * from db_sync_info where ch_db=#{chDb} and is_deleted=0")
  List<DBSyncInfo> queryDBSyncInfoByChDB(@Param("chDb") String chDb);

  @Update("update db_sync_info set status=#{status},last_merge_agg_time=#{lastMergeAggTime} where ch_db=#{chDb} and is_deleted=0")
  int updateLastMergeAggTime(@Param("chDb") String chDb,@Param("status") int status, @Param("lastMergeAggTime") long lastMergeAggTime);

  @Select("insert into bi_ch_merge_task(ch_url, status, last_merge_time) values(#{chDb}, #{status}, #{lastMergeAggTime}) ON CONFLICT(ch_url) DO UPDATE set status=excluded.status, last_merge_time=excluded.last_merge_time, last_modified_time = round(extract(epoch from now())*1000)  where bi_ch_merge_task.ch_url=#{chDb} and bi_ch_merge_task.status=#{oldStatus} returning status")
  Integer upsertCHMergeTaskStatus(@Param("chDb") String chDb, @Param("status") int status, @Param("oldStatus") int oldStatus, @Param("lastMergeAggTime") long lastMergeAggTime);

  @Select("select concat(last_merge_time, '|',status) from bi_ch_merge_task where ch_url=#{chDb}")
  String getLastMergeTimeAndStatus(@Param("chDb") String chDb);

  @Select("select * from db_table_sync_info where db_sync_id=#{db_sync_id} and is_deleted=0")
  List<DbTableSyncInfoDO> queryDbTableSyncInfos(@Param("db_sync_id") String dbSyncId);

  @Select("select * from db_table_sync_info where db_sync_id=#{db_sync_id} and table_name=#{table_name} and " +
    "is_deleted=0")
  DbTableSyncInfoDO queryDbTableSyncInfo(@Param("db_sync_id") String dbSyncId, @Param("table_name") String tableName);

  @Select("select * from db_table_sync_info where db_sync_id=#{db_sync_id} and table_name=any(array[#{table_name}]) and " +
    "is_deleted=0")
  List<DbTableSyncInfoDO> batchQueryDbTableSyncInfo(@Param("db_sync_id") String dbSyncId, @Param("table_name") String[] tableName);
  /**
   * 重置db同步信息元数据
   * @param chDb
   * @return
   */
  @Update("update db_sync_info set status=-2,batch_num=0,last_sync_eis='' where ch_db=#{chDb} and is_deleted=0")
  int resetCHDbSyncInfo(@Param("chDb") String chDb);

  /**
   * 重置db table同步信息元数据
   * @param dbSyncId db sync id
   * @return
   */
  @Update("update db_table_sync_info set status=0,max_sys_modified_time=0,batch_num=0,api_name_ei_map='' where db_sync_id=#{dbSyncId} and is_deleted=0")
  int resetCHDbTableSyncInfo(@Param("dbSyncId") String dbSyncId);

  /**
   * 重置db table同步最大变更时间
   * @param id 主键
   * @return
   */
  @Update("update db_table_sync_info set max_sys_modified_time=#{maxModifiedTime} where id=#{id} and is_deleted=0")
  int updateCHDbTableMaxModifiedTime(@Param("id") String id,@Param("maxModifiedTime") long maxModifiedTime);

  @Update("update db_table_sync_info set api_name_ei_map=#{apiNameEiMap} where id=#{id} and is_deleted=0")
  int updateCHDbTableApiNameEiMap(@Param("id") String id,@Param("apiNameEiMap") String apiNameEiMap);

  int deleteTableSyncInfoByDbSyncId(@Param("dbSyncIds") String[] dbSyncIds, @Param("tables") String[] tables);

  @Delete("delete from db_sync_info where id=any(array[#{ids}])")
  int deleteDbSyncInfoById(@Param("ids") String[] ids);

  /**
   * 根据chDBURL和status更新merge agg time
   * @param chDb
   * @param lastMergeAggTime
   * @param status
   * @return
   */
  @Update("update db_sync_info set last_merge_agg_time=#{lastMergeAggTime} where ch_db=#{chDb} and status=#{status} and is_deleted=0")
  int updateLastMergeAggTimeByStatus(@Param("chDb") String chDb, @Param("lastMergeAggTime") long lastMergeAggTime,@Param("status") int status);

  /**
   * 更新最近一次1+N开始时间
   * @return
   */
  @Update("update db_sync_info set last_integrate_time=#{lastMergeAggTime} where id=#{id}")
  int updateLastIntegrateTimeById(@Param("id") String id, @Param("lastIntegrateTime") long lastIntegrateTime);

  int updateDbSyncInfoStatus(@Param("id") String id,@Param("status") Integer status,@Param("lastModifiedTime") Long lastModifiedTime,@Param("lastSyncTime") Long lastSyncTime);

  /**
   * 批量更新 db table 同步信息
   * @param dbTableSyncInfos
   * @return
   */
  int batchUpdateDbTableSyncInfo(List<DbTableSyncInfoDO> dbTableSyncInfos);

  /**
   * 更新db sync info
   * @param dbSyncInfo
   * @return
   */
  int updateDbSyncInfo(DBSyncInfo dbSyncInfo);

  @Select("""
    select dsi.id,
           dsi.ch_db,
           dsi.pg_db,
           dsi.pg_schema,
           case when dsi.allow_inc_partition = 1 then dai.status else dsi.status end as status,
           dsi.batch_num,
           dsi.is_deleted,
           dsi.last_merge_agg_time,
           dsi.allow_inc_partition
    from db_sync_info dsi
             left join db_agg_info dai
                       on dsi.id = dai.id and dai.is_deleted=0
    where dsi.ch_db=#{chDB} and dsi.is_deleted=0
    """)
  List<DBMergeInfoDO> queryDbAggMergeInfo(@Param("chDB") String chDB);

  /**
   * 根据chDBURL和status更新merge agg time
   * @param chDb
   * @param lastMergeAggTime
   * @param status
   * @return
   */
  @Update("update db_sync_info set last_merge_agg_time=#{lastMergeAggTime} where ch_db=#{chDb} and status=#{status} and is_deleted=0")
  int updateDbSyncInfoLastMergeTimeByStatus(@Param("chDb") String chDb, @Param("lastMergeAggTime") long lastMergeAggTime,@Param("status") int status);

  /**
   * 根据chDBURL和status更新merge agg time
   * @param chDb
   * @param lastMergeAggTime
   * @param status
   * @return
   */
  @Update("update db_agg_info set last_merge_agg_time=#{lastMergeAggTime} " +
    "where id=any(select id from db_sync_info where ch_db=#{chDb} and is_deleted=0) and status=#{status} and is_deleted=0")
  int updateDbAggInfoLastMergeTimeByStatus(@Param("chDb") String chDb, @Param("lastMergeAggTime") long lastMergeAggTime,@Param("status") int status);


  /**
   * 根据chDBURL批量更新status
   * @param chDb
   * @param status
   * @return
   */
  @Update("update db_sync_info set status=#{status} where ch_db=#{chDb} and status=#{preStatus}")
  int updateDbSyncInfoStatusByCHDB(@Param("chDb") String chDb,@Param("status") int status,@Param("preStatus") int preStatus);

  /**
   * 根据chDBURL 更新 status
   * @param chDb
   * @param status
   * @return
   */
  @Update("update db_agg_info set status=#{status} " +
    "where id=any(select id from db_sync_info where ch_db=#{chDb} and is_deleted=0) and status=#{preStatus}")
  int updateDbAggInfoStatusByCHDB(@Param("chDb") String chDb,@Param("status") int status,@Param("preStatus") int preStatus);

  @Select("select * from db_sync_info where pg_db=#{pg_db} and pg_schema=#{pg_schema} and is_deleted=0 limit 1")
  DBSyncInfo queryDBSyncInfoByPgDbAndPgSchema(@Param("pg_db") String pgDb, @Param("pg_schema") String pgSchema);
}
