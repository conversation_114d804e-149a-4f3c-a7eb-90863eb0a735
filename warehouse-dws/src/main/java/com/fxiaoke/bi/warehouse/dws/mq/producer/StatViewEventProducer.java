package com.fxiaoke.bi.warehouse.dws.mq.producer;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;

/**
 * @Author:jief
 * @Date:2023/5/24
 * 老的 mq 集群的生产者
 */
@Slf4j
@Service
public class StatViewEventProducer {
  @Getter
  private AutoConfMQProducer producer;

  @PostConstruct
  public void init() {
    producer = new AutoConfMQProducer("fs-bi-warehouse", "DWSStatConsumer");
  }

  /**
   * 发送消息
   * @param topic topic
   * @param tag
   * @param keys
   * @param body
   * @param hash
   */
  public void sendMessage(String topic, String tag, String keys, String body, int hash, long delayTimeMS) {
    Message syncEventMsg = new Message(topic, tag, keys, body.getBytes(StandardCharsets.UTF_8));
    try {
      if(delayTimeMS > 0) {
        syncEventMsg.setDelayTimeMs(delayTimeMS);
      }
      this.producer.send(syncEventMsg, (mqs, msg1, arg) -> {
        Integer id = (Integer) arg;
        int index = id % mqs.size();
        return mqs.get(index);
      }, hash);
    } catch (Exception e) {
      log.error("send sendTransferEvent msg fail msg:{}", body, e);
    }
  }

  /**
   * 发送mq message
   * @param messageExt
   * @param hash
   * @param delayTimeMS
   */
  public void sendMessage(MessageExt messageExt, int hash, long delayTimeMS) {
    this.sendMessage(messageExt.getTopic(), messageExt.getTags(), messageExt.getKeys(), new String(messageExt.getBody(), StandardCharsets.UTF_8), hash, delayTimeMS);
  }

  @PreDestroy
  public void destroy() {
    if (producer != null) {
      producer.close();
    }
  }
}
