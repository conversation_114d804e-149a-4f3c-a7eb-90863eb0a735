package com.fxiaoke.bi.warehouse.dws.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.entity.DbSyncInfoFlowDO;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.ClickhouseTenantPolicy;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.DBTableSyncInfoMapper;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.mapper.AggDataSyncInfoMapper;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.BIMtTopologyTableDao;
import com.fxiaoke.common.Pair;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Author:jief
 * @Date:2024/2/26
 */
@Slf4j
@Component
public class DbTableSyncInfoInterfaceImpl implements DbTableSyncInfoInterface {

  @Resource
  private DBTableSyncInfoMapper dbTableSyncInfoMapper;
  @Resource
  private AggDataSyncInfoMapper aggDataSyncInfoMapper;
  @Resource(name = "dbRouterClient")
  private DbRouterClient dbRouterClient;
  @Resource
  private ClickhouseTenantPolicy clickhouseTenantPolicy;
  @Resource(name = "jedisFactory")
  private MergeJedisCmd calcJedisFactory;
  @Resource
  private BIMtTopologyTableDao biMtTopologyTableDao;
  protected static LoadingCache<String, Integer> VIEW_FIELD_COST_CACHE;

  @PostConstruct
  public void init() {
    VIEW_FIELD_COST_CACHE = CacheBuilder.newBuilder()
                                        .maximumSize(10000)
                                        .refreshAfterWrite(60, TimeUnit.MINUTES)
                                        .build(new CacheLoader<>() {
                                          public @NotNull Integer load(@NonNull String arg) {
                                            String[] argArr = arg.split("\\|");
                                            Double cost = aggDataSyncInfoMapper.setTenantId(argArr[0])
                                                                               .queryCostFromBiAggLog(argArr[0], argArr[1], argArr[2], WarehouseConfig.calcFlowStatCostQuantile);
                                            if (cost != null) {
                                              return cost.intValue();
                                            }
                                            return 0;
                                          }
                                        });
  }

  public String getCHJdbcURL(String tenantId) {
    RouterInfo routerInfo = clickhouseTenantPolicy.getRouterInfo(tenantId);
    if (routerInfo != null) {
      return "jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + routerInfo.getDbName();
    }
    return "";
  }

  /**
   * 获取DBURL 主库 和schema
   *
   * @param tenantId 租户id
   */
  private Pair<String, Boolean> getDBURLAndSchema(String tenantId) {
    //获取主库的context 因为db同步是按照主库同步
    RouterInfo routerInfo = this.dbRouterClient.queryRouterInfo(tenantId, "BI", "fs-bi-stat", "postgresql", false, false);
    if (routerInfo != null) {
      boolean isStandalone = routerInfo.getStandalone() != null && routerInfo.getStandalone();
      return Pair.build(routerInfo.getJdbcUrl(), isStandalone);
    }
    return null;
  }

  /**
   * 找到指标所涉及的表的最近计算时间，计算完成只返回更新时间，计算中返回更新时间和延迟时间
   *
   * @param viewId 统计图ID
   * @param fieldId 指标ID
   * @return 最近更新时间
   */
  @Override
  public FieldAggDelayInfo findDetailUpdateTime(String tenantId, String viewId, String fieldId, boolean forceFresh, String timezone) {
    FieldAggDelayInfo aggDelayInfo = new FieldAggDelayInfo();
    Pair<String, Boolean> dbUrlAndSchema = getDBURLAndSchema(tenantId);
    // 无数据库信息 返回空
    if (Objects.isNull(dbUrlAndSchema)) {
      log.error("tenantId:{} viewId:{} findDetailUpdateTime dbUrlAndSchema is null", tenantId, viewId);
      return null;
    }
    String pgJdbcUrl = dbUrlAndSchema.getT1();
    Boolean standalone = dbUrlAndSchema.getT2();
    String pgSchema = standalone ? "sch_" + tenantId : "public";
    String chProxyDbUrl = getCHJdbcURL(tenantId);
    DbSyncAggInfo syncAndAggBatchNum = dbTableSyncInfoMapper.setTenantId("-1")
                                                            .querySyncAndAggBatchNum(chProxyDbUrl, pgJdbcUrl, pgSchema);
    // 无同步和计算信息
    if (Objects.isNull(syncAndAggBatchNum)) {
      throw new RuntimeException("tenantId: %s not have sync info".formatted(tenantId));
    }
    String dbSyncId = syncAndAggBatchNum.getDbSyncId();
    Integer batchNum = syncAndAggBatchNum.getBatchNum();
    Integer currentBatchNum = syncAndAggBatchNum.getCurrentBatchNum();
    //merge期间dbAggInfo中的sync_batch_nums会清空
    if (Objects.isNull(currentBatchNum) || currentBatchNum == 0) {
      currentBatchNum = dbTableSyncInfoMapper.setTenantId(tenantId).queryCurrentBatchNum(dbSyncId);
    }
    TopologyTableDO biMtTopologyTable = biMtTopologyTableDao.queryTopologyTable(tenantId, viewId);
    // 无计算信息
//    if (Objects.isNull(biMtTopologyTable)) {
//      return aggDelayInfo;
//    }
    //统计图计算状态不是走预计算
    timezone = StringUtils.isBlank(timezone) ? biMtTopologyTable.getTimezone() : timezone;
    String updateTime = DateFormatUtils.format(new Date(syncAndAggBatchNum.getLastSyncTime()), Utils.DATE_FORMAT_1, TimeZone.getTimeZone(timezone));
    aggDelayInfo.setUpdateTime(updateTime);
    if (Objects.isNull(biMtTopologyTable) || biMtTopologyTable.getStatus() != 1) {
      return aggDelayInfo;
    }
    //无对应指标信息
    String aggEffectApiNamesStr = Objects.toString(biMtTopologyTable.getAggEffectApiNames(), "");
    if (StringUtils.isBlank(aggEffectApiNamesStr)) {
      return aggDelayInfo;
    }
    Map<String, Set<String>> aggEffectApiNames = biMtTopologyTable.getAggEffectApiNames();
    Set<String> fieldTableList = aggEffectApiNames.get(fieldId);
    //无对应指标信息
    if (CollectionUtils.isEmpty(fieldTableList)) {
      return aggDelayInfo;
    }
    aggDelayInfo.setBatchNum(currentBatchNum);
    if (Objects.equals(batchNum, currentBatchNum)) {
      updateTime = calcSyncNumSame(tenantId, dbSyncId, fieldTableList, timezone);
      aggDelayInfo.setUpdateTime(updateTime);
      return aggDelayInfo;
    }
    if (currentBatchNum < batchNum) {
      String statViewUniqueKey = biMtTopologyTable.getStatViewUniqueKey();
      String aggKey = String.format("dws:agg:%s:%s:%d", tenantId, statViewUniqueKey, batchNum);
      String calcResult = calcJedisFactory.hget(aggKey, fieldId);
      // 计算完成 或者 redis 指标计算状态为空并且统计图计算的批次和当前这个库的起算批次相等时
      long statBatchNum = biMtTopologyTable.getBatchNum();
      if ("1".equals(calcResult) || (StringUtils.isBlank(calcResult) && statBatchNum == currentBatchNum)) {
        updateTime = calcSyncNumSame(tenantId, dbSyncId, fieldTableList, timezone);
        aggDelayInfo.setUpdateTime(updateTime);
        aggDelayInfo.setPartitions(List.of(WarehouseConfig.STOCK_PARTITION_NAME, WarehouseConfig.CAL_PARTITION_NAME));
        aggDelayInfo.setBatchNum(currentBatchNum);
        return aggDelayInfo;
      }
      // 当前批次还没计算到，返回更新时间，此时应该查存量分区数据
      if (StringUtils.isBlank(calcResult) && statBatchNum < currentBatchNum) {
        updateTime = calcSyncNumSame(tenantId, dbSyncId, fieldTableList, timezone);
        aggDelayInfo.setUpdateTime(updateTime);
        aggDelayInfo.setPartitions(List.of(WarehouseConfig.STOCK_PARTITION_NAME));
        aggDelayInfo.setBatchNum(currentBatchNum);
        return aggDelayInfo;
      }
      // 未计算完成，但是已经在计算中了
      if ("0".equals(calcResult)) {
        DbSyncInfoFlowDO dbSyncInfoFlowDO = dbTableSyncInfoMapper.setTenantId(tenantId)
                                                                 .querySyncFlowUpdateTime(dbSyncId, batchNum);
        if (Objects.isNull(dbSyncInfoFlowDO)) {
          return aggDelayInfo;
        }
        long lastSyncTime = dbSyncInfoFlowDO.getLastSyncTime();
        String maxSysModifiedTime = dbSyncInfoFlowDO.getMaxSysModifiedTime();
        if (StringUtils.isBlank(maxSysModifiedTime)) {
          String updateTimeStr = DateFormatUtils.format(new Date(lastSyncTime), Utils.DATE_FORMAT_1, TimeZone.getTimeZone(timezone));
          aggDelayInfo.setUpdateTime(updateTimeStr);
        } else {
          JSONObject tableSyncTimeMap = JSON.parseObject(maxSysModifiedTime);
          Long maxTableTime = fieldTableList.stream()
                                            .filter(StringUtils::isNotBlank)
                                            .map(str -> str.substring(0, str.length() - 3))
                                            .map(tableSyncTimeMap::getLong)
                                            .filter(Objects::nonNull)
                                            .max(Long::compareTo)
                                            .orElse(lastSyncTime);
          String lastAggTime = DateFormatUtils.format(new Date(maxTableTime), Utils.DATE_FORMAT_1, TimeZone.getTimeZone(timezone));
          aggDelayInfo.setUpdateTime(lastAggTime);
        }
        aggDelayInfo.setPartitions(List.of(WarehouseConfig.STOCK_PARTITION_NAME));
        Integer calcCost = getStatCalcCost(viewId, fieldId, tenantId, forceFresh);
        aggDelayInfo.setRefreshDelay(calcCost);
        return aggDelayInfo;
      }
    }
    return aggDelayInfo;
  }

  private Integer getStatCalcCost(String viewId, String fieldId, String tenantId, boolean forceFresh) {
    try {
      String key = String.format("%s|%s|%s", tenantId, viewId, fieldId);
      if (forceFresh) {
        VIEW_FIELD_COST_CACHE.refresh(key);
      }
      return VIEW_FIELD_COST_CACHE.get(key);
    } catch (Exception e) {
      log.error("getStatCalcCost error, tenantId:{}, viewId:{}, fieldId:{}", tenantId, viewId, fieldId, e);
      return null;
    }
  }

  private String calcSyncNumSame(String tenantId, String dbSyncId, Set<String> fieldTableList, String timezone) {
    DbSyncInfoFlowDO dbSyncInfoFlow = dbTableSyncInfoMapper.setTenantId(tenantId).queryTableLastUpdateTime(dbSyncId);
    if (Objects.isNull(dbSyncInfoFlow)) {
      return null;
    }
    long lastSyncTime = dbSyncInfoFlow.getLastSyncTime();
    String maxSysModifiedTime = dbSyncInfoFlow.getMaxSysModifiedTime();
    if (StringUtils.isBlank(maxSysModifiedTime)) {
      return DateFormatUtils.format(new Date(lastSyncTime), Utils.DATE_FORMAT_1, TimeZone.getTimeZone(timezone));
    }
    JSONObject tableSyncTimeMap = JSON.parseObject(maxSysModifiedTime);
    Long maxTableTime = fieldTableList.stream()
                                      .filter(StringUtils::isNotBlank)
                                      .map(str -> str.substring(0, str.length() - 3))
                                      .map(tableSyncTimeMap::getLong)
                                      .filter(Objects::nonNull)
                                      .max(Long::compareTo)
                                      .orElse(lastSyncTime);
    return DateFormatUtils.format(new Date(maxTableTime), Utils.DATE_FORMAT_1, TimeZone.getTimeZone(timezone));
  }
}
