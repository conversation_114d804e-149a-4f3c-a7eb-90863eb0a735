package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.common.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class BiMtDetailRule {
  private String detailId;
  private String tenantId;
  private String topologyDescribeId;
  private String detailField;
  private String fieldId;

  public Pair<String, String> nodeIdFieldPair() {
    String[] dimFields = detailField.split("\\.");
    return Pair.build(dimFields[0], dimFields[1]);
  }
}
