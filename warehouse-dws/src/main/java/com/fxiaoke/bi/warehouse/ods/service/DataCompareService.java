package com.fxiaoke.bi.warehouse.ods.service;

import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.component.BIPgDataSource;
import com.fxiaoke.bi.warehouse.ods.args.ComparePgChDataArg;
import com.fxiaoke.bi.warehouse.ods.bean.PGSchema;
import com.fxiaoke.bi.warehouse.ods.entity.ClickHouseColumnType;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseColumn;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.common.Pair;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ClickHouse和PostgreSQL数据对比服务
 */
@Slf4j
@Service
public class DataCompareService {

  @Resource
  private PGMetadataService pgMetadataService;
  @Resource
  private CHMetadataService chMetadataService;
  @Resource
  private CHDataSource chDataSource;
  @Resource
  private BIPgDataSource biPgDataSource;

  /**
   * 默认每页处理数据量
   */
  private static final int DEFAULT_PAGE_SIZE = 1000;

  // 字段类型缓存，避免重复查询
  private final Map<String, Map<String, String>> tableColumnTypeCache = Maps.newConcurrentMap();

  /**
   * 获取表中删除标记字段的名称和类型
   * @param pgJdbcUrl 数据库连接URL
   * @param tableName 表名
   * @return 返回删除标记字段的名称和类型，如果没有找到返回null
   */
  private BIPair<String, String> getDeletedFieldInfo(String pgJdbcUrl, String tableName) {
    // 先从缓存获取表的字段类型信息
    String tableKey = pgJdbcUrl + ":" + tableName.toLowerCase();
    Map<String, String> columnTypes = tableColumnTypeCache.computeIfAbsent(tableKey, k -> {
      Map<String, String> types = Maps.newHashMap();
      try {
        // 查询表的所有字段类型
        String checkColumnSQL = String.format("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = '%s' AND column_name IN ('is_deleted', 'is_delete', 'deleted')", tableName.toLowerCase());
        try (JdbcConnection conn = biPgDataSource.getConnectionByPgbouncerURL(pgJdbcUrl)) {
          conn.query(checkColumnSQL, rs -> {
            while (rs.next()) {
              String columnName = rs.getString("column_name");
              String dataType = rs.getString("data_type");
              types.put(columnName, dataType);
            }
          });
        }
      } catch (SQLException e) {
        log.error("Error querying column types for table {}: {}", tableName, e.getMessage());
      }
      return types;
    });
    // 检查不同的删除标记字段名
    String[] possibleDeleteFieldNames = {"is_deleted", "is_delete", "deleted"};
    for (String fieldName : possibleDeleteFieldNames) {
      if (columnTypes.containsKey(fieldName)) {
        return BIPair.of(fieldName, columnTypes.get(fieldName));
      }
    }
    // 没有找到删除字段
    return BIPair.of("is_deleted", "integer");
  }

  /**
   * 比较ClickHouse和PostgreSQL数据，并在ClickHouse中标记需要删除的数据
   *
   * @param arg 对比参数
   * @return 处理结果描述
   */
  public String compareAndMarkDeletedData(ComparePgChDataArg arg) {
    String tenantId = arg.getTenantId();
    String tableName = arg.getTableName();
    List<String> primaryIds = arg.getPrimaryIds();
    String batchId = arg.getBatchId();
    String objectDescribeApiName = arg.getObjectDescribeApiName();
    int pageSize = arg.getPageSize() != null && arg.getPageSize() > 0 ? arg.getPageSize() : DEFAULT_PAGE_SIZE;
    log.info("Start comparing and marking deleted data: tenantId={}, tableName={}, primaryIds={}, batchId={}, objectDescribeApiName={}", tenantId, tableName, primaryIds, batchId, objectDescribeApiName);
    try {
      // 1. 获取PG和CH的连接信息
      String pgJdbcUrl = biPgDataSource.getPgBouncerJdbcUrlByTenantId(tenantId);
      String chJdbcUrl = chMetadataService.getCHJdbcURL(tenantId);
      if (StringUtils.isBlank(pgJdbcUrl) || StringUtils.isBlank(chJdbcUrl)) {
        log.error("Failed to get JDBC URLs. pgJdbcUrl={}, chJdbcUrl={}", pgJdbcUrl, chJdbcUrl);
        return "获取数据库连接失败";
      }
      // 2. 获取表的元数据信息
      boolean standalone = biPgDataSource.isStandalone(tenantId);
      String schemaTable = standalone ? "sch_" + tenantId + "." + tableName : tableName;
      PGSchema pgSchema = pgMetadataService.loadSchema(pgJdbcUrl, schemaTable);
      if (pgSchema == null) {
        log.error("PG schema not found for table: {}", tableName);
        return "PG schema not found for table：" + tableName;
      }
      String primaryKeyColumn = pgSchema.findPrimaryKeyId();
      if (StringUtils.isBlank(primaryKeyColumn)) {
        log.error("Primary key not found for table: {}", schemaTable);
        return "表没有主键：" + tableName;
      }
      Optional<ClickhouseTable> chTableOpt = chMetadataService.loadTableFromDB(chJdbcUrl, tableName);
      if (chTableOpt.isEmpty()) {
        log.error("ClickHouse table not found: {}", tableName);
        return "ClickHouse table not found：" + tableName;
      }
      ClickhouseTable chTable = chTableOpt.get();
      // 3. 对比数据并标记删除
      int totalMarked;
      if (CollectionUtils.isNotEmpty(primaryIds)) {
        // 3.1 按指定的主键ID列表处理
        totalMarked = processWithPrimaryIds(tenantId, pgJdbcUrl, chJdbcUrl, pgSchema, chTable, primaryIds, primaryKeyColumn, batchId, objectDescribeApiName);
      } else {
        // 3.2 遍历全表数据比较
        totalMarked = processFullTableData(tenantId, pgJdbcUrl, chJdbcUrl, pgSchema, chTable, primaryKeyColumn, pageSize, batchId, objectDescribeApiName);
      }
      return String.format("成功处理完成，共标记删除 %d 条数据", totalMarked);
    } catch (Exception e) {
      log.error("Error while comparing and marking deleted data", e);
      return "处理过程中发生错误: " + e.getMessage();
    }
  }

  /**
   * 根据主键列表处理数据
   */
  private int processWithPrimaryIds(String tenantId,
                                    String pgJdbcUrl,
                                    String chJdbcUrl,
                                    PGSchema pgSchema,
                                    ClickhouseTable chTable,
                                    List<String> primaryIds,
                                    String primaryKeyColumn,
                                    String batchId,
                                    String objectDescribeApiName) throws SQLException {
    int totalMarked = 0;
    // 1. 从ClickHouse获取所有数据
    Set<String> chKeys = queryClickHouseKeys(chJdbcUrl, chTable.getName(), tenantId, primaryKeyColumn, primaryIds, batchId, objectDescribeApiName);
    // 2. 直接在PostgreSQL中查询需要删除的数据
    Set<String> keysToMark = queryMissingPostgreSQLKeys(pgJdbcUrl, pgSchema.getName(), tenantId, primaryKeyColumn, Lists.newArrayList(chKeys));
    // 3. 标记删除
    if (!keysToMark.isEmpty()) {
      totalMarked = markDeletedInClickHouse(chJdbcUrl, chTable, tenantId, keysToMark, primaryKeyColumn);
    }
    return totalMarked;
  }

  /**
   * 处理全表数据
   */
  private int processFullTableData(String tenantId,
                                   String pgJdbcUrl,
                                   String chJdbcUrl,
                                   PGSchema pgSchema,
                                   ClickhouseTable chTable,
                                   String primaryKeyColumn,
                                   int pageSize,
                                   String batchId,
                                   String objectDescribeApiName) throws SQLException {
    int totalMarked = 0;
    String lastKey = "";
    boolean hasMore = true;

    while (hasMore) {
      // 1. 从ClickHouse分页查询数据
      Pair<Set<String>, String> chResult = queryClickHouseKeysPaginated(chJdbcUrl, chTable.getName(), tenantId, primaryKeyColumn, lastKey, pageSize, batchId, objectDescribeApiName);
      Set<String> chKeys = chResult.first;
      lastKey = chResult.second;
      if (chKeys.isEmpty()) {
        hasMore = false;
        continue;
      }
      // 2. 直接在PostgreSQL中查询需要删除的数据
      Set<String> keysToMark = queryMissingPostgreSQLKeys(pgJdbcUrl, pgSchema.getName(), tenantId, primaryKeyColumn, Lists.newArrayList(chKeys));
      // 3. 标记删除
      if (!keysToMark.isEmpty()) {
        int marked = markDeletedInClickHouse(chJdbcUrl, chTable, tenantId, keysToMark, primaryKeyColumn);
        totalMarked += marked;
      }
      if (chKeys.size() < pageSize) {
        hasMore = false;
      }
    }
    return totalMarked;
  }

  /**
   * 根据主键列表查询ClickHouse中的主键值
   */
  private Set<String> queryClickHouseKeys(String chJdbcUrl,
                                          String tableName,
                                          String tenantId,
                                          String primaryKeyColumn,
                                          List<String> primaryIds,
                                          String batchId,
                                          String objectDescribeApiName) throws SQLException {
    Set<String> keys = Sets.newHashSet();

    String inClause = primaryIds.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
    StringBuilder whereClause = new StringBuilder().append("tenant_id = '")
                                                   .append(tenantId)
                                                   .append("'")
                                                   .append(" AND ")
                                                   .append(primaryKeyColumn)
                                                   .append(" IN (")
                                                   .append(inClause)
                                                   .append(")")
                                                   .append(" AND bi_sys_is_deleted = 0");

    if (StringUtils.isNotBlank(batchId)) {
      whereClause.append(" AND bi_sys_batch_id = '").append(batchId).append("'");
    }

    if (StringUtils.isNotBlank(objectDescribeApiName)) {
      whereClause.append(" AND object_describe_api_name = '").append(objectDescribeApiName).append("'");
    }

    String sql = String.format("SELECT %s FROM %s WHERE %s", primaryKeyColumn, tableName, whereClause);

    try (JdbcConnection conn = chDataSource.getJdbcConnection(chJdbcUrl)) {
      conn.query(sql, rs -> {
        while (rs.next()) {
          keys.add(rs.getString(1));
        }
      });
    }

    return keys;
  }

  /**
   * 分页查询ClickHouse中的主键值
   */
  private Pair<Set<String>, String> queryClickHouseKeysPaginated(String chJdbcUrl,
                                                                 String tableName,
                                                                 String tenantId,
                                                                 String primaryKeyColumn,
                                                                 String lastKey,
                                                                 int pageSize,
                                                                 String batchId,
                                                                 String objectDescribeApiName) throws SQLException {
    Set<String> keys = Sets.newHashSet();
    final String[] lastKeyHolder = {lastKey};

    StringBuilder whereClause = new StringBuilder().append("tenant_id = '")
                                                   .append(tenantId)
                                                   .append("'")
                                                   .append(" AND is_deleted = 0 AND bi_sys_flag = 1");

    if (StringUtils.isNotBlank(batchId)) {
      whereClause.append(" AND bi_sys_batch_id = '").append(batchId).append("'");
    }

    if (StringUtils.isNotBlank(objectDescribeApiName)) {
      whereClause.append(" AND object_describe_api_name = '").append(objectDescribeApiName).append("'");
    }

    if (StringUtils.isNotBlank(lastKey)) {
      whereClause.append(" AND ").append(primaryKeyColumn).append(" > '").append(lastKey).append("'");
    }

    String sql = String.format("SELECT %s FROM %s FINAL WHERE %s ORDER BY %s LIMIT %d", primaryKeyColumn, tableName, whereClause, primaryKeyColumn, pageSize);

    try (JdbcConnection conn = chDataSource.getJdbcConnection(chJdbcUrl)) {
      conn.query(sql, rs -> {
        while (rs.next()) {
          String key = rs.getString(1);
          keys.add(key);
          lastKeyHolder[0] = key;
        }
      });
    }
    return Pair.of(keys, lastKeyHolder[0]);
  }

  /**
   * 查询PostgreSQL中不存在或已删除的主键值
   */
  private Set<String> queryMissingPostgreSQLKeys(String pgJdbcUrl,
                                                 String tableName,
                                                 String tenantId,
                                                 String primaryKeyColumn,
                                                 List<String> chKeys) throws SQLException {
    Set<String> missingKeys = Sets.newHashSet();
    if (chKeys.isEmpty()) {
      return missingKeys;
    }
    String inClause = chKeys.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
    // 获取删除标记字段信息
    BIPair<String, String> deleteFieldInfo = getDeletedFieldInfo(pgJdbcUrl, tableName);
    // 构建删除条件
    String isDeletedCondition;
    String deleteFieldName;
    deleteFieldName = deleteFieldInfo.first;
    String dataType = deleteFieldInfo.second;
    // 根据字段类型设置适当的条件
    isDeletedCondition = switch (dataType.toLowerCase()) {
      case "boolean" -> String.format("pg.%s = false", deleteFieldName);
      case "bit" -> String.format("pg.%s = B'0'", deleteFieldName);
      default -> String.format("pg.%s = 0", deleteFieldName);
    };
    // 直接在PostgreSQL中查询出ClickHouse中需要删除的数据
    String sql = String.format("SELECT ch.key FROM (SELECT unnest(ARRAY[%s]) as key) ch " +
                               "LEFT JOIN %s pg ON pg.%s = ch.key AND pg.tenant_id = '%s' AND (%s OR pg.%s IS NULL) " +
                               "WHERE pg.%s IS NULL", inClause, tableName, primaryKeyColumn, tenantId, isDeletedCondition, deleteFieldName, primaryKeyColumn);
    log.debug("Executing SQL to find missing keys: {}", sql);
    try (JdbcConnection conn = biPgDataSource.getConnectionByPgbouncerURL(pgJdbcUrl)) {
      conn.query(sql, rs -> {
        while (rs.next()) {
          missingKeys.add(rs.getString(1));
        }
      });
    }
    return missingKeys;
  }

  /**
   * 在ClickHouse中标记数据为已删除
   */
  private int markDeletedInClickHouse(String chJdbcUrl,
                                      ClickhouseTable chTable,
                                      String tenantId,
                                      Set<String> keysToMark,
                                      String primaryKeyColumn) throws SQLException {
    if (keysToMark.isEmpty()) {
      return 0;
    }

    // 1. 获取order by字段列表
    List<String> orderByColumns = chTable.getOrderByColumns();
    if (orderByColumns == null) {
      orderByColumns = Lists.newArrayList();
    }

    // 确保主键字段和tenant_id在列表中
    if (!orderByColumns.contains(primaryKeyColumn)) {
      orderByColumns.add(primaryKeyColumn);
    }
    if (!orderByColumns.contains("tenant_id")) {
      orderByColumns.add("tenant_id");
    }

    // 添加必须的系统字段
    List<String> allFields = Lists.newArrayList(orderByColumns);
    allFields.add("is_deleted");

    // 2. 查询需要标记删除数据的原始值
    Map<String, Map<String, Object>> originalValues = queryOriginalValues(chJdbcUrl, chTable.getName(), tenantId, keysToMark, primaryKeyColumn, orderByColumns);

    // 3. 准备插入语句
    StringBuilder sql = new StringBuilder();
    sql.append("INSERT INTO ").append(chTable.getName());
    // 添加字段列表
    sql.append(" (").append(String.join(", ", allFields)).append(")");
    sql.append(" VALUES ");

    // 4. 添加值
    List<String> valuesList = Lists.newArrayList();
    for (String key : keysToMark) {
      Map<String, Object> rowValues = originalValues.getOrDefault(key, Maps.newHashMap());
      List<String> values = Lists.newArrayList();

      for (String column : allFields) {
        if (column.equals(primaryKeyColumn)) {
          values.add("'" + key + "'");
        } else if ("tenant_id".equals(column)) {
          values.add("'" + tenantId + "'");
        } else if ("is_deleted".equals(column)) {
          values.add("-2"); // is_deleted设置为-2表示标记删除
        } else {
          // 使用原始值，如果没有则设置为NULL
          Object originalValue = rowValues.get(column);
          if (originalValue != null) {
            ClickhouseColumn columnDef = chTable.getColumnMap().get(column);
            if (columnDef != null && columnDef.getType() == ClickHouseColumnType.STRING) {
              values.add("'" + originalValue.toString().replace("'", "\\'") + "'");
            } else {
              values.add(originalValue.toString());
            }
          } else {
            values.add("NULL");
          }
        }
      }
      valuesList.add("(" + String.join(", ", values) + ")");
    }

    sql.append(String.join(", ", valuesList));

    try (JdbcConnection conn = chDataSource.getJdbcConnection(chJdbcUrl)) {
      String finalSql = sql.toString();
      log.debug("Executing SQL: {}", finalSql);
      int affected = conn.executeUpdate(finalSql);
      log.info("Marked {} records as deleted in ClickHouse table {}", affected, chTable.getName());
      return affected;
    }
  }

  /**
   * 查询原始数据的指定字段值
   */
  private Map<String, Map<String, Object>> queryOriginalValues(String chJdbcUrl,
                                                               String tableName,
                                                               String tenantId,
                                                               Set<String> keys,
                                                               String primaryKeyColumn,
                                                               List<String> columnsToQuery) throws SQLException {
    Map<String, Map<String, Object>> result = Maps.newHashMap();

    if (keys.isEmpty() || columnsToQuery.isEmpty()) {
      return result;
    }

    // 转换keys为IN子句
    String inClause = keys.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));

    // 构建查询字段
    List<String> queryColumns = Lists.newArrayList(columnsToQuery);
    if (!queryColumns.contains(primaryKeyColumn)) {
      queryColumns.add(primaryKeyColumn);
    }
    String selectColumns = String.join(", ", queryColumns);

    // 构建SQL
    String sql = String.format("SELECT %s FROM %s WHERE tenant_id = '%s' AND %s IN (%s) AND bi_sys_is_deleted = 0", selectColumns, tableName, tenantId, primaryKeyColumn, inClause);

    try (JdbcConnection conn = chDataSource.getJdbcConnection(chJdbcUrl)) {
      conn.query(sql, rs -> {
        while (rs.next()) {
          String keyValue = rs.getString(primaryKeyColumn);
          Map<String, Object> rowValues = Maps.newHashMap();

          for (String column : columnsToQuery) {
            if (!column.equals(primaryKeyColumn) && !column.equals("tenant_id")) {
              Object value = rs.getObject(column);
              rowValues.put(column, value);
            }
          }

          result.put(keyValue, rowValues);
        }
      });
    }

    return result;
  }
} 