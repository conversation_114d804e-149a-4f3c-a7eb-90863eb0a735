package com.fxiaoke.bi.warehouse.core.db.mapper;

import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface UdfObjFieldMapper extends ICrudMapper<UdfObjFieldDO>, ITenant<UdfObjFieldMapper> {
  /**
   * 查询对象的子对象列表
   * @param objNames
   * @param ei
   * @return
   */
  @Select("select * from udf_obj_field where ei=#{ei} and type='master_detail' and ref_obj_name=any(array[#{objNames}]) " +
    " and is_delete=0 and is_show=1")
  List<UdfObjFieldDO> batchQueryMDField(@Param("objNames") String[] objNames, @Param("ei") int ei);

  /**
   * 根据类型返回字段列表
   *
   * @param tenantId
   * @param apiName
   * @param apiXName
   * @param type
   * @return
   */
  @Select("select * from udf_obj_field where ei=#{tenantId}::int and db_obj_name in (#{apiName},#{apiXName}) and " +
    "type = #{type} and is_delete=0 ")
  List<UdfObjFieldDO> findUdfDbFieldInfoByType(@Param("tenantId") String tenantId,
                                               @Param("apiName") String apiName,
                                               @Param("apiXName") String apiXName,
                                               @Param("type") String type);

  @Select("select * from udf_obj_field where (ei=#{ei} ) and (db_obj_name = #{db_obj_name} or " +
    "db_obj_name=#{db_obj_udef_name})and db_field_name = #{db_field_name} and is_delete = 0 limit 1")
  UdfObjFieldDO queryFieldByObjNameAndDbFieldName(@Param("db_field_name") String dbFieldName,
                                                  @Param("db_obj_name") String dbObjName,
                                                  @Param("db_obj_udef_name") String dbObjUdefName,
                                                  @Param("ei") Integer ei);

  @Select("select * from udf_obj_field where (ei=#{ei} ) and (db_obj_name = #{db_obj_name} or " +
    "db_obj_name=#{db_obj_udef_name}) and db_field_name = any(array[#{db_field_names}]) and is_delete = 0 ")
  List<UdfObjFieldDO> batchQueryFieldByObjNameAndDbFieldName(@Param("ei") Integer ei,
                                                             @Param("db_obj_name") String dbObjName,
                                                             @Param("db_obj_udef_name") String dbObjUdefName,
                                                             @Param("db_field_names") String[] dbFieldName);

  /**
   * 基于paas apiName 查询udf_obj_field
   *
   * @param ei
   * @param crmObjName
   * @param dbFieldName
   * @return
   */
  @Select("select * from udf_obj_field where (ei=#{ei} ) and crm_obj_name = #{crmObjName}  and db_field_name = any" +
    "(array[#{db_field_names}]) and is_delete = 0 ")
  List<UdfObjFieldDO> queryFieldByPaasNameAndDbFieldName(@Param("ei") Integer ei,
                                                         @Param("crmObjName") String crmObjName,
                                                         @Param("db_field_names") String[] dbFieldName);


  @Select("select * from udf_obj_field where ei=#{tenantId}::int and db_obj_name in (#{apiName},#{apiXName}) and " +
    "db_field_name = #{fieldName} and is_delete=0 limit 1 ")
  Map<String, Object> findFieldInfoByFieldName(@Param("tenantId") String tenantId,
                                               @Param("apiName") String apiName,
                                               @Param("apiXName") String apiXName,
                                               @Param("fieldName") String fieldName);

  @Select("select * from udf_obj_field where ei=#{tenantId}::int and db_obj_name in (#{apiName},#{apiXName}) and " +
    "db_field_name = #{fieldName} and is_delete=0 limit 1 ")
  UdfObjFieldDO findUdfObjFieldDOByFieldName(@Param("tenantId") String tenantId,
                                             @Param("apiName") String apiName,
                                             @Param("apiXName") String apiXName,
                                             @Param("fieldName") String fieldName);

  @Select("select * from udf_obj_field where ei=#{tenantId}::int and db_obj_name in (#{apiName},#{apiXName}) and " +
    "field_location = #{fieldLocation} and is_delete=0 limit 1 ")
  Map<String, Object> findFieldInfoByFieldLocation(@Param("tenantId") String tenantId,
                                                   @Param("apiName") String apiName,
                                                   @Param("apiXName") String apiXName,
                                                   @Param("fieldLocation") int fieldLocation);

  @Select("select * from udf_obj_field where ei=#{tenantId}::int and db_obj_name in (#{apiName},#{apiXName}) and " +
          "field_location = #{fieldLocation} and is_delete=0 limit 1 ")
  UdfObjFieldDO findUdfObjFieldByFieldLocation(@Param("tenantId") String tenantId,
                                               @Param("apiName") String apiName,
                                               @Param("apiXName") String apiXName,
                                               @Param("fieldLocation") int fieldLocation);

  /**
   * 按照fieldId反查字段描述
   *
   * @param tenantId
   * @param fieldId
   * @return
   */
  @Select("select * from udf_obj_field where ei=#{tenantId}::int and field_id=#{fieldId} and is_delete=0 limit 1 ")
  UdfObjFieldDO findFieldInfoByFieldId(@Param("tenantId") String tenantId, @Param("fieldId") String fieldId);

  @Select("""
    SELECT *
    FROM udf_obj_field
    WHERE ei = #{ei}
      AND field_id ${dimensionIds}
      AND is_delete = 0
    """)
  List<UdfObjFieldDO> findFieldByFieldIds(@Param("ei") Integer ei, @Param("dimensionIds") String dimensionIds);


  @Select("select * from udf_obj_field where (ei=#{ei} ) and (db_obj_name = #{objName} or db_obj_name = #{objNameUdef}) and is_show=any(array[#{is_show}]) and is_delete = 0 ")
  List<UdfObjFieldDO> getFieldsByObjName(@Param("objName") String objName, @Param("objNameUdef") String objNameUdef, @Param("ei") int ei, @Param("is_show") int[] isShow);

  //根据db_field_name 查询 udf_obj_field 信息
  @Select("select * from udf_obj_field where (ei=#{ei} ) and (db_obj_name = #{objName} or db_obj_name = #{objNameUdef}) and is_show=any(array[#{is_show}]) and db_field_name = any(array[#{db_field_names}]) and is_delete = 0 ")
  List<UdfObjFieldDO> getFieldsByObjNameByDbFieldName(@Param("objName") String objName, @Param("objNameUdef") String objNameUdef, @Param("ei") int ei, @Param("is_show") int[] isShow, @Param("db_field_names") String[]dbFieldNames);

  @Select("SELECT db_field_name, field_location FROM udf_obj_field WHERE (ei=#{ei}) AND db_obj_name = #{objName} and is_show=any(array[0, 1]) AND field_location > 0 AND type ${typeList} AND is_delete = 0")
  List<UdfObjFieldDO> getFieldsByObjNameAndType(@Param("ei") Integer ei, @Param("objName") String objName, @Param("typeList") String typeList);

  @Select("SELECT relation_table FROM udf_obj_field WHERE ei=#{ei} AND db_obj_name = #{dbObjName} AND db_field_name = 'related_object' AND type = 'group' AND relation_table IS NOT NULL AND is_delete = 0 LIMIT 1")
  UdfObjFieldDO findWhatListRelatedObjField(@Param("ei") Integer ei, @Param("dbObjName") String dbObjName);

  @Select("SELECT * FROM udf_obj_field WHERE ei = #{ei} AND db_obj_name = #{dbObjName} AND is_key = 1 AND is_delete = 0 LIMIT 1")
  UdfObjFieldDO findObjIdFieldByObjName(@Param("ei") Integer ei, @Param("dbObjName") String dbObjName);

  @Select("SELECT * FROM udf_obj_field WHERE ei = #{ei} AND field_id = #{udfFieldId} AND is_delete = 0 LIMIT 1")
  UdfObjFieldDO findFieldByStatFieldIds(@Param("ei") Integer ei, @Param("udfFieldId") String udfFieldId);
}
