package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 同步策略图表规则
 */
@Data
@Builder
@NoArgsConstructor(staticName = "empty")
@AllArgsConstructor(staticName = "of")
public class AggMappingRule {

    /**
     * 图表viewId
     */
    private String viewId;

    /**
     * 图表viewName
     */
    private String viewName;

    /**
     * 图表维度
     */
    private List<DimField> dimFiledIdList;

    /**
     * 图表数据范围
     */
    private List<FilterList> filterList;

    /**
     * 上下游指标映射规则
     */
    private List<AggMapping> aggMappingList;
}
