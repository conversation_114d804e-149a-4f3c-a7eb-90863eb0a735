package com.fxiaoke.bi.warehouse.ods.compare.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-26
 * @desc ch库和pg库差异
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComparePgToChResult {

    /**
     * ch表和pg表差异结果
     */
    private ComparePgToChTableResult comparePgToChTableResult;

    /**
     * pg需要同步表数量以及缺少触发器的表
     */
    private ComparePgToChTriggerResult comparePgToChTriggerResult;

    /**
     * ch表和pg表数量差异结果
     */
    private List<ComparePgToChCountResult> comparePgToChCountResultList;

    /**
     * ch表和pg表字段差异结果
     */
    private List<ComparePgToChColumnResult> comparePgToChColumnResultList;
}
