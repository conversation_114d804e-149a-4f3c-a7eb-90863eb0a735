package com.fxiaoke.bi.warehouse.core.db;

import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.entity.DBMergeInfoDO;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.dao.mapper.BISystemMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AggMergeDao {
  @Autowired
  private BISystemMapper biSystemMapper;

  /**
   * 更新merge状态
   * @param dbSyncId
   * @param pgUrl
   * @param pgSchema
   * @param chUrl
   * @param allowIncPartition
   * @param casStatus
   * @return
   */
  public int updateStatusFromExchangeCAS(String dbSyncId,
                                         String pgUrl,
                                         String pgSchema,
                                         String chUrl,
                                         Integer allowIncPartition,
                                         List<Integer> casStatus) {
    String chDb = CHContext.getDBName(chUrl);
    String tableName = "db_sync_info";
    int status = SyncStatusEnum.SYNC_ABLE.getStatus();
    if (GrayManager.isAllowByRule("use_ch_ods_partition", chDb) &&
      Objects.equals(allowIncPartition, WarehouseConfig.OPEN_INC_PARTITION)) {
      tableName = "db_agg_info";
      status = SyncStatusEnum.AGG_ED.getStatus();
    }
    String anyStatus = casStatus.stream()
                                .filter(Objects::nonNull)
                                .map(Object::toString)
                                .collect(Collectors.joining(","));
    String sql = String.format("update %s set status=%d where id='%s' and status=any(array[%s]) and is_deleted=0", tableName, status, dbSyncId, anyStatus);
    int result = biSystemMapper.setTenantId("-1").execCommonSQL(sql);
    log.info("updateDbSyncInfoByIdStatusCAS success pgDB:{},pgSchema:{},chDB:{},table:{},result:{}", pgUrl, pgSchema, chDb, tableName, result);
    return result;
  }

  public List<DBMergeInfoDO> queryDBMergeInfoByChUrl(String chUrl){
    return biSystemMapper.setTenantId("-1").queryDbAggMergeInfo(chUrl);
  }

  public int updateAggMergeTime(String chUrl, long mergeTime, int preStatus) {
    int result1 = biSystemMapper.setTenantId("-1").updateDbSyncInfoLastMergeTimeByStatus(chUrl, mergeTime, preStatus);
    int result2 = biSystemMapper.setTenantId("-1").updateDbAggInfoLastMergeTimeByStatus(chUrl, mergeTime, preStatus);
    log.info("updateAggMergeTime result1:{},result2:{}", result1, result2);
    return result1 + result2;
  }

  /**
   *
   * @param chURL
   * @param preStatus
   * @return
   */
  public int changeStatusToRunByChDbUrl(String chURL, int preStatus) {
    int result = biSystemMapper.setTenantId("-1")
                               .updateDbSyncInfoStatusByCHDB(chURL, SyncStatusEnum.SYNC_ABLE.getStatus(), preStatus);
    int result2 = biSystemMapper.setTenantId("-1")
                                .updateDbAggInfoStatusByCHDB(chURL, SyncStatusEnum.AGG_ED.getStatus(), preStatus);
    log.info("updateDbSyncInfoStatusByChDbUrl result:{},result2:{}", result, result2);
    return result + result2;
  }

  /**
   * 校验 同一个chdb中所有同步信息，判断当前时间是否允许执行merge任务
   * @param dbSyncInfos db同步信息
   * @return bool
   */
  public boolean canMergedTodayByDbMergeInfos(List<DBMergeInfoDO> dbSyncInfos) {
    if (CollectionUtils.isEmpty(dbSyncInfos)) {
      return false;
    }
    Optional<Long> lastMergeTime = dbSyncInfos.stream()
                                              .map(DBMergeInfoDO::getLastMergeAggTime)
                                              .filter(Objects::nonNull)
                                              .max(Comparator.naturalOrder());
    if (lastMergeTime.isEmpty()) {
      return true;
    }
    LocalDate lastMergeDate = new Timestamp(lastMergeTime.get()).toLocalDateTime().toLocalDate();
    LocalDate localDate = LocalDate.now();
    return localDate.isAfter(lastMergeDate);
  }

  /**
   * 校验 同一个chdb中所有同步信息，判断当前时间是否允许执行merge任务
   * @param dbSyncInfos db同步信息
   * @return bool
   */
  private boolean canMergedTodayByDbSyncInfos(List<DBSyncInfo> dbSyncInfos) {
    if (CollectionUtils.isEmpty(dbSyncInfos)) {
      return false;
    }
    Optional<Long> lastMergeTime = dbSyncInfos.stream()
                                              .map(DBSyncInfo::getLastMergeAggTime)
                                              .filter(Objects::nonNull)
                                              .max(Comparator.naturalOrder());
    if (lastMergeTime.isEmpty()) {
      return true;
    }
    LocalDate lastMergeDate = new Timestamp(lastMergeTime.get()).toLocalDateTime().toLocalDate();
    LocalDate localDate = LocalDate.now();
    return localDate.isAfter(lastMergeDate);
  }
}
