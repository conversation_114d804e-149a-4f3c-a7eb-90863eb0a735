package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.transform.model.GoalRuleBO;
import com.github.mybatis.annotation.DynamicTypeHandler;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.List;

/**
 * @Author:jief
 * @Date:2023/6/24
 */
@Table(name = "goal_rule")
@Data
public class GoalRuleDO {
  @Column(name = "tenant_id")
  public String tenantId;
  @Column(name = "id")
  public String id;
  @Column(name = "name")
  public String name;
  @Column(name = "description")
  public String description;
  @Column(name = "check_object_api_name")
  public String checkObjectApiName;
  @Column(name = "check_field_api_name")
  public String checkFieldApiName;
  @Column(name = "count_time_api_name")
  public String countTimeApiName;
  @Column(name = "check_field_aggregate_type")
  public String checkFieldAggregateType;
  @Column(name = "count_fiscal_year")
  @DynamicTypeHandler(value = "com.github.mybatis.handler.list.ListTypeHandler")
  public List<String> countFiscalYear;
  @Column(name = "start_month")
  public String startMonth;
  @Column(name = "wheres")
  public String wheres;
  @Column(name = "subgoal_object_api_name")
  public String subgoalObjectApiName;
  @Column(name = "subgoal_field_api_name")
  public String subgoalFieldApiName;
  @Column(name = "allow_personal_modify_goal")
  public int allowPersonalModifyGoal;
  @Column(name = "status")
  public int status;
  @Column(name = "define_type")
  public String defineType;
  @Column(name = "object_describe_api_name")
  public String objectDescribeApiName;
  @Column(name = "object_describe_id")
  public String objectDescribeId;
  @Column(name = "created_by")
  public String createdBy;
  @Column(name = "last_modified_by")
  public String lastModifiedBy;
  @Column(name = "package")
  public String packageStr;
  @Column(name = "version")
  public int version;
  @Column(name = "create_time")
  public long createTime;
  @Column(name = "last_modified_time")
  public long lastModifiedTime;
  @Column(name = "is_deleted")
  public int isDeleted;
  @Column(name = "action")
  public String action;
  @Column(name = "theme_api_name")
  public String themeApiName;
  @Column(name = "check_level_type")
  public String checkLevelType;
  @Column(name = "check_cycle")
  public String checkCyCle;
  @Column(name = "check_level_field_api_name")
  public String checkLevelFieldApiName;
  @Column(name = "subgoal_object_join_field_id")
  public String subgoalObjectJoinFieldId;
  @Column(name = "dept_field_api_name")
  private String deptFieldApiName;

  @Column(name = "time_zone")
  public String timeZone;
  @DynamicTypeHandler(value = "com.github.mybatis.handler.list.ListTypeHandler")
  @Column(name = "check_measure_fields")
  private List<String> checkMeasureFields;
  @Column(name = "check_dimension_fields")
  private String checkDimensionFields;
  @Column(name = "start_week")
  private String startWeek;
  @Column(name = "start_quarter")
  private String startQuarter;

  public String findSubGoalBiObjApiName(MappingService mappingService) {
    return StringUtils.isBlank(this.subgoalObjectApiName) ? null : mappingService.biApiName(this.subgoalObjectApiName);
  }

  /**
   * 检出所有考核字段id
   *
   * @return
   */
  public List<String> findCheckDimensionIds() {
    if (StringUtils.isNotBlank(checkDimensionFields)) {
      JSONArray dimFields = JSON.parseArray(checkDimensionFields);
      return dimFields.stream().map(field -> {
        JSONObject fieldObj = (JSONObject) field;
        return fieldObj.getString("dimension_id");
      }).toList();
    }
    return null;
  }

  /**
   * 构建GoalRule对象
   *
   * @return {@link GoalRuleBO}
   */
  public GoalRuleBO createGoalRuleBO() {
    return GoalRuleBO.builder()
                     .tenantId(tenantId)
                     .id(id)
                     .name(name)
                     .checkCyCle(StringUtils.isBlank(checkCyCle) ? "month" : checkCyCle)
                     .checkLevelType(checkLevelType)
                     .checkMeasureFields(checkMeasureFields)
                     .checkDimensionFields(checkDimensionFields)
                     .startMonth(startMonth)
                     .startWeek(startWeek)
                     .startQuarter(startQuarter)
                     .deptFieldApiName(deptFieldApiName)
                     .themeApiName(themeApiName)
                     .status(status)
                     .timeZone(StringUtils.isBlank(timeZone) ? "Asia/Shanghai" : timeZone)
                     .defineType(defineType)
                     .objectDescribeApiName(objectDescribeApiName)
                     .description(description)
                     .countFiscalYear(countFiscalYear)
                     .version(version)
                     .isDeleted(isDeleted)
                     .build();
  }
}
