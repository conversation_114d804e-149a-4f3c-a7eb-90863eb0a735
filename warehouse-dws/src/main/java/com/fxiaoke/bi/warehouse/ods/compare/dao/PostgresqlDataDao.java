package com.fxiaoke.bi.warehouse.ods.compare.dao;

import com.fxiaoke.bi.warehouse.common.util.SQLUtil;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.ods.compare.arg.ColumnInfo;
import com.fxiaoke.bi.warehouse.ods.compare.util.SqlUtil;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class PostgresqlDataDao {

    @Resource
    private PgDataSource pgDataSource;

    /**
     * 根据pgdb和schema查询表的行数
     */
    public Map<String, Long> queryTableRowsByDb(String dbUrl, String schemaName, Set<String> tableList) {
        if (CollectionUtils.isEmpty(tableList)) {
            return Maps.newHashMap();
        }
        Map<String, Long> tableToRowsMap = Maps.newHashMap();
        String inTable = SQLUtil.generateInExpress((tableList.stream().toList()));
        try (JdbcConnection jdbcConnection = pgDataSource.getConnectionByPgbouncerURL(dbUrl, schemaName)) {
            jdbcConnection.query(String.format(SqlUtil.queryPostgreTableRows, inTable, schemaName), resultSet -> {
                while (resultSet.next()) {
                    tableToRowsMap.put(resultSet.getString(1), resultSet.getLong(2));
                }
            });
        } catch (Exception e) {
            log.error("queryTableRowsByDb error dbUrl:{}, schemaName:{}", dbUrl, schemaName, e);
        }
        return tableToRowsMap;
    }

    /**
     * 根据pgdb和schema查询表的字段类型
     */
    public List<ColumnInfo> queryTableColumnInfoList(String dbUrl, String schemaName, String tableName) {
        List<ColumnInfo> columnInfoList = Lists.newArrayList();
        try (JdbcConnection jdbcConnection = pgDataSource.getConnectionByPgbouncerURL(dbUrl, schemaName)) {
            jdbcConnection.query(String.format(SqlUtil.queryPostgreTableColumns, tableName, schemaName), resultSet -> {
                while (resultSet.next()) {
                    ColumnInfo columnInfo = new ColumnInfo(resultSet.getString(1), resultSet.getString(2));
                    columnInfoList.add(columnInfo);
                }
            });
        } catch (Exception e) {
            log.error("queryTableColumnInfoList error dbUrl:{}, schemaName:{}", dbUrl, schemaName, e);
        }
        return columnInfoList;
    }

    /**
     * 查询抽样表数量
     */
    public long querySimpleTableRowsByTenantId(String dbUrl, String schemaName, Function<String, String> function, String tenantId) {
        final long[] simpleTableCount = {0};
        try (JdbcConnection jdbcConnection = pgDataSource.getConnectionByPgbouncerURL(dbUrl, schemaName)) {
            jdbcConnection.query(function.apply(tenantId), resultSet -> {
                if (resultSet.next()) {
                    simpleTableCount[0] = resultSet.getLong(1);
                }
            });
            return simpleTableCount[0];
        } catch (Exception e) {
            log.error("querySimpleTableRowsByTenantId error chDbUrl:{}", dbUrl, e);
        }
        return simpleTableCount[0];
    }

    /**
     * 查询待同步ch的没有触发器的pg表
     */
    public List<String> queryNotTriggerPgTableList(String dbUrl, String schemaName, Set<String> neededToSyncTableList) {
        if (CollectionUtils.isEmpty(neededToSyncTableList)) {
            return Lists.newArrayList();
        }
        List<String> notTriggerPgTableList = Lists.newArrayList();
        String tableWhereStr = neededToSyncTableList.stream().map(x -> "'" + x + "'").collect(Collectors.joining(","));
        try (JdbcConnection jdbcConnection = pgDataSource.getConnectionByPgbouncerURL(dbUrl, schemaName)) {
            jdbcConnection.query(String.format(SqlUtil.queryNotTriggerTable, schemaName, tableWhereStr), resultSet -> {
                while (resultSet.next()) {
                    notTriggerPgTableList.add(resultSet.getString(1));
                }
            });
        } catch (Exception e) {
            log.error("queryNotTriggerPgTableList error dbUrl:{}, schemaName:{}", dbUrl, schemaName, e);
        }
        return notTriggerPgTableList;
    }
}
