package com.fxiaoke.bi.warehouse.dws.transform.db.mapper.provider;

import com.github.mybatis.annotation.DynamicTypeHandler;
import com.github.mybatis.handler.list.ListTypeHandler;
import com.github.mybatis.handler.set.SetTypeHandler;
import com.github.mybatis.util.EntityUtil;
import com.github.mybatis.util.PersistMeta;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import java.lang.reflect.Field;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.github.mybatis.provider.CrudProvider.FIELD_LEFT;
import static com.github.mybatis.provider.CrudProvider.FIELD_RIGHT;

/**
 * @Author:jief
 * @Date:2024/5/3
 */
@Slf4j
public class CASProvider {
  /**
   * @param version
   * @return
   */
  public String updateWithVersion(@Param("version") Integer version,
                                  @Param("obj") Object obj,
                                  @Param("primaryKeys") Map<String, String> pkMap) {
    PersistMeta meta = EntityUtil.getMeta(obj.getClass());
    Iterator<Map.Entry<String, Field>> columnsEntryIter = meta.getColumns().entrySet().iterator();
    StringBuilder updateFields = new StringBuilder();
    Set<String> pks = pkMap.keySet();
    int i = 0;
    while (columnsEntryIter.hasNext()) {
      Map.Entry<String, Field> kv = columnsEntryIter.next();
      Field field = kv.getValue();
      if (pks.contains(kv.getKey())) {
        continue;
      }
      if (i++ != 0) {
        updateFields.append(',');
      }
      updateFields.append(FIELD_LEFT).append(kv.getKey()).append(FIELD_RIGHT);
      DynamicTypeHandler typeHandler = field.getAnnotation(DynamicTypeHandler.class);
      if (typeHandler == null) {
        if (field.getType().isAssignableFrom(List.class)) {
          updateFields.append(" = #{obj.")
                      .append(field.getName())
                      .append(",typeHandler=")
                      .append(ListTypeHandler.class.getName())
                      .append("}");
        } else if (field.getType().isAssignableFrom(Set.class)) {
          updateFields.append(" = #{ obj.")
                      .append(field.getName())
                      .append(",typeHandler=")
                      .append(SetTypeHandler.class.getName())
                      .append("}");
        } else {
          updateFields.append(" = #{obj.").append(field.getName()).append("}");
        }
      } else {
        updateFields.append(" = #{obj.")
                    .append(field.getName())
                    .append(",typeHandler=")
                    .append(typeHandler.value())
                    .append("}");
      }
    }
    SQL sql = new SQL();
    sql.UPDATE(this.getTableName(meta, obj));
    sql.SET(updateFields.toString());
    StringBuilder where = new StringBuilder();
    where.append("1=1");
    pkMap.forEach((k, v) -> {
      where.append(" AND ").append(k).append(" = ").append("#{obj.").append(v).append("}");
    });
    sql.WHERE(where + " and version=#{version}");
    return sql.toString();
  }

  protected String getTableName(PersistMeta meta, Object obj) {
    if (meta.getPostfix() != null) {
      try {
        return meta.getTableName() + '_' + meta.getPostfix().invoke(obj);
      } catch (Exception var4) {
        log.error("cannot invoke postfix: {}", meta.getPostfix(), var4);
      }
    }

    return meta.getTableName();
  }
}
