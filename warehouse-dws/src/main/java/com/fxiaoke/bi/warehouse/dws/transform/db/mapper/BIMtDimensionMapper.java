package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtDimensionDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BIMtDimensionMapper extends IBatchMapper<BIMtDimensionDO>, ITenant<BIMtDimensionMapper> {

  @Select("select * from bi_mt_dimension where tenant_id=#{tenantId} and topology_describe_id=#{topologyDescribeId}")
  List<BIMtDimensionDO> findDimByTopologyDescribeId(@Param("tenantId") String tenantId, @Param("topologyDescribeId") String topologyDescribeId);
  @Select("select * from bi_mt_dimension where tenant_id=#{tenantId} and split_part(topology_describe_id,'|',1) = #{topologyDescribeIds}")
  List<BIMtDimensionDO> findDimsByParentGoalId(@Param("tenantId") String tenantId, @Param("topologyDescribeIds") String topologyDescribeIds);

  @Select("SELECT * FROM bi_mt_dimension WHERE tenant_id = #{tenantId} AND dimension_id ${dimensionIds} AND status IN (1, 2) AND is_deleted = 0")
  List<BIMtDimensionDO> findDimByFieldIds(@Param("tenantId") String tenantId, @Param("dimensionIds") String dimensionIds);

  @Select("SELECT * FROM bi_mt_dimension WHERE tenant_id = #{ei} AND dimension_id ${dimensionIds} AND status = ANY(ARRAY[#{status}])")
  List<BIMtDimensionDO> findDimByDimensionIds(@Param("ei") String ei, @Param("dimensionIds") String dimensionIds, @Param("status") Integer[] status);

  @Select("select * from bi_mt_dimension where tenant_id=#{tenantId} and topology_describe_id=#{topologyDescribeId} and " +
  " dimension_id in (select jsonb_array_elements(check_dimension_fields::jsonb) ->> 'dimension_id' from goal_rule where tenant_id =#{tenantId} and id =#{topologyDescribeId})")
  List<BIMtDimensionDO> findDimByTopologyDescribeIdAndGoal(@Param("tenantId") String tenantId, @Param("topologyDescribeId") String topologyDescribeId);

}
