package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.helper.StringHelper;
import lombok.experimental.SuperBuilder;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 数据Id列
 */
@SuperBuilder
public class ObjectIdRule extends QuoteOfAgg<AggRule> {

  /**
   * 目标计算，只计算指定维度聚合
   */
  public Map<String, List<String>> dimensionIn;

  @Override
  public void init(AggRule aggRule) {
    //处理what，特殊字段
    fix(aggRule.objectIdTable());
    //归位到AggApiOpLogListener
    //目标的还要侦听object_id对象的变化，所以还有点麻烦
    if (aggRule.objectIdApiIsAggApi()) {
      return;
    }
  }

  /**
   * select field sql
   *
   * @param table
   * @param tableAlias
   * @return
   */
  public String objectIdSelectSQL(String table, String tableAlias) {
    String columnName = columnSelectSQL(table);

    //员工部门数组
    if (Utils.isSlotColumn(column)) {
      if (FieldType.EMP_DEPT.contains(fieldType)) {
        return " unnest(string_to_array(NULLIF(replace(replace(trim(" + tableAlias + "." + columnName +
          ", '{[]}'), '|', ','), '\"', ''), ''), ','))";
      }
    }
    if ("approval_task".equals(table)) {
      if ("candidate_ids".equals(columnName) || "dealed_persons".equals(columnName)) {
        return " unnest(" + tableAlias + "." + columnName + ") ";
      }
    }
    //处理数组
    if (columnType == PGColumnType.ARRAY_String || columnType == PGColumnType.ARRAY_Int16 || columnType == PGColumnType.ARRAY_Int32 || columnType == PGColumnType.ARRAY_Int64) {
      return " unnest(" + tableAlias + "." + columnName + ") ";
    }
    //处理字符串
    if (!isSingle) {
      return " unnest(string_to_array(NULLIF(replace(replace(trim(" + tableAlias + "." + columnName +
        ", '{[]}'), '|', ','), '\"', ''), ''), ','))";
    }

    return tableAlias + "." + columnName;
  }

  public String objectIdWhereSQL(String table, String tableAlias, Collection<String> objectIds) {
    Collection<String> ids = Utils.stringsToArray(objectIds);
    String columnName = columnSelectSQL(table);
    if ("approval_task".equals(table)) {
      if ("candidate_ids".equals(columnName) || "dealed_persons".equals(columnName)) {
        return tableAlias + "." + columnSelectSQL(table) + " && " + "'{" + JoinHelper.joinSkipNullOrBlank(',',
                                                                                                          '"',
                                                                                                          ids) + "}'";
      }
    }
    if (columnType == PGColumnType.ARRAY_String) {
      return tableAlias + "." + columnSelectSQL(table) + " && " + "'{" + JoinHelper.joinSkipNullOrBlank(',', '"', ids) +
        "}'";
    }
    //处理数组列
    if (!isSingle) {
      return "string_to_array(NULLIF(replace(replace(trim(" + tableAlias + "." + columnName +
        ", '{[]}'), '|', ','), '\"', ''), ''), ',') && " + "'{" + JoinHelper.joinSkipNullOrBlank(',', '"', ids) + "}'";
    }
    //    return tableAlias + "." + columnSelectSQL(table) + " IN (" + JoinHelper.joinSkipNullOrBlank(',', '\'', (ids)) + ")";
    return tableAlias + "." + columnSelectSQL(table) + " = ANY('{" + JoinHelper.joinSkipNullOrBlank(',', ids) + "}')";
  }

  /**
   * select 别名
   *
   * @return
   */
  public String columnAlias() {
    return "object_id";
  }

  private void fix(String table) {
    if ("value0".equals(column)) {
      if (table.endsWith("__c")) {
        column = "id";
      }
      if (!"object_data".equals(table)) {
        column = "id";
      }
    }
    //特殊处理what字段类型
    if (table.equals("approval_task") || table.equals("approval_instance")) {
      if (column.equals("relatedObject")) {
        column = "object_data_id";
      }
    }
    if (table.equals("bpm_instance") || table.equals("bpm_task")) {
      if (column.equals("relatedObject")) {
        column = "objectDataId";
      }
    }
    if (table.equals("behavior_integral_detail")) {
      if (column.equals("relatedObject")) {
        column = "object_id";
      }
    }
  }

  /**
   * 列名, SQL用
   *
   * @param table
   * @return
   */
  public String columnSelectSQL(String table) {
    String columnName = column;
    //大写字母列需要加双引号
    if (StringHelper.containUpperChar(columnName)) {
      columnName = "\"" + columnName + "\"";
    }
    return columnName;
  }
}
