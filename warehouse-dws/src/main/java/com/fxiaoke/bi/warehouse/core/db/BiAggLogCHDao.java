package com.fxiaoke.bi.warehouse.core.db;

import com.fxiaoke.bi.warehouse.core.db.entity.BiAggLogDO;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.jdbc.JdbcConnection;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author:jief
 * @Date:2024/12/11
 */
@Slf4j
@Service
public class BiAggLogCHDao {
  @Resource
  private CHDataSource chDataSource;

  public void batchInsertAggLog(String jdbcURL, List<BiAggLogDO> biAggLogDOS) {
    if (CollectionUtils.isEmpty(biAggLogDOS)) {
      log.warn("biAggLogDOS is empty jdbcURL:{}", jdbcURL);
      return;
    }
    String insertSQL= """
      insert into bi_agg_log(tenant_id, view_id,view_version,batch_num,field_id,cost) select tenant_id, view_id,view_version,batch_num,field_id,cost
      from input('tenant_id String, view_id String, view_version Int32,batch_num Int64,field_id String,cost Int64')
      SETTINGS async_insert = 1,wait_for_async_insert = 1,async_insert_busy_timeout_ms = 1000,async_insert_max_data_size = 1000000,async_insert_max_query_number = 450
      """;
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(jdbcURL)) {
      jdbcConnection.prepareUpdateBatch(insertSQL,preparedStatement -> {
        biAggLogDOS.forEach(log->{
          try{
            preparedStatement.setString(1,log.getTenantId());
            preparedStatement.setString(2,log.getViewId());
            preparedStatement.setInt(3,log.getViewVersion());
            preparedStatement.setLong(4,log.getBatchNum());
            preparedStatement.setString(5,log.getFieldId());
            preparedStatement.setLong(6,log.getCost());
            preparedStatement.addBatch();
          }catch (Exception e1){
            throw new RuntimeException(e1);
          }
        });
      });
    } catch (Exception e) {
      log.error("batchInsertAggLog error jdbcURL:{}",jdbcURL,e);
    }
  }
}
