package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum PolicyLogEnum {

    SUCCESS("success", "成功", "同步成功"),
    EXCEPTION("exception", "异常", "异常原因"),
    ERROR("fail", "失败", "同步失败");

    private String logType;

    private String desc;

    private String pushRptDesc;

    public static PolicyLogEnum getPolicyLogEnumByLogType(String logType) {
        for (PolicyLogEnum policyLogEnum : values()) {
            if (StringUtils.equals(logType, policyLogEnum.getLogType())) {
                return policyLogEnum;
            }
        }
        return SUCCESS;
    }
}
