package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * stat_view 实体类
 */
@Table(name = "rpt_view")
@Data
public class RptViewDO {
  @Column(name = "view_id")
  private String viewId;
  @Column(name = "view_name")
  private String viewName;
  @Column(name = "ei")
  private int ei;
  @Column(name = "creator")
  private int creator;
  @Column(name = "create_time")
  private Date createTime;
  @Column(name = "updator")
  private int updator;
  @Column(name = "update_time")
  private Date updateTime;
  @Column(name = "is_delete")
  private int isDelete;
  @Column(name = "rpt_type")
  private int rptType;
  @Column(name = "data_source")
  private String dataSource;
  @Column(name = "time_zone")
  private String timeZone;
}
