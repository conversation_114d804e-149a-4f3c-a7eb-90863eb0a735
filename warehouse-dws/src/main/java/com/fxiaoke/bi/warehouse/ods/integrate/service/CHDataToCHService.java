package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

public interface CHDataToCHService {
  /**
   * 同步用户操作数据
   */
  void transferOperationDataToCH(AtomicLong nextBatchNum,
                                 DBSyncInfoBO dbSyncInfoCopy,
                                 List<String> tenantIds,
                                 Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                 String partitionName);
    /**
     * 迁移数据Login
     */
    String transferTenantLoginData(String tenantId);


    /**
     * 迁移数据ApiName
     */
    String transferTenantApiNameData(String tenantId);
}
