package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.goal.BITopology;
import com.fxiaoke.bi.warehouse.common.goal.Edge;
import com.fxiaoke.bi.warehouse.common.goal.Node;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.ibatis.jdbc.SQL;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiMtRule {
  private String tenantId;
  private String topologyDescribeId;
  private String measureFieldId; //统计图明细主题的field_id
  private Integer sourceType;
  private int detailSourceType;
  private int version;
  private boolean standalone;
  private GoalRuleBO goalRuleBO;
  private BITopology biTopology;
  private List<BiMtDimRule> biMtDimRules;
  private List<BiMtMeasureRule> biMtMeasureRules;
  private List<BiMtDetailRule> biMtDetailRules;

  public JSONArray goalWheres() {
    return this.biTopology.getWheres();
  }

  public String timeZone() {
    return goalRuleBO.getTimeZone();
  }

  public String goalRuleId() {
    return goalRuleBO.getId();
  }

  /**
   * 生成反查goal_value 表的sql
   *
   * @param goalRuleId          目标id
   * @param fromSysModifiedTime 起始时间戳
   * @param toSysModifiedTime   截止时间戳
   * @param isDeleted           删除状态
   * @return sql
   */
  public String createGoalValueSQL(String goalRuleId,
                                   Long fromSysModifiedTime,
                                   Long toSysModifiedTime,
                                   Integer isDeleted) {
    List<Pair<String, String>> checkFieldLocation = findFieldLocationPair();
    String[] columns = checkFieldLocation.stream().map(item -> item.second).toArray(String[]::new);
    SQL sql = new SQL();
    sql.SELECT(columns).FROM("goal_value").WHERE("tenant_id='" + tenantId + "'", "goal_rule_id='" + goalRuleId + "'");
    if (fromSysModifiedTime != null) {
      sql.WHERE("sys_modified_time > " + fromSysModifiedTime);
    }
    if (toSysModifiedTime != null) {
      sql.WHERE("sys_modified_time <= " + toSysModifiedTime);
    }
    if (isDeleted != null) {
      sql.WHERE("is_deleted = " + isDeleted);
    }
    sql.WHERE("bi_sys_flag = 1");
    return sql.toString();
  }

  /**
   * 字段和fieldLocation对
   *
   * @return List<Pair < String, String>>
   */
  public List<Pair<String, String>> findFieldLocationPair() {
    JSONArray jsonArray = JSONArray.parseArray(goalRuleBO.getCheckDimensionFields());
    return jsonArray.stream().map(item -> {
      JSONObject dimFieldOBj = (JSONObject) item;
      return Pair.build(dimFieldOBj.getString("dimension_id"), dimFieldOBj.getString("field_location"));
    }).toList();
  }

  /**
   * 生成 考核字段映射关系
   * @param subGoalRuleDimMap 子目标维度映射 or 查看明细维度映射 <dimension_id, field_id>
   * @param nameMtIdList 查看明细维度映射name转id
   * @return Map<String, String>
   */
  public Map<String, String> checkFieldLocationMapper(Map<String, String> subGoalRuleDimMap, List<String/*name的mtId*/> nameMtIdList) {
    Map<String, String> checkFieldMapper = Maps.newHashMap();
    JSONArray jsonArray = JSONArray.parseArray(goalRuleBO.getCheckDimensionFields());
    Map<String, String> fieldIdDimIdMap;
    //报表（目标查看明细）的考核维度从mt_detail中取
    if (sourceType == 3) {
      fieldIdDimIdMap = biMtDetailRules.stream()
              .collect(Collectors.toMap(BiMtDetailRule::getDetailField, BiMtDetailRule::getDetailId));
    } else {
      fieldIdDimIdMap = biMtDimRules.stream()
              .collect(Collectors.toMap(BiMtDimRule::getFieldId, BiMtDimRule::getDimensionId));
    }
    jsonArray.forEach(item -> {
      JSONObject dimFieldOBj = (JSONObject) item;
      String dimensionId = dimFieldOBj.getString("dimension_id");
      if (MapUtils.isNotEmpty(subGoalRuleDimMap)) {
        String fieldId = subGoalRuleDimMap.get(dimensionId);
        String mtId = fieldIdDimIdMap.get(fieldId);
        String finalMtId = nameMtIdList.contains(mtId) ? mtId + Constants._ID : mtId;
        checkFieldMapper.put(finalMtId, dimFieldOBj.getString("field_location"));
      } else {
        checkFieldMapper.put(dimensionId, dimFieldOBj.getString("field_location"));
      }
    });
    return checkFieldMapper;
  }

  /**
   * 考核对象的id合日期字段pair
   *
   * @return node id
   */
  public Map<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> createMeasureRuleMapper() {
    //处理一个拓扑图中多个计算指标
    Map<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> measureRuleMaps = Maps.newHashMap();
    biMtMeasureRules.forEach(item -> {
      Pair<String, String> key = Pair.build(item.getNodeId(), item.getActionDateField());
      measureRuleMaps.computeIfAbsent(key, k -> Lists.newArrayList()).add(item);
    });
    return measureRuleMaps;
  }

  /**
   * 过滤掉action_date字段
   *
   * @return 过滤掉action_date字段后的集合
   */
  public Map<String/*nodeId*/, List<BiMtDimRule>> filterActionDateDim() {
    Map<String/*nodeId*/, List<BiMtDimRule>> nodeFieldMap = Maps.newHashMap();
    biMtDimRules.stream()
                .filter(dimRule -> !Objects.equals(Constants.ACTION_DATE, dimRule.getDimensionType()))
                .toList()
                .forEach(dimRule -> {
                   String[] dimField = dimRule.getDimensionField().split("\\.");
                   nodeFieldMap.computeIfAbsent(dimField[0], key -> Lists.newArrayList()).add(dimRule);
                 });
    return nodeFieldMap;
  }

  /**
   * 字段去重
   *
   * @return 查询字段去重
   */
  public Map<String/*nodeId*/, Multimap<String/*dbFieldName*/, String/*fieldId*/>> findDbFieldNames() {
    Map<String/*nodeId*/, Multimap<String/*dbFieldName*/, String/*fieldId*/>> nodeFieldMap = Maps.newHashMap();
    if (CollectionUtils.isNotEmpty(biMtDimRules)) {
      biMtDimRules.forEach(rule -> {
        Multimap<String, String> dbFieldMap = nodeFieldMap.computeIfAbsent(rule.nodeIdFieldPair().first,
                key -> MultimapBuilder.hashKeys().arrayListValues().build());
          dbFieldMap.put(rule.nodeIdFieldPair().second, rule.getDimensionId());
        });
    }
    if (CollectionUtils.isNotEmpty(biMtDetailRules)) {
      biMtDetailRules.forEach(rule -> {
        Multimap<String, String> dbFieldMap = nodeFieldMap.computeIfAbsent(rule.nodeIdFieldPair().first,
                key -> MultimapBuilder.hashKeys().arrayListValues().build());
        dbFieldMap.put(rule.nodeIdFieldPair().second, rule.getDetailId());
      });
    }
    if (CollectionUtils.isNotEmpty(biMtMeasureRules)) {
      biMtMeasureRules.forEach(rule -> {
        Multimap<String, String> dbFieldMap = nodeFieldMap.computeIfAbsent(rule.getNodeId(),
                key -> MultimapBuilder.hashKeys().arrayListValues().build());
        dbFieldMap.put(rule.findValueField(), rule.getMeasureId());
      });
    }
    return nodeFieldMap;
  }

  /**
   * 根据node id获取node
   *
   * @param nodeId nodeId
   * @return
   */
  public Node findNodeById(String nodeId) {
    Optional<Node> nodeOptional = this.biTopology.findNodeById(nodeId);
    return nodeOptional.orElseThrow(() -> new RuntimeException(String.format("can not find node nodeId:%s:%s",
      tenantId, nodeId)));
  }

  public List<Edge> findLookupList(String fromId, String toId) {
    List<Edge> lookupList = Lists.newArrayList();
    this.biTopology.findLookupList(new AtomicInteger(0), lookupList, fromId, toId);
    Collections.reverse(lookupList);
    return lookupList;
  }


  /**
   * 报表获取主对象
   *
   * @return node id
   */
  public Node findRootNode() {
    //处理一个拓扑图中多个计算指标
    Map<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> measureRuleMaps = Maps.newHashMap();
    for (Node node : biTopology.getNodes()) {
      if (node.isRoot()) {
        return node;
      }
    }
    return null;
  }
}
