package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.alibaba.fastjson.JSONArray;
import com.fxiaoke.bi.warehouse.common.goal.BITopology;
import com.fxiaoke.bi.warehouse.common.goal.Edge;
import com.fxiaoke.bi.warehouse.common.goal.Node;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RptViewMTRule {
  private String tenantId;
  private boolean standalone;
  private BITopology biTopology;
  private List<BiMtDimRule> biMtDimRules;
  private List<BiMtMeasureRule> biMtMeasureRules;
  private List<BiMtDetailRule> biMtDetailRules;

  public JSONArray goalWheres() {
    return this.biTopology.getWheres();
  }

  /**
   * 考核对象的id合日期字段pair
   *
   * @return node id
   */
  public Map<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> createMeasureRuleMapper() {
    //处理一个拓扑图中多个计算指标
    Map<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> measureRuleMaps = Maps.newHashMap();
    biMtMeasureRules.forEach(item -> {
      Pair<String, String> key = Pair.build(item.getNodeId(), item.getActionDateField());
      measureRuleMaps.computeIfAbsent(key, k -> Lists.newArrayList()).add(item);
    });
    return measureRuleMaps;
  }

  /**
   * 过滤掉action_date字段
   *
   * @return 过滤掉action_date字段后的集合
   */
  public Map<String/*nodeId*/, List<BiMtDimRule>> filterActionDateDim() {
    Map<String/*nodeId*/, List<BiMtDimRule>> nodeFieldMap = Maps.newHashMap();
    biMtDimRules.stream()
                .filter(multiDimRule -> !Objects.equals(Constants.ACTION_DATE, multiDimRule.getDimensionType()))
                .toList()
                .forEach(multiDimRule -> {
                   String[] dimField = multiDimRule.getDimensionField().split("\\.");
                   nodeFieldMap.computeIfAbsent(dimField[0], key -> Lists.newArrayList()).add(multiDimRule);
                 });
    return nodeFieldMap;
  }

  /**
   * 根据node id获取node
   *
   * @param nodeId nodeId
   * @return
   */
  public Node findNodeById(String nodeId) {
    Optional<Node> nodeOptional = this.biTopology.findNodeById(nodeId);
    return nodeOptional.orElseThrow(() -> new RuntimeException(String.format("can not find node nodeId:%s:%s",
      tenantId, nodeId)));
  }

  public List<Edge> findLookupList(String fromId, String toId) {
    List<Edge> lookupList = Lists.newArrayList();
    this.biTopology.findLookupList(new AtomicInteger(0), lookupList, fromId, toId);
    Collections.reverse(lookupList);
    return lookupList;
  }
}
