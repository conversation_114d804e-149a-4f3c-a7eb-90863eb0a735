package com.fxiaoke.bi.warehouse.dws.agg.service.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.arg.AggRequestContext;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.dws.agg.bean.AggCalResult;
import com.fxiaoke.bi.warehouse.dws.agg.service.AbstractAggHandler;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBAggInfoDAO;
import com.fxiaoke.bi.warehouse.dws.model.DBUpdatedEvent;
import com.fxiaoke.bi.warehouse.dws.service.DWSComputeService;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * @Author:jief
 * @Date:2024/12/7
 */
@Slf4j
public class AggInc2CalHandler extends AbstractAggHandler<Boolean> {
  @Resource
  private DBAggInfoDAO dbAggInfoDAO;
  @Override
  public Boolean doHandler(AggRequestContext aggRequestContext) {
    DBUpdatedEvent event = aggRequestContext.getRequestArg("DBUpdatedEvent", DBUpdatedEvent.class);
    StopWatch stopWatch = aggRequestContext.getRequestArg("StopWatch", StopWatch.class);
    AggCalResult aggCalResult = aggRequestContext.getRequestArg("AggCalResult", AggCalResult.class);
    Preconditions.checkArgument(event != null, "DBUpdatedEvent is null!");
    Preconditions.checkArgument(stopWatch != null, "StopWatch is null!");
    stopWatch.start("AggInc2CalHandler");
    if (aggCalResult == null) {
      aggCalResult = this.prepare(event, stopWatch);
    }
    try {
      Integer result = dbAggInfoDAO.updateDBAggInfoStatus(event.getId(), SyncStatusEnum.INC_2_CAL_AFTER_ING.getStatus(), event.getAggInfoVersion());
      if (result != null) {
        event.setAggInfoVersion(result);
        DWSComputeService.copyInc2CalAfterIng.add(event.getId());
        this.doInsertSQLs(event, aggCalResult.getCalPrepares(), SyncStatusEnum.INC_2_CAL_AFTER_ING.getStatus(), false);
        log.info("inc2CalAfter success,pgDB:{},chDB:{},pgSchema:{},batchNums:{}", event.getPgJdbcUrl(), event.getChJdbcUrl(), event.getPgSchema(), JSON.toJSONString(event.getBatchNums()));
        DWSComputeService.copyInc2CalAfterIng.remove(event.getId());
        if (this.getNextHandler() != null) {
         return this.getNextHandler().doHandler(aggRequestContext);
        }
        return true;
      }
    } finally {
      DWSComputeService.copyInc2CalAfterIng.remove(event.getId());
      stopWatch.stop("AggInc2CalHandler");
    }
    return false;
  }
}
