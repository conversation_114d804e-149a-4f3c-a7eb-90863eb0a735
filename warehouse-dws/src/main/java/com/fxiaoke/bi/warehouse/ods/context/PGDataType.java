package com.fxiaoke.bi.warehouse.ods.context;

import com.google.common.collect.Sets;

import java.util.Set;

public interface PGDataType {
  String VARCHAR = "varchar";
  String BPCHAR = "bpchar";
  String TEXT = "text";
  String INT2 = "int2";
  String INT4 = "int4";
  String INT8 = "int8";

  String NUMERIC = "numeric";
  String FLOAT8 = "float8";
  String FLOAT4 = "float4";

  String BIT = "bit";
  String VARBIT = "varbit";

  String JSON = "json";
  String JSONB = "jsonb";

  String TIMESTAMP = "timestamp";
  String TIMESTAMPTZ = "timestamptz";
  String DATE = "date";
  String TIME = "time";
  String TIMEZ = "timetz";


  String _NUMERIC = "_numeric";


  String LTREE = "ltree";

  String LINE = "line";
  String POINT = "point";
  String GEOGRAPHY = "geography";
  String PATH = "path";
  String LSEQ = "lseg";
  String MONEY = "money";
  String MACADDR = "macaddr";
  String BOOL = "bool";

  Set<String> NOT_NULL_FIELDS= Sets.newHashSet("create_time","last_modified_time","update_time","is_deleted");

}
