package com.fxiaoke.bi.warehouse.dws.utils.sql;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 硬编码的特殊计算表达式
 */
public class HardCodeCalculateExpression {
  /**
   * The constant CURRENT_STAGE_REMAIN_TIME.
   */
  public static final String CURRENT_STAGE_REMAIN_TIME = "stage_runtime|current_stage_remain_time";
  /**
   * The constant FACT_TIME_OUT_TIME.
   */
  public static final String FACT_TIME_OUT_TIME = "stage_runtime|fact_time_out_time";
  /**
   * The constant APPROVAL_INSTANCE_DURATION.
   */
  public static final String APPROVAL_INSTANCE_DURATION = "approval_instance|duration";
  /**
   * The constant APPROVAL_TASK_DURATION.
   */
  public static final String APPROVAL_TASK_DURATION = "approval_task|duration";
  /**
   * The constant BPM_INSTANCE_DURATION.
   */
  public static final String BPM_INSTANCE_DURATION = "bpm_instance|duration";
  /**
   * The constant BPM_TASK_DURATION.
   */
  public static final String BPM_TASK_DURATION = "bpm_task|duration";
  /**
   * The constant BPM_TASK_TIMEOUT_TIME1.
   */
  public static final String BPM_TASK_TIMEOUT_TIME1 = "bpm_task|\"timeoutTime\"";
  /**
   * The constant BPM_TASK_TIMEOUT_TIME.
   */
  public static final String BPM_TASK_TIMEOUT_TIME = "bpm_task|timeoutTime";
  /**
   * The constant APPROVAL_INSTANCE_TIMEOUT_TIME.
   */
  public static final String APPROVAL_INSTANCE_TIMEOUT_TIME = "approval_instance|timeout_time";
  /**
   * The constant APPROVAL_TASK_TIMEOUT_TIME.
   */
  public static final String APPROVAL_TASK_TIMEOUT_TIME = "approval_task|timeout_time";
  public static final String FLOW_TASK_DURATION_C = "flow_task_handle_time_detail|duration_c";

  /**
   * The constant CALCULATED_FIELDS_PRESET.
   */
  public static final ImmutableList<String> CALCULATED_FIELDS_PRESET = ImmutableList.of(CURRENT_STAGE_REMAIN_TIME, FACT_TIME_OUT_TIME, APPROVAL_INSTANCE_DURATION, APPROVAL_TASK_DURATION, BPM_INSTANCE_DURATION, BPM_TASK_DURATION, BPM_TASK_TIMEOUT_TIME, BPM_TASK_TIMEOUT_TIME1, APPROVAL_INSTANCE_TIMEOUT_TIME, APPROVAL_TASK_TIMEOUT_TIME, FLOW_TASK_DURATION_C);

  /**
   * The constant CURRENT_STAGE_REMAIN_TIME_SQL.
   */
  public static final String CURRENT_STAGE_REMAIN_TIME_SQL = " CASE WHEN $stage_runtime|is_terminal$ = false and  $stage_runtime|stage_status$ like '%in_progress' THEN round( cast (extract(epoch from now())*1000 - $stage_runtime|start_time$::NUMERIC +  COALESCE($stage_runtime|accumulate_time$::NUMERIC, 0) as numeric) ,2)   ELSE round( cast (COALESCE($stage_runtime|accumulate_time$::NUMERIC, 0)  as numeric) ,2)  ::NUMERIC END";
  /**
   * The constant FACT_TIME_OUT_TIME_SQL.
   */
  public static final String FACT_TIME_OUT_TIME_SQL = " CASE WHEN $stage_runtime|is_time_out$ = true   THEN    CASE WHEN  $stage_runtime|is_terminal$ = false and $stage_runtime|stage_status$ like '%in_progress' THEN  round( cast (extract(epoch from now())*1000 - $stage_runtime|start_time$::NUMERIC + COALESCE($stage_runtime|accumulate_time$::NUMERIC, 0) - $stage_runtime|remind_latency$::NUMERIC*3600000  as numeric) ,2)   ELSE  round( cast (COALESCE($stage_runtime|accumulate_time$::NUMERIC, 0) - $stage_runtime|remind_latency$::NUMERIC*3600000  as numeric) ,2)  END ELSE 0 END ";
  /**
   * The constant APPROVAL_INSTANCE_DURATION_SQL.
   */
  public static final String APPROVAL_INSTANCE_DURATION_SQL = "CASE WHEN $approval_instance|state$ like '%in_progress' THEN round( cast (extract(epoch from now())*1000 - $approval_instance|start_time$::NUMERIC as numeric) ,2)   WHEN $approval_instance|state$ like '%error' THEN 0  ELSE round( cast ($approval_instance|end_time$::NUMERIC - $approval_instance|start_time$::NUMERIC as numeric) ,2) END";
  /**
   * The constant APPROVAL_TASK_DURATION_SQL.
   */
  public static final String APPROVAL_TASK_DURATION_SQL = "CASE WHEN $approval_task|state$ like '%in_progress'   or  $approval_task|state$ like '%tag_waiting'    THEN round( cast (extract(epoch from now())*1000 - $approval_task|start_time$::NUMERIC as numeric) ,2) WHEN $approval_task|state$ like '%error' THEN  0  ELSE round( cast ($approval_task|last_modify_time$::NUMERIC - $approval_task|start_time$::NUMERIC as numeric) ,2) END";
  /**
   * The constant BPM_INSTANCE_DURATION_SQL.
   */
  public static final String BPM_INSTANCE_DURATION_SQL = "CASE WHEN $bpm_instance|state$ like '%in_progress' THEN  round( cast (extract(epoch from now())*1000 - $bpm_instance|startTime$::NUMERIC as numeric) ,2) WHEN $bpm_instance|state$ like '%error' THEN 0 ELSE  round( cast ($bpm_instance|endTime$::NUMERIC - $bpm_instance|startTime$::NUMERIC  as numeric) ,2)  END ";
  /**
   * The constant BPM_TASK_DURATION_SQL.
   */
  public static final String BPM_TASK_DURATION_SQL = "CASE WHEN $bpm_task|state$ like '%in_progress' THEN round( cast (extract(epoch from now())*1000 - $bpm_task|startTime$::NUMERIC as numeric) ,2)  WHEN $bpm_task|state$ like '%error' THEN  0 ELSE round( cast ($bpm_task|endTime$::NUMERIC - $bpm_task|startTime$::NUMERIC as numeric) ,2) END";
  /**
   * The constant BPM_TASK_TIMEOUT_TIME_SQL.
   */
  public static final String BPM_TASK_TIMEOUT_TIME_SQL = " CASE WHEN $bpm_task|isTimeout$ = true   THEN   CASE WHEN $bpm_task|state$ like '%in_progress' THEN round( cast (extract(epoch from now())*1000 - $bpm_task|startTime$::NUMERIC - $bpm_task|remindLatency$::NUMERIC  as numeric ) ,2)  WHEN $bpm_task|state$ like '%error' THEN 0 ELSE round( cast ($bpm_task|endTime$::NUMERIC - $bpm_task|startTime$::NUMERIC - $bpm_task|remindLatency$::NUMERIC   as numeric) ,2) END  ELSE 0 END ";
  /**
   * The constant APPROVAL_INSTANCE_TIMEOUT_TIME_SQL.
   */
  public static final String APPROVAL_INSTANCE_TIMEOUT_TIME_SQL = " CASE WHEN $approval_instance|is_timeout$ = 'true'   THEN   CASE WHEN $approval_instance|state$ like '%in_progress' THEN round( cast (extract(epoch from now())*1000 - $approval_instance|start_time$::NUMERIC  -$approval_instance|remind_latency$::NUMERIC  as numeric) ,2)   WHEN $approval_instance|state$ like '%error' THEN 0  ELSE round( cast ($approval_instance|end_time$::NUMERIC - $approval_instance|start_time$::NUMERIC  -$approval_instance|remind_latency$::NUMERIC  as numeric) ,2) END ELSE 0 END";
  /**
   * The constant APPROVAL_TASK_TIMEOUT_TIME_SQL.
   */
  public static final String APPROVAL_TASK_TIMEOUT_TIME_SQL = " CASE WHEN $approval_task|is_timeout$ = 'true'   THEN CASE WHEN $approval_task|state$ like '%in_progress'   or  $approval_task|state$ like '%tag_waiting'    THEN round( cast (extract(epoch from now())*1000 - $approval_task|start_time$::NUMERIC - $approval_task|remind_latency$::NUMERIC  as numeric) ,2) WHEN $approval_task|state$ like '%error' THEN  0  ELSE round( cast ($approval_task|last_modify_time$::NUMERIC - $approval_task|start_time$::NUMERIC - $approval_task|remind_latency$::NUMERIC as numeric) ,2) END ELSE 0 END";

  public static final String FLOW_TASK_DURATION_C_SQL = " CASE WHEN $flow_task_handle_time_detail|end_time$ IS NOT NULL AND $flow_task_handle_time_detail|end_time$ <> 0   THEN $flow_task_handle_time_detail|duration$::NUMERIC ELSE round(cast(extract(epoch from now()) * 1000 - $flow_task_handle_time_detail|start_time$::NUMERIC as numeric ),2) END";

  /**
   * The constant CALCULATED_FIELDS_PRESET_MAP.
   */
  public static final ImmutableMap<String, List<String>> CALCULATED_FIELDS_PRESET_MAP = ImmutableMap.<String, List<String>>builder()
                                                                                                    .put(CURRENT_STAGE_REMAIN_TIME, Lists.newArrayList("is_terminal", "stage_status", "start_time", "accumulate_time"))
                                                                                                    .put(FACT_TIME_OUT_TIME, Lists.newArrayList("is_terminal", "stage_status", "start_time", "accumulate_time", "remind_latency", "is_time_out"))
                                                                                                    .put(APPROVAL_INSTANCE_DURATION, Lists.newArrayList("state", "start_time", "end_time"))
                                                                                                    .put(APPROVAL_TASK_DURATION, Lists.newArrayList("state", "start_time", "last_modify_time"))
                                                                                                    .put(BPM_INSTANCE_DURATION, Lists.newArrayList("state", "startTime", "endTime"))
                                                                                                    .put(BPM_TASK_DURATION, Lists.newArrayList("state", "startTime", "endTime"))
                                                                                                    .put(BPM_TASK_TIMEOUT_TIME, Lists.newArrayList("isTimeout", "state", "startTime", "endTime", "remindLatency"))
                                                                                                    .put(BPM_TASK_TIMEOUT_TIME1, Lists.newArrayList("isTimeout", "state", "startTime", "endTime", "remindLatency"))
                                                                                                    .put(APPROVAL_INSTANCE_TIMEOUT_TIME, Lists.newArrayList("state", "start_time", "end_time", "is_timeout", "remind_latency"))
                                                                                                    .put(APPROVAL_TASK_TIMEOUT_TIME, Lists.newArrayList("state", "start_time", "last_modify_time", "is_timeout", "remind_latency"))
                                                                                                    .put(FLOW_TASK_DURATION_C, Lists.newArrayList("end_time", "duration", "start_time"))
                                                                                                    .build();

  /**
   * The constant CALCULATED_FIELDS_PRESET_SQL_MAP.
   */
  public static final ImmutableMap<String, String> CALCULATED_FIELDS_PRESET_SQL_MAP = ImmutableMap.<String, String>builder()
                                                                                                  .put(CURRENT_STAGE_REMAIN_TIME, CURRENT_STAGE_REMAIN_TIME_SQL)
                                                                                                  .put(FACT_TIME_OUT_TIME, FACT_TIME_OUT_TIME_SQL)
                                                                                                  .put(APPROVAL_INSTANCE_DURATION, APPROVAL_INSTANCE_DURATION_SQL)
                                                                                                  .put(APPROVAL_TASK_DURATION, APPROVAL_TASK_DURATION_SQL)
                                                                                                  .put(BPM_INSTANCE_DURATION, BPM_INSTANCE_DURATION_SQL)
                                                                                                  .put(BPM_TASK_DURATION, BPM_TASK_DURATION_SQL)
                                                                                                  .put(BPM_TASK_TIMEOUT_TIME, BPM_TASK_TIMEOUT_TIME_SQL)
                                                                                                  .put(BPM_TASK_TIMEOUT_TIME1, BPM_TASK_TIMEOUT_TIME_SQL)
                                                                                                  .put(APPROVAL_INSTANCE_TIMEOUT_TIME, APPROVAL_INSTANCE_TIMEOUT_TIME_SQL)
                                                                                                  .put(APPROVAL_TASK_TIMEOUT_TIME, APPROVAL_TASK_TIMEOUT_TIME_SQL)
                                                                                                  .put(FLOW_TASK_DURATION_C, FLOW_TASK_DURATION_C_SQL)
                                                                                                  .build();


  /**
   * The constant CURRENT_STAGE_REMAIN_TIME_CH_SQL.
   */
  public static final String CURRENT_STAGE_REMAIN_TIME_CH_SQL = " CASE WHEN not $stage_runtime|is_terminal$ AND $stage_runtime|stage_status$ LIKE '%in_progress' THEN ROUND((toUnixTimestamp(now()) * 1000 - $stage_runtime|start_time$ + COALESCE($stage_runtime|accumulate_time$, 0)), 2) ELSE ROUND(COALESCE($stage_runtime|accumulate_time$, 0), 2) END ";
  /**
   * The constant FACT_TIME_OUT_TIME_CH_SQL.
   */
  public static final String FACT_TIME_OUT_TIME_CH_SQL = " CASE WHEN $stage_runtime|is_time_out$ THEN CASE WHEN NOT $stage_runtime|is_terminal$ AND $stage_runtime|stage_status$ LIKE '%in_progress' THEN ROUND((toUnixTimestamp(now()) * 1000 - $stage_runtime|start_time$ + COALESCE($stage_runtime|accumulate_time$, 0) - $stage_runtime|remind_latency$ * 3600000), 2) ELSE ROUND(COALESCE($stage_runtime|accumulate_time$, 0) - $stage_runtime|remind_latency$ * 3600000, 2) END ELSE 0 END ";
  /**
   * The constant APPROVAL_INSTANCE_DURATION_CH_SQL.
   */
  public static final String APPROVAL_INSTANCE_DURATION_CH_SQL = " CASE WHEN $approval_instance|state$ LIKE '%in_progress' THEN ROUND((toUnixTimestamp(now()) * 1000 - $approval_instance|start_time$), 2) WHEN $approval_instance|state$ LIKE '%error' THEN 0 ELSE ROUND(($approval_instance|end_time$ - $approval_instance|start_time$), 2) END ";
  /**
   * The constant APPROVAL_TASK_DURATION_CH_SQL.
   */
  public static final String APPROVAL_TASK_DURATION_CH_SQL = " CASE WHEN $approval_task|state$ LIKE '%in_progress' OR $approval_task|state$ LIKE '%tag_waiting' THEN ROUND((toUnixTimestamp(now()) * 1000 - $approval_task|start_time$), 2) WHEN $approval_task|state$ LIKE '%error' THEN 0 ELSE ROUND(($approval_task|last_modify_time$ - $approval_task|start_time$), 2) END ";
  /**
   * The constant BPM_INSTANCE_DURATION_CH_SQL.
   */
  public static final String BPM_INSTANCE_DURATION_CH_SQL = " CASE WHEN $bpm_instance|state$ LIKE '%in_progress' THEN ROUND((toUnixTimestamp(now()) * 1000 - $bpm_instance|startTime$), 2) WHEN $bpm_instance|state$ LIKE '%error' THEN 0 ELSE ROUND(($bpm_instance|endTime$ - $bpm_instance|startTime$), 2) END ";
  /**
   * The constant BPM_TASK_DURATION_CH_SQL.
   */
  public static final String BPM_TASK_DURATION_CH_SQL = " CASE WHEN $bpm_task|state$ LIKE '%in_progress' THEN ROUND((toUnixTimestamp(now()) * 1000 - $bpm_task|startTime$), 2) WHEN $bpm_task|state$ LIKE '%error' THEN 0 ELSE ROUND(($bpm_task|endTime$ - $bpm_task|startTime$), 2) END ";
  /**
   * The constant BPM_TASK_TIMEOUT_TIME_CH_SQL.
   */
  public static final String BPM_TASK_TIMEOUT_TIME_CH_SQL = " CASE WHEN $bpm_task|isTimeout$ THEN CASE WHEN $bpm_task|state$ LIKE '%in_progress' THEN ROUND((toUnixTimestamp(now()) * 1000 - $bpm_task|startTime$ - $bpm_task|remindLatency$), 2) WHEN $bpm_task|state$ LIKE '%error' THEN 0 ELSE ROUND(($bpm_task|endTime$ - $bpm_task|startTime$ - $bpm_task|remindLatency$), 2 ) END ELSE 0 END ";
  /**
   * The constant APPROVAL_INSTANCE_TIMEOUT_TIME_CH_SQL.
   */
  public static final String APPROVAL_INSTANCE_TIMEOUT_TIME_CH_SQL = " CASE WHEN $approval_instance|is_timeout$ = 'true' THEN CASE WHEN $approval_instance|state$ LIKE '%in_progress' THEN ROUND((toUnixTimestamp(now()) * 1000 - $approval_instance|start_time$ - $approval_instance|remind_latency$), 2) WHEN $approval_instance|state$ LIKE '%error' THEN 0 ELSE ROUND(($approval_instance|end_time$ - $approval_instance|start_time$ - $approval_instance|remind_latency$), 2) END ELSE 0 END ";
  /**
   * The constant APPROVAL_TASK_TIMEOUT_TIME_CH_SQL.
   */
  public static final String APPROVAL_TASK_TIMEOUT_TIME_CH_SQL = " CASE WHEN $approval_task|is_timeout$ = 'true' THEN CASE WHEN $approval_task|state$ LIKE '%in_progress' OR $approval_task|state$ LIKE '%tag_waiting' THEN ROUND((toUnixTimestamp(now()) * 1000 - $approval_task|start_time$ - $approval_task|remind_latency$), 2) WHEN $approval_task|state$ LIKE '%error' THEN 0 ELSE ROUND(($approval_task|last_modify_time$ - $approval_task|start_time$ - $approval_task|remind_latency$), 2) END ELSE 0 END ";

  public static final String FLOW_TASK_DURATION_C_CH_SQL = " CASE WHEN $flow_task_handle_time_detail|end_time$ IS NOT NULL AND $flow_task_handle_time_detail|end_time$ <> 0 THEN toInt64OrZero($flow_task_handle_time_detail|duration$) ELSE ROUND((toUnixTimestamp(now()) * 1000 - $flow_task_handle_time_detail|start_time$), 2) END ";

  /**
   * The constant CALCULATED_FIELDS_PRESET_SQL_MAP.
   */
  public static final ImmutableMap<String, String> CALCULATED_FIELDS_PRESET_CH_SQL_MAP = ImmutableMap.<String, String>builder()
                                                                                                     .put(CURRENT_STAGE_REMAIN_TIME, CURRENT_STAGE_REMAIN_TIME_CH_SQL)
                                                                                                     .put(FACT_TIME_OUT_TIME, FACT_TIME_OUT_TIME_CH_SQL)
                                                                                                     .put(APPROVAL_INSTANCE_DURATION, APPROVAL_INSTANCE_DURATION_CH_SQL)
                                                                                                     .put(APPROVAL_TASK_DURATION, APPROVAL_TASK_DURATION_CH_SQL)
                                                                                                     .put(BPM_INSTANCE_DURATION, BPM_INSTANCE_DURATION_CH_SQL)
                                                                                                     .put(BPM_TASK_DURATION, BPM_TASK_DURATION_CH_SQL)
                                                                                                     .put(BPM_TASK_TIMEOUT_TIME, BPM_TASK_TIMEOUT_TIME_CH_SQL)
                                                                                                     .put(BPM_TASK_TIMEOUT_TIME1, BPM_TASK_TIMEOUT_TIME_CH_SQL)
                                                                                                     .put(APPROVAL_INSTANCE_TIMEOUT_TIME, APPROVAL_INSTANCE_TIMEOUT_TIME_CH_SQL)
                                                                                                     .put(APPROVAL_TASK_TIMEOUT_TIME, APPROVAL_TASK_TIMEOUT_TIME_CH_SQL)
                                                                                                     .put(FLOW_TASK_DURATION_C, FLOW_TASK_DURATION_C_CH_SQL)
                                                                                                     .build();

}
