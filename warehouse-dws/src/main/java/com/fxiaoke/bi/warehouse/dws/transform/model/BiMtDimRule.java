package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.common.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class BiMtDimRule {
  private String dimensionId;
  private String tenantId;
  private String topologyDescribeId;
  private String dimensionField;
  private String dimensionType;
  private String fieldLocation;
  private String fieldId;

  public Pair<String, String> nodeIdFieldPair() {
    String[] dimFields = dimensionField.split("\\.");
    return Pair.build(dimFields[0], dimFields[1]);
  }
}
