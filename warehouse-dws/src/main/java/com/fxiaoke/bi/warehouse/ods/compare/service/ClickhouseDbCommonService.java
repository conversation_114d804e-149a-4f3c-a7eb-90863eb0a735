package com.fxiaoke.bi.warehouse.ods.compare.service;

import com.fxiaoke.bi.warehouse.dws.db.dao.DbSyncInfoDao;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.bean.CHSystemTable;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.jdbc.JdbcConnection;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@lombok.extern.slf4j.Slf4j
@Slf4j
@Service
public class ClickhouseDbCommonService {

    @Resource
    private DbSyncInfoDao dbSyncInfoDao;

    @Resource
    private CHMetadataService chMetadataService;

    @Resource
    private CHDataSource chDataSource;

    private final ConcurrentHashMap<String, ConcurrentLinkedQueue<String>> missingTTLTables = new ConcurrentHashMap<>();

    private final String sqlFilePath = System.getProperty("catalina.home") + File.separatorChar + "logs" + File.separatorChar + "checkChTableTTL" + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".txt";

    public void logTablesWithoutTTL() {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        List<String> chDbUrlList = dbSyncInfoDao.getAllCHDbList();
        List<CompletableFuture<Void>> futures = chDbUrlList.stream().map(chDbUrl -> CompletableFuture.runAsync(() -> checkDatabaseTables(chDbUrl), executorService)).toList();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executorService.shutdown();
        writeMissingTablesToFile(sqlFilePath);
    }

    public void checkDatabaseTables(String chDbUrl) {
        String checkChTableTTLTemplate = """
                select table
                from system.tables
                where database = '%s'
                  and table in (%s)
                  and not match(engine_full, '(?i).*bi_sys_ods_part\\\\s+IN\\\\s*\\\\(\\\\s*''?i''?,\\\\s*''?c''?\\\\s*\\\\).*');
                """;
        List<CHSystemTable> tableList = chMetadataService.findAllSimpleTables(chDbUrl, false, null);
        String InTableWhere = tableList.stream().map(CHSystemTable::getTable).map(table -> "'" + table + "'").collect(Collectors.joining(","));
        String chDB = Utils.parseDBNameFromJdbcUrl(chDbUrl);
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbUrl)) {
            jdbcConnection.query(String.format(checkChTableTTLTemplate, chDB, InTableWhere), resultSet -> {
                while (resultSet.next()) {
                    String table = resultSet.getString("table");
                    missingTTLTables.computeIfAbsent(chDbUrl, k -> new ConcurrentLinkedQueue<>()).add(table);
                }
            });
        } catch (Exception e) {
            log.error("checkDatabaseTables error, this chDbUrl is {}", chDbUrl);
        }
    }

    public void writeMissingTablesToFile(String filePath) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            log.info("missingTTLTable start write: {}", filePath);
            for (Map.Entry<String, ConcurrentLinkedQueue<String>> entry : missingTTLTables.entrySet()) {
                String database = entry.getKey();
                ConcurrentLinkedQueue<String> value = entry.getValue();
                writer.write(database + "=" + String.join(",", value) + "\n\n");
            }
            log.info("missingTTLTable complete write: {}", filePath);
        } catch (IOException e) {
            log.info("missingTTLTable write error: {}", e.getMessage());
        }
    }
}
