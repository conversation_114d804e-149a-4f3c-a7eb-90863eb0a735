package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.DashBoardLinkDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author:jief
 * @Date:2023/9/14
 */
@Repository
public interface DashboardLinkMapper extends IBatchMapper<DashBoardLinkDO>, ITenant<DashboardLinkMapper> {

  @Select("select * from dash_board_link where tenant_id=#{tenantId} and #{followId} = any(follow_view_ids)")
  List<DashBoardLinkDO> queryDashBoardLinkByFollowId(@Param("tenantId") String tenantId, @Param("followId") String followId);
}
