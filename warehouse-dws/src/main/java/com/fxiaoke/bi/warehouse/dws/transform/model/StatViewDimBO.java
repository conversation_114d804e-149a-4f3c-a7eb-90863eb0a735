package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * @Author:jief
 * @Date:2023/7/6
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatViewDimBO {
  private String tenantId;
  private String themeApiName;
  private String masterApiName;
  private UdfObjFieldDO lookupField;
  private List<DimRule> themeDimRules;
  private List<DimRule> masterDimRules;
  private List<DimRule> customDimRules;
  /**
   * 维度是否包含日期字段
   */
  private boolean withActionDate;
  /**
   * 日期字段描述信息
   */
  private StatFieldDO actionDate;

  /**
   * 检测维度字段是否有引用字段或what字段或id转name
   * 返回false
   *
   * @return boolean
   */
  public boolean themeDimWithQuoteField() {
    if (CollectionUtils.isNotEmpty(themeDimRules)) {
      Optional<DimRule> dimWithQuoteOp = themeDimRules.stream().filter(dimRule -> {
        DimFieldRule dimFieldRule = dimRule.getDimFieldRule();
        return dimFieldRule.joinRelation != null;
      }).findAny();
      return dimWithQuoteOp.isPresent();
    }
    return false;
  }

  /**
   * 检测维度字段是否有引用字段或what字段或id转name
   * 返回false
   *
   * @return boolean
   */
  public boolean masterDimWithQuoteField() {
    if (CollectionUtils.isNotEmpty(masterDimRules)) {
      Optional<DimRule> dimWithQuoteOp = masterDimRules.stream().filter(dimRule -> {
        DimFieldRule dimFieldRule = dimRule.getDimFieldRule();
        return dimFieldRule.joinRelation != null;
      }).findAny();
      return dimWithQuoteOp.isPresent();
    }
    return false;
  }
}
