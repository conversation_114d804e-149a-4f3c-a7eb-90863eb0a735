package com.fxiaoke.bi.warehouse.dws.transform.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.bean.TopologyTableAggDownStream;
import com.fxiaoke.bi.warehouse.common.db.entity.BiMtDimensionStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.StatFieldStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.StatViewStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.er.AggRuleType;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.ClickhouseTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBUpdateEventDao;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyStatusDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.exception.VersionConflictException;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.service.BizLogService;
import com.fxiaoke.bi.warehouse.dws.transform.TopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.*;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.AggRuleDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtDimensionDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatSchemaDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewDO;
import com.fxiaoke.bi.warehouse.dws.transform.model.StatViewDimBO;
import com.fxiaoke.bi.warehouse.dws.transform.model.StatViewEntity;
import com.fxiaoke.bi.warehouse.dws.transform.model.TransformContext;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.MapUtils;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.Uninterruptibles;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OldStatViewTopologyTransformer extends TopologyTransformer {
  @Resource
  private AggRuleDao aggRuleDao;
  @Resource
  private StatViewDao statViewDao;
  @Resource
  private StatSchemaDao statSchemaDao;
  @Resource
  private DimRuleDao dimRuleDao;
  @Resource
  private UdfObjDao udfObjDao;
  @Resource
  private ClickhouseTenantPolicy chTenantPolicy;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  @Resource(name = "mybatisTenantPolicy")
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Resource
  private DBUpdateEventDao dbUpdateEventDao;
  @Resource
  private BizLogService bizLogService;
  @Resource
  private TopologyStatusDao topologyStatusDao;
  @Resource
  private BiMtDimensionDao biMtDimensionDao;

  @Override
  public TopologyTableDO transform(TransformContext context) {

    return null;
  }

  @Override
  public void upsertTopologyTable(TransformContext context) {

  }

  /**
   * 按照租户刷统计图工具
   *
   * @param tenantId     租户id
   * @param fromDateTime 开始时间
   * @param all          是否刷全部
   */
  public void flashStatViewByTenantId(String tenantId, String fromDateTime, boolean all) {
    List<StatViewDO> statViewDOS = statViewDao.batchQueryStatViewByEi(tenantId, fromDateTime);
    this.flashStatViewListByTenantId(tenantId, fromDateTime, all, statViewDOS);
  }

  /**
   * 迁移明细主题  只包含目标的老图
   * @param tenantId     租户id
   * @param fromDateTime 开始时间
   * @param all          是否刷全部
   */
  public void flashGoalStatViewByTenantId(String tenantId, String fromDateTime, boolean all) {
    List<StatViewDO> statViewDOS = statViewDao.batchQueryGoalStatViewByEi(tenantId, fromDateTime);
    this.flashStatViewListByTenantId(tenantId, fromDateTime, all, statViewDOS);
  }

  /**
   * 按照租户刷统计图工具
   * @param tenantId
   * @param fromDateTime
   * @param all
   * @param statViewDOS
   */
  public void flashStatViewListByTenantId(String tenantId, String fromDateTime, boolean all, List<StatViewDO> statViewDOS) {
    if (CollectionUtils.isEmpty(statViewDOS)) {
      log.info("can not find active view tenantId:{},fromDateTime:{}", tenantId, fromDateTime);
      return;
    }
    //需要计算topology的统计图集合
    List<StatViewDO> allStatViewNeedCal = Lists.newArrayList();
    //不需要计算topology的统计图但是需要立即启用的统计图集合
    List<String> activeAndNoTopology = Lists.newArrayList();
    statViewDOS.forEach(statViewDO -> {
      if (Constants.no2Topology(statViewDO.getSchemaId(), statViewDO.getViewId())) {
        activeAndNoTopology.add(statViewDO.getViewId());
        return;
      }
      allStatViewNeedCal.add(statViewDO);
    });
    List<String> activeViews = Lists.newArrayList();
    List<String> inactiveViews = Lists.newArrayList();
    if (!all) {//判断是否检测统计图是否活跃
      Set<String> allActiveViewSet = Sets.newHashSet();
      if (GrayManager.isAllowByRule("support_biz_log", tenantId)) {
        Map<String, List<String>> eiViewMapper = bizLogService.queryActiveViewIds(Lists.newArrayList(tenantId));
        allActiveViewSet.addAll(eiViewMapper.getOrDefault(tenantId, Lists.newArrayList()));
      }
      log.info("this tenantId:{},allViewId size:{},allActiveViewSet size:{}", tenantId, allStatViewNeedCal.size(), allActiveViewSet.size());
      allStatViewNeedCal.forEach(statViewDO -> {
        if (allActiveViewSet.contains(statViewDO.getViewId())) {
          activeViews.add(statViewDO.getViewId());
        } else if (Objects.equals(tenantId, String.valueOf(statViewDO.getEi())) &&
          statViewDO.activeInDays(Constants.ACTIVE_DAYS_THRESHOLD)) {
          activeViews.add(statViewDO.getViewId());
        } else if (topologyStatusDao.isActive(tenantId, statViewDO.getViewId())) {
          activeViews.add(statViewDO.getViewId());
        } else {
          inactiveViews.add(statViewDO.getViewId());
        }
      });
      //停用不活跃的图
      if (CollectionUtils.isNotEmpty(inactiveViews)) {
        List<String> disabledViews = Lists.newArrayList();
        inactiveViews.forEach(viewId -> {
          if (topologyTableService.disableTopologyTableBySourceId(tenantId, viewId)) {
            log.info("disableTopologyTableBySourceId tenantId:{},viewId:{}", tenantId, viewId);
            disabledViews.add(viewId);
          }
        });
        if (!disabledViews.isEmpty()) {
          //修改不活跃的图状态
          int stops = topologyTableService.batchUpdateStatViewStatus(tenantId, inactiveViews, StatViewStatusEnum.disabled4Idle.getStatus());
          log.info("stop stat view tenantId:{},size:{},viewIds:{}", tenantId, stops,disabledViews);
        }
      }
    } else {
      allStatViewNeedCal.forEach(s -> activeViews.add(s.getViewId()));
    }
    //不需要预刷的图直接启用
    if (CollectionUtils.isNotEmpty(activeAndNoTopology)) {
      int result = topologyTableService.batchUpdateStatViewStatus(tenantId, activeAndNoTopology, StatViewStatusEnum.used.getStatus());
      log.info("active stat view tenantId:{},result:{}", tenantId, result);
    }
    //检测并补刷活跃的图
    activeViews.forEach(sourceId -> {
      try {
        this.doCreateViewTopology(tenantId, sourceId, TopologyTableStatus.Prepared.getValue());
        log.info("repair doCreateViewTopology finish tenantId:{},viewId:{}", tenantId, sourceId);
      } catch (Exception e) {
        log.error("doCreateViewTopology error,tenantId:{},viewId:{}", tenantId, sourceId, e);
      }
    });
  }

  /**
   * 重置统计图状态
   *
   * @param tenantId  租户id
   * @param sourceIds 图表id
   */
  public void resetTopologyByEi(String tenantId, int sourceType, int status, String[] sourceIds) {
    int result = topologyTableService.resetTopologyByEi(tenantId, sourceType, status, sourceIds);
    log.info("resetTopologyByEi finish tenantId:{},size:{}", tenantId, result);
  }

  /**
   * 生成预览sql
   *
   * @param statViewPreArg
   * @return
   */
  public StatViewPreSQL createStatViewPreSQL(StatViewPreArg statViewPreArg) {
    String tenantId = statViewPreArg.getTenantId();
    StatViewPreArg.UserSegmentFilter userSegmentFilter = statViewPreArg.getUserSegmentFilter();
    TopologyTable rTopologyTable = findRealTimeQueryIfExists(statViewPreArg);
    if (rTopologyTable != null) {
      this.checkViewHasGoalRule(statViewPreArg.getGoalRuleIds(), rTopologyTable);
      int sampleSize = -1;
      Map<String, Map<String, String>> rightLargeTables = Maps.newHashMap();
      this.simpleDimFields(rTopologyTable, statViewPreArg);
      return this.createStatViewPreSQL(tenantId, rTopologyTable, -1, sampleSize, rightLargeTables, userSegmentFilter,statViewPreArg.getStatFilter());
    }
    TopologyTable topologyTable = this.buildTopologyTableByByFieldIds(statViewPreArg, true);
    if (topologyTable == null || CollectionUtils.isEmpty(topologyTable.getStatRuleList())) {
      log.error("createStatViewPreSQL statRuleList is empty arg:{}", statViewPreArg.toJSONString());
      return StatViewPreSQL.builder().tenantId(tenantId).build();
    }
    this.simpleDimFields(topologyTable, statViewPreArg);
    this.checkViewHasGoalRule(statViewPreArg.getGoalRuleIds(), topologyTable);
    int resultLimit = StringUtils.isNotBlank(topologyTable.getDatabaseId())?-1:this.getViewSqlResultLimit();
    int sampleSize = this.calSample(topologyTable,  mybatisTenantPolicy.standalone(tenantId));
    //判断指标各个表的数据量，统计出每个指标最靠右的大表
    Map<String, Map<String, String>> rightLargeTables = Maps.newHashMap();
    return this.createStatViewPreSQL(tenantId, topologyTable, resultLimit, sampleSize, rightLargeTables, userSegmentFilter,statViewPreArg.getStatFilter());
  }

  /**
   * 精简不需要的维度，减少实时查询的group by 尤其是埋点的分析
   * @param topologyTable
   * @param statViewPreArg
   */
  public void simpleDimFields(TopologyTable topologyTable, StatViewPreArg statViewPreArg) {
    if (GrayManager.isAllowByRule("simple_group_by", topologyTable.getTenantId()) && StringUtils.isNotBlank(topologyTable.getDatabaseId())) {
     List<String> allFields = statViewPreArg.getAllStatFieldIds();
      if (CollectionUtils.isEmpty(allFields)) {
        log.warn("allStatFieldIds is empty {}",JSON.toJSONString(statViewPreArg));
        return;
      }
      Map<String,String> fieldLocation = topologyTable.getStatFieldLocation();
      if (fieldLocation == null) {
        log.warn("statFieldLocation is empty sourceId:{}", statViewPreArg.getSourceId());
        return;
      }
      List<String> rmDimFieldIds = Lists.newArrayList();
      Set<String> rmDimFields = Sets.newHashSet();
      Set<String> queryFields= Sets.newHashSet(allFields);
      Set<String> commonDims = Sets.newHashSet(topologyTable.getCommonDimList());
      fieldLocation.forEach((id, location) -> {
        if (!queryFields.contains(id)) {
          if (StringUtils.equalsAny(location, WarehouseConfig.authFields)) {
            rmDimFields.add(location);
            rmDimFieldIds.add(id);
          }
          if (location.startsWith("dim_") && commonDims.contains(location)) {
            rmDimFields.add(location);
            rmDimFieldIds.add(id);
          }
        }
      });
      if (!rmDimFields.isEmpty()) {
        if (CollectionUtils.isNotEmpty(topologyTable.getStatRuleList())) {
          topologyTable.getStatRuleList().forEach(statRule -> {
            List<String> dimConfigStringList = statRule.getDimConfigStringList()
                                                       .stream()
                                                       .filter(dimConfig -> !rmDimFields.contains(dimConfig.split(":")[2]))
                                                       .toList();
            statRule.getDimConfigStringList().clear();
            statRule.getDimConfigStringList().addAll(dimConfigStringList);
          });
        }
        List<String> newCommonDims = topologyTable.getCommonDimList()
                                                  .stream()
                                                  .filter(commonDim -> !rmDimFields.contains(commonDim))
                                                  .toList();
        topologyTable.getCommonDimList().clear();
        topologyTable.getCommonDimList().addAll(newCommonDims);
      }
      if(!rmDimFieldIds.isEmpty()){
        rmDimFieldIds.forEach(topologyTable.getStatFieldLocation()::remove);
      }
    }
  }

  private StatViewPreSQL createStatViewPreSQL(String tenantId,
                                              TopologyTable topologyTable,
                                              int resultLimit,
                                              int sampleSize,
                                              Map<String,Map<String,String>> rightLargeTables,
                                              StatViewPreArg.UserSegmentFilter userSegmentFilter,
                                              List<StatViewFilter> statFilter){
    //如果是1+N的图,需要补充实时查的条件
    if (GrayManager.isAllowByRule("biDataSyncPolicyGray", tenantId) && topologyTable.getAggDownStream() != null) {
      topologyTableService.addDownStreamRuleWhereSql(topologyTable);
    }
    //用户分群条件
    if (userSegmentFilter != null) {
      topologyTableService.addUserSegmentFilterSql(tenantId, topologyTable, userSegmentFilter);
    }
    Set<String> pushDownFields = this.statViewPredicatePushDown(topologyTable, statFilter);
    TopologyTableViewMonitor viewMonitor;
    if (StringUtils.isNotBlank(topologyTable.getDatabaseId()) && topologyTable.shouldMergeAllRule() && GrayManager.isAllowByRule("support_merge_agg_rule", tenantId)) {
      viewMonitor = topologyTable.toMergePreViewMonitor(sampleSize, resultLimit, rightLargeTables, Maps.newHashMap(), true, null);
    } else {
      //table key 直接返回空对象
      viewMonitor = topologyTable.toViewMonitor(sampleSize, resultLimit, rightLargeTables, Maps.newHashMap(), null);
    }
    long maxModifiedTime = new Date().getTime();
    if (StringUtils.isBlank(topologyTable.getDatabaseId())) {
      maxModifiedTime = dbUpdateEventDao.findMaxModifiedTimeByEi(tenantId, viewMonitor.getStatViewEffectTables());
    }
    return StatViewPreSQL.builder()
                         .tenantId(tenantId)
                         .statFieldLocation(topologyTable.getStatFieldLocation())
                         .sampleType(sampleSize == -1 ? 0 : 1)
                         .preViewSQL(viewMonitor.getViewSQL())
                         .uniqFieldLocation(topologyTable.getDimUniqFieldMapper())
                         .maxModifiedTime(maxModifiedTime)
                         .pushDownFields(pushDownFields)
                         .build();
  }

  /**
   * 创建topology
   * 统计图
   *
   * @param tenantId 租户id
   * @param viewId   图表id
   * @param status   状态
   */
  @Transactional(propagation = Propagation.REQUIRED)
  public int doCreateViewTopology(String tenantId, String viewId, int status) throws VersionConflictException {
    long start = System.currentTimeMillis();
    StatViewDO statViewDO = statViewDao.queryStatView(tenantId, viewId);
    if (statViewDO == null) {
      log.warn("queryStatView statViewDO is null tenantId:{},viewId{}", tenantId, viewId);
      return 0;
    }
    if (statViewDO.getIsDelete() == 1) {
      boolean isOK = topologyTableService.deleteTopologyTableBySourceId(tenantId, viewId);
      log.info("delete this view:{}:{}:{}", tenantId, viewId, isOK);
      return 0;
    }
    if (Constants.no2Topology(statViewDO.getSchemaId(), statViewDO.getViewId())) {
      int result = topologyTableService.batchUpdateStatViewStatus(tenantId, Lists.newArrayList(statViewDO.getViewId()), StatViewStatusEnum.used.getStatus());
      log.info("doCreateViewTopology#batchUpdateStatViewStatus tenantId:{},result:{}", tenantId, result);
      return result;
    }
    TopologyTable topologyTable = this.createStatView(tenantId, statViewDO);
    if (topologyTable == null) {
      log.warn("createStatView null tenantId:{},viewId:{}", tenantId, viewId);
      return 0;
    }
    int resultLimit = this.getViewSqlResultLimit();
    int sampleLimit = this.calSample(topologyTable, mybatisTenantPolicy.standalone(tenantId));
    long createTime = new Date().getTime();
    return topologyTableService.saveTopologyTable(tenantId, viewId, status, statViewDO.getIsDelete(), statViewDO.getCreator(), createTime, statViewDO.getTimeZone(), topologyTable, start, sampleLimit, resultLimit, "");
  }

  /**
   * 创建statView对象
   *
   * @param tenantId
   * @param statViewDO
   * @return
   */
  public TopologyTable createStatView(String tenantId, StatViewDO statViewDO) {
    StatViewEntity statViewEntity = statViewDao.findStatViewById(tenantId, statViewDO);
    if (statViewEntity == null) {
      return null;
    }
    return this.createTopologyTable(tenantId, statViewEntity, false);
  }

  /**
   * 统计图变更前后对比判断是否需要重新计算
   *
   * @param statViewBatchArg 需要对比得图集合
   * @return List<Map < String, Object>>
   */
  public List<Map<String, Object>> batchCompareStatView(StatViewBatchArg statViewBatchArg) {
    List<Map<String, Object>> compareResult = Lists.newArrayList();
    List<StatViewPreArg> statViewPreArgList = statViewBatchArg.getStatViewArgList();
    if (CollectionUtils.isNotEmpty(statViewPreArgList)) {
      List<String> sourceIds = statViewPreArgList.stream().map(StatViewPreArg::getSourceId).toList();
      Map<String, Set<String>> compareFromFieldLocations = statViewDao.batchQueryStatViewBaseField(statViewBatchArg.getTenantId(), sourceIds);
      List<Map<String, Object>> topologyFieldMap = topologyTableService.batchQueryFieldLocations(statViewBatchArg.getTenantId(), sourceIds);
      Map<String, Set<String>> compareToFieldLocations = topologyFieldMap.stream()
                                                                         .map(JSONObject::new)
                                                                         .collect(Collectors.toMap(item -> item.getString("source_id"), item2 -> {
                                                                           String sfl = item2.getString("stat_field_location");
                                                                           if (StringUtils.isNotBlank(sfl)) {
                                                                             return JSON.parseObject(sfl).keySet();
                                                                           }
                                                                           return Sets.newHashSet();
                                                                         }));
      sourceIds.forEach(sourceId -> {
        Set<String> newFieldIds = compareFromFieldLocations.get(sourceId);
        Set<String> oldFieldIds = compareToFieldLocations.get(sourceId);
        if (newFieldIds == null && oldFieldIds == null) {
          compareResult.add(Map.of("sourceId", sourceId, "compared", 1));
          return;
        }
        if (newFieldIds != null && oldFieldIds != null && oldFieldIds.containsAll(newFieldIds)) {
          compareResult.add(Map.of("sourceId", sourceId, "compared", 1));
          return;
        }
        compareResult.add(Map.of("sourceId", sourceId, "compared", 0));
      });
    }
    return compareResult;
  }

  /**
   * 创建统计图视图层
   *
   * @param tenantId
   * @param statViewEntity
   * @return
   */
  public TopologyTable createTopologyTable(String tenantId, StatViewEntity statViewEntity, boolean isPreSql) {
    TopologyTable topologyTable = new TopologyTable();
    topologyTable.setViewId(statViewEntity.getViewId());
    topologyTable.setTenantId(tenantId);
    topologyTable.setTimezone(statViewEntity.getTimeZone());
    topologyTable.setApiName(statViewEntity.getThemeName());
    StatViewDimBO statViewDimBO = dimRuleDao.buildStatViewDimBo(tenantId, statViewEntity);
    List<AggRuleDO> aggRules = statViewEntity.getAggRuleIds();
    //指标作为筛选条件暂时也作为agg指标计算，后面想通了再做处理
    if (CollectionUtils.isNotEmpty(statViewEntity.getAggRuleFilterIds())) {
      aggRules.addAll(statViewEntity.getAggRuleFilterIds());
      aggRules = aggRules.stream().distinct().collect(Collectors.toList());
    }
    //排序方便图合并生成的stat_json_list 相似度更高
    aggRules.sort(Comparator.comparing(AggRuleDO::getFieldId));
    final Map<String, AtomicInteger> aggAliasMapper = Maps.newHashMap();
    final Map<String, AtomicInteger> dimsMapper = Maps.newHashMap();
    final Map<String, String/*dim字段别名*/> dimFieldAliasMapper = Maps.newHashMap();
    List<TopologyTableAggRule> statRuleList = Lists.newArrayList();
    Set<String> commonDims = Sets.newHashSet();
    Map<String/*fieldId*/, String/*agg column*/> statFieldLocation = Maps.newHashMap();
    Map<String, String> dimUniqFieldMapper = Maps.newHashMap();
    Set<String> downViewIds = Sets.newHashSet();
    Set<String> upTables = Sets.newHashSet();
    aggRules.forEach(aggRule -> {
      String ruleId = aggRule.getRuleId();
      if (ruleId.contains("|") || "BI_5d9ff3331b9ad40001873792".equals(ruleId)) {
        log.warn("now not support old goal calculate tenantId:{},viewId:{},ruleId:{}", tenantId, statViewEntity.getViewId(), ruleId);
        return;
      }
      TopologyTableAggRule statRule;
      if (Constants.OLD_STAT_VIEW_GOAL_RULE_IDS.contains(ruleId)) {
        statRule = aggRuleDao.parseGoalRuleFromMap(tenantId, ruleId, statViewDimBO, aggAliasMapper, dimsMapper, dimFieldAliasMapper, statViewEntity.getThemeName(), statViewEntity.getCheckCyCle(), isPreSql);
      } else if ("downstream_agg".equals(aggRule.getType())) {
        statRule = aggRuleDao.parseDownStreamRuleFromMap(tenantId, ruleId, aggRule.getFieldId(), statViewDimBO, aggAliasMapper, dimsMapper, dimFieldAliasMapper, statViewEntity.getThemeName(), downViewIds, upTables, isPreSql);
      } else {
        statRule = aggRuleDao.parseAggRuleFromMap(tenantId, ruleId, statViewDimBO, aggAliasMapper, dimsMapper, dimFieldAliasMapper);
      }
      List<String> dimConfigList = statRule.getDimConfigStringList();
      //公共维度
      if (CollectionUtils.isNotEmpty(dimConfigList)) {
        dimConfigList.forEach(dim -> {
          commonDims.add(dim.split(":")[2]);
        });
      }
      //字段槽位 增加action_date 槽位
      statFieldLocation.putAll(statRule.getFieldLocationMap());
      if (statViewDimBO.isWithActionDate() && statViewDimBO.getActionDate() != null) {
        statFieldLocation.put(statViewDimBO.getActionDate().getFieldId(), Constants.ACTION_DATE);
      }
      //去重,统计图只有一个去重列
      if (!MapUtils.isNullOrEmpty(statRule.getUniqDimConfigMap())) {
        String uniqDimConfig = statRule.getUniqDimConfigMap().get(statRule.getFieldId());
        if (StringUtils.isNotBlank(uniqDimConfig)) {
          dimUniqFieldMapper.put(statRule.getFieldId(), uniqDimConfig.split(":")[2]);
        }
      }
      statRule.setFieldLocationMap(null);//获取完后置空
      statRuleList.add(statRule);
    });
    String DBName = chTenantPolicy.getDBName(tenantId,statViewEntity.getDatabaseId());
    if (StringUtils.isBlank(DBName)) {
      throw new RuntimeException(String.format("can not find DBName tenantId:%s", tenantId));
    }
    topologyTable.setDatabaseId(statViewEntity.getDatabaseId());
    topologyTable.setDatabase(DBName);
    topologyTable.setStatRuleList(statRuleList);
    topologyTable.setCommonDimList(Lists.newArrayList(commonDims));
    topologyTable.setStatFieldLocation(statFieldLocation);
    topologyTable.setDimUniqFieldMapper(dimUniqFieldMapper);
    topologyTable.setSource(AggRuleType.Agg.getRuleType());
    topologyTable.setAllAggStatFields(statViewEntity.findAllAggFieldsWithStatus());
    if (!downViewIds.isEmpty()) {
      TopologyTableAggDownStream aggDownStream = statViewDao.buildAggDownStream(tenantId, downViewIds, upTables);
      topologyTable.setAggDownStream(aggDownStream);
    }
    return topologyTable;
  }

  /**
   * 根据 fieldId 组装topology table对象
   *
   * @param statViewPreArg 参数
   * @return TopologyTable
   */
  public TopologyTable buildTopologyTableByByFieldIds(StatViewPreArg statViewPreArg, boolean isPreSql) {
    String tenantId = statViewPreArg.getTenantId();
    JSONObject statSchemaInfo = statViewDao.findStatSchemaById(tenantId, statViewPreArg.getSchemaId());
    String themeApiName = statSchemaInfo.getString("schema_en_name");
    if (StringUtils.isBlank(themeApiName)) {
      log.error("schemaEnName is empty tenantId:{},schemaId:{}", tenantId, statViewPreArg.getSchemaId());
      return null;
    }
    if (StringUtils.isBlank(statViewPreArg.getCheckCyCle())) {
      statViewPreArg.setCheckCyCle("month");
    }
    StatViewEntity statViewEntity = statViewDao.findStatViewEntityByFieldIds(tenantId, null, null, themeApiName, statViewPreArg.getSchemaId(), statViewPreArg.getAllStatFieldIds(), statViewPreArg.getAggFilterFieldIds(), statViewPreArg.findTimeZone(), statViewPreArg.getCheckCyCle(), statSchemaInfo.getString("database_id"));
    return this.createTopologyTable(tenantId, statViewEntity, isPreSql);
  }

  /**
   * 批量校验
   *
   * @param dimEventList
   */
  public void batchOnChangeTheme(Set<DimRefreshEvent> dimEventList) {
    Map<String, Set<String>> eiThemeNameMapper = Maps.newHashMap();
    dimEventList.forEach(dimRefreshEvent -> {
      eiThemeNameMapper.computeIfAbsent(dimRefreshEvent.getTenantId(), key -> new HashSet<>())
                       .add(dimRefreshEvent.getThemeApiName());
    });
    eiThemeNameMapper.forEach((ei, themeApiNames) -> {
      Set<String> allThemeApiNames = Sets.newHashSet(themeApiNames);
      //结合所有从对象主题一起校验
      List<UdfObjFieldDO> mdFieldList = udfObjDao.batchQueryMDField(themeApiNames.toArray(String[]::new), ei);
      if (CollectionUtils.isNotEmpty(mdFieldList)) {
        mdFieldList.stream().map(UdfObjFieldDO::getDbObjName).forEach(allThemeApiNames::add);
      }
      allThemeApiNames.forEach(themeApiName -> this.onChangeTheme(ei, themeApiName));
    });
  }

  /**
   * 统计图 - 主题变更对 topology的影响
   *
   * @param tenantId  租户id
   * @param themeName 主题名称
   */
  public void onChangeTheme(String tenantId, String themeName) {
    List<StatSchemaDO> statSchemaDOS = statSchemaDao.findAllStatSchemaByEnName(tenantId, themeName);
    if (CollectionUtils.isEmpty(statSchemaDOS)) {
      log.warn("can not find schemas no matter is_deleted=1 or status in(0,5) tenantId:{},schemaEnName:{}", tenantId, themeName);
      return;
    }
    Optional<StatSchemaDO> statSchemaDOOP = statSchemaDOS.stream()
                                                         .filter(statSchemaDO -> statSchemaDO.getIsDeleted() == 0 &&
                                                                                 statSchemaDO.getStatus() == 1)
                                                         .findFirst();
    List<TopologyTableDO> topologyTableDOS = topologyTableService.queryTopologyTableByApiName(tenantId, TopologyTableStatus.allStatus(), themeName, 0);
    boolean enable = statSchemaDOOP.isPresent();
    topologyTableDOS.forEach(topologyTableDO -> {
      String viewId = topologyTableDO.getSourceId();
      String lockKey = String.format("BI_%s_%s", tenantId, viewId);
      try (JedisLock jedisLock = new JedisLock(jedisCmd, lockKey, 1000 * 30)) {
        for (int i = 0; i < 3; i++) {//获取锁重试
          if (jedisLock.tryLock()) {
            if (!enable) {
              //主题停用
              boolean isOk = topologyTableService.disableTopologyTableBySourceId(tenantId, topologyTableDO);
              // topologyTableDO.setStatus(TopologyTableStatus.UnUsed.getValue());
              // int result = topologyTableService.saveTopologyWithVersion(tenantId, topologyTableDO,
              // topologyTableDO.getVersion());
              log.info("onChangeTheme stop topologyTable tenantId:{},viewId:{},themeName:{} result:{}", tenantId, topologyTableDO.getSourceId(), themeName, isOk);
              if (isOk) {
                int result2 = topologyTableService.batchUpdateStatViewStatus(tenantId, Lists.newArrayList(viewId), StatViewStatusEnum.disabled.getStatus());
                log.info("onChangeTheme stop topologyStatus tenantId:{},viewId:{},themeName:{} result:{}", tenantId, topologyTableDO.getSourceId(), themeName, result2);
              }
            } else {
              this.batchOnChangeDimRule(tenantId, topologyTableDO);
            }
            break;
          } else {
            log.warn("onChangeTheme tryLock fail tenantId:{},viewId:{},themeName:{} retryTimes:{}", tenantId, topologyTableDO.getSourceId(), themeName, i);
            Uninterruptibles.sleepUninterruptibly(10, TimeUnit.SECONDS);
          }
        }
      } catch (Exception e) {
        log.error("onChangeTheme error tenantId:{},themeName:{},viewId:{}", tenantId, themeName, topologyTableDO.getSourceId(), e);
      }
    });
  }

  /**
   * 维度字段状态变更，判断停用或启用
   *
   * @param tenantId        租户id
   * @param topologyTableDO topologyTable 对象
   */
  public void batchOnChangeDimRule(String tenantId, TopologyTableDO topologyTableDO) {
    String viewId = topologyTableDO.getSourceId();
    TopologyTable topologyTable = TopologyTable.fromTopologyTableDO(topologyTableDO);
    List<String> dimFieldIds = topologyTable.findCommonDimFieldIds();
    if (CollectionUtils.isNotEmpty(dimFieldIds)) {
      //查找所有停用的字段
      List<StatFieldDO> disableStatFields = dimRuleDao.findStatFieldByFieldId(tenantId, dimFieldIds, new String[] {"dim"}, new int[] {0, 3, 5});
      // 有些固定维度字段不需要校验状态
      if (GrayManager.isAllowByRule("notCheckNewAggDataFixDim", tenantId) && CollectionUtils.isNotEmpty(disableStatFields)) {
        disableStatFields = disableStatFields.stream().filter(statFieldDO -> !WarehouseConfig.forceDbFieldNames.contains(statFieldDO.getDbFieldName())).toList();
      }
      TopologyTableStatus ttStatus = TopologyTableStatus.from(topologyTable.getStatus());
      if (CollectionUtils.isNotEmpty(disableStatFields)) {
        switch (ttStatus) {
          case Prepared, Calculating, failSkip -> {
            boolean isOk = topologyTableService.disableTopologyTableBySourceId(tenantId, topologyTableDO);
            log.info("batchOnChangeDimRule stop topologyTable tenantId:{},viewId:{},result:{}", tenantId, topologyTableDO.getSourceId(), isOk);
          }
        }
      } else {
        if (ttStatus == TopologyTableStatus.UnUsed) {
          try {
            int result2 = this.doCreateViewTopology(tenantId, viewId, TopologyTableStatus.Prepared.getValue());
            log.info("batchOnChangeDimRule doCreateViewTopology,tenantId:{},viewId:{},result:{}", tenantId, viewId, result2);
          } catch (Exception e) {
            log.error("doCreateViewTopology error,tenantId:{},viewId:{}", tenantId, viewId, e);
          }
        }
      }
    }
  }

  private void configStopDisableTopology(String tenantId, TopologyTableDO topologyTableDO) {
    boolean isOk = topologyTableService.disableTopologyTableBySourceId(tenantId, topologyTableDO);
    log.info("batchOnChangeDimRule stop topologyTable tenantId:{},viewId:{},result:{}", tenantId, topologyTableDO.getSourceId(), isOk);
  }

  /**
   * 批量处理指标变更事件
   *
   * @param aggRuleChangedEvents
   */
  public void batchOnChangeAggRule(Set<AggRuleChangedEvent> aggRuleChangedEvents) {
    Map<String, Set<String>> eiRuleIdMapper = Maps.newHashMap();
    aggRuleChangedEvents.forEach(aggRuleChangedEvent -> {
      eiRuleIdMapper.computeIfAbsent(aggRuleChangedEvent.getTenantId(), key -> new HashSet<>())
                    .add(aggRuleChangedEvent.getRuleId());
    });
    eiRuleIdMapper.forEach((ei, ruleIds) -> {
      int[] status = new int[] {StatFieldStatusEnum.enable.getCode(), StatFieldStatusEnum.Initialize.getCode(), StatFieldStatusEnum.unable.getCode(), StatFieldStatusEnum.System_unable.getCode(), StatFieldStatusEnum.Object_unable.getCode(), StatFieldStatusEnum.Reservation.getCode(), StatFieldStatusEnum.Trial_data.getCode()};
      List<StatFieldDO> statFieldDOS = aggRuleDao.batchQueryAggRuleByRuleIds(ei, ruleIds.toArray(String[]::new), status);
      if (CollectionUtils.isNotEmpty(statFieldDOS)) {
        statFieldDOS.forEach(statFieldDO -> this.onChangeAggRule(ei, statFieldDO));
      }
    });
  }

  /**
   * 指标变更对topology 的影响
   * 1、指标变更，重跑
   * 2、指标禁用，重跑
   * 3、指标启用、没法重跑了
   *
   * @param tenantId    租户id
   * @param statFieldDO 字段id
   */
  public void onChangeAggRule(String tenantId, StatFieldDO statFieldDO) {
    String fieldId = statFieldDO.getFieldId();
    List<TopologyTableDO> topologyTableDOS;
    if (GrayManager.isAllowByRule("use_all_agg_field", tenantId)) {
      topologyTableDOS = topologyTableService.queryTopologyTableFromAllAgg(tenantId, TopologyTableStatus.needCalcStatus(), new String[] {fieldId});
    } else {
      topologyTableDOS = topologyTableService.findTopologyTablesByFieldId(tenantId, TopologyTableStatus.needCalcStatus(), new String[] {fieldId});
    }
    if (CollectionUtils.isEmpty(topologyTableDOS)) {
      log.info("can not find topologyTable by agg fieldId tenantId:{},fieldId:{}", tenantId, statFieldDO.getFieldId());
      return;
    }
    topologyTableDOS.forEach(topologyTableDO -> {
      String viewId = topologyTableDO.getSourceId();
      String lockKey = String.format("BI_%s_%s", tenantId, viewId);
      try (JedisLock jedisLock = new JedisLock(jedisCmd, lockKey, 1000 * 30)) {
        for (int logTimes = 0; logTimes < 3; logTimes++) { //获取锁重试
          if (jedisLock.tryLock()) {
            int status = statFieldDO.getStatus();
            log.info("statField is used in topology table tenantId:{},fieldId:{},topologyViewId:{},fieldStatus:{}", tenantId, fieldId, viewId, status);
            switch (status) {
              case 1, 2, 6 -> {
                TopologyTableDO tmpTopologyTable = topologyTableDO;
                for (int retryTimes = 0; retryTimes < 3; retryTimes++) {
                  try {
                    //检测状态是否一致
                    Integer usedStatus = this.checkStatus(tenantId, fieldId, tmpTopologyTable);
                    if (usedStatus != null && !StatFieldStatusEnum.isValidStatus(usedStatus)) {
                      int result = this.doCreateViewTopology(tenantId, viewId, TopologyTableStatus.Prepared.getValue());
                      log.info("after check doCreateViewTopology statField status tenantId:{},fieldId:{}, topologyViewId:{}, result:{}", tenantId, fieldId, viewId, result);
                      break;
                    }
                    //只有处于初始化中和计算中的图重新生成
//                    boolean checkResult = this.checkAggRule(tenantId, statFieldDO, tmpTopologyTable);
//                    log.info("after check tenantId:{},fieldId:{},topologyViewId:{},result:{}", tenantId, fieldId, viewId, checkResult);
//                    if (!checkResult) {
                      int result = this.doCreateViewTopology(tenantId, viewId, TopologyTableStatus.Prepared.getValue());
                      log.info("after check doCreateViewTopology tenantId:{},fieldId:{},topologyViewId:{}," +
                        "result:{}", tenantId, fieldId, viewId, result);
//                    }
                    break;
                  } catch (VersionConflictException e) {
                    log.error("doCreateViewTopology VersionConflictException error,tenantId:{},fieldId:{}, topologyViewId:{} retryTimes:{}", tenantId, fieldId, viewId, retryTimes, e);
                    tmpTopologyTable = topologyTableService.queryTopologyBySourceId(tenantId, viewId);
                  } catch (Exception e) {
                    log.error("doCreateViewTopology error,tenantId:{},fieldId:{},topologyViewId:{}", tenantId, fieldId, viewId, e);
                    break;
                  }
                }
              }
              case 0, 3, 5 -> {//指标禁用
                try {
                  if (GrayManager.isAllowByRule("use_all_agg_field", tenantId)) {
                    //检测状态是否一致
                    Integer usedStatus = this.checkStatus(tenantId, fieldId, topologyTableDO);
                    if (usedStatus != null && StatFieldStatusEnum.isValidStatus(usedStatus)) {
                      int result = this.doCreateViewTopology(tenantId, viewId, TopologyTableStatus.Prepared.getValue());
                      log.info(
                        "after check doCreateViewTopology statField status tenantId:{},fieldId:{},topologyViewId:{}," +
                        "result:{}", tenantId, fieldId, viewId, result);
                    }
                  } else {
                    int result = this.doCreateViewTopology(tenantId, viewId, TopologyTableStatus.Prepared.getValue());
                    log.info("after check doCreateViewTopology tenantId:{},fieldId:{},topologyViewId:{},result:{}", tenantId, fieldId, viewId, result);
                  }
                } catch (Exception e) {
                  log.error("doCreateViewTopology error,tenantId:{},fieldId:{},topologyViewId:{}", tenantId, fieldId, viewId, e);
                }
              }
            }
            break;
          } else {
            log.warn("onChangeAggRule tryLock fail tenantId:{},viewId:{},fieldId:{} retryTimes:{}", tenantId, topologyTableDO.getSourceId(), fieldId, logTimes);
            Uninterruptibles.sleepUninterruptibly(10, TimeUnit.SECONDS);
          }
        }
      } catch (Exception e) {
        log.error("onChangeAggRule checkAggRule error tenantId:{},fieldId:{},viewId:{}", tenantId, fieldId, topologyTableDO.getSourceId(), e);
      }
    });
  }

  /**
   * 判断当前的统计图是否包含fieldId
   *
   * @param tenantId 租户id
   * @param fieldId  fieldId
   * @param sourceId 统计图id
   * @return Bollean
   */
  public boolean containsField(String tenantId, String fieldId, String sourceId) {
    StatViewDO statViewDO = statViewDao.queryStatView(tenantId, sourceId);
    if (statViewDO == null) {
      log.warn("queryStatView statViewDO is null tenantId:{},viewId:{}", tenantId, sourceId);
      return false;
    }
    StatViewEntity statViewEntity = statViewDao.findStatViewById(tenantId, statViewDO);
    if (statViewEntity == null) {
      log.warn("findStatViewById statViewEntity is null tenantId:{},viewId:{}", tenantId, sourceId);
      return false;
    }
    return statViewEntity.containsField(fieldId);
  }

  /**
   * 对比topology 中aggRule 是否需要修改重跑
   *
   * @param statFieldDO     当前最新的agg fieldId
   * @param topologyTableDO topology aggRule
   * @return boolean
   */
  public boolean checkAggRule(String tenantId, StatFieldDO statFieldDO, TopologyTableDO topologyTableDO) {
    Preconditions.checkArgument(statFieldDO != null, "statFieldDO is null");
    Preconditions.checkArgument(topologyTableDO != null &&
                                CollectionUtils.isNotEmpty(topologyTableDO.getStatRuleList()), "topologyTableDO is null");
    log.info("begin compare topologyTable tenantId:{},fieldId:{},viewId:{}", tenantId, statFieldDO.getFieldId(), topologyTableDO.getSourceId());
    String commonDims = topologyTableDO.getCommonDims();
    List<String> fieldIds = Lists.newArrayList(statFieldDO.getFieldId());
    if (StringUtils.isNotBlank(commonDims)) {
      Set<String> dimFieldLocations = JSONArray.parseArray(commonDims)
                                               .stream()
                                               .map(String::valueOf)
                                               .collect(Collectors.toSet());
      Map<String, String> fieldLocationMap = JSON.parseObject(topologyTableDO.getStatFieldLocation(), new TypeReference<>() {
      });
      fieldLocationMap.forEach((fid, location) -> {
        if (dimFieldLocations.contains(location)) {
          fieldIds.add(fid);
        }
      });
    }
    StatViewPreArg statViewPreArg = StatViewPreArg.builder()
                                                  .allStatFieldIds(fieldIds)
                                                  .tenantId(tenantId)
                                                  .timeZone(statFieldDO.getTimeZone())
                                                  .sourceType(0)
                                                  .schemaId(statFieldDO.getSchemaId())
                                                  .aggFilterFieldIds(Lists.newArrayList())
                                                  .build();
    TopologyTable newTopologyTable = this.buildTopologyTableByByFieldIds(statViewPreArg, false);
    if (newTopologyTable != null && CollectionUtils.isNotEmpty(newTopologyTable.getStatRuleList())) {
      TopologyTableAggRule newStatRule = newTopologyTable.getStatRuleList().get(0);
      Optional<TopologyTableAggRule> aggRuleOptional = topologyTableDO.getStatRuleList()
                                                                      .stream()
                                                                      .filter(statRule -> Objects.equals(statFieldDO.getFieldId(), statRule.getFieldId()))
                                                                      .findFirst();
      //对比的条件1、规则一致；2、状态为0;
      return aggRuleOptional.filter(aggRule -> TopologyTableStatus.Prepared.getValue() == aggRule.getStatus() &&
                                               newStatRule.compareAggRule(aggRule)).isPresent();
    }
    return false;
  }

  /**
   * 反查topologytable中用到的fieldId是启用还是停用
   *
   * @param tenantId        租户id
   * @param fieldId         指标id
   * @param topologyTableDO topology table
   * @return
   */
  public Integer checkStatus(String tenantId, String fieldId, TopologyTableDO topologyTableDO) {
    if (GrayManager.isAllowByRule("use_all_agg_field", tenantId)) {
      String allFields = topologyTableDO.getAllAggStatField();
      if (StringUtils.isNotBlank(allFields)) {
        try {
          JSONObject statField = JSON.parseObject(allFields);
          return statField.getInteger(fieldId);
        } catch (Exception e) {
          log.error("json parse allAggStatField to json object error,tenantId:{},fields:{}", tenantId, allFields, e);
        }
      }
    }
    return null;
  }

  /**
   * 自定义维度变更
   */
  public void batchOnChangeCustomDimTheme(Set<CustomDimRefreshEvent> customDimRefreshEventSet) {
    Map<String, Set<String>> eiDimensionIdMapper = new HashMap<>();
    for (CustomDimRefreshEvent event : customDimRefreshEventSet) {
      List<String> dimensionIds = event.getDimensionIds();
      if (CollectionUtils.isNotEmpty(dimensionIds)) {
        eiDimensionIdMapper.computeIfAbsent(event.getTenantId(), k -> new HashSet<>()).addAll(dimensionIds);
      }
    }
    for (Map.Entry<String, Set<String>> entry : eiDimensionIdMapper.entrySet()) {
      String ei = entry.getKey();
      Set<String> dimensionIds = entry.getValue();
      Integer[] allStatus = BiMtDimensionStatusEnum.allStatus();
      List<BIMtDimensionDO> mtDimensionDOS = biMtDimensionDao.batchQueryDimensionByIds(ei, dimensionIds, allStatus);
      Optional.ofNullable(mtDimensionDOS)
              .ifPresent(mtDimensionDOList -> mtDimensionDOList.forEach(biMtDimensionDO -> this.onChangeMtDimension(ei, biMtDimensionDO)));
    }
  }

  /**
   * 自定义维度变更触发统计图重跑topology
   */
  private void onChangeMtDimension(String tenantId, BIMtDimensionDO biMtDimensionDO) {
    String dimensionId = biMtDimensionDO.getDimensionId();
    List<TopologyTableDO> topologyTableDOList = topologyTableService.findTopologyTablesByFieldId(tenantId, TopologyTableStatus.needCalcStatus(), new String[] {dimensionId});
    if (CollectionUtils.isEmpty(topologyTableDOList)) {
      log.info("can not find topologyTable by dimensionId:{}, tenantId:{}", dimensionId, tenantId);
    }
    for (TopologyTableDO topologyTableDO : topologyTableDOList) {
      String viewId = topologyTableDO.getSourceId();
      String lockKey = "BI_%s_%s".formatted(tenantId, viewId);
      try (JedisLock jedisLock = new JedisLock(jedisCmd, lockKey, 1000 * 30)) {
        // 获取锁重试
        for (int logTimes = 0; logTimes < 3; logTimes++) {
          if (!jedisLock.tryLock()) {
            log.warn("onChangeMtDimension tryLock fail tenantId:{},viewId:{},fieldId:{} retryTimes:{}", tenantId, topologyTableDO.getSourceId(), dimensionId, logTimes);
            Uninterruptibles.sleepUninterruptibly(10, TimeUnit.SECONDS);
          }
          Integer status = biMtDimensionDO.getStatus();
          BiMtDimensionStatusEnum statusEnum = BiMtDimensionStatusEnum.fromStatus(status);
          log.info("biMtDimension is used in topology table tenantId:{},dimensionId:{},topologyViewId:{},fieldStatus:{}", tenantId, dimensionId, viewId, status);
          switch (statusEnum) {
            case enable, init -> {
              TopologyTableDO tmpTopologyTable = topologyTableDO;
              for (int retryTimes = 0; retryTimes < 3; retryTimes++) {
                try {
                  // 校验自定义维度配置是否变更
                  boolean isRefresh = this.checkMtDimension(biMtDimensionDO, tmpTopologyTable);
                  log.info("after check tenantId:{},dimensionId:{},topologyViewId:{},isRefresh:{}", tenantId, dimensionId, viewId, isRefresh);
                  if (isRefresh) {
                    int result = this.doCreateViewTopology(tenantId, viewId, TopologyTableStatus.Prepared.getValue());
                    log.info("after check doCreateViewTopology tenantId:{},dimensionId:{},topologyViewId:{}, result:{}", tenantId, dimensionId, viewId, result);
                  }
                  break;
                } catch (VersionConflictException e) {
                  log.error("doCreateViewTopology VersionConflictException error,tenantId:{},dimensionId:{}, topologyViewId:{} retryTimes:{}", tenantId, dimensionId, viewId, retryTimes, e);
                  tmpTopologyTable = topologyTableService.queryTopologyBySourceId(tenantId, viewId);
                } catch (Exception e) {
                  log.error("doCreateViewTopology error,tenantId:{},dimensionId:{},topologyViewId:{}", tenantId, dimensionId, viewId, e);
                  break;
                }
              }
            }
            case unable, system_unable -> {
              log.info("bi_mt_dimension stop causes stat view topology table stop, dimensionId:{}, viewId:{}", dimensionId, viewId);
              this.configStopDisableTopology(tenantId, topologyTableDO);
            }
          }
        }
      } catch (Exception e) {
        log.error("onChangeMtDimension checkMtDimension error tenantId:{},dimensionId:{},viewId:{}", tenantId, dimensionId, topologyTableDO.getSourceId(), e);
      }
    }
  }

  /**
   * 校验自定义维度配置是否发生变更
   *
   * @param biMtDimensionDO 现有配置
   * @param tmpTopologyTable 统计图配置中存的
   * @return true:发生变更
   */
  private boolean checkMtDimension(BIMtDimensionDO biMtDimensionDO, TopologyTableDO tmpTopologyTable) {
    String dbDimensionConfig = biMtDimensionDO.getDimensionConfig();
    String dimensionId = biMtDimensionDO.getDimensionId();
    List<TopologyTableAggRule> statRuleList = tmpTopologyTable.getStatRuleList();
    for (TopologyTableAggRule aggRule : statRuleList) {
      List<CustomDimField> customerDimList = aggRule.getCustomerDimList();
      for (CustomDimField customDimField : customerDimList) {
        if (customDimField.getDimensionId().equals(dimensionId)) {
          return !customDimField.getDimensionConfigString().equals(dbDimensionConfig);
        }
      }
    }
    return false;
  }

  /**
   * 如果是明细主题（统计图目标联合分析）,需要在虚拟指标上加目标条件过滤
   * @param goalRuleIds
   * @param topologyTable
   */
  private void checkViewHasGoalRule(List<String> goalRuleIds, TopologyTable topologyTable) {
    if (CollectionUtils.isEmpty(goalRuleIds) || CollectionUtils.isEmpty(topologyTable.getStatRuleList())) {
      return;
    }
    List<String> goalRuleIdList = goalRuleIds.stream().map(ruleId -> ruleId.contains("|") ? ruleId : ruleId + "|nogoaldetail").toList();
    String goalRuleIdsWhere = String.format(" AND CONCAT(goal_rule_id,'|',goal_rule_detail_id) in('%s')", Joiner.on("','").join(goalRuleIdList));
    topologyTable.getStatRuleList().forEach(statRule -> {
      if (Constants.OLD_STAT_VIEW_GOAL_RULE_IDS.contains(statRule.getFieldId())) {
        statRule.getRootNodeTable().appendSubWheres(goalRuleIdsWhere);
      }
    });
  }

  /**
   * 按pg库重刷虚拟指标统计图
   * @param dbSyncId
   * @param all
   */
  public void batchRefreshGoalViewTopology(String dbSyncId, boolean all, boolean del) {
    Map<String, Map<String, Object>> repairGoalsMap = Maps.newHashMap();
    if (StringUtils.isNotBlank(dbSyncId)) {
      DBSyncInfoDO dbSyncInfoDO = dbUpdateEventDao.findSyncById(dbSyncId);
      batchRefreshGoalViewTopologyByDbSyncId(dbSyncInfoDO, repairGoalsMap, del);
      log.info("repairGoalsMap:{}", JSON.toJSONString(repairGoalsMap));
      return;
    }
    if (all) {
      List<DBSyncInfoDO> dbSyncInfoDOList = dbUpdateEventDao.findSyncByIdAll();
      dbSyncInfoDOList.forEach(dbSyncInfoDO -> batchRefreshGoalViewTopologyByDbSyncId(dbSyncInfoDO, repairGoalsMap, del) );
      log.info("repairGoalsMap:{}", JSON.toJSONString(repairGoalsMap));
    }
  }

  public void batchRefreshGoalViewTopologyByDbSyncId(DBSyncInfoDO dbSyncInfoDO, Map<String, Map<String, Object>> repairGoalsMap, boolean del) {
    try {
      if (dbSyncInfoDO != null && StringUtils.isNotBlank(dbSyncInfoDO.getLastSyncEis())) {
        String dbSyncId = dbSyncInfoDO.getId();
        String[] tenantIds = dbSyncInfoDO.getLastSyncEis().split(",");
        int success =0;
        int fail =0;
        for (String tenantId : tenantIds) {
          try {
            if (!userCenterService.isValidateStatus(tenantId)) {
              log.warn("this tenantId:{} is invalidate Status", tenantId);
              continue;
            }
            List<String> viewIdList = topologyTableService.queryGoalViewIdTopologyByEI(tenantId);
            if (CollectionUtils.isEmpty(viewIdList)) {
              continue;
            }
            if (del) {
              topologyTableService.deleteTopologyTables(tenantId, viewIdList, 0);
            }
            this.flashGoalStatViewByTenantId(tenantId, "1970-01-01 00:00:00", false);
            success=success+1;
          } catch (Exception e) {
            fail=fail+1;
            log.warn("batchRefreshGoalViewTopologyByDbSyncId get goal_id fail,dbSyncId:{}, tenantId:{}",dbSyncId, tenantId, e);
          }
        }
        Map<String, Integer> resultMap = Maps.newHashMap();
        resultMap.put("success", success);
        resultMap.put("fail", fail);
        repairGoalsMap.computeIfAbsent(dbSyncId, key -> Maps.newHashMap()).putAll(resultMap);
      }
    } catch (Exception e) {
      log.error("batchRefreshGoalViewTopologyByDbSyncId ERROR, dbSyncInfoDO:{}", JSON.toJSONString(dbSyncInfoDO), e);
    }
  }
}
