package com.fxiaoke.bi.warehouse.core.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;


/**
 * 统计图字段(stat_field)
 */
@Table(name = "stat_field")
@Data
public class StatFieldDO {
  @Column(name = "tenant_id")
  private String tenantId;

  @Id
  @Column(name = "field_id")
  private String fieldId;

  @Column(name = "udf_field_id")
  private String udfFieldId;

  @Column(name = "field_name")
  private String fieldName;

  @Column(name = "field_name_i18n_key")
  private String fieldNameI18NKey;

  @Column(name = "schema_id")
  private String schemaId;

  @Column(name = "field_location")
  private String fieldLocation;

  @Column(name = "paas_field_location")
  private String paasFieldLocation;

  @Column(name = "db_field_name")
  private String dbFieldName;

  @Column(name = "field_type")
  private String fieldType;

  @Column(name = "sub_field_type")
  private String subFieldType;

  @Column(name = "db_obj_name")
  private String dbObjName;

  @Column(name = "agg_dim_type")
  private String aggDimType;

  @Column(name = "status")
  private int status = -1;

  @Column(name = "is_key")
  private int isKey = 0;

  @Column(name = "is_show")
  private int isShow = 1;

  @Column(name = "is_level")
  private int isLevel = 0;

  @Column(name = "creator")
  private String creator;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "last_modifier")
  private String lastModifier;

  @Column(name = "last_modified_time")
  private Date lastModifiedTime;

  @Column(name = "is_deleted")
  private int isDeleted = -1;

  @Column(name = "enum_name")
  private String enumName;

  @Column(name = "format_str")
  private String formatStr;

  @Column(name = "ref_obj_name")
  private String refObjName;

  @Column(name = "ref_key_field")
  private String refKeyField;

  @Column(name = "ref_target_field")
  private String refTargetField;

  @Column(name = "field_order")
  private int fieldOrder = -1;

  @Column(name = "type")
  private String type;

  @Column(name = "unable_reason")
  private String unableReason;

  @Column(name = "is_pre")
  private int isPre = 0;

  @Column(name = "object_describe_api_name")
  private String objectDescribeApiName;

  @Column(name = "calc_feature")
  private String calcFeature;

  @Column(name = "version")
  private String version;

  /**
   * chenshy 20181106
   * 这个是agg_rule的字段，是聚合字段的方式
   */
  @Column(name = "check_field_aggregate_type")
  private Integer checkFieldAggregateType;

  @Column(name = "is_hide")
  private int isHide;


  /**
   * 时间标尺逻辑列名展示名
   */
  @Column(name = "count_time_api_display_name_field")
  private String countTimeApiDisplayNameField;
  @Column(name = "count_time_api_name_field")
  private String countTimeApiNameField;

  @Column(name = "theme_api_name")
  private String themeApiName;

  @Column(name = "is_null_action_date")
  private String isNullActionDate;

  //时区
  @Column(name = "time_zone")
  private String timeZone;

  /**
   * 时间标尺lookup字段展示名
   */
  @Column(name = "count_time_lookup_field_display_name")
  private String countTimeLookupFieldDisplayName;

  /**
   * 是否多时区无关
   */
  @Column(name = "not_use_multitime_zone")
  private Boolean notUseMultitimeZone;

  @Column(name = "count_time_lookup_field_name")
  private String countTimeLookupFieldName;

  /**
   * 下游图ID
   */
  @Column(name = "downstream_view_id")
  private String downstreamViewId;
  /**
   * 下游字段ID
   */
  @Column(name = "downstream_field_id")
  private String downstreamFieldId;
  /**
   * 是否需要多时区
   */
  @Column(name = "downstream_is_null_action_date")
  private String downstreamIsNullActionDate;

  @Column(name = "limit_control_tag")
  private Integer limitControlTag;

  @Column(name = "outbound_tenant_id")
  private String outboundTenantId;

  @Column(name = "downstream_policy_structure")
  private String downstreamPolicyStructure;
}
