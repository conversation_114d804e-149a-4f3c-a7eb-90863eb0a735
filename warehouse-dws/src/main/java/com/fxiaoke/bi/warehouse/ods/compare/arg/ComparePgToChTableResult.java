package com.fxiaoke.bi.warehouse.ods.compare.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *  <AUTHOR>
 *  @date 2024-07-26
 *  @desc ch和pg表的数量差异
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComparePgToChTableResult {

    /**
     * ch相对于pg存在差异的表-多的表
     */
    private List<String> chDiffTable;

    /**
     * pg相对于ch存在差异的表-多的表
     */
    private List<String> pgDiffTable;
}
