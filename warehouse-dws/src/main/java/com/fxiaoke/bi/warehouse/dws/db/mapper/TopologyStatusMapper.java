package com.fxiaoke.bi.warehouse.dws.db.mapper;

import com.fxiaoke.bi.warehouse.common.provider.CommonProvider;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtTopologyStatusDO;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/1/1
 */
@Mapper
public interface TopologyStatusMapper extends ICrudMapper<BIMtTopologyStatusDO>, ITenant<TopologyStatusMapper> {

  @Update("update bi_mt_topology_status set status=#{status} where tenant_id=#{tenantId} and source_id=any" +
    "(array[#{viewIds}])")
  int batchUpdateTopologyStatus(@Param("tenantId") String tenantId,
                                @Param("viewIds") String[] viewIds,
                                @Param("status") int status);

  @Update("update bi_mt_topology_status set status=#{status} where tenant_id=#{tenantId} and source_id=any" +
    "(array[#{viewIds}]) and status=#{preStatus}")
  int batchUpdateStatViewStatusWithCompare(@Param("tenantId") String tenantId,
                                           @Param("viewIds") String[] viewIds,
                                           @Param("status") int status,
                                           @Param("preStatus") int preStatus);

  /**
   * @param tenantId
   * @param viewIds
   * @param status
   * @return
   */
  int batchUpdateStatus(@Param("tenantId") String tenantId,
                        @Param("viewIds") String[] viewIds,
                        @Param("status") int status,
                        @Param("lastModifiedTime") Long lastModifiedTime,
                        @Param("source") Integer source);

  /**
   * 批量插入topology status
   *
   * @param list
   * @param primaryKey
   * @return
   */
  @InsertProvider(type = CommonProvider.class, method = "batchUpsert")
  int batchUpsertStatViewStatus(@Param(CommonProvider.FKey) List<BIMtTopologyStatusDO> list,
                                @Param(CommonProvider.SKey) Set<String> primaryKey);

  /**
   * 批量查询bi_mt_topology_status
   *
   * @param tenantId
   * @param viewIds
   * @return
   */
  @Select("select * from bi_mt_topology_status where tenant_id=#{tenantId} and source_id=any(array[#{viewIds}]) and " +
    "is_deleted=0")
  List<BIMtTopologyStatusDO> batchQueryTopologyStatus(@Param("tenantId") String tenantId,
                                                      @Param("viewIds") String[] viewIds);

  /**
   * 根据viewId查找被停用的图列表
   * @param tenantId 租户Id
   * @param viewIds 图id集合
   * @return
   */
  @Select("""
  select * from bi_mt_topology_status where tenant_id=#{tenantId} and source_id=any(array[#{viewIds}]) and status=any(array[#{status}])
  """)
  List<BIMtTopologyStatusDO> findStatViewStatusByStatus(@Param("tenantId") String tenantId,
                                                        @Param("viewIds") String[] viewIds,
                                                        @Param("status") int[] status);

}
