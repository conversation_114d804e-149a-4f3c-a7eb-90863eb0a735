package com.fxiaoke.bi.warehouse.dws.model;

import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.dws.agg.bean.AggCalResult;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * DB同步更新完成事件
 *
 * <AUTHOR>
 * @since 2023/3/2
 */
@Data
public class DBUpdatedEvent {
  /**
   * db_sync_info#id
   */
  private String id;
  /**
   * 下游同步信息id
   * db_agg_sync_info#id
   */
  private String aggSyncId;
  /**
   * 变化的DB
   */
  private String chJdbcUrl;
  /**
   * pg db
   */
  private String pgJdbcUrl;
  /**
   *
   */
  private String pgSchema;
  /**
   * 增加的批次
   */
  private long batchNum;
  /**
   * 上下游同步的批次号
   */
  private Long dsBatchNum;
  /**
   * DB同步任务状态
   */
  private int status;
  /**
   * 表对应变化的租户
   */
  private Map<String, Set<String>> tableTenantIdSetMap;
  /**
   * 每个租户变化的BI对象
   */
  private Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap;
  /**
   * 同步和计算解耦后得一个批次集合
   */
  private Long[] batchNums;
  /**
   * 是否开启增量分区同步0:否,1:是
   */
  private Integer allowIncPartition;
  /**
   * 是否开启计算分区 0:否，1:是
   */
  private int allowCalPartition;

  /**
   * 当前db所有的租户列表
   */
  private String lastSyncEis;
  /**
   * agg info 版本
   */
  private int aggInfoVersion;
  /**
   * 本次计算的flow集合逗号分隔
   */
  private String syncFlows;
  /**
   * 最后修改时间主要用于状态超时校验
   */
  private long lastModifiedTime;
  /**
   * 增量系统修改时间范围
   */
  private Map<String,BIPair<Long,Long>> incSysModifiedTimeRangeMap;
  /**
   * 保存过程sql的临时缓存
   */
  private AggCalResult aggCalResult;
  /**
   * 生成租户id和变更表集合映射对象
   *
   * @return
   */
  public Map<String/*tenantId*/, Set<String>/*tables*/> buildTenantIdTableMapper() {
    Map<String, Set<String>> tenantIdTableMapper = Maps.newHashMap();
    if (tableTenantIdSetMap != null) {
      tableTenantIdSetMap.forEach((table, tenantIds) -> tenantIds.forEach(tenantId -> {
        tenantIdTableMapper.computeIfAbsent(tenantId, key -> new HashSet<>()).add(table);
      }));
    }
    return tenantIdTableMapper;
  }

  public String pgDbName() {
    if (StringUtils.isNotBlank(this.pgJdbcUrl)) {
      return CommonUtils.getDBName(this.pgJdbcUrl);
    }
    return "";
  }

  public String chDbName() {
    if (StringUtils.isNotBlank(this.chJdbcUrl)) {
      return CommonUtils.getDBName(this.chJdbcUrl);
    }
    return "";
  }
  /**
   * 变更的表集合
   * @return Set
   */
  public Set<String> changeTables() {
    if (this.tableTenantIdSetMap != null) {
      return this.tableTenantIdSetMap.keySet();
    }
    return null;
  }

  /**
   * 返回排序好的批次号
   * @return
   */
  public Long[] sortedBatchNums(){
    if(this.batchNums!=null){
      Arrays.sort(this.batchNums);
    }
    return this.batchNums;
  }

  /**
   * 返回当前计算周期内所有变更的租户
   * @return
   */
  public Set<String> findModifiedTenantIds(){
    if(tenantIdObjectDescribeApiNamesMap!=null){
      return tenantIdObjectDescribeApiNamesMap.keySet();
    }
    return Sets.newHashSet();
  }
}
