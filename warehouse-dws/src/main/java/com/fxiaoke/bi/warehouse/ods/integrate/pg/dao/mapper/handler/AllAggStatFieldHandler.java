package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

public class AllAggStatFieldHandler extends BaseTypeHandler<Map<String, String>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps,
                                    int i,
                                    Map<String, String> parameter,
                                    JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String st = rs.getString(columnName);
        return JSON.parseObject(st, new TypeReference<>() {
        });
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String st = rs.getString(columnIndex);
        return JSON.parseObject(st, new TypeReference<>() {
        });
    }

    @Override
    public Map<String, String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String st = cs.getString(columnIndex);
        return JSON.parseObject(st, new TypeReference<>() {
        });
    }
}
