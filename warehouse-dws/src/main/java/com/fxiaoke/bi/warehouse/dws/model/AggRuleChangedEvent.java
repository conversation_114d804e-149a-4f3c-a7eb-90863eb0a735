package com.fxiaoke.bi.warehouse.dws.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @Author:jief
 * @Date:2024/1/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggRuleChangedEvent {
  private String tenantId;
  private String ruleId;
  private String schemaEnName;
  private boolean isTrigger;
  private int schemaChangeType;
  private String tags;
  private long createDate;

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AggRuleChangedEvent that = (AggRuleChangedEvent) o;
    return tenantId.equals(that.tenantId) && ruleId.equals(that.ruleId) && schemaEnName.equals(that.schemaEnName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tenantId, ruleId, schemaEnName);
  }
}
