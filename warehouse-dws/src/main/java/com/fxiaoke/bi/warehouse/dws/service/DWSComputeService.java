package com.fxiaoke.bi.warehouse.dws.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.bi.warehouse.common.arg.AggRequestContext;
import com.fxiaoke.bi.warehouse.common.bean.CHColumn;
import com.fxiaoke.bi.warehouse.common.db.dao.DbSyncInfoFlowDao;
import com.fxiaoke.bi.warehouse.common.db.entity.StatViewStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.er.AggRuleType;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.mq.message.DBUpdateMessage;
import com.fxiaoke.bi.warehouse.common.util.*;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.core.db.entity.BiAggLogDO;
import com.fxiaoke.bi.warehouse.dws.agg.bean.AggCalResult;
import com.fxiaoke.bi.warehouse.dws.agg.bean.CHTable;
import com.fxiaoke.bi.warehouse.dws.agg.service.impl.AggCal2StockAfterHandler;
import com.fxiaoke.bi.warehouse.dws.agg.service.impl.AggCal2StockBeforeHandler;
import com.fxiaoke.bi.warehouse.dws.agg.service.impl.AggInc2CalHandler;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBAggInfoDAO;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBUpdateEventDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.exception.RetryException;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.transform.model.MergeStatusEnum;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.service.DBTransferService;
import com.fxiaoke.bi.warehouse.ods.service.PGMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.QiXinNotifyService;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.helper.CollectionHelper;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.helper.Pair;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import com.google.common.base.CharMatcher;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 处理DWS层计算
 *
 * <AUTHOR>
 * @since 2023/3/2
 */
@Slf4j
@Service
public class DWSComputeService {
  @Resource
  private TopologyTableService topologyTableService;
  @Resource
  private ClickHouseService clickHouseService;
  @Resource
  private DBUpdateEventDao dbUpdateEventDao;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  @Resource
  private EIEAConverter eieaConverter;
  @Autowired
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Resource
  private DBAggInfoDAO dbAggInfoDAO;
  @Resource
  private DbSyncInfoFlowDao dbSyncInfoFlowDao;
  @Resource
  private DBTransferService dbTransferService;
  @Resource
  private AggInc2CalHandler aggInc2CalHandler;
  @Resource
  private AggCal2StockBeforeHandler aggCal2StockBeforeHandler;
  @Resource
  private AggCal2StockAfterHandler aggCal2StockAfterHandler;
  @Resource
  private BIAggLogCollector biAggLogCollector;
  @Resource
  private PGMetadataService pgMetadataService;
  private static final ThreadFactory factory = new ThreadFactoryBuilder().setDaemon(true).setNameFormat("ch-cal-%d").build();
  private volatile int readTimeOut;
  private volatile double publicThreadRate;
  private volatile double standLoneThreadRate;
  private Long maxBytesBeforeExternalSort;
  private Long maxBytesBeforeExternalGroupBy;
  private Long groupByTwoLevelThresholdBytes;
  /**
   * 不包含before数据的表，因此不用删除before数据
   */
  private volatile Set<String> haveNoBeforeDataTables = Sets.newHashSet();
  /**
   * 自定义线程数
   */
  private Map<String, Double> tenantThreadRate = Maps.newHashMap();
  /**
   * 缓存计算中的pgdb列表，重启后，或计算完毕后需要删除
   */
  public static final Set<String> calculatePgDB = Sets.newHashSet();
  public static final Set<String> copyBeforeIng = Sets.newHashSet();
  public static final Set<String> copyAfterIng = Sets.newHashSet();
  public static final Set<String> copyInc2CalAfterIng = Sets.newHashSet();
  public static final Set<String> copyCal2StockAfterIng = Sets.newHashSet();
  public static final Set<String> copyCal2StockBeforeIng = Sets.newHashSet();
  /**
   * 离线计算group by 优化setting ei^viewId
   */
  private Set<String> preparedGroupBySettings = Sets.newHashSet();
  /**
   * 增量计算group by 优化setting ei^viewId
   */
  private Set<String> usedGroupBySettings = Sets.newHashSet();
  private int fixRetryTimes = 5;
  private int maxInsertThreads=2;
  private int minInsertBlockSizeBytes=524288000;
  private int minInsertBlockSizeRows=3145728;
  /**
   * 租户id是ei的表集合
   */
  private Set<String> eiTables = Sets.newHashSet();
  //临时缓存agg_data 是否包含batchNum和RuleId字段。
  private final ConcurrentHashMap<String,Boolean> checkAggDataColumn = new ConcurrentHashMap<>();

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", iConfig -> {
      readTimeOut = iConfig.getInt("ch_socket_time_out", 30 * 60 * 1000);
      publicThreadRate = iConfig.getDouble("ch_public_thread_rate", 3.0);
      standLoneThreadRate = iConfig.getDouble("ch_standLone_thread_rate", 5.0);
      haveNoBeforeDataTables = Sets.newHashSet(Splitter.on(",").splitToList(iConfig.get("no_need_before_tables", "")));
      tenantThreadRate = JSON.parseObject(iConfig.get("ch_tenant_thread_rate", "{}"), new TypeReference<>() {
      });
      maxBytesBeforeExternalSort = iConfig.getLong("max_bytes_before_external_sort", 1073741824);
      maxBytesBeforeExternalGroupBy = iConfig.getLong("max_bytes_before_external_group_by", 1073741824);
      groupByTwoLevelThresholdBytes = iConfig.getLong("group_by_two_level_threshold_bytes", 1073741824);
      this.maxInsertThreads = iConfig.getInt("max_insert_threads", 2);
      minInsertBlockSizeBytes = iConfig.getInt("min_insert_block_size_bytes", 524288000);
      minInsertBlockSizeRows = iConfig.getInt("min_insert_block_size_rows", 3145728);
      String pgs = iConfig.get("prepared_group_setting", "");
      preparedGroupBySettings = Sets.newHashSet(Splitter.on(CharMatcher.anyOf(",|")).splitToList(pgs));
      String ugs = iConfig.get("used_group_setting", "");
      usedGroupBySettings = Sets.newHashSet(Splitter.on(CharMatcher.anyOf(",|")).splitToList(ugs));
      fixRetryTimes = iConfig.getInt("agg_fail_retry_times", 5);
      String eiTablesStr = iConfig.get("ei_tables","dim_sys_area_gray,dim_sys_date,sale_action_stage");
      eiTables= Sets.newHashSet(Splitter.on(CharMatcher.anyOf(",|")).splitToList(eiTablesStr));
    });
  }

  /**
   * 处理数据库同步完成事件
   *
   * @param dbUpdateMessage 数据库同步完成事件
   */
  public void dbDataUpdated(DBUpdateMessage dbUpdateMessage) {
    String lockKey = dbUpdateMessage.getId();
    try (JedisLock jedisLock = new JedisLock(jedisCmd, lockKey, 1000 * 60 * 20)) {
      if (jedisLock.tryLock()) {
        StopWatch stopWatch = StopWatch.createStarted("dbUpdateEvent:" + dbUpdateMessage.getId());
        DBSyncInfoDO dbSyncInfoDO = dbUpdateEventDao.findSyncById(dbUpdateMessage.getId());
        if (dbSyncInfoDO == null) {
          log.error("can not findSyncById id:{}", JSON.toJSONString(dbUpdateMessage));
          return;
        }
        BIAggSyncInfoDO biAggSyncInfoDO=null;
        if(StringUtils.isNotBlank(dbUpdateMessage.getAggSyncId())){
          biAggSyncInfoDO= dbUpdateEventDao.findBIAggSyncById(dbUpdateMessage.getAggSyncId());
        }
        stopWatch.start("findUpdateApiNames");
        DBUpdatedEvent dbUpdateEvent = dbUpdateEventDao.createDbUpdateEventPlus(dbSyncInfoDO, dbUpdateMessage.getBatchNum(), biAggSyncInfoDO, dbUpdateMessage.getDsBatchNum());
        stopWatch.stop("findUpdateApiNames");
        if (dbUpdateEvent == null) {
          log.info("findByIdAndBatchNum is null dbSyncId:{},batchNum:{},detail:{},aggSyncId:{},dsBatchNum:{}", dbUpdateMessage.getId(), dbUpdateMessage.getBatchNum(), stopWatch.prettyPrint(), dbUpdateMessage.getAggSyncId(), dbUpdateMessage.getDsBatchNum());
          return;
        }
        TransferEvent transferEvent = TransferEvent.builder().dbURL(dbUpdateEvent.getPgJdbcUrl()).chDbURL(dbUpdateEvent.getChJdbcUrl()).schema(dbUpdateEvent.getPgSchema()).build();
        SyncStatusEnum syncStatusEnum = SyncStatusEnum.createFromStatus(dbUpdateEvent.getStatus());
        switch (syncStatusEnum) {
          case SYNC_ED -> {
            if (!this.before(dbUpdateEvent, stopWatch)) {
              String msg = MessageFormat.format("before updateDBStatus:{0} fail ,chDB:{1},pgDB:{2},aggSyncId:{3}", SyncStatusEnum.AGG_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateEvent.getAggSyncId());
              log.error(msg);
              QiXinNotifyService.sendTextMessage(dbUpdateEvent.pgDbName(), dbUpdateEvent.getPgSchema(), dbUpdateEvent.chDbName(), msg);
              return;
            }
            calculatePgDB.add(dbUpdateMessage.getId());
          }
          case COPY_BEFORE_ING -> {
            if (copyBeforeIng.contains(dbUpdateEvent.getId())) {
              String msg = String.format("copyBeforeIng is running or updateDBStatus fail, calDBStatus:%s,chDB:%s,pgDB:%s,aggSyncId:%s", SyncStatusEnum.COPY_BEFORE_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateMessage.getAggSyncId());
              Utils.checkOverTime(SyncStatusEnum.COPY_BEFORE_ING,transferEvent,dbUpdateEvent.getId()+":COPY_BEFORE_ING",dbUpdateEvent.getLastModifiedTime(),60*60*1000L,msg,null);
              return;
            }
            if(!this.before(dbUpdateEvent,stopWatch)){
              String msg = MessageFormat.format("copyBeforeIng fail, calDBStatus:{0},chDB:{1},pgDB:{2},aggSyncId:{3}", SyncStatusEnum.COPY_BEFORE_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateMessage.getAggSyncId());
              log.error(msg);
              QiXinNotifyService.sendTextMessage(dbUpdateEvent.pgDbName(), dbUpdateEvent.getPgSchema(), dbUpdateEvent.chDbName(), msg);
              return;
            }
          }
          case COPY_AFTER_ING -> {
            if (copyAfterIng.contains(dbUpdateEvent.getId())) {
              String msg = String.format("copyAfterIng is running or updateDBStatus fail, calDBStatus:%s,chDB:%s,pgDB:%s,aggSyncId:%s", SyncStatusEnum.COPY_AFTER_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateMessage.getAggSyncId());
              Utils.checkOverTime(SyncStatusEnum.COPY_AFTER_ING,transferEvent,dbUpdateEvent.getId()+":COPY_AFTER_ING",dbUpdateEvent.getLastModifiedTime(),60*60*1000L,msg,null);
              return;
            }
            if(!this.copyAfter(dbUpdateEvent, aggInc2CalHandler.prepare(dbUpdateEvent,stopWatch),stopWatch)){
              String msg = MessageFormat.format("copyAfterIng fail, calDBStatus:{0},chDB:{1},pgDB:{2},aggSyncId:{3}", SyncStatusEnum.COPY_AFTER_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateMessage.getAggSyncId());
              log.error(msg);
              QiXinNotifyService.sendTextMessage(dbUpdateEvent.pgDbName(), dbUpdateEvent.getPgSchema(), dbUpdateEvent.chDbName(), msg);
              return;
            }
          }
          case INC_2_CAL_AFTER_ING -> {
            if (copyInc2CalAfterIng.contains(dbUpdateEvent.getId())) {
              String msg = String.format("INC_2_CAL_AFTER_ING is running or updateDBStatus fail, calDBStatus:%s,chDB:%s,pgDB:%s,aggSyncId:%s", SyncStatusEnum.INC_2_CAL_AFTER_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateMessage.getAggSyncId());
              Utils.checkOverTime(SyncStatusEnum.INC_2_CAL_AFTER_ING,transferEvent,dbUpdateEvent.getId()+":INC_2_CAL_AFTER_ING",dbUpdateEvent.getLastModifiedTime(),60*60*1000L,msg,null);
              return;
            }
            if (!this.before(dbUpdateEvent, stopWatch)) {
              String msg = MessageFormat.format("INC_2_CAL_AFTER_ING updateDBStatus:{0} fail ,chDB:{1},pgDB:{2},aggSyncId:{3}", SyncStatusEnum.INC_2_CAL_AFTER_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateEvent.getAggSyncId());
              log.error(msg);
              QiXinNotifyService.sendTextMessage(dbUpdateEvent.pgDbName(), dbUpdateEvent.getPgSchema(), dbUpdateEvent.chDbName(), msg);
              return;
            }
          }
          case CAL_2_STOCK_BEFORE_ING -> {
            if (copyCal2StockBeforeIng.contains(dbUpdateEvent.getId())) {
              String msg = String.format("CAL_2_STOCK_BEFORE_ING is running or updateDBStatus fail, calDBStatus:%s,chDB:%s,pgDB:%s,aggSyncId:%s", SyncStatusEnum.CAL_2_STOCK_BEFORE_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateMessage.getAggSyncId());
              Utils.checkOverTime(SyncStatusEnum.CAL_2_STOCK_BEFORE_ING,transferEvent,dbUpdateEvent.getId()+":CAL_2_STOCK_BEFORE_ING",dbUpdateEvent.getLastModifiedTime(),60*60*1000L,msg,null);
              return;
            }
            AggRequestContext aggRequestContext = AggRequestContext.getInstance(Map.of("DBUpdatedEvent", dbUpdateEvent,"StopWatch", stopWatch,"AggCalResult", aggInc2CalHandler.prepare(dbUpdateEvent, stopWatch)));
            if (!aggCal2StockBeforeHandler.doHandler(aggRequestContext)) {
              String msg = MessageFormat.format("CAL_2_STOCK_BEFORE_ING updateDBStatus:{0} fail ,chDB:{1},pgDB:{2},aggSyncId:{3}", SyncStatusEnum.CAL_2_STOCK_BEFORE_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateEvent.getAggSyncId());
              log.error(msg);
              QiXinNotifyService.sendTextMessage(dbUpdateEvent.pgDbName(), dbUpdateEvent.getPgSchema(), dbUpdateEvent.chDbName(), msg);
              return;
            }
          }
          case CAL_2_STOCK_AFTER_ING -> {
            if (copyCal2StockAfterIng.contains(dbUpdateEvent.getId())) {
              String msg = String.format("CAL_2_STOCK_AFTER_ING is running or updateDBStatus fail, calDBStatus:%s,chDB:%s,pgDB:%s,aggSyncId:%s", SyncStatusEnum.CAL_2_STOCK_AFTER_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateMessage.getAggSyncId());
              Utils.checkOverTime(SyncStatusEnum.CAL_2_STOCK_AFTER_ING,transferEvent,dbUpdateEvent.getId()+":CAL_2_STOCK_AFTER_ING",dbUpdateEvent.getLastModifiedTime(),60*60*1000L,msg,null);
              return;
            }
            AggRequestContext aggRequestContext = AggRequestContext.getInstance(Map.of("DBUpdatedEvent", dbUpdateEvent, "StopWatch", stopWatch, "AggCalResult", aggInc2CalHandler.prepare(dbUpdateEvent, stopWatch)));
            if (!aggCal2StockAfterHandler.doHandler(aggRequestContext)) {
              String msg = MessageFormat.format("CAL_2_STOCK_AFTER_ING updateDBStatus:{0} fail ,chDB:{1},pgDB:{2},aggSyncId:{3}", SyncStatusEnum.CAL_2_STOCK_AFTER_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateEvent.getAggSyncId());
              log.error(msg);
              QiXinNotifyService.sendTextMessage(dbUpdateEvent.pgDbName(), dbUpdateEvent.getPgSchema(), dbUpdateEvent.chDbName(), msg);
              return;
            }
          }
          case AGG_ING -> {
            if (calculatePgDB.contains(dbUpdateMessage.getId())) {
              String msg = String.format("agg is running or updateDBStatus fail, calDBStatus:%s,chDB:%s,pgDB:%s,aggSyncId:%s", SyncStatusEnum.AGG_ING, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateMessage.getAggSyncId());
              Utils.checkOverTime(SyncStatusEnum.AGG_ING,transferEvent,dbUpdateEvent.getId()+":AGG_ING",dbUpdateEvent.getLastModifiedTime(),60*60*1000L,msg,null);
              return;
            }
            calculatePgDB.add(dbUpdateMessage.getId());
          }
          case EXCHANGE_AGG -> {
            if (Objects.isNull(dbUpdateEvent.getAllowIncPartition())) {
              log.warn("allowIncPartition is null, calDBStatus:{},dbId:{},currentStatus:{},aggSyncId:{}", SyncStatusEnum.EXCHANGE_AGG, dbUpdateEvent.getId(), syncStatusEnum.getDesc(), dbUpdateMessage.getAggSyncId());
              return;
            }
            dbTransferService.dealWithExchangeStatus(dbUpdateEvent.getId(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateEvent.getPgSchema(), dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getAllowIncPartition());
            return;
          }
          case AGG_ED -> {
            String msg = String.format("agg_ed is running or updateDBStatus fail, calDBStatus:%s,chDB:%s,pgDB:%s,aggSyncId:%s", SyncStatusEnum.AGG_ED, dbUpdateEvent.getChJdbcUrl(), dbUpdateEvent.getPgJdbcUrl(), dbUpdateMessage.getAggSyncId());
            Utils.checkOverTime(SyncStatusEnum.AGG_ED, transferEvent,
              dbUpdateEvent.getId() + ":AGG_ED", dbUpdateEvent.getLastModifiedTime(), 60 * 60 * 1000L, msg, null);
            return;
          }
          default -> {
            log.error("this status:{} should skip id:{}, aggSyncId:{}", syncStatusEnum.getDesc(), dbUpdateMessage.getId(), dbUpdateMessage.getAggSyncId());
            return;
          }
        }
        try {
          this.compute(dbUpdateEvent, stopWatch);
          this.after(dbUpdateEvent, stopWatch);
        } finally {
          calculatePgDB.remove(dbUpdateMessage.getId());//清理执行状态
          Utils.resetDelayTime(Lists.newArrayList(
            dbUpdateEvent.getId() + ":COPY_BEFORE_ING",
            dbUpdateEvent.getId() + ":COPY_AFTER_ING",
            dbUpdateEvent.getId() + ":AGG_ING",
            dbUpdateEvent.getId() + ":INC_2_CAL_AFTER_ING",
            dbUpdateEvent.getId() + ":CAL_2_STOCK_BEFORE_ING", dbUpdateEvent.getId() + ":CAL_2_STOCK_AFTER_ING",dbUpdateEvent.getId() + ":AGG_ED"));
        }
        log.info("dbDataUpdated finished pgDb:{},chDB:{},schema:{},batchNum:{},detail:{}", dbSyncInfoDO.getPgDB(), dbSyncInfoDO.getChDB(), dbSyncInfoDO.getPgSchema(), dbSyncInfoDO.getBatchNum(), stopWatch.prettyPrint());
      } else {
        log.warn("can not try lock to update Event:{}", JSON.toJSONString(dbUpdateMessage));
      }
    } catch (Exception e) {
      String errorMsg = String.format("dbDataUpdated error message:%s", JSON.toJSONString(dbUpdateMessage));
      log.error(errorMsg, e);
      QiXinNotifyService.sendTextMessage(dbUpdateMessage.getPgDB(), dbUpdateMessage.getSchema(), dbUpdateMessage.getChDB(), errorMsg);
      throw new RuntimeException(JSON.toJSONString(dbUpdateMessage), e);
    }
  }

  //给单元测试用
  public void computeData(DBUpdatedEvent event, StopWatch stopWatch){
    this.compute(event, stopWatch);
  }
  /**
   * 一个批次内，相同指标如果已经计算过了就不用重新计算了，直接从agg_data中复制一份
   * @param statViewMonitor
   * @param sqlList
   * @param statRuleMonitor
   * @return
   */
  private List<String> processSameRuleSql(TopologyTableMonitor statViewMonitor,
                                          List<String> sqlList,
                                          long batchNum,
                                          TopologyTable topologyTable,
                                          String calculatedRuleKey,
                                          TopologyTableAggRuleMonitor statRuleMonitor,
                                          Map<String, Map<String, String>> ruleHasCalculated){
    String tenant_id = statViewMonitor.getTenantId();
    try{
      boolean haseColumn = checkAggDataColumn.computeIfAbsent(topologyTable.getDatabase(), key -> clickHouseService.tableColumnCheck(tenant_id, topologyTable.getDatabase(), "agg_data", Arrays.asList("rule_id", "batch_num")));
      if(!haseColumn){
        log.info("processSameRuleSql agg_data not contains both rule_id and batch_num");
        return sqlList;
      }
      int viewVersion = 0;
      String viewId = statViewMonitor.getStatViewUniqueKey();
      if (GrayManager.isAllowByRule("stat_status_from_table_merge", tenant_id)) {
        viewVersion = statViewMonitor.getVersion();
      }
      if(ruleHasCalculated.containsKey(calculatedRuleKey)){
        //当前批次这个指标在别的同维度的图中已经计算过了，从agg_data中直接拷贝结果数据
        Map<String, String> hasCalcInfo = ruleHasCalculated.get(calculatedRuleKey);
        String hasCalcViewId = hasCalcInfo.get(Constants.CALC_SAME_RULE_VIEW_UNIQUE_ID);
        String hasCalcViewVersion = hasCalcInfo.get(Constants.CALC_SAME_RULE_VIEW_VERSION);
        String hasCalcSourceId = hasCalcInfo.get(Constants.CALC_SAME_RULE_VIEW_SOURCE_ID);

        List<String> newSqlList = Lists.newArrayList();
        String aggStr = topologyTableService.getAggDataInsertSelectSql(topologyTable, viewId, viewVersion, batchNum, hasCalcViewId, hasCalcViewVersion, hasCalcSourceId, statRuleMonitor);
        if(StringUtils.isNotEmpty(aggStr)) {
          newSqlList.add(aggStr);
          log.info("processSameRuleSql rule with same dims has calculated, {}, ({}), {}, {}, {}", tenant_id, calculatedRuleKey, batchNum, viewId, aggStr);
          if(GrayManager.isAllowByRule("allow_same_rule_execute_once_take", tenant_id) ||
                  GrayManager.isAllowByRule(String.format("allow_same_rule_execute_once_take_%s_rules", tenant_id), statRuleMonitor.getFieldId()) ||
                  GrayManager.isAllowByRule("allow_same_rule_execute_once_take_db", topologyTable.getDatabase())) {
            log.info("processSameRuleSql rule task effect, {}, {}, {}, {}, {}", statRuleMonitor.getFieldId(), tenant_id, viewId, batchNum, hasCalcViewId);
            return newSqlList;
          }
          else {
            return sqlList;
          }
        }
        else{
          return sqlList;
        }
      }
      else {
        log.info("processSameRuleSql rule with same dims has not calculated, {}, {}, {}, {}", tenant_id, calculatedRuleKey, batchNum, viewId);
      }
    }
    catch (Exception ex){
      log.error("processSameRuleSql exception {}, {}, {}, {}", tenant_id, statViewMonitor.getViewId(), batchNum, statRuleMonitor.getFieldId(), ex);
    }
    return sqlList;
  }

  /**
   * 计算
   * 1、计算受影响的topology
   * 2、status=0 的即使不受影响也要计算。
   *
   * @param event
   */
  public void compute(DBUpdatedEvent event, StopWatch stopWatch) {
    stopWatch.start("compute");
    final long batchNum = event.getBatchNum();
    final Long dsBatchNum = event.getDsBatchNum();
    Map<String, Set<String>> changeGoalMap = selectChangeGoals(event);
    event.getTenantIdObjectDescribeApiNamesMap().forEach((tenantId, changedObjectDescribeApiNameSet) -> {
      String ea = TraceUtils.ei2EA(tenantId,eieaConverter);
      TraceUtils.createTrace(tenantId, ea, ObjectId.get().toString(), "1000");
      if (!GrayManager.isAllowByRule("use_ch_agg_pgdb", event.pgDbName()) && !GrayManager.isAllowByRule("use_ch_agg", tenantId)) {
        log.warn("this tenantId:{},not gray", tenantId);
        return;
      }
      if (!pgMetadataService.checkPgRouter(event.pgDbName(), event.getPgSchema(), tenantId)) {
        log.warn("this tenantId:{},pg route is invalidate", tenantId);
        return;
      }
      log.info("change info tenantId:{},batchNum{},dsBatchNum:{},changeApiNameSize:{}", tenantId, batchNum, dsBatchNum, changedObjectDescribeApiNameSet.size());
      List<TopologyTableMonitor> statViewMonitorList = topologyTableService.findViewMonitorByTenantId(tenantId, changedObjectDescribeApiNameSet, batchNum, dsBatchNum, changeGoalMap.getOrDefault(tenantId, Sets.newHashSet()));
      if (statViewMonitorList.isEmpty()) {
        log.warn("this tenantId:{},statViewMonitorList is empty, chDBUrl:{},batchNum:{},dsBatchNum:{}", tenantId, event.getChJdbcUrl(), event.getBatchNum(), dsBatchNum);
        return;
      }
      long beginTime = System.currentTimeMillis();
      //计算默认的并发基数
      double threadRate = tenantThreadRate.getOrDefault(tenantId, mybatisTenantPolicy.standalone(tenantId) ? standLoneThreadRate : publicThreadRate);
      List<List<TopologyTableMonitor>> monitPartitions = Lists.partition(statViewMonitorList, (int) Math.ceil(statViewMonitorList.size() / threadRate));
      //线程池挪到方法内
      final ExecutorService executor = Executors.newFixedThreadPool(monitPartitions.size(), factory);
      Map<String, Map<String, String>> ruleHasCalculated = Maps.newConcurrentMap();
      try {
        AtomicInteger calStatRules = new AtomicInteger(0);
        AtomicInteger calStatViews = new AtomicInteger(0);
        CompletableFuture.allOf(monitPartitions.stream().map(key -> CompletableFuture.runAsync(() -> {
          Thread.currentThread().setName(String.format("ch-cal-%s-%d", tenantId, key.hashCode()));
          TraceUtils.createTrace(tenantId, ea, ObjectId.get().toString(), "1000");
          key.forEach(statViewMonitor -> {
            String aggKey = String.format("dws:agg:%s:%s:%d", tenantId, statViewMonitor.getStatViewUniqueKey(), batchNum);
            try {
              long start = System.currentTimeMillis();
              if (statViewMonitor.getBatchNum() == batchNum && statViewMonitor.getStatus() == TopologyTableStatus.Calculating.getValue()) {
                log.info("this statView has calculated tenantId:{},viewId:{},status:{},batchNum:{},dsBatchNum:{}", tenantId, statViewMonitor.getViewId(), statViewMonitor.getStatus(), batchNum, dsBatchNum);
                return;
              }
              if (GrayManager.isAllowByRule("skip_stat_view_unique_key", String.format("%s^%s", tenantId, statViewMonitor.getStatViewUniqueKey()))) {
                log.info("this statView is ignored tenantId:{},viewId:{},status:{},batchNum:{},dsBatchNum:{}", tenantId, statViewMonitor.getViewId(), statViewMonitor.getStatus(), batchNum, dsBatchNum);
                return;
              }
              List<TopologyTableAggRuleMonitor> statRuleMonitorList = statViewMonitor.getStatRuleMonitorList();
              if (CollectionUtils.isNotEmpty(statRuleMonitorList)) {
                log.info("cal begin tenantId:{},viewId:{},batchNum:{},dsBatchNum:{}", tenantId, statViewMonitor.getViewId(), batchNum, dsBatchNum);
                calStatViews.addAndGet(1);
                TopologyTable topologyTable = statViewMonitor.getTopologyTable();
                // 这里查出来可能为空，计算之前这个图的topology_table可能没被删除，到这里计算时可能已经被删除了，需要判空 跳过计算
                if (Objects.isNull(topologyTable)) {
                  log.info("topology table is null, cannot calc statViewMonitorList skip! tenantId:{}, viewId:{}", tenantId, statViewMonitor.getViewId());
                  return;
                }
                String strDimFields = topologyTable.getStatFieldLocation().entrySet().stream().filter(entry -> !entry.getValue().startsWith("agg_")).map(Map.Entry::getKey).sorted().collect(Collectors.joining(","));
                for (TopologyTableAggRuleMonitor statRuleMonitor : statRuleMonitorList) {
                  long startCalAggRule = System.currentTimeMillis();
                  jedisCmd.hset(aggKey, statRuleMonitor.getFieldId(), "0");
                  jedisCmd.expire(aggKey, 3600L);
                  //如果是下游指标则用dsBatchNum,dsBatchNum为null则下游指标为全量计算
                  long num = Constants.AGG_DOWNSTREAM_DATA.equals(statRuleMonitor.getRootTableName()) ? (dsBatchNum == null ? 0 : dsBatchNum) : batchNum;
                  List<String> sqlList = statRuleMonitor.computeSQL(num);
                  //兼容明细主题的虚拟指标,key需要加上主题,因为不同主题下的虚拟指标的field_id都是一样的,但计算逻辑有区别
                  String calculatedRuleKey = String.format("%s|%s|%s", statRuleMonitor.getFieldId(), strDimFields, topologyTable.getApiName());
                  //只有增量计算的情况下才做指标合并
                  if(GrayManager.isAllowByRule("allow_same_rule_execute_once", statViewMonitor.getTenantId()) && statViewMonitor.getStatus() == TopologyTableStatus.Calculating.getValue()){
                    sqlList = this.processSameRuleSql(statViewMonitor, sqlList, num, topologyTable, calculatedRuleKey, statRuleMonitor, ruleHasCalculated);
                  }
                  this.retryExecuteStatRule(tenantId, statViewMonitor, changedObjectDescribeApiNameSet, sqlList, fixRetryTimes, statRuleMonitor,event);
                  calStatRules.addAndGet(1);
                  this.calRuleHasCalculated(statViewMonitor, ruleHasCalculated, sqlList, statRuleMonitor, calculatedRuleKey, topologyTable);
                  jedisCmd.hset(aggKey, statRuleMonitor.getFieldId(), "1");
                  //记录日志
                  BiAggLogDO biAggLogDO = BiAggLogDO.builder()
                                                    .tenantId(tenantId)
                                                    .viewId(statViewMonitor.getViewId())
                                                    .viewVersion(statViewMonitor.getVersion())
                                                    .batchNum(batchNum)
                                                    .cost(System.currentTimeMillis() - startCalAggRule)
                                                    .fieldId(statRuleMonitor.getFieldId())
                                                    .build();
                  biAggLogCollector.send(event.getChJdbcUrl(),biAggLogDO);
                }
                Long maxModifiedTime = dbUpdateEventDao.findMaxModifiedTimeN(tenantId, statViewMonitor.isIncludeDSRule(), event.getPgJdbcUrl(),event.getPgSchema(),event.getId(), event.getAggSyncId(), statViewMonitor.findAllEffectTables());
                //全量计算完成后,更新状态
                this.updateStatusOrVersionWithUnique(tenantId, statViewMonitor, batchNum, maxModifiedTime);
                long cost = System.currentTimeMillis() - start;
                log.info("cal finish tenantId:{},viewId:{},batchNum:{},calStatView:{},calStatRules:{},cost:{}", tenantId, statViewMonitor.getViewId(), batchNum, calStatViews.get(), calStatRules.get(), cost);
                BizAuditLog.builder()
                           .tenantId(tenantId)
                           .userId("1000")
                           .ea(ea)
                           .module(WarehouseConfig.DWS_AGG_EVENT)
                           .cost(cost)
                           .viewId(statViewMonitor.getViewId())
                           .num(batchNum)
                           .startTime(start)
                           .queryType(String.valueOf(statViewMonitor.getStatus()))
                           .build()
                           .log();
              }
            } catch (Exception e) {
              if (e instanceof RetryException) {
                int updateSize = topologyTableService.updateTopologyTableStatusByUniqueKey(tenantId, TopologyTableStatus.Prepared.getValue(), statViewMonitor.getStatViewUniqueKey(), batchNum, null, false, null, true);
                if (updateSize > 0 && GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
                  int uSize = topologyTableService.updateTopologyTableMergeStatusAndVersion(tenantId,
                    statViewMonitor.getStatViewUniqueKey(), MergeStatusEnum.PREPARE.getStatus(), batchNum, null);
                  log.info("updateTopologyTableMergeStatusAndVersion tenantId:{},uniqueKey:{},status:{},batchNum:{},uSize:{}", statViewMonitor.getTenantId(), statViewMonitor.getStatViewUniqueKey(), MergeStatusEnum.PREPARE.getStatus(), batchNum, uSize);
                }
                log.error("exec topologyTable fail after n times, tenantId:{},viewId:{},uniqueKey:{},version:{},batchNum:{},updateSize:{}", statViewMonitor.getTenantId(), statViewMonitor.getViewId(), statViewMonitor.getStatViewUniqueKey(), statViewMonitor.getVersion(), batchNum, updateSize, e);
              } else {
                log.error("exec topologyTable fail tenantId:{},viewId:{},version:{},batchNum:{}", statViewMonitor.getTenantId(), statViewMonitor.getViewId(), statViewMonitor.getVersion(), batchNum, e);
                throw new RuntimeException(e);
              }
            }finally {
              String[] fields = statViewMonitor.findAllStatFields();
              if (fields != null && fields.length > 0) {
                jedisCmd.hdel(aggKey, fields);
              }
            }
          });
          log.info("cal by part finish tenantId:{},batchNum{},taskName:{}", tenantId, batchNum, Thread.currentThread().getName());
        }, executor)).toArray(CompletableFuture[]::new)).join();
        log.info("all calculate task finish tenantId:{},batchNum:{},viewSize:{},ruleSize:{},pgDB:{},chDB:{},calc_cost:{}", tenantId, batchNum, calStatViews.get(), calStatRules.get(), event.pgDbName(), event.getChJdbcUrl(), System.currentTimeMillis() - beginTime);
      } finally {
        executor.shutdown();
        TraceUtils.removeContext();
      }
    });
    stopWatch.stop("compute");
  }

  private void calRuleHasCalculated(TopologyTableMonitor statViewMonitor,
                                    final Map<String, Map<String, String>> ruleHasCalculated,
                                    List<String> sqlList,
                                    TopologyTableAggRuleMonitor statRuleMonitor,
                                    String calculatedRuleKey,
                                    TopologyTable topologyTable) {
    if(GrayManager.isAllowByRule("allow_same_rule_execute_once", statViewMonitor.getTenantId()) && statViewMonitor.getStatus() == TopologyTableStatus.Calculating.getValue()){
      if(!ruleHasCalculated.containsKey(calculatedRuleKey)){
        //如果sql中没有包含rule_id和batch_num两列，那么不缓存；如果生成insertFields后才开灰度就会发生这种情况
        boolean bValidSql = true;
        for(String sql : sqlList){
          if(!sql.contains("rule_id") || !sql.contains("batch_num")){
            bValidSql = false;
            log.info("processSameRuleSql compute, sql not contains rule_id 、batch_num, {}, {}, {}, {}", statViewMonitor.getTenantId(), statViewMonitor.getViewId(), statRuleMonitor.getFieldId(),sql);
            break;
          }
        }
        if(bValidSql) {
          int viewVersion = 0;
          String viewId = statViewMonitor.getStatViewUniqueKey();
          if (GrayManager.isAllowByRule("stat_status_from_table_merge", statViewMonitor.getTenantId())) {
            viewVersion = statViewMonitor.getVersion();
          }
          Map<String, String> hasCalcMap = Maps.newConcurrentMap();
          hasCalcMap.put(Constants.CALC_SAME_RULE_VIEW_UNIQUE_ID, viewId);
          hasCalcMap.put(Constants.CALC_SAME_RULE_VIEW_VERSION, String.valueOf(viewVersion));
          hasCalcMap.put(Constants.CALC_SAME_RULE_VIEW_SOURCE_ID, topologyTable.getViewId());
          ruleHasCalculated.put(calculatedRuleKey, hasCalcMap);
        }
      }
    }
  }

  /**
   * 支持重试的函数
   *
   * @param tenantId
   * @param statViewMonitor
   * @param changedObjectDescribeApiNameSet
   * @param sqlList
   * @param retryTimes
   * @throws RetryException
   */
  private void retryExecuteStatRule(String tenantId,
                                    TopologyTableMonitor statViewMonitor,
                                    Set<String> changedObjectDescribeApiNameSet,
                                    List<String> sqlList,
                                    int retryTimes,
                                    TopologyTableAggRuleMonitor statRuleMonitor,DBUpdatedEvent event) throws RetryException {
    String settings = Constants.JOIN_USE_NULLS_1_CACHE_1;
    //如果是全量计算，join_algorithm设置为auto，防止大图触发memory limit circuit；如果是增量计算，可以灰度使用parallel_hash
    if (statViewMonitor.getStatus() == TopologyTableStatus.Prepared.getValue()) {
      if (GrayManager.isAllowByRule("grace_hash_join_when_create", tenantId)) {
        settings = String.format("%s, join_algorithm='grace_hash', grace_hash_join_initial_buckets = 4", settings);
      }
      //增加全量计算的并行度提升速度，这回导致CH资源占用增加，需要配合内存控制参数一起使用
      if (GrayManager.isAllowByRule("add_max_insert_thread", tenantId)) {
        settings = String.format("%s,%s", settings, String.format(Constants.MAX_INSERT_THREADS, maxInsertThreads));
      }
      if (GrayManager.isAllowByRule("add_min_insert_block_size_bytes", tenantId)) {
        settings = String.format("%s,%s", settings, String.format(Constants.MIN_INSERT_BLOCK_SIZE_BYTES, minInsertBlockSizeBytes));
      }
      if (GrayManager.isAllowByRule("add_min_insert_block_size_rows", tenantId)) {
        settings = String.format("%s,%s", settings, String.format(Constants.MIN_INSERT_BLOCK_SIZE_ROWS, minInsertBlockSizeRows));
      }
      if (
        preparedGroupBySettings.contains(String.format("%s^%s^%s", tenantId, statViewMonitor.getViewId(), statRuleMonitor.getFieldId())) ||
          GrayManager.isAllowByRule("limit_prepared_calculate_memory_usage", tenantId)) {
        settings += ",max_bytes_before_external_sort=" + maxBytesBeforeExternalSort.toString() +
          ",max_bytes_before_external_group_by=" + maxBytesBeforeExternalGroupBy.toString() +
          ",group_by_two_level_threshold_bytes=" + groupByTwoLevelThresholdBytes.toString();
      }
    } else {
      if (GrayManager.isAllowByRule("grace_hash_join_when_increment", tenantId)) {
        settings = String.format("%s, join_algorithm='grace_hash', grace_hash_join_initial_buckets = 4", settings);
      }
      //增加增量计算的并行度提升速度，这回导致CH资源占用增加，需要配合内存控制参数一起使用
      if (GrayManager.isAllowByRule("add_max_insert_thread_increment", tenantId)) {
        settings = String.format("%s,%s", settings, String.format(Constants.MAX_INSERT_THREADS, maxInsertThreads));
      }
      if (usedGroupBySettings.contains(String.format("%s^%s^%s", tenantId, statViewMonitor.getViewId(),
        statRuleMonitor.getFieldId())) || GrayManager.isAllowByRule("limit_increment_calculate_memory_usage", tenantId)) {
        settings += ",max_bytes_before_external_sort=" + maxBytesBeforeExternalSort.toString() +
          ",max_bytes_before_external_group_by=" + maxBytesBeforeExternalGroupBy.toString() +
          ",group_by_two_level_threshold_bytes=" + groupByTwoLevelThresholdBytes.toString();
      }
    }
    for (int rt = 0; rt < retryTimes; rt++) {
      try {
        if (statViewMonitor.getSource() == AggRuleType.MultiDimGoal.getRuleType() ||
          statViewMonitor.getSource() == AggRuleType.OldGoal.getRuleType()) {
          String finalSettings = settings;
          sqlList.stream()
                 .map(sql -> sql + finalSettings)
                 .forEach(sql -> clickHouseService.executeSQL(tenantId, sql, readTimeOut));
        } else {
          if(GrayManager.isAllowByRule("large_view_execute_multi_time", tenantId)
                  && statRuleMonitor.getMostRightLargeTableLimits() != null
                  && sqlList.get(0).contains(NodeTable.partialExecutePlaceHolderPrefix) ){
            String finalSqlSettings = settings;
            Iterator<Map.Entry<String, String>> iterator = statRuleMonitor.getMostRightLargeTableLimits().entrySet().iterator();
            if(iterator.hasNext()){
              Map.Entry<String, String> entry = iterator.next();
              String alias = entry.getKey();
              String limitStr = entry.getValue();
              List<String> limitOffsetList = Splitter.on("|").splitToList(limitStr);
              limitOffsetList.forEach(limitOffset -> {
                String sql = sqlList.getFirst().replace(String.format(NodeTable.partialExecutePlaceHolder, alias), limitOffset);
                if(GrayManager.isAllowByRule("print_agg_rule_sql", tenantId)){
                  log.info("retryExecuteStatRule rule status:{}, view_id:{},  rule_id:{}, sql:{}", statViewMonitor.getStatus(), statViewMonitor.getViewId(), statRuleMonitor.getFieldId(), sql + finalSqlSettings);
                }
                clickHouseService.executeSQL(tenantId, sql + finalSqlSettings, readTimeOut);
              });
            }
          }
          else{
            clickHouseService.executeSQL(tenantId, sqlList.get(0) + settings, readTimeOut);
          }
        }
        break;
      } catch (Exception e) {
        if (rt < retryTimes - 1) {
          log.warn("executeStatRule error begin to retry tenantId:{},viewId:{},fieldId:{},retryTimes:{},{}", tenantId, statViewMonitor.getViewId(), statRuleMonitor.getFieldId(), retryTimes, e.getMessage());
          Uninterruptibles.sleepUninterruptibly(Utils.calculateWaitTime(rt,10,25), TimeUnit.SECONDS);
        } else {
          log.error("retryExecuteStatRule tenantId:{},viewId:{},fieldId:{},error {}", tenantId, statViewMonitor.getViewId(), statRuleMonitor.getFieldId(), e.getMessage());
          throw new RetryException(e);
        }
      }
    }
  }

  /**
   * 更新topology table的状态或版本
   *
   * @param tenantId        租户id
   * @param statViewMonitor monitor 对象
   * @param batchNum        批次号
   */
  @Transactional
  public void updateStatusOrVersionWithUnique(String tenantId,
                                               TopologyTableMonitor statViewMonitor,
                                               long batchNum,
                                               Long maxModifiedTime) {
    if (statViewMonitor.getStatus() == TopologyTableStatus.Prepared.getValue()) {
      int result = topologyTableService.updateTopologyTableStatusByUniqueKey(statViewMonitor.getTenantId(), TopologyTableStatus.Calculating.getValue(), statViewMonitor.getStatViewUniqueKey(), batchNum, maxModifiedTime, statViewMonitor.isUpdateStatRule(), statViewMonitor.getStatRuleList(), false);
      log.info("updateStatusOrVersionWithUnique bi_mt_topology_table by uniqueKey tenantId:{},uniqueKey:{},status:1,batchNum:{}," + "size:{}", tenantId, statViewMonitor.getStatViewUniqueKey(), batchNum, result);
      if (result > 0) {
        if(GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)){
          int mergeSize = topologyTableService.updateTopologyTableMergeStatus(tenantId, statViewMonitor.getStatViewUniqueKey(), MergeStatusEnum.CAL.getStatus(), batchNum, maxModifiedTime);
          log.info("updateTopologyTableMergeStatus by uniqueKey tenantId:{},uniqueKey:{},batchNum:{},size:{}", tenantId, statViewMonitor.getStatViewUniqueKey(), batchNum, mergeSize);
        }
        List<String> sourceIds = topologyTableService.queryViewIdByUniqueKey(tenantId, statViewMonitor.getStatViewUniqueKey());
        if (CollectionUtils.isNotEmpty(sourceIds)) {
          int usedSize = topologyTableService.batchUpdateStatViewStatus(tenantId, sourceIds, StatViewStatusEnum.used.getStatus());
          log.info("updateStatusOrVersionWithUnique bi_mt_topology_status by uniqueKey tenantId:{},uniqueKey:{},batchNum:{},size:{}", tenantId, statViewMonitor.getStatViewUniqueKey(), batchNum, usedSize);
        }
      }
    } else {
      if(GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)){
        int mergeSize = topologyTableService.updateTopologyTableMergeByStatus(tenantId, statViewMonitor.getStatViewUniqueKey(), MergeStatusEnum.CAL.getStatus(), batchNum, maxModifiedTime);
        log.info("updateTopologyTableMergeByStatus by uniqueKey tenantId:{},uniqueKey:{},batchNum:{},size:{}", tenantId, statViewMonitor.getStatViewUniqueKey(), batchNum, mergeSize);
      }
      int result = topologyTableService.updateTopologyTableStatusByUniqueKey(statViewMonitor.getTenantId(), null, statViewMonitor.getStatViewUniqueKey(), batchNum, maxModifiedTime, statViewMonitor.isUpdateStatRule(), statViewMonitor.getStatRuleList(), false);
      log.info("updateTopologyTableStatusByUniqueKey bi_mt_topology_table by uniqueKey tenantId:{},uniqueKey:{},batchNum:{},status:null,size:{}", statViewMonitor.getTenantId(), statViewMonitor.getStatViewUniqueKey(),
        batchNum, result);
    }
  }

  /**
   * 计算前动作
   *
   * @param event 计算事件
   * @return
   */
  public boolean before(DBUpdatedEvent event, StopWatch stopWatch) {
    if (event.getBatchNum() < 0) {
      log.error("before pgDB:{},pgSchema:{},chDB:{},syncFlows:{},batchNums:{},batchNum<0", event.getPgJdbcUrl(), event.getPgSchema(), event.getChJdbcUrl(), event.getSyncFlows(), event.getBatchNums());
      return false;
    }
    if (GrayManager.isAllowByRule("use_ch_ods_partition", event.chDbName()) && Objects.equals(event.getAllowIncPartition(), WarehouseConfig.OPEN_INC_PARTITION)) {
      if (event.getAllowCalPartition() == WarehouseConfig.OPEN_CAL_PARTITION) {
        AggRequestContext aggRequestContext = AggRequestContext.getInstance(Map.of("DBUpdatedEvent",event,"StopWatch", stopWatch));
        AggCalResult aggCalResult = aggInc2CalHandler.prepare(event, stopWatch);
        aggRequestContext.put("AggCalResult",aggCalResult);
        event.setAggCalResult(aggCalResult);
        return aggInc2CalHandler.doHandler(aggRequestContext);
      }
      return this.copyBefore(event, this.prepare(event, stopWatch), stopWatch);
    }
    return dbUpdateEventDao.updateDBStatus(event.getId(), SyncStatusEnum.AGG_ING.getStatus(), event.getStatus());
  }

  /**
   * 计算前动作-goal_value_obj
   * 查出本批次考核维度变更的目标规则
   * @param event 计算事件
   * @return
   */
  public Map<String/*ei*/, Set<String>/*goal_rule_id*/> selectChangeGoals(DBUpdatedEvent event) {
    String chJdbcUrl = event.getChJdbcUrl();
    Map<String, Set<String>> changeGoalMap = Maps.newHashMap();
    Map<String, Set<String>> goalApiNameEisMap = Maps.newHashMap();
    event.getTenantIdObjectDescribeApiNamesMap().forEach((ei, apiNames) -> {
      Constants.GOAL_VALUE_OBJ_SET.forEach(goalApiName -> {
        if (apiNames.contains(goalApiName)) {
          goalApiNameEisMap.computeIfAbsent(goalApiName, key -> Sets.newHashSet()).add(ei);
        }
      });
    });
    if (goalApiNameEisMap.isEmpty()) {
      return changeGoalMap;
    }
    String database = chJdbcUrl.substring(chJdbcUrl.lastIndexOf("/") + 1);
    String preWhere = "";
    if(GrayManager.isAllowByRule("use_ch_ods_partition", database)){
      preWhere= "PREWHERE bi_sys_ods_part in('s','c')";
    }
    String selectSql = "select distinct tenant_id, " +
      "if(goal_rule_detail_id like '%s', goal_rule_id, concat(goal_rule_id,'|',goal_rule_detail_id)) as rule_id " +
      "from %s.%s %s " +
      "where tenant_id in('%s') and bi_sys_flag=1 and bi_sys_batch_id=" + event.getBatchNum();
    List<String> goalSQls = Lists.newArrayList();
    for (Map.Entry<String, Set<String>> entry : goalApiNameEisMap.entrySet()) {
      goalSQls.add(String.format(selectSql, "nogoaldetail%", database, entry.getKey(), preWhere, Joiner.on("','")
                                                                                                       .join(entry.getValue())));
    }
    String selectRuleSQL = Joiner.on(" UNION DISTINCT ").join(goalSQls) + " SETTINGS final=1 ";
    try {
      List<Pair<String, String>> result = clickHouseService.executeQuerySQLWithJdbc(chJdbcUrl, selectRuleSQL,60000L, rs -> {
        try {
          String tenantId = rs.getString("tenant_id");
          String ruleId = rs.getString("rule_id");
          return Pair.of(tenantId, ruleId);
        } catch (SQLException e) {
          throw new RuntimeException(e);
        }
      });
      result.forEach(pair -> changeGoalMap.computeIfAbsent(pair.first, key -> Sets.newHashSet()).add(pair.second));
    } catch (Exception e) {
      log.error("selectChangeGoals exception:{} ", chJdbcUrl, e);
    }
    return changeGoalMap;
  }

  /**
   * 执行插入before数据
   * @param event 事件
   * @param aggCalResult
   * @return
   */
  public boolean copyBefore(DBUpdatedEvent event, AggCalResult aggCalResult, StopWatch stopWatch) {
    stopWatch.start("copyBefore");
    try {
      Integer result = dbAggInfoDAO.updateDBAggInfoStatus(event.getId(), SyncStatusEnum.COPY_BEFORE_ING.getStatus(), event.getAggInfoVersion());
      if (result != null) {
        event.setAggInfoVersion(result);
        copyBeforeIng.add(event.getId());
        if (aggCalResult != null) {
          this.doInsertSQLs(event, aggCalResult.getCalPrepares(), true);
          log.info("doInsertSQLs before success,pgDB:{},chDB:{},pgSchema:{},batchNums:{}", event.getPgJdbcUrl(), event.getChJdbcUrl(), event.getPgSchema(), JSON.toJSONString(event.getBatchNums()));
        }
        copyBeforeIng.remove(event.getId());
        return this.copyAfter(event, aggCalResult,stopWatch);
      }
      log.warn("copyBefore updateDBAggInfoStatus fail pgDB:{},chDB:{},pgSchema:{},batchNums:{}", event.getPgJdbcUrl(), event.getChJdbcUrl(), event.getPgSchema(), JSON.toJSONString(event.getBatchNums()));
    } finally {
      copyBeforeIng.remove(event.getId());
      stopWatch.stop("copyBefore");
    }
    return false;
  }

  /**
   * 执行插入after数据
   * @param event 事件
   * @param aggCalResult
   * @return
   */
  public boolean copyAfter(DBUpdatedEvent event, AggCalResult aggCalResult, StopWatch stopWatch) {
    stopWatch.start("copyAfter");
    try {
      Integer result = dbAggInfoDAO.updateDBAggInfoStatus(event.getId(), SyncStatusEnum.COPY_AFTER_ING.getStatus(), event.getAggInfoVersion());
      if (result != null) {
        event.setAggInfoVersion(result);
        copyAfterIng.add(event.getId());
        if (aggCalResult != null) {
          this.doInsertSQLs(event, aggCalResult.getCalPrepares(), false);
          log.info("doInsertSQLs after success,pgDB:{},chDB:{},pgSchema:{},batchNums:{}", event.getPgJdbcUrl(), event.getChJdbcUrl(), event.getPgSchema(), JSON.toJSONString(event.getBatchNums()));
        }
        copyAfterIng.remove(event.getId());
        result = dbAggInfoDAO.updateDBAggInfoStatus(event.getId(), SyncStatusEnum.AGG_ING.getStatus(), event.getAggInfoVersion());
        if (result != null) {
          calculatePgDB.add(event.getId());
          event.setAggInfoVersion(result);
          return true;
        }
      }
    }finally {
      copyAfterIng.remove(event.getId());
      stopWatch.stop("copyAfter");
    }
    log.warn("copyAfter updateDBAggInfoStatus fail pgDB:{},chDB:{},pgSchema:{},batchNums:{}", event.getPgJdbcUrl(), event.getChJdbcUrl(), event.getPgSchema(), JSON.toJSONString(event.getBatchNums()));
    return false;
  }

  /**
   * 初始化before和after sql
   * @param event
   * @return
   */
  public AggCalResult prepare(DBUpdatedEvent event, StopWatch stopWatch) {
    AggCalResult aggCalResult = new AggCalResult();
    List<AggCalResult.CalPrepare> calPrepares = Lists.newArrayList();
    aggCalResult.setCalPrepares(calPrepares);
    Set<String> tableNames = event.changeTables();
    if (CollectionUtils.isEmpty(tableNames)) {
      log.warn("insertBefore return tableNames is empty chDB:{},pgDB:{},pgSchema:{}", event.getChJdbcUrl(), event.getPgJdbcUrl(), event.getPgSchema());
      return aggCalResult;
    }
    if (event.getBatchNums() == null || event.getBatchNums().length == 0) {
      log.warn("insertBefore batchNums is empty chDB:{},pgDB:{},pgSchema:{}", event.getChJdbcUrl(), event.getPgJdbcUrl(), event.getPgSchema());
      return aggCalResult;
    }
    stopWatch.start("prepare");
    try {
      Long[] batchNums = event.getBatchNums();
      Arrays.sort(batchNums);
      Map<String, List<CHColumn>> tableCHColumnsMap = Maps.newHashMap();
      if (event.getPgSchema().startsWith("sch_")) {
        List<List<String>> tablesPartitions = Lists.partition(Lists.newArrayList(tableNames), 10);
        tablesPartitions.forEach(tables -> {
          Map<String, List<CHColumn>> tableCHColumnMap1 = clickHouseService.batchLoadColumn(event.getChJdbcUrl(), CommonUtils.getDBName(event.getChJdbcUrl()), tables);
          if (!tableCHColumnMap1.isEmpty()) {
            tableCHColumnsMap.putAll(tableCHColumnMap1);
          }
        });
      }
      tableNames.forEach(table -> {
        List<CHColumn> columns = tableCHColumnsMap.getOrDefault(table, clickHouseService.loadTableCHColumn(event.getChJdbcUrl(), event.getPgSchema(), table));
        if (CollectionUtils.isEmpty(columns)) {
          log.warn("ch table columns is empty chDB:{},tableName:{}", event.getChJdbcUrl(), table);
          return;
        }
        List<String> columnNames = columns.stream().map(CHColumn::getName).toList();
        if (!columnNames.contains(WarehouseConfig.ODS_PARTITION_KEY)) {
          log.info("no deed before data chDB:{},table:{}", event.getChJdbcUrl(), table);
          AggCalResult.CalPrepare calPrepare = new AggCalResult.CalPrepare();
          calPrepare.setTableName(table);
          calPrepare.setInsertSQL(Pair.of("", ""));
          calPrepares.add(calPrepare);
          return;
        }
        List<String> sortedKeys = clickHouseService.queryCHTablePkColumns(event.getChJdbcUrl(), table);
        CHTable chTable = CHTable.builder().sortedKeys(sortedKeys).tableName(table).columnNames(columnNames).build();
        AggCalResult.CalPrepare calPrepare = this.createInsertSqL(event, chTable);
        calPrepares.add(calPrepare);
      });
      return aggCalResult;
    } finally {
      stopWatch.stop("prepare");
    }
  }

  public String createSQL(String insertSQL,
                          String chDB,
                          String tableName,
                          String inEiSQL,
                          String inBatchIds,
                          String sortedKey,
                          String eiName,
                          List<String> columns,
                          Long[] batchNums,
                          boolean before,
                          String fromPartition,
                          String toPartition) {
    if (before) {
      if (WarehouseConfig.skipBeforeTableSet.contains(tableName)) {
        log.info("this table no need before data {}", tableName);
        return "";
      }
      String withSQL = String.format("WITH inc AS (SELECT %s FROM %s.%s PREWHERE bi_sys_ods_part='%s' WHERE %s IN(%s) AND bi_sys_batch_id in(%s))", sortedKey, chDB, tableName, fromPartition, eiName, inEiSQL, inBatchIds);
      String selectBeforeColumn = this.createSelectColumn(columns, batchNums, true, toPartition);
      String selectBeforeSQL = String.format(
        "SELECT %s FROM  %s.%s PREWHERE %s.bi_sys_ods_part='s' WHERE (%s) in inc AND " +
          "%s.bi_sys_flag=1 AND %s.bi_sys_batch_id < %d ", selectBeforeColumn, chDB, tableName, tableName, sortedKey, tableName, tableName, batchNums[
          batchNums.length - 1]);
      return String.format("%s\n%s\n%s\n%s", insertSQL, withSQL, selectBeforeSQL, WarehouseConfig.finalsetting);
    }
    String selectAfterColumn = this.createSelectColumn(columns, batchNums, false, toPartition);
    String selectAfterSQL = String.format("SELECT %s FROM %s.%s PREWHERE %s.bi_sys_ods_part='%s' WHERE %s.%s IN(%s) AND %s.bi_sys_batch_id in(%s) ", selectAfterColumn, chDB, tableName, tableName, fromPartition, tableName, eiName, inEiSQL, tableName, inBatchIds);
    return String.format("%s\n%s\n%s", insertSQL, selectAfterSQL, WarehouseConfig.finalsetting);
  }
  /**
   * 生成插入before的sql
   * @param event
   * @param chTable
   * @return
   */
  public AggCalResult.CalPrepare createInsertSqL(DBUpdatedEvent event, CHTable chTable) {
    AggCalResult.CalPrepare calPrepare = new AggCalResult.CalPrepare();
    Set<String> lastSyncEis = event.findModifiedTenantIds();
    if (CollectionUtils.isEmpty(lastSyncEis)) {
      throw new RuntimeException(String.format("createInsertBeforeSqL lastSyncEis is empty chDB:%s,pgDB:%s",event.getChJdbcUrl(), event.getPgJdbcUrl()));
    }
    Long[] sortedBatchNums = event.sortedBatchNums();
    String chDB = CommonUtils.getDBName(event.getChJdbcUrl());
    String tableName = chTable.getTableName();
    String inEiSQL =JoinHelper.joinSkipNullOrBlank(",", "'", lastSyncEis);
    String inBatchIds = Lists.newArrayList(sortedBatchNums).stream().map(String::valueOf).collect(Collectors.joining(","));
    String sortedKey = chTable.getSortedKeys().stream().map(key->String.format("%s.%s",tableName,key.trim())).collect(Collectors.joining(","));
    String eiName = "tenant_id";
    if (WarehouseConfig.eiTables.contains(tableName)) {
      eiName = "ei";
    }
    calPrepare.setTableName(tableName);
    String insertColumn = String.join(",", chTable.getColumnNames());
    String insertSQL = String.format("INSERT INTO %s.%s (%s) ", chDB, chTable.getTableName(), insertColumn);
    if (event.getAllowCalPartition() == WarehouseConfig.OPEN_CAL_PARTITION) {
      String inc2CalAfter = this.createSQL(insertSQL, chDB, tableName, inEiSQL, inBatchIds, sortedKey, eiName, chTable.getColumnNames(), sortedBatchNums, false, "i", "c");
      log.info("inc2CalAfter chDb:{}, Sql:{}, sortedBatchNums:{}", chDB, inc2CalAfter, sortedBatchNums);
      String cal2StockBefore = this.createSQL(insertSQL, chDB, tableName, inEiSQL, inBatchIds, sortedKey, eiName, chTable.getColumnNames(), sortedBatchNums, true, "c", "s");
      String cal2StockAfter = this.createSQL(insertSQL, chDB, tableName, inEiSQL, inBatchIds, sortedKey, eiName, chTable.getColumnNames(), sortedBatchNums, false, "c", "s");
      calPrepare.setInc2CalAfter(inc2CalAfter);
      calPrepare.setCal2StockBefore(cal2StockBefore);
      calPrepare.setCal2StockAfter(cal2StockAfter);
    }else{
      String selectBeforeColumn = this.createSelectColumn(chTable.getColumnNames(), sortedBatchNums, true,"s");
      String selectAfterColumn = this.createSelectColumn(chTable.getColumnNames(), sortedBatchNums, false,"s");
      String withSQL = String.format("WITH inc AS (SELECT %s FROM %s.%s PREWHERE bi_sys_ods_part='i' WHERE %s IN(%s) " +
        "AND bi_sys_batch_id in(%s))", sortedKey, chDB, chTable.getTableName(), eiName, inEiSQL, inBatchIds);
      String selectBeforeSQL = String.format("SELECT %s FROM  %s.%s PREWHERE %s.bi_sys_ods_part='s' WHERE (%s) in inc AND " +
        "%s.bi_sys_flag=1 AND %s.bi_sys_batch_id < %d ", selectBeforeColumn, chDB, tableName,tableName, sortedKey,tableName,tableName,sortedBatchNums[sortedBatchNums.length-1]);
      String selectAfterSQL = String.format("SELECT %s FROM %s.%s PREWHERE %s.bi_sys_ods_part='i' WHERE %s.%s IN(%s) AND " +
        "%s.bi_sys_batch_id in(%s) ", selectAfterColumn,chDB, tableName,tableName, tableName,eiName,inEiSQL,tableName,inBatchIds);
      String before = String.format("%s\n%s\n%s\n%s", insertSQL, withSQL, selectBeforeSQL, WarehouseConfig.finalsetting);
      if (WarehouseConfig.skipBeforeTableSet.contains(tableName)) {
        before = "";
      }
      String after = String.format("%s\n%s\n%s", insertSQL, selectAfterSQL, WarehouseConfig.finalsetting);
      calPrepare.setInsertSQL(Pair.of(before, after));
    }
    return calPrepare;
  }
  /**
   *
   * @param event
   * @param calPrepares
   * @param before
   */
  public void doInsertSQLs(DBUpdatedEvent event, List<AggCalResult.CalPrepare> calPrepares, boolean before) {
    if (CollectionUtils.isEmpty(calPrepares)) {
      log.warn("calPrepares is empty dhDB:{},pgDB:{},batchNum:{}", event.getChJdbcUrl(), event.getPgJdbcUrl(), event.getBatchNum());
      return;
    }
    List<List<AggCalResult.CalPrepare>> calPrepareParts = Lists.partition(calPrepares, (int) Math.ceil(calPrepares.size() / WarehouseConfig.insertThreadRate));
    final ExecutorService executor = Executors.newFixedThreadPool(calPrepareParts.size(), factory);
    try {
      CompletableFuture.allOf(calPrepareParts.stream().map(key -> CompletableFuture.runAsync(() -> {
        key.forEach(calPrepare -> {
          String sql = before ? calPrepare.getInsertSQL().first : calPrepare.getInsertSQL().second;
          if (StringUtils.isBlank(sql)) {
            log.warn("execute insert sql is empty table:{},dhDB:{},pgDB:{},batchNum:{}", calPrepare.getTableName(), event.getChJdbcUrl(), event.getPgJdbcUrl(),event.getBatchNum());
            return;
          }
          if (!before) {
            String jedisK = String.format("ods:%s:%s:all", event.getId(), calPrepare.getTableName());
            try (JedisLock jedisLock = new JedisLock(jedisCmd, jedisK, 30 * 60 * 1000)) {
              boolean lock = jedisLock.tryLock();
              if (lock) {
                this.retryRunInsertSQL(event, sql, calPrepare);
              }else{
                log.warn("execute insert sql tryLock fail table:{},dhDB:{},pgDB:{},batchNum:{}", calPrepare.getTableName(), event.getChJdbcUrl(), event.getPgJdbcUrl(),event.getBatchNum());
              }
            } catch (Exception e) {
              throw new RuntimeException(String.format("doInsertSQLs tryLock error chDB:%s,pgDB:%s,table:%s,before:%s,batchNum:%d", event.getChJdbcUrl(), event.getPgJdbcUrl(), calPrepare.getTableName(), false,event.getBatchNum()), e);
            }
          }else{
            this.retryRunInsertSQL(event, sql, calPrepare);
          }
        });
        log.info("doInsertSQLs finish this part chDB:{},pgDB:{},before:{},batchNum:{}", event.getChJdbcUrl(), event.getPgJdbcUrl(), before, event.getBatchNum());
      }, executor)).toArray(CompletableFuture[]::new)).join();
    } finally {
      executor.shutdown();
    }
  }

  private void retryRunInsertSQL(DBUpdatedEvent event, String sql, AggCalResult.CalPrepare calPrepare) {
    for (int rt = 0; rt < WarehouseConfig.partitionCopyRetryTimes; rt++) {
      try {
        clickHouseService.executeSQLWithJdbcUrl(event.getChJdbcUrl(), sql, 1200000);
        break;
      } catch (Exception e) {
        if (rt < WarehouseConfig.partitionCopyRetryTimes - 1) {
          log.warn("execute insert sql error dhDB:{},pgDB:{},table:{},execute:{} times", event.getChJdbcUrl(), event.getPgJdbcUrl(),calPrepare.getTableName(), rt);
          Uninterruptibles.sleepUninterruptibly(Utils.calculateWaitTime(rt, 10, 25), TimeUnit.SECONDS);
        } else {
          CHContext.sqlErrDelCache(e, jedisCmd, event.getChJdbcUrl(), calPrepare.getTableName());
          throw new RuntimeException(String.format("execute insert sql error after 3 times dhDB:%s,pgDB:%s,table:%s", event.getChJdbcUrl(), event.getPgJdbcUrl(), calPrepare.getTableName()), e);
        }
      }
    }
  }

  /**
   * 生成 select 列
   * @param columns
   * @param batchNums
   * @param before
   * @return
   */
  private String createSelectColumn(List<String> columns, Long[] batchNums, boolean before, String partitionName) {
    return columns.stream().map(column -> {
      if (!column.startsWith("bi_sys_")) {
        return column;
      }
      return switch (column) {
        case "bi_sys_flag" -> String.format("%s AS bi_sys_flag", before ? "0" : "1");
        case "bi_sys_batch_id" -> String.format("%d AS bi_sys_batch_id", batchNums[batchNums.length - 1]);
        case "bi_sys_ods_part" -> String.format("'%s' AS bi_sys_ods_part", partitionName);
        case "bi_sys_is_deleted" -> "0 AS bi_sys_is_deleted";
        case "bi_sys_version" -> before ? "now() AS bi_sys_version" : column;
        default -> column;
      };
    }).collect(Collectors.joining(","));
  }
  /**
   * 执行完计算后,清理CH数据库before数据
   * 有些表不包含before数据因此不用执行删除
   *
   * @param event
   */
  public void after(DBUpdatedEvent event, StopWatch stopWatch) {
    stopWatch.start("after" + event.getChJdbcUrl());
    if (GrayManager.isAllowByRule("use_ch_ods_partition", event.chDbName()) && Objects.equals(event.getAllowIncPartition(), WarehouseConfig.OPEN_INC_PARTITION)) {
      if (event.getAllowCalPartition() == WarehouseConfig.OPEN_CAL_PARTITION) {
        AggRequestContext aggRequestContext = AggRequestContext.getInstance();
        aggRequestContext.put("DBUpdatedEvent", event);
        aggRequestContext.put("StopWatch", stopWatch);
        if(event.getAggCalResult() != null){
          aggRequestContext.put("AggCalResult",event.getAggCalResult());
        }
        boolean result = aggCal2StockAfterHandler.doHandler(aggRequestContext);
        if (!result) {
          log.error("aggCal2StockAfterHandler eventId:{}, false ", event.getId());
          stopWatch.stop("after" + event.getChJdbcUrl());
          return;
        }
      }
      Integer newVersion = dbAggInfoDAO.updateDBAggInfoStatus(event.getId(), SyncStatusEnum.AGG_ED.getStatus(), event.getAggInfoVersion());
      if (newVersion != null) {
        event.setAggInfoVersion(newVersion);
        if (StringUtils.isNotBlank(event.getSyncFlows())) {
         int result = dbSyncInfoFlowDao.deleteDbSyncInfoFlow(event.getPgJdbcUrl(), event.getPgSchema(), Lists.newArrayList(event.getId()), Splitter.on(",").splitToList(event.getSyncFlows()),event.getBatchNum());
         log.info("deleteDbSyncInfoFlow jdbcURL:{},pgSchema:{},size:{}",event.getPgJdbcUrl(),event.getPgSchema(),result);
        }
      }
    } else {
      event.getTableTenantIdSetMap().forEach((table, tenantIdSet) -> {
        if (CollectionHelper.isNotEmpty(tenantIdSet) && !haveNoBeforeDataTables.contains(table)) {
          String eiName = "tenant_id";
          if (eiTables.contains(table)) {
            eiName = "ei";
          }
          if (Constants.AGG_DOWNSTREAM_DATA.equals(table) || table.endsWith(Constants.DOWNSTREAM_SUFFIX)) {
            this.deleteBeforeData(event.getChJdbcUrl(), table, event.getDsBatchNum(), eiName, tenantIdSet);
          } else {
            this.deleteBeforeData(event.getChJdbcUrl(), table, event.getBatchNum(), eiName, tenantIdSet);
          }
        }
      });
      dbUpdateEventDao.updateDBStatus(event.getId(), SyncStatusEnum.AGG_ED.getStatus(), SyncStatusEnum.AGG_ING.getStatus());
    }
    stopWatch.stop("after" + event.getChJdbcUrl());
  }

  /**
   * 根据batchid删除数据，防止输出插入重复
   */
  public void deleteBeforeData(String chDBUrl, String tableName, long batchNum, String eiName, Set<String> tenantIds) {
    List<String> columns = clickHouseService.queryCHTablePkColumns(chDBUrl, tableName);
    if (CollectionUtils.isEmpty(columns)) {
      log.warn("query ch table columns empty dbURL:{},tableName:{}", chDBUrl, tableName);
      return;
    }
    List<String> dynamicColumns = Lists.newArrayList(columns);
    if (WarehouseConfig.mustInsertSysModifiedTime.contains(tableName)) {
      dynamicColumns.add("sys_modified_time");
    }
    //跳过部分租户删除before数据
    String eiInFilterStr = tenantIds.stream()
                                    .filter(ei -> !GrayManager.isAllowByRule("skip_del_before", ei))
                                    .map(ei -> "'" + ei + "'")
                                    .collect(Collectors.joining(","));
    if (StringUtils.isBlank(eiInFilterStr)) {
      log.warn("eiInFilterStr dbURL:{},tableName:{}", chDBUrl, tableName);
      return;
    }
    String chDB = Utils.getDBName(chDBUrl);
    String insertSQLSB;
    if (GrayManager.isAllowByRule("use_ch_ods_partition", chDB)) {
      if (!GrayManager.isAllowByRule("delete_before_ch_db", chDB)) {
        log.info("do not delete before data on this ch db:{},tableName:{}", chDBUrl, tableName);
        return;
      }
      if (WarehouseConfig.skipBeforeTableSet.contains(tableName)) {
        log.info("do not delete before data on this ch db:{},tableName:{}", chDBUrl, tableName);
        return;
      }
      String insertColumns =
        String.join(",", dynamicColumns) + ", bi_sys_is_deleted, bi_sys_version,bi_sys_batch_id,bi_sys_ods_part";
      String selectField = String.join(",", dynamicColumns) + ", toUInt8(1) AS bi_sys_is_deleted,now() AS bi_sys_version," +
        String.format("toInt64(%d) AS bi_sys_batch_id", batchNum) + ",'s' AS bi_sys_ods_part";
      insertSQLSB = String.format(" INSERT INTO %s (%s) " + "SELECT %s FROM %s" +
        " PREWHERE %s.bi_sys_ods_part='s' WHERE %s IN (%s) AND bi_sys_flag = '0'  AND %s.bi_sys_batch_id = %d %s ",
        tableName, insertColumns, selectField, tableName, tableName, eiName, eiInFilterStr, tableName, batchNum,
        Constants.JOIN_USE_NULLS_1);
    } else {
      String insertColumns = String.join(",", dynamicColumns) + ", bi_sys_is_deleted, bi_sys_version,bi_sys_batch_id";
      String selectField = String.join(",", dynamicColumns) + ", toUInt8(1) AS bi_sys_is_deleted,now() AS bi_sys_version," +
        String.format("toInt64(%d) AS bi_sys_batch_id", batchNum);
      insertSQLSB = String.format(" INSERT INTO %s (%s) SELECT %s FROM %s WHERE %s IN (%s) AND bi_sys_flag = '0'" +
        " AND %s.bi_sys_batch_id = %d SETTINGS final = 1 ", tableName, insertColumns, selectField, tableName, eiName,
        eiInFilterStr, tableName, batchNum);
    }
    try {
      clickHouseService.executeSQLWithJdbcUrl(chDBUrl, insertSQLSB, readTimeOut);
    } catch (Exception e) {
      if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("UNKNOWN_TABLE")) {
        clickHouseService.invalidatePkColumnsCache(chDBUrl, tableName);
        log.error("deleteBeforeData sql error chDBurl:{},tableName:{},batchNum:{}", chDBUrl, tableName, batchNum, e);
      } else {
        throw e;
      }
    }
  }
}
