package com.fxiaoke.bi.warehouse.ods.compare.arg;

import com.fxiaoke.common.Pair;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComparePgToChDelBizData {
    private String pgSchema;
    private String pgDB;
    private String chDB;
    private Map<String, Map<String,Pair<String,Integer>>> result;
}
