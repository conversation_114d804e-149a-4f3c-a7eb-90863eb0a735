package com.fxiaoke.bi.warehouse.ods.compare.util;

import com.fxiaoke.bi.warehouse.ods.compare.arg.ColumnInfo;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class CompareUtil {

    public static final Set<String> SYNC_TABLE_WHITES_SET = Sets.newHashSet("dt_auth_simple", "dt_auth_out_simple", "feed_relation", "dim_sys_area_gray", "dim_sys_date", "mt_tag", "mt_sub_tag", "mt_data_tag", "bi_mt_topology_table", "wechat_group_user");

    public static final Set<String> SYNC_TABLE_BLACK_SET = Sets.newHashSet("agg_goal_achieved", "ta", "object_t1679993801354__c", "object_t1679993925635__c", "object_t1679994548041__c", "object_t1679994895896__c", "object_t1679995349999__c", "testlj", "dim_data_new", "stage_runtime_new_opportunity", "biz_account_main_data", "stage_runtime_task_new_opportunity", "agg_effect_field", "object_t1709607224659__c", "object_t1711070097340__c", "object_t1711070109055__c", "object_t17103271843305101734422874__c_65f185ae541c200001022437_", "object_t17102408421028687467164788__c_6");

    /**
     * 比较两个数据是否在一定阈值内是否相等
     */
    public static boolean isApproximatelyEqual(long pgCount, long chCount, int threshold) {
        if (pgCount == 0) {
            return true;
        }
        long abs = Math.abs(chCount - pgCount);
        return (abs / (double) pgCount) * 100 < threshold;
    }

    /**
     * pg比ch多字段
     */
    public static List<String> pgGreaterThanCh(List<ColumnInfo> pgColumnInfoList, List<ColumnInfo> chColumnInfoList) {
        Set<String> pgColumnSet = pgColumnInfoList.stream().map(ColumnInfo::getName).collect(Collectors.toSet());
        Set<String> chColumnSet = chColumnInfoList.stream().map(ColumnInfo::getName).collect(Collectors.toSet());
        pgColumnSet.removeAll(chColumnSet);
        return pgColumnSet.stream().toList();
    }

    /**
     * ch比pg多多字段
     */
    public static List<String> chGreaterThanPg(List<ColumnInfo> pgColumnInfoList, List<ColumnInfo> chColumnInfoList) {
        Set<String> pgColumnSet = pgColumnInfoList.stream().map(ColumnInfo::getName).collect(Collectors.toSet());
        Set<String> chColumnSet = chColumnInfoList.stream().map(ColumnInfo::getName).collect(Collectors.toSet());
        chColumnSet.removeAll(pgColumnSet);
        Set<String> specialChFieldSet = Sets.newHashSet(CHContext.BI_SYS_FLAG, CHContext.BI_SYS_VERSION, CHContext.BI_SYS_BATCH_ID, CHContext.BI_SYS_IS_DELETED, CHContext.BI_SYS_ODS_PART);
        return chColumnSet.stream().filter(x -> !specialChFieldSet.contains(x)).toList();
    }
}
