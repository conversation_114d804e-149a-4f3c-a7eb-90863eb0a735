package com.fxiaoke.bi.warehouse.dws.utils.sql;

import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.db.er.NodeColumn;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.dws.model.ActionDateConfig;
import com.fxiaoke.bi.warehouse.dws.model.DimConfig;
import com.fxiaoke.bi.warehouse.dws.model.StatViewFilter;
import com.fxiaoke.bi.warehouse.dws.transform.model.FilterType;
import com.fxiaoke.bi.warehouse.dws.transform.model.PGFilterType;
import com.fxiaoke.bi.warehouse.dws.transform.model.WhereRule;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @Author: zhaomh
 * @Description:
 * @Date: Created in 2024/8/13
 * @Modified By:
 */
public class SQLUtils {

    public static List<String> createSubquery(List<Pair<DimConfig, StatViewFilter>> dimFieldFilterList,NodeTable nodeTable,String tableAlias,String timeZone) {
        if (CollectionUtils.isEmpty(dimFieldFilterList)) {
            return null;
        }
        List<String> whereRules = Lists.newArrayList();
        for (Pair<DimConfig, StatViewFilter> pair : dimFieldFilterList) {
            DimConfig dimConfig = pair.first;
            StatViewFilter statViewFilter = pair.second;
            NodeColumn nodeColumn = nodeTable.getColumnList().stream()
                                             .filter(column -> column.getName().equals(dimConfig.getColumnName())).findFirst().orElse(null);
            if (nodeColumn == null) {
                continue;
            }
            PGColumnType pgColumnType = switch (nodeColumn.getType()) {
                case _String -> PGColumnType.String;
                case _int -> PGColumnType.Int4;
                default ->
                  throw new RuntimeException("unsupported column type");
            };
            String whereSQL;
            WhereRule whereRule = WhereRule.builder()
                                           .filterType(FilterType.parseFromId(Integer.parseInt(statViewFilter.getOperator())))
                                           .pgFilterType(PGFilterType.parseFromId(Integer.parseInt(statViewFilter.getOperator())))
                                           .column(dimConfig.getColumnName())
                                           .value1(statViewFilter.getValue1())
                                           .value2(statViewFilter.getValue2())
                                           .fieldType(dimConfig.getFieldType())
                                           .columnType(pgColumnType)
                                           .isSingle(nodeColumn.getIsSingle() == 1)
                                           .build();
            whereSQL = whereRule.whereSQL(nodeTable,nodeTable.getName(),tableAlias,timeZone);
            if(CollectionUtils.isNotEmpty(statViewFilter.getRatioDateValue1List()) || CollectionUtils.isNotEmpty(statViewFilter.getRatioDateValue2List())){
                List<String> ratioDateValue1 = Lists.newArrayListWithCapacity(statViewFilter.getRatioDateValue1List().size());
                for(int i=0;i<statViewFilter.getRatioDateValue1List().size();i++){
                    WhereRule orWhereRule = WhereRule.builder()
                                                     .filterType(FilterType.parseFromId(Integer.parseInt(statViewFilter.getOperator())))
                                                     .pgFilterType(PGFilterType.parseFromId(Integer.parseInt(statViewFilter.getOperator())))
                                                     .column(dimConfig.getColumnName())
                                                     .value1(statViewFilter.getRatioDateValue1List().get(i))
                                                     .value2(statViewFilter.getRatioDateValue2List().get(i))
                                                     .fieldType(dimConfig.getFieldType())
                                                     .columnType(pgColumnType)
                                                     .isSingle(nodeColumn.getIsSingle() == 1)
                                                     .build();
                    ratioDateValue1.add(orWhereRule.whereSQL(nodeTable, nodeTable.getName(), tableAlias, timeZone));
                }
                ratioDateValue1.add(whereSQL);
                whereSQL = " ( " +ratioDateValue1.stream().map(orWhereRule -> " ( " + orWhereRule + ") ").collect(Collectors.joining(" OR "))+" ) ";
            }
            whereRules.add(whereSQL);
        }
        return whereRules;
    }

    public static List<String> createActionDateSubquery(NodeTable nodeTable,
                                                  List<Pair<ActionDateConfig, StatViewFilter>> actionDateFilters,
                                                  String tableAlias,
                                                  String timeZone) {
        if (CollectionUtils.isEmpty(actionDateFilters)) {
            return null;
        }
        List<String> whereRules = Lists.newArrayList();
        for (Pair<ActionDateConfig, StatViewFilter> actionDateFilter : actionDateFilters) {
            ActionDateConfig actionDateConfig = actionDateFilter.first;
            StatViewFilter statViewFilter = actionDateFilter.second;
            NodeColumn nodeColumn = nodeTable.getColumnList().stream()
                                             .filter(column -> column.getName().equals(actionDateConfig.getColumnName())).findFirst().orElse(null);
            if (nodeColumn == null) {
                return null;
            }
            PGColumnType pgColumnType = switch (nodeColumn.getType()) {
                case _String -> PGColumnType.String;
                case _int -> PGColumnType.Int4;
                default ->
                  throw new RuntimeException("unsupported column type");
            };
            WhereRule actionWhere= WhereRule.builder()
                                            .filterType(FilterType.parseFromId(Integer.parseInt(statViewFilter.getOperator())))
                                            .pgFilterType(PGFilterType.parseFromId(Integer.parseInt(statViewFilter.getOperator())))
                                            .column(actionDateConfig.getColumnName())
                                            .value1(statViewFilter.getValue1())
                                            .value2(statViewFilter.getValue2())
                                            .fieldType(nodeColumn.getFieldType())
                                            .columnType(pgColumnType)
                                            .build();
            String actionDateSQL = actionWhere.whereSQL(nodeTable,nodeTable.getName(),tableAlias,timeZone);
            if(CollectionUtils.isNotEmpty(statViewFilter.getRatioDateValue1List()) || CollectionUtils.isNotEmpty(statViewFilter.getRatioDateValue2List())){
                List<String> ratioDateValue1 = Lists.newArrayListWithCapacity(statViewFilter.getRatioDateValue1List().size());
                for(int i=0;i<statViewFilter.getRatioDateValue1List().size();i++){
                    WhereRule orWhereRule = WhereRule.builder()
                                                     .filterType(FilterType.parseFromId(Integer.parseInt(statViewFilter.getOperator())))
                                                     .pgFilterType(PGFilterType.parseFromId(Integer.parseInt(statViewFilter.getOperator())))
                                                     .column(actionDateConfig.getColumnName())
                                                     .value1(statViewFilter.getRatioDateValue1List().get(i))
                                                     .value2(statViewFilter.getRatioDateValue2List().get(i))
                                                     .fieldType(nodeColumn.getFieldType())
                                                     .columnType(pgColumnType)
                                                     .isSingle(nodeColumn.getIsSingle() == 1)
                                                     .build();
                    ratioDateValue1.add(orWhereRule.whereSQL(nodeTable, nodeTable.getName(), tableAlias, timeZone));
                }
                ratioDateValue1.add(actionDateSQL);
                actionDateSQL = " ( " +ratioDateValue1.stream().map(orWhereRule -> " ( " + orWhereRule + ") ").collect(Collectors.joining(" OR "))+" ) ";
            }
            whereRules.add(actionDateSQL);
        }
        return whereRules;
    }

    public static String buildDimEnterpriseSubQuery(String tenantId, List<String> whereRules, String database) {
        if (CollectionUtils.isEmpty(whereRules)) {
            return null;
        }
        StringBuilder subSql = new StringBuilder(String.format("ei in (select dei.id FROM %s.dim_enterprise_info as dei final where dei.tenant_id = '%s' ", database, tenantId));
        whereRules.forEach(whereRule -> subSql.append(" AND ").append(whereRule));
        subSql.append(" ) ");
        return subSql.toString();
    }

    /**
     * todo 字符串的大小匹配是按照字典顺序对比的，不太靠谱。
     * @param tenantId
     * @param actionTable
     * @param actionDateConfig
     * @param database
     * @return
     */
    public static String createActionDateRangeSQL(String tenantId,
                                                  NodeTable actionTable,
                                                  ActionDateConfig actionDateConfig,
                                                  String database) {
        List<NodeColumn> columnList = actionTable.getColumnList();
        Map<String, NodeColumn> columnMap = columnList.stream()
                                                      .collect(Collectors.toMap(NodeColumn::getName, nodeColumn -> nodeColumn));
        String eiColumnName = "tenant_id";
        if (WarehouseConfig.eiTables.contains(actionTable.getName())) {
            eiColumnName = "ei";
        }
        String columnName = actionDateConfig.getColumnName();
        NodeColumn nodeColumn = columnMap.get(columnName);
        if (nodeColumn == null) {
            return null;
        }
        String filter = switch (nodeColumn.getType()) {
            case _String -> columnName+" > '0' ";
            case _int -> columnName+" > 0 ";
            default ->
              throw new RuntimeException("unsupported column type");
        };
      return String.format("select min(%s) AS minDate,max(%s) AS maxDate FROM %s.%s where %s = '%s' AND %s is not null AND %s", columnName, columnName, database, actionTable.getName(), eiColumnName, tenantId, columnName, filter);
    }

    /**
     * @param tenantId
     * @param statViewUniqueKey
     * @param database
     * @return
     */
    public static String createDeleteAggDataSQL(String tenantId, String statViewUniqueKey, String database) {
        return String.format("delete FROM %s.agg_data where tenant_id = '%s' AND view_id='%s'", database, tenantId, statViewUniqueKey);
    }

    public static void pushDownActionDateFilterTemplate(NodeTable actionTable) {
        actionTable.appendSubWheres(" ${actionDateFilter} ");
    }

    public static String createPushDownActionDateFilterSQL(NodeTable actionTable,
                                                           ActionDateConfig actionDateConfig,
                                                           BIPair<String/*start dateTime ms*/, String/*end dateTime ms*/> dateRange) {
        List<NodeColumn> columnList = actionTable.getColumnList();
        Map<String, NodeColumn> columnMap = columnList.stream()
                                                      .collect(Collectors.toMap(NodeColumn::getName, nodeColumn -> nodeColumn));
        String columnName = actionDateConfig.getColumnName();
        NodeColumn nodeColumn = columnMap.get(columnName);
        if (nodeColumn == null) {
            return "";
        }
        String filter = switch (nodeColumn.getType()) {
            case _String -> {
                if (dateRange != null) {
                    yield columnName + " >= '" + dateRange.first + "' AND " + columnName + " < '" + dateRange.second +
                      "' ";
                } else {
                    yield columnName + " = '' ";
                }
            }
            case _int -> {
                if (dateRange != null) {
                    yield columnName + " >= " + dateRange.first + " AND " + columnName + " < " + dateRange.second + " ";
                } else {
                    yield columnName + " is null ";
                }
            }
            default -> throw new RuntimeException("unsupported column type");
        };
        return " AND " + filter;
    }
}
