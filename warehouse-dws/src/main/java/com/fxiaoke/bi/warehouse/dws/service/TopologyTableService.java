package com.fxiaoke.bi.warehouse.dws.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.db.entity.StatViewStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.er.AggRuleType;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.util.*;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyStatusDao;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyTableDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableMergeDO;
import com.fxiaoke.bi.warehouse.dws.exception.VersionConflictException;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.StatViewDao;
import com.fxiaoke.bi.warehouse.dws.transform.model.FilterType;
import com.fxiaoke.bi.warehouse.dws.transform.model.GoalChangeType;
import com.fxiaoke.bi.warehouse.dws.transform.model.MergeStatusEnum;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.helper.CollectionHelper;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.CharMatcher;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/9
 */
@Slf4j
@Service
public class TopologyTableService {
  @Resource
  public TopologyTableDao topologyTableDao;
  @Resource
  public TopologyStatusDao topologyStatusDao;
  @Resource
  private StatViewDao statViewDao;
  @Resource
  private ClickHouseService clickHouseService;
  @Resource
  private PgDataSource pgDataSource;

  private long largeViewSplit2BatchRows;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", iConfig -> {
      largeViewSplit2BatchRows = iConfig.getInt("large_view_execute_split_to_batch_rows", 10000000);
    });
  }

  public int resetTopologyByEi(String tenantId, int sourceType,int status,String[] sourceIds) {
    return topologyTableDao.resetTopologyByEi(tenantId, sourceType,status,sourceIds);
  }

  public TopologyTableDO queryTopologyBySourceId(String tenantId, String sourceId) {
    return topologyTableDao.queryTopologyBySourceId(tenantId, sourceId);
  }

  public List<Map<String,Object>> queryRepairGoalViewTopologyByEI(String tenantId) {
    return topologyTableDao.queryRepairGoalViewTopologyByEI(tenantId);
  }

  public List<String> queryGoalViewIdTopologyByEI(String tenantId) {
    return topologyTableDao.queryGoalViewIdTopologyByEI(tenantId);
  }

  public List<String> queryGoalRuleIdTopologyByEI(String tenantId) {
    return topologyTableDao.queryGoalRuleIdTopologyByEI(tenantId);
  }

  /**
   * 批量删除统计图topology table信息
   *
   * @param tenantId  租户id
   * @param sourceIds 图列表
   */
  @Transactional
  public void deleteTopologyTables(String tenantId, List<String> sourceIds, Integer sourceType) {
    try {
      if (CollectionUtils.isEmpty(sourceIds)) {
        Integer st = sourceType == -1 ? null : sourceType;
        int result1 = topologyTableDao.batchDeleteTopologyTableBySourceId(tenantId, null, st);
        int result2 = topologyStatusDao.batchDisableStatViewStatus(tenantId, null, st);
        int result3 = 0;
        if (GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
          result3 = topologyTableDao.batchDeleteTopologyTableMergeBySource(tenantId, st);
        }
        log.info("tenantId:{},sourceType:{},deleteTopologyTables:{} and deletedStatViewStatus:{},batchDeleteTopologyTableMerge:{},viewIds:all", tenantId, sourceType, result1, result2, result3);
      } else {
        sourceIds.forEach(sourceId -> {
          boolean result = this.deleteTopologyTableBySourceId(tenantId, sourceId);
          int result2 = 0;
          if (result) {
            result2 = topologyStatusDao.batchDisableStatViewStatus(tenantId, new String[] {sourceId}, null);
          }
          log.info("tenantId:{},deleteTopologyTableBySourceId:{} and batchDisableStatViewStatus:{},viewId:{}", tenantId, result, result2, sourceId);
        });
      }
    } catch (Exception e) {
      log.error("deleteTopologyTables is error, tenantId:{}, sourceType:{}", tenantId, sourceType, e);
    }
  }

  /**
   * 删除 topology table 需要检测是否删除topology table merge数据
   * 注意：线程不安全
   * @param tenantId 租户id
   * @param sourceId 图id
   */
  @Transactional
  public boolean deleteTopologyTableBySourceId(String tenantId, String sourceId) {
    Map<String, Object> keyAndVersion = topologyTableDao.queryUniqueKeyAndVersion(tenantId, sourceId);
    if (MapUtils.isNotEmpty(keyAndVersion)) {
      JSONObject kv = new JSONObject(keyAndVersion);
      Integer version = kv.getInteger("version");
      String statViewUniqueKey = kv.getString("stat_view_unique_key");
      int result = topologyTableDao.deleteTopologyTableBySourceIdWithVersion(tenantId, sourceId, version);
      log.info("deleteTopologyTableBySourceId tenantId:{},sourceId:{},result:{}", tenantId, sourceId, result);
      if (result > 0) {
        this.deleteTopologyTableMergeOrNot(tenantId, statViewUniqueKey);
        return true;
      }
      return false;
    }
    return true;
  }

  /**
   * 判断是否要删除merge 记录
   * @param tenantId 租户id
   * @param statViewUniqueKey unique key
   */
  private void deleteTopologyTableMergeOrNot(String tenantId, String statViewUniqueKey) {
    if (StringUtils.isNotBlank(statViewUniqueKey) &&
      GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
      int otherTopology = topologyTableDao.queryTopologyByUniqueKey(tenantId, statViewUniqueKey);
      if (otherTopology == 0) {
        int r = topologyTableDao.batchDeleteTopologyTableMerge(tenantId, new String[] {statViewUniqueKey});
        log.info("batchDeleteTopologyTableMerge tenantId:{},id:{},result:{}", tenantId, statViewUniqueKey, r);
      }
    }
  }

  private void getTableCount(NodeTable nodeTable, TopologyTable statView, Map<String, Long> result){
    String tableName = statView.getDatabase() + "." + nodeTable.getName();
    boolean isAllCal = TopologyTableStatus.Prepared.getValue() == statView.getStatus();
    String whereFilter = nodeTable.buildWhereFilter(statView.getTenantId(), true, isAllCal, false);
    if (statView.getSource() == AggRuleType.OldGoal.getRuleType() || statView.getSource() == AggRuleType.MultiDimGoal.getRuleType()) {
      whereFilter = TemplateUtil.replace(whereFilter, Map.of( "filterFirstDimSQL", "", "RootSubWhere", ""));
    }
    String sql = String.format("select count() from %s %s", tableName, whereFilter);
    List<Long> countList = clickHouseService.executeQuerySQL(statView.getTenantId(), sql, res -> {
      try {
        return res.getLong(1);
      } catch (SQLException e) {
        log.error("getMostRightLargeTable executeQuerySQL error, tenantId:{}, sql:{}, {}", statView.getTenantId(), sql, e);
      }
      return 0L;
    });
    if(CollectionUtils.isNotEmpty(countList))
      result.put(nodeTable.getAlias(), countList.get(0));
    else
      result.put(nodeTable.getAlias(), 0L);

    if (CollectionHelper.isNotEmpty(nodeTable.getJoinSet())) {
      nodeTable.getJoinSet().forEach(join -> {
        if(join.getTable() != null){
          getTableCount(join.getTable(), statView, result);
        }
      });
    }
  }

  public void getTableKey(NodeTable nodeTable, TopologyTable topologyTable, Map<String, List<String>> result){
    if(result.containsKey(nodeTable.getName()) || !StringUtils.equals(nodeTable.getTableType(), "table"))
      return;

    String orderKeySql = String.format("select sorting_key from system.tables where name = '%s' and database = '%s' limit 1", SQLUtil.escapeSql(nodeTable.getName()), topologyTable.getDatabase());
    List<String> sortKey = clickHouseService.executeQuerySQL(topologyTable.getTenantId(), orderKeySql, res -> {
      try {
        return res.getString(1);
      } catch (SQLException e) {
        log.error("getTableKey executeQuerySQL error, tenantId:{}, orderKeySql:{}, {}", topologyTable.getTenantId(), orderKeySql, e);
      }
      return "";
    });

    if(CollectionUtils.isNotEmpty(sortKey)) {
      result.put(nodeTable.getName(), Splitter.on(",").splitToList(sortKey.get(0)));
    }

    if (CollectionHelper.isNotEmpty(nodeTable.getJoinSet())) {
      nodeTable.getJoinSet().forEach(join -> {
        if (join.getTable() != null) {
          getTableKey(join.getTable(), topologyTable, result);
        }
      });
    }
  }

  public Map<String, List<String>> getTableKeys(TopologyTable topologyTable){
    Map<String, List<String>> result = Maps.newHashMap();
    try {
      for (TopologyTableAggRule topologyTableAggRule : topologyTable.getStatRuleList()) {
        getTableKey(topologyTableAggRule.getRootNodeTable(), topologyTable, result);
      }
    }
    catch (Exception ex){
      log.error("getTableKeys exception, tenantId:{}", topologyTable.getTenantId(), ex);
    }
    return result;
  }

  /**
   * 递归获取指标各个table的count值
   * @param nodeTable
   * @param statView
   * @return
   */
  public Map<String, String> getMostRightLargeTable(NodeTable nodeTable, TopologyTable statView, Integer thresholdSize, boolean calcLimitOffset){
    Map<String, String> result = Maps.newHashMap();
    long size = thresholdSize > 0 ? thresholdSize :largeViewSplit2BatchRows;
    try {
      Map<String, Long> aliasCountMap = Maps.newLinkedHashMap();
      getTableCount(nodeTable, statView, aliasCountMap);
      AtomicReference<String> tableAlias = new AtomicReference<>("");
      aliasCountMap.keySet().forEach(alias -> {
        if (aliasCountMap.get(alias) > size) {
          tableAlias.set(alias);
        }
      });
      if (calcLimitOffset) {
        if(StringUtils.isNotEmpty(tableAlias.get())) {
          List<String> limitOffset = Lists.newArrayList();
          long times = Math.floorDiv(aliasCountMap.get(tableAlias.get()), size);
          for (int i = 0; i < times; i++) {
            limitOffset.add(String.format(" limit %d offset %d ", size, size * i));
          }
          //即使count被largeViewSplit2BatchRows整除，为了防止count误差导致的丢数据，也需要补最后一次offset
          limitOffset.add(String.format(" offset %d ", size * times));
          result.put(tableAlias.get(), Joiner.on("|").join(limitOffset));
        }
      }
      else {
        if(StringUtils.isNotEmpty(tableAlias.get())) {
          result.put(tableAlias.get(), thresholdSize.toString());
        }
      }
    }
    catch (Exception ex){
      log.error("getMostRightLargeTable error: {},{},{}", statView.getTenantId(), statView.getViewId(),ex);
    }
    return result;
  }


  /**
   * 禁用topology table 需要检测是否删除topology table merge数据
   * @param tenantId 租户id
   * @param sourceId 图id
   */
  @Transactional
  public boolean disableTopologyTableBySourceId(String tenantId, String sourceId) {
    Map<String, Object> keyAndVersion = topologyTableDao.queryUniqueKeyAndVersion(tenantId, sourceId);
    if (MapUtils.isNotEmpty(keyAndVersion)) {
      JSONObject kv = new JSONObject(keyAndVersion);
      Integer version = kv.getInteger("version");
      String statViewUniqueKey = kv.getString("stat_view_unique_key");
      int result = topologyTableDao.updateTopologyTableStatusWithVersion(tenantId, sourceId, TopologyTableStatus.UnUsed.getValue(), version);
      log.info("disableTopologyTableBySourceId tenantId:{},sourceId:{},result:{}", tenantId, sourceId, result);
      if (result > 0) {
        this.deleteTopologyTableMergeOrNot(tenantId, statViewUniqueKey);
        return true;
      }
      return false;
    }
    return true;
  }

  /**
   * 禁用topology table 需要检测是否删除topology table merge数据
   * @param tenantId 租户id
   * @param topologyTableDO 图id
   */
  @Transactional
  public boolean disableTopologyTableBySourceId(String tenantId, TopologyTableDO topologyTableDO) {
    if (topologyTableDO != null) {
      int result = topologyTableDao.updateTopologyTableStatusWithVersion(tenantId, topologyTableDO.getSourceId(),
        TopologyTableStatus.UnUsed.getValue(), topologyTableDO.getVersion());
      log.info("disableTopologyTableBySourceId tenantId:{},sourceId:{},result:{}", tenantId,
        topologyTableDO.getSourceId(), result);
      if (result > 0) {
        this.deleteTopologyTableMergeOrNot(tenantId, topologyTableDO.getStatViewUniqueKey());
        return true;
      }
      return false;
    }
    return true;
  }

  /**
   * 修改topology table状态改为初始化中并更新版本
   * @param tenantId 租户id
   * @param statViewUniqueKey 图合并key
   */
  @Transactional
  public void changeTopologyTable2PreparedByKey(String tenantId, String statViewUniqueKey) {
    Preconditions.checkArgument(StringUtils.isNotBlank(tenantId), "tenantId should not blank!");
    Preconditions.checkArgument(StringUtils.isNotBlank(statViewUniqueKey), "statViewUniqueKey should not blank!");
    List<String> sourceIds = topologyTableDao.queryViewIdByUniqueKeyAndStatus(tenantId, statViewUniqueKey, new int[] {TopologyTableStatus.NONeedCal.getValue()});
    if (CollectionUtils.isNotEmpty(sourceIds)) {
      int updateSize = this.updateTopologyTableStatusByUniqueKey(tenantId, TopologyTableStatus.Prepared.getValue(), statViewUniqueKey, 0L, null, false, null, true);
      log.info("changeTopologyTable2PreparedByKey tenantId:{},uniqueKey:{},status:{},batchNum:{},uSize:{}", tenantId, statViewUniqueKey, TopologyTableStatus.Prepared.getValue(), 0, updateSize);
      if (updateSize > 0) {
        if (GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
          //主要是针对status=2的统计图，如果merge表不存在则插入version默认是1，防止和agg_data中的version重复。
          int uSize = this.initTopologyTableMergeOrIncVersion(TopologyTableMergeDO.of(tenantId, statViewUniqueKey, 1, MergeStatusEnum.PREPARE.getStatus(), 0L, 0L));
          log.info("updateTopologyTableMergeStatusAndVersion tenantId:{},uniqueKey:{},status:{},batchNum:{},uSize:{}", tenantId, statViewUniqueKey, MergeStatusEnum.PREPARE.getStatus(), 0L, uSize);
        }
        int result = this.batchUpdateStatViewStatus(tenantId, sourceIds, StatViewStatusEnum.init.getStatus());
        log.info("batchUpdateStatViewStatus tenantId:{},viewId:{},updateSize:{},size:{}", tenantId, JSON.toJSONString(sourceIds), updateSize, result);
      }
    }
  }

  /**
   * 修改topology table状态改为实时查询
   * @param tenantId 租户id
   * @param statViewUniqueKey 图合并key
   */
  @Transactional
  public void changeTopologyTable2NoNeedCalByKey(String tenantId, String statViewUniqueKey) {
    Preconditions.checkArgument(StringUtils.isNotBlank(tenantId), "tenantId should not blank!");
    Preconditions.checkArgument(StringUtils.isNotBlank(statViewUniqueKey), "statViewUniqueKey should not blank!");
    List<String> sourceIds = topologyTableDao.queryViewIdByUniqueKeyAndStatus(tenantId, statViewUniqueKey, new int[] {TopologyTableStatus.Calculating.getValue(), TopologyTableStatus.Prepared.getValue()});
    if (CollectionUtils.isNotEmpty(sourceIds)) {
      int updateSize = this.updateTopologyTableStatusByUniqueKey(tenantId, TopologyTableStatus.NONeedCal.getValue(), statViewUniqueKey, 0L, null, false, null, true);
      log.info("changeTopologyTable2NoNeedCalByKey tenantId:{},uniqueKey:{},status:{},batchNum:{},uSize:{}", tenantId, statViewUniqueKey, TopologyTableStatus.NONeedCal.getValue(), 0, updateSize);
      if (updateSize > 0) {
        if (GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
          int uSize = this.updateTopologyTableMergeStatusAndVersion(tenantId, statViewUniqueKey, MergeStatusEnum.NONeedCal.getStatus(), 0L, null);
          log.info("updateTopologyTableMergeStatusAndVersion tenantId:{},uniqueKey:{},status:{},batchNum:{},uSize:{}", tenantId, statViewUniqueKey, MergeStatusEnum.NONeedCal.getStatus(), 0L, uSize);
        }
        int result = this.batchUpdateStatViewStatus(tenantId, sourceIds, StatViewStatusEnum.used.getStatus());
        log.info("batchUpdateStatViewStatus tenantId:{},viewIds:{},status:{},updateSize:{},size:{}", tenantId, JSON.toJSONString(sourceIds), StatViewStatusEnum.used.getStatus(), updateSize, result);
      }
    }
  }

  /**
   * 更新状态
   * @param tenantId
   * @param statViewUniqueKey
   * @param batchNum
   * @param maxModifiedTime
   */
  @Transactional
  public void changeTopologyTable2CalByKey(String tenantId,
                                           String statViewUniqueKey,
                                           long batchNum,
                                           Long maxModifiedTime) {
    Preconditions.checkArgument(StringUtils.isNotBlank(tenantId), "tenantId should not blank!");
    Preconditions.checkArgument(StringUtils.isNotBlank(statViewUniqueKey), "statViewUniqueKey should not blank!");
    int result = this.updateTopologyTableStatusByUniqueKey(tenantId, TopologyTableStatus.Calculating.getValue(), statViewUniqueKey, batchNum, maxModifiedTime, false, null, false);
    log.info(
      "updateStatusOrVersionWithUnique bi_mt_topology_table by uniqueKey tenantId:{},uniqueKey:{},status:1,batchNum:{}," +
        "size:{}", tenantId, statViewUniqueKey, batchNum, result);
    if (result > 0) {
      if (GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
        int mergeSize = this.updateTopologyTableMergeStatus(tenantId, statViewUniqueKey, MergeStatusEnum.CAL.getStatus(), batchNum, maxModifiedTime);
        log.info("updateTopologyTableMergeStatus by uniqueKey tenantId:{},uniqueKey:{},batchNum:{},size:{}", tenantId, statViewUniqueKey, batchNum, mergeSize);
      }
      List<String> sourceIds = this.queryViewIdByUniqueKey(tenantId, statViewUniqueKey);
      if (CollectionUtils.isNotEmpty(sourceIds)) {
        int usedSize = this.batchUpdateStatViewStatus(tenantId, sourceIds, StatViewStatusEnum.used.getStatus());
        log.info("updateStatusOrVersionWithUnique bi_mt_topology_status by uniqueKey tenantId:{},uniqueKey:{},batchNum:{},size:{}", tenantId, statViewUniqueKey, batchNum, usedSize);
      }
    }
  }

  /**
   * 更新统计图状态 并升级版本号
   * @param tenantId 租户id
   * @param sourceIds 统计图id
   * @param status 状态
   * @return
   */
  public int updateTopologyTableStatusBySourceId(String tenantId, List<String> sourceIds, int status) {
    String[] sourceIdArray = sourceIds.toArray(new String[0]);
    int result=topologyTableDao.updateTopologyTableStatusBySourceId(tenantId, sourceIdArray, status);
    log.info("updateStatusTopologyTableBySourceId finish tenantId:{},sourceIds:{},status:{},result:{}", tenantId, sourceIds, status,result);
    return result;
  }

  public void updateTopologyTableDetailSqlBySourceId(String tenantId, String sourceId, String pgDetailSql) {
    topologyTableDao.updateTopologyTableDetailSqlBySourceId(tenantId, sourceId, pgDetailSql);
  }

  public List<Map<String, Object>> batchQueryFieldLocations(String tenantId, List<String> sourceIds) {
    return topologyTableDao.batchQueryFieldLocations(tenantId, sourceIds);
  }

  public List<TopologyTableDO> queryTopologyTableByApiName(String tenantId, int[] status, String themeApiName, int sourceType) {
    return topologyTableDao.queryTopologyTableByApiName(tenantId, status, themeApiName, sourceType);
  }

  public List<TopologyTableDO> findTopologyTablesByFieldId(String tenantId, int[] status, String[] fieldIds) {
    return topologyTableDao.findTopologyTablesByFieldId(tenantId, status, fieldIds);
  }

  public List<TopologyTableDO> queryTopologyTableFromAllAgg(String tenantId, int[] status, String[] fieldIds) {
    return topologyTableDao.queryTopologyTableFromAllAgg(tenantId, status, fieldIds);
  }
  /**
   * 保存topologyTable
   * 注意：没有在此直接检测统计图是否启用的原因是有可能有并发状态更新的问题
   * 如：设置成1后，如果统计图增量计算失败会由1改成0，因此这个时候新增的图会导致
   * 可能会有漏算的情况
   *
   * @param tenantId 租户id
   * @param viewId   图表id
   * @param status   状态
   */
  @Transactional(propagation= Propagation.REQUIRED)
  public int saveTopologyTable(String tenantId,
                               String viewId,
                               int status,
                               int isDelete,
                               int createdBy,
                               long createTime,
                               String timeZone,
                               TopologyTable topologyTable,
                               long start,
                               int sampleSize,
                               int resultLimit,
                               String pgDetailSql) throws VersionConflictException {
    if (topologyTable == null) {
      log.error("this view is not validate to ch,delete this view:{}:{}", tenantId, viewId);
      return 0;
    }
    TopologyTableDO topologyTableDO = topologyTableDao.queryTopologyBySourceIdWithDel(tenantId, viewId);
    if (CollectionUtils.isEmpty(topologyTable.getStatRuleList())) {
      int updateSize = this.batchUpdateStatViewStatus(tenantId, Lists.newArrayList(viewId),
        StatViewStatusEnum.used.getStatus());
      log.info("batchUpdateTopologyStatusBySourceId finish tenantId:{},viewId:{},size:{}", tenantId, viewId,
        updateSize);
      return 0;
    }
    //灰度的企业用md5非灰度的企业用view_id
    String statViewUniqKey = this.createStatViewUniqueKey(tenantId, viewId, topologyTable);
    if (topologyTableDO != null && topologyTableDO.getIsDeleted() == 0 &&
      Objects.equals(statViewUniqKey, topologyTableDO.getStatViewUniqueKey()) &&
      CommonUtils.equalsAny(topologyTableDO.getStatus(),TopologyTableStatus.Calculating.getValue(),TopologyTableStatus.Prepared.getValue(),TopologyTableStatus.NONeedCal.getValue())) {
      if (CommonUtils.equalsAny(topologyTableDO.getStatus(),TopologyTableStatus.Calculating.getValue(),TopologyTableStatus.NONeedCal.getValue())) {
        int uSize = 0;
        if (GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
          uSize = topologyTableDao.upsertTopologyTableMerge(TopologyTableMergeDO.of(tenantId, statViewUniqKey, 0, MergeStatusEnum.from(topologyTableDO.getStatus())
                                                                                                                                 .getStatus(), topologyTableDO.getBatchNum(), topologyTableDO.getLatestAggTime()));
        }
        int updateSize = this.batchUpdateStatViewStatus(tenantId, Lists.newArrayList(viewId), StatViewStatusEnum.used.getStatus());
        log.info("this statView has created topology_table exact once tenantId:{},viewId:{},updateSize:{}," +
          "upsertTopologyTableMerge size:{}", tenantId, viewId, updateSize, uSize);
      }
      log.info("this statView has created topology_table exact once tenantId:{},viewId:{}", tenantId, viewId);
      return 0;
    }
    //判断指标各个表的数据量，统计出每个指标最靠右的大表
    Map<String,Map<String,String>> rightLargeTables = Maps.newHashMap();
    if(GrayManager.isAllowByRule("sample_limit_to_right_large_table", tenantId)){
      for (TopologyTableAggRule topologyTableAggRule : topologyTable.getStatRuleList()) {
        Map<String, String> mostRightLargeTable = this.getMostRightLargeTable(topologyTableAggRule.getRootNodeTable(), topologyTable, sampleSize,false);
        rightLargeTables.put(topologyTableAggRule.getFieldId(), mostRightLargeTable);
      }
    }
    String viewSQL = topologyTable.toViewSQL(sampleSize, resultLimit, rightLargeTables, null);
    if (StringUtils.isNotBlank(topologyTable.getDatabaseId())) {
      //如果是外部数据源直接改为不做预计算
      status = TopologyTableStatus.NONeedCal.getValue();
    }
    TopologyTableDO newTopologyTableDO = TopologyTableDO.from(tenantId,topologyTable,isDelete,createdBy,viewSQL,pgDetailSql,status,timeZone,statViewUniqKey,topologyTable.getDatabaseId());
    if (AggRuleType.Agg.getRuleType() == topologyTable.getSource()) {
      Map<String, Object> usedStatMap = topologyTableDao.queryUsedByUniqueKeyExcludedSelf(tenantId, statViewUniqKey, topologyTable.getViewId());
      if (MapUtils.isNotEmpty(usedStatMap) && usedStatMap.get("status") != null) {
        newTopologyTableDO.setStatus(TopologyTableStatus.from((int) usedStatMap.get("status")).getValue());
      }
    }
    int result;
    if (topologyTableDO == null) {
      newTopologyTableDO.setId(ObjectId.get().toString());
      result = topologyTableDao.saveTopologyTable(tenantId, newTopologyTableDO);
    } else {
      int expectedVersion = topologyTableDO.getVersion();
      newTopologyTableDO.setId(topologyTableDO.getId());
      newTopologyTableDO.setVersion(expectedVersion + 1);
      result = topologyTableDao.saveTopologyWithVersion(tenantId, newTopologyTableDO, expectedVersion);
    }
    if (result > 0) {
      int statViewStatus = StatViewStatusEnum.init.getStatus();
      int mergeStatus = MergeStatusEnum.PREPARE.getStatus();
      //如果是已开启计算
      if (newTopologyTableDO.getStatus() == TopologyTableStatus.Calculating.getValue()) {
        statViewStatus = StatViewStatusEnum.used.getStatus();
        mergeStatus = MergeStatusEnum.CAL.getStatus();
      }
      //如果不用预计算
      if (newTopologyTableDO.getStatus() == TopologyTableStatus.NONeedCal.getValue()) {
        statViewStatus = StatViewStatusEnum.used.getStatus();
        mergeStatus = MergeStatusEnum.NONeedCal.getStatus();
      }
      int uSize = 0;
      if (GrayManager.isAllowByRule("stat_status_from_table_merge", tenantId)) {
        uSize = topologyTableDao.upsertTopologyTableMerge(TopologyTableMergeDO.of(tenantId, statViewUniqKey, 0, mergeStatus, 0L, 0L));
      }
      int updateSize = this.batchUpdateStatViewStatus(tenantId, Lists.newArrayList(viewId), statViewStatus);
      log.info("batchUpdateTopologyStatusBySourceId finish tenantId:{},viewId:{},uniqueKey:{},size:{},cost:{},upsertTopologyTableMerge size:{},topologyStatus:{},mergeStatus:{}", tenantId, viewId, statViewUniqKey, updateSize,
        System.currentTimeMillis() - start, uSize, newTopologyTableDO.getStatus(), mergeStatus);
    } else {
      log.warn("doCreateTopology finished but upsert size is 0 tenantId:{},viewId:{},cost:{}", tenantId, viewId,
        System.currentTimeMillis() - start);
    }
    return result;
  }

  /**
   * 生成合并图的unique_key
   *
   * @param tenantId
   * @param viewId
   * @param topologyTable
   * @return
   */
  public String createStatViewUniqueKey(String tenantId, String viewId, TopologyTable topologyTable) {
    if (AggRuleType.OldGoal.getRuleType() == topologyTable.getSource() || AggRuleType.MultiDimGoal.getRuleType() == topologyTable.getSource() ||
      AggRuleType.Rpt.getRuleType() == topologyTable.getSource()) {
      return Utils.md5(JSON.toJSONString(topologyTable.getStatRuleList())+viewId);
    }
    return Utils.md5(JSON.toJSONString(topologyTable.getStatRuleList()));
  }

  /**
   * 根据租户获取所有topology table对象
   *
   * @param tenantId 租户id
   * @return
   */
  public List<TopologyTable> findTopologyByTenantId(String tenantId,int[] status) {
    return topologyTableDao.findByTenantId(tenantId,status);
  }


  public String getAggDataInsertSelectSql(TopologyTable topologyTable,
                                          String viewId, int viewVersion,
                                          long batchNum,
                                          String hasCalcViewId,
                                          String hasCalcViewVersion,
                                          String hasCalcSourceId,
                                          TopologyTableAggRuleMonitor statRuleMonitor){
    //String sql = String.format("select name from system.columns where database='%s' and table = 'agg_data' order by position", dbName);

    String ruleId = statRuleMonitor.getFieldId();
    List<String> insertFields = statRuleMonitor.getInsertFields();
    String valueSlot = statRuleMonitor.getValueSlot();
    String dbName = topologyTable.getDatabase();
    String tenantId = topologyTable.getTenantId();
    TopologyTable hasCalcTopologyTable = findByTenantIdAndSourceId(tenantId, hasCalcSourceId);

    try {
      /*List<String> columnList = clickHouseService.executeQuerySQL(tenantId, sql, res -> {
        try {
          return res.getString(1);
        } catch (SQLException e) {
          throw new RuntimeException(e);
        }
      });
      StringBuilder columns = new StringBuilder();
      for(String column : columnList){
        if(!StringUtils.equalsAny(column, "tenant_id","view_id", "view_version", "timestamp", "sys_modified_time", "value_slot")) {
          columns.append(String.format("%s,", column));
        }
      }
      columns.deleteCharAt(columns.length()-1);
       */
      String hasCalcAggColumn = hasCalcTopologyTable.getStatFieldLocation().get(statRuleMonitor.getFieldId());
      String hasCalcAggMergeColumn = String.format("%s_merge", hasCalcAggColumn);
      String aggColumn = topologyTable.getStatFieldLocation().get(statRuleMonitor.getFieldId());
      String aggMergeColumn = String.format("%s_merge", aggColumn);
      String aggColumnStr = String.format("%s", aggColumn);
      String hasCalcAggColumnStr = String.format("%s", hasCalcAggColumn);
      if(insertFields.contains(aggMergeColumn)){
        aggColumnStr = String.format("%s, %s", aggColumn, aggMergeColumn);
        hasCalcAggColumnStr = String.format("%s, %s", hasCalcAggColumn, hasCalcAggMergeColumn);
      }

      //校验缓存的指标的计的结果，count不能为0
      if(!GrayManager.isAllowByRule("disable_same_rule_execute_once_check", tenantId)){
        String checkSql = String.format("select tenant_id from %s.agg_data where tenant_id = '%s' and view_id = '%s' and view_version = %s " +
                                                  " and rule_id = '%s' and batch_num = %d limit 1 ", dbName, tenantId, hasCalcViewId, hasCalcViewVersion, ruleId, batchNum);
        List<String> checkResult = clickHouseService.executeQuerySQL(tenantId, checkSql, res -> {
          try {
            return res.getString(1);
          } catch (SQLException e) {
            throw new RuntimeException(e);
          }
        });
        if(CollectionUtils.isEmpty(checkResult)) {
          log.info("getAggDataInsertSelectSql has calculated rule result check fail, {}, {}, {}, {}, {}", tenantId, hasCalcViewId, hasCalcViewVersion, ruleId, batchNum);
          return null;
        }
      }

      if(!insertFields.contains("rule_id") || !insertFields.contains("batch_num")){
        log.info("getAggDataInsertSelectSql insertFields not contains rule_id and batch_num, {}, {}, {}, {}, {}, {}", Joiner.on(",").join(insertFields), tenantId, hasCalcViewId, hasCalcViewVersion, ruleId, batchNum);
        return null;
      }

      String columns = Joiner.on(",").join(Collections2.filter(insertFields,
              column -> !StringUtils.equalsAny(column, "tenant_id", "view_id", "view_version", "value_slot") && !column.startsWith("agg_")));
      String insertSql = String.format("insert into %s.agg_data(tenant_id, view_id, view_version, %s, value_slot, %s) " +
                      "select '%s', '%s', %d, %s, '%s', %s from %s.agg_data where tenant_id = '%s' and view_id = '%s' and view_version = %s " +
                      "and rule_id = '%s' and batch_num = %d ",
                        dbName, columns, aggColumnStr, tenantId, viewId, viewVersion, columns, valueSlot, hasCalcAggColumnStr, dbName, tenantId, hasCalcViewId, hasCalcViewVersion, ruleId, batchNum);
      return insertSql;
    }
    catch (Exception ex){
      log.error("getAggDataInsertSelectSql executeQuerySQL error, {}, {}, {}, {}, {}", tenantId, viewId, viewVersion, ruleId, batchNum, ex);
    }
    return null;
  }

  /**
   * 找到租户所有的StatViewMonitor
   *
   * @param tenantId                        租户Id
   * @param changedObjectDescribeApiNameSet 变更的对象apiName
   * @return 租户所有的RptViewMonitor
   */
  public List<TopologyTableMonitor> findViewMonitorByTenantId(String tenantId,
                                                              Set<String> changedObjectDescribeApiNameSet,
                                                              long batchNum,
                                                              Long dsBatchNum,
                                                              Set<String> changeGoalSet) {
    long start = System.currentTimeMillis();
    List<TopologyTableMonitor> result = Lists.newArrayList();
    List<TopologyTable> topologyTables = topologyTableDao.findByTenantId(tenantId, new int[] {TopologyTableStatus.Prepared.getValue(), TopologyTableStatus.Calculating.getValue()});
    topologyTables = this.combineTopologyTables(topologyTables);
    Map<String, Boolean> addOrDeleteCacheMap = Maps.newHashMap();
    Map<String, List<String>> changedColumnsMap = Maps.newHashMap();
    for (TopologyTable topologyTable : topologyTables) {
      try {
        //判断指标各个表的数据量，统计出最靠右的大表
        Map<String,Map<String,String>> rightLargeTables = Maps.newHashMap();
        TopologyTableMonitor topologyTableMonitor = this.trans2MonitorByTopologyTable(topologyTable, changedObjectDescribeApiNameSet, addOrDeleteCacheMap, changedColumnsMap, batchNum, dsBatchNum, rightLargeTables, changeGoalSet);
        if (topologyTableMonitor != null) {
          result.add(topologyTableMonitor);
        }
      } catch (Exception e) {
        log.error("statView#toStatViewMonitor {}:{} failed. ", tenantId, topologyTable.getViewId(), e);
      }
    }
    if(CollectionUtils.isNotEmpty(result)){
      long allRulesCount = 0;
      for (TopologyTable topologyTable : topologyTables){
        if(MapUtils.isNotEmpty(topologyTable.getAggEffectApiNames()) && topologyTable.getStatus() != TopologyTableStatus.Prepared.getValue()){
          allRulesCount += topologyTable.getAggEffectApiNames().keySet().size();
        }
      }
      long needCalcRulesCount = 0;
      for(TopologyTableMonitor topologyTableMonitor : result){
        if(CollectionUtils.isNotEmpty(topologyTableMonitor.getStatRuleMonitorList())){
          needCalcRulesCount += topologyTableMonitor.getStatRuleMonitorList().size();
        }
      }
      recordAuditLog(topologyTables.getFirst().getDatabase(), tenantId, "", "", batchNum, System.currentTimeMillis()-start, String.format("all views check finished: all rules:%d, need calculate rules: %d", allRulesCount, needCalcRulesCount));
    }
    log.info("findViewMonitorByTenantId finish tenantId:{},monitorSize:{},cost{}", tenantId, result.size(), System.currentTimeMillis() - start);
    return result;
  }

  /**
   * 查询topology Table 并做图合并
   * @param tenantId
   * @param status
   * @return
   */
  public List<TopologyTable> findCombineTopologyTable(String tenantId, int[] status) {
    List<TopologyTable> topologyTables = topologyTableDao.findByTenantId(tenantId, status);
    return this.combineTopologyTables(topologyTables);
  }

  /**
   * 合并拓扑图
   * 1、目标计算不参与合并。
   * 2、首先过滤出是否已启用的图如果有合并后为增量计算，如果不包含已启用的则为初始化中为全量计算。
   * 3、agg_data view_id 为unique kye,view_version默认为0
   *
   * @param topologyTables
   * @return
   */
  public List<TopologyTable> combineTopologyTables(List<TopologyTable> topologyTables) {
    List<TopologyTable> combineTopologies = Lists.newArrayList();
    Map<String, List<TopologyTable>> groupByUniqueKey = topologyTables.stream()
                                                                      .peek(t -> {
                                                                        if (AggRuleType.MultiDimGoal.getRuleType() ==
                                                                          t.getSource() ||
                                                                          AggRuleType.OldGoal.getRuleType() ==
                                                                            t.getSource()) {
                                                                          combineTopologies.add(t);
                                                                        }
                                                                      })
                                                                      .filter(t1 -> AggRuleType.Agg.getRuleType() ==
                                                                        t1.getSource() && StringUtils.isNotBlank(t1.getStatViewUniqueKey()))
                                                                      .collect(Collectors.groupingBy(TopologyTable::getStatViewUniqueKey));
    groupByUniqueKey.forEach((uniqueKey, topologies) -> {
      if (topologies.size() > 1) {
        Optional<TopologyTable> calTopologyOp = topologies.stream()
                                                          .filter(topologyTable ->
                                                            TopologyTableStatus.Calculating.getValue() ==
                                                              topologyTable.getStatus())
                                                          .max(Comparator.comparingInt(TopologyTable::getVersion));
        if (calTopologyOp.isPresent()) {
          combineTopologies.add(calTopologyOp.get());
        } else {
          //如果都是初始化中的图取第一个即可
          combineTopologies.add(topologies.get(0));
        }
      } else {
        combineTopologies.add(topologies.get(0));
      }
    });
    return combineTopologies;
  }

  /**
   * 上报审计日志
   * @param message
   */
  private void recordAuditLog(String database, String tenantId, String viewId, String ruleId, long batchNum, long cost, String message){
    if (!GrayManager.isAllowByRule("ch_check_columns_audit_log", tenantId)){
      return;
    }
    BizAuditLog auditLog = new BizAuditLog();
    auditLog.setModule("STAT_CHART_DATA_CALC");
    auditLog.setStartTime(System.currentTimeMillis());
    auditLog.setFrom(database);
    auditLog.setTenantId(tenantId);
    auditLog.setViewId(viewId);
    auditLog.setNum(batchNum);
    auditLog.setCost(cost);
    auditLog.setFromId("increment_calc_check_changed_columns");
    auditLog.setExtra(String.format("rule_id:%s", ruleId));
    if(message.length() > 30000){
      int i = 0;
      for (String msg: Splitter.fixedLength(30000).split(message)) {
        auditLog.setMessage(String.format("[PART%d]%s",i,msg));
        auditLog.log();
        i++;
      }
    }
    else {
      auditLog.setMessage(message);
      auditLog.log();
    }
  }

  /**
   * 用sql从CH中实时group by计算当前批次是否有【新增】或者【删除】
   * @param tenantId
   * @param viewId
   * @param ruleId
   * @param dbTable
   * @param batchNum
   * @param apiName
   * @param addOrDeleteCacheMap
   * @return
   */
  public boolean hasAddOrDelete(String tenantId, String viewId, String ruleId, String dbTable, long batchNum, String apiName, @Nonnull Map<String, Boolean> addOrDeleteCacheMap){
    String tableKey = String.format("%s|%s",dbTable,apiName);
    try{
      if(addOrDeleteCacheMap.containsKey(tableKey)){
        return addOrDeleteCacheMap.get(tableKey);
      }
      boolean bAdd = true;
      boolean bDelete = true;
      List<String> dbAndTable = Splitter.on(".").splitToList(dbTable);
      String orderKeySql = String.format("select sorting_key from system.tables where name = '%s' and database = '%s' limit 1", dbAndTable.get(1), dbAndTable.get(0));
      List<String> sortKey = clickHouseService.queryCHTablePkColumnsByEI(tenantId, dbAndTable.get(1));
      if (CollectionUtils.isEmpty(sortKey)) {
        log.warn("hasAddOrDelete tenantId {} table {} has no order key, orderKeySql:{}", tenantId, dbTable, orderKeySql);
        addOrDeleteCacheMap.put(tableKey, true);
        return true;
      }
      long start = System.currentTimeMillis();
      String apiNameFilter = "";
      if(Objects.equals("biz_account",dbAndTable.get(1)) || Objects.equals("biz_account_downstream",dbAndTable.get(1))){
        if(Objects.equals(apiName,"biz_account")){
          apiNameFilter = String.format(" and object_describe_api_name in ('%s') ","AccountObj");
        }else if(Objects.equals(apiName,"biz_account_main_data")){
          apiNameFilter = String.format(" and object_describe_api_name in ('%s') ","AccountMainDataObj");
        }
      }
      String preWhere = "";
      if (GrayManager.isAllowByRule("use_ch_ods_partition", dbAndTable.get(0))) {
        preWhere = " PREWHERE bi_sys_ods_part in('s','c') ";
      }
      //多对象共存需要增加api_name过滤；
      if (Utils.isCommonObjectTable(dbAndTable.get(1))) {
        apiNameFilter = String.format(" and object_describe_api_name in ('%s') ", apiName);
      } else if (Utils.isObjectDataLangTable(dbAndTable.get(1)) && apiName.endsWith(Constants.LANG)) {
        apiNameFilter = String.format(" and object_describe_api_name in ('%s') ", apiName.substring(0, apiName.length() - 5));
      }
      String keyStr = sortKey.stream().filter(column -> !StringUtils.equalsIgnoreCase("bi_sys_flag", column.trim())).collect(Collectors.joining(","));
      String eiColumnName = "tenant_id";
      if (Objects.equals("sale_action_stage", dbAndTable.get(1))) {
        eiColumnName = "ei";
      }
      String addSql = String.format("select %s from %s final %s where %s = '%s' %s and bi_sys_batch_id = %d group by %s having count(distinct bi_sys_flag) = 1 limit 1", keyStr, dbTable, preWhere, eiColumnName,tenantId, apiNameFilter, batchNum, keyStr);
      List<String> keys = Splitter.on(",").splitToList(keyStr);
      List<String> addId = clickHouseService.executeQuerySQL(tenantId, addSql, res -> {
        try {
          List<String> values = new ArrayList<>();
          for (String key : keys) {
            values.add(res.getString(key.trim()));
          }
          return Joiner.on("_").join(values);
        } catch (SQLException e) {
          log.error("hasAddOrDelete executeQuerySQL error, tenantId:{}, addSql:{}", tenantId, addSql, e);
        }
        return "";
      });
      if(CollectionHelper.isEmpty(addId)){
        bAdd = false;
      }
      recordAuditLog(dbAndTable.get(0), tenantId, viewId,ruleId, batchNum,System.currentTimeMillis() - start, String.format("hasAddOrDelete bAdd: %s, timestamp:%d, add ids: %s, sql: %s", bAdd, System.currentTimeMillis(), addId, addSql));
      start = System.currentTimeMillis();
      if (!Constants.isHasIsDeleted(dbAndTable.get(1))) {
        bDelete = false;
        log.info(" hasAddOrDelete table:{} do not check is_delete",dbAndTable.get(1));
        recordAuditLog(dbAndTable.get(0), tenantId, viewId, ruleId, batchNum,System.currentTimeMillis() - start, String.format("hasAddOrDelete bDelete: %s, timestamp:%d, delete ids: %s, sql: %s", bDelete, System.currentTimeMillis(), "empty", "no sql"));
      } else {
        String isDeletedCol = "is_deleted";
        if (Objects.equals(dbAndTable.get(1), Constants.EN_DRR_DAILY)) {
          isDeletedCol = "is_delete";
        }
        String deleteSql = String.format("select %s from %s final %s where %s = '%s' %s and bi_sys_batch_id = %d and bi_sys_flag = 1 and %s in (1,-1,-2)  limit 1", keyStr, dbTable, preWhere, eiColumnName, tenantId, apiNameFilter, batchNum, isDeletedCol);
        List<String> deleteId = clickHouseService.executeQuerySQL(tenantId, deleteSql, res -> {
          try {
            List<String> values = new ArrayList<>();
            for (String key : keys) {
              values.add(res.getString(key.trim()));
            }
            return Joiner.on("_").join(values);
          } catch (SQLException e) {
            log.error("hasAddOrDelete executeQuerySQL error, tenantId:{}, deleteSql:{}", tenantId, deleteSql, e);
          }
          return "";
        });
        if (CollectionHelper.isEmpty(deleteId)) {
          bDelete = false;
        }
        recordAuditLog(dbAndTable.getFirst(), tenantId, viewId, ruleId, batchNum,System.currentTimeMillis() - start, String.format("hasAddOrDelete bDelete: %s, timestamp:%d, delete ids: %s, sql: %s", bDelete, System.currentTimeMillis(), deleteId, deleteSql));
      }
      boolean bResult = bAdd || bDelete;
      addOrDeleteCacheMap.put(tableKey, bResult);
      return bResult;
    }
    catch (Exception ex){
      log.error("hasAddOrDelete tenant_id {}, table {}, apiName {}, batchNum {}, error", tenantId, dbTable, apiName, batchNum, ex);
      addOrDeleteCacheMap.put(tableKey, true);
    }
    return true;
  }

  /**
   * 用sql从CH中实时group by计算当前批次哪些column发生了【变更】
   * @param tenantId
   * @param viewId
   * @param ruleId
   * @param dbTable
   * @param batchNum
   * @param apiName
   * @param changedColumnsMap
   * @return
   */
  public List<String> getChangedColumns(String tenantId, String viewId, String ruleId, String dbTable, long batchNum, String apiName, Map<String, List<String>> changedColumnsMap){
    String tableKey = String.format("%s|%s",dbTable,apiName);
    if(changedColumnsMap != null && changedColumnsMap.containsKey(tableKey)){
      return changedColumnsMap.get(tableKey);
    }
    List<String> pair = Splitter.on(".").splitToList(dbTable);
    String database = pair.get(0);
    String table = pair.get(1);
    String orderKeySql = String.format("select sorting_key from system.tables where name = '%s' and database = '%s' limit 1", table, database);
    List<String> sortKey = clickHouseService.queryCHTablePkColumnsByEI(tenantId, table);
    if(CollectionUtils.isEmpty(sortKey)){
      throw new RuntimeException(String.format("getChangedColumns tenantId %s table %s has no order key, orderKeySql:%s", tenantId, dbTable,  orderKeySql));
    }
    String keyStr = sortKey.stream().filter(column -> !StringUtils.equalsIgnoreCase("bi_sys_flag", column.trim())).collect(Collectors.joining(","));
    String apiNameFilter = "";
    long start = System.currentTimeMillis();
    if (Objects.equals("biz_account", table) || Objects.equals("biz_account_downstream", table)) {
      if (Objects.equals(apiName, "biz_account")) {
        apiNameFilter = String.format(" and object_describe_api_name in ('%s') ", "AccountObj");
      } else if (Objects.equals(apiName, "biz_account_main_data")) {
        apiNameFilter = String.format(" and object_describe_api_name in ('%s') ", "AccountMainDataObj");
      }
    }
    String preWhere = "";
    if (GrayManager.isAllowByRule("use_ch_ods_partition", database)) {
      preWhere = " PREWHERE bi_sys_ods_part in('s','c') ";
    }
    //这个表是多对象共存，需要增加api_name过滤;
    if(Utils.isCommonObjectTable(table)){
      apiNameFilter = String.format(" and object_describe_api_name in ('%s') ",apiName);
    } else if (Utils.isObjectDataLangTable(table) && apiName.endsWith(Constants.LANG)) {
      apiNameFilter = String.format(" and object_describe_api_name in ('%s') ", apiName.substring(0, apiName.length() - 5));
    }
    String excludeColumns = " '','version','sys_modified_time','id','tenant_id','_id', 'bi_sys_flag', 'bi_sys_batch_id','bi_sys_is_deleted','bi_sys_version' ";
    StringBuilder columnChangeStr = new StringBuilder();
    String eiColumnName = "tenant_id";
    if (Objects.equals("sale_action_stage", table)) {
      eiColumnName = "ei";
      excludeColumns = " '','version','sys_modified_time','id','ei','_id', 'bi_sys_flag', 'bi_sys_batch_id','bi_sys_is_deleted','bi_sys_version' ";
    }
    String queryTemplate = """
            select arrayJoin(arrayFilter(k -> (k not in (%s)) ,arraySort(arrayDistinct(arrayFlatten(groupArray(row_change_columns)))))) as column
            from (
                    select arrayDistinct(array(
                      %s
                    )) as row_change_columns
                    from (
                            select * from %s.%s %s
                              where %s = '%s' %s and bi_sys_batch_id = %d
                        )
                    group by %s
                );
            """;
    String columnSql = String.format("select name, type from system.columns where database = '%s' and table = '%s' and name not in (%s)", database, table, excludeColumns);
    List<String> columns = clickHouseService.executeQuerySQL(tenantId, columnSql, res -> {
      try {
        return String.format("%s.%s", res.getString("name"), res.getString("type"));
      } catch (SQLException e) {
        log.error("getChangedColumns executeQuerySQL error, tenantId:{}, columnSql:{}", tenantId, columnSql, e);
      }
      return "";
    });
    for (String column : columns){
      List<String> nameType = Splitter.on(".").splitToList(column);
      if(StringUtils.startsWithIgnoreCase(nameType.get(1), "Nullable")){
        columnChangeStr.append(String.format("if(arrayUniq(groupArray(tuple(%s))) > 1, '%s', ''),", nameType.get(0), nameType.get(0)));
      }
      else {
        columnChangeStr.append(String.format("if(arrayUniq(groupArray(%s)) > 1, '%s', ''),", nameType.get(0), nameType.get(0)));
      }
    }
    columnChangeStr.append("''");
    String querySql = String.format(queryTemplate, excludeColumns, columnChangeStr, database, table,preWhere, eiColumnName,tenantId, apiNameFilter, batchNum, keyStr);
    List<String> results = clickHouseService.executeQuerySQL(tenantId, querySql, res -> {
      try {
        return res.getString("column");
      } catch (SQLException e) {
        log.error("getChangedColumns executeQuerySQL error, tenantId:{}, querySql:{}", tenantId, columnSql, e);
      }
      return "";
    });
    recordAuditLog(database, tenantId, viewId,ruleId, batchNum, System.currentTimeMillis()-start, String.format("getChangedColumns sql: %s", querySql));
    changedColumnsMap.put(tableKey, results);
    return results;
  }

  private void checkChangedTables(TopologyTable topologyTable, Set<String> fieldIds, Set<String> changedObjectDescribeApiNameSet){
    try {
      List<TopologyTableAggRule> aggRuleList = topologyTable.getStatRuleList().stream().filter(rule -> fieldIds.contains(rule.getFieldId())).toList();
      for(TopologyTableAggRule statAggRule : aggRuleList) {
        //获取指标table
        List<AggConfig> aggConfigList = Lists.newArrayList();
        statAggRule.getAggConfigStringList().forEach(aggConfigString -> aggConfigList.add(AggConfig.parse(aggConfigString, topologyTable.getTimezone())));
        Set<String> aggTableList = Sets.newHashSet();
        aggConfigList.forEach(aggConfig -> aggTableList.add(aggConfig.getTableName()));
        //指标涉及的table哪些发生了变更 todo 没考虑自关联
        Map<String, Set<String>> tableColumnsMap = statAggRule.selectTableAndColumn();
        Set<String> changeTableList = Sets.newHashSet();
        for (String objTable : tableColumnsMap.keySet()) {
          String tableName = objTable;
          String apiName = objTable;
          if (objTable.contains("|")) {
            List<String> tableAndApiName = Splitter.on("|").splitToList(objTable);
            tableName = tableAndApiName.get(0);
            apiName = tableAndApiName.get(1);
          }
          if (changedObjectDescribeApiNameSet.contains(apiName)) {
            changeTableList.add(tableName);
          }
        }
        boolean bOnlyRuleTableChange = false;
        if(aggTableList.containsAll(changeTableList)) {
          bOnlyRuleTableChange = true;
        }
        BizAuditLog auditLog = new BizAuditLog();
        auditLog.setModule("STAT_CHART_DATA_CALC");
        auditLog.setStartTime(System.currentTimeMillis());
        auditLog.setFrom(topologyTable.getDatabase());
        auditLog.setTenantId(topologyTable.getTenantId());
        auditLog.setViewId(topologyTable.getViewId());
        auditLog.setMessage(String.format("OnlyRuleTableChange:%s, aggTable: %s, changeTables: %s, changedApiNames:%s, ruleTables:%s", bOnlyRuleTableChange,
                Joiner.on(",").join(aggTableList),
                Joiner.on(",").join(changeTableList),
                Joiner.on(",").join(changedObjectDescribeApiNameSet),
                Joiner.on(",").join(tableColumnsMap.keySet())));
        auditLog.setExtra(String.format("rule_id:%s", statAggRule.getFieldId()));
        auditLog.setFromId("check_only_rule_table_changed");
        auditLog.log();
      }
    }
    catch (Exception ex){
      log.error("checkChangedTables exception,{},{},{}", topologyTable.getTenantId(), topologyTable.getDatabase(), topologyTable.getViewId(), ex);
    }
  }

  public boolean isNeedCalculateByTopology(String tenantId, TopologyTableAggRule statAggRule, String viewId, long batchNum, String database,
                                            Map<String, Boolean> addOrDeleteCacheMap,
                                            Map<String, List<String>> changedColumnsMap){
    long start = System.currentTimeMillis();
    try {
      Map<String, Set<String>> tableColumnsMap = statAggRule.selectTableAndColumn();
      recordAuditLog(database, tenantId, viewId, statAggRule.getFieldId(), batchNum,System.currentTimeMillis()-start, String.format("agg rule tables and columns: %s", tableColumnsMap));
      for (String objTable : tableColumnsMap.keySet()) {
        String tableName = objTable;
        String apiName = objTable;
        if(objTable.contains("|")){
          List<String> tableAndApiName = Splitter.on("|").splitToList(objTable);
          tableName = tableAndApiName.get(0);
          apiName = tableAndApiName.get(1);
        }
        String dbTable = String.format("%s.%s", database, tableName);
        //判断当前批次是否有【新增】和【删除】的数据，如果有那么此批次需要增量计算
        if (hasAddOrDelete(tenantId, viewId, statAggRule.getFieldId(), dbTable, batchNum, apiName, addOrDeleteCacheMap)) {
          recordAuditLog(database, tenantId, viewId, statAggRule.getFieldId(), batchNum,System.currentTimeMillis()-start, String.format("table %s api_name %s has add or delete rows", dbTable, apiName));
          return true;
        }
        //判断当前批次【更新】的列中是否有被指标使用到了，如果有那么当前批次需要重新计算
        start = System.currentTimeMillis();
        List<String> tableChangeColumns = getChangedColumns(tenantId, viewId, statAggRule.getFieldId(), dbTable, batchNum, apiName, changedColumnsMap);
        if (CollectionHelper.hasIntersection(tableChangeColumns, tableColumnsMap.get(objTable))) {
          recordAuditLog(database, tenantId, viewId, statAggRule.getFieldId(), batchNum,System.currentTimeMillis()-start, String.format("table %s apiName %s involved changed columns, rule_columns: %s, changed_columns:%s", dbTable, apiName, tableColumnsMap.get(objTable), tableChangeColumns));
          return true;
        }
        else {
          recordAuditLog(database, tenantId, viewId, statAggRule.getFieldId(), batchNum,System.currentTimeMillis()-start, String.format("table %s apiName %s not involved changed columns, rule_columns: %s, changed_columns:%s", dbTable, apiName, tableColumnsMap.get(objTable), tableChangeColumns));
        }
      }
      return false;
    }
    catch (Exception ex){
      log.error("isNeedCalculateByTopology error, {}:{}",tenantId,viewId, ex);
      recordAuditLog(database, tenantId, viewId, statAggRule.getFieldId(), batchNum,System.currentTimeMillis()-start, String.format("isNeedCalculateByTopology check changed columns error: %s", ex));
      //发生异常的时候返回true,确保不丢失计算
      return true;
    }
  }
  /**
   * 根据变更对象和表获取统计图中每个指标的计算sql，包含增量计算或全量计算
   *
   * @param topologyTable                   拓扑对象
   * @param changedObjectDescribeApiNameSet 变更的apiName集合
   * @param changeGoalSet 当前批次涉及考核维度变更的目标规则
   * @return
   */
  public TopologyTableMonitor trans2MonitorByTopologyTable(TopologyTable topologyTable,
                                                           Set<String> changedObjectDescribeApiNameSet,
                                                           Map<String, Boolean> addOrDeleteCacheMap,
                                                           Map<String, List<String>> changedColumnsMap,
                                                           long batchNum,
                                                           Long dsBatchNum,
                                                           Map<String,Map<String,String>> rightLargeTables,
                                                           Set<String> changeGoalSet) {
    boolean isDSCalculate = this.downStreamAggIsCalculate(topologyTable);
    boolean standalone = pgDataSource.isStandalone(topologyTable.getTenantId());
    if (topologyTable.getStatus() == TopologyTableStatus.Prepared.getValue()) {
      if (GrayManager.isAllowByRule("biDataSyncPolicyGray", topologyTable.getTenantId())) {
        this.addDownStreamRuleWhereSql(topologyTable);
      }
      return topologyTable.toStatViewMonitor(null, isDSCalculate, rightLargeTables, GoalChangeType.DATA_ALL, null, standalone);
    }
    Set<String> fieldIds = topologyTable.findNeedComputeField(changedObjectDescribeApiNameSet);
    //当全量重刷的时候，不能跳过指标
    if (topologyTable.getStatus() != TopologyTableStatus.Prepared.getValue() && GrayManager.isAllowByRule("ch_check_columns_changed", topologyTable.getTenantId())) {
      for (TopologyTableAggRule statRule : topologyTable.getStatRuleList()) {
        if(fieldIds == null || !fieldIds.contains(statRule.getFieldId())){
          continue;
        }
        long finalBatchNum = dsBatchNum != null && Constants.AGG_DOWNSTREAM_DATA.equals(statRule.getRootNodeTable().getName()) ? dsBatchNum : batchNum;
        long start = System.currentTimeMillis();
        //计算当前批次变更数据涉及的列，如果指标中的table以及column在当前批次没有发生变更，则不需要增量计算
        if(!isNeedCalculateByTopology(topologyTable.getTenantId(),statRule, topologyTable.getViewId(), finalBatchNum, topologyTable.getDatabase(), addOrDeleteCacheMap, changedColumnsMap)){
          //上报审计日志
          recordAuditLog(topologyTable.getDatabase(), topologyTable.getTenantId(), topologyTable.getViewId(), statRule.getFieldId(), batchNum, CommonUtils.cost(start), "columns has not changed, no need calculating");
          //ch_take_columns_changed是总开关；ch_take_columns_changed_%s_views按ei+view_id放开；ch_take_columns_changed_all_view按企业所有的view都放开
          if(GrayManager.isAllowByRule("ch_take_columns_changed", topologyTable.getTenantId()) || GrayManager.isAllowByRule("ch_take_columns_changed_db", topologyTable.getDatabase())){
            if (GrayManager.isAllowByRule("ch_take_columns_changed_all_view", topologyTable.getTenantId()) ||
              GrayManager.isAllowByRule(String.format("ch_take_columns_changed_%s_views", topologyTable.getTenantId()), topologyTable.getViewId()) ||
              GrayManager.isAllowByRule("ch_take_columns_changed_db", topologyTable.getDatabase())) {
              recordAuditLog(topologyTable.getDatabase(), topologyTable.getTenantId(), topologyTable.getViewId(), statRule.getFieldId(), batchNum, CommonUtils.cost(start), "rule do not need calculate");
              fieldIds.remove(statRule.getFieldId());
            }
          }
        }
      }
    }
    //不为空证明有业务数据变更需要增量计算
    if (CollectionUtils.isNotEmpty(fieldIds)) {
      if (GrayManager.isAllowByRule("biDataSyncPolicyGray", topologyTable.getTenantId())) {
        this.addDownStreamRuleWhereSql(topologyTable);
      }
      //统计是否只有指标表发生了变更，上报审计日志
      if(GrayManager.isAllowByRule("allow_only_one_rule_check", topologyTable.getTenantId())) {
        checkChangedTables(topologyTable, fieldIds, changedObjectDescribeApiNameSet);
      }
      //目标且考核维度也变更了
      GoalChangeType goalChangeType = changeGoalSet.contains(topologyTable.getViewId()) ?
        GoalChangeType.GOAL_ALL_DATA_INC :
        GoalChangeType.DATA_INC;
      return topologyTable.toStatViewMonitor(fieldIds, isDSCalculate, rightLargeTables, goalChangeType, null, standalone);
    }
    //fieldIds为空:业务数据没发生变化;changeGoalSet存在,目标考核维度变更了。需要重新计算变更的考核维度的完成值
    if (CollectionUtils.isEmpty(fieldIds) && changeGoalSet.contains(topologyTable.getViewId())) {
      if (GrayManager.isAllowByRule("biDataSyncPolicyGray", topologyTable.getTenantId())) {
        this.addDownStreamRuleWhereSql(topologyTable);
      }
      return topologyTable.toStatViewMonitor(fieldIds, isDSCalculate, rightLargeTables, GoalChangeType.GOAL_ALL, null, standalone);
    }
    return null;
  }

  /**
   * 按照unique key 更新topology table状态
   *
   * @param tenantId        租户id
   * @param status          状态
   * @param uniqueKey       统计图唯一标记
   * @param batchNum        批次
   * @param maxModifiedTime 最新统计时间
   * @return
   */
  public int updateTopologyTableStatusByUniqueKey(String tenantId,
                                                  Integer status,
                                                  String uniqueKey,
                                                  long batchNum,
                                                  Long maxModifiedTime,
                                                  boolean isUpdateStatRule,
                                                  List<TopologyTableAggRule> statRuleList,
                                                  boolean incVersion) {
    return topologyTableDao.updateTopologyStatusByUniqueKey(tenantId, status, uniqueKey, batchNum, maxModifiedTime, isUpdateStatRule, JSON.toJSONString(statRuleList), incVersion);
  }

  /**
   * 反查 unique key相同的拓扑图
   *
   * @param tenantId          租户id
   * @param statViewUniqueKey 图唯一标识
   * @return
   */
  public List<String> queryViewIdByUniqueKey(String tenantId, String statViewUniqueKey) {
    return topologyTableDao.queryViewIdByUniqueKey(tenantId, statViewUniqueKey);
  }

  public int batchUpdateStatViewStatus(String tenantId, List<String> sourceIds, int status) {
    return topologyStatusDao.batchUpdateStatViewStatus(tenantId, sourceIds, status);
  }

  public TopologyTable findByTenantIdAndSourceId(String tenantId, String sourceId){
    return topologyTableDao.findByTenantIdAndSourceId(tenantId, sourceId);
  }

  /**
   * 批量刷stat_view_unique_key 值
   * @param statViewBatchArg
   * @return
   */
  public int batchInitTopologyTableMerge(StatViewBatchArg statViewBatchArg) {
    Preconditions.checkArgument(statViewBatchArg != null, "statViewBatchArg is null");
    AtomicInteger atomicInteger = new AtomicInteger(0);
    List<String> tenantIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(statViewBatchArg.getTenantId());
    tenantIds.forEach(tenantId -> {
      if (statViewBatchArg.isDel()) {
        int delCount = topologyTableDao.deleteTopologyMergeByEI(tenantId);
        log.info("deleteTopologyMergeByEI tenantId:{},size:{}", tenantId, delCount);
      }
      List<TopologyTable> topologyTables = this.findTopologyByTenantId(tenantId, new int[] {TopologyTableStatus.Prepared.getValue(), TopologyTableStatus.Calculating.getValue(), TopologyTableStatus.NONeedCal.getValue()});
      List<TopologyTable> combineTopologyTables = this.combineTopologyTables(topologyTables);
      if (CollectionUtils.isNotEmpty(combineTopologyTables)) {
        combineTopologyTables.stream()
                             .filter(t -> StringUtils.isNotBlank(t.getStatViewUniqueKey()))
                             .forEach(topologyTable -> {
                               TopologyTableMergeDO topologyTableMergeDO = TopologyTableMergeDO.of(topologyTable.getTenantId(), topologyTable.getStatViewUniqueKey(), topologyTable.getVersion(), topologyTable.getStatus(), topologyTable.getBatchNum(), 0L);
                               int result = topologyTableDao.insertTopologyTableMergeOnConflictDoNothing(topologyTable.getTenantId(), Lists.newArrayList(topologyTableMergeDO));
                               atomicInteger.addAndGet(result);
                             });
      }
    });
    return atomicInteger.get();
  }

  /**
   * 判断下游指标是否可以计算
   * @param topologyTable
   * @return
   */
  public boolean downStreamAggIsCalculate(TopologyTable topologyTable) {
    if (topologyTable.isNeedCheckTables()) {
      return clickHouseService.checkAllExistCHUpTables(topologyTable.getTenantId(), topologyTable.getAggDownStream()
                                                                                                 .getUpTables());
    }
    return true;
  }

  /**
   * 更新topology table merge 状态
   *
   * @param tenantId          租户id
   * @param statViewUniqueKey id
   * @param status            状态
   */
  public int updateTopologyTableMergeStatus(String tenantId,
                                            String statViewUniqueKey,
                                            int status,
                                            long batchNum,
                                            Long maxModifiedTime) {
    return topologyTableDao.updateTopologyTableMergeStatus(tenantId, statViewUniqueKey, status,batchNum,maxModifiedTime);
  }

  /**
   * 更新opology table merge 的最近计算时间
   * @param tenantId
   * @param id
   * @param status
   * @param batchNum
   * @param maxModifiedTime
   * @return
   */
  public int updateTopologyTableMergeByStatus(String tenantId,
                                              String id,
                                              int status,
                                              long batchNum,
                                              Long maxModifiedTime) {
    return topologyTableDao.updateTopologyTableMergeByStatus(tenantId, id, status, batchNum, maxModifiedTime);
  }

  /**
   * 更新topology table merge 并更新版本
   * @param tenantId 租户id
   * @param id  主键
   * @param status 状态
   * @param batchNum  批次
   * @param maxModifiedTime
   * @return
   */
  public int updateTopologyTableMergeStatusAndVersion(String tenantId,
                                                      String id,
                                                      int status,
                                                      long batchNum,
                                                      Long maxModifiedTime) {
    return topologyTableDao.updateTopologyTableMergeStatusAndVersion(tenantId, id, status, batchNum, maxModifiedTime);
  }

  public  int initTopologyTableMergeOrIncVersion(TopologyTableMergeDO topologyTableMergeDO){
    return topologyTableDao.initTopologyTableMergeOrIncVersion(topologyTableMergeDO);
  }
  /**
   * 批量刷stat_view_unique_key 值
   * @param statViewBatchArg
   * @return
   */
  public int batchInitAllAggFields(StatViewBatchArg statViewBatchArg) {
    Preconditions.checkArgument(statViewBatchArg != null, "statViewBatchArg is null");
    AtomicInteger atomicInteger = new AtomicInteger(0);
    List<String> tenantIds = Splitter.on(CharMatcher.anyOf(",|")).splitToList(statViewBatchArg.getTenantId());
    tenantIds.forEach(tenantId -> {
      List<Map<String, String>> aggEffects = topologyTableDao.queryAggRuleEffectTenantId(tenantId);
      if (CollectionUtils.isNotEmpty(aggEffects)) {
        aggEffects.forEach(aggEffectMap -> {
          String sourceId = aggEffectMap.get("source_id");
          String aggEffectApiName = aggEffectMap.get("agg_effect_api_names");
          if (StringUtils.isNotEmpty(aggEffectApiName)) {
            try {
              Map<String, Set<String>> effectApiNameMap = JSON.parseObject(aggEffectApiName, new TypeReference<>() {
              });
              if (MapUtils.isNotEmpty(effectApiNameMap)) {
                Map<String, Integer> allAggFieldMap = effectApiNameMap.keySet()
                                                                      .stream()
                                                                      .collect(Collectors.toMap(k -> k, v -> 1));
                topologyTableDao.updateAllAggStatField(tenantId, sourceId, JSON.toJSONString(allAggFieldMap));
              }
            } catch (Exception e) {
              log.error("agg_effect_api_names parse error tenantId:{}:{}", tenantId, aggEffectApiName, e);
            }
          }
        });
      }
    });
    return atomicInteger.get();
  }

  /**
   * 如果是1+N的上游企业需要实时填补虚拟指标的过滤条件
   */
  public void addDownStreamRuleWhereSql(TopologyTable topologyTable) {
    if (topologyTable.getAggDownStream() == null) {
      return;
    }
    String tenantId = topologyTable.getTenantId();
    String[] fieldIds = topologyTable.getStatFieldLocation().keySet().toArray(new String[0]);
    Map<String, String> inSqlMap = statViewDao.queryDownStreamFields(tenantId, fieldIds);
    if (inSqlMap.isEmpty()) {
      log.error("downStreamRule policyId is null, please check tenantId:{}, viewId:{}, fieldIds:{}", tenantId, topologyTable.getViewId(), Arrays.toString(fieldIds));
      return;
    }
    topologyTable.getStatRuleList().forEach(statRule -> {
      if (Constants.AGG_DOWNSTREAM_DATA.equals(statRule.getRootNodeTable().getName())) {
        if (StringUtils.isBlank(inSqlMap.get(statRule.getFieldId()))) {
          log.error("downStreamRule policyId is null, please check tenantId:{}, viewId:{}, fieldId:{}", tenantId, topologyTable.getViewId(), statRule.getFieldId());
          statRule.getRootNodeTable().appendSubWheres(" 1 = 2 ");
        } else {
          statRule.getRootNodeTable().appendSubWheres(inSqlMap.get(statRule.getFieldId()));
        }
      }
    });
  }

  /**
   * 如果是用户分群,需要添加用户分群过滤条件
   */
  public void addUserSegmentFilterSql(String tenantId, TopologyTable topologyTable, StatViewPreArg.UserSegmentFilter userSegmentFilter) {
    String filterSql = "";
    String userSegmentIdSql = Joiner.on("','").join(userSegmentFilter.getUserSegmentIdList());
    if (FilterType.IS.getId() == userSegmentFilter.getOperator()) {
      filterSql = String.format(Utils.USER_SEGMENT_FILTER_SQL, FilterType.IN.getOperator(), topologyTable.getDatabase(), tenantId, userSegmentIdSql);
    } else if (FilterType.NOTIS.getId() == userSegmentFilter.getOperator()) {
      filterSql = String.format(Utils.USER_SEGMENT_FILTER_SQL, FilterType.NOTIN.getOperator(), topologyTable.getDatabase(), tenantId, userSegmentIdSql);
    } else {
      log.error("addUserSegmentFilterSql is error, operator is invalid, tenantId:{}, viewId:{}, userSegmentFilter:{}",
        tenantId, topologyTable.getViewId(), JSON.toJSONString(userSegmentFilter));
    }
    if (StringUtils.isNotBlank(filterSql)) {
      for (TopologyTableAggRule statRule : topologyTable.getStatRuleList()) {
        statRule.getRootNodeTable().appendSubWheres(filterSql);
      }
    }
  }

}
