package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.entity.DbSyncInfoFlowDO;
import com.fxiaoke.bi.warehouse.dws.service.DbTableSyncInfoInterface;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DBTableSyncInfoMapper extends I<PERSON>rudMapper<DbTableSyncInfoDO>, ITenant<DBTableSyncInfoMapper> {
  @Select("SELECT * FROM db_table_sync_info where db_sync_id=#{db_sync_id} and batch_num=#{batch_num}" +
    " and length(api_name_ei_map) > 2 and is_deleted=0")
  List<DbTableSyncInfoDO> findByDBSyncInfoID(@Param("db_sync_id") String dbSyncId,@Param("batch_num") long batchNum);

  /**
   * 查询某个db表的最新同步信息
   * @param dbSyncId dbid
   * @param tableName 表名
   * @return {@link DbTableSyncInfoDO}
   */
  @Select("SELECT * FROM db_table_sync_info where db_sync_id=#{db_sync_id} and table_name=#{table_name} and is_deleted=0 limit 1")
  DbTableSyncInfoDO findByDBSyncInfoByTable(@Param("db_sync_id") String dbSyncId,@Param("table_name") String tableName);

  @Select(
    "select dts.* from db_sync_info ds,db_table_sync_info dts where ds.pg_db=#{pg_db} and ch_db=#{ch_db} and ds.pg_schema=#{pg_schema} " +
      "and ds.id=dts.db_sync_id and dts.table_name=any(array[#{tableNames}]) and dts.is_deleted=0 ")
  List<DbTableSyncInfoDO> queryDbTableSyncInfo(@Param("pg_db") String pgDb,
                                               @Param("ch_db") String chDb,
                                               @Param("pg_schema") String pgSchema,
                                               @Param("tableNames") String[] tableNames);
  @Select(
    "select * from db_table_sync_info where db_sync_id=#{dbSyncId} and table_name=any(array[#{tableNames}]) and is_deleted=0")
  List<DbTableSyncInfoDO> queryDbTableSyncInfoBySyncId(@Param("dbSyncId") String dbSyncId,
                                                       @Param("tableNames") String[] tableNames);

  /**
   * 获取最近的一个同步批次和计算批次
   */
  @Select("SELECT dsi.id AS db_sync_id, dsi.batch_num AS batch_num, CAST(CASE WHEN dai.sync_batch_nums='' THEN '0' ELSE REGEXP_REPLACE(dai.sync_batch_nums, '.*,', '') END AS INTEGER) AS current_batch_num, dsi.last_sync_time FROM db_sync_info dsi INNER JOIN db_agg_info dai ON dai.id = dsi.id WHERE dsi.ch_db = #{chDb} AND dsi.pg_db = #{pgDb} AND dsi.pg_schema = #{pgSchema} LIMIT 1")
  DbTableSyncInfoInterface.DbSyncAggInfo querySyncAndAggBatchNum(@Param("chDb") String chDb,
                                                                 @Param("pgDb") String pgDb,
                                                                 @Param("pgSchema") String pgSchema);

  @Select("SELECT max_sys_modified_time, last_sync_time FROM db_sync_info_flow WHERE db_sync_id = #{dbSyncId} ORDER BY create_time DESC LIMIT 1")
  DbSyncInfoFlowDO queryTableLastUpdateTime(@Param("dbSyncId") String dbSyncId);

  @Select("SELECT max_sys_modified_time, last_sync_time FROM db_sync_info_flow WHERE db_sync_id = #{dbSyncId} AND cal_batch_num= (SELECT MAX(cal_batch_num) FROM db_sync_info_flow WHERE db_sync_id = #{dbSyncId} AND cal_batch_num <= #{batchNum}) LIMIT 1")
  DbSyncInfoFlowDO querySyncFlowUpdateTime(@Param("dbSyncId") String dbSyncId, @Param("batchNum") Integer batchNum);

  @Select("SELECT cal_batch_num FROM db_sync_info_flow WHERE db_sync_id = #{dbSyncId} AND cal_batch_num is not null ORDER BY create_time DESC LIMIT 1")
  Integer queryCurrentBatchNum(@Param("dbSyncId") String dbSyncId);

}
