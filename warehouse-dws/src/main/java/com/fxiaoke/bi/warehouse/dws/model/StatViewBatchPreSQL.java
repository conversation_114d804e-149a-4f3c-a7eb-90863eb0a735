package com.fxiaoke.bi.warehouse.dws.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Author: zhaomh
 * @Description:
 * @Date: Created in 2024/10/25
 * @Modified By:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatViewBatchPreSQL {
  //租户id
  private String tenantId;
  /**
   * 预览sql
   */
  private List<StatViewPreSQL> preViewSQLList;
  /**
   * 抽样类型:0:全量，1:抽样
   */
  private Integer sampleType;
  /**
   * 多个规则的最小更新时间
   */
  private long minModifiedTime;
}
