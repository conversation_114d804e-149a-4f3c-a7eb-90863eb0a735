package com.fxiaoke.bi.warehouse.ods.service;

import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.ods.args.ExecutePgSqlArg;
import com.fxiaoke.jdbc.JdbcConnection;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class ExecutePgSqlService {

    @Resource
    private PgDataSource pgDataSource;

    public String executePgSql(ExecutePgSqlArg executePgSqlArg) {
        if (StringUtils.isBlank(executePgSqlArg.getTenantId()) || StringUtils.isBlank(executePgSqlArg.getExecutePgSql())) {
            return "this executePgSqlArg exist null";
        }

        if (!GrayManager.isAllowByRule("executePgSqlGray", executePgSqlArg.getTenantId())) {
            log.warn("this tenant is not allowed to execute, this tenantId is {}, this sql is {}", executePgSqlArg.getTenantId(), executePgSqlArg.getExecutePgSql());
            return "this tenant is not allowed to execute";
        }
        String tenantId = executePgSqlArg.getTenantId();
        String executePgSql = executePgSqlArg.getExecutePgSql();
        try (JdbcConnection jdbcConnection = pgDataSource.getJdbcConnection(tenantId)) {
            jdbcConnection.executeUpdate(executePgSql);
        } catch (Exception e) {
            log.error("executePgSql error this executePgSqlArg is {}", executePgSqlArg, e);
            return "error";
        }
        return "ok";
    }

    /**
     * 转义sql为请求arg,没有经过转义请求解析不了
     */
    public String escapeSql(String sql) {
        return sql.replace("\\", "\\\\").replace("\"", "\\\"");
    }
}
