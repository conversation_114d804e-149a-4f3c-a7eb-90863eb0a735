package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao;

import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BiDataSyncPolicyDo;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper.BiDataSyncPolicyMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Repository
public class BiDataSyncPolicyDao {

    @Resource
    private BiDataSyncPolicyMapper biDataSyncPolicyMapper;

    /**
     * 根据企业tenant_id查询同步策略
     */
    public List<BiDataSyncPolicyDo> queryBiDataSyncPolicyList(String tenantId) {
        try {
            return biDataSyncPolicyMapper.setTenantId(tenantId).getBiDataSyncPolicy(tenantId);
        } catch (Exception e) {
            log.error("queryBiDataSyncPolicyList error, this tenantId is {}", tenantId);
            return Lists.newArrayList();
        }
    }

    /**
     * 根据企业tenant_id查询逻辑删除的同步的同步策略
     */
    public List<BiDataSyncPolicyDo> queryDeleteBiDataSyncPolicyList(String tenantId) {
        try {
            return biDataSyncPolicyMapper.setTenantId(tenantId).getDeletedBiDataSyncPolicy(tenantId);
        } catch (Exception e) {
            log.error("queryDeleteBiDataSyncPolicyList error, this tenantId is {}", tenantId);
            return Lists.newArrayList();
        }
    }

    public BiDataSyncPolicyDo getBiDataSyncPolicyById(String tenantId, String policyId) {
        return biDataSyncPolicyMapper.setTenantId(tenantId).getBiDataSyncPolicyById(tenantId, policyId);
    }

    public int getPolicySizeByTenantId(String tenantId) {
        return biDataSyncPolicyMapper.setTenantId(tenantId).getPolicySizeByTenantId(tenantId);
    }
}
