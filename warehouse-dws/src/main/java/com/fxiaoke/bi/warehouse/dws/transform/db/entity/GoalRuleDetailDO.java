package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "goal_rule_detail")
@Data
public class GoalRuleDetailDO {

  @Column(name="subgoal_value")
  public String subgoalValue;
  @Column(name = "subgoal_comparison")
  public String subgoalComparison;
  @Column(name = "subgoal_fieldtype")
  public String subgoalFieldType; //存paas的字段类型
  @Column(name="subgoal_name")
  public String subgoalName;
  @Column(name="object_describe_api_name")
  public String objectDescribeApiName;
  @Column(name="object_describe_id")
  public String objectDescribeId;
  @Column(name="goal_rule_id")
  public String goalRuleId;
  @Column(name="create_time")
  public String createTime;
  @Column(name="is_deleted")
  public int subIsDeleted;
  @Column(name="last_modified_time")
  public long lastModifiedTime;
  @Column(name="version")
  public String version;
  @Column(name="last_modified_by")
  public String lastModifiedBy;
  @Column(name="created_by")
  public String createdBy;
  @Column(name="package")
  public String packageStr;
  @Column(name="id")
  public String id;
}

