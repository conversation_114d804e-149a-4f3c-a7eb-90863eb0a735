package com.fxiaoke.bi.warehouse.dws.transform.impl;

import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.model.StatViewPreArg;
import com.fxiaoke.bi.warehouse.dws.model.StatViewPreSQL;
import com.fxiaoke.bi.warehouse.dws.transform.TopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.model.TransformContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author:jief
 * @Date:2024/3/7
 */
@Slf4j
@Component
public class WideTableTopologyTransformer extends TopologyTransformer {

  @Override
  public TopologyTableDO transform(TransformContext context) {
    return null;
  }

  @Override
  public void upsertTopologyTable(TransformContext context) {

  }

  @Override
  public StatViewPreSQL createStatViewPreSQL(StatViewPreArg statViewPreArg) {
    return null;
  }
}
