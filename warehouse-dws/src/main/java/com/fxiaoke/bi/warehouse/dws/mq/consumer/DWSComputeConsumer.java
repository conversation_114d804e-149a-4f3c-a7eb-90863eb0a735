package com.fxiaoke.bi.warehouse.dws.mq.consumer;

import com.fxiaoke.bi.warehouse.common.mq.message.DBUpdateMessage;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.dws.service.DWSComputeService;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.mq.CalculateEventProducer;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.base.Charsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * DWS层计算消费者
 * 接收数据库同步完成事件，进行DWS层计算
 */
@Slf4j
@Component
public class DWSComputeConsumer implements MessageListenerConcurrently, ApplicationListener<ContextRefreshedEvent> {
  @Resource
  private DWSComputeService dwsComputeService;
  @Resource
  private CalculateEventProducer calculateEventProducer;
  private AutoConfMQPushConsumer consumer;

  @PostConstruct
  public void init() {
    consumer = new AutoConfMQPushConsumer("fs-bi-warehouse", "DWSConsumer", this);
  }

  @Override
  public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
    try {
      list.forEach(msg -> {
        log.info("cal consumer bornHost:{},topic:{},msgId:{},queue:{},tags:{},msg:{}", msg.getBornHostString(), msg.getTopic(), msg.getMsgId(), msg.getQueueId(), msg.getTags(), new String(msg.getBody(), Charsets.UTF_8));
        DBUpdateMessage dbUpdateMessage = DBUpdateMessage.parseFromMsg(msg);
        String pgSchema = dbUpdateMessage.getSchema();
        String pgDBName = CommonUtils.getDBName(dbUpdateMessage.getPgDB());
        if ((pgSchema.startsWith("sch_") && GrayManager.isAllowByRule("use_gray_topic_ei", pgSchema.substring(4))) || GrayManager.isAllowByRule("use_gray_topic_pgdb", pgDBName)) {
          if(!Objects.equals(msg.getTopic(), CHContext.BI_WAREHOUSE_EVENT_TOPIC_GRAY)){
            msg.setTopic(CHContext.BI_WAREHOUSE_EVENT_TOPIC_GRAY);
            calculateEventProducer.sendMessage(msg,msg.getQueueId());
            log.info("this msg is use gray topic:{},msgId:{},queueId:{},body:{}",msg.getTopic(), msg.getMsgId(), msg.getQueueId(), new String(msg.getBody(), Charsets.UTF_8));
            return;
          }
        }
        dwsComputeService.dbDataUpdated(dbUpdateMessage);
      });
      return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    } catch (Exception e) {
      log.error("DWS dbDataUpdated error", e);
      return ConsumeConcurrentlyStatus.RECONSUME_LATER;
    }
  }

  @PreDestroy
  public void destroy() {
    if (null != consumer) {
      consumer.shutdown();
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (null == event.getApplicationContext().getParent() && consumer != null) {
      consumer.start();
    }
  }
}
