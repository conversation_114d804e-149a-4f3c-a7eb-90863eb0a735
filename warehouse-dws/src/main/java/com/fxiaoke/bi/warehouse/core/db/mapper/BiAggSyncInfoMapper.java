package com.fxiaoke.bi.warehouse.core.db.mapper;

import com.fxiaoke.bi.warehouse.common.provider.CommonProvider;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/4/5
 */
@Mapper
public interface BiAggSyncInfoMapper extends IBatchMapper<BIAggSyncInfoDO>, ITenant<BiAggSyncInfoMapper> {
  @Select("select * from bi_agg_sync_info WHERE id=#{id} and is_deleted=0 limit 1")
  BIAggSyncInfoDO queryBiAggSyncInfo(@Param("id") String id);
  /**
   * 根据企业Id查询下游企业数据同步信息
   */
  @Select("SELECT * FROM bi_agg_sync_info WHERE tenant_id = #{tenantId} LIMIT 1")
  BIAggSyncInfoDO queryAggSyncInfoByEi(@Param("tenantId") String tenantId);
  @Update("UPDATE bi_agg_sync_info set status = #{biAggSyncInfoDO.status} WHERE tenant_id = {biAggSyncInfoDO.tenantId}")
  void updateBiAggSyncInfo(@Param("biAggSyncInfoDO") BIAggSyncInfoDO biAggSyncInfoDO);

  @Select("select * from bi_agg_sync_info where tenant_id=#{tenant_id} and is_deleted=0")
  BIAggSyncInfoDO queryBiAggSyncInfoDB(@Param("tenant_id") String tenantId);

  /**
   * 查询所有做1+N同步的1端同步信息
   * @return
   */
  @Select("select * from bi_agg_sync_info where is_deleted=0")
  List<BIAggSyncInfoDO> queryAllAggSyncInfo();

  /**
   * upsert 系统库
   *
   * @param list
   * @param primaryKey
   * @param <T>
   * @return
   */
  @InsertProvider(type = CommonProvider.class, method = "batchUpsert")
  <T> int upsertSyncDBInfo(@Param(CommonProvider.FKey) List<T> list,
                           @Param(CommonProvider.SKey) Set<String> primaryKey);
}
