package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DimFieldTypeEnum {

    NULL,

    DATE_MONTH("日期-月", "f_month", "", "date"),

    REF("查找关联", "", "", "object_reference");

    private String desc;

    private String dbFieldName;

    private String dbObjName;

    private String type;

    public static DimFieldTypeEnum getDimFieldTypeEnum(DimField dimField) {
        if (DATE_MONTH.type.equals(dimField.getType()) && DATE_MONTH.dbFieldName.equals(dimField.getDbFieldName())) {
            return DATE_MONTH;
        }
        if (REF.type.equals(dimField.getType())) {
            return REF;
        }
        return NULL;
    }

    public static boolean isSupportDimFieldType(DimFieldTypeEnum dimFieldTypeEnum) {
        return dimFieldTypeEnum != NULL;
    }
}
