package com.fxiaoke.bi.warehouse.dws.transform.model;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Author:jief
 * @Date:2023/9/27
 */
@Data
public class FiscalYear {
  // 财年的名字
  String year;
  // 财月设置
  List<FiscalMonth> fiscalMonths;

  /**
   * 生成财年的起始月
   *
   * @return 01
   */
  public int findStartMonth() {
    if (CollectionUtils.isNotEmpty(fiscalMonths)) {
      Long start = fiscalMonths.get(0).begin;
      Timestamp timestamp = new Timestamp(start);
      return timestamp.toLocalDateTime().getMonthValue();
    }
    return 1;
  }

  /**
   * 生成财年的起始月
   *
   * @return 01
   */
  public int findStartYear() {
    if (CollectionUtils.isNotEmpty(fiscalMonths)) {
      Long start = fiscalMonths.get(0).begin;
      Timestamp timestamp = new Timestamp(start);
      return timestamp.toLocalDateTime().getYear();
    }
    return Integer.parseInt(this.year);
  }

  public String startFiscalYearMonth() {
    if (CollectionUtils.isNotEmpty(fiscalMonths)) {
      Long start = fiscalMonths.get(0).begin;
      Timestamp timestamp = new Timestamp(start);
      DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMdd");
      return timestamp.toLocalDateTime().format(format);
    }
    return null;
  }

  /**
   * 根据财月获取财月的起始日期。
   *
   * @param month 1~12
   * @return fiscalMonth
   */
  public FiscalMonth getFiscalMonthByMonth(int month) {
    if (CollectionUtils.isEmpty(this.fiscalMonths)) {
      throw new RuntimeException("fiscalMonths is empty!");
    }
    if (month >= 1 && month <= 12) {
      return fiscalMonths.get(month - 1);
    }
    throw new RuntimeException("month must between 1~12");
  }

  @Data
  public static class FiscalMonth {
    private static final DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMdd");
    // 财月起点，单位毫秒（包含）
    Long begin;
    // 财月终点，单位毫秒（排除）
    Long end;

    //获取begin年
    public int findBeginYear() {
      return new Timestamp(this.begin).toLocalDateTime().getYear();
    }

    //生成action_date yyyyMMdd
    public String findBeginDate() {
      return new Timestamp(this.begin).toLocalDateTime().format(format);
    }

    //获取月
    public int findBeginMonth() {
      return new Timestamp(this.begin).toLocalDateTime().getMonthValue();
    }
  }
}
