package com.fxiaoke.bi.warehouse.ods.entity;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.bean.CHColumn;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.Pair;
import com.fxiaoke.helper.JoinHelper;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;

import java.sql.PreparedStatement;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;

import java.sql.PreparedStatement;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.fxiaoke.bi.warehouse.ods.context.CHContext.SYS_MODIFIED_TIME;

/**
 * <AUTHOR>
 * @since 2022/11/2
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClickhouseTable {
  private static final Pattern dsAggUniqColumnRex =  Pattern.compile("^ds_agg_uniq_[0-9]+");
  public static final CSVFormat formatter = CSVFormat.Builder.create().setDelimiter(',').build();
  private String name;
  private String db;
  private String dbURL;
  private List<ClickhouseColumn> columnList;
  private Map<String, ClickhouseColumn> columnMap;
  private List<String> orderByColumns;
  private List<String> pointColumns;

  /**
   * 判断是否包含存量分区
   * @return
   */
  public boolean existsPartitionKey(){
    return columnMap.containsKey(WarehouseConfig.ODS_PARTITION_KEY);
  }

  public boolean existsSysModifiedTime(){
    return this.columnMap.containsKey(SYS_MODIFIED_TIME);
  }


  public byte[] toCSVHeader() {
    Object[] header = new Object[columnList.size()];
    for (int i = 0; i < columnList.size(); i++) {
      header[i] = columnList.get(i).getName();
    }
    String headerStr = formatter.format(header);
    return headerStr.getBytes(Charsets.UTF_8);
  }

  /**
   * 清洗数据为CSV数据
   * 注意：跳过了最后一个字段，bi_sys_version 为了让CH自动生成
   *
   * @param bizLog    pg数据记录
   * @param etlTarget 清洗后的格式枚举
   * @return
   */
  public byte[] toCSVLine(BizLog bizLog, CHContext.ETLTarget etlTarget) {
    Object[] values = CollectionUtils.isEmpty(this.pointColumns) ?
      this.toValues(bizLog, etlTarget) :
      this.toValuesFromList(bizLog, etlTarget);
    String line = formatter.format(values);
    return line.getBytes(Charsets.UTF_8);
  }

  /**
   * 生成value数组
   *
   * @param bizLog
   * @param etlTarget
   * @return
   */
  public Object[] toValues(BizLog bizLog, CHContext.ETLTarget etlTarget) {
    Object[] values = new Object[columnList.size()];
    for (int i = 0; i < columnList.size(); i++) {
      ClickhouseColumn column = columnList.get(i);
      if (CHContext.BI_SYS_VERSION.equals(column.getName())) {
        values[i] = null;
      } else {
        try {
          Object value = column.getType().format(bizLog.getValue(column.getName()), etlTarget, column.getTypeName());
          values[i] = value;
        } catch (Exception e) {
          log.error("format error column:{} {},bizLog:{}", column.getName(), column.getTypeName(),
            JSON.toJSONString(bizLog), e);
          throw new RuntimeException(e);
        }
      }
    }
    return values;
  }

  /**
   * 生成value数组
   *
   * @param bizLog
   * @param etlTarget
   * @return
   */
  public Object[] toValuesFromList(BizLog bizLog, CHContext.ETLTarget etlTarget) {
    List<Object> values = Lists.newArrayList();
    for (ClickhouseColumn column : columnList) {
      if (CHContext.BI_SYS_VERSION.equals(column.getName())) {
        values.add(null);
      } else {
        try {
          Object value = column.getType().format(bizLog.getValue(column.getName()), etlTarget, column.getTypeName());
          if (value != null && column.getType() == ClickHouseColumnType.POINt && value instanceof Pair<?, ?>) {
            Pair<String, String> point = (Pair<String, String>) value;
            values.add(point.first);
            values.add(point.second);
          } else {
            values.add(value);
          }
        } catch (Exception e) {
          log.error("format error column:{} {},bizLog:{}", column.getName(), column.getTypeName(),
            JSON.toJSONString(bizLog), e);
          throw new RuntimeException(e);
        }
      }
    }
    return values.toArray(new Object[0]);
  }

  /**
   * 按照CSV 格式清洗db列值并存入数组
   *
   * @param columnName
   * @param pgValue
   * @param etlTarget
   * @param values
   */
  public void toCSVFromColumn(String columnName, Object pgValue, CHContext.ETLTarget etlTarget, final Object[] values) {
    ClickhouseColumn column = this.columnMap.get(columnName);
    if (column != null) {
      Object value = column.getType().format(pgValue, etlTarget, column.getTypeName());
      values[column.getOrder()] = value;
    }
  }

  /**
   * 清洗数据为jdbc batch
   *
   * @param bizLog
   * @param etlTarget
   * @return
   */
  public void addBatch(BizLog bizLog, CHContext.ETLTarget etlTarget, PreparedStatement statement) throws Exception {
    for (int i = 0; i < columnList.size(); i++) {
      ClickhouseColumn column = columnList.get(i);
      Object value = column.getType().format(bizLog.getValue(column.getName()), etlTarget, column.getTypeName());
      statement.setObject(i + 1, value);
    }
    statement.addBatch();
  }

  /**
   * 专门给agg_data 表创建的insert sql
   *
   * @return insert sql
   */
  public String createInsertSQL4AggData() {
    String db = CHContext.getDBName(this.dbURL);
    String columns = columnList.stream().map(ClickhouseColumn::getName).collect(Collectors.joining(","));
    String selects = columnList.stream().map(column -> {
      if (dsAggUniqColumnRex.matcher(column.getName()).matches()) {
        return String.format("cast(unhex(%s),'AggregateFunction(uniqExact,Nullable(String))')", column.getName());
      }
      return column.getName();
    }).collect(Collectors.joining(","));
    String input = columnList.stream().map(column -> {
      if (dsAggUniqColumnRex.matcher(column.getName()).matches()) {
        return String.format("%s %s", column.getName(), "String");
      }
      return String.format("%s %s", column.getName(), column.getTypeName());
    }).collect(Collectors.joining(","));
    String sql = "insert into %s.%s(%s) select %s from input('%s')";
    return String.format(sql, db, this.name, columns, selects, input);
  }

  /**
   * 获取order by列
   *
   * @return
   */
  public static List<String> findOrderByColumn(List<ClickhouseColumn> columnList) {
    if (columnList == null) {
      return null;
    }
    return columnList.stream().filter(column -> column.getInSortKey() == 1).map(ClickhouseColumn::getName).toList();
  }

  /**
   * 获取时间过滤字段 减少循环
   *
   * @return
   */
  public Pair<String, String> findTimeFilter() {
    if (MapUtils.isNullOrEmpty(this.columnMap)) {
      return null;
    }
    if (this.columnMap.containsKey(SYS_MODIFIED_TIME)) {
      return Pair.build(SYS_MODIFIED_TIME, columnMap.get(SYS_MODIFIED_TIME).getType().name().toLowerCase());
    } else if (this.columnMap.containsKey(CHContext.LAST_MODIFIED_TIME)) {
      return Pair.build(CHContext.LAST_MODIFIED_TIME,
          columnMap.get(CHContext.LAST_MODIFIED_TIME).getType().name().toLowerCase());
    } else if (this.columnMap.containsKey(CHContext.CREATE_TIME)) {
      return Pair.build(CHContext.CREATE_TIME, columnMap.get(CHContext.CREATE_TIME).getType().name().toLowerCase());
    } else if (this.columnMap.containsKey(CHContext.CREATE_DATE)) {
      return Pair.build(CHContext.CREATE_DATE, columnMap.get(CHContext.CREATE_DATE).getType().name().toLowerCase());
    }
    return null;
  }

  /**
   * 获取时间过滤字段 减少循环
   * @param columns
   * @return
   */
  public static BIPair<String, String> findSysModifiedTimeColumn(String tableName,List<CHColumn> columns) {
    if(Objects.equals(tableName, "object_data") || tableName.endsWith("__c")){
       return BIPair.build(SYS_MODIFIED_TIME, ClickHouseColumnType.LONG.name().toLowerCase());
    }
    Map<String, String> timeColumnMap = columns.stream()
        .filter(column -> (StringUtils.endsWithAny(column.getName(), "time") || "create_date".equals(column.getName())))
        .collect(Collectors.toMap(CHColumn::getName, c -> ClickHouseColumnType.parseFromTypeName(c.getTypeName()).name().toLowerCase()));
    if (MapUtils.isNullOrEmpty(timeColumnMap)) {
      return null;
    }
    if (timeColumnMap.containsKey(SYS_MODIFIED_TIME)) {
      return BIPair.build(SYS_MODIFIED_TIME, timeColumnMap.get(SYS_MODIFIED_TIME));
    } else if (timeColumnMap.containsKey(CHContext.LAST_MODIFIED_TIME)) {
      return BIPair.build(CHContext.LAST_MODIFIED_TIME, timeColumnMap.get(CHContext.LAST_MODIFIED_TIME));
    } else if (timeColumnMap.containsKey(CHContext.CREATE_TIME)) {
      return BIPair.build(CHContext.CREATE_TIME, timeColumnMap.get(CHContext.CREATE_TIME));
    } else if (timeColumnMap.containsKey(CHContext.CREATE_DATE)) {
      return BIPair.build(CHContext.CREATE_DATE, timeColumnMap.get(CHContext.CREATE_DATE));
    }
    return null;
  }

  /**
   * 查找EI 字段的名称
   *
   * @return Optional<ClickhouseColumn>
   */
  public Optional<String> findEiColumn() {
    return this.columnList.stream()
                          .map(ClickhouseColumn::getName)
                          .filter(columnName -> StringUtils.equalsAny(columnName, "tenant_id", "ei"))
                          .findFirst();
  }

  /**
   * 创建查询sql
   *
   * @param columns      指定列字段
   * @param tenantId     租户集合
   * @param sysTimeRange 采用左开右闭(a,b]
   * @return
   */
  public String createBizDataQuerySQL(List<String> columns, List<String> tenantId, Pair<Long, Long> sysTimeRange) {
    StringBuilder wheres = new StringBuilder();
    Optional<String> eiColumnOP = this.findEiColumn();
    if (tenantId.size() == 1) {
      wheres.append(eiColumnOP.orElse("tenant_id")).append(" = ").append("'").append(tenantId.get(0)).append("'");
    } else if (tenantId.size() > 1) {
      String eiSQL = tenantId.stream().map(ei -> "'" + ei + "'").collect(Collectors.joining(","));
      wheres.append(eiColumnOP.orElse("tenant_id")).append(" in ").append("(").append(eiSQL).append(")");
    }
    if (sysTimeRange != null) {
      Pair<String, String> timeColumnType = this.findTimeFilter();
      if (timeColumnType == null) {
        throw new RuntimeException(MessageFormat.format("can not find time field for filter table:{0}", name));
      }
      if (sysTimeRange.first != null) {
        wheres.append(" ").append("AND").append(" ");
        wheres.append(timeColumnType.first).append(" > ");
        if ("string".equals(timeColumnType.second)) {
          wheres.append("'").append(sysTimeRange.first).append("'");
        } else {
          wheres.append(sysTimeRange.first);
        }
      }
      wheres.append(" ").append("AND").append(" ");
      wheres.append(timeColumnType.first).append(" <= ");
      if ("string".equals(timeColumnType.second)) {
        wheres.append("'").append(sysTimeRange.second).append("'");
      } else {
        wheres.append(sysTimeRange.second);
      }
    }
    //增加bi_sys_flag 过滤
    wheres.append(" ").append("AND").append(" ");
    wheres.append("bi_sys_flag=1");
    String cols = " * ";
    if (CollectionUtils.isNotEmpty(columns)) {
      cols = " " + String.join(",", columns) + " ";
    }
    SQL sql = new SQL();
    sql.SELECT(cols).FROM(this.db + "." + this.name).WHERE(wheres.toString());
    return sql.toString();
  }
  /**
   * 查找某个批次变更的租户集合
   * @param tenantIds 租户集合
   * @return
   */
  public String queryEisByBatchNum(List<String> tenantIds, long batchNum) {
    StringBuilder wheres = new StringBuilder();
    Optional<String> eiColumnOP = this.findEiColumn();
    if (tenantIds.size() == 1) {
      wheres.append(eiColumnOP.orElse("tenant_id")).append(" = ").append("'").append(tenantIds.get(0)).append("'");
    } else if (tenantIds.size() > 1) {
      String eiSQL = tenantIds.stream().map(ei -> "'" + ei + "'").collect(Collectors.joining(","));
      wheres.append(eiColumnOP.orElse("tenant_id")).append(" in ").append("(").append(eiSQL).append(")");
    }
    //增加bi_sys_flag 过滤
    wheres.append(" ").append("AND").append(" ");
    wheres.append(String.format("bi_sys_batch_id=%d", batchNum));
    SQL sql = new SQL();
    sql.SELECT_DISTINCT(eiColumnOP.orElse("tenant_id")).FROM(this.db + "." + this.name).WHERE(wheres.toString());
    return sql.toString();
  }
  /**
   * 创建获取最大变更时间的
   *
   * @return
   */
  public String createMaxModifiedTimeSQL(Long fromValue) {
    Pair<String, String> timeField = this.findTimeFilter();
    String querySQL = String.format("SELECT max(`%s`) AS max_sys_modified_time FROM `%s`.`%s` ", timeField.first, this.db, this.name);
    StringBuilder filterSB = new StringBuilder(" WHERE ");
    if (fromValue != null) {
      if (timeField.second.contains("Int") || timeField.second.contains("Decimal")) {
        filterSB.append(String.format(" `%s` > %s", timeField.first, fromValue));
      } else {
        filterSB.append(String.format(" `%s` > '%s'", timeField.first, fromValue));
      }
      querySQL += filterSB.toString();
    }
    return querySQL;
  }


  public ClickhouseTable createAggClickhouseTable() {
    return null;
  }

  public String buildFullSyncSQL() {
    return null;
  }

  public String buildIncrementSyncSQL() {
    return null;
  }

  /**
   * 计算分区值
   * @param defaultPartValue 默认的分区值
   * @return
   */
  public String computePartitionValue(String defaultPartValue) {
    return GrayManager.isAllowByRule("use_ch_ods_partition", this.db) && this.existsPartitionKey() ? defaultPartValue : null;
  }


  /**
   * 创建获取最大变更时间的
   *
   * @return String
   */
  public String createQueryApiAndEIsSQL(long fromValue, long toValue, long[] batchNums, String eiColumnName,List<String> currentIds) {
    if (!this.existsPartitionKey()) {
      log.warn("{} not exists partition key, so can not create partition sql", this.name);
      return null;
    }
    if (eiColumnName == null) {
      eiColumnName = "tenant_id";
    }
    String groupBy = String.format(" GROUP BY `%s`", eiColumnName);
    String apiNameSelect = String.format("'%s' AS %s", this.name, CHContext.OBJECT_DESCRIBE_API_NAME);
    if (StringUtils.equalsAnyIgnoreCase(this.name, CHContext.OBJECT_DATA, CHContext.OBJECT_DATA_LANG, CHContext.BIZ_ACCOUNT,CHContext.GOAL_VALUE)) {
      apiNameSelect = String.format("`%s` AS %s", CHContext.OBJECT_DESCRIBE_API_NAME, CHContext.OBJECT_DESCRIBE_API_NAME);
      groupBy = String.format(" GROUP BY `%s`,`%s`", eiColumnName, CHContext.OBJECT_DESCRIBE_API_NAME);
    }
    Pair<String, String> timeField = this.findTimeFilter();
    if (timeField == null) {
      log.warn("{} timeField is empty", this.name);
      return null;
    }
    String querySQL = String.format("SELECT %s ,`%s`AS tenant_id FROM `%s`.`%s` PREWHERE bi_sys_ods_part in('i') ", apiNameSelect, eiColumnName, this.db, this.name);
    StringBuilder filterSB = new StringBuilder(" WHERE ");
    if (currentIds != null) {
      filterSB.append(String.format(" `%s` in (%s) ", eiColumnName, JoinHelper.joinSkipNullOrBlank(",","'", currentIds)));
    }
    if (timeField.second.contains("long") || timeField.second.contains("int") || timeField.second.contains("decimal")) {
      filterSB.append(" AND ").append(String.format(" `%s` > %s AND `%s` <= %s ", timeField.first, fromValue, timeField.first, toValue));
    } else {
      filterSB.append(" AND ").append(String.format(" `%s` > '%s' AND `%s` <= '%s' ", timeField.first, fromValue, timeField.first, toValue));
    }
    if (batchNums != null) {
      filterSB.append(" AND `bi_sys_batch_id` in (")
              .append(Arrays.stream(batchNums).boxed().map(String::valueOf).collect(Collectors.joining(",")))
              .append(")");
    }
    querySQL += filterSB.toString();
    querySQL += groupBy;
    return querySQL;
  }


  /**
   * 创建比较CH的sql
   * @param tenantId 租户id
   * @param idColumn 主键列
   * @param fromid 开始id
   * @param limit 限制数量
   * @param batchId 批次id
   */
  public String createCompareCHSQL(String tenantId,String idColumn,String apiName,String fromid,int limit,long batchId){
    String comparesql="SELECT %s FROM %s.%s % WHERE %s limit %d";
    String preWahere="";
    if(this.existsPartitionKey()){
      preWahere=" PREWHERE "+WarehouseConfig.ODS_PARTITION_KEY+" = 's'";
    }
    StringBuilder whereSQL=new StringBuilder();
    whereSQL.append(this.findEiColumn().orElse("tenant_id")).append("='").append(tenantId).append("' AND ");
    if(StringUtils.isNotBlank(apiName)){
      whereSQL.append(CHContext.OBJECT_DESCRIBE_API_NAME).append("='").append(apiName).append("' AND ");
    }
    whereSQL.append(CHContext.BI_SYS_FLAG).append("=1 AND ");
    whereSQL.append(idColumn).append(">").append("'").append(fromid).append("'").append(" AND ");
    whereSQL.append("is_deleted=0 AND ");
    whereSQL.append(CHContext.BI_SYS_BATCH_ID).append(" > ").append(batchId);
    whereSQL.append(" ORDER BY ").append(idColumn);
    return String.format(comparesql,idColumn,this.db,this.name,preWahere,whereSQL,limit);
  }
}
