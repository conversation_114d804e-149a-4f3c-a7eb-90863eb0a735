package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import lombok.Data;

import java.util.List;

@Data
public class TopologyTableAggDownStream {

    private List<DownStreamInfo> downStreamInfoList;

    private List<BIApiName> biApiNameList;

    @Data
    public static class DownStreamInfo {
        /**
         * 图表id
         */
        private String viewId;


        private List<DownStreamField> downStreamFieldList;
    }

    @Data
    public static class DownStreamField {
        /**
         * 字段field_id
         */
        private String fieldId;

        /**
         * 字段名字
         */
        private String bdFieldName;

        /**
         * -1停用 0新增 1增量
         */
        private int status;
    }

    @Data
    public static class BIApiName {
        /**
         * 需要同步是业务表
         */
        private String biApiName;

        /**
         * -1停用 0新增 1新增
         */
        private int status;
    }

}
