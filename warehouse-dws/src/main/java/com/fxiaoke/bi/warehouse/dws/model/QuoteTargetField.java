package com.fxiaoke.bi.warehouse.dws.model;

import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:jief
 * @Date:2023/7/31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuoteTargetField {
  /**
   * 关联属性
   */
  public String column;
  /**
   * 被引用列的数据库列类型
   */
  public PGColumnType columnType;
  /**
   * 被引用列的元数据字段类型
   */
  public String fieldType;
  /**
   * 是否低多值
   */
  public boolean isSingle;
  /**
   * 被应用列的日期格式化
   */
  public String formatStr;
}
