package com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper;

import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2023/9/7
 */
@Repository
public interface GoalValueMapper extends ITenant<GoalValueMapper> {

  /**
   * 批量查询目标值
   *
   * @param tenantId
   * @param sysModifiedTime
   * @param limit
   * @return
   */
  @Select("select id,name,check_object_id,goal_type,is_deleted,check_dimension_fields,check_dimension_value1," +
    "check_dimension_value2,check_dimension_value3,check_dimension_value4,check_dimension_value5," +
    "check_dimension_value6,check_dimension_value7," +
    " check_dimension_value8,check_dimension_value9,check_dimension_value10 from goal_rule where " +
    "tenant_id=#{tenantId} and " +
    "goal_rule_id=#{goalRuleId} and " +
    "sys_modified_time>#{sysModifiedTime} order by sys_modified_time limit #{limit}")
  List<Map<String, Object>> batchQueryGoalValues(@Param("tenantId") String tenantId,
                                                 @Param("goalRuleId") String goalRuleId,
                                                 @Param("sysModifiedTime") long sysModifiedTime,
                                                 @Param("limit") int limit);
}
