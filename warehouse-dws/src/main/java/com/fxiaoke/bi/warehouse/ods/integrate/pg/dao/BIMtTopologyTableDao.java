package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao;

import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.TopologyTableIntegrateDO;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper.BIMtTopologyTableMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author:jief
 * @Date:2024/4/19
 */
@Slf4j
@Repository
public class BIMtTopologyTableDao {
  @Autowired
  BIMtTopologyTableMapper biMtTopologyTableMapper;

  /**
   * 获取素有downstream tables
   *
   * @param tenantId 租户id
   * @return
   */
  public List<TopologyTableIntegrateDO> queryAllDownstreamTopologyTables(String tenantId) {
    return biMtTopologyTableMapper.setTenantId(tenantId).queryIntegrateFromTopology(tenantId);
  }

  /**
   * @param tenantId
   * @param sourceIds
   * @return
   */
  public List<TopologyTableIntegrateDO> batchQueryFieldLocationByViewId(String tenantId, List<String> sourceIds) {
    return biMtTopologyTableMapper.setTenantId(tenantId)
                                  .queryFieldLocationFromTopology(tenantId, sourceIds.toArray(String[]::new));
  }

  public List<TopologyTableIntegrateDO> batchQuerySourceTenantTopology(String tenantId, List<String> sourceIds) {
    return biMtTopologyTableMapper.setTenantId(tenantId).querySourceTenantTopology(tenantId, sourceIds.toArray(String[]::new));
  }

  public TopologyTableDO queryTopologyTable(String tenantId, String viewId) {
    return biMtTopologyTableMapper.setTenantId(tenantId).queryTopologyTable(tenantId, viewId);
  }
}
