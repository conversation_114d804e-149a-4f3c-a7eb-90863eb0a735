package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.common.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BiMtMeasureRule {
  private String measureId;
  private String tenantId;
  private String name;
  private String topologyDescribeId;
  private String aggExpression;
  private String aggType;
  private String fieldLocation;
  private String filters;
  private Integer status;
  private String description;
  private long lastModifiedTime;
  private boolean isDeleted;
  private String syslog;
  private String nodeId;
  private String actionDateField;


  /**
   * 获取要聚合的字段 SUM($base_order_amount$)
   *
   * @return 字段名称 eg:base_order_amount
   */
  public String findValueField() {
    if (StringUtils.isBlank(this.aggExpression) || StringUtils.isBlank(this.aggType)) {
      return null;
    }
    String regex = this.aggType + "\\(([^)]+)\\)";
    Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
    Matcher matcher = pattern.matcher(this.aggExpression);
    if (matcher.find()) {
      String parameter = matcher.group(1);
      return parameter.replaceAll("\\$", "");
    }
    return null;
  }

  /**
   * 获取nodeId 和日期字段
   *
   * @return nodeId, dbFieldName
   */
  public Pair<String, String> findActionDateFieldPair() {
    String nodeId = this.nodeId;
    if (actionDateField.contains(".")) {
      nodeId = this.actionDateField.split("\\.")[0];
      String dbFieldName = this.actionDateField.split("\\.")[1];
      return Pair.build(nodeId, dbFieldName);
    }
    return Pair.build(nodeId, this.actionDateField);
  }

  /**
   * 获取nodeId 和日期字段
   *
   * @return nodeId, dbFieldName
   */
  public static Pair<String, String> createActionDateFieldPair(Pair<String/*rootNodeId*/, String/*actionDateField*/> measureActionDate) {
    String nodeId = measureActionDate.first;
    if (measureActionDate.second.contains(".")) {
      nodeId = measureActionDate.second.split("\\.")[0];
      String dbFieldName = measureActionDate.second.split("\\.")[1];
      return Pair.build(nodeId, dbFieldName);
    }
    return Pair.build(nodeId, measureActionDate.second);
  }
}
