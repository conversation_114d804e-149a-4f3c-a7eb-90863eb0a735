package com.fxiaoke.bi.warehouse.core.db;

import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * @Author:jief
 * @Date:2023/7/13
 */
@Slf4j
@Service
public class PgDataSource {
  @Resource(name = "mybatisTenantPolicy")
  private MybatisTenantPolicy mybatisTenantPolicy;
  private String userName;
  private String passWord;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-statistic-db-online", this::changed);
  }

  private void changed(IConfig config) {
    this.userName = config.get("username");
    try {
      this.passWord = PasswordUtil.decode(config.get("password"));
    } catch (Exception e) {
      log.error("decode passWord error", e);
    }
  }

  public JdbcConnection getJdbcConnection(String jdbcURL, boolean master) {
    //todo 这个地方转pgbouncer有漏洞
    String pgbouncerURL = mybatisTenantPolicy.getPgbouncerURL(jdbcURL, master);
    return new JdbcConnection(pgbouncerURL, userName, passWord);
  }

  public JdbcConnection getJdbcConnection(String jdbcURL, String schemaName) {
    String pgbouncerURL;
    if (schemaName.startsWith("sch_")) {
      pgbouncerURL = mybatisTenantPolicy.getPgBouncerJdbcURL(schemaName.substring(4), false);
    } else {
      pgbouncerURL = mybatisTenantPolicy.getPgbouncerURL(jdbcURL, true);
    }
    return new JdbcConnection(pgbouncerURL, userName, passWord);
  }

  public JdbcConnection getJdbcConnection(String tenantId) {
    String jdbcURL = mybatisTenantPolicy.getPgBouncerJdbcURL(tenantId, false);
    if (StringUtils.isEmpty(jdbcURL)) {
      return null;
    }
    return new JdbcConnection(jdbcURL, this.userName, this.passWord);
  }

  /**
   * 判断是否是schema隔离企业
   * @param tenantId 租户id
   * @return
   */
  public boolean standalone(String tenantId){
   return mybatisTenantPolicy.standalone(tenantId);
  }
  /**
   * 根据企业id
   * @param tenantId
   * @return
   */
  public String getPgBouncerJdbcUrlByTenantId(String tenantId){
    String jdbcURL = mybatisTenantPolicy.getPgBouncerJdbcURL(tenantId);
    if (StringUtils.isEmpty(jdbcURL)) {
      return null;
    }
    return jdbcURL;
  }

  /**
   * <p>判断某个企业是否做了schema隔离</p>
   *
   * @param tenantId
   * @return
   */
  public boolean isStandalone(String tenantId) {
    return mybatisTenantPolicy.standalone(tenantId);
  }

  /**
   * 获取pgbouncer jdbc 连接
   * @param jdbcURL pgbouncer url
   * @return JdbcConnection
   */
  public JdbcConnection getConnectionByPgbouncerURL(String jdbcURL){
    JdbcConnection jdbcConnection = null;
    try {
      String pgBouncerURL = mybatisTenantPolicy.getPgBouncerURLByURL(jdbcURL,true);
      jdbcConnection = new JdbcConnection(pgBouncerURL, this.userName, this.passWord);
    } catch (Exception e) {
      log.error("get connection error.", e);
    }
    return jdbcConnection;
  }

  /**
   * 获取pgbouncer jdbc 连接
   * @param jdbcURL pgbouncer url
   * @return JdbcConnection
   */
  public JdbcConnection getConnectionByPgbouncerURL(String jdbcURL, String schemaName) {
    JdbcConnection jdbcConnection = null;
    try {
      String pgBouncerURL;
      if (schemaName.startsWith("sch_")) {
        pgBouncerURL = mybatisTenantPolicy.getPgBouncerJdbcURL(schemaName.substring(4));
      } else {
        pgBouncerURL = mybatisTenantPolicy.getPgBouncerURLByURL(jdbcURL, true);
      }
      jdbcConnection = new JdbcConnection(pgBouncerURL, this.userName, this.passWord);
    } catch (Exception e) {
      log.error("get connection error.", e);
    }
    return jdbcConnection;
  }

  /**
   * <p>获取router info</p>
   *
   * @param tenantId
   * @return
   */
  public RouterInfo getRouterInfo(String tenantId) {
    return mybatisTenantPolicy.getRouterInfo(tenantId);
  }
}
