package com.fxiaoke.bi.warehouse.dws.service;

import com.fxiaoke.bi.warehouse.dws.transform.model.BiApi;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@Service
public class MappingService {

  /**
   * 同步表的白名单
   */
  /**
   * 预置对象，元数据对象跟bi对象的对应关系
   */
  private Map<String, BiApi> paasApi2BiApi;
  /**
   * 预置对象，预置表跟bi对象的对应关系
   */
  private Map<String, BiApi> table2BiApi;
  /**
   * 扩展对象名称对应bi描述
   */
  private Map<String, BiApi> udfApiName2BiApi;

  private static final List<BiApi> pres = Lists.newArrayList();

  private Set<String> biTables = Sets.newHashSet();

  static {
    pres.add(BiApi.builder()
                  .paasApiName("DepartmentObj")
                  .table("org_dept")
                  .biApiName("org_dept")
                  .biApiXName("org_dept_udef")
                  .build());
    pres.add(BiApi.builder()
                  .paasApiName("BpmInstance")
                  .table("bpm_instance")
                  .biApiName("bpm_instance")
                  .biApiXName("bpm_instance_udef")
                  .build());
    //客户主数据
    pres.add(BiApi.builder()
                  .paasApiName("AccountMainDataObj")
                  .table("biz_account_main_data")
                  .biApiName("biz_account_main_data")
                  .biApiXName("biz_account_main_data_udef")
                  .build());
    //流程阶段--商机
    pres.add(BiApi.builder()
                  .paasApiName("StageRuntimeNewOpportunityObj")
                  .table("stage_runtime_new_opportunity")
                  .biApiName("stage_runtime_new_opportunity")
                  .biApiXName("stage_runtime_new_opportunity_udef")
                  .build());
    //流程阶段任务商机
    pres.add(BiApi.builder()
            .paasApiName("StageRuntimeTaskNewOpportunityObj")
            .table("stage_runtime_task_new_opportunity")
            .biApiName("stage_runtime_task_new_opportunity")
            .biApiXName("stage_runtime_task_new_opportunity_udef")
            .build());
    pres.add(BiApi.builder()
                  .paasApiName("BpmTask")
                  .table("bpm_task")
                  .biApiName("bpm_task")
                  .biApiXName("bpm_task_udef")
                  .build());
    pres.add(BiApi.builder()
                  .paasApiName("EmployeeBehaviorAnalysisObj")
                  .table("biz_user_api_name_operation")
                  .biApiName("biz_user_api_name_operation")
                  .biApiXName("biz_user_api_name_operation_udef")
                  .build());
    pres.add(BiApi.builder()
                  .paasApiName("EmployeeLoginAnalysisObj")
                  .table("biz_user_login_online_operation")
                  .biApiName("biz_user_login_online_operation")
                  .biApiXName("biz_user_login_online_operation_udef")
                  .build());
    pres.add(BiApi.builder()
                  .paasApiName("EmployeeBIBehaviorAnalysisObj")
                  .table("biz_user_bi_operation")
                  .biApiName("biz_user_bi_operation")
                  .biApiXName("biz_user_bi_operation_udef")
                  .build());
    pres.add(BiApi.builder()
                  .paasApiName("event_data_click")
                  .table("event_data_click_with_dim")
                  .biApiName("event_data_click_with_dim")
                  .biApiXName("event_data_click_with_dim_udef")
                  .build());
    pres.add(BiApi.builder()
            .paasApiName("EventDataClickObj")
            .table("event_data_click")
            .biApiName("event_data_click")
            .biApiXName("event_data_click_udef")
            .build());
  }

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("paas-bi-mapping", c -> {
      Map<String, String> all = c.getAll();
      Map<String, BiApi> newPaasApi2BiApi = Maps.newHashMap();
      Map<String, BiApi> newTable2BiApi = Maps.newHashMap();
      Map<String, BiApi> newUdfApiName2BiApi = Maps.newHashMap();
      all.forEach((k, v) -> {
        if (k.startsWith("paas.api.")) {
          String paasApi = k.substring(9);
          //paasApiName, table,biApiName,biApiXName, biDescribeId
          List<String> item = Splitter.on(CharMatcher.anyOf(", |")).trimResults().omitEmptyStrings().splitToList(v);
          if (item.size() == 5) {
            BiApi biApi = new BiApi(item.get(0), item.get(1), item.get(2), item.get(3), item.get(4));
            newPaasApi2BiApi.put(biApi.getPaasApiName(), biApi);
            newTable2BiApi.put(biApi.getTable(), biApi);
            newUdfApiName2BiApi.put(biApi.getBiApiXName(), biApi);
          }
        }
      });
      for (BiApi pre : pres) {
        newPaasApi2BiApi.put(pre.getPaasApiName(), pre);
        newTable2BiApi.put(pre.getTable(), pre);
        newUdfApiName2BiApi.put(pre.getBiApiXName(), pre);
      }
      paasApi2BiApi = newPaasApi2BiApi;
      table2BiApi = newTable2BiApi;
      udfApiName2BiApi = newUdfApiName2BiApi;
    });

    ConfigFactory.getConfig("bi-statictic-agg", new IniChangeListener("agg_data2_refactor") {
      @Override
      public void iniChanged(IniConfig iniConfig) {
        biTables = Sets.newHashSet(Splitter.on(CharMatcher.anyOf(", |"))
                                           .trimResults()
                                           .omitEmptyStrings()
                                           .splitToList(iniConfig.get("bi.agg.tables", "")));
      }
    });
  }

  public Collection<BiApi> findAllBiApi() {
    return paasApi2BiApi.values();
  }


  public BiApi findBiApiByPaasApi(String paasApiName) {
    return paasApi2BiApi.get(paasApiName);
  }

  public BiApi findBiApiByTable(String table) {
    return table2BiApi.get(table);
  }


  public String getApiXName(String apiName) {
    BiApi biApi = table2BiApi.get(apiName);
    if (biApi == null) {
      return apiName;
    }
    return biApi.getBiApiXName();
  }

  public String biApiName(String apiName) {
    if (apiName.endsWith("__c")) {
      return apiName;
    }
    BiApi biApi = paasApi2BiApi.get(apiName);
    return biApi == null ? apiName : biApi.getBiApiName();
  }

  public String paasApiName(String apiName) {
    if (apiName.endsWith("__c")) {
      return apiName;
    }
    if (table2BiApi.containsKey(apiName)) {
      return table2BiApi.get(apiName).getPaasApiName();
    }
    return apiName;
  }

  public boolean isPreObjTable(String table) {
    return table2BiApi.keySet().contains(table);
  }

  public String udfApiName2ApiName(String udfApiName) {
    BiApi biApi = udfApiName2BiApi.get(udfApiName);
    return null != biApi ? biApi.getBiApiName() : udfApiName;
  }

  public boolean isBiTable(String table) {
    return biTables.contains(table);
  }
}
