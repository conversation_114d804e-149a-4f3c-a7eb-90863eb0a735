package com.fxiaoke.bi.warehouse.ods.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.bean.*;
import com.fxiaoke.bi.warehouse.common.component.RpcPaasService;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.SQLUtil;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.ods.args.CHPublicCreatorArg;
import com.fxiaoke.bi.warehouse.ods.bean.CHSystemTable;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.ClickHouseColumnType;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseColumn;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.helper.CollectionHelper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CHMetadataService {
  @Resource
  private CHDataSource chDataSource;
  @Resource
  private CHRouterPolicy chRouterPolicy;
  @Resource
  private RpcPaasService rpcPaasService;
  private static final String queryTableSQL = "select name from system.tables where database='%s' and name='%s'";
  private static final String queryAllTableSQL = "select name from system.tables where database='%s'%s";
  private static final String queryAllTableSQL2 = "select name,engine_full from system.tables where database='%s'%s";
  private static final String tableQuantileSql = "SELECT quantiles(%s)(hash_code) AS quantilesNums FROM %s WHERE %s";
  //查询数据库db是否存在
  private static final String queryDataBase = "select count(name) from system.databases where name='%s'";

  private static final String tableSizeSQL =
    "select name,total_rows from system.tables where database='%s' and name " + "%s ";

  private static final String queryTableColumnSQL = """
    select t.name table_name from system.tables t left join system.columns c
on t.database=c.database and t.name=c.table where t.database='%s' and c.database='%s'
and t.engine not in('View','MaterializedView') and c.name='sys_modified_time' and c.type ='Int64'
and default_expression <>'toUnixTimestamp64Micro(now64(9))'
    """;
  private static final String  modifyColumnDefault="ALTER TABLE %s.%s MODIFY COLUMN if exists sys_modified_time Int64 DEFAULT toUnixTimestamp64Micro(now64(9))";
  /**
   * 反查列名和类型
   */
  private static final String tableColumnSQL =
    "select name,type,position,is_in_sorting_key,default_kind,default_expression from system.columns where " +
      "database='%s' and" + " table='%s' order by position";

  private final Cache<String, Set<String>> chTables = Caffeine.newBuilder()
                                                              .maximumSize(1000)
                                                              .expireAfterWrite(30, TimeUnit.MINUTES)
                                                              .build();
  private final Cache<String, Optional<ClickhouseTable>> chTableSchema = Caffeine.newBuilder()
                                                                                 .maximumSize(10000)
                                                                                 .expireAfterWrite(1, TimeUnit.HOURS)
                                                                                 .build();

   private final Cache<String, Optional<BIPair<String,String>>> chTableFilterTimeColumn = Caffeine.newBuilder()
                                                                                 .maximumSize(3000)
                                                                                 .expireAfterWrite(10, TimeUnit.HOURS)
                                                                                 .build();                                                                               

  public String getSysTimeZone(String tenantId) throws Exception {
    String getTimeZone = "SELECT timezoneOf(now())";
    JdbcConnection jdbcConnection = chDataSource.getJdbcConnectionByTenantId(tenantId);
    if (jdbcConnection == null) {
      return "Asia/Shanghai";
    }
    StringBuilder tzSB = new StringBuilder();
    jdbcConnection.query(getTimeZone, rs -> {
      while (rs.next()) {
        tzSB.append(rs.getString(1));
      }
    });
    return !tzSB.isEmpty() ? tzSB.toString() : null;
  }
  /**
   * 根据企业id获取ch连接url
   */
  public String getCHJdbcURL(String tenantId) {
    return chRouterPolicy.getCHJdbcURL(tenantId);
  }

  /**
   * 查询ch中表的大小
   *
   * @param tableNames
   * @return
   */
  public Map<String, Long> queryTableSize(String chDbURL, List<String> tableNames) {
    Map<String, Long> result = Maps.newHashMap();
    String inSql = SQLUtil.generateInExpress(tableNames);
    String dbName = CHContext.getDBName(chDbURL);
    String sql = String.format(tableSizeSQL, dbName, inSql);
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbURL)) {
      jdbcConnection.query(sql, rs -> {
        while (rs.next()) {
          result.put(rs.getString("name"), rs.getLong("total_rows"));
        }
      });
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
    return result;
  }

  /**
   * 查询表分位数
   *
   * @param quantileList 为空集合时说明数据量小于 10000000 此时直接返回 0，hash_code > 0
   * @return quantileList 为空时返回 List.of("0")
   */
  public List<String> queryTableQuantile(String chDbURL,
                                         List<BigDecimal> quantileList,
                                         String tableName,
                                         String whereSql) {
    List<String> result = new ArrayList<>();
    result.add("0");
    if (CollectionUtils.isEmpty(quantileList)) {
      return result;
    }
    String quantileSql = tableQuantileSql(quantileList, tableName, whereSql);
    log.info("queryTableQuantile sql:{}, chDbURL:{}", quantileSql, chDbURL);
    JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbURL);
    double[] quantilesNums = CHContext.queryOne(quantileSql, jdbcConnection, double[].class);
    if (Objects.isNull(quantilesNums) ||
      (quantilesNums.length == 1 && (Double.isNaN(quantilesNums[0]) || Double.isInfinite(quantilesNums[0])))) {
      return result;
    }
    for (double num : quantilesNums) {
      BigDecimal bigDecimal = new BigDecimal(num);
      result.add(Long.toUnsignedString(bigDecimal.toBigInteger().longValue()));
    }
    return result;
  }

  /**
   * 查询表分位数
   */
  public String tableQuantileSql(List<BigDecimal> quantileList, String tableName, String whereSql) {
    String replace = quantileList.stream().map(BigDecimal::toString).collect(Collectors.joining(","));
    return String.format(tableQuantileSql, replace, tableName, whereSql);
  }

  /**
   * 创建 ch db
   *
   * @param chURL   ch url
   * @param engine  db引擎
   * @param comment 注释
   */
  public void createDatabase(String chURL, String engine, String comment) {
    String customEngine = StringUtils.isBlank(engine) ? "Atomic" : engine;
    String customComment = StringUtils.isBlank(comment) ? "" : comment;
    String createDB = "CREATE DATABASE IF NOT EXISTS %s ENGINE = %s COMMENT '%s'";
    String defaultURL = chURL.substring(0, chURL.lastIndexOf("/"));
    String dbName = CHContext.getDBName(chURL);
    log.info("createDatabase url:{},db:{}", defaultURL, dbName);
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(defaultURL)) {
      jdbcConnection.executeUpdate(String.format(createDB, dbName, customEngine, customComment));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 检测chDB是否存在
   *
   * @param chURL ch url ip:port/db
   * @return Boolean
   */
  public boolean checkCHDBExists(String chURL) {
    AtomicBoolean exists = new AtomicBoolean(false);
    String defaultURL = chURL.substring(0, chURL.lastIndexOf("/"));
    String dbName = CHContext.getDBName(chURL);
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(defaultURL)) {
      jdbcConnection.query(String.format(queryDataBase, dbName), rs -> {
        if (rs.next()) {
          exists.set(rs.getInt(1) > 0);
        }
      });
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    return exists.get();
  }

  /**
   * 作废ch table缓存
   *
   * @param chDbUrl ch jdbcUrl
   * @param table   表名称
   */
  public void invalidateCHTableCache(String chDbUrl, String table) {
    chTableSchema.invalidate(chDbUrl + "^" + table);
  }

  /**
   *
   * @param chDbUrl
   */
  public void invalidateCHTablesCache(String chDbUrl){
    if(StringUtils.isBlank(chDbUrl)){
      chTables.cleanUp();
    }else{
      chTables.invalidate(chDbUrl);
    }
  }

  /**
   * 加载table 元数据信息
   *
   * @param pgSchema 这个表所在pg的schema名称
   * @param chDbURL  chDbURL
   * @param table    table
   * @return
   */
  public Optional<ClickhouseTable> loadTable(String pgSchema, String chDbURL, String table) {
    if (Objects.equals(pgSchema, "public")) {
      return chTableSchema.get(chDbURL + "^" + table, key -> loadTableFromDB(chDbURL, table));
    }
    return loadTableFromDB(chDbURL, table);
  }

  /**
   * 获取ch表结构
   *
   * @param chDbURL chUrl
   * @param table   表名称
   * @return
   */
  public Optional<ClickhouseTable> loadTableFromDB(String chDbURL, String table) {
    String db = CHContext.getDBName(chDbURL);
    List<ClickhouseColumn> clickhouseColumns = this.loadColumnsPlus(chDbURL, db, table);
    if (CollectionUtils.isEmpty(clickhouseColumns)) {
      return Optional.empty();
    }
    Map<String, ClickhouseColumn> columnMap = Maps.newHashMap();
    List<String> pointColumns = Lists.newArrayList();
    for (int i = 0; i < clickhouseColumns.size(); i++) {
      ClickhouseColumn column = clickhouseColumns.get(i);
      column.setOrder(i);
      columnMap.put(column.getName(), column);
      if(column.getType()==ClickHouseColumnType.POINt){
        pointColumns.add(column.getName());
      }
    }
    return Optional.of(ClickhouseTable.builder()
                                      .name(table)
                                      .columnList(clickhouseColumns)
                                      .columnMap(columnMap)
                                      .dbURL(chDbURL)
                                      .db(db)
                                      .orderByColumns(ClickhouseTable.findOrderByColumn(clickhouseColumns))
                                      .pointColumns(pointColumns).build());
  }

  /**
   * 检测ch表是否存在 先从缓存中获取
   *
   * @param chDbUrl   chDBurl
   * @param tableName ch table名称
   * @return
   */
  public boolean checkCHTableExists(String chDbUrl, String tableName) {
    AtomicBoolean exits = new AtomicBoolean(false);
    Set<String> allCHTablesByDB = chTables.get(chDbUrl, key -> this.findAllTables(chDbUrl,true));
    if (CollectionUtils.isNotEmpty(allCHTablesByDB)) {
      exits.set(allCHTablesByDB.contains(tableName));
    } else {
     exits.set(this.existsByTableName(chDbUrl,tableName));
    }
    return exits.get();
  }

  /**
   * 根据chdb反查所有表列表
   * @param chDbUrl
   * @return
   */
  public Set<String> findAllTables(String chDbUrl,boolean withView) {
    Set<String> allTables = Sets.newHashSet();
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbUrl)) {
      String db = CHContext.getDBName(chDbUrl);
      jdbcConnection.query(String.format(queryAllTableSQL, db, withView ? "" : " AND engine <> 'View'"), r -> {
        while (r.next()) {
          allTables.add(r.getString(1));
        }
      });
    } catch (SQLException e) {
      log.error("queryAllTableSQL exec error dbUrl:{}", chDbUrl, e);
      throw new RuntimeException(e);
    }
    return allTables;
  }

  /**
   * 根据chdb反查所有表列表
   * @param chDbUrl
   * @return
   */
  public List<CHSystemTable> findAllSimpleTables(String chDbUrl, boolean withView,List<String> tables) {
    List<CHSystemTable> allTables = Lists.newArrayList();
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbUrl)) {
      String db = CHContext.getDBName(chDbUrl);
      String ext = "";
      if (!withView) {
        ext += " AND engine not in ('View','MaterializedView')";
      }
      if (CollectionUtils.isNotEmpty(tables)) {
        ext += " AND table " + SQLUtil.generateInExpress(tables);
      }
      jdbcConnection.query(String.format(queryAllTableSQL2, db, ext), r -> {
        while (r.next()) {
          allTables.add(CHSystemTable.builder().table(r.getString(1)).engineFull(r.getString(2)).build());
        }
      });
    } catch (SQLException e) {
      log.error("findAllSimpleTables exec error dbUrl:{}", chDbUrl, e);
      throw new RuntimeException(e);
    }
    return allTables;
  }

  /**
   * 根据system.tables 反查ch表是否存在
   * @param chDbUrl
   * @param tableName
   * @return
   */
  public boolean existsByTableName(String chDbUrl, String tableName) {
    AtomicBoolean exits = new AtomicBoolean(false);
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbUrl)) {
      String db = CHContext.getDBName(chDbUrl);
      jdbcConnection.query(String.format(queryTableSQL, db, tableName), r -> {
        exits.set(r.next());
      });
    } catch (SQLException e) {
      log.error("checkCHTableExists error dbUrl:{},tableName:{}", chDbUrl, tableName, e);
      throw new RuntimeException(e);
    }
    return exits.get();
  }

  /**
   * 按表加载列
   *
   * @param dbURL chdb url
   * @param db    chdb
   * @param table 表
   * @return
   */
  public List<ClickhouseColumn> loadColumnsPlus(String dbURL, String db, String table) {
    List<ClickhouseColumn> result = Lists.newArrayList();
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(dbURL)) {
      jdbcConnection.query(String.format(tableColumnSQL, db, table), r -> {
        while (r.next()) {
          String typeName = r.getString(2);
          int position = r.getInt(3);
          int inSortKey = r.getInt(4);
          String defaultKind = r.getString(5);
          String defaultExpression = r.getString(6);
          result.add(new ClickhouseColumn(r.getString(1), ClickHouseColumnType.parseFromTypeName(typeName), position, typeName, inSortKey, defaultKind, defaultExpression));
        }
      });
      Collections.sort(result);
      return result;
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 批量删除ch表
   *
   * @param chPublicCreatorArg
   * @return
   */
  public String batchDDLTableOnCh(CHPublicCreatorArg chPublicCreatorArg, String op) {
    List<String> tables = chPublicCreatorArg.getTables();
    if (CollectionUtils.isEmpty(tables)) {
      return "tables is empty";
    }
    String chDbURL = chPublicCreatorArg.getChDBName();
    String chDB = CHContext.getDBName(chDbURL);
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbURL)) {
      tables.forEach(table -> {
        try {
          long start = System.currentTimeMillis();
          if (Objects.equals(op, "drop")) {
            String sql = String.format("DROP TABLE IF EXISTS %s.%s ON CLUSTER '{cluster}' SYNC", chDB, table);
            jdbcConnection.executeUpdate(sql);
          } else if (Objects.equals(op, "truncate")) {
            String sql = String.format("TRUNCATE TABLE IF EXISTS %s.%s ON CLUSTER '{cluster}' ", chDB, table);
            jdbcConnection.executeUpdate(sql);
          } else {
            throw new RuntimeException("op is not support");
          }
          log.info("{} table dbUrl:{},tableName:{} success cost:{} ms", op, chDbURL, table,
            System.currentTimeMillis() - start);
        } catch (Exception e) {
          log.error("{} table error dbUrl:{},tableName:{}", op, chDbURL, table, e);
          throw new RuntimeException(e);
        }
      });
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
    return "ok";
  }

  /**
   * 根据已有得表结构创建新表
   *
   * @param chJdbcUrl     chJdbc url
   * @param fromTableName 模板表
   * @param newTableName  新建表
   */
  public void createFromExistsTable(String chJdbcUrl, String fromTableName, String newTableName) {
    String dbName = CHContext.getDBName(chJdbcUrl);
    String sql = String.format("CREATE TABLE IF NOT EXISTS %s.%s on cluster '{cluster}' AS  %s.%s", dbName, newTableName, dbName, fromTableName);
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chJdbcUrl)) {
      jdbcConnection.executeUpdate(sql);
      log.info("execute sql finish {}", sql);
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 对比两个表结构如果不同需要修改新表
   *
   * @param chJdbcUrl     chJdbc url
   * @param fromTableName 模板表
   * @param newTableName  新建表
   */
  public List<String> compareAndUpdateTable(String chJdbcUrl, String fromTableName, String newTableName) {
    String dbName = CHContext.getDBName(chJdbcUrl);
    List<ClickhouseColumn> fromTableColumns = this.loadColumnsPlus(chJdbcUrl, dbName, fromTableName);
    List<ClickhouseColumn> newTableColumns = this.loadColumnsPlus(chJdbcUrl, dbName, newTableName);
    if (CollectionUtils.isEmpty(fromTableColumns) || CollectionUtils.isEmpty(newTableColumns)) {
      throw new RuntimeException(String.format(
        "compareAndUpdateTable error chJdbcUrl:%s,fromTableName:%s,columnSiz:%d " +
          "newTableName:%s,columnSize:%d", chJdbcUrl, fromTableName, fromTableColumns.size(), newTableName, newTableColumns.size()));
    }
    Map<String, ClickhouseColumn> fMaps = fromTableColumns.stream()
                                                          .collect(Collectors.toMap(ClickhouseColumn::getName,
                                                            Function.identity()));
    Map<String, ClickhouseColumn> nMaps = newTableColumns.stream()
                                                         .collect(Collectors.toMap(ClickhouseColumn::getName,
                                                           Function.identity()));
    Set<String> allColumns = CollectionHelper.union2Set(fMaps.keySet(), nMaps.keySet());
    List<String> alterSQLs = Lists.newArrayList();
    allColumns.forEach(columnName -> {
      ClickhouseColumn fColumn = fMaps.get(columnName);
      ClickhouseColumn nColumn = nMaps.get(columnName);
      if (fColumn != null && nColumn != null) {
        if (!nColumn.isSameTypeWith(fColumn)) {
          String dropSQL = nColumn.createAlterSQL("D", dbName, newTableName, "'{cluster}'");
          alterSQLs.add(dropSQL);
          String addSQL = fColumn.createAlterSQL("I", dbName, newTableName, "'{cluster}'");
          alterSQLs.add(addSQL);
        }
      } else if (fColumn == null) {
        String dropSQL = nColumn.createAlterSQL("D", dbName, newTableName, "'{cluster}'");
        alterSQLs.add(dropSQL);
      } else {
        String addSQL = fColumn.createAlterSQL("I", dbName, newTableName, "'{cluster}'");
        alterSQLs.add(addSQL);
      }
    });
    return alterSQLs;
  }


  /**
   * 为新建企业创建新的路由
   */
  public void createChRoute(String tenantId, String chDb, Map<Integer, String> eiToEaMap) {
    if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(chDb)) {
      return;
    }
    String initChDb = CHContext.getDBName(chDb);
    if (StringUtils.isBlank(initChDb)) {
      return;
    }
    String ea = eiToEaMap.get(Integer.valueOf(tenantId));
    EnterPriseLevelEnum enterPriseLevelEnum = EnterPriseLevelEnum.getEnterPriseLevelEnum(false, ea);
    InitChDbRouteArg initChDbRouteArg = InitChDbRouteArg.builder()
                                                        .dbName(initChDb)
                                                        .activityLevel(enterPriseLevelEnum.getStatus())
                                                        .podRouter(InitChDbRouteArg.PodRouter.builder()
                                                                                             .biz("BI")
                                                                                             .tenantId(tenantId)
                                                                                             .dialect("clickhouse")
                                                                                             .standalone(false)
                                                                                             .build())
                                                        .build();
    ApiResult apiResult = rpcPaasService.initChDbRoute(initChDbRouteArg);
    if (!ApiResult.IsSuccess(apiResult)) {
      log.error("初始化企业CH路由失败,请求json:{}", JSON.toJSON(initChDbRouteArg));
    }
  }

  /**
   * 删除ch表分区
   * @param chJdbcUrl
   * @param partitionName
   */
  public void dropPartition(String chJdbcUrl, String tableName, String partitionName) {
    String dbName = CHContext.getDBName(chJdbcUrl);
    String sql = String.format("ALTER TABLE %s.%s on cluster '{cluster}' DROP PARTITION '%s'", dbName, tableName, partitionName);
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chJdbcUrl)) {
      jdbcConnection.executeUpdate(sql);
      log.info("exec dropPartition sql finish {}", sql);
    } catch (SQLException e) {
      throw new RuntimeException(String.format("exec dropPartition sql error %s",sql),e);
    }
  }

  /**
   * 批量修改ch表的TTL设置
   * @param chPublicCreatorArgs
   */
  public void batchModifyCustomChTableTTL(List<CHPublicCreatorArg> chPublicCreatorArgs) {
    Pattern ttlRegx = Pattern.compile("(?i).*bi_sys_ods_part\\s+IN\\s*\\(\\s*'i'\\s*,\\s*'c'\\s*\\).*",Pattern.DOTALL);
    String settings = String.format(" SETTINGS  distributed_ddl_task_timeout=%d;",WarehouseConfig.distributed_ddl_task_timeout);
    if (CollectionUtils.isEmpty(chPublicCreatorArgs)) {
      log.error("batchModifyChTableTTL chPublicCreatorArgs is empty!");
      return;
    }
    chPublicCreatorArgs.forEach(arg -> {
      String catalinaHome = System.getProperty("catalina.home");
      if (StringUtils.isBlank(catalinaHome)) {
        log.error("catalina.home is empty can not createTTLSqL {}", JSON.toJSONString(arg));
        return;
      }
      String chDBName = CommonUtils.getDBName(arg.getChDBName());
      String sqlFilePath = catalinaHome + File.separatorChar + "logs" + File.separatorChar + "ch_" + chDBName + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".sql";
      log.info("sqlFieldPath is:{}", sqlFilePath);
      try (FileWriter writer = new FileWriter(sqlFilePath, StandardCharsets.UTF_8)) {
        List<CHSystemTable> finalTables;
        List<String> tables = arg.getTables();
        if (CollectionUtils.isEmpty(tables)) {
          List<CHSystemTable> tableSet = this.findAllSimpleTables(arg.getChDBName(), false, null);
          finalTables = tableSet.stream()
                                .filter(table -> !WarehouseConfig.ch2chTables.contains(table.getTable()))
                                .toList();
        } else {
          finalTables = this.findAllSimpleTables(arg.getChDBName(), false, tables);
        }
        if (CollectionUtils.isEmpty(finalTables)) {
          log.error("tables is empty chURL:{}", arg.getChDBName());
          return;
        }
        finalTables.forEach(table -> {
          if(arg.isOnlyCustomTable() && !table.getTable().endsWith("__c")){
            log.warn("this table:{} is not customer table skip",table);
            return;
          }
          Optional<ClickhouseTable> clickhouseTable = this.loadTableFromDB(arg.getChDBName(), table.getTable());
          clickhouseTable.ifPresent(chTable -> {
            if (chTable.existsPartitionKey()) {
              Map<String, ClickhouseColumn> columnMap = chTable.getColumnMap();
              if (columnMap.get("bi_sys_flag") == null || columnMap.get("bi_sys_version") == null) {
                log.warn("this table:{} has no column:bi_sys_flag or bi_sys_version skip!", table.getTable());
                return;
              }
              if(StringUtils.isNotBlank(table.getEngineFull()) && ttlRegx.matcher(table.getEngineFull()).matches()){
                log.warn("this table:{} has changed TTL!enginFull:{}", table.getTable(),table.getEngineFull());
                return;
              }
              ClickhouseColumn clickhouseColumn = chTable.getColumnMap().get("is_deleted");
              String ttlSQL = "ALTER TABLE %s.%s ON CLUSTER '{cluster}' MODIFY TTL bi_sys_version + toIntervalMonth(1) WHERE (bi_sys_ods_part = 's') AND ((bi_sys_flag = 0) OR (is_deleted IN (-1, -2))), bi_sys_version + toIntervalDay(3) WHERE bi_sys_ods_part in ('i','c') %s";
              if (clickhouseColumn == null) {
                ttlSQL = "ALTER TABLE %s.%s ON CLUSTER '{cluster}' MODIFY TTL bi_sys_version + toIntervalMonth(1) WHERE (bi_sys_ods_part = 's') AND (bi_sys_flag = 0), bi_sys_version + toIntervalDay(3) WHERE bi_sys_ods_part in ('i','c') %s";
              } else {
                ClickHouseColumnType columnType = clickhouseColumn.getType();
                if (columnType == ClickHouseColumnType.BOOL) {
                  ttlSQL = "ALTER TABLE %s.%s ON CLUSTER '{cluster}' MODIFY TTL bi_sys_version + toIntervalMonth(1) WHERE (bi_sys_ods_part = 's') AND ((bi_sys_flag = 0) OR (is_deleted = 1)), bi_sys_version + toIntervalDay(3) WHERE bi_sys_ods_part in ('i','c') %s";
                }
              }
              try {
                writer.write(String.format(ttlSQL, chTable.getDb(), chTable.getName(),settings));
                writer.write("\n");
              } catch (Exception e) {
                throw new RuntimeException(e);
              }
              if (arg.isNeedCreate()) {
                try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(arg.getChDBName(),7200000L)) {
                  long start = System.currentTimeMillis();
                  log.info("execute alter start db:{},table:{}", chTable.getDb(), chTable.getName());
                  jdbcConnection.executeUpdate(String.format(ttlSQL, chTable.getDb(), chTable.getName(),settings));
                  log.info("execute alter success db:{},table:{},cost:{} ms", chTable.getDb(), chTable.getName(),System.currentTimeMillis()-start);
                } catch (Exception e) {
                  log.error("execute alter error {}", String.format(ttlSQL, chTable.getDb(), chTable.getName(),settings), e);
                  if (Objects.equals(e.getMessage(), "Read timed out")) {
                    return;
                  }
                  throw new RuntimeException(e);
                }
              }
            }
          });
        });
      } catch (Exception e) {
        log.error("batchModifyCustomChTableTTL write to file error db:{}", arg.getChDBName(), e);
        return;
      }
      log.info("batchModifyCustomChTableTTL finish chDB:{}",arg.getChDBName());
    });
  }

  public void repairPartition(List<CHPublicCreatorArg> chPublicCreatorArgs) {
    new Thread(() -> {
       String moveSql = "alter table %s.agg_data_history on cluster '{cluster}' move partition %s to table %s.agg_data";
       String queryPartition = "select partition from system.parts where database = '%s' and table = 'agg_data_history' and active = 1 and partition='%s' limit 1";
       chPublicCreatorArgs.forEach(chPublicCreatorArg -> {
         long start=System.currentTimeMillis();
         String chDB = CommonUtils.getDBName(chPublicCreatorArg.getChDBName());
         List<String> partitions = chPublicCreatorArg.getTables();
         if (CollectionUtils.isNotEmpty(partitions)) {
           for (String part : partitions) {
             AtomicBoolean have = new AtomicBoolean(false);
             String queryPart = String.format(queryPartition, chDB, part);
             try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chPublicCreatorArg.getChDBName())) {
               jdbcConnection.query(queryPart, rs -> {
                 if (rs.next()) {
                   have.set(true);
                 }
               });
               log.info("db:{},table:agg_data_history,partition:{} have or not?:{}", chPublicCreatorArg.getChDBName(), part, have.get());
             } catch (Exception e) {
               log.error("execute sql:{} error,db:{},table:{}", queryPart, chPublicCreatorArg.getChDBName(), "agg_data_history", e);
             }
             if (have.get()) {
               String msql = String.format(moveSql, chDB, part,chDB);
               try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chPublicCreatorArg.getChDBName())) {
                 jdbcConnection.executeUpdate(msql);
                 log.info("execute alter success db:{},table:{}", chPublicCreatorArg.getChDBName(), "agg_data");
               } catch (Exception e) {
                 log.error("execute alter error {},db:{},table:{}", msql, chPublicCreatorArg.getChDBName(), "agg_data", e);
               }
             }
           }
         }
         log.info("repairPartition finish chdb:{},cost:{}",chPublicCreatorArg.getChDBName(),System.currentTimeMillis()-start);
       });
     }).start();
  }

  public Map<String, Set<String>> queryPaas2BICHIncInfo(DBSyncInfoBO dbSyncInfo,
                                                        long[] batchNums,
                                                        ClickhouseTable clickhouseTable,
                                                        long fromModifiedTime,
                                                        long toModifiedTime,
                                                        String eiColumnName,
                                                        List<String> currentIds) {
    Map<String, Set<String>> apiNameEiMapper = Maps.newHashMap();
    String sql = clickhouseTable.createQueryApiAndEIsSQL(fromModifiedTime, toModifiedTime, batchNums, eiColumnName,currentIds);
    if(StringUtils.isEmpty(sql)){
      log.warn("queryPaas2BICHIncInfo createQueryApiAndEIsSQL is empty pgDB:{},chDB:{} ",dbSyncInfo.getPgDb(),dbSyncInfo.getChDb());
      return apiNameEiMapper;
    }
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(dbSyncInfo.getChDb())) {
      jdbcConnection.query(sql, rs -> {
        while (rs.next()) {
          String apiName = rs.getString("object_describe_api_name");
          String ei = rs.getString("tenant_id");
          if (StringUtils.isNotBlank(apiName) && StringUtils.isNotBlank(ei)) {
            //获取BI对象变更记录,schema隔离的租户自定义对象的表名称全部小写了，
            String objectDescribeApiName = apiName;
            if (StringUtils.equalsAnyIgnoreCase(clickhouseTable.getName(), CHContext.OBJECT_DATA, CHContext.BIZ_ACCOUNT, CHContext.GOAL_VALUE)) {
              switch (apiName) {
                case CHContext.AccountObj -> objectDescribeApiName = CHContext.BIZ_ACCOUNT;
                case CHContext.AccountMainDataObj -> objectDescribeApiName = CHContext.BIZ_ACCOUNT_MAIN_DATA;
                case CHContext.GoalValueObj -> objectDescribeApiName = CHContext.GOAL_VALUE;
                default -> objectDescribeApiName = apiName;
              }
            } else if (StringUtils.equalsAnyIgnoreCase(clickhouseTable.getName(), CHContext.OBJECT_DATA_LANG)) {
              objectDescribeApiName = objectDescribeApiName + CHContext._LANG;
            }
            apiNameEiMapper.computeIfAbsent(objectDescribeApiName, key -> Sets.newHashSet()).add(ei);
          }
        }
      });
    } catch (Exception e) {
      throw new RuntimeException(String.format("queryPaas2BICHIncInfo error pgDB:%s,chDB:%s,sql:%s",dbSyncInfo.getPgDb(),dbSyncInfo.getChDb(),sql),e);
    }
    return apiNameEiMapper;
  }

  public Optional<BIPair<String,String>> findSysModifiedTimeRange(String tableName,List<CHColumn> columns) {
    return chTableFilterTimeColumn.get(tableName, key -> Optional.ofNullable(ClickhouseTable.findSysModifiedTimeColumn(tableName, columns)));
  }

  /**
   * 批量修改表的sys_modified_time列的默认值
   *
   * @param chPublicCreatorArgs
   */
  public void batchModifyColumnDefault(List<CHPublicCreatorArg> chPublicCreatorArgs) {
    if (CollectionUtils.isEmpty(chPublicCreatorArgs)) {
      log.error("batchModifyColumnDefault chPublicCreatorArgs is empty!");
      return;
    }
    chPublicCreatorArgs.forEach(arg -> {
      String chDBName = CommonUtils.getDBName(arg.getChDBName());
      List<String> tables = Lists.newArrayList();

      // Step 1: Query tables that need modification
      try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(arg.getChDBName())) {
        jdbcConnection.query(String.format(queryTableColumnSQL, chDBName, chDBName), rs -> {
          while (rs.next()) {
            tables.add(rs.getString("table_name"));
          }
        });
      } catch (Exception e) {
        log.error("Query tables error for database: {}", arg.getChDBName(), e);
        return;
      }

      if (CollectionUtils.isEmpty(tables)) {
        log.info("No tables need to modify column default in database: {}", arg.getChDBName());
        return;
      }
      // Step 2: Execute modify column default SQL for each table
      tables.forEach(tableName -> {
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(arg.getChDBName())) {
          long start = System.currentTimeMillis();
          log.info("Start modifying column default for table: {}.{}", chDBName, tableName);

          String sql = String.format(modifyColumnDefault, chDBName, tableName);
          jdbcConnection.executeUpdate(sql);

          log.info("Successfully modified column default for table: {}.{}:SQL:{}, cost: {} ms", chDBName, tableName, sql,
            System.currentTimeMillis() - start);
        } catch (Exception e) {
          log.error("Failed to modify column default for table: {}.{}", chDBName, tableName, e);
          if (Objects.equals(e.getMessage(), "Read timed out")) {
            return;
          }
          throw new RuntimeException(e);
        }
      });

      log.info("Finished modifying column default for all tables in database: {}", arg.getChDBName());
    });
  }
}
