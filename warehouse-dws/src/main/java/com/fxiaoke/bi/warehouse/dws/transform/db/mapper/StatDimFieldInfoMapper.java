package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatDimFieldInfoDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 对象详情页图表维度
 * @Author:jief
 * @Date:2023/9/14
 */
@Repository
public interface StatDimFieldInfoMapper extends IBatchMapper<StatDimFieldInfoDO>, ITenant<StatDimFieldInfoMapper> {
  @Select("select * from stat_dim_field_info where tenant_id=#{tenantId} and view_id=any(array[#{viewIds}])")
  List<StatDimFieldInfoDO> queryStatDetailInfoByViewId(@Param("tenantId") String tenantId, @Param("viewIds") String[] viewIds);
}
