package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import com.facishare.paas.pod.dto.RouterInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SynchronizationAggDataArg {

    /**
     * 上游企业
     */
    private String upStreamTenantId;

    /**
     * 下游企业
     */
    private String downStreamTenantId;

    /**
     * 上游企业ch路由
     */
    private RouterInfo upStreamRouterInfo;

    /**
     * 下游企业ch路由
     */
    private RouterInfo downStreamRouterInfo;

    /**
     * 同步批次
     */
    private int batchNum;

    /**
     * 同步信息
     */
    private SynchronizationReadSqlArg synchronizationReadSqlArg;
}
