package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "udf_obj")
@Data
public class UdfObjDO {
  @Column(name = "obj_id")
  private String objId;

  @Column(name = "obj_name")
  private String objName;

  @Column(name = "obj_show_name")
  private String objShowName;

  @Column(name = "is_pre")
  private int isPre;

  @Column(name = "obj_type")
  private int objType;

  @Column(name = "ei")
  private int ei;

  @Column(name = "app_id")
  private String appId;

  @Column(name = "datasource_id")
  private int datasourceId;

  @Column(name = "creator")
  private String creator;

  @Column(name = "is_show")
  private int isShow;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "updator")
  private String updator;

  @Column(name = "update_time")
  private Date updateTime;

  @Column(name = "is_delete")
  private int isDelete;

  @Column(name = "database_id")
  private String databaseId;
}
