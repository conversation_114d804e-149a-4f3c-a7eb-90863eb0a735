package com.fxiaoke.bi.warehouse.ods.service;

import com.clickhouse.client.ClickHouseNode;
import com.clickhouse.client.ClickHouseProtocol;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.helper.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;

@Slf4j
@Service
public class CHNodeService {
  public ClickHouseNode findNode(String dbURL) {
    Pair<String, Integer> ipPort = CHContext.parseIpAndPort(dbURL);
    return ClickHouseNode.builder()
                         .address(ClickHouseProtocol.HTTP, new InetSocketAddress(ipPort.first, ipPort.second))
                         .build();
  }
}
