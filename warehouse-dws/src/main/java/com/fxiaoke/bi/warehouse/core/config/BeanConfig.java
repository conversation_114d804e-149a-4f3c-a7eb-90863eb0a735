package com.fxiaoke.bi.warehouse.core.config;

import com.facishare.bi.license.service.BaseLicenseService;
import com.facishare.converter.EIEAConverter;
import com.facishare.dubbo.plugin.client.DubboRestFactoryBean;
import com.facishare.dubbo.plugin.client.ServerHostProfile;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.restful.client.FRestApiProxyFactoryBean;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.CRMNotifyService;
import com.fxiaoke.bi.warehouse.common.bean.UserCenterService;
import com.fxiaoke.bi.warehouse.common.component.ClickHouseUtilService;
import com.fxiaoke.bi.warehouse.common.component.RpcPaasService;
import com.fxiaoke.bi.warehouse.common.util.DBInfoService;
import com.fxiaoke.bi.warehouse.dws.agg.service.impl.*;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBUpdateEventDao;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.TenantGroupService;
import com.fxiaoke.enterpriserelation2.service.UpstreamService;
import com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean;
import com.fxiaoke.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory;
import com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer;
import com.github.jedis.support.JedisFactoryBean;
import com.github.mybatis.util.SpringUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.util.Map;


/**
 * <AUTHOR>
 * @since 2023/3/17
 */
@Configuration
@ImportResource(locations = {"classpath:/META-INF/spring/pod-api-client.xml", "classpath:spring/ei-ea-converter.xml", "classpath:fs-uc-cache-no-dubbo.xml", "classpath:fs-paas-bizconf-client.xml", "classpath:spring/license-client.xml"})
public class BeanConfig {
  @Bean("springUtil")
  public SpringUtil springUtil() {
    return new SpringUtil();
  }

  @Bean("dbRouterClient")
  public DbRouterClient dbRouterClient() {
    return new DbRouterClient();
  }

  @Bean("autoConf")
  public ReloadablePropertySourcesPlaceholderConfigurer autoConf() throws Exception {
    ReloadablePropertySourcesPlaceholderConfigurer autoConf = new ReloadablePropertySourcesPlaceholderConfigurer();
    autoConf.setFileEncoding("UTF-8");
    autoConf.setIgnoreResourceNotFound(true);
    autoConf.setIgnoreUnresolvablePlaceholders(false);
    ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    autoConf.setLocations(resolver.getResources("classpath*:*.properties"));
    //    autoConf.setLocation(appProperties);
    autoConf.setConfigName("fs-bi-metadata-ant");
    return autoConf;
  }

  @Bean("jedisFactory")
  public JedisFactoryBean jedisFactory() {
    JedisFactoryBean jedisFactoryBean = new JedisFactoryBean();
    jedisFactoryBean.setConfigName("fs-bi-statistic-offline-redis");
    return jedisFactoryBean;
  }

  @Bean("serverHostProfile")
  public ServerHostProfile serverHostProfile() {
    ServerHostProfile serverHostProfile = new ServerHostProfile();
    serverHostProfile.setConfigName("fs-organization-adapter-api-rest-client");
    return serverHostProfile;
  }

  @Lazy
  @Bean("dubboRestFactoryBean")
  public <T> DubboRestFactoryBean dubboRestFactoryBean(@Qualifier("serverHostProfile") ServerHostProfile serverHostProfile) {
    DubboRestFactoryBean<T> dubboRestFactoryBean = new DubboRestFactoryBean<T>();
    dubboRestFactoryBean.setServerHostProfile(serverHostProfile);
    return dubboRestFactoryBean;
  }

  @Bean("userCenterApiHostProfile")
  public ServerHostProfile userCenterApiHostProfile() {
    ServerHostProfile serverHostProfile = new ServerHostProfile();
    serverHostProfile.setConfigName("fs-uc-api-rest-client");
    return serverHostProfile;
  }

  @Lazy
  @Bean("userCenterDubboRestFactoryBean")
  public <T> DubboRestFactoryBean userCenterDubboRestFactoryBean(@Qualifier("userCenterApiHostProfile") ServerHostProfile serverHostProfile) {
    DubboRestFactoryBean<T> dubboRestFactoryBean = new DubboRestFactoryBean<T>();
    dubboRestFactoryBean.setServerHostProfile(serverHostProfile);
    return dubboRestFactoryBean;
  }

  @Bean("orgAdapterApiEnterpriseConfigService")
  public EnterpriseConfigService orgAdapterApiEnterpriseConfigService(@Qualifier("dubboRestFactoryBean") DubboRestFactoryBean<EnterpriseConfigService> dubboRestFactoryBean) {
    dubboRestFactoryBean.setObjectType(EnterpriseConfigService.class);
    return dubboRestFactoryBean.getObject();
  }

  @Bean("enterpriseEditionService")
  public EnterpriseEditionService enterpriseEditionService(@Qualifier("userCenterDubboRestFactoryBean") DubboRestFactoryBean<EnterpriseEditionService> dubboRestFactoryBean) {
    dubboRestFactoryBean.setObjectType(EnterpriseEditionService.class);
    return dubboRestFactoryBean.getObject();
  }

  /**
   * 用户中心配hi
   *
   * @param enterpriseEditionService
   * @return
   */
  @Bean("userCenterService")
  public UserCenterService UserCenterService(@Qualifier("enterpriseEditionService") EnterpriseEditionService enterpriseEditionService) {
    UserCenterService userCenterService = new UserCenterService();
    userCenterService.setEnterpriseEditionService(enterpriseEditionService);
    return userCenterService;
  }

  @Bean("httpSupport")
  public HttpSupportFactoryBean httpSupportFactoryBean() {
    HttpSupportFactoryBean httpSupportFactoryBean = new HttpSupportFactoryBean();
    httpSupportFactoryBean.setConfigName("fs-bi-statistic-offline");
    return httpSupportFactoryBean;
  }

  /**
   * 配置路由
   *
   * @param client
   * @return
   */
  @Bean("rpcPaasService")
  public RpcPaasService rpcPaasService(@Qualifier("httpSupport") OkHttpSupport client) {
    RpcPaasService rpcPaasService = new RpcPaasService();
    rpcPaasService.setClient(client);
    return rpcPaasService;
  }

  @Bean
  public ClickHouseUtilService clickHouseUtilService(@Qualifier("rpcPaasService") RpcPaasService rpcPaasService,
                                                     EIEAConverter eieaConverter) {
    ClickHouseUtilService clickHouseUtilService = new ClickHouseUtilService();
    clickHouseUtilService.setRpcPaasService(rpcPaasService);
    clickHouseUtilService.setEIEAConverter(eieaConverter);
    return clickHouseUtilService;
  }

  @Bean
  public AggCopyAfterHandler createAggCopyAfterHandler(){
    return new AggCopyAfterHandler();
  }

  @Bean
  public AggCopyBeforeHandler createAggCopyBeforeHandler(AggCopyAfterHandler aggCopyAfterHandler){
    AggCopyBeforeHandler aggCopyBeforeHandler = new AggCopyBeforeHandler();
    aggCopyBeforeHandler.setNextHandler(aggCopyAfterHandler);
    return aggCopyBeforeHandler;
  }

  /**
   * 计算分区拷贝before到存量分区
   * @return
   */
  @Bean
  public AggCal2StockBeforeHandler createAggCal2StockBeforeHandler(){
    return new AggCal2StockBeforeHandler();
  }
  /**
   * 计算分区数据拷贝after到存量分区
   * @return
   */
  @Bean
  public AggCal2StockAfterHandler createAggCal2StockAfterHandler() {
    return new AggCal2StockAfterHandler();
  }

  /**
   * 增量分区拷贝计算分区
   * @param aggCal2StockBeforeHandler
   * @return
   */
  @Bean
  public AggInc2CalHandler createAggInc2CalHandler(AggCal2StockBeforeHandler aggCal2StockBeforeHandler){
    AggInc2CalHandler aggInc2CalHandler = new AggInc2CalHandler();
    aggInc2CalHandler.setNextHandler(aggCal2StockBeforeHandler);
   return aggInc2CalHandler;
  }
  @Bean("dbInfoService")
  public DBInfoService createDbInfoService(@Qualifier("httpSupport") OkHttpSupport httpSupport) {
    return new DBInfoService(httpSupport);
  }

  @Bean("fRestApiProxyFactoryBean")
  public <T> FRestApiProxyFactoryBean userFRestApiProxyFactoryBean() {
    return new FRestApiProxyFactoryBean<T>();
  }

  @Bean("cRMNotifyService")
  public CRMNotifyService CRMNotifyService(@Qualifier("fRestApiProxyFactoryBean") FRestApiProxyFactoryBean<CRMNotifyService> fRestApiProxyFactoryBean) throws Exception {
    fRestApiProxyFactoryBean.setType(CRMNotifyService.class);
    return fRestApiProxyFactoryBean.getObject();
  }

  @Bean(value = "erOkHttpSupport", initMethod = "init")
  public HttpSupportFactoryBean createErOkHttpSupport() {
    HttpSupportFactoryBean httpSupportFactoryBean = new HttpSupportFactoryBean();
    httpSupportFactoryBean.setConfigName("fs-rest-api-http-support");
    return httpSupportFactoryBean;
  }

  @Bean(value = "erRetrofitFactory", initMethod = "init")
  public ConfigRetrofitSpringFactory createConfigRetrofitSpringFactory(@Qualifier("erOkHttpSupport") OkHttpSupport erOkHttpSupport) {
    ConfigRetrofitSpringFactory configRetrofitSpringFactory = new ConfigRetrofitSpringFactory();
    configRetrofitSpringFactory.setConfigNames("fs-enterpriserelation-rest-api");
    configRetrofitSpringFactory.setHeaders(Map.of("x-eip-appid", "defaultApp"));
    configRetrofitSpringFactory.setOkHttpSupport(erOkHttpSupport);
    return configRetrofitSpringFactory;
  }

  @Bean
  public FxiaokeAccountService createFxiaokeAccountService(@Qualifier("erRetrofitFactory") ConfigRetrofitSpringFactory erRetrofitFactory) throws Exception {
    RetrofitSpringFactoryBean<FxiaokeAccountService> retrofitSpringFactoryBean = new RetrofitSpringFactoryBean<>();
    retrofitSpringFactoryBean.setFactory(erRetrofitFactory);
    retrofitSpringFactoryBean.setType(FxiaokeAccountService.class);
    return retrofitSpringFactoryBean.getObject();
  }

  @Bean
  public UpstreamService createUpstreamService(@Qualifier("erRetrofitFactory") ConfigRetrofitSpringFactory erRetrofitFactory) throws Exception {
    RetrofitSpringFactoryBean<UpstreamService> retrofitSpringFactoryBean = new RetrofitSpringFactoryBean<>();
    retrofitSpringFactoryBean.setFactory(erRetrofitFactory);
    retrofitSpringFactoryBean.setType(UpstreamService.class);
    return retrofitSpringFactoryBean.getObject();
  }

  @Bean
  public TenantGroupService createTenantGroupService(@Qualifier("erRetrofitFactory") ConfigRetrofitSpringFactory erRetrofitFactory) throws Exception {
    RetrofitSpringFactoryBean<TenantGroupService> retrofitSpringFactoryBean = new RetrofitSpringFactoryBean<>();
    retrofitSpringFactoryBean.setFactory(erRetrofitFactory);
    retrofitSpringFactoryBean.setType(TenantGroupService.class);
    return retrofitSpringFactoryBean.getObject();
  }

  @Bean
  public EnterpriseRelationService createEnterpriseRelationService(@Qualifier("erRetrofitFactory") ConfigRetrofitSpringFactory erRetrofitFactory) throws Exception {
    RetrofitSpringFactoryBean<EnterpriseRelationService> retrofitSpringFactoryBean = new RetrofitSpringFactoryBean<>();
    retrofitSpringFactoryBean.setFactory(erRetrofitFactory);
    retrofitSpringFactoryBean.setType(EnterpriseRelationService.class);
    return retrofitSpringFactoryBean.getObject();
  }

  @Bean("baseLicenseService")
  public BaseLicenseService createBaseLicenseService() {
    return new BaseLicenseService();
  }
}
