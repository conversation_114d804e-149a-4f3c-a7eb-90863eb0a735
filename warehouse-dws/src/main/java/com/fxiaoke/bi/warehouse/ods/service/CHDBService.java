package com.fxiaoke.bi.warehouse.ods.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.ods.bean.PgSysModifiedTimeInfo;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseColumn;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.ResultSetMetaData;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CHDBService {

  @Resource
  private CHDataSource chDataSource;
  @Resource
  private CHMetadataService chMetadataService;

  private static final String DEL_SQL = "delete from %s where %s in (%s) AND " + CHContext.BI_SYS_BATCH_ID + "=%d";
  //按租户全量删除
  private static final String DEL_ALL_SQL = "delete from %s where %s in (%s) ";

  private static final String insertSysUser = "insert into org_employee_user (id,tenant_id,user_id," +
    "owner,name,is_deleted,life_status,bi_sys_flag,bi_sys_version,bi_sys_batch_id,bi_sys_is_deleted) " + "values %s";

  /**
   * 批量插入系统人员数据
   *
   * @param tenantIds 租户列表
   */
  public void batchInsertSysUser(List<String> tenantIds, String chDb) {
    if (CollectionUtils.isEmpty(tenantIds)) {
      log.warn("batchInsertSysUser tenantIds is empty");
      return;
    }
    Map<String, Set<String>> urlEiMapper = Maps.newHashMap();
    tenantIds.forEach(ei -> {
      if (StringUtils.equals(ei, "-1")) {
        return;
      }
      if (StringUtils.isNotBlank(chDb)) {
        urlEiMapper.computeIfAbsent(chDb, key -> Sets.newHashSet()).add(ei);
        return;
      }
      log.error("can not find ch jdbcUrl tenantId:{}", ei);
    });
    urlEiMapper.forEach((url, eis) -> {
      String valuesSQL = eis.stream()
                            .map(ei -> String.format("('-10000','%s','-10000','-10000','system',0,'normal',1,now(),1," +
                              "0)", ei))
                            .collect(Collectors.joining(","));
      String insertSQL = String.format(insertSysUser, valuesSQL);
      try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(url)) {
        jdbcConnection.executeUpdate(insertSQL);
      } catch (Exception e) {
        log.error("execute sql error tenantId:{},sql:{}", JSON.toJSONString(eis), insertSQL, e);
      }
    });
  }


  /**
   * 根据batchid删除数据，防止输出插入重复
   *
   * @param chDbURL   chDbURL
   * @param table     table
   * @param batchNum  批次id
   * @param stopWatch 秒表
   * @param eiName    ei 字段名称
   * @param tenantIds 租户集合
   * @throws Exception
   */
  public void deleteByBatchId(String chDbURL,
                              String table,
                              long batchNum,
                              StopWatch stopWatch,
                              String eiName,
                              List<String> tenantIds,
                              boolean offline) throws Exception {
    stopWatch.start(MessageFormat.format("deleteByBatchId:dbURL{0},table:{1}", chDbURL, table));
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDbURL)) {
      String inSQL = tenantIds.stream().map(ei -> "'" + ei + "'").collect(Collectors.joining(","));
      String sql = String.format(offline ? DEL_ALL_SQL : DEL_SQL, table, eiName, inSQL, batchNum);
      jdbcConnection.executeUpdate("set mutations_sync=1;\n" + sql);
    } catch (Exception e) {
      log.error("deleteByBatchId from chDb error chURL{},table:{},batchNum:{}", chDbURL, table, batchNum, e);
      throw new RuntimeException(e);
    } finally {
      stopWatch.stop();
    }
  }

  /**
   * 根据batchid删除数据，防止输出插入重复
   */
  public void deleteByBatchIdPlus(ClickhouseTable clickhouseTable,
                                  long batchNum,
                                  StopWatch stopWatch,
                                  String eiName,
                                  List<String> tenantIds,
                                  boolean offline) {
    stopWatch.start(MessageFormat.format("delete before data :dbURL{0},table:{1}", clickhouseTable.getDbURL(),
      clickhouseTable.getName()));
    String eiInFilter = JoinHelper.joinSkipNullOrBlank(",", "'", tenantIds);
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(clickhouseTable.getDbURL())) {
      String selectField = clickhouseTable.getColumnList()
                                          .stream()
                                          .filter(column -> !StringUtils.equalsAny(column.getName(),
                                            CHContext.BI_SYS_IS_DELETED))
                                          .map(ClickhouseColumn::getName)
                                          .collect(Collectors.joining(","));
      selectField = selectField + " ,toUInt8(1) as bi_sys_is_deleted";
      StringBuilder insertSQLSB = new StringBuilder(" INSERT INTO " + clickhouseTable.getName() + " \n");
      insertSQLSB.append("SELECT ")
                 .append(selectField)
                 .append(" FROM ")
                 .append(clickhouseTable.getName())
                 .append(" WHERE ")
                 .append(eiName)
                 .append(" IN ")
                 .append("(")
                 .append(eiInFilter)
                 .append(")");
      if (!offline) {
        insertSQLSB.append(" AND ").append(CHContext.BI_SYS_BATCH_ID).append(" = ").append(batchNum);
      }
      insertSQLSB.append(" SETTINGS mutations_sync=1 ");
      log.info("delete sql is:{}", insertSQLSB);
      int num = jdbcConnection.executeUpdate(insertSQLSB.toString());
      log.info("delete db:{}, table:{},batchNum:{},success rows:{} ", clickhouseTable.getDbURL(),
        clickhouseTable.getName(), batchNum, num);
    } catch (Exception e) {
      log.error("delete before data error chURL{},table:{},batchNum:{}", clickhouseTable.getDbURL(),
        clickhouseTable.getName(), batchNum, e);
      throw new RuntimeException(e);
    } finally {
      stopWatch.stop();
    }
  }

  /**
   * 采用 in 查询的方式插入before 数据，agg计算完毕后再清理掉
   *
   * @param clickhouseTable clickhouse 表元数据对象
   * @param batchNum        批次号
   * @param orderByFields        in条件字段
   * @param fieldValues     in条件字段的值集合
   */
  public void insertBeforeDataPlus(ClickhouseTable clickhouseTable,
                                   long batchNum,
                                   List<String> orderByFields,
                                   List<String> fieldValues) {
    long start = System.currentTimeMillis();
    String insertFields = clickhouseTable.getColumnList()
                                         .stream()
                                         .map(ClickhouseColumn::getName)
                                         .collect(Collectors.joining(","));;
    Map<String/*column*/, String/*value*/> fixValue = Maps.newHashMap();
    fixValue.put("bi_sys_flag", "toInt8(" + CHContext.BI_SYS_FLAG_BEFORE + ")");
    fixValue.put("bi_sys_batch_id", "toInt64(" + batchNum + ")");
    fixValue.put("bi_sys_is_deleted", "toUInt8(0)");
    fixValue.put("bi_sys_version", "now()");
    String insertField = clickhouseTable.getColumnList().stream().map(column -> {
      if (StringUtils.isNotBlank(fixValue.get(column.getName()))) {
        return fixValue.get(column.getName()) + " AS " + column.getName();
      }
      return column.getName();
    }).collect(Collectors.joining(","));
    //拼接主键tuples集合
    List<String> primaryKeyValues = fieldValues.stream().map(value -> {
      String tuples = Arrays.stream(value.split("\\^")).map(v -> "'" + v + "'").collect(Collectors.joining(","));
      if (value.endsWith("^")) {
        tuples = tuples + ",''";
      }
      return tuples;
    }).map(v -> "(" + v + ")").toList();
    String withTuples = JoinHelper.joinSkipNullOrBlank(",", primaryKeyValues);
    StringBuilder insertSQLSB = new StringBuilder(" INSERT INTO " + clickhouseTable.getName() + " \n");
    insertSQLSB.append("(").append(insertFields).append(") \n");
    insertSQLSB.append(" WITH  [ ").append(withTuples).append("]").append(" AS before_data ").append("\n");
    String inFields = orderByFields.stream().map(field->String.format("%s.%s",clickhouseTable.getName(),field)).collect(Collectors.joining(","));
    //生成select 语句
    insertSQLSB.append(" SELECT ").append(insertField).append(" FROM ").append(clickhouseTable.getName()).append(" WHERE ").append("(").append(inFields).append(") IN before_data ");
    if (GrayManager.isAllowByRule("prevent_repeat_insert_before", clickhouseTable.getDb())) {
      insertSQLSB.append(String.format(" AND %s.bi_sys_batch_id < %d",clickhouseTable.getName(),batchNum));
    }
    //设置final
    insertSQLSB.append(" SETTINGS final = 1, do_not_merge_across_partitions_select_final=1, " +
      "optimize_move_to_prewhere_if_final=1,mutations_sync=1");
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(clickhouseTable.getDbURL(),500000L)) {
      int num = jdbcConnection.executeUpdate(insertSQLSB.toString());
      log.info("insertBeforeDataPlus chURL{},table:{},batchNum:{},rows:{},cost:{}", clickhouseTable.getDbURL(),
        clickhouseTable.getName(), batchNum, num,
        System.currentTimeMillis() - start);
    } catch (Exception e) {
      String errorMsg = e.getMessage();
      if (StringUtils.isNotBlank(errorMsg) &&
        (errorMsg.contains("No such column") || errorMsg.contains("Missing columns"))) {
        chMetadataService.invalidateCHTableCache(clickhouseTable.getDbURL(), clickhouseTable.getName());
      }
      log.error("insertBeforeDataPlus error chURL{},table:{},batchNum:{},sql:{}", clickhouseTable.getDbURL(),
        clickhouseTable.getName(), batchNum, insertSQLSB, e);
      throw new RuntimeException(e);
    }
  }

  /**
   * 通过insert into select方式插入before数据
   *
   * @param fromClickhouseTable 数据来源ch表
   * @param toClickhouseTable   数据接收表
   * @param batchNum            批次号
   * @param sysTimeRange        事件区间
   * @param stopWatch
   */
  public int insertBeforeData(String tenantId,
                               ClickhouseTable fromClickhouseTable,
                               ClickhouseTable toClickhouseTable,
                               long batchNum,
                               Pair<Long, Long> sysTimeRange,
                               StopWatch stopWatch,
                              String partitionName) {
    stopWatch.start(String.format("insert before:dbURL%s,table:%s", toClickhouseTable.getDbURL(), toClickhouseTable.getName()));
    String insertFields = fromClickhouseTable.getColumnList()
                                             .stream()
                                             .map(ClickhouseColumn::getName)
                                             .collect(Collectors.joining(","));
    Map<String/*column*/, String/*value*/> fixValue = Maps.newHashMap();
    fixValue.put(CHContext.BI_SYS_FLAG, "toInt8(" + CHContext.BI_SYS_FLAG_BEFORE + ")");
    fixValue.put(CHContext.BI_SYS_BATCH_ID, "toInt64(" + batchNum + ")");
    fixValue.put(CHContext.BI_SYS_IS_DELETED, "toUInt8(0)");
    fixValue.put(CHContext.BI_SYS_VERSION, "now()");
    if (StringUtils.isNotBlank(partitionName) && toClickhouseTable.existsPartitionKey()) {
      fixValue.put(CHContext.BI_SYS_ODS_PART, String.format("'%s'", partitionName));
    }
    String insertField = fromClickhouseTable.getColumnList().stream().map(column -> {
      if (StringUtils.isNotBlank(fixValue.get(column.getName()))) {
        return fixValue.get(column.getName()) + " AS " + column.getName();
      }
      return column.getName();
    }).collect(Collectors.joining(","));
    List<String> orderByColumns = fromClickhouseTable.getOrderByColumns();
    String queryOrderByColumn = fromClickhouseTable.createBizDataQuerySQL(orderByColumns, Lists.newArrayList(tenantId), sysTimeRange);
    StringBuilder insertSQLSB = new StringBuilder(" INSERT INTO " + toClickhouseTable.getDb()+"."+toClickhouseTable.getName() + " \n");
    insertSQLSB.append("(").append(insertFields).append(") \n");
    insertSQLSB.append(" WITH before_data AS ( ").append(queryOrderByColumn).append(")").append("\n");
    //查询原表sql需要加上表名称
    String wheres = "WHERE (" +orderByColumns.stream().map(c->String.format("%s.%s",toClickhouseTable.getName(),c)).collect(Collectors.joining(",")) + ") IN before_data ";
    if(StringUtils.isNotBlank(partitionName) && fromClickhouseTable.existsPartitionKey()){
      wheres = String.format(" PREWHERE %s.bi_sys_ods_part='s' ",toClickhouseTable.getName()) + wheres;
    }
    //生成select 语句
    insertSQLSB.append(" SELECT ")
               .append(insertField)
               .append(" FROM ")
               .append(toClickhouseTable.getDb())
               .append(".")
               .append(toClickhouseTable.getName()).append(" ").append(wheres);
    //设置final
    insertSQLSB.append(" SETTINGS final = 1, do_not_merge_across_partitions_select_final=1, optimize_move_to_prewhere_if_final=1,mutations_sync=1");
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(toClickhouseTable.getDbURL(),7200000L)) {
      int num = jdbcConnection.executeUpdate(insertSQLSB.toString());
      log.info("sql:{}",insertSQLSB);
      log.info("insertBeforeDataPlus chURL{},table:{},batchNum:{},rows:{},cost:{}", toClickhouseTable.getDbURL(), toClickhouseTable.getName(), batchNum, num, stopWatch.getTaskInfo());
      return num;
    } catch (Exception e) {
      String errorMsg = e.getMessage();
      if (StringUtils.isNotBlank(errorMsg) &&
        (errorMsg.contains("No such column") || errorMsg.contains("Missing columns"))) {
        chMetadataService.invalidateCHTableCache(toClickhouseTable.getDbURL(), toClickhouseTable.getName());
      }
      log.error("insertBeforeDataPlus error chURL{},table:{},batchNum:{},sql:{}", toClickhouseTable.getDbURL(), toClickhouseTable.getName(), batchNum, insertSQLSB, e);
      throw new RuntimeException(e);
    }
  }

  /**
   * 反查明细表数据写入新的明细表
   *
   * @param fromClickhouseTable
   * @param toClickhouseTable
   * @param batchNum
   * @param stopWatch
   */
  public int insertBizData(List<String> tenantIds,
                           ClickhouseTable fromClickhouseTable,
                           ClickhouseTable toClickhouseTable,
                           long batchNum,
                           Pair<Long, Long> sysTimeRange,
                           StopWatch stopWatch,
                           String partitionName) {
    stopWatch.stopLastAndStart(String.format("insert data dbURL:%s,table:%s", toClickhouseTable.getDbURL(), toClickhouseTable.getName()));
    List<String> toColumns = toClickhouseTable.getColumnList().stream().map(ClickhouseColumn::getName).toList();
    StringBuilder insertSQL = new StringBuilder();
    insertSQL.append("INSERT INTO ").append(toClickhouseTable.getDb()).append(".").append(toClickhouseTable.getName());
    String insertColumns = String.join(",", toColumns.stream().map(cName -> "`" + cName + "`").toList());
    insertSQL.append("(").append(insertColumns).append(")");
    Set<String> fromColumnSet = fromClickhouseTable.getColumnMap().keySet();
    List<String> selectColumns = toColumns.stream().map(cName -> {
      if (Objects.equals(cName, "bi_sys_batch_id")) {
        return batchNum + " AS bi_sys_batch_id";
      }
      if (Objects.equals(cName, "bi_sys_version")) {
        return "now() AS bi_sys_version";
      }
      if (Objects.equals(cName, "bi_sys_ods_part") && StringUtils.isNotBlank(partitionName)) {
        return String.format("'%s' AS bi_sys_ods_part",partitionName);
      }
      if (!fromColumnSet.contains(cName)) {
        if(Objects.equals(cName,"bi_sys_is_deleted")){
          return String.format("%d AS bi_sys_is_deleted",0);
        }
        if(Objects.equals(cName,"is_deleted")){
          return String.format("%d AS is_deleted",0);
        }
        if(Objects.equals(cName,"bi_sys_flag")){
          return String.format("%d AS bi_sys_flag",1);
        }
        return "null AS " + cName;
      }
      return cName;
    }).toList();
    String selectSQL = fromClickhouseTable.createBizDataQuerySQL(selectColumns, tenantIds, sysTimeRange);
    insertSQL.append(selectSQL);
    insertSQL.append(" SETTINGS final = 1, do_not_merge_across_partitions_select_final=1, optimize_move_to_prewhere_if_final=1");
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(toClickhouseTable.getDbURL(), 7200000L)) {
      int num = jdbcConnection.executeUpdate(insertSQL.toString());
      log.info("insertBizData db:{},fromTable:{},toTable:{},batchNum:{},success rows{} ", fromClickhouseTable.getDbURL(), fromClickhouseTable.getName(), toClickhouseTable.getName(), batchNum, num);
      return num;
    } catch (Exception e) {
      log.error("insertBizData error tenantId:{},chJdbcURL:{},fromTable:{},toTable:{},batchNum:{}", JSON.toJSONString(tenantIds), fromClickhouseTable.getDbURL(), fromClickhouseTable.getName(), toClickhouseTable.getName(), batchNum, e);
      throw new RuntimeException(e);
    } finally {
      stopWatch.stop();
    }
  }

  /**
   * @param dbURL
   * @param sql
   */
  public void executeUpsertOnCH(String dbURL, String[] sql) {
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(dbURL)) {
      jdbcConnection.executeUpdate(sql);
    } catch (Exception e) {
      log.error("execute sql error dbRUL:{},sql:{}", dbURL, JSON.toJSONString(sql), e);
      throw new RuntimeException(e);
    }
  }

  public void executeSQLWithJdbcUrl(String jdbcUrl, String sql, long readTimeOut) {
    try (JdbcConnection conn = chDataSource.getJdbcConnection(jdbcUrl, readTimeOut)) {
      long start = System.currentTimeMillis();
      int count = conn.executeUpdate(sql);
      log.info("sql: {}, count: {},cost:{}", sql, count, System.currentTimeMillis() - start);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 查找大于某个系统修改时间的企业id和最大系统修改时间
   *
   * @param clickhouseTable
   * @param fromSysModifiedTime
   * @param stopWatch
   * @return
   */
  public PgSysModifiedTimeInfo findMaxSysModifiedTimeByTable(JdbcConnection jdbcConnection,
                                                             ClickhouseTable clickhouseTable,
                                                             Long fromSysModifiedTime,
                                                             StopWatch stopWatch) throws Exception {
    stopWatch.stopLastAndStart("findMaxSysModifiedTimeByTable");
    String queryMaxModifiedSQL = clickhouseTable.createMaxModifiedTimeSQL(fromSysModifiedTime);
    PgSysModifiedTimeInfo.PgSysModifiedTimeInfoBuilder builder = PgSysModifiedTimeInfo.builder();
    try {
      jdbcConnection.query(queryMaxModifiedSQL, rs -> {
        List<String> columns = Lists.newArrayList();
        ResultSetMetaData resultSetMetaData = rs.getMetaData();
        int cc = resultSetMetaData.getColumnCount();
        for (int i = 1; i <= cc; i++) {
          columns.add(resultSetMetaData.getColumnName(i));
        }
        if (rs.next()) {
          for (String column : columns) {
            Object value = rs.getObject(column);
            switch (column) {
              case "ei": {
                builder.tenantId(String.valueOf(value));
                break;
              }
              case "max_sys_modified_time": {
                if (Objects.isNull(value)) {
                  builder.maxModifiedTime(0);
                } else {
                  builder.maxModifiedTime((Long) value);
                }
                break;
              }
            }
          }
        }
      });
    } catch (Exception e) {
      log.error("findMaxSysModifiedTimeByTable error db:{},table:{}", clickhouseTable.getDb(),
        clickhouseTable.getName(), e);
      throw e;
    } finally {
      stopWatch.stop();
    }
    return builder.build();
  }
}
