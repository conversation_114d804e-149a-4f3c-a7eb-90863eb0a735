package com.fxiaoke.bi.warehouse.core.config;

import com.facishare.paas.pod.client.DbRouterClient;
import com.fxiaoke.bi.warehouse.common.component.BIPgDataSource;
import com.fxiaoke.bi.warehouse.common.component.MybatisBITenantPolicy;
import com.fxiaoke.bi.warehouse.common.db.dao.DbSyncInfoFlowDao;
import com.fxiaoke.bi.warehouse.common.db.dao.DbTableSyncInfoDao;
import com.github.mybatis.spring.DynamicDataSource;
import com.github.mybatis.spring.ScannerConfigurer;
import com.github.mybatis.tenant.TenantPolicy;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
public class BiDBConfig implements ResourceLoaderAware {
  private ResourceLoader resourceLoader;

  @Bean("biDataSource")
  @DependsOn("springUtil")
  public DataSource tenantDataSource(@Qualifier("mybatisTenantPolicy") TenantPolicy tenantPolicy) {
    DynamicDataSource dataSource = new DynamicDataSource();
    dataSource.setConfigName("fs-bi-statistic-db-online");
    dataSource.setTenantPolicy(tenantPolicy);
    dataSource.setConnectionPoolDriver("hikari");
    return dataSource;
  }

  @Bean("biSqlSessionFactoryBean")
  public SqlSessionFactoryBean sqlSessionFactoryBean(@Qualifier("biDataSource") DataSource dataSource) throws Exception {
    SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
    sqlSessionFactoryBean.setDataSource(dataSource);
    sqlSessionFactoryBean.setTypeAliasesPackage("com.fxiaoke.bi.warehouse.dws.db.entity,com.fxiaoke.bi.warehouse.dws.transform.db.entity,com.fxiaoke.bi.warehouse.ods.bean,com.fxiaoke.bi.warehouse.core.db.entity");
    sqlSessionFactoryBean.setConfigLocation(resourceLoader.getResource("classpath:mybatis/mybatis-config.xml"));
    ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath*:mybatis/mapper/*.xml"));
    return sqlSessionFactoryBean;
  }

  @Bean("scannerConfigurer")
  public ScannerConfigurer scannerConfigurer() {
    ScannerConfigurer scannerConfigurer = new ScannerConfigurer();
    scannerConfigurer.setBasePackage("com.fxiaoke.bi.warehouse.dws.db.mapper,com.fxiaoke.bi.warehouse.dws.transform.db.mapper,com.fxiaoke.bi.warehouse.ods.dao.mapper,com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper,com.fxiaoke.bi.warehouse.core.db.mapper");
    scannerConfigurer.setSqlSessionFactoryBeanName("biSqlSessionFactoryBean");
    return scannerConfigurer;
  }

  @Bean("biTxManager")
  public DataSourceTransactionManager dataSourceTransactionManager(@Qualifier("biDataSource") DataSource dataSource) {
    DataSourceTransactionManager txManager = new DataSourceTransactionManager();
    txManager.setDataSource(dataSource);
    return txManager;
  }

  @Bean("mybatisBITenantPolicy")
  public MybatisBITenantPolicy createMybatisBITenantPolicy(@Qualifier("dbRouterClient") DbRouterClient dbRouterClient) {
    MybatisBITenantPolicy mybatisBITenantPolicy= new MybatisBITenantPolicy();
    mybatisBITenantPolicy.setDbRouterClient(dbRouterClient);
    return mybatisBITenantPolicy;
  }
  @Bean("biPgDataSource")
  public BIPgDataSource biPgDataSource(@Qualifier("mybatisBITenantPolicy") MybatisBITenantPolicy mybatisBITenantPolicy) {
    return new BIPgDataSource(mybatisBITenantPolicy);
  }

  @Bean
  public DbSyncInfoFlowDao dbSyncInfoFlowDao(@Qualifier("biPgDataSource") BIPgDataSource biPgDataSource){
    DbSyncInfoFlowDao dbSyncInfoFlowDao= new DbSyncInfoFlowDao();
    dbSyncInfoFlowDao.setBiPgDataSource(biPgDataSource);
    return dbSyncInfoFlowDao;
  }

  @Bean
  public DbTableSyncInfoDao dbTableSyncInfoDao(@Qualifier("biPgDataSource") BIPgDataSource biPgDataSource){
    DbTableSyncInfoDao dbTableSyncInfoDao= new DbTableSyncInfoDao();
    dbTableSyncInfoDao.setBiPgDataSource(biPgDataSource);
    return dbTableSyncInfoDao;
  }
  @Override
  public void setResourceLoader(ResourceLoader resourceLoader) {
    this.resourceLoader = resourceLoader;
  }
}
