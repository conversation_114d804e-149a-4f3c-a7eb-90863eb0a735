package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Author:jief
 * @Date:2023/9/14
 */
@Data
@Table(name = "stat_dim_field_info")
public class StatDimFieldInfoDO {
  @Id
  @Column(name = "id")
  private String id;
  @Column(name = "view_id")
  private String viewId;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "source")
  private String source;
  @Column(name = "field_ids")
  private String fieldIds;
  @Column(name = "before_fields")
  private String beforeFields;
  @Column(name = "create_time")
  private long createTime;
  @Column(name = "last_modified_time")
  private long lastModifiedTime;
}
