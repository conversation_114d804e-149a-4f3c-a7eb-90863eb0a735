package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewFilterDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StatViewFilterMapper extends ITenant<StatViewFilterMapper>, IBatchMapper<StatViewFilterDO> {

  @Select("select * from stat_view_filter where (ei=-1 or ei=#{ei}) and field_id <> '0' and is_delete=0 and view_id=any(array[#{viewIds}]) ")
  List<StatViewFilterDO> getStatViewFilters(@Param("ei") int ei,@Param("viewIds") String[] viewIds);

  @Select("select count(1) from stat_view_filter where (ei=-1 or ei=#{ei}) and view_id=#{viewId} and field_id=any(array[#{fieldIds}]) and is_delete=0 ")
  int countOfGoalFields(@Param("ei") int ei,@Param("viewId") String viewId,@Param("fieldIds") String[] fieldIds);

  @Select("select distinct view_id from stat_view_filter where (ei=-1 or ei=#{ei}) and view_id=any(array[#{viewIds}]) and field_id=any(array[#{fieldIds}]) and is_delete=0 ")
  List<String> queryViewIdsGoalFields(@Param("ei") int ei,@Param("viewIds") String[] viewIds,@Param("fieldIds") String[] fieldIds);

  @Select("select * from stat_view_filter where (ei=-1 or ei=#{ei}) and view_id=#{viewId} and field_id = #{fieldId} and is_delete=0 ")
  StatViewFilterDO getStatViewFilterByFieldId(@Param("ei") int ei,@Param("viewId") String viewId,@Param("fieldId") String fieldId);
}
