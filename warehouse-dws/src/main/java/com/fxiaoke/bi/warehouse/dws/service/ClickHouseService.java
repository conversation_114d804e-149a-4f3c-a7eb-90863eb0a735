package com.fxiaoke.bi.warehouse.dws.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.facishare.paas.pod.exception.DbRouterException;
import com.fxiaoke.bi.warehouse.common.bean.CHColumn;
import com.fxiaoke.bi.warehouse.common.component.ClickHouseUtilService;
import com.fxiaoke.bi.warehouse.common.component.RpcPaasService;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.SQLUtil;
import com.fxiaoke.bi.warehouse.core.db.ClickhouseTenantPolicy;
import com.fxiaoke.bi.warehouse.dws.db.dao.DbSyncInfoDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.common.Pair;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.jedis.support.JedisCmd;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/9
 */
@Service
@Slf4j
public class ClickHouseService {
  @Autowired
  @Setter
  private DbRouterClient dbRouterClient;
  @Resource
  private ClickhouseTenantPolicy clickhouseTenantPolicy;
  @Resource
  private RpcPaasService rpcPaasService;
  @Resource
  private DbSyncInfoDao dbSyncInfoDao;
  @Resource
  private EIEAConverter eieaConverter;
  @Resource
  private ClickHouseUtilService clickHouseUtilService;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;

  private static final String columnSQL = "select name from system.columns where database='%s' and table ='%s' and " +
    "is_in_primary_key=1 order by position ";
  private static final String tableColumnSQL =
    "select name,type,position,is_in_sorting_key from system.columns where database='%s' and  table='%s'";
  private static final String batchTableColumnSQL =
    "select table,name,type,position,is_in_sorting_key from system.columns where database='%s' and table in (%s)";
  /**
   * 排序键
   */
  private static final String sortingKeySQL="select sorting_key,primary_key from system.tables where " +
    "database='%s' and name='%s'";
  private static final String tableSizeSQL =
    "select name,total_rows from system.tables where database='%s' and name " + "%s ";
  private static final String publicObjSizeSQL =
    "select object_describe_api_name,count() as nums from %s.object_data where " +
      "tenant_id='%s' and object_describe_api_name in(%s) group by object_describe_api_name";
  private static final String preTableSizeSQL = "select %s from %s.%s where %s ='%s' %s";
  private static final String queryTableSQL = "select name from system.tables where database='%s' and name='%s'";
  private static final String queryAllTableSQL = "select name from system.tables where database='%s'";

  /**
   * 保存租户对象数量得cache
   */
  private final Cache<String, Integer> objectSizeCaffCalCache = Caffeine.newBuilder()
                                                                        .expireAfterWrite(60 * 4, TimeUnit.MINUTES)
                                                                        .maximumSize(30000)
                                                                        .build();
  private final Cache<String, List<String>> chTablePkColumCache = Caffeine.newBuilder()
                                                                        .maximumSize(15000)
                                                                        .expireAfterWrite(1, TimeUnit.DAYS)
                                                                        .build();
  private final Cache<String, Set<String>> dwsChTables = Caffeine.newBuilder()
                                                              .maximumSize(1000)
                                                              .expireAfterWrite(30, TimeUnit.MINUTES)
                                                              .build();

  public List<CHColumn> loadTableCHColumn(String chDbURL, String pgSchema, String table) {
    String chDB = CommonUtils.getDBName(chDbURL);
    if (Objects.equals(pgSchema, "public")) {
      String key = CHContext.getCHTableColumnCacheKey(chDB, table);
      String columnsJSON = jedisCmd.get(key);
      if (StringUtils.isNotBlank(columnsJSON)) {
        return JSON.parseObject(columnsJSON, new TypeReference<>() {
        });
      } else {
        List<CHColumn> columnList = this.loadColumnsPlus(chDbURL, chDB, table);
        if (CollectionUtils.isNotEmpty(columnList)) {
          jedisCmd.set(key, JSON.toJSONString(columnList));
          jedisCmd.expire(key, 60 * 60L);
        }
        return columnList;
      }
    }
    return this.loadColumnsPlus(chDbURL, chDB, table);
  }

  /**
   * 按表加载列
   *
   * @param dbURL chdb url
   * @param db    chdb
   * @param table 表
   * @return
   */
  public List<CHColumn> loadColumnsPlus(String dbURL, String db, String table) {
    List<CHColumn> result = Lists.newArrayList();
    try (JdbcConnection jdbcConnection = this.createJdbcConnection(dbURL, 600000, false)) {
      jdbcConnection.query(String.format(tableColumnSQL, db, table), r -> {
        while (r.next()) {
          String name = r.getString(1);
          String typeName = r.getString(2);
          int position = r.getInt(3);
          result.add(CHColumn.builder().table(table).name(name).typeName(typeName).order(position).build());
        }
      });
      return result;
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  public Map<String,List<CHColumn>> batchLoadColumn(String dbURL,String db,List<String> tables){
    Map<String,List<CHColumn>> resultMap= Maps.newHashMap();
    List<CHColumn> result = Lists.newArrayList();
    String tableIn = JoinHelper.joinSkipNullOrBlank(",", "'", tables);
    try (JdbcConnection jdbcConnection = this.createJdbcConnection(dbURL, 600000, false)) {
      jdbcConnection.query(String.format(batchTableColumnSQL, db, tableIn), r -> {
        while (r.next()) {
          String table= r.getString(1);
          String name = r.getString(2);
          String typeName = r.getString(3);
          int position = r.getInt(4);
          result.add(CHColumn.builder().table(table).name(name).typeName(typeName).order(position).build());
        }
      });
      result.forEach(chColumn -> {
        resultMap.computeIfAbsent(chColumn.getTable(),key->Lists.newArrayList()).add(chColumn);
      });
      return resultMap;
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 用try-with-resource语句创建JdbcConnection， 并执行sql，打印受影响条数 如果遇到checked exception, 转换成unchecked
   * exception抛出。
   *
   * @param tenantId
   * @param sql
   */
  public void executeSQL(String tenantId, String sql, int readTimeOut) {
    JdbcConnection connection = createJdbcConnection(tenantId, readTimeOut);
    if (connection == null) {
      log.warn("connection is null, tenantId: {}", tenantId);
      return;
    }
    try (connection) {
      long start = System.currentTimeMillis();
      int count = connection.executeUpdate(sql);
      if (GrayManager.isAllowByRule("ch_show_exe_sql", tenantId)) {
        log.info("sql: {}, count: {},cost:{}", sql, count, System.currentTimeMillis() - start);
      }
    } catch (Exception e) {
      log.error("execute sql error: {} ", sql, e);
      throw new RuntimeException(e);
    }
  }

  public void executeSQLWithJdbcUrl(String jdbcUrl, String sql, int readTimeOut) {
    String chDB = Utils.parseDBNameFromJdbcUrl(jdbcUrl);
    String userNameSuffix = chDB.substring(0, chDB.length() - 3);
    try (JdbcConnection conn = new JdbcConnection(
      jdbcUrl + "?socket_timeout=" + readTimeOut, clickhouseTenantPolicy.getChProxyCalUserNamePre() +
      userNameSuffix, clickhouseTenantPolicy.getChProxyCalPassword())) {
      long start = System.currentTimeMillis();
      int count = conn.executeUpdate(sql);
      log.info("sql: {}, count: {},cost:{}", sql, count, System.currentTimeMillis() - start);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 获取路由，并创建JdbcConnection
   *
   * @param tenantId
   * @return
   */
  public JdbcConnection createJdbcConnection(String tenantId, int readTimeOut) {
    try {
      RouterInfo routerInfo = clickhouseTenantPolicy.getRouterInfo(tenantId);
      String jdbcUrl = "jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + routerInfo.getDbName();
      String userNameSuffix = routerInfo.getDbName().substring(0, routerInfo.getDbName().length() - 3);
      return new JdbcConnection(
        jdbcUrl + "?socket_timeout=" + readTimeOut, clickhouseTenantPolicy.getChProxyCalUserNamePre() +
        userNameSuffix, clickhouseTenantPolicy.getChProxyCalPassword());
    } catch (DbRouterException e) {
      if (e.getMessage().contains("不存在")) {//IgnoreI18n
        log.warn("tenantId: {} not found routerInfo", tenantId);
        return null;
      } else {
        throw new RuntimeException(e);
      }
    }
  }

  /**
   * 获取路由，并创建JdbcConnection
   *
   * @param jdbcUrl ch proxy url
   * @param readOnly 是否读从库
   * @param readTimeOut socket_timeout
   * @return
   */
  public JdbcConnection createJdbcConnection(String jdbcUrl, int readTimeOut, boolean readOnly) {
    String dbName = CommonUtils.getDBName(jdbcUrl);
    String userNameSuffix = dbName.substring(0, dbName.length() - 3);
    String user = (readOnly ?
      clickhouseTenantPolicy.getChProxyQueryUserNamePre() :
      clickhouseTenantPolicy.getChProxyCalUserNamePre()) + userNameSuffix;
    String pwd = readOnly ?
      clickhouseTenantPolicy.getChProxyQueryPassword() :
      clickhouseTenantPolicy.getChProxyCalPassword();
    return new JdbcConnection(jdbcUrl + "?socket_timeout=" + readTimeOut, user, pwd);
  }

  /**
   * 作废ch table PK columns
   *
   * @param dbURL ch dbURL
   * @param table table名称
   */
  public void invalidatePkColumnsCache(String dbURL, String table) {
    this.chTablePkColumCache.invalidate(dbURL + "^" + table);
  }

  /**
   * 根据租户id获取表的sorted key 字段
   * @param tenantId
   * @param table
   * @return
   */
  public List<String> queryCHTablePkColumnsByEI(String tenantId, String table) {
    RouterInfo routerInfo = clickhouseTenantPolicy.getRouterInfo(tenantId);
    String jdbcUrl = "jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + routerInfo.getDbName();
    return this.queryCHTablePkColumns(jdbcUrl, table);
  }

  /**
   * 因为是删除before数据的操作因此表结构完全从cache获取就行了
   * 不用考虑变化，关键的order by 字段存在即可
   *
   * @param dbURL dbURL
   * @param table table 名称
   * @return
   */
  public List<String> queryCHTablePkColumns(String dbURL, String table) {
    return chTablePkColumCache.get(dbURL + "^" + table, key -> {
      List<String> result = Lists.newArrayList();
      String chDB = Utils.parseDBNameFromJdbcUrl(dbURL);
      try (JdbcConnection jdbcConnection = this.createJdbcConnection(dbURL, 600000,false)) {
        jdbcConnection.query(String.format(sortingKeySQL, chDB, table), rs -> {
          if (rs.next()) {
            String sortingKey = rs.getString("sorting_key");
            result.addAll(Splitter.on(",").omitEmptyStrings().splitToList(sortingKey));
          }
        });
        return result;
      } catch (SQLException e) {
        throw new RuntimeException(e);
      }
    });
  }

  /**
   * 查询ch中表的大小
   *
   * @param tenantId
   * @param tableNames
   * @return
   */
  public Map<String, Integer> queryTableSize(String tenantId, List<String> tableNames) {
    RouterInfo routerInfo = clickhouseTenantPolicy.getRouterInfo(tenantId);
    if (routerInfo == null) {
      throw new RuntimeException("find router info error:" + tenantId);
    }
    Map<String, Integer> result = Maps.newHashMap();
    String inSql = SQLUtil.generateInExpress(tableNames);
    String dbName = routerInfo.getDbName();
    String sql = String.format(tableSizeSQL, dbName, inSql);
    String jdbcUrl = "jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + routerInfo.getDbName();
    String userNameSuffix = routerInfo.getDbName().substring(0, routerInfo.getDbName().length() - 3);
    try (JdbcConnection jdbcConnection = new JdbcConnection(jdbcUrl, clickhouseTenantPolicy.getChProxyCalUserNamePre() +
      userNameSuffix, clickhouseTenantPolicy.getChProxyCalPassword())) {
      jdbcConnection.query(sql, rs -> {
        while (rs.next()) {
          result.put(rs.getString("name"), rs.getInt("total_rows"));
        }
      });
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
    return result;
  }

  /**
   * 查询ch中表的大小
   *
   * @param tenantId
   * @param tableApiNamePair
   * @return
   */
  public boolean checkSamplePublicData(String tenantId,
                                       List<Pair<String, String>> tableApiNamePair,
                                       int limitThreshold) {
    RouterInfo routerInfo = clickhouseTenantPolicy.getRouterInfo(tenantId);
    if (routerInfo == null) {
      throw new RuntimeException("find router info error:" + tenantId);
    }
    Map<String, Integer> apiNameSize = Maps.newHashMap();
    //整理校验的对象和表的数据量来确定是否采用limit 计算,只对object_data做对象apiName 区分其他表不做区分了。比如客户主对象和客户对象都在biz_account 表中。
    Map<String, Set<String>> tableApiNameMapper = tableApiNamePair.stream().map(item -> {
      String apiName = item.getValue();
      if (apiName.endsWith("__c")) {
        return item;
      } else {
        return Pair.build(item.getKey(), item.getKey());
      }
    }).collect(Collectors.groupingBy(Pair::getKey, Collectors.mapping(Pair::getValue, Collectors.toSet())));
    AtomicBoolean moreThenThreshold = new AtomicBoolean(false);
    tableApiNameMapper.forEach((k, v) -> {
      if (moreThenThreshold.get()) {
        return;
      }
      List<String> haveNoSize = Lists.newArrayList();
      v.forEach(apiName -> {
        String key = String.format("%s:%s", tenantId, apiName);
        Integer size = objectSizeCaffCalCache.getIfPresent(key);
        if (size == null) {
          haveNoSize.add(apiName);
        } else {
          apiNameSize.put(apiName, size);
        }
      });
      String dbName = routerInfo.getDbName();
      if (!haveNoSize.isEmpty()) {
        String sql = null;
        if (k.equals(Constants.OBJECT_DATA)) {
          String inSQL = haveNoSize.stream().map(apiName -> "'" + apiName + "'").collect(Collectors.joining(","));
          sql = String.format(publicObjSizeSQL, dbName, tenantId, inSQL);
        } else {
          String tenantIdColumnName = "tenant_id";
          if (StringUtils.equalsAny(k, "opportunity_sale_action_stage")) {
            tenantIdColumnName = "ei";
          }
          sql = String.format(preTableSizeSQL, "count() as nums", dbName, k,tenantIdColumnName, tenantId, "");
        }
        this.queryApiNameSizeFromCH(tenantId, routerInfo, k, sql, apiNameSize);
      }
      Optional<Integer> maxSizeOP = apiNameSize.values().stream().max(Comparator.naturalOrder());
      if (maxSizeOP.orElse(0) >= limitThreshold) {
        moreThenThreshold.set(true);
      }
    });
    return moreThenThreshold.get();
  }

  private void queryApiNameSizeFromCH(String tenantId,
                                      RouterInfo routerInfo,
                                      String tableName,
                                      String sql,
                                      Map<String, Integer> apiNameSize) {
    String jdbcUrl = "jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + routerInfo.getDbName();
    String userNameSuffix = routerInfo.getDbName().substring(0, routerInfo.getDbName().length() - 3);
    try (JdbcConnection jdbcConnection =this.createJdbcConnection(jdbcUrl,600000,false)) {
      jdbcConnection.query(sql, rs -> {
        while (rs.next()) {
          if (tableName.equals(Constants.OBJECT_DATA)) {
            String apiName = rs.getString("object_describe_api_name");
            if (StringUtils.isNotBlank(apiName)) {
              int nums = rs.getInt("nums");
              log.info("queryApiNameSizeFromCH table:{},tenantId:{},apiName:{},size:{}", tenantId, tableName, apiName
                , nums);
              apiNameSize.put(apiName, nums);
              objectSizeCaffCalCache.put(String.format("%s:%s", tenantId, apiName), nums);
            }
          } else {
            int nums = rs.getInt("nums");
            log.info("queryApiNameSizeFromCH table:{},tenantId:{},apiName:{},size:{}", tenantId, tableName, tableName
              , nums);
            apiNameSize.put(tableName, nums);
            objectSizeCaffCalCache.put(String.format("%s:%s", tenantId, tableName), nums);
          }
        }
      });
    } catch (SQLException e) {
      log.error("queryApiNameSizeFromCH error sql:{}", sql, e);
      throw new RuntimeException(e);
    }
  }

  /**
   * 查询数据
   *
   * @param jdbcUrl
   * @param querySQL
   * @param mapper
   * @param <T>
   * @return
   */
  public <T> List<T> executeQuerySQLWithJdbc(String jdbcUrl, String querySQL, long readTimeout, Function<ResultSet, T> mapper) {
    List<T> resultList = new ArrayList<>();
    String chDB = Utils.parseDBNameFromJdbcUrl(jdbcUrl);
    String userNameSuffix =chDB.substring(0, chDB.length() - 3);
    try (JdbcConnection connection = new JdbcConnection(jdbcUrl + "?socket_timeout=" + readTimeout, clickhouseTenantPolicy.getChProxyCalUserNamePre() +
            userNameSuffix, clickhouseTenantPolicy.getChProxyCalPassword())) {
      connection.query(querySQL, res -> {
        while (res.next()) {
          T result = mapper.apply(res);
          resultList.add(result);
        }
      });
    }
    catch (Exception e) {
      throw new RuntimeException(e);
    }
    return resultList;
  }
  public <T> List<T> executeQuerySQL(String tenantId, String querySQL, Function<ResultSet, T> mapper) {
    List<T> resultList = new ArrayList<>();
    JdbcConnection connection = this.createJdbcConnection(tenantId, 30 * 60 * 1000);
    if (connection == null) {
      log.warn("executeQuerySQL get connection is null, tenantId: {}", tenantId);
      return resultList;
    }
    try (connection) {
      connection.query(querySQL, res -> {
        while (res.next()) {
          T result = mapper.apply(res);
          resultList.add(result);
        }
      });
    } catch (Exception e) {
      log.error("executeQuerySQL execute error, tenantId: {}, querySQL: {}, {}", tenantId, querySQL, e);
      throw new RuntimeException(e);
    }
    return resultList;
  }

  /**
   * 如果是下游指标，则需要判断表是否存在来确定该类指标是否需要计算
   * @return
   */
  public boolean checkAllExistCHUpTables(String tenantId, Set<String> tableNames) {
    if (CollectionUtils.isEmpty(tableNames)) {
      return true;
    }
    for (String tableName : tableNames) {
      String table = Constants.AGG_DOWNSTREAM_DATA.equals(tableName) ?
        tableName :
        tableName.concat(Constants.DOWNSTREAM_SUFFIX);
      if (!checkCHTableExists(tenantId, table)) {
        return false;
      }
    }
    return true;
  }

  /**
   * 检测ch表是否存在 先从缓存中获取
   *
   * @param tenantId   tenantId
   * @param tableName ch table名称
   * @return
   */
  public boolean checkCHTableExists(String tenantId, String tableName) {
    RouterInfo routerInfo = clickhouseTenantPolicy.getRouterInfo(tenantId);
    String chDbUrl = routerInfo.getJdbcUrl();
    String dbName = routerInfo.getDbName();
    AtomicBoolean exits = new AtomicBoolean(false);
    Set<String> allCHTablesByDB = dwsChTables.get(chDbUrl, key -> {
      Set<String> allTables = Sets.newHashSet();
      try (JdbcConnection jdbcConnection = createJdbcConnection(tenantId, 10 * 60 * 1000)) {
        jdbcConnection.query(String.format(queryAllTableSQL, dbName), r -> {
          while (r.next()) {
            allTables.add(r.getString(1));
          }
        });
      } catch (SQLException e) {
        log.error("dws queryAllTableSQL exec error dbUrl:{}", chDbUrl, e);
        throw new RuntimeException(e);
      }
      return allTables;
    });
    if (CollectionUtils.isNotEmpty(allCHTablesByDB)) {
      exits.set(allCHTablesByDB.contains(tableName));
    } else {
      try (JdbcConnection jdbcConnection = createJdbcConnection(tenantId, 10 * 60 * 1000)) {
        jdbcConnection.query(String.format(queryTableSQL, dbName, tableName), r -> exits.set(r.next()));
      } catch (SQLException e) {
        log.error("dws checkCHTableExists error dbUrl:{},tableName:{}", chDbUrl, tableName, e);
        throw new RuntimeException(e);
      }
    }
    return exits.get();
  }


  /**
   * 针对新企业验证添加路由
   *
   * @param newTenantIds
   */
  public void checkAndAddChRouter(List<String> newTenantIds) {
    if (CollectionUtils.isNotEmpty(newTenantIds)) {
      List<String> haveNoRouteEis = newTenantIds.stream()
                                                .filter(tenantId -> StringUtils.isEmpty(clickhouseTenantPolicy.getCHJdbcURL(tenantId)))
                                                .toList();
      Map<Integer, String> eiToEaMap = clickHouseUtilService.getEiToEaMap(haveNoRouteEis);
      if (CollectionUtils.isNotEmpty(haveNoRouteEis)) {
        Multimap<String, DBSyncInfoDO> dbSyncInfoDOS = dbSyncInfoDao.queryDbSyncInfoByTenantId(haveNoRouteEis);
        if (dbSyncInfoDOS != null) {
          dbSyncInfoDOS.forEach((tenantId, dbSyncInfo) -> {
            if (dbSyncInfo == null) {
              log.warn("tenantId:{},DBSyncInfoDO is null!", tenantId);
              return;
            }
            boolean standalone = !Objects.equals("public", dbSyncInfo.getPgSchema());
            clickHouseUtilService.createChRoute(tenantId, dbSyncInfo.getChDB(), standalone, eiToEaMap);
          });
        }
      }
    }
  }

  /**
   * 检测table的column是否存在
   * @param tenantId
   * @param dbName
   * @param tableName
   * @param columnNames
   * @return
   */
  public boolean tableColumnCheck(String tenantId, String dbName, String tableName, List<String> columnNames) {
    if(CollectionUtils.isEmpty(columnNames))
      return true;
    String querySql = String.format("select count() from system.columns where database = '%s' and table = '%s' and name in ('%s') ;", dbName, tableName, Joiner.on("','")
                                                                                                                                                               .join(columnNames));
    List<Integer> cList = this.executeQuerySQL(tenantId, querySql, res -> {
      try {
        return res.getInt(1);
      } catch (SQLException e) {
        log.error("tableColumnCheck executeQuerySQL error, tenantId:{}, querySql:{}", tenantId, querySql, e);
      }
      return 0;
    });
    return CollectionUtils.isNotEmpty(cList) && cList.get(0) == columnNames.size();
  }

}
