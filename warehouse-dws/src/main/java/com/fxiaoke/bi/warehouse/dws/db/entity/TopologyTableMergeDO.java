package com.fxiaoke.bi.warehouse.dws.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author:jief
 * @Date:2024/1/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "bi_mt_topology_table_merge")
public class TopologyTableMergeDO {
  @Column(name = "id")
  @Id
  private String id;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "status")
  private int status;
  @Column(name = "is_deleted")
  private int isDeleted;
  @Column(name = "version")
  private int version;
  @Column(name = "create_time")
  private long createTime;
  @Column(name = "last_modified_time")
  private long lastModifiedTime;
  /**
   * 计算的批次号
   */
  @Column(name="batch_num")
  private long batchNum;
  /**
   * 基于图中各个表的最新同步时间
   */
  @Column(name="latest_agg_time")
  private long latestAggTime;

  /**
   * 生成合并对象
   * @return TopologyTableMergeDO
   */
  public static TopologyTableMergeDO of(String tenantId,
                                        String statViewUniqKey,
                                        int version,
                                        int status,
                                        long batchNum,
                                        long lastAggTime) {
    return TopologyTableMergeDO.builder()
                               .tenantId(tenantId)
                               .id(statViewUniqKey)
                               .version(version)
                               .status(status)
                               .isDeleted(0)
                               .createTime(new Date().getTime())
                               .lastModifiedTime(new Date().getTime())
                               .batchNum(batchNum)
                               .latestAggTime(lastAggTime)
                               .build();
  }
}
