package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import com.facishare.paas.pod.dto.RouterInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Date 20240423
 * <AUTHOR>
 * @Desc 同步写入请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SynchronizationWriteArg {

  /**
   * 上游企业Id
   */
  private String upStreamTenantId;

  /**
   * 上游企业ch路由
   */
  private RouterInfo routerInfo;

  /**
   * 上游企业数据库dbName
   */
  private String dbName;

  /**
   * 同步的字段
   */
  private List<String> fieldList;

  /**
   * 同步字段上下游槽位映射
   */
  private Map<String, String> downToUpMap;

  /**
   * 图表viewId
   */
  private String viewId;

  /**
   * 策略id
   */
  private String policyId;

  public static SynchronizationWriteArg getSynchronizationWriteArg(String upStreamTenantId,
                                                                   RouterInfo routerInfo,
                                                                   String dbName,
                                                                   List<String> fieldList,
                                                                   Map<String, String> downToUpMap,
                                                                   String viewId,
                                                                   String policyId) {
    return SynchronizationWriteArg.builder()
                                  .upStreamTenantId(upStreamTenantId)
                                  .routerInfo(routerInfo)
                                  .dbName(dbName)
                                  .fieldList(fieldList)
                                  .downToUpMap(downToUpMap)
                                  .viewId(viewId)
                                  .policyId(policyId)
                                  .build();
  }

  /**
   * 获取写入租户的db jdbcUrl
   * @return
   */
  public String findMustUrl() {
    if (routerInfo != null) {
      return "jdbc:clickhouse://"+routerInfo.getMasterProxyUrl()+"/"+routerInfo.getDbName();
    }
    return null;
  }
}
