package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper;

import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BiDataSyncPolicyDo;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface BiDataSyncPolicyMapper extends ITenant<BiDataSyncPolicyMapper> {

    @Select("SELECT * FROM bi_data_sync_policy WHERE tenant_id = #{tenantId} AND status = 1 AND is_deleted = 0")
    List<BiDataSyncPolicyDo> getBiDataSyncPolicy(@Param("tenantId") String tenantId);

    @Select("SELECT * FROM bi_data_sync_policy WHERE tenant_id = #{tenantId} AND is_deleted = 1")
    List<BiDataSyncPolicyDo> getDeletedBiDataSyncPolicy(@Param("tenantId") String tenantId);

    @Select("SELECT * FROM bi_data_sync_policy WHERE policy_id=#{policyId} and tenant_id = #{tenantId}  AND is_deleted = 0 limit 1")
    BiDataSyncPolicyDo getBiDataSyncPolicyById(@Param("tenantId") String tenantId,@Param("policyId") String policyId);

    @Select("SELECT count(1) FROM bi_data_sync_policy WHERE tenant_id = #{tenantId} AND status = 1 AND is_deleted = 0")
    int getPolicySizeByTenantId(@Param("tenantId") String tenantId);
}
