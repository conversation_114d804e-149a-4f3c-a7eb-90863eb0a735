package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * @Author:jief
 * @Date:2023/9/9
 */
@Table(name = "bi_mt_topology_status")
@Data
public class BIMtTopologyStatusDO {
  @Id
  @Column(name = "id")
  private String id;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "source_id")
  private String sourceId;
  @Column(name = "last_visit_time")
  private Long  lastVisitTime;
  @Column(name = "last_visitor_id")
  private Integer lastVisitorId;
  @Column(name = "status")
  private Integer status;
  @Column(name = "visit_count")
  private Integer viewCount;
  @Column(name = "visit_from")
  private String visitFrom;
  @Column(name = "from_id")
  private String fromId;
  @Column(name = "is_deleted")
  private Integer isDeleted;
  @Column(name = "create_time")
  private Long createTime;
  @Column(name = "last_modified_time")
  private Long lastModifiedTime;

  public boolean activeInDays(int days) {
    if (lastVisitTime == null) {
      return false;
    }
    LocalDateTime localDateTime = LocalDateTime.now();
    localDateTime = localDateTime.plusDays(-days);
    LocalDateTime updateDateTime = new Timestamp(lastVisitTime).toLocalDateTime();
    return localDateTime.isBefore(updateDateTime);
  }
  public static BIMtTopologyStatusDO getInstance(){
    return new BIMtTopologyStatusDO();
  }
}
