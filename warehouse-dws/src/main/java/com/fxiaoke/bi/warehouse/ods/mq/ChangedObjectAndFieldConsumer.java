package com.fxiaoke.bi.warehouse.ods.mq;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ods.dao.UdfObjFieldDao;
import com.fxiaoke.bi.warehouse.ods.entity.ChangedObjectAndFieldMessage;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ChangedObjectAndFieldConsumer implements ApplicationListener<ContextRefreshedEvent> {

  private AutoConfMQPushConsumer consumer;
  @Autowired
  private UdfObjFieldDao udfObjFieldDao;

  @PostConstruct
  public void init() {
    log.warn("k8s ip:{}", ConfigHelper.getProcessInfo().getIp());
    consumer = new AutoConfMQPushConsumer("fs-bi-warehouse", "ODSObjectChangeConsumer", this::consumeMessage);
  }

  /**
   * 对象变更清除udf_obj_field缓存
   */
  private ConsumeOrderlyStatus consumeMessage(List<MessageExt> list, ConsumeOrderlyContext consumeConcurrentlyContext) {
    List<ChangedObjectAndFieldMessage> msgList = Lists.newArrayList();
    for (MessageExt messageExt : list) {
      ChangedObjectAndFieldMessage changedObjectAndFieldMessage = JSON.parseObject(messageExt.getBody(),
        ChangedObjectAndFieldMessage.class);
      msgList.add(changedObjectAndFieldMessage);
      changedObjectAndFieldMessage.setMsgId(messageExt.getMsgId());
    }
    // 去掉重复的
    msgList = msgList.stream().distinct().collect(Collectors.toList());
    msgList.forEach(udfObjFieldDao::removeCache);
    return ConsumeOrderlyStatus.SUCCESS;
  }

  @PreDestroy
  public void destroy() {
    if (null != consumer) {
      consumer.shutdown();
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (null == event.getApplicationContext().getParent()) {
      consumer.start();
    }
  }
}
