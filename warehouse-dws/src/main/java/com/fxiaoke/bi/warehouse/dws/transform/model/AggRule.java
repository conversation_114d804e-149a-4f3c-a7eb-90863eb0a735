package com.fxiaoke.bi.warehouse.dws.transform.model;


import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.helper.CollectionHelper;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AggRule {

  public static final AggRule empty = new AggRule();

  /**
   * 租户Id
   */
  public final String tenantId;
  /**
   * 是否在独立库
   */
  public final boolean standalone;
  /**
   * stat_field#id
   */
  public final String fieldId;
  /**
   * 规则Id
   */
  public final String ruleId;
  /**
   * 主题名称
   */
  public final String themeApiName;

  /**
   * 是否是无日期（指标聚合没有日期维度）
   */
  public final boolean noDate;
  /**
   * agg类型,agg, base_agg
   */
  public final String aggType;
  /**
   * value对象
   */
  public final String apiName;
  /**
   * object_id配置
   */
  public final ObjectIdRule objectIdRule;
  /**
   * value配置
   */
  public final ValueRule valueRule;
  /**
   * time配置
   */
  public final TimeRule timeRule;
  /**
   * where配置
   */
  public final WhereRules whereRules;
  /**
   * 处理对象映射
   */
  public final MappingService mappingService;

  /**
   * 时区 没有用final关键字 当多时区全网时候不用清理jvm 缓存和清理mongo 数据。
   * 有的规则比如排行榜规则在系统库中没有设置多时区
   */
  private String timeZone;
  /**
   * 扩展维度，只有目标有
   */
  private final List<String> xDimensions;
  /**
   * 只计算哪些数据ID的目标完成值
   */
  private final List<String> goalObjectIds;

  /**
   * new empty
   */
  private AggRule() {
    this.tenantId = null;
    this.standalone = false;
    this.fieldId = null;
    this.ruleId = null;
    this.themeApiName = null;
    this.noDate = false;
    this.aggType = null;
    this.apiName = null;
    this.xDimensions = null;
    this.goalObjectIds = null;
    this.objectIdRule = null;
    this.valueRule = null;
    this.timeRule = null;
    this.whereRules = null;
    this.mappingService = null;
    this.timeZone = null;
  }

  public AggRule(String tenantId,
                 boolean standalone,
                 String fieldId,
                 String ruleId,
                 String themeApiName,
                 boolean noDate,
                 String aggType,
                 String apiName,
                 List<String> xDimensions,
                 List<String> goalObjectIds,
                 ObjectIdRule objectIdRule,
                 ValueRule valueRule,
                 TimeRule timeRule,
                 WhereRules whereRules,
                 String timeZone,
                 MappingService mappingService) {
    this.tenantId = tenantId;
    this.standalone = standalone;
    this.fieldId = fieldId;
    this.ruleId = ruleId;
    this.themeApiName = themeApiName;
    this.noDate = noDate;
    this.aggType = aggType;
    this.apiName = apiName;
    this.xDimensions = xDimensions;
    this.goalObjectIds = goalObjectIds;
    this.objectIdRule = objectIdRule;
    this.valueRule = valueRule;
    this.timeRule = timeRule;
    this.whereRules = whereRules;
    this.timeZone = timeZone;
    this.mappingService = mappingService;
  }

  private String sqlFullTemplate;
  private String sqlTemplate;

  /**
   * 初始化
   */
  public void init() {
    this.objectIdRule.init(this);
    this.valueRule.init(this);
    if(null!=this.timeRule){
      this.timeRule.init(this);
    }
    if (null != whereRules) {
      this.whereRules.init(this);
    }
  }

  public String objectIdApiName() {
    if (objectIdRule.joinRelation != null) {
      return objectIdRule.joinRelation.apiName;
    }
    return apiName;
  }

  public String timeApiName() {
    if (timeRule.joinRelation != null) {
      return timeRule.joinRelation.apiName;
    }
    return apiName;
  }

  public String valueApiName() {
    if (valueRule.joinRelation != null) {
      return valueRule.joinRelation.apiName;
    }
    return apiName;
  }

  public boolean objectIdApiIsAggApi() {
    return objectIdRule.joinRelation == null;
  }

  public boolean timeApiIsAggApi() {
    return timeRule.joinRelation == null;
  }

  public boolean timeApiIsObjectIdApi() {
    if (objectIdRule.joinRelation == null) {
      return false;
    }
    return objectIdRule.joinRelation.equals(timeRule.joinRelation);
  }

  public boolean valueApiIsAggApi() {
    return valueRule.joinRelation == null;
  }

  public boolean valueApiIsObjectIdApi() {
    if (objectIdRule.joinRelation == null) {
      return false;
    }
    return objectIdRule.joinRelation.equals(valueRule.joinRelation);
  }

  public boolean valueApiIsTimeApi() {
    if (timeRule.joinRelation == null) {
      return false;
    }
    return timeRule.joinRelation.equals(valueRule.joinRelation);
  }

  public List<WhereRule> whereApiIsObjectIdApi() {
    if (objectIdRule.joinRelation == null) {
      return Lists.newArrayList();
    }
    if (whereRules == null) {
      return Lists.newArrayList();
    }
    List<WhereRule> result = Lists.newArrayList();
    whereRules.getWhereRulesList().forEach(wl -> {
      wl.forEach(w -> {
        if (objectIdRule.joinRelation.equals(w.joinRelation)) {
          result.add(w);
        }
      });
    });
    return result;
  }

  public List<WhereRule> whereApiIsTimeApi() {
    if (timeRule.joinRelation == null) {
      return Lists.newArrayList();
    }
    if (whereRules == null) {
      return Lists.newArrayList();
    }
    List<WhereRule> result = Lists.newArrayList();
    whereRules.getWhereRulesList().forEach(wl -> {
      wl.forEach(w -> {
        if (timeRule.joinRelation.equals(w.joinRelation)) {
          result.add(w);
        }
      });
    });
    return result;
  }

  public List<WhereRule> whereApiIsValueApi() {
    if (valueRule.joinRelation == null) {
      return Lists.newArrayList();
    }
    if (whereRules == null) {
      return Lists.newArrayList();
    }
    List<WhereRule> result = Lists.newArrayList();
    whereRules.getWhereRulesList().forEach(wl -> {
      wl.forEach(w -> {
        if (valueRule.joinRelation.equals(w.joinRelation)) {
          result.add(w);
        }
      });
    });
    return result;
  }

  public List<WhereRule> whereApiIsAggApi() {
    if (whereRules == null) {
      return Lists.newArrayList();
    }
    List<WhereRule> result = Lists.newArrayList();
    whereRules.getWhereRulesList().forEach(wl -> {
      wl.forEach(w -> {
        if (whereApiIsAggApi(w)) {
          result.add(w);
        }
      });
    });
    return result;
  }

  public boolean whereApiIsObjectApi(WhereRule whereRule) {
    if (whereRule.joinRelation == null) {
      return false;
    }
    if (whereRule.joinRelation.equals(objectIdRule.joinRelation)) {
      return true;
    }
    return false;
  }

  public boolean whereApiIsTimeApi(WhereRule whereRule) {
    if (whereRule.joinRelation == null) {
      return false;
    }
    if (whereRule.joinRelation.equals(timeRule.joinRelation)) {
      return true;
    }
    return false;
  }

  public boolean whereApiIsValueApi(WhereRule whereRule) {
    if (whereRule.joinRelation == null) {
      return false;
    }
    if (whereRule.joinRelation.equals(valueRule.joinRelation)) {
      return true;
    }
    return false;
  }

  public boolean whereApiIsPreWhereApi(WhereRule whereRule) {
    if (whereRule.joinRelation == null) {
      return false;
    }
    bb:
    for (ArrayList<WhereRule> wl : whereRules.getWhereRulesList()) {
      for (WhereRule w : wl) {
        if (w == whereRule) {
          break bb;
        }
        if (whereRule.joinRelation.equals(w.joinRelation)) {
          return true;
        }
      }
    }
    return false;
  }

  public boolean whereApiIsAggApi(WhereRule whereRule) {
    return whereRule.joinRelation == null;
  }

  /**
   * object_id列是否在扩展表上
   *
   * @return
   */
  public boolean objectIdInX() {
    if (standalone) {
      return false;
    }
    if (objectIdApiName().endsWith("__c")) {
      return false;
    }
    return Utils.isSlotColumn(objectIdRule.column);
  }

  public boolean timeInX() {
    if (standalone) {
      return false;
    }
    if (timeApiName().endsWith("__c")) {
      return false;
    }
    return Utils.isSlotColumn(timeRule.column);
  }

  public boolean whereInX(WhereRule whereRule) {
    if (standalone) {
      return false;
    }
    if (whereApiName(whereRule).endsWith("__c")) {
      return false;
    }
    if (Utils.isSlotColumn(whereRule.column)) {
      return true;
    }
    return false;
  }

  public String whereApiName(WhereRule whereRule) {
    if (whereRule.joinRelation == null) {
      return apiName;
    }
    return whereRule.joinRelation.apiName;
  }

  public boolean valueInX() {
    if (standalone) {
      return false;
    }
    if (valueApiName().endsWith("__c")) {
      return false;
    }
    return Utils.isSlotColumn(valueRule.column);
  }

  public boolean objectIdJoinInX() {
    if (standalone) {
      return false;
    }
    if (objectIdApiIsAggApi()) {
      return false;
    }
    if (apiName.endsWith("__c")) {
      return false;
    }
    return Utils.isSlotColumn(objectIdRule.joinRelation.column);
  }

  public boolean timeJoinInX() {
    if (standalone) {
      return false;
    }
    if (timeApiIsAggApi()) {
      return false;
    }
    if (apiName.endsWith("__c")) {
      return false;
    }
    return Utils.isSlotColumn(timeRule.joinRelation.column);
  }

  public boolean valueJoinInX() {
    if (standalone) {
      return false;
    }
    if (valueApiIsAggApi()) {
      return false;
    }
    if (apiName.endsWith("__c")) {
      return false;
    }
    return Utils.isSlotColumn(valueRule.joinRelation.column);
  }

  public boolean whereJoinInX(WhereRule whereRule) {
    if (standalone) {
      return false;
    }
    if (whereRule.joinRelation == null) {
      return false;
    }
    if (apiName.endsWith("__c")) {
      return false;
    }
    return Utils.isSlotColumn(whereRule.joinRelation.column);
  }

  public String objectIdTable() {
    String objectIdApiName = objectIdApiName();
    if (standalone) {
      return objectIdApiName;
    }
    if (objectIdApiName.endsWith("__c")) {
      return "object_data";
    }
    return objectIdApiName;
  }

  public String objectIdTableId() {
    String objectIdTable = objectIdTable();
    if ("object_data".equals(objectIdTable)) {
      return "_id";
    }
    return "id";
  }

  public String objectIdTableAlias() {
    return "object_id_table";
  }

  /**
   * object_id表扩展表SQL别名
   *
   * @return
   */
  public String objectIdXTableAlias() {
    if (standalone) {
      return "object_id_table";
    }
    if (objectIdApiName().endsWith("__c")) {
      return "object_id_table";
    }
    return "object_id_table_x";
  }

  /**
   * object_id列所在表的SQL别名
   *
   * @return
   */
  public String objectIdCTableAlias() {
    return objectIdInX() ? objectIdXTableAlias() : objectIdTableAlias();
  }

  /**
   * value列所在表SQL别名
   *
   * @return
   */
  public String valueCTableAlias() {
    if (valueApiIsObjectIdApi()) {
      return valueInX() ? objectIdXTableAlias() : objectIdTableAlias();
    }
    if (valueApiIsAggApi()) {
      return valueInX() ? xTableAlias() : tableAlias();
    }
    if (valueApiIsTimeApi()) {
      return valueInX() ? timeXTableAlias() : timeTableAlias();
    }
    return valueInX() ? valueXTableAlias() : valueTableAlias();
  }

  public String table() {
    if (standalone) {
      return apiName;
    }
    if (!apiName.endsWith("__c")) {
      return apiName;
    }
    return "object_data";
  }

  public String tableAlias() {
    if (objectIdApiIsAggApi()) {
      return "object_id_table";
    }
    return "agg_table";
  }

  public String xTableAlias() {
    if (objectIdApiIsAggApi()) {
      return objectIdXTableAlias();
    }
    if (standalone) {
      return tableAlias();
    }
    if (apiName.endsWith("__c")) {
      return tableAlias();
    }
    return tableAlias() + "_x";
  }

  public String timeJoinTableAlias() {
    if (timeRule.joinRelation == null) {
      return null;
    }
    String result = tableAlias();
    if (timeJoinInX()) {
      return result + "_x";
    }
    return result;
  }

  public String valueJoinTableAlias() {
    if (valueRule.joinRelation == null) {
      return null;
    }
    String result = tableAlias();
    if (valueJoinInX()) {
      return result + "_x";
    }
    return result;
  }

  public String whereJoinTableAlias(WhereRule whereRule) {
    if (whereRule.joinRelation == null) {
      return null;
    }
    return Utils.isSlotColumn(whereRule.joinRelation.column) ? xTableAlias() : tableAlias();
  }

  public boolean tableX() {
    if (standalone) {
      return false;
    }
    if (apiName.endsWith("__c")) {
      return false;
    }
    return true;
  }

  public String timeTable() {
    if (timeApiIsObjectIdApi()) {
      return objectIdTable();
    }
    if (timeApiIsAggApi()) {
      return table();
    }
    String timeApiName = timeRule.joinRelation.apiName;
    if (standalone) {
      return timeApiName;
    }
    if (timeApiName.endsWith("__c")) {
      return "object_data";
    } else {
      return timeApiName;
    }
  }

  public String timeTableId() {
    String timeTable = timeTable();
    if ("object_data".equals(timeTable)) {
      return "_id";
    }
    return "id";
  }

  public String valueTableId() {
    String valueTable = valueTable();
    if ("object_data".equals(valueTable)) {
      return "_id";
    }
    return "id";
  }

  public String timeTableAlias() {
    if (timeApiIsObjectIdApi()) {
      return objectIdTableAlias();
    }
    if (timeApiIsAggApi()) {
      return tableAlias();
    }
    return "time_table";
  }

  public String timeXTableAlias() {
    if (standalone) {
      return timeTableAlias();
    }
    if (timeApiName().endsWith("__c")) {
      return timeTableAlias();
    }
    return timeTableAlias() + "_x";
  }

  public String timeCTableAlias() {
    return Utils.isSlotColumn(timeRule.column) ? timeXTableAlias() : timeTableAlias();
  }

  public String valueTable() {
    if (valueRule.joinRelation == null) {
      return table();
    }
    String valueApiName = valueRule.joinRelation.apiName;
    if (standalone) {
      return valueApiName;
    }
    if (valueApiName.endsWith("__c")) {
      return "object_data";
    }
    return valueApiName;
  }

  public String valueTableAlias() {
    if (valueApiIsObjectIdApi()) {
      return objectIdTableAlias();
    }
    if (valueApiIsAggApi()) {
      return tableAlias();
    }
    if (valueApiIsTimeApi()) {
      return timeTableAlias();
    }
    return "value_table";
  }

  public String valueXTableAlias() {
    if (standalone) {
      return valueTableAlias();
    }
    if (valueApiName().endsWith("__c")) {
      return valueTableAlias();
    }
    return valueTableAlias() + "_x";
  }

  public String whereTable(WhereRule whereRule) {
    String whereApiName = whereApiName(whereRule);
    if (standalone) {
      return whereApiName;
    }
    if (whereApiName.endsWith("__c")) {
      return "object_data";
    }
    return whereApiName;
  }

  public String whereTableId(WhereRule whereRule) {
    String whereTable = whereTable(whereRule);
    if ("object_data".equals(whereTable)) {
      return "_id";
    }
    if ("org_employee_user".equals(whereTable)) {
      return "user_id";
    }
    if ("org_dept".equals(whereTable)) {
      return "dept_id";
    }
    return "id";
  }

  public String whereTableAlias(WhereRule whereRule) {
    if (whereApiIsObjectApi(whereRule)) {
      return objectIdTableAlias();
    }
    if (whereApiIsAggApi(whereRule)) {
      return tableAlias();
    }
    if (whereApiIsTimeApi(whereRule)) {
      return timeTableAlias();
    }
    if (whereApiIsValueApi(whereRule)) {
      return valueTableAlias();
    }
    DOUBLE_LOOP:
    for (ArrayList<WhereRule> wl : whereRules.getWhereRulesList()) {
      for (WhereRule w : wl) {
        if (w == whereRule) {
          break DOUBLE_LOOP;
        }
        if (whereRule.joinRelation.equals(w.joinRelation)) {
          return "where_table_" + w.getId();
        }
      }
    }
    return "where_table_" + whereRule.getId();
  }

  public String whereXTableAlias(WhereRule whereRule) {
    if (standalone) {
      return whereTableAlias(whereRule);
    }
    if (whereApiName(whereRule).endsWith("__c")) {
      return whereTableAlias(whereRule);
    }
    return whereTableAlias(whereRule) + "_x";
  }

  public String whereCTableAlias(WhereRule whereRule) {
    return Utils.isSlotColumn(whereRule.column) ? whereXTableAlias(whereRule) : whereTableAlias(whereRule);
  }

  /**
   * 唯一计数 且唯一计数列就是object_id列
   *
   * @return
   */
  public boolean quotaIsObjectId() {
    if (valueRule.getAggType() == AggType.countuniq || valueRule.getAggType() == AggType.countuniq2) {
      if (valueRule.column.equals(objectIdRule.column)) {
        if (valueRule.joinRelation == null && objectIdRule.joinRelation == null) {
          return true;
        } else {
          if (valueRule.joinRelation != null && objectIdRule.joinRelation != null) {
            if (valueRule.joinRelation.apiName.equals(objectIdRule.joinRelation.apiName)) {
              if (valueRule.joinRelation.column.equals(objectIdRule.joinRelation.column)) {
                return true;
              }
            }
          }
        }
      }
    }
    return false;
  }

  public boolean objectIdIsObjectReference() {
    return "object_reference".equals(objectIdRule.fieldType);
  }


  public String paasThemeApiName() {
    return mappingService.paasApiName(themeApiName);
  }


  /**
   * 某些表只监听符合条件的列，一般认为这种列创建后就不会变化
   *
   * @param table
   * @return
   */
  public String listenApiName(String table) {
    if (whereRules == null || whereRules.listenTableApiNameMap == null) {
      return null;
    }
    return whereRules.listenTableApiNameMap.get(table);
  }

  /**
   * 是否只侦听某些表的某些对象
   *
   * @param table
   * @param apiName
   * @return
   */
  public boolean listenApiName(String table, String apiName) {
    String listenApiName = listenApiName(table);
    if (listenApiName == null) {
      return true;
    }
    if (listenApiName.equals(apiName)) {
      return true;
    }
    return false;
  }

  public String goalRuleId() {
    int b = ruleId.indexOf('|');
    return ruleId.substring(0, b);
  }

  public String goalRuleDetailId() {
    int b = ruleId.indexOf('|');
    return ruleId.substring(b + 1);
  }

  public String l1Column() {
    if (CollectionHelper.isNotEmpty(xDimensions)) {
      if (xDimensions.size() > 1) {
        return xDimensions.get(0);
      }
    }
    return null;
  }

  public String l2Column() {
    if (CollectionHelper.isNotEmpty(xDimensions)) {
      if (xDimensions.size() > 2) {
        return xDimensions.get(1);
      }
    }
    return null;
  }

  public String l3Column() {
    if (CollectionHelper.isNotEmpty(xDimensions)) {
      if (xDimensions.size() > 3) {
        return xDimensions.get(2);
      }
    }
    return null;
  }
}
