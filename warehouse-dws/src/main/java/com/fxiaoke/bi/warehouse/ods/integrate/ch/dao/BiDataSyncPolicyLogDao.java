package com.fxiaoke.bi.warehouse.ods.integrate.ch.dao;

import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.BiDataSyncPolicyLogDo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.mapper.BiDataSyncPolicyLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Slf4j
@Repository
public class BiDataSyncPolicyLogDao {

    @Resource
    private BiDataSyncPolicyLogMapper biDataSyncPolicyLogMapper;

    public void insertBiDataSyncPolicyLog(BiDataSyncPolicyLogDo biDataSyncPolicyLogDo) {
        try {
            biDataSyncPolicyLogMapper.setTenantId(biDataSyncPolicyLogDo.getTenantId()).insert(biDataSyncPolicyLogDo);
        } catch (Exception e) {
            log.error("insertBiDataSyncPolicyLog error biDataSyncPolicyLogDo is {}", biDataSyncPolicyLogDo);
        }
    }
}
