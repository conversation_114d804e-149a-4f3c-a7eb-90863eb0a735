package com.fxiaoke.bi.warehouse.dws.strategy;

import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/7/7
 */
@Component
public class CmsAggRemainDayStrategy implements AggRemainDayStrategy {
  private static Map<String, Integer> aggRemainDays;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("bi-statictic-agg", new IniChangeListener("agg_data2_refactor") {
      @Override
      public void iniChanged(IniConfig c) {
        Map<String, String> aggRemainDaysMap = Splitter
          .on(CharMatcher.anyOf(", |"))
          .trimResults()
          .omitEmptyStrings()
          .withKeyValueSeparator(':')
          .split(c.get("agg.remain.days", ""));

        Map<String, Integer> newAggRemainDays = Maps.newHashMap();
        aggRemainDaysMap.forEach((k, v) -> {
          newAggRemainDays.put(k, Integer.parseInt(v));
        });
        aggRemainDays = newAggRemainDays;

        aggRemainDaysMap.forEach((k, v) -> {
          AggRemainStrategyFactory.register(k, CmsAggRemainDayStrategy.this);
        });
      }
    });
  }


  @Override
  public String minActionDate(String tenantId, String themeApiName,String timeZone) {
    Integer result = aggRemainDays.get(tenantId + "^" + themeApiName);
    if (result == null) {
      return Constants.ACTION_DATE_EMPTY;
    }
    return Utils.formatActionDateTz(Utils.fromNow(-result, 0,timeZone),timeZone);
  }
}
