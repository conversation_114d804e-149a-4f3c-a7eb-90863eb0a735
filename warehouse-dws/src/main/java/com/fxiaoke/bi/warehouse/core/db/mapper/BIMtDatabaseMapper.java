package com.fxiaoke.bi.warehouse.core.db.mapper;

import com.fxiaoke.bi.warehouse.core.db.entity.BIMtDatabaseDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Author:jief
 * @Date:2024/10/10
 */
@Mapper
public interface BIMtDatabaseMapper extends IBatchMapper<BIMtDatabaseDO>, ITenant<BIMtDatabaseMapper> {

  @Select("""
    select * from bi_mt_database where id=#{id} and tenant_id=#{tenantId} and status='active'
    """)
  BIMtDatabaseDO queryDatabaseById(@Param("id") String id,@Param("tenantId") String tenantId);
}
