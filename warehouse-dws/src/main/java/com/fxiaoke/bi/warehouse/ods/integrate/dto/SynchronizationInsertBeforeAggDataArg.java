package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 同步before数据插入的请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SynchronizationInsertBeforeAggDataArg {

    /**
     * 上游企业Id
     */
    private String upStreamTenantId;

    /**
     * 上游企业路由
     */
    private RouterInfo routerInfo;

    /**
     * 同步批次
     */
    private Long batchNum;

    /**
     * 统计图图表id
     */
    private String viewId;

    /**
     * 统计图版本
     */
    private int viewVersion;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 同步的字段
     */
    private List<String> fieldList;

    /**
     * 反查的hashCode
     */
    private List<String> hashCodeList;

    public static SynchronizationInsertBeforeAggDataArg getSynchronizationInsertBeforeAggDataArg(AggDataSyncInfoDo aggDataSyncInfoDo, String upStreamTenantId, RouterInfo routerInfo, String dbName, List<String> fieldList) {
        return SynchronizationInsertBeforeAggDataArg.builder()
                                                    .upStreamTenantId(upStreamTenantId)
                                                    .routerInfo(routerInfo)
                                                    .batchNum(aggDataSyncInfoDo.getBatchNum())
                                                    .viewId(aggDataSyncInfoDo.getViewId())
                                                    .viewVersion(aggDataSyncInfoDo.getViewVersion())
                                                    .dbName(dbName)
                                                    .fieldList(fieldList)
                                                    .build();
    }
}
