package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.db.er.CustomDimType;
import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.ObjectConfigManager;
import com.fxiaoke.bi.warehouse.common.util.SQLUtil;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.StatFieldMapper;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import com.fxiaoke.bi.warehouse.dws.exception.ParseRuleException;
import com.fxiaoke.bi.warehouse.dws.model.QuoteTargetField;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.sqlgenerator.MtTagViewTable;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.ods.dao.UdfObjFieldDao;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class DimRuleDao extends RuleDao {
  @Autowired
  private StatFieldMapper statFieldMapper;
  @Autowired
  private UdfObjFieldMapper udfObjFieldMapper;
  @Autowired
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Resource
  private MappingService mappingService;
  private Set<String> chAggDataFixSlot = Sets.newHashSet();
  @Autowired
  private UdfObjFieldDao udfObjFieldDao;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", new IniChangeListener("common") {
      @Override
      public void iniChanged(IniConfig iniConfig) {
        String chAggFixSlot = iniConfig.get("ch.agg.fix.slot", "owner,data_auth_code,out_tenant_id,out_data_auth_code");
        chAggDataFixSlot = Sets.newHashSet(Splitter.on(",").splitToList(chAggFixSlot));
      }
    });
  }

  /**
   * 获取主题的action_date 槽位
   *
   * @param tenantId
   * @param schemaId
   * @return
   */
  public StatFieldDO findActionDateStatField(String tenantId, String schemaId) {
    return statFieldMapper.setTenantId(tenantId).queryActionDateField(tenantId, schemaId);
  }

  /**
   * 对象负责人字段
   *
   * @param tenantId
   * @param schemaId
   * @param themeApiName
   * @return
   */
  public StatFieldDO findOwnerStatField(String tenantId, String schemaId, String themeApiName) {
    List<StatFieldDO> ownerStatFields = statFieldMapper.setTenantId(tenantId)
                                                       .queryDimFieldByFieldName(tenantId, "'owner'", schemaId, themeApiName);
    if (CollectionUtils.isNotEmpty(ownerStatFields)) {
      return ownerStatFields.get(0);
    }
    return null;
  }

  /**
   * 获取统计图中所有的维度规则，包含主从对象
   *
   * @param tenantId       租户id
   * @param statViewEntity 统计图实体
   * @return
   */
  public StatViewDimBO buildStatViewDimBo(String tenantId, StatViewEntity statViewEntity) {
    boolean standalone = mybatisTenantPolicy.standalone(tenantId);
    String themeApiName = statViewEntity.getThemeName();
    Map<String, List<DisplayField>> apiNameDisplayFieldMap = statViewEntity.getDimFields()
                                                                           .stream()
                                                                           .collect(Collectors.groupingBy(DisplayField::getDescribeApiName));
    StatViewDimBO statViewDimBO = StatViewDimBO.builder().tenantId(tenantId).themeApiName(themeApiName).build();
    //获取依赖master对象主题的维度
    List<UdfObjFieldDO> masterDbObjLookupField = udfObjFieldMapper.setTenantId(tenantId)
                                                                  .findUdfDbFieldInfoByType(tenantId, themeApiName, mappingService.getApiXName(themeApiName), FieldType.MASTER_DETAIL);
    Set<String> usedSlotName = Sets.newHashSet();//记录已经使用的固定槽位
    if (CollectionUtils.isNotEmpty(masterDbObjLookupField)) {
      UdfObjFieldDO lookupMasterField = masterDbObjLookupField.get(0);
      String masterObjName = mappingService.udfApiName2ApiName(lookupMasterField.getRefObjName());
      List<DisplayField> masterDisplayFields = apiNameDisplayFieldMap.get(masterObjName);
      if (CollectionUtils.isNotEmpty(masterDisplayFields)) {
        masterDisplayFields.forEach(displayField -> {
          if (this.chAggDataFixSlot.contains(displayField.getDbFieldName())) {
            displayField.setDstColumnName(displayField.getDbFieldName());
            usedSlotName.add(displayField.getDbFieldName());
          }
        });
        List<DimRule> masterDimRule = this.createStatViewDimRule(tenantId, masterObjName, standalone, masterDisplayFields);
        statViewDimBO.setMasterApiName(masterObjName);
        statViewDimBO.setMasterDimRules(masterDimRule);
        statViewDimBO.setLookupField(lookupMasterField);
      }
    }
    //处理主题对象的字段
    List<DisplayField> themeDisplayFields = apiNameDisplayFieldMap.get(themeApiName);
    if (CollectionUtils.isNotEmpty(themeDisplayFields)) {
      themeDisplayFields.forEach(displayField -> {
        if (this.chAggDataFixSlot.contains(displayField.getDbFieldName()) &&
            !usedSlotName.contains(displayField.getDbFieldName())) {
          displayField.setDstColumnName(displayField.getDbFieldName());
        }
      });
      List<DimRule> themeDimRules = this.createStatViewDimRule(tenantId, themeApiName, standalone, themeDisplayFields);
      statViewDimBO.setThemeDimRules(themeDimRules);
    }

    //处理action_date维度
    StatFieldDO actionDateField = statFieldMapper.setTenantId(tenantId)
                                                 .queryActionDateField(tenantId, statViewEntity.getSchemaId());
    if (actionDateField == null) {
      throw new ParseRuleException(String.format("can not find action_date from tenantId:%s,schemaId:%s,themeApiName:%s", tenantId, statViewEntity.getSchemaId(), themeApiName));
    }
    statViewDimBO.setWithActionDate(true);
    statViewDimBO.setActionDate(actionDateField);

    //标签维度，暂时作为主题对象查找关联的标签维度
    if (CollectionUtils.isNotEmpty(statViewEntity.getTagDimFields())) {
      List<DimRule> tagDimRule = MtTagViewTable.createMtTagDimRules(tenantId, standalone, themeApiName, statViewEntity.getTagDimFields());
      List<DimRule> themeDimRule = statViewDimBO.getThemeDimRules();
      if (themeDimRule == null) {
        statViewDimBO.setThemeDimRules(tagDimRule);
      } else {
        themeDimRule.addAll(tagDimRule);
        statViewDimBO.setThemeDimRules(themeDimRule);
      }
    }
    // 自定义维度
    List<DisplayField> customDimFields = statViewEntity.getCustomDimFields();
    if (CollectionUtils.isNotEmpty(customDimFields)) {
      List<DimRule> customDimRules = this.createCustomDimRule(tenantId, themeApiName, standalone, customDimFields);
      statViewDimBO.setCustomDimRules(customDimRules);
    }
    return statViewDimBO;
  }

  /**
   * 更具statField 创建dimeRule集合
   *
   * @param tenantId
   * @param describeApiName
   * @param displayFields
   * @return
   */
  public List<DimRule> createStatViewDimRule(String tenantId,
                                             String describeApiName,
                                             boolean standalone,
                                             List<DisplayField> displayFields) {
    //排除调action_date 字段因为每个指标都计算了action_date这一列,如果是schema隔离的租户需要将value改成id
    Map<String, DisplayField> displayFieldMap = Maps.newHashMap();
    displayFields.forEach(displayField -> {
      if (Objects.equals("action_date", displayField.getDbFieldName())) {
        return;
      }
      displayFieldMap.put(displayField.getDbFieldName(), displayField);
    });
    String[] dbFieldNames = displayFieldMap.keySet().toArray(new String[0]);
    List<UdfObjFieldDO> udfObjFieldDOS = udfObjFieldMapper.setTenantId(tenantId)
                                                          .batchQueryFieldByObjNameAndDbFieldName(Integer.parseInt(tenantId), describeApiName, ObjectConfigManager.getExtendObjName(describeApiName), dbFieldNames);
    List<DimRule> dimRules = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(udfObjFieldDOS)) {
      Map<String, Map<String, ColumnDefinition>> tableColumnDefinitionMap = Maps.newHashMap();
      udfObjFieldDOS.forEach(udfObjFieldDO -> {
        DisplayField displayField = displayFieldMap.get(udfObjFieldDO.getDbFieldName());
        DimRule dimRule = this.buildDimRule(tenantId, describeApiName, udfObjFieldDO, tableColumnDefinitionMap, displayField.getStatFieldId(), displayField.getDstColumnName(), displayField.getDisPlayType());
        if (GrayManager.isAllowByRule("whatListDim", tenantId) && "related_api_names".equals(udfObjFieldDO.getDbFieldName()) && "select_many".equals(udfObjFieldDO.getType())) {
          UdfObjFieldDO whatObjIdField = udfObjFieldDao.findObjIdFieldByObjName(tenantId, describeApiName);
          StatFieldDO statFieldDO = statFieldMapper.setTenantId(tenantId)
                                                   .queryObjectIdFieldByApiName(tenantId, describeApiName);
          DimRule whatListObjIdDimRule = this.buildDimRule(tenantId, describeApiName, whatObjIdField, tableColumnDefinitionMap, statFieldDO.getFieldId(), Constants.OBJECT_ID, DisplayField.DisplayType.valueOf(FieldType.GROUP));
          dimRules.add(whatListObjIdDimRule);
        }
        if (dimRule != null) {
          dimRules.add(dimRule);
        }
        if (displayField.isLookupName()) {
          DimRule lookupNameDimRule = this.buildLookupNameRule(tenantId, standalone, describeApiName, udfObjFieldDO, displayField.getStatFieldId(), null, tableColumnDefinitionMap, displayField.getDisPlayType());
          if (lookupNameDimRule != null) {
            dimRules.add(lookupNameDimRule);
          }
        }
      });
    }
    return dimRules;
  }

  /**
   * 创建自定义维度集合
   */
  private List<DimRule> createCustomDimRule(String tenantId,
                                            String describeApiName,
                                            boolean standalone,
                                            List<DisplayField> customDimFields) {
    Map<String, DisplayField> udfFieldIdFieldMap = customDimFields.stream()
                                                                  .collect(Collectors.toMap(DisplayField::getStatFieldId, Function.identity(), (a, b) -> b));
    Set<String> udfFieldIdList = udfFieldIdFieldMap.keySet();
    List<UdfObjFieldDO> udfObjFieldDOS = udfObjFieldMapper.setTenantId(tenantId)
                                                          .findFieldByFieldIds(Integer.valueOf(tenantId), SQLUtil.generateInExpress(Lists.newArrayList(udfFieldIdList)));
    if (CollectionUtils.isEmpty(udfObjFieldDOS)) {
      return new ArrayList<>();
    }
    Map<String, UdfObjFieldDO> fieldIdUdfFieldMap = udfObjFieldDOS.stream()
                                                                  .collect(Collectors.toMap(UdfObjFieldDO::getFieldId, Function.identity(), (a, b) -> b));
    Map<String, Map<String, ColumnDefinition>> tableColumnDefinitionMap = Maps.newHashMap();
    List<DimRule> customDimRules = new ArrayList<>();
    for (DisplayField customDimField : customDimFields) {
      String udfFieldId = customDimField.getStatFieldId();
      UdfObjFieldDO udfObjFieldDO = fieldIdUdfFieldMap.get(udfFieldId);
      if (CustomDimType.reference.name().equals(customDimField.getCustomType())) {
        UdfObjFieldDO relationUdfField = udfObjFieldMapper.setTenantId(tenantId).findFieldByStatFieldIds(Integer.valueOf(tenantId), customDimField.getRelationFieldId());
        DimRule lookupNameDimRule = this.buildCustomReferenceRule(tenantId, standalone, udfObjFieldDO, udfFieldId, tableColumnDefinitionMap, customDimField.getDisPlayType(), relationUdfField);
        if (lookupNameDimRule != null) {
          lookupNameDimRule.getDimFieldRule().customType = customDimField.getCustomType();
          lookupNameDimRule.getDimFieldRule().dimensionConfig = customDimField.getDimensionConfig();
          lookupNameDimRule.getDimFieldRule().dimensionId = customDimField.getDimensionId();
          customDimRules.add(lookupNameDimRule);
        }
        continue;
      }
      DimRule dimRule = this.buildDimRule(tenantId, describeApiName, udfObjFieldDO, tableColumnDefinitionMap, udfFieldId, customDimField.getDstColumnName(), customDimField.getDisPlayType());
      if (dimRule != null) {
        dimRule.getDimFieldRule().customType = customDimField.getCustomType();
        dimRule.getDimFieldRule().dimensionConfig = customDimField.getDimensionConfig();
        dimRule.getDimFieldRule().dimensionId = customDimField.getDimensionId();
        customDimRules.add(dimRule);
      }
    }
    return customDimRules;
  }

  /**
   * 自定义维度引用类型
   */
  private DimRule buildCustomReferenceRule(String tenantId,
                                           boolean standalone,
                                           UdfObjFieldDO udfObjFieldDO,
                                           String lookupFieldId,
                                           Map<String, Map<String, ColumnDefinition>> tableColumnDefinitionMap,
                                           DisplayField.DisplayType displayType,
                                           UdfObjFieldDO relationUdfField) {
    String type = udfObjFieldDO.getType();
    String dbObjName = udfObjFieldDO.getDbObjName();
    String dbFieldName = udfObjFieldDO.getDbFieldName();
    if (StringUtils.isBlank(dbObjName)) {
      log.error("udfObjFieldDO dbObjName is blank tenantId:{},dbObjName:{},dbFieldName:{},refObjName,type:{} is empty!", tenantId, dbObjName, dbFieldName, type);
      return null;
    }
    if (dbObjName.endsWith("_udef")) {
      dbObjName = mappingService.udfApiName2ApiName(dbObjName);
    }
    String refColumn = relationUdfField.getDbFieldName();
    String fieldLocation = relationUdfField.getFieldLocation();
    if (Objects.nonNull(fieldLocation) && fieldLocation.matches("\\d+")) {
      if (Integer.parseInt(fieldLocation) > 0) {
        refColumn = "value" + fieldLocation;
      }
    }
    JoinRelation joinRelation = new JoinRelation(refColumn, "reference", dbObjName, AggJoinType.LEFT, PGColumnType.String);
    QuoteTargetField quoteTargetField = this.buildQuoteField(tenantId, standalone, dbObjName, dbFieldName, tableColumnDefinitionMap);
    if (quoteTargetField != null) {
      QuoteOfAgg<Object> quoteOfAgg = QuoteOfAgg.builder()
                                                .joinRelation(joinRelation)
                                                .column(quoteTargetField.column)
                                                .columnType(quoteTargetField.columnType)
                                                .isSingle(quoteTargetField.isSingle)
                                                .fieldType(quoteTargetField.fieldType)
                                                .build();
      DimFieldRule dimFieldRule = DimFieldRule.builder()
                                              .tenantId(tenantId)
                                              .apiName(dbObjName)
                                              .fieldId(lookupFieldId + "_name")
                                              .fieldName(udfObjFieldDO.getFieldName() + "_udef_name")
                                              .dbFieldName(dbFieldName + "_udef_name")
                                              .dbObjName(dbObjName)
                                              .column(quoteOfAgg.column)
                                              .joinRelation(quoteOfAgg.joinRelation)
                                              .columnType(quoteOfAgg.columnType)
                                              .fieldType(quoteOfAgg.fieldType)
                                              .isSingle(quoteOfAgg.isSingle)
                                              .displayType(displayType)
                                              .build();
      return DimRule.builder().dimFieldRule(dimFieldRule).build();
    }
    return null;
  }

  /**
   * 生成查找关联字段转name
   *
   * @param tenantId                 租户id
   * @param standalone               是否schema隔离
   * @param describeApiName          对象apiName
   * @param udfObjFieldDO            需要转name的字段描述，一般是object_reference，master_detail employee,department类型
   * @param lookupFieldId            字段在stat_field表中的 id
   * @param dstColumnName            写入agg_data 中的固定槽位名称
   * @param tableColumnDefinitionMap 表物理字段cache
   * @return
   */
  public DimRule buildLookupNameRule(String tenantId,
                                     boolean standalone,
                                     String describeApiName,
                                     UdfObjFieldDO udfObjFieldDO,
                                     String lookupFieldId,
                                     String dstColumnName,
                                     Map<String, Map<String, ColumnDefinition>> tableColumnDefinitionMap,
                                     DisplayField.DisplayType displayType) {
    String type = udfObjFieldDO.getType();
    if (Constants.SINGLE_BI_LOOKUP_TYPE.contains(type)) {
      String targetApiName = udfObjFieldDO.getRefObjName();
      if (StringUtils.isBlank(targetApiName)) {
        log.error("tenantId:{},dbObjName:{},dbFieldName:{},refObjName,type:{} is empty!", tenantId, udfObjFieldDO.getDbObjName(), udfObjFieldDO.getDbFieldName(), type);
        return null;
      }
      if (targetApiName.endsWith("_udef")) {
        targetApiName = mappingService.udfApiName2ApiName(targetApiName);
      }
      JoinRelation joinRelation = this.createRelation(tenantId, standalone, describeApiName, udfObjFieldDO, targetApiName, tableColumnDefinitionMap, AggJoinType.LEFT);
      String refTargetField = udfObjFieldDO.getRefTargetField();
      QuoteTargetField quoteTargetField = this.buildQuoteField(tenantId, standalone, targetApiName, refTargetField, tableColumnDefinitionMap);
      if (quoteTargetField != null) {
        QuoteOfAgg quoteOfAgg = QuoteOfAgg.builder()
                                          .joinRelation(joinRelation)
                                          .column(quoteTargetField.column)
                                          .columnType(quoteTargetField.columnType)
                                          .isSingle(quoteTargetField.isSingle)
                                          .fieldType(quoteTargetField.fieldType)
                                          .build();
        DimFieldRule dimFieldRule = DimFieldRule.builder()
                                                .tenantId(tenantId)
                                                .apiName(describeApiName)
                                                .fieldId(lookupFieldId + "_name")
                                                .dstColumnName(dstColumnName)
                                                .fieldName(udfObjFieldDO.getFieldName() + "_udef_name")
                                                .dbFieldName(udfObjFieldDO.getDbFieldName() + "_udef_name")
                                                .dbObjName(udfObjFieldDO.getDbObjName())
                                                .column(quoteOfAgg.column)
                                                .joinRelation(quoteOfAgg.joinRelation)
                                                .columnType(quoteOfAgg.columnType)
                                                .fieldType(quoteOfAgg.fieldType)
                                                .isSingle(quoteOfAgg.isSingle)
                                                .displayType(displayType)
                                                .build();
        return DimRule.builder().dimFieldRule(dimFieldRule).build();
      }
    }
    return null;
  }

  /**
   * statFiled 创建dimRule集合
   *
   * @param tenantId        租户id
   * @param describeApiName 对象apiName
   * @param dbFieldNameMap  db_field_name 和 field_id 映射关系
   * @return
   */
  public List<DimRule> createDimRule(String tenantId,
                                     String describeApiName,
                                     Map<String/*dbFieldName*/, String/*fieldId*/> dbFieldNameMap,
                                     DisplayField.DisplayType displayType) {
    String[] dbFieldNames = dbFieldNameMap.keySet().toArray(new String[0]);
    List<UdfObjFieldDO> udfObjFieldDOS = udfObjFieldMapper.setTenantId(tenantId)
                                                          .batchQueryFieldByObjNameAndDbFieldName(Integer.parseInt(tenantId), describeApiName, ObjectConfigManager.getExtendObjName(describeApiName), dbFieldNames);
    List<DimRule> dimRules = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(udfObjFieldDOS)) {
      Map<String, Map<String, ColumnDefinition>> tableColumnDefinitionMap = Maps.newHashMap();
      udfObjFieldDOS.forEach(udfObjFieldDO -> {
        DimRule dimRule = this.buildDimRule(tenantId, describeApiName, udfObjFieldDO, tableColumnDefinitionMap, dbFieldNameMap.get(udfObjFieldDO.getDbFieldName()), null, displayType);
        if (dimRule != null) {
          dimRules.add(dimRule);
        }
      });
    }
    return dimRules;
  }

  /**
   * 创建dimRule
   *
   * @param tenantId        企业id
   * @param describeApiName apiName
   * @param displayFields   展示字段
   * @return dimRule集合
   */
  public List<DimRule> createDimRule(String tenantId,
                                     String describeApiName,
                                     List<DisplayField> displayFields,
                                     Map<String, Map<String, ColumnDefinition>> tableColumnDefinitionMap) {
    Map<String, DisplayField> displayFieldMap = displayFields.stream()
                                                             .collect(Collectors.toMap(field ->
                                                               field.getDescribeApiName() + "^" +
                                                               field.getDbFieldName(), Function.identity()));
    String[] dbFieldNames = displayFields.stream().map(DisplayField::getDbFieldName).toList().toArray(new String[0]);
    List<UdfObjFieldDO> udfObjFieldDOS = udfObjFieldMapper.setTenantId(tenantId)
                                                          .batchQueryFieldByObjNameAndDbFieldName(Integer.parseInt(tenantId), describeApiName, ObjectConfigManager.getExtendObjName(describeApiName), dbFieldNames);
    List<DimRule> dimRules = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(udfObjFieldDOS)) {
      udfObjFieldDOS.forEach(udfObjFieldDO -> {
        DisplayField displayField = displayFieldMap.get(describeApiName + "^" + udfObjFieldDO.getDbFieldName());
        DimRule dimRule = this.buildDimRule(tenantId, describeApiName, udfObjFieldDO, tableColumnDefinitionMap, displayField.getStatFieldId(), displayField.getDstColumnName(), displayField.getDisPlayType());
        if (dimRule != null) {
          dimRules.add(dimRule);
        }
      });
    }
    return dimRules;
  }


  /**
   * 构建 dimRule 结构
   *
   * @param tenantId                 租户id
   * @param themeApiName             主题名称
   * @param udfObjFieldDO            元数据对象
   * @param tableColumnDefinitionMap table元数据 cache
   * @return
   */
  public DimRule buildDimRule(String tenantId,
                              String themeApiName,
                              UdfObjFieldDO udfObjFieldDO,
                              Map<String, Map<String, ColumnDefinition>> tableColumnDefinitionMap,
                              String fieldId,
                              String dstColumnName,
                              DisplayField.DisplayType displayType) {
    boolean isStandalone = mybatisTenantPolicy.standalone(tenantId);
    String refObjName = udfObjFieldDO.getRefObjName();
    String relationTable = udfObjFieldDO.getRelationTable();
    if (StringUtils.isNotBlank(refObjName) && refObjName.endsWith("_udef")) {
      refObjName = ObjectConfigManager.getPreObjName(refObjName);
    }
    QuoteOfAgg quoteOfAgg = null;
    if ("quote".equals(udfObjFieldDO.getType())) {
      quoteOfAgg = this.buildFromQuote(tenantId, isStandalone, themeApiName, refObjName, udfObjFieldDO.getRelationKeyField(), udfObjFieldDO.getRefTargetField(), tableColumnDefinitionMap);
    } else if ("group".equals(udfObjFieldDO.getType()) && StringUtils.isNotBlank(relationTable)) {
      JoinRelation relation = JoinRelation.builder()
                                          .apiName(relationTable)
                                          .column("id")
                                          .joinType(AggJoinType.LEFT)
                                          .build();
      quoteOfAgg = QuoteOfAgg.builder()
                             .joinRelation(relation)
                             .column("target_data_id")
                             .columnType(PGColumnType.String)
                             .isSingle(true)
                             .fieldType(FieldType.TEXT)
                             .build();
    } else if (GrayManager.isAllowByRule("whatListDim", tenantId) && "related_api_names".equals(udfObjFieldDO.getDbFieldName()) && "select_many".equals(udfObjFieldDO.getType())) {
      // what list 字段需要将 related_api_names 替换为 target_api_name，创建一个引用关系
      UdfObjFieldDO relatedObjField = udfObjFieldDao.findWhatListRelatedObjField(tenantId, udfObjFieldDO.getDbObjName());
      if (Objects.nonNull(relatedObjField)) {
        JoinRelation relation = JoinRelation.builder()
                                            .apiName(relatedObjField.getRelationTable())
                                            .column("id")
                                            .joinType(AggJoinType.LEFT)
                                            .build();
        quoteOfAgg = QuoteOfAgg.builder()
                               .joinRelation(relation)
                               .column("target_api_name")
                               .columnType(PGColumnType.String)
                               .isSingle(true)
                               .fieldType(FieldType.TEXT)
                               .build();
      }
    } else {
      String dbColumn = udfObjFieldDO.getDbFieldName();
      //加载一下，看看是否是槽位列
      int dbFieldLocation = Integer.parseInt(udfObjFieldDO.getFieldLocation());
      if (dbFieldLocation > 0) {
        dbColumn = "value" + dbFieldLocation;
      }
      if ("value0".equals(dbColumn) && isStandalone) {
        dbColumn = "id";
      }
      PGColumnType dbColumnType;
      String dbFieldType = udfObjFieldDO.getType();
      if (StringUtils.equalsAny(dbFieldType, "formula", "count")) {
        String udfFieldType = udfObjFieldDO.getFieldType();
        if (StringUtils.isNotBlank(udfObjFieldDO.getReturnType()) &&
              ("Date".equals(udfFieldType) || GrayManager.isAllowByRule("dim_rule_return_type", tenantId))) {
          udfFieldType = udfObjFieldDO.getReturnType();
        }
        dbFieldType = StringUtils.isBlank(udfFieldType) ? FieldType.TEXT : udfFieldType.toLowerCase();
      }
      //员工部门的引用需要单独处理一下
      boolean isSingle = this.isSingleValue(dbFieldType, dbColumn, udfObjFieldDO.getIsSingle());
      if (themeApiName.equals("active_record") && "related_api_names".equalsIgnoreCase(dbColumn)) {
        dbColumnType = PGColumnType.ARRAY_String;
      } else {
        String table = themeApiName;
        if (table.endsWith("__c") && !isStandalone) {
          table = "object_data";
        }
        Map<String, ColumnDefinition> tableColumnDefinitions = findTableColumnDefinitions(tenantId, isStandalone, table, tableColumnDefinitionMap);
        ColumnDefinition definition = tableColumnDefinitions.get(dbColumn);
        if (definition == null) {
          dbColumnType = PGColumnType.String;
        } else {
          dbColumnType = definition.columnType();
        }
      }
      boolean enableMultiLang = Objects.equals(udfObjFieldDO.getEnableMultiLang(), true);
      quoteOfAgg = QuoteOfAgg.builder()
                             .joinRelation(null)
                             .column(dbColumn)
                             .columnType(dbColumnType)
                             .fieldType(dbFieldType)
                             .isSingle(isSingle)
                             .enableMultiLang(enableMultiLang)
                             .build();
    }
    if (quoteOfAgg == null) {
      throw new ParseRuleException(String.format("buildDimRule quoteOfAgg is null tenantId:%s,themeApiName:%s,udfObjFieldDO:%s", tenantId, themeApiName, JSON.toJSONString(udfObjFieldDO)));
    }
    DimFieldRule dimFieldRule = DimFieldRule.builder()
                                            .tenantId(tenantId)
                                            .apiName(themeApiName)
                                            .fieldId(fieldId)
                                            .dstColumnName(dstColumnName)
                                            .fieldName(udfObjFieldDO.getFieldName())
                                            .dbFieldName(udfObjFieldDO.getDbFieldName())
                                            .dbObjName(udfObjFieldDO.getDbObjName())
                                            .column(quoteOfAgg.column)
                                            .joinRelation(quoteOfAgg.joinRelation)
                                            .columnType(quoteOfAgg.columnType)
                                            .fieldType(quoteOfAgg.fieldType)
                                            .isSingle(quoteOfAgg.isSingle)
                                            .enableMultiLang(quoteOfAgg.enableMultiLang)
                                            .displayType(displayType)
                                            .originalType(udfObjFieldDO.getType())
                                            .sameFieldIds(Sets.newHashSet(fieldId))
                                            .build();
    return DimRule.builder().dimFieldRule(dimFieldRule).build();
  }
}
