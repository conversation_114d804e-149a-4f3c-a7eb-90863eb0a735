package com.fxiaoke.bi.warehouse.dws.transform.model;

/**
 * topology merge 状态
 *
 * @Author:jief
 * @Date:2024/6/27
 */
public enum MergeStatusEnum {
  PREPARE(0),
  CAL(1),
  /**
   * 不用计算
   */
  NONeedCal(2);

  MergeStatusEnum(int status) {
    this.status = status;
  }

  private final int status;

  public int getStatus() {
    return status;
  }

  public static MergeStatusEnum from(int status) {
    switch (status) {
      case 0 -> {
        return PREPARE;
      }
      case 1 -> {
        return CAL;
      }
      case 2 -> {
        return NONeedCal;
      }
      default -> throw new RuntimeException("no support this status:"+status);
    }
  }
}
