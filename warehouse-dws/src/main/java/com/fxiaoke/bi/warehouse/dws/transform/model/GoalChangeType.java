package com.fxiaoke.bi.warehouse.dws.transform.model;

/**
 * @Author: zhaomh
 * @Description: 目标值是否变更:0->只计算业务数据;1->业务和goal_value_obj都要计算;2->只计算goal_value_obj变更数据
 * @Date: Created in 2024/9/27
 * @Modified By:
 */
public enum GoalChangeType {
  /**
   * 全量计算,实际并没有用到此判断
   */
  DATA_ALL(-1),
  /**
   * 业务数据增量计算
   */
  DATA_INC(0),
  /**
   * 新增目标全量计算+原始业务数据增联计算
   */
  GOAL_ALL_DATA_INC(1),
  /**
   * 新增目标全量计算
   */
  GOAL_ALL(2);

  GoalChangeType(int type) {
    this.type = type;
  }

  private final int type;

  public int getType() {
    return type;
  }

  public static GoalChangeType from(int type) {
    switch (type) {
      case -1 -> {
        return DATA_ALL;
      }
      case 0 -> {
        return DATA_INC;
      }
      case 1 -> {
        return GOAL_ALL_DATA_INC;
      }
      case 2 -> {
        return GOAL_ALL;
      }
      default -> throw new RuntimeException("GoalChangeType no support this type:"+type);
    }
  }
}
