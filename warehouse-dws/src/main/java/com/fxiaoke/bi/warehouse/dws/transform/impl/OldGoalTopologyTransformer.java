package com.fxiaoke.bi.warehouse.dws.transform.impl;

import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.model.StatViewPreArg;
import com.fxiaoke.bi.warehouse.dws.model.StatViewPreSQL;
import com.fxiaoke.bi.warehouse.dws.transform.TopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.model.TransformContext;
import org.springframework.stereotype.Component;

/**
 * 老目标转换成bi_mt_topology_table的转换器
 */
@Component
public class OldGoalTopologyTransformer extends TopologyTransformer {
  @Override
  public TopologyTableDO transform(TransformContext context) {
    return null;
  }
  @Override
  public void upsertTopologyTable(TransformContext context){

  }

  public StatViewPreSQL createStatViewPreSQL(StatViewPreArg statViewPreArg){
    return null;
  }
}
