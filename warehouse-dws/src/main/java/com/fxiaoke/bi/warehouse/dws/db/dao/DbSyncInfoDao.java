package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.db.mapper.DBSyncInfoMapper;
import com.google.common.collect.LinkedListMultimap;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/5/29
 */
@Slf4j
@Repository
public class DbSyncInfoDao {
  @Autowired
  private DBSyncInfoMapper dbSyncInfoMapper;
  @Resource(name = "mybatisTenantPolicy")
  private MybatisTenantPolicy mybatisTenantPolicy;

  public Multimap<String,DBSyncInfoDO> queryDbSyncInfoByTenantId(List<String> tenantIds) {
    Multimap<String, DBSyncInfoDO> dbSyncInfoDOS = LinkedListMultimap.create();
    if (CollectionUtils.isEmpty(tenantIds)) {
      return dbSyncInfoDOS;
    }
    Map<String, RouterInfo> routerInfoMap = mybatisTenantPolicy.batchQueryRouterInfo(tenantIds);
    if (MapUtils.isNotEmpty(routerInfoMap)) {
      routerInfoMap.values().stream().distinct().forEach(routeInfo -> {
        Boolean standalone = routeInfo.getStandalone();
        String schemaName = (standalone == null || !standalone) ?
          "public" :
          String.format("sch_%s", routeInfo.getTenantId());
        String jdbcURL = routeInfo.getJdbcUrl();
        DBSyncInfoDO dbSyncInfoDO = this.queryDBSyncInfoByPgDb(jdbcURL, schemaName);
        dbSyncInfoDOS.put(routeInfo.getTenantId(), dbSyncInfoDO);
      });
    }
    return dbSyncInfoDOS;
  }
  /**
   * 根据pgdb和schema反查db同步信息
   * @param pgDb pgdb
   * @param pgSchema schema
   * @return
   */
  private DBSyncInfoDO queryDBSyncInfoByPgDb(String pgDb, String pgSchema){
    return dbSyncInfoMapper.setTenantId("-1").queryDBSyncInfoByPgDb(pgDb,pgSchema);
  }

  public List<String> getAllCHDbList(){
    return dbSyncInfoMapper.setTenantId("-1").queryChDBList();
  }

  public DBSyncInfoDO querySchemaDBSyncInfoByCHDB(String chDB){
    return dbSyncInfoMapper.setTenantId("-1").querySchemaDBSyncInfoByCHDB(chDB);
  }
}
