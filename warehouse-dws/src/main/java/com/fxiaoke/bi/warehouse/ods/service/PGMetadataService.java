package com.fxiaoke.bi.warehouse.ods.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.facishare.paas.pod.dto.SimpleRouterInfo;
import com.fxiaoke.bi.warehouse.common.bean.UserCenterService;
import com.fxiaoke.bi.warehouse.common.component.MybatisBITenantPolicy;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.DateTimeUtil;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.ods.args.CHPublicCreatorArg;
import com.fxiaoke.bi.warehouse.ods.bean.DBColumnType;
import com.fxiaoke.bi.warehouse.ods.bean.PGSchema;
import com.fxiaoke.bi.warehouse.ods.context.*;
import com.fxiaoke.bi.warehouse.ods.dao.MetaDataDao;
import com.fxiaoke.bi.warehouse.ods.dao.UdfObjFieldDao;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.utils.InitSQL;
import com.fxiaoke.common.Pair;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.CharMatcher;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PGMetadataService {
  @Resource
  private PgDataSource pgDataSource;
  @Resource
  private MetaDataDao metaDataDao;
  @Resource
  private CHMetadataService chMetadataService;
  @Resource
  private UdfObjFieldDao udfObjFieldDao;
  @Autowired
  private DbRouterClient dbRouterClient;
  @Resource
  private CHDBService chdbService;
  @Resource
  private UserCenterService userCenterService;
  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
  private static final String schemaSQL = "SELECT pn.oid AS schema_oid, iss.catalog_name, iss.schema_owner, iss" +
    ".schema_name FROM information_schema.schemata iss INNER JOIN pg_namespace pn ON pn.nspname = iss.schema_name " +
    "where iss.schema_name='public' OR iss.schema_name like 'sch_%'";

  private static final String queryTables =
    "select tablename from pg_tables where schemaname=? and tablename like " + "'object\\_%'";

  private static final String queryStandaloneSchemas="SELECT schema_name FROM information_schema.schemata  where schema_name like 'sch\\_%'";

  private Set<String> blackTableList = Sets.newHashSet();

  private Set<String> whiteTableList = Sets.newHashSet();

  /**
   * 从 paas-bi-mapping.sync.tables 获取需要同步的表结构
   */
  private Set<String> syncTableSets = Sets.newHashSet();
  /**
   * schema独立租户的表列表
   */
  private Map<String, Set<String>> standaloneWhiteTables = Maps.newHashMap();
  /**
   * 匹配自定义字段删除的槽位名称
   */
  private static final Pattern delUdfFieldRegx = Pattern.compile("^value[0-9]+_[0-9a-zA-Z]+__del_[0-9]+$");
  private static final Pattern tenantIdRegx = Pattern.compile("^[1-9]\\d*$");
  private static final Pattern extTableRegx = Pattern.compile("(.*)_ext_[0-9]+$");
  private final LoadingCache<String, Optional<PGSchema>> cache = Caffeine.newBuilder()
                                                                         .maximumSize(10000)
                                                                         .expireAfterWrite(30, TimeUnit.MINUTES)
                                                                         .build(key -> {
                                                                           String[] keys = key.split("\\^");
                                                                           PGSchema pgSchema = loadSchema(keys[0],
                                                                             keys[1] + "." + keys[2]);
                                                                           if (pgSchema == null) {
                                                                             return Optional.empty();
                                                                           } else {
                                                                             return Optional.of(pgSchema);
                                                                           }
                                                                         });
  /**
   * 公共库的列表
   */
  private final Cache<String, List<String>> publicTables = Caffeine.newBuilder()
                                                                   .maximumSize(1000)
                                                                   .expireAfterWrite(3, TimeUnit.HOURS)
                                                                   .build();
  private long maxTenantId;

  @SuppressWarnings("UnstableApiUsage")
  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
      String whiteTables = config.get("sysnc_table_white_list", "");
      String blackTables = config.get("sysnc_table_black_list", "");
      whiteTableList = Splitter.on(",").omitEmptyStrings().splitToStream(whiteTables).collect(Collectors.toSet());
      blackTableList = Splitter.on(",").omitEmptyStrings().splitToStream(blackTables).collect(Collectors.toSet());
      //独立schema企业的白名单列表
      String whiteTableByEi = config.get("sync_table_standalone_tables", "");
      Map<String, Set<String>> tmp = Maps.newHashMap();
      Splitter.on(CharMatcher.anyOf(",|")).omitEmptyStrings().splitToList(whiteTableByEi).forEach(key -> {
        String[] eiAndApiName = key.split("\\^");
        if (eiAndApiName.length >= 2) {
          tmp.computeIfAbsent(eiAndApiName[0], k -> Sets.newHashSet()).add(eiAndApiName[1]);
        }
      });
      standaloneWhiteTables = tmp;
      maxTenantId = config.getLong("max_tenantId", 2147483647L);
    });
    // 从paas-bi配置获取要同步的表
    ConfigFactory.getConfig("paas-bi-mapping", config -> {
      List<String> paas2BITableList = Splitter.on(",")
                                              .omitEmptyStrings()
                                              .splitToStream(config.get("sync.tables"))
                                              .toList();
      syncTableSets = Sets.newHashSet(paas2BITableList);
    });
  }

  /**
   * 获取pgtable 得schema对象
   *
   * @param key
   * @return
   */
  public Optional<PGSchema> loadSchema(String key) {
    return cache.get(key);
  }

  /**
   * 作废缓存信息
   *
   * @param key
   */
  public void invalidateSchema(String key) {
    cache.invalidate(key);
  }

  /**
   * 根据bipgdb获取该db合法的租户id，用于数据拉取。
   *
   * @param transferEvent bi pg db 和schema
   * @return
   */
  public List<String> findValidaTenantId(TransferEvent transferEvent) {
    if ("public".equals(transferEvent.getSchema())) {
      String url = transferEvent.getDbURL();
      String pgDBName = CHContext.getDBName(url);
      List<SimpleRouterInfo> simpleRouterInfos = dbRouterClient.queryRoutersByDb(url.substring(18),
        MybatisBITenantPolicy.BIZ, MybatisBITenantPolicy.DIALECT);
      if (CollectionUtils.isNotEmpty(simpleRouterInfos)) {
        return simpleRouterInfos.stream()
                                .map(routerInfo -> {
                                  String tenantId = routerInfo.getTenantId();
                                  if (tenantId != null && tenantIdRegx.matcher(tenantId).matches() &&
                                    Long.parseLong(tenantId) <= maxTenantId) {
                                    return tenantId;
                                  }
                                  return null;
                                })
                                .filter(ei -> StringUtils.isNotBlank(ei) &&
                                  (GrayManager.isAllowByRule("use_ch_warehouse_pgdb", pgDBName) ||
                                    GrayManager.isAllowByRule("use_ch_warehouse", ei)) && this.isValidate(pgDBName, ei))
                                .toList();
      }
    } else if (transferEvent.getSchema().startsWith("sch_")) {
      String pgDBName = CHContext.getDBName(transferEvent.getDbURL());
      String tenantId = transferEvent.getSchema().substring(4);
      if (GrayManager.isAllowByRule("use_ch_warehouse", tenantId) ||
        GrayManager.isAllowByRule("use_ch_warehouse_pgdb", pgDBName)) {
        return Lists.newArrayList(tenantId);
      }
    }
    return Lists.newArrayList();
  }

  /**
   * 灰度，检测企业状态
   * @param pgDBName
   * @param ei
   * @return
   */
  public boolean isValidate(String pgDBName,String ei){
    if(GrayManager.isAllowByRule("check_tenant_status",pgDBName)){
      return userCenterService.isValidateStatus(ei);
    }
    return true;
  }
  /**
   * 根据tenantId 获取 pg JdbcUrl
   */
  public String getPgJdbcUrl(String tenantId) {
    return pgDataSource.getPgBouncerJdbcUrlByTenantId(tenantId);
  }

  /**
   * 是否是schema隔离
   */
  public boolean isSchema(String tenantId) {
    return pgDataSource.isStandalone(tenantId);
  }

  /**
   * 生成全量表的CH建表语句，放到tomcat目录下
   */
  public void createCHInitSqL(CHPublicCreatorArg chPublicCreatorArg,String fileDir) {
    TransferEvent transferEvent = new TransferEvent("", chPublicCreatorArg.getPgDbUrl(),
      chPublicCreatorArg.getSchemaName());
    List<String> tableList;
    if (CollectionUtils.isNotEmpty(chPublicCreatorArg.getTables())) {
      tableList = chPublicCreatorArg.getTables();
    } else {
      tableList = Lists.newArrayList(this.findAllPgTableExcludeStatTable(transferEvent));
      tableList.addAll(WarehouseConfig.initChTables);
      tableList.add("agg_data");
      tableList.add("agg_data_history");
      tableList.add("bi_mt_data_tag_v");
      tableList.add("v_saleactionstage");
      tableList.add("goal_value_obj_snapshot");
      tableList.add("biz_account_main_data");
      tableList.add("biz_user_login_online_operation_ods");
      tableList.add("biz_user_login_online_operation");
      tableList.add("biz_user_api_name_operation_ods");
      tableList.add("biz_user_api_name_operation");
      tableList.add("biz_user_bi_operation_ods");
      tableList.add("biz_user_bi_operation");
      tableList.add("dim_name_data");
      tableList.add("bi_agg_log");
      tableList.add("agg_downstream_data");
      tableList.add("bi_data_sync_policy_log");
      tableList.add("stage_runtime_task_new_opportunity");
      tableList.add("stage_runtime_new_opportunity");
      tableList.add("object_data");
      tableList.add("object_data_lang");
      tableList.add("agg_data_sync_info");
    }
    tableList = tableList.stream().distinct().collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(tableList)) {
      String sqlFilePath;
      if(StringUtils.isBlank(fileDir)){
        String catalinaHome = System.getProperty("catalina.home");
        if (StringUtils.isBlank(catalinaHome)) {
          log.error("catalina.home is empty can not createCHInitSqL {}", JSON.toJSONString(chPublicCreatorArg));
          return;
        }
        sqlFilePath = catalinaHome + File.separatorChar + "logs" + File.separatorChar + "ch_" + LocalDateTime.now().format(formatter) + ".sql";
      }else{
        sqlFilePath = fileDir+ "ch_" + LocalDateTime.now().format(formatter) + ".sql";
      }
      log.info("sqlFieldPath is:{}", sqlFilePath);
      try (FileWriter writer = new FileWriter(sqlFilePath, StandardCharsets.UTF_8)) {
        int count = 0;
        for (String table : tableList) {
          try {
            String tableName = table.toLowerCase();
            String sql = this.createAllCHTableFromPg(chPublicCreatorArg.getTenantId(), tableName, CommonUtils.getDBName(chPublicCreatorArg.getChDBName()), chPublicCreatorArg.getChCluster(), CHContext.REPLICATED_REPLACING_MERGE_TREE, chPublicCreatorArg.isNeedCreate());
            if (StringUtils.isNotBlank(sql)) {
              if (chPublicCreatorArg.isNeedCreate()) {
                chdbService.executeUpsertOnCH(chPublicCreatorArg.getChDBName(), new String[] {sql});
                //todo 如果是 dim_sys_date 或 dim_sys_area_gray 需要提前预设好数据
              }
              writer.write(sql + ";");
              writer.write("\n\n");
            } else {
              log.error("create sql error tableName:{}", tableName);
            }
            count++;
          } catch (Exception e) {
            log.error("create ch sql error pgDbUrl:{},chDBUrl:{},tableName:{}", chPublicCreatorArg.getPgDbUrl(), chPublicCreatorArg.getChDBName(), table, e);
          }
        }
        writer.flush();
        log.info("create sql finish pgDbUrl:{}, count:{}", chPublicCreatorArg.getPgDbUrl(), count);
      } catch (Exception e) {
        log.error("create CH init sql error {}", JSON.toJSONString(chPublicCreatorArg), e);
      }
    }
  }

  /**
   * 根据pg表生成相应的ch表
   * todo tenantId 要支持 ei=-1
   *
   * @param tenantId 企业id 预置表的话只需要从非schema隔离的企业id即可
   * @param table    表名称
   */
  public String createCHTableFromPg(String tenantId,
                                    String table,
                                    String chDB,
                                    String chCluster,
                                    String tableEngine) throws Exception {
    String schemaName = "public";
    if (pgDataSource.isStandalone(tenantId)) {
      schemaName = "sch_" + tenantId;
    }
    PGSchema schema = checkTable(tenantId, schemaName, table);
    return getChCreateTableSql(tenantId, table, chDB, chCluster, tableEngine, schemaName, schema);
  }

  /**
   * 创建全部表，包含没有时间列和ei tenant_id列的表
   *
   * @return ch建表SQL
   */
  public String createAllCHTableFromPg(String tenantId,
                                       String table,
                                       String chDB,
                                       String chCluster,
                                       String tableEngine,
                                       boolean needCreate) throws Exception {
    if (Objects.equals(table, "agg_data")) {
      return InitSQL.aggDataSQL.formatted(table);
    }
    if (Objects.equals(table, "agg_data_history")) {
      return InitSQL.aggDataSQL.formatted(table);
    }
    if (Objects.equals(table, "bi_mt_data_tag_v")) {
      return InitSQL.mtDataTagVSQL;
    }
    if (Objects.equals(table, "v_saleactionstage")) {
      return InitSQL.v_saleactionstage_sql;
    }
    String jdbcUrl = pgDataSource.getPgBouncerJdbcUrlByTenantId(tenantId);
    String schemaName = pgDataSource.isStandalone(tenantId) ? "sch_" + tenantId : "public";
    CHStaticSQL staticSQL = CHStaticSQL.of(table);
    if(staticSQL!=null){
     return staticSQL.buildSQL(chMetadataService,metaDataDao,tenantId,schemaName,chCluster,needCreate);
    }
    PGSchema schema = this.loadSchema(jdbcUrl, schemaName + "." + table);
    if (Objects.isNull(schema)) {
      log.error("无数据库表的schema信息 租户：{}，表名：{}.{}", tenantId, schemaName, table);
      return null;
    }
    //根据表名称反查对象apiName
    return getChCreateTableSql(tenantId, table, chDB, chCluster, tableEngine, schemaName, schema);
  }

  /**
   * 拼接CH建表SQL
   */
  private String getChCreateTableSql(String tenantId,
                                     String table,
                                     String chDB,
                                     String chCluster,
                                     String tableEngine,
                                     String schemaName,
                                     PGSchema schema) throws Exception {
    //根据表名称反查对象apiName
    String apiName = table;
    if (table.endsWith("__c")) {
      String dbObjName = udfObjFieldDao.findObjNameByTable(tenantId, table);
      if (StringUtils.isNotBlank(dbObjName)) {
        apiName = dbObjName;
      }
    }
    Map<String/*table column*/, UdfObjFieldDO> udfObjFieldDOMap = this.createUdfObjFieldMapper(tenantId, apiName);
    List<DBColumnType> dbColumnTypes = metaDataDao.findAllPgTypeByTable(tenantId, table, schemaName);
    if (CollectionUtils.isEmpty(dbColumnTypes)) {
      log.error("find all pg type by table is empty tenantId:{},table:{},schemaName:{}", tenantId, table, schemaName);
      return null;
    }
    //如果是删除的自定义槽位字段则跳过
    List<DBColumnType> dbColumnWithoutDelSlot = dbColumnTypes.stream()
                                                             .filter(dbColumn -> !delUdfFieldRegx.matcher(dbColumn.getColumnName())
                                                                                                 .matches())
                                                             .toList();
    StringBuilder chSQL = new StringBuilder("CREATE TABLE IF NOT EXISTS ");
    chSQL.append(table);
    if (StringUtils.isNotBlank(chCluster)) {
      chSQL.append(" ON CLUSTER '").append(chCluster).append("'");
    }
    chSQL.append("( ");
    String timeZone = chMetadataService.getSysTimeZone(tenantId);
    AtomicInteger unm = new AtomicInteger(0);
    dbColumnWithoutDelSlot.forEach(dbColumnType -> {
      this.dealWithColumnType(dbColumnType, schema, timeZone, table, udfObjFieldDOMap);
      //如果是bi表需要将create_time 和 last_modified_time 转成int64类型
      chSQL.append("\n").append("`").append(dbColumnType.getColumnName()).append("`").append("\t");
      if (StringUtils.equalsAny(dbColumnType.getColumnName(), CHContext.CREATE_TIME, CHContext.LAST_MODIFIED_TIME)) {
        chSQL.append(CHDataType.Int64);
      } else {
        chSQL.append(dbColumnType.getUdtName());
      }
      if (CHContext.NO.equalsIgnoreCase(dbColumnType.getIsNullable())) {
        chSQL.append("\t").append("NOT NULL");
      }
      //处理默认值问题
      this.appendDefault(dbColumnType, chSQL);
      int fieldNums = unm.incrementAndGet();
      if (fieldNums < dbColumnWithoutDelSlot.size()) {
        chSQL.append(", ");
      }
    });
    //增加扩展字段
    this.appendExtColumn(table, chSQL,chDB);
    //增加默认索引owner字段的索引
    this.createIndex(dbColumnWithoutDelSlot, chSQL);
    chSQL.append("\n) ");
    //定义表引擎
    chSQL.append(this.createCHEngine(tableEngine, CHContext.BI_SYS_VERSION));
    //定义分区
    if (!WarehouseConfig.noNeedPartitionTableSet.contains(table)) {
      chSQL.append("\n").append(this.createCHPartitionBy());
    }
    //定义order by
    chSQL.append("\n").append(this.createCHOrderBy(schema));
    // appendExtColumn 默认会加上 bi_sys_flag 字段
    this.createTTL(chSQL,dbColumnWithoutDelSlot, chDB, table);
    chSQL.append("\n").append("SETTINGS index_granularity = 8192");
    return chSQL.toString();
  }

  public void createTTL(StringBuilder chSQL, List<DBColumnType> dbColumnWithoutDelSlot, String chDB, String table) {
    Optional<DBColumnType> isDelDBColumnType = dbColumnWithoutDelSlot.stream()
                                                                     .filter(dbColumnType -> "is_deleted".equals(dbColumnType.getColumnName()))
                                                                     .findAny();
    String filter = " bi_sys_flag = 0 ";
    if (isDelDBColumnType.isPresent()) {
      if (StringUtils.contains(isDelDBColumnType.get().getUdtName(), "Int")) {
        filter = " (bi_sys_flag = 0 OR is_deleted IN (-1, -2))";
      } else if (StringUtils.contains(isDelDBColumnType.get().getUdtName(), "Bool")) {
        filter = " (bi_sys_flag = 0 OR is_deleted = 1)";
      }
    }
    if (!WarehouseConfig.skipBeforeTableSet.contains(table)) {
      chSQL.append("\n").append(String.format("TTL bi_sys_version + INTERVAL 15 DAY DELETE WHERE %s = '%s' AND %s",WarehouseConfig.ODS_PARTITION_KEY,WarehouseConfig.STOCK_PARTITION_NAME,filter));
      chSQL.append(",");
      chSQL.append("\n")
           .append(String.format("bi_sys_version + INTERVAL 3 DAY DELETE WHERE %s in('%s','%s')", WarehouseConfig.ODS_PARTITION_KEY, WarehouseConfig.INC_PARTITION_NAME,WarehouseConfig.CAL_PARTITION_NAME));
    } else {
      chSQL.append("\n").append(String.format("TTL bi_sys_version + INTERVAL 15 DAY DELETE WHERE %s", filter));
    }
  }
  /**
   * 检查表是否符合同步条件<br>
   * 1. 是否包含 ei \ tenant_id<br>
   * 2. 是否包含时间列<br>
   * 不符合则抛出异常信息
   *
   * @return {@link PGSchema} 检验通过返回数据库表的schema信息
   */
  public PGSchema checkTable(String tenantId, String schemaName, String table) throws SQLException {
    String jdbcUrl = pgDataSource.getPgBouncerJdbcUrlByTenantId(tenantId);
    PGSchema schema = this.loadSchema(jdbcUrl, schemaName + "." + table);
    if (schema == null) {
      throw new RuntimeException(String.format("find schema is null tenantId:%s,table:%s,schemaName:%s", tenantId,
        table, schemaName));
    }
    List<String> primaryKeys = schema.getPrimaryKeys();
    if (CollectionUtils.isEmpty(primaryKeys)) {
      throw new RuntimeException(String.format("this table primaryKeys is empty tenantId:%s,table:%s,schemaName:%s",
        tenantId, table, schemaName));
    }
    Pair<String, String> filterTime = schema.findFilterTimeColumn();
    if (filterTime == null) {
      throw new RuntimeException(String.format(
        "this table filter time field is empty tenantId:%s,table:%s," + "schemaName:%s", tenantId, table, schemaName));
    }
    return schema;
  }

  /**
   * 创建默认索引
   *
   * @param dbColumnWithoutDelSlot 字段集合
   * @param chSQL                  chSql buffer
   */
  private void createIndex(List<DBColumnType> dbColumnWithoutDelSlot, StringBuilder chSQL) {
    Optional<String> ownerOp = dbColumnWithoutDelSlot.stream()
                                                     .map(DBColumnType::getColumnName)
                                                     .filter(columnName -> Objects.equals(columnName, "owner"))
                                                     .findAny();
    if (ownerOp.isPresent()) {
      chSQL.append(",\n");
      chSQL.append("INDEX idx_owner owner TYPE set(1000) GRANULARITY 1 ");
    }
  }

  /**
   * 处理列类型
   *
   * @param dbColumnType     pg列类型
   * @param schema           pgtable schema信息
   * @param timeZone         时区
   * @param table            表名
   * @param udfObjFieldDOMap
   */
  private void dealWithColumnType(DBColumnType dbColumnType,
                                  PGSchema schema,
                                  String timeZone,
                                  String table,
                                  Map<String/*table column*/, UdfObjFieldDO> udfObjFieldDOMap) {
    String columnName = dbColumnType.getColumnName();
    UdfObjFieldDO udfObjFieldDO = udfObjFieldDOMap.get(columnName);
    if (udfObjFieldDO != null) {
      if ("owner".equals(udfObjFieldDO.getDbFieldName()) && "org_employee_user".equals(udfObjFieldDO.getRefObjName()) &&
        PaasFieldType.OBJECT_REFERENCE.equals(udfObjFieldDO.getType())) {
        udfObjFieldDO.setType(PaasFieldType.EMPLOYEE);
      }
    }
    //如果sys_modified_time 设置默认值 last_modified_time * 1000
    if (CHContext.SYS_MODIFIED_TIME.equalsIgnoreCase(columnName)) {
      dbColumnType.setIsNullable(CHContext.NO);
      //object_data 表的last_modified_time 写入ch后回转成int64
      dbColumnType.setColumnDefault("toUnixTimestamp64Micro(now64(9))");
    }
    if (Objects.equals(table, "dim_udfobj_enum") && "ei".equalsIgnoreCase(columnName)) {
      dbColumnType.setIsNullable(CHContext.NO);
    }
    //如果last_modified_time 在表引擎中出现则这个字段不要设置成nullable
    if (CHContext.LAST_MODIFIED_TIME.equalsIgnoreCase(columnName)) {
      dbColumnType.setIsNullable(CHContext.NO);
    }
    if ("base_crmfeedrelation".equalsIgnoreCase(table) && "feed_id".equalsIgnoreCase(columnName)) {
      dbColumnType.setUdtName(PGDataType.VARCHAR);
    }
    //如果udf_obj_field 不为空并且type为日期和时间类型，但是pg table列类型为 numeric 类型的话统一映射到CH的Int8
    if (udfObjFieldDO != null && StringUtils.equalsAnyIgnoreCase(udfObjFieldDO.getType(), "date", "date_time") &&
      "numeric".equalsIgnoreCase(dbColumnType.getUdtName())) {
      dbColumnType.setUdtName(PGDataType.INT8);
    }
    // goal_value_obj.annual_value描述有问题，需要特殊兼容一下
    if (table.startsWith("goal_value_obj") && "annual_value".equals(columnName)) {
      dbColumnType.setUdtName(PGDataType.NUMERIC);
      dbColumnType.trans2CHType(ZoneId.of(timeZone), null);
      return;
    }
    //转化成ch对应的字段类型
    dbColumnType.trans2CHType(ZoneId.of(timeZone), udfObjFieldDO == null ? null : udfObjFieldDO.getType());
  }

  /**
   * 处理默认值
   *
   * @param dbColumnType pg列类型对象
   * @param chSQL        sql
   */
  private void appendDefault(DBColumnType dbColumnType, StringBuilder chSQL) {
    String defaultValue = dbColumnType.getColumnDefault();
    String chDefaultValue = dbColumnType.getColumnDefault();
    if (StringUtils.isNotBlank(defaultValue) && !dbColumnType.getColumnDefault().contains("NULL")) {
      if (defaultValue.contains("::")) {
        chDefaultValue = defaultValue.split("::")[0];
        if (StringUtils.isNotBlank(dbColumnType.getDataType()) &&
          dbColumnType.getDataType().startsWith(PGDataType.TIMESTAMP)) {
          long timestampDefaultValue = DateTimeUtil.convertToTimestamp(chDefaultValue.replace("'", ""));
          chDefaultValue = String.valueOf(timestampDefaultValue);
        }
      }
      //处理从序列获取默认值
      if (!dbColumnType.getColumnDefault().contains("nextval")) {
        chSQL.append("\t").append("DEFAULT").append("\t").append(chDefaultValue);
      }
    }
  }

  /**
   * 增加扩展字段函数
   *
   * @param tableName 表名称
   * @param chSQL     sql
   */
  private void appendExtColumn(String tableName, StringBuilder chSQL,String chDb) {
    if ("mt_sub_tag".equalsIgnoreCase(tableName)) {
      chSQL.append(",\n")
           .append("`")
           .append(CHContext.MT_SUB_TAG_EXT)
           .append("`")
           .append("\t")
           .append(CHDataType.STRING);
    }
    //添加两个系统字段用于agg计算
    chSQL.append(",\n").append("`").append(CHContext.BI_SYS_FLAG).append("`\t").append(CHDataType.Int8);
    chSQL.append(",\n").append("`").append(CHContext.BI_SYS_BATCH_ID).append("`").append("\t").append(CHDataType.Int64);
    chSQL.append(",\n")
         .append("`")
         .append(CHContext.BI_SYS_IS_DELETED)
         .append("`")
         .append("\t")
         .append(CHDataType.UInt8);
    chSQL.append(",\n")
         .append("`")
         .append(CHContext.BI_SYS_VERSION)
         .append("`")
         .append("\t");
    chSQL.append(CHDataType.DateTime).append("\t").append("DEFAULT").append("\t").append("now()");
    //定义分区 用字符串 方便扩展
    if(!WarehouseConfig.noNeedPartitionTableSet.contains(tableName)) {
      chSQL.append(",\n")
           .append("`")
           .append(WarehouseConfig.ODS_PARTITION_KEY)
           .append("`")
           .append("\t")
           .append(CHDataType.STRING).append("\t").append("DEFAULT").append("\t").append("'s'");
    }
  }

  /**
   * 创建udfObjField mapper 用于CH生成字段
   *
   * @param tenantId 租户id
   * @param apiName  apiName
   * @return
   */
  private Map<String, UdfObjFieldDO> createUdfObjFieldMapper(String tenantId, String apiName) {
    Map<String/*column*/, UdfObjFieldDO> udfObjFieldDOMap = Maps.newHashMap();
    if (pgDataSource.isStandalone(tenantId) || (StringUtils.isNotBlank(apiName) && !apiName.endsWith("__c"))) {
      List<UdfObjFieldDO> udfObjFieldDOList = udfObjFieldDao.getFieldsByObjName(apiName, Integer.parseInt(tenantId),
        new int[] {0, 1}, null);
      if (CollectionUtils.isNotEmpty(udfObjFieldDOList)) {
        udfObjFieldDOList.stream().filter(fieldDO -> {
          if (!pgDataSource.isStandalone(tenantId)) {
            return !fieldDO.getDbFieldName().endsWith("__c");
          }
          return true;
        }).forEach(udfObjFieldDO -> {
          if (udfObjFieldDO.getDbFieldName().endsWith("__c")) {
            String columnName = "value" + udfObjFieldDO.getFieldLocation();
            udfObjFieldDOMap.put(columnName, udfObjFieldDO);
          } else {
            udfObjFieldDOMap.put(udfObjFieldDO.getDbFieldName(), udfObjFieldDO);
          }
        });
      }
    }
    return udfObjFieldDOMap;
  }

  /**
   * 创建分区
   *
   * @return
   */
  private String createCHPartitionBy() {
    return String.format("PARTITION BY %s",WarehouseConfig.ODS_PARTITION_KEY);
  }
  /**
   * 创建排序
   *
   * @param schema        pg schema对象
   */
  private String createCHOrderBy(PGSchema schema) {
    String tableName = schema.schemaAndTableName().second;
    List<String> orderByList = WarehouseConfig.tableOrderByColumnsMap.get(tableName);
    if (CollectionUtils.isEmpty(orderByList)) {
      orderByList = schema.createOrderByList();
    }
    StringBuilder chSQL = new StringBuilder("ORDER BY ( ");
    chSQL.append(Joiner.on(",").join(orderByList));
    chSQL.append(")");
    return chSQL.toString();
  }

  /**
   * 生成表引擎语句
   *
   * @param tableEngine 引擎名称
   * @param versionCol  版本列名
   * @return
   */
  private String createCHEngine(String tableEngine, String versionCol) {
    if (Objects.equals(tableEngine, CHContext.REPLICATED_REPLACING_MERGE_TREE)) {
      return "\nENGINE = " + tableEngine + "('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', " +
        versionCol + ", " + CHContext.BI_SYS_IS_DELETED + ")";
    }
    return "\nENGINE = " + tableEngine + "(" + versionCol + ", " + CHContext.BI_SYS_IS_DELETED + ")";
  }

  /**
   * 获取表的schema信息
   *
   * @param db    jdbcURL pgbouncer
   * @param table public.biz_account
   * @return
   * @throws SQLException
   */
  public PGSchema loadSchema(String db, String table) throws SQLException {
    String schemaName = "public";
    String tableName = table;
    int pos = table.indexOf('.');
    if (pos != -1) {
      schemaName = table.substring(0, pos);
      tableName = table.substring(pos + 1);
    }
    @Cleanup JdbcConnection jdbc = pgDataSource.getConnectionByPgbouncerURL(db, schemaName);
    @Cleanup Connection connection = jdbc.connection();
    DatabaseMetaData metaData = connection.getMetaData();
    @Cleanup ResultSet columns = metaData.getColumns(null, schemaName, tableName, "%");
    String eiName = null, eiType = null, timeName = null, timeType = null;
    List<String> columnNames = Lists.newArrayList();
    Map<String, String> timeColumns = Maps.newConcurrentMap();
    Map<String,String> pointColumns = Maps.newHashMap();
    while (columns.next()) {
      String columnName = columns.getString("COLUMN_NAME");
      String datatype = columns.getString("TYPE_NAME");
      columnNames.add(columnName);
      if ("tenant_id".equals(columnName)) {
        eiName = "tenant_id";
        eiType = "string";
      } else if ("ei".equals(columnName)) {
        eiName = "ei";
        eiType = datatype.startsWith("int") ? "long" : "string";
      } else if (StringUtils.endsWithAny(columnName, "time") || "create_date".equals(columnName)) {
        timeType = datatype.startsWith("int") ? "long" : "string";
        timeName = columnName;
        timeColumns.put(columnName, timeType);
      } else if (StringUtils.equalsAny(datatype, PGDataType.GEOGRAPHY, PGDataType.POINT)) {
        pointColumns.put(columnName, datatype);
      }
    }
    // 如果有多个时间字段，则考虑优先级
    for (String name : CHContext.times) {
      String type = timeColumns.get(name);
      if (type != null) {
        timeName = name;
        timeType = type;
        break;
      }
    }
    if (eiName == null) {
      log.warn("cannot found tenant_id or ei in {}/{}", db, table);
    }
    // 查找主键
    @Cleanup ResultSet primaryKeys = metaData.getPrimaryKeys(null, schemaName, tableName);
    Multimap<String, String> names = LinkedListMultimap.create();
    while (primaryKeys.next()) {
      String columnName = primaryKeys.getString("COLUMN_NAME");
      String pkName = primaryKeys.getString("PK_NAME");
      names.put(pkName, columnName);
    }
    log.debug("db:{}, schema:{}, table:{}, names: {}", db, schemaName, tableName, names);
    int size = names.size();
    List<String> pks = Lists.newArrayList();
    if (size == 1) {
      pks = Lists.newArrayList(names.values().iterator());
    } else {
      for (Map.Entry<String, Collection<String>> entry : names.asMap().entrySet()) {
        Collection<String> v = entry.getValue();
        pks = Lists.newArrayList(v.iterator());
        if (v.contains("tenant_id") || v.contains("ei")) {
          break;
        }
      }
    }
    // 查找唯一性索引
    if (pks.isEmpty()) {
      // 可能有多个唯一性索引
      Set<String> uniques = Sets.newHashSet();
      try (ResultSet rs = metaData.getIndexInfo(null, schemaName, tableName, true, true)) {
        while (rs.next()) {
          String name = rs.getString("COLUMN_NAME");
          if (CharMatcher.anyOf("()").matchesAnyOf(name)) {
            log.warn("skip column: {}, table: {}", name, table);
          } else {
            uniques.add(name);
          }
        }
      }
      // 补充到到主键里面
      for (String s : uniques) {
        if (!pks.contains(s)) {
          pks.add(s);
        }
      }
    }
    // 尝试补全主键信息
    for (String s : new String[] {"ei", "id", "tenant_id"}) {
      if (!pks.contains(s) && columnNames.contains(s)) {
        pks.add(s);
      }
    }
    if (pks.size() == 0) {
      log.warn("cannot found PK, db: {}, table: {}, columns: {}", db, table, Joiner.on(',').join(columnNames));
    }
    return PGSchema.builder()
                   .db(db)
                   .name(table)
                   .eiName(eiName)
                   .eiType(eiType)
                   .columns(columnNames)
                   .primaryKeys(pks)
                   .timeColumns(timeColumns)
                   .pointColumns(pointColumns)
                   .build();
  }

  /**
   * <p>获取所有做schema隔离的schema列表</p>
   *
   * @param jdbcURL pgBouncer 地址
   */
  public Set<String> findPGSchemaSets(String jdbcURL) {
    Set<String> schemaSet = new HashSet<>();
    try (JdbcConnection jdbcConnection = this.pgDataSource.getConnectionByPgbouncerURL(jdbcURL)) {
      jdbcConnection.query(schemaSQL, resultSet -> {
        while (resultSet.next()) {
          String schemaName = resultSet.getString(4);
          schemaSet.add(schemaName);
        }
      });
    } catch (Exception e) {
      log.error("find schemas from this db error jdbcURL:{}", jdbcURL, e);
    }
    return schemaSet;
  }

  /**
   * 获取所有需要同步的table 集合
   *
   * @param transferEvent
   * @return
   */
  public Set<String> findNeededToSyncTables(TransferEvent transferEvent) {
    //获取所有tables 需要过滤掉不用同步得表
    Set<String> needSyncTables = Sets.newHashSet();
    List<String> tables = this.findAllPgTableWithSmt(transferEvent.getDbURL(), transferEvent.getSchema());
    if (CollectionUtils.isNotEmpty(tables)) {
      needSyncTables.addAll(tables.stream()
                                  .filter(table -> !blackTableList.contains(table) &&
                                    !extTableRegx.matcher(table).matches())
                                  .toList());
    }
    if (!this.whiteTableList.isEmpty()) {
      needSyncTables.addAll(this.whiteTableList);
    }
    return needSyncTables;
  }

  /**
   * 获取所有需要同步的BI表
   * @param transferEvent
   * @return
   */
  public Set<String> onlySyncBITables(TransferEvent transferEvent) {
    return WarehouseConfig.allowBiPgCHTables;
  }

  /**
   * 获取pg所有适合同步到ch的表列表，public schema 增加了缓存
   *
   * @param dbURL      pgurl
   * @param schemaName public 或 sch_*
   * @return
   */
  public List<String> findAllPgTableWithSmt(String dbURL, String schemaName) {
    //public schema 的表列表从缓存中存储一段时间
    if (Objects.equals("public", schemaName)) {
      return publicTables.get(dbURL, key -> this.metaDataDao.findAllPgTableWithSmt(dbURL, schemaName));
    } else if (schemaName.startsWith("sch_")) {
      String tenantId = schemaName.substring(4);
      Set<String> whiteTables = this.standaloneWhiteTables.get(tenantId);
      List<String> allDefaultTables = this.metaDataDao.findAllPgTableWithSmt(dbURL, schemaName);
      if (CollectionUtils.isNotEmpty(whiteTables)) {
        allDefaultTables.addAll(whiteTables);
      }
      return allDefaultTables;
    }
    return this.metaDataDao.findAllPgTableWithSmt(dbURL, schemaName);
  }

  /**
   * BI pg中全部的表结构同步到CH，schema隔离+非schema隔离 表数量不一样(排除掉 agg_data_* 、 dim_data_* +黑名单表)<br>
   * 去掉一些测试表
   */
  public List<String> findAllPgTableExcludeStatTable(TransferEvent event) {
    //获取所有tables 需要过滤掉不用同步得表
    Set<String> needSyncTables = Sets.newHashSet();
    List<String> tables = metaDataDao.findAllPgTableExcludeStatTable(event.getDbURL(), event.getSchema());
    if (CollectionUtils.isNotEmpty(tables)) {
      needSyncTables.addAll(tables.stream()
                                  .filter(table -> !blackTableList.contains(table) && !table.startsWith("agg_data") &&
                                    !table.startsWith("dim_data"))
                                  .toList());
    }
    if (!this.whiteTableList.isEmpty()) {
      needSyncTables.addAll(this.whiteTableList);
    }
    return Lists.newArrayList(needSyncTables);
  }

  /**
   * 获取所有tableName
   *
   * @param dbURL
   * @param schemaName
   * @return
   */
  public Set<String> findAllCustomTablesBySchema(String dbURL, String schemaName) {
    Set<String> tableNameSet = new HashSet<>();
    try (JdbcConnection jdbcConnection = this.pgDataSource.getConnectionByPgbouncerURL(dbURL,schemaName)) {
      jdbcConnection.prepareQuery(queryTables, preparedStatement -> {
        preparedStatement.setString(1, schemaName);
      }, resultSet -> {
        while (resultSet.next()) {
          String tableName = resultSet.getString(1);
          if (StringUtils.isNotBlank(tableName) && tableName.endsWith("_c")) {
            tableNameSet.add(tableName);
          }
        }
      });
    } catch (Exception e) {
      log.error("find schemas from this db error jdbcURL:{},schemaName:{}", dbURL, schemaName, e);
    }
    return tableNameSet;
  }

  /**
   * 判断是否是paas同步Bi的表
   * @param table
   * @return
   */
  public boolean isPaas2BiTable(String table){
    return syncTableSets.contains(table);
  }

  /**
   * 验证同步过来的数据来源符合路由规则 *
   */
  public boolean checkPgRouter(String db, String schemaName, String tenantId) {
    if (StringUtils.isNotBlank(db)) {
      try {
        RouterInfo routerInfo = pgDataSource.getRouterInfo(tenantId);
        if (null == routerInfo) {
          log.warn("router info is null ei:{}", tenantId);
          return false;
        }
        String masterURL = routerInfo.getJdbcUrl();
        String dbParams = masterURL.substring(masterURL.lastIndexOf("/") + 1);
        String schema = "public";
        if (routerInfo.getStandalone() != null && routerInfo.getStandalone()) {
          schema = "sch_" + tenantId;
        }
        if (dbParams.equals(db) && schema.equals(schemaName)) {
          return true;
        }
        log.warn("check rout false tenant_id={},db:{},schemaName:{},reallyDB:{},reallySchema:{}", tenantId, db, schemaName, dbParams, schema);
      } catch (Exception e) {
        log.error("check rout error tenant_id:{}",tenantId, e);
      }
    }
    return false;
  }

  /**
   * 获取所有standalone的schema列表
   * @param jdbcURLs
   * @return
   */
  public Map<String, List<String>> findAllStandaloneSchemas(List<String> jdbcURLs) {
    Map<String, List<String>> result = Maps.newHashMap();
    for (String jdbcURL : jdbcURLs) {
      try (JdbcConnection jdbcConnection = this.pgDataSource.getConnectionByPgbouncerURL(jdbcURL, "public")) {
        jdbcConnection.query(queryStandaloneSchemas, resultSet -> {
          while (resultSet.next()) {
            String schemaName = resultSet.getString(1);
            if (StringUtils.isNotBlank(schemaName) && schemaName.startsWith("sch_")) {
              String tenantId = schemaName.substring(4);
              if (userCenterService.isValidateStatus(tenantId)) {
                result.computeIfAbsent(jdbcURL, k -> Lists.newArrayList()).add(schemaName);
              }else{
                log.warn("tenant_id:{} is not valid", tenantId);
              }
            }
          }
        });
      } catch (Exception e) {
        log.error("find all standalone schemas error jdbcURL:{}", jdbcURL, e);
      }
    }
    return result;
  }
}
