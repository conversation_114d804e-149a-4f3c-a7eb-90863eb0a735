package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Map;
import java.util.Set;
@Deprecated
@Data
@Table(name = "db_table_sync_info")
public class DBTableSyncInfoDO {
  @Id
  private String id;
  @Column(name = "db_sync_id")
  private String dbSyncId;
  @Column(name = "table_name")
  private String tableName;
  @Column(name="max_sys_modified_time")
  private Long maxSysModifiedTime;
  @Column(name="last_sync_time")
  private Long lastSyncTime;
  @Column(name="api_name_ei_map")
  private String apiNameEiMap;
  @Column(name="create_time")
  private Long createTime;
  @Column(name="last_modified_time")
  private Long LastModifiedTime;
  @Column(name="is_deleted")
  private Integer isDeleted;
  @Column(name="batch_num")
  private Long batchNum;
  @Column(name="status")
  private Integer status;

  public static Map<String, Set<String>> parseApiNameEiMap(String apiNameEiMapString) {
    if (StringUtils.isBlank(apiNameEiMapString)) {
      return Maps.newHashMap();
    }
    return JSON.parseObject(apiNameEiMapString, new TypeReference<>() {
    });
  }
}
