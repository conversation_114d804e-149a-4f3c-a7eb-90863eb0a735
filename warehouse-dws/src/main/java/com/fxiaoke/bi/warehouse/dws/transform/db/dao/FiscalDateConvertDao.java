package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.alibaba.fastjson.JSONObject;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.fxiaoke.bi.warehouse.dws.transform.model.FiscalYear;
import com.fxiaoke.bi.warehouse.dws.transform.model.PaasFiscalConfig;
import com.fxiaoke.common.Pair;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Author:jief
 * @Date:2023/9/27
 */
@Service
public class FiscalDateConvertDao {
  private static final String FISCAL_CONFIG_KEY = "FISCAL_MONTH_SETTING";
  @Resource(name = "orgAdapterApiEnterpriseConfigService")
  private EnterpriseConfigService enterpriseConfigService;


  /**
   * 获取当前财年的财年配置
   *
   * @return 财年配置
   * @throws Exception 异常
   */
  public PaasFiscalConfig getFiscalConfig(String tenantId) throws Exception {
    //如果缓存中没有财年配置信息，请求PaaS接口获取
    GetConfigDto.Argument argument = new GetConfigDto.Argument();
    argument.setKey(FISCAL_CONFIG_KEY);
    final Integer employeeId = -1000;
    argument.setEmployeeId(employeeId);
    argument.setCurrentEmployeeId(employeeId);
    argument.setEnterpriseId(Integer.valueOf(tenantId));
    final GetConfigDto.Result config = this.enterpriseConfigService.getConfig(argument);
    String fiscalConfig = config.getValue();
    if (Strings.isNullOrEmpty(fiscalConfig)) {
      throw new RuntimeException("fiscal config is null");
    }
    return JSONObject.parseObject(fiscalConfig, PaasFiscalConfig.class);
  }

  /**
   * //todo 没考虑2月份最后一天问题，只适用于中肯
   * 根据年份获取对应年份财年财月配置
   *
   * @param paramYear 年 如：2020
   * @return
   */
  public PaasFiscalConfig getFiscalDateConfig(String tenantId, String paramYear) throws Exception {
    PaasFiscalConfig fiscalConfig = this.getFiscalConfig(tenantId);
    List<FiscalYear.FiscalMonth> fiscalMonthList = fiscalConfig.getCurrentYear().getFiscalMonths();
    String year = fiscalConfig.getCurrentYear().getYear();
    //如果日期不是当前年
    if (!year.equals(paramYear)) {
      PaasFiscalConfig fiscalConfigTmp = new PaasFiscalConfig();
      FiscalYear fiscalYearTmp = new FiscalYear();
      List<FiscalYear.FiscalMonth> fiscalYearMonthTmp = Lists.newArrayList();
      int yearDiff = Integer.parseInt(paramYear) - Integer.parseInt(year);
      for (FiscalYear.FiscalMonth month : fiscalMonthList) {
        Calendar begin = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        begin.setTime(new Date(month.getBegin()));
        end.setTime(new Date(month.getEnd()));
        begin.add(Calendar.YEAR, yearDiff);
        end.add(Calendar.YEAR, yearDiff);
        FiscalYear.FiscalMonth fiscalMonth = new FiscalYear.FiscalMonth();
        fiscalMonth.setBegin(begin.getTimeInMillis());
        fiscalMonth.setEnd(end.getTimeInMillis());
        fiscalYearMonthTmp.add(fiscalMonth);
      }
      fiscalYearTmp.setYear(paramYear);
      fiscalYearTmp.setFiscalMonths(fiscalYearMonthTmp);
      fiscalConfigTmp.setCurrentYear(fiscalYearTmp);
      return fiscalConfigTmp;
    }
    return fiscalConfig;
  }

  /**
   * todo 也会有2月最有一天问题
   * 计算自定义财年的租户，起始结束时间 左闭 右闭
   * @param tenantId 租户id
   * @param zoneId 时区
   * @param fromYear 起始年
   * @param toYear 截止年
   * @return
   */
  public Pair<Long,Long> calFiscalStartAndEndDate(String tenantId,String zoneId,int fromYear,int toYear,int startMonth)throws Exception{
    PaasFiscalConfig fiscalConfig = this.getFiscalConfig(tenantId);
    if(fiscalConfig==null){
      return null;
    }
    List<FiscalYear.FiscalMonth> fiscalMonthList = fiscalConfig.getCurrentYear().getFiscalMonths();
    if(CollectionUtils.isEmpty(fiscalMonthList)){
      return null;
    }
    String year = fiscalConfig.getCurrentYear().getYear();
    if(StringUtils.isBlank(year)){
      return null;
    }
    FiscalYear.FiscalMonth month = fiscalMonthList.get(startMonth-1);
    ZonedDateTime fromZonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(month.getBegin()), ZoneId.of(zoneId)).plusYears(fromYear-Integer.parseInt(year));
    ZonedDateTime toZonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(month.getBegin()), ZoneId.of(zoneId)).plusYears((toYear+1)-Integer.parseInt(year));
//    String.format("%s%02d%02d",fromZonedDateTime.getYear(),fromZonedDateTime.getMonth().getValue(),fromZonedDateTime.getDayOfMonth());
//    String.format("%s%02d%02d",toZonedDateTime.getYear(),toZonedDateTime.getMonth().getValue(),toZonedDateTime.getDayOfMonth());
    return Pair.build(fromZonedDateTime.toInstant().toEpochMilli(),toZonedDateTime.toInstant().toEpochMilli());
  }
}
