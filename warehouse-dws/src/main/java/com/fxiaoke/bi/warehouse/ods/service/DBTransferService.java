package com.fxiaoke.bi.warehouse.ods.service;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.bi.warehouse.common.component.ClickHouseUtilService;
import com.fxiaoke.bi.warehouse.common.db.dao.DbSyncInfoFlowDao;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncTableStatusEnum;
import com.fxiaoke.bi.warehouse.common.entity.DbSyncInfoFlowDO;
import com.fxiaoke.bi.warehouse.common.util.*;
import com.fxiaoke.bi.warehouse.core.db.AggMergeDao;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.core.db.entity.DBMergeInfoDO;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.ods.bean.BatchUpdateDbTableSyncInfo;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.bean.PGSchema;
import com.fxiaoke.bi.warehouse.ods.bean.PgSysModifiedTimeInfo;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.dao.UdfObjFieldDao;
import com.fxiaoke.bi.warehouse.ods.entity.*;
import com.fxiaoke.bi.warehouse.ods.integrate.service.CHDataToCHService;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.IntegrateServiceImpl;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.helper.NumberHelper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DBTransferService {
  @Resource
  private PgDataSource pgDataSource;
  @Resource
  private PGMetadataService pgMetadataService;
  @Resource
  private CHMetadataService chMetadataService;
  @Resource
  private CHNodeService chNodeService;
  @Resource
  private CHClientService chClientService;
  @Resource
  private PgCommonDao pgCommonDao;
  @Resource
  private CHDBService chdbService;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  @Autowired
  private EIEAConverter eieaConverter;
  @Resource
  private UdfObjFieldDao udfObjFieldDao;
  @Resource
  private MergeTaskService mergeTaskService;
  @Resource
  private CHRouterPolicy chRouterPolicy;
  @Resource
  private IntegrateServiceImpl integrateService;
  @Resource
  private DbTableSyncInfoService dbTableSyncInfoService;
  @Resource
  private ClickHouseUtilService clickHouseUtilService;
  @Resource
  private DbSyncInfoFlowDao dbSyncInfoFlowDao;
  @Autowired
  private CHDataToCHService chDataToCHService;
  @Resource
  private AggMergeDao aggMergeDao;
  @Resource
  QiXinNotifyService qiXinNotifyService;
  //持久化磁盘阈值
  private int savePointSize;
  /**
   * 批量查询CH 中before data的阈值
   */
  private int batchQueryBeforeSize;
  //分页拉取pg数据，每批次数量
  private int batchSize;
  //最大支持并行计算多少db
  private Semaphore parallel;
  //最大并发线程数据
  @Getter
  private volatile int chReadTimeOut;
  //线程池
  private static final Set<String> initFlag = Sets.newConcurrentHashSet();
  private int baseTablePartitionSize;
  private long syncDelayThreshold;
  /**
   * agg data merge分页大小
   */
  @Getter
  private volatile int mergePageSize;

  private int newMergePageSize;
  /**
   * 等待agg计算超时
   */
  private volatile long waitForCalDelayThreshold;
  /**
   * merge 延迟超过此阈值后自动打印error错误
   */
  private long mergeDelayThreshold;
  /**
   * merge 延迟超过此阈值后自动变更 status 状态
   */
  private long mergeDelayChangeStatusThreshold;
  private Set<String> hasSysEiTables = Sets.newHashSet();
  private Set<String> skipBeforeTableSet = Sets.newHashSet();
  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
  private long mergeBeginTime;
  private long mergeEndTime;
  private long mergeTerminalTime;
  private Set<String> allowMergeCHDB = Sets.newHashSet();
  private static final ThreadFactory factory = new ThreadFactoryBuilder().setDaemon(true)
                                                                         .setNameFormat("bi2ch-fetch-%d")
                                                                         .build();
  private static final ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newFixedThreadPool(25, factory);
  /**
   * 发送计算消息hash值后缀
   */
  private volatile int sendCalHashSalt;
  /**
   * 同步等待agg时间报警阈值缓存
   */
  private static final ConcurrentHashMap<String, Long> aggStatusWaitTime = new ConcurrentHashMap<>();
  /**
   * 同步等待agg时间报警阈值缓存
   */
  private static final ConcurrentHashMap<String, Long> aggIngWaitTime = new ConcurrentHashMap<>();

  private boolean allowMergeAll;
  /**
   * 是否允许下游数据同步
   */
  private volatile boolean allowIntegrate;
  /**
   * 自定义表的同步阈值，包含savePoint,
   */
  private Map<String/*table名称*/,Pair<Integer/*before数据cacheSize*/,Integer/*savePointSize*/>> customThreshHold = Maps.newHashMap();
  /**
   * 公共库的表的schema信息
   * eg:public_biz_account
   * public_object_data
   */
  private final Cache<String, PGSchema> publicTableSchema = Caffeine.newBuilder()
                                                                    .maximumSize(1500)
                                                                    .expireAfterWrite(3, TimeUnit.HOURS)
                                                                    .build();
  private long integrateBeginTime;
  private long integrateEndTime;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
      batchSize = config.getInt("bi2ch_batch_size", 800);
      parallel = new Semaphore(config.getInt("bi2ch_parallel_size", 50));
      savePointSize = config.getInt("bi2ch_save_point_threshold", 100000);
      batchQueryBeforeSize = config.getInt("bi2ch_query_before_threshold", 500);
      mergePageSize = config.getInt("agg_data_merge_page_size", ********);
      newMergePageSize = config.getInt("agg_data_new_merge_page_size", 1000000);
      baseTablePartitionSize = config.getInt("query_table_base_partition_size", 150);
      //同步延迟超1小时就开始报error日志过
      syncDelayThreshold = config.getInt("sync_db_delay_threshold", 1000 * 60 * 30);
      chReadTimeOut = config.getInt("ch_socket_time_out", 1000 * 60 * 30);
      mergeDelayThreshold = config.getInt("merge_agg_delay_threshold", 1000 * 60 * 60 * 5);
      mergeDelayChangeStatusThreshold = config.getInt("merge_agg_delay_change_status_threshold", 1000 * 60 * 60 * 10);
      waitForCalDelayThreshold = config.getLong("wait_for_cal_delay_threshold", 1000 * 60 * 15);
      String sysEiTables = config.get("have_sys_ei_tables", "");
      hasSysEiTables = Sets.newHashSet(Splitter.on(",").splitToList(sysEiTables));
      String skipBeforeTableStr = config.get("no_need_before_tables", "");
      skipBeforeTableSet = Sets.newHashSet(Splitter.on(",").splitToList(skipBeforeTableStr));
      mergeBeginTime = LocalTime.parse(config.get("mergeBeginTime", "01:00"), formatter)
                                .getLong(ChronoField.MINUTE_OF_DAY);
      mergeEndTime = LocalTime.parse(config.get("mergeEndTime", "06:00"), formatter).getLong(ChronoField.MINUTE_OF_DAY);
      mergeTerminalTime = LocalTime.parse(config.get("mergeTerminalTime", "07:00"), formatter).getLong(ChronoField.MINUTE_OF_DAY);
      sendCalHashSalt = config.getInt("send_cal_hash_code_salt", 1);
      allowMergeAll = config.getBool("allow_merge_all", false);
      //放开agg_data merge的chDB
      String allMergeCHTmp = config.get("all_merge_chDB", "");
      if (StringUtils.isBlank(allMergeCHTmp)) {
        allowMergeCHDB = Sets.newHashSet();
      } else {
        allowMergeCHDB = Sets.newHashSet(Splitter.on(CharMatcher.anyOf(",|"))
                                                 .omitEmptyStrings()
                                                 .splitToList(allMergeCHTmp));
      }
      String ctt = config.get("custom_threshold_table","");
      customThreshHold = CHContext.createCustomThreshold(ctt);
      //下游数据集成到上游时间
      integrateBeginTime = LocalTime.parse(config.get("integrateBeginTime", "00:01"), formatter)
                                .getLong(ChronoField.MINUTE_OF_DAY);
      integrateEndTime = LocalTime.parse(config.get("integrateEndTime", "23:59"), formatter).getLong(ChronoField.MINUTE_OF_DAY);
      allowIntegrate = config.getBool("allow_integrate",false);
    });
  }

  /**
   * 根据pgdb拉取
   *
   * @param transferEvent
   */
  public void doTransfer2CH(TransferEvent transferEvent) {
    String chDBName = CHContext.getDBName(transferEvent.getChDbURL());
    if (GrayManager.isAllowByRule("delay_calculate_ch_DB", chDBName)) {
      log.warn("this chDBName:{} is stop transfer please wait for notified", chDBName);
      return;
    }
    String transferKey = String.format("%s^%s", CHContext.getDBName(transferEvent.getDbURL()), transferEvent.getSchema());
    DBSyncInfo dbSyncInfo = null;
    try (JedisLock jedisLock = new JedisLock(jedisCmd, transferKey, 1000 * 60 * 15)) {
      if (jedisLock.tryLock()) {
        long start = System.currentTimeMillis();
        dbSyncInfo = pgCommonDao.queryDBSyncInfo(transferEvent.getChDbURL(), transferEvent.getDbURL(), transferEvent.getSchema());
        if (dbSyncInfo == null) {
          log.error("can not find db info CHDBURL:{},pgDbURL{}", transferEvent.getChDbURL(), transferEvent.getDbURL());
          return;
        }
        if (!GrayManager.isAllowByRule("merge_agg_in_situation_db", CHContext.getDBName(dbSyncInfo.getChDb()))) {
          //如果没有灰度新的merge，原merge机制启动全量merge任务
          startMergeAggDataAll(dbSyncInfo);
        }
        SyncStatusEnum statusEnum = SyncStatusEnum.createFromStatus(dbSyncInfo.getStatus());
        Long batchNum = dbSyncInfo.getBatchNum();
        switch (statusEnum) {
          case AGG_ING -> {
            long delayTime = aggIngWaitTime.computeIfAbsent(dbSyncInfo.getPgDb(), key -> this.waitForCalDelayThreshold);
            long aggingTime = System.currentTimeMillis() - dbSyncInfo.getLastModifiedTime();
            if (aggingTime >= delayTime) {
              log.error("this event:{} is batchNum:{} AGG_ING cost:{} ", JSON.toJSONString(transferEvent), batchNum, aggingTime);
              //如果超过阈值了打印一个错误日志并且发送消息,并且阈值逐渐增加。
              aggIngWaitTime.put(dbSyncInfo.getPgDb(), delayTime + (30 * 60 * 1000L));
              log.error("this event:{} is batchNum:{} resend calculate message", JSON.toJSONString(transferEvent), batchNum);
              pgCommonDao.sendCalculateEvent(dbSyncInfo, sendCalHashSalt, null);
              QiXinNotifyService.sendTextMessage(statusEnum, transferEvent, aggingTime, dbSyncInfo.getLastModifiedTime(), delayTime);
            } else {
              log.warn("this event:{} is  AGG_ING please wait next time ", JSON.toJSONString(transferEvent));
            }
            return;
          }
          case SYNC_ED -> {
            long delayTime = aggStatusWaitTime.computeIfAbsent(dbSyncInfo.getPgDb(), key -> this.waitForCalDelayThreshold);
            long syncedTime = System.currentTimeMillis() - dbSyncInfo.getLastModifiedTime();
            if (syncedTime >= delayTime) {
              log.error("this event:{} is  SYNC_ED waiting for calculate cost:{}", JSON.toJSONString(transferEvent), syncedTime);
              //如果超过阈值了打印一个错误日志并且发送消息,并且阈值逐渐增加。
              aggStatusWaitTime.put(dbSyncInfo.getPgDb(), delayTime + (30 * 60 * 1000L));
              pgCommonDao.sendCalculateEvent(dbSyncInfo, sendCalHashSalt, null);
              QiXinNotifyService.sendTextMessage(statusEnum, transferEvent, syncedTime, dbSyncInfo.getLastModifiedTime(), delayTime);
            } else {
              log.warn("this event:{} is SYNC_ED please wait next time ", JSON.toJSONString(transferEvent));
            }
            return;
          }
          // 如果是 SYNC_ING 两种情况1、服务意外中断。2、redis锁超时没有同步完另外的线程抢到锁
          case SYNC_ING -> {
            //如果是服务第一次启动并且同步状态是SYNC_ING 则需要修改状态继续同步
            if (!initFlag.contains(transferEvent.getDbURL())) {
              initFlag.add(transferEvent.getDbURL());
              log.info("system start so continue syncing");
            } else {
              long syncTime = System.currentTimeMillis() - dbSyncInfo.getLastSyncTime();
              if (syncTime >= this.syncDelayThreshold) {
                log.error("this event:{} is SYNC_ING waiting for SYNC_ING cost:{}", JSON.toJSONString(transferEvent), syncTime);
                QiXinNotifyService.sendTextMessage(statusEnum, transferEvent, syncTime, dbSyncInfo.getLastSyncTime(), syncDelayThreshold);
              } else {
                log.warn("this event:{} is SYNC_ING please wait next time ", JSON.toJSONString(transferEvent));
              }
              return;
            }
          }
          case EXCHANGE_AGG -> {
            this.dealWithExchangeStatus(dbSyncInfo.getId(), dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getChDb(), dbSyncInfo.getAllowIncPartition());
            return;
          }
          case AGG_ED, SYNC_ABLE, SYNC_ERROR -> {
            if (this.shouldWait4IncrementMergeMergeAggData(dbSyncInfo)) {
              return;
            }
            dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ING.getStatus());
            dbSyncInfo.setLastSyncTime(System.currentTimeMillis());
            dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());
            pgCommonDao.updateDBSyncInfoStatus(dbSyncInfo);
            initFlag.add(transferEvent.getDbURL());
          }
        }
        AtomicLong nextBatchNum = new AtomicLong(batchNum + 1L);
        this.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, start);
        //重置等待时间
        aggStatusWaitTime.put(dbSyncInfo.getPgDb(), this.waitForCalDelayThreshold);
        aggIngWaitTime.put(dbSyncInfo.getPgDb(), this.waitForCalDelayThreshold);
        initFlag.remove(transferEvent.getDbURL());
      } else {
        log.warn("jedisLock.tryLock fail key:{}", transferKey);
      }
    } catch (Exception e) {
      if (dbSyncInfo != null) {
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ERROR.getStatus());
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());
        try {
          pgCommonDao.updateDBSyncInfoStatus(dbSyncInfo);
          initFlag.remove(transferEvent.getDbURL());
        } catch (Exception e2) {
          String errorMsg = String.format("batchUpsertDbSyncInfo error status:%s,event:%s", SyncStatusEnum.SYNC_ERROR.getStatus(), JSON.toJSONString(transferEvent));
          log.error(errorMsg, e);
          //如果状态更新失败如：数据库crash掉了，删除initFlag，保证下次可以继续同步
          initFlag.remove(transferEvent.getDbURL());
          QiXinNotifyService.sendTextMessage(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getChDb(), errorMsg);
        }
      }
      log.error("doTransfer2CH event:{},error ", JSON.toJSONString(transferEvent), e);
      throw new RuntimeException(e);
    }
  }

  public void dealWithExchangeStatus(String dbSyncId,
                                     String pgURL,
                                     String pgSchema,
                                     String chURL,
                                     Integer allowIncPartition) {
    if (this.wait4IncrementMergeAggData(pgURL, pgSchema, chURL)) {
      return;
    }
    ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.getInstance(chURL);
    //swap的merge方式：获取增量任务的开始时间，当全量任务在执行时，获得的值是0；
    //新merge方式：获取上一个分区的merge开始时间
    long startTime = mergeTaskService.getMergeTaskBeginTime(clickhouseNodeInfo);
    long delayMs = System.currentTimeMillis() - startTime;
    long minuteOfDay = LocalTime.now().getLong(ChronoField.MINUTE_OF_DAY);
    //如果merge任务执行时长超过了最大时间，或者已经过了可以merge的有效时间区间，需要停止merge恢复状态
    if (delayMs >= this.mergeDelayChangeStatusThreshold || minuteOfDay <= this.mergeBeginTime || minuteOfDay >= this.mergeTerminalTime) {
      int size = aggMergeDao.updateStatusFromExchangeCAS(dbSyncId, pgURL, pgSchema, chURL, allowIncPartition, Lists.newArrayList(SyncStatusEnum.EXCHANGE_AGG.getStatus()));
      log.warn("this event:{} is EXCHANGE_AGG more than:{} ms OR out of merge time, change status:{},startTime {}", dbSyncId, delayMs, size, startTime);
    } else if (delayMs >= this.mergeDelayThreshold) {
      log.error("this event:{} is EXCHANGE_AGG more than:{} ms", dbSyncId, delayMs);
      TransferEvent event = TransferEvent.builder().dbURL(pgURL).schema(pgSchema).chDbURL(chURL).build();
      QiXinNotifyService.sendTextMessage(SyncStatusEnum.EXCHANGE_AGG, event, delayMs, startTime, mergeDelayThreshold);
    } else {
      log.warn("this event:{} is EXCHANGE_AGG please wait next time ", dbSyncId);
    }
  }

  private boolean canMergedToday(DBSyncInfo dbSyncInfo) {
    String chUrl = dbSyncInfo.getChDb();
    boolean bAfter = true;
    List<DBSyncInfo> dbSyncInfos = pgCommonDao.queryDBSyncInfoByChDB(chUrl);
    if (CollectionUtils.isEmpty(dbSyncInfos)) {
      return false;
    }
    Optional<Long> lastMergeTime = dbSyncInfos.stream()
                                              .map(DBSyncInfo::getLastMergeAggTime)
                                              .filter(Objects::nonNull)
                                              .max(Comparator.naturalOrder());
    if (lastMergeTime.isEmpty()) {
      return true;
    } else {
      LocalDate lastMergeDate = new Timestamp(lastMergeTime.get()).toLocalDateTime().toLocalDate();
      LocalDate localDate = LocalDate.now();
      bAfter = localDate.isAfter(lastMergeDate);
      log.info("hasMergedToday canMerge Today:{}, lastMergeDate:{}, LocalDate:{},{},{}", bAfter, lastMergeDate, localDate, dbSyncInfo.getChDb(), dbSyncInfo.getPgDb());
    }
    return bAfter;
  }

  /**
   * 对agg_data表执行全量merge，时间比较长，为每天晚上的定时增量做准备
   *
   * @param dbSyncInfo
   * @return
   */
  private void startMergeAggDataAll(DBSyncInfo dbSyncInfo) {
    String chURL = dbSyncInfo.getChDb();
    if (!CHContext.openMerge(allowMergeCHDB, chURL)) {
      log.warn("startMergeAggDataAll:{},do not allow merge", chURL);
      return;
    }

    long minuteOfDay = LocalTime.now().getLong(ChronoField.MINUTE_OF_DAY);
    if ((minuteOfDay >= this.mergeBeginTime && minuteOfDay <= this.mergeEndTime) ||
            GrayManager.isAllowByRule("allow_merge_all_by_db", CHContext.getDBName(chURL))) {
      try {
        if (!canMergedToday(dbSyncInfo) && !GrayManager.isAllowByRule("allow_merge_all_by_db", CHContext.getDBName(chURL))) {
          log.warn("startMergeAggDataAll :{},{},today has already merged", chURL, dbSyncInfo.getPgDb());
          return;
        }
        String dbName = CHContext.getDBName(chURL);
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                                                                  .database(dbName)
                                                                  .jdbcUrl(chURL)
                                                                  .cluster("{cluster}")
                                                                  .build();
        boolean bOk = mergeTaskService.merge(clickhouseNodeInfo, mergePageSize, chReadTimeOut, false, false);
        log.info("startMergeAggDataAll status: {}, churl:{}, ", bOk, chURL);
      } catch (Exception e) {
        //重置merge任务状态释放锁
        log.error("startMergeAggDataAll error,churl:{}", chURL, e);
      }
    } else {
      log.info("cannot startMergeAggDataAll, time is not reach:{},{},{}", chURL, dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema());
    }
  }

  /**
   * 针对新企业验证添加路由
   *
   * @param newTenantIds
   * @param dbSyncInfoCopy
   */
  public void checkAndAddChRouter(List<String> newTenantIds, DBSyncInfoBO dbSyncInfoCopy) {
    //过滤出需要全量同步的租户id
    List<String> offlineTenantIds = newTenantIds.stream().filter(dbSyncInfoCopy::isNewTenant).toList();
    if (CollectionUtils.isNotEmpty(offlineTenantIds)) {
      Map<Integer, String> eiToEaMap = clickHouseUtilService.getEiToEaMap(offlineTenantIds);
      offlineTenantIds.forEach(tenantId -> {
        String jdbcUrl = chRouterPolicy.getCHJdbcURL(tenantId);
        if (StringUtils.isEmpty(jdbcUrl)) {
          clickHouseUtilService.createChRoute(tenantId, dbSyncInfoCopy.getChDb(), false, eiToEaMap);
        }
      });
    }
  }

  /**
   * 执行同步代码
   *
   * @param nextBatchNum  批次号
   * @param transferEvent
   * @param dbSyncInfo
   * @param start
   */
  public void doTranslate(AtomicLong nextBatchNum, TransferEvent transferEvent, DBSyncInfo dbSyncInfo, long start) {
    BIAggSyncInfoDO biAggSyncInfoDO = null;
    List<String> tenantIdList = pgMetadataService.findValidaTenantId(transferEvent);//获取合法的租户id
    if (CollectionUtils.isNotEmpty(tenantIdList)) {
      DBSyncInfoBO dbSyncInfoCopy = DBSyncInfoBO.createInstanceOf(dbSyncInfo);//获取需要同步的table列表
      this.checkAndAddChRouter(tenantIdList,dbSyncInfoCopy);//验证是否有路由
      List<String> tableList= Lists.newArrayList(pgMetadataService.findNeededToSyncTables(transferEvent));
      List<List<String>> tablePartition = Lists.partition(tableList, this.baseTablePartitionSize);
      log.info("ThreadPoolExecutor LinkedBlockingQueue size:{}", executor.getQueue().size());
      Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = dbTableSyncInfoService.queryDbTableSyncInfoMap(dbSyncInfo);
      DbSyncInfoFlowDO dbSyncInfoFlowDO = this.findDbSyncingFlow(dbSyncInfo);
      String partitionName = this.createPartitionName(dbSyncInfo, dbSyncInfoFlowDO);
      log.info("createPartitionName:{},pgURL:{},pgSchema:{},chURL:{},batchNum:{}", partitionName, transferEvent.getDbURL(), transferEvent.getSchema(), transferEvent.getChDbURL(), nextBatchNum.get());
      long defaultMaxModifiedTime = System.currentTimeMillis()*1000;//获取默认的全局的最大时间偏移量标尺。
      log.info("defaultMaxModifiedTime:{},pgURL:{},pgSchema:{},chURL:{},batchNum:{}", defaultMaxModifiedTime, transferEvent.getDbURL(), transferEvent.getSchema(), transferEvent.getChDbURL(), nextBatchNum.get());
      CompletableFuture.allOf(tablePartition.stream().map(key -> CompletableFuture.runAsync(() -> {
        try (JdbcConnection jdbcConnection = pgDataSource.getConnectionByPgbouncerURL(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema())) {
          BatchUpdateDbTableSyncInfo batchUpdateDbTableSyncInfo = BatchUpdateDbTableSyncInfo.createInstance( dbSyncInfoCopy, dbTableSyncInfoService,30);
          for (String table : key) {
            this.doTransferByTable(jdbcConnection, dbSyncInfoCopy, table, tenantIdList,dbTableSyncInfoMap,partitionName,batchUpdateDbTableSyncInfo,defaultMaxModifiedTime);
          }
          batchUpdateDbTableSyncInfo.flush();
        } catch (Exception e) {
          log.error("doTransferByPartition error pgURL:{},pgSchema:{},chURL:{},batchNum:{}", transferEvent.getDbURL(), transferEvent.getSchema(), transferEvent.getChDbURL(), nextBatchNum.get(), e);
          throw new RuntimeException(e);
        }
      }, executor)).toArray(CompletableFuture[]::new)).join();
      //同步用户行为数据
      chDataToCHService.transferOperationDataToCH(nextBatchNum, dbSyncInfoCopy,tenantIdList,dbTableSyncInfoMap,partitionName);
      //上下游同步
      String chDBName = CHContext.getDBName(transferEvent.getChDbURL());
      if(GrayManager.isAllowByRule("integrate_support_inc_partition",chDBName)){
        integrateService.executeIntegrateDataPlus(nextBatchNum,dbSyncInfoCopy,dbTableSyncInfoMap,partitionName);
      }else{
        biAggSyncInfoDO = integrateService.executeIntegrateData(nextBatchNum,dbSyncInfoCopy,dbTableSyncInfoMap,partitionName);
      }
      this.updateDbSyncInfoFlow(dbSyncInfo, nextBatchNum, dbTableSyncInfoMap, dbSyncInfoFlowDO);
    } else {
      log.warn("this db:{},schema:{} have no validate tenant id", transferEvent.getDbURL(), transferEvent.getSchema());
    }
    //执行成功后更新db状态
    this.updateDbSyncInfo(dbSyncInfo, nextBatchNum, tenantIdList);
    pgCommonDao.sendCalculateEvent(dbSyncInfo, sendCalHashSalt,biAggSyncInfoDO);
    long cost = System.currentTimeMillis() - start;
    this.sendBizLog(nextBatchNum, dbSyncInfo, start, cost);
    log.info("sync success and send message end :{},cost:{},batchNum:{}", JSON.toJSONString(transferEvent), cost,nextBatchNum.get());
  }

  private void sendBizLog(AtomicLong nextBatchNum, DBSyncInfo dbSyncInfo,long start,long cost){
    String tenantId = "-1";
    String ea = "-1";
    if (dbSyncInfo.getPgSchema().startsWith("sch_")) {
      tenantId = dbSyncInfo.getPgSchema().substring(4);
      ea=TraceUtils.ei2EA(tenantId, eieaConverter);
    }
    BizAuditLog.builder()
               .tenantId(tenantId)
               .userId("1000")
               .ea(ea)
               .module(WarehouseConfig.ODS_EVENT)
               .cost(cost)
               .from(dbSyncInfo.getPgDb()).extra(dbSyncInfo.getPgSchema())
               .num(nextBatchNum.get())
               .startTime(start)
               .queryType("pg2ch")
               .build()
               .log();
  }

  /**
   * 创建partition 名称
   * @param dbSyncInfo
   * @param dbSyncInfoFlowDO
   * @return
   */
  private String createPartitionName(DBSyncInfo dbSyncInfo, DbSyncInfoFlowDO dbSyncInfoFlowDO) {
    String chDb = CHContext.getDBName(dbSyncInfo.getChDb());
    if (GrayManager.isAllowByRule("use_ch_ods_partition", chDb)) {
      String partitionName = WarehouseConfig.STOCK_PARTITION_NAME;
      if (dbSyncInfoFlowDO != null) {
        partitionName = dbSyncInfoFlowDO.getPartitionName();
      }
      return partitionName;
    }
    return null;
  }

  /**
   * 获取同步中的批次集合
   * @param dbSyncInfo
   * @return
   */
  public DbSyncInfoFlowDO findDbSyncingFlow(DBSyncInfo dbSyncInfo) {
    String chDb = CHContext.getDBName(dbSyncInfo.getChDb());
    if (GrayManager.isAllowByRule("use_ch_ods_partition", chDb) && Objects.equals(dbSyncInfo.getAllowIncPartition(), WarehouseConfig.OPEN_INC_PARTITION)) {
      return dbSyncInfoFlowDao.queryDbSyncFlowAsSyncIng(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getId(),true,WarehouseConfig.querySyncFlowLimit);
    }
    return null;
  }

  /**
   * 更新同步
   * @param dbSyncInfo
   */
  public void updateDbSyncInfoFlow(DBSyncInfo dbSyncInfo,
                                 AtomicLong nextBatchNum,
                                 Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                 DbSyncInfoFlowDO dbSyncInfoFlowDO) {
    String chDb = CHContext.getDBName(dbSyncInfo.getChDb());
    if (GrayManager.isAllowByRule("use_ch_ods_partition", chDb) && Objects.equals(dbSyncInfo.getAllowIncPartition(), WarehouseConfig.OPEN_INC_PARTITION) && dbSyncInfoFlowDO != null) {
      List<DbTableSyncInfoDO> dbTableSyncInfos = Lists.newArrayList();
      if (dbTableSyncInfoMap != null) {
        dbTableSyncInfoMap.forEach((tableName, dbTableSyncInfoDO) -> {
          dbTableSyncInfos.add(dbTableSyncInfoDO);
        });
        log.info("chDB:{},pgDB:{},pgSchema:{},dbTableSyncInfos size:{}", dbSyncInfo.getChDb(), dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbTableSyncInfos.size());
      }
      dbSyncInfoFlowDao.mergeApiNameEiMap(dbSyncInfoFlowDO, dbTableSyncInfos, nextBatchNum);
      if (System.currentTimeMillis() - dbSyncInfoFlowDO.getCreateTime() >= WarehouseConfig.dbSyncFlowDelay) {
        dbSyncInfoFlowDO.setStatus(SyncStatusEnum.SYNC_ED.getStatus());
        log.info("dbSyncFlow time is more than {},createTime:{},batchNum:{}",dbSyncInfoFlowDO.getCreateTime(),WarehouseConfig.dbSyncFlowDelay,nextBatchNum.get());
      }
      dbSyncInfoFlowDO.setLastSyncTime(new Date().getTime());
      int result = dbSyncInfoFlowDao.upsertDbSyncInfoFlow(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), Lists.newArrayList(dbSyncInfoFlowDO));
      log.info("upsertDbSyncInfoFlow pgDb:{},pgSchema:{},chDb:{},size:{}", dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getChDb(), result);
    }
  }

  /**
   * 更新db同步信息
   * @param dbSyncInfo
   */
  public void updateDbSyncInfo(DBSyncInfo dbSyncInfo, AtomicLong nextBatchNum, List<String> tenantIdList) {
    String chDb = CHContext.getDBName(dbSyncInfo.getChDb());
    if (GrayManager.isAllowByRule("use_ch_ods_partition", chDb) &&
      Objects.equals(dbSyncInfo.getAllowIncPartition(), WarehouseConfig.OPEN_INC_PARTITION)) {
      dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
    } else {
      dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ED.getStatus());
    }
    dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());
    dbSyncInfo.setBatchNum(nextBatchNum.get());
    dbSyncInfo.setLastSyncEis(String.join(",", tenantIdList));
    pgCommonDao.updateDbSyncInfo(dbSyncInfo);
  }

  /**
   * 创建 ch 节点信息
   *
   * @param id db
   * @return
   */
  public ClickhouseNodeInfo createCHNodeInfo(String id) {
    List<DBSyncInfo> dbSyncInfos = pgCommonDao.queryDBSyncInfoById(Lists.newArrayList(id));
    if (CollectionUtils.isNotEmpty(dbSyncInfos)) {
      DBSyncInfo dbSyncInfo = dbSyncInfos.get(0);
      String dbName = CHContext.getDBName(dbSyncInfo.getChDb());
      return ClickhouseNodeInfo.builder().database(dbName).jdbcUrl(dbSyncInfo.getChDb()).cluster("{cluster}").build();
    }
    return null;
  }

  /**
   * 判断是否需要变更为merge状态
   * 如果上次merge日期和当前日期跨天，并且状态是 AGG_ED，SYNC_ABLE，SYNC_ERROR 则修改状态并跳过下面的计算
   * @param dbSyncInfo
   * @return
   */
  public boolean shouldWait4IncrementMergeMergeAggData(DBSyncInfo dbSyncInfo) {
    String chURL = dbSyncInfo.getChDb();
    if (!CHContext.shouldMerge(chURL)) {
      return false;
    }
    String chDb = CHContext.getDBName(dbSyncInfo.getChDb());
    if (GrayManager.isAllowByRule("use_ch_ods_partition", chDb) &&
      Objects.equals(dbSyncInfo.getAllowIncPartition(), WarehouseConfig.OPEN_INC_PARTITION)) {
      log.info("skip shouldWait4IncrementMergeMergeAggData OPEN_INC_PARTITION pgDB:{},pgSchema:{},chDB:{}", dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), chDb);
      return false;
    }
    int updateSize = pgCommonDao.updateDbSyncInfoByIdStatusCAS(SyncStatusEnum.EXCHANGE_AGG.getStatus(), Lists.newArrayList(dbSyncInfo.getId()), Lists.newArrayList(SyncStatusEnum.AGG_ED.getStatus(), SyncStatusEnum.SYNC_ABLE.getStatus(), SyncStatusEnum.SYNC_ERROR.getStatus()));
    if (updateSize == 0) {
      log.warn("updateDbSyncInfoByIdStatusCAS size:{},dbSyncInfo:{}", updateSize, JSON.toJSONString(dbSyncInfo));
    } else {
      log.info("updateDbSyncInfoByIdStatusCAS size:{},dbSyncInfo:{}", updateSize, JSON.toJSONString(dbSyncInfo));
    }
    return true;
  }

  /**
   * 检测是否需要增量merge agg_data
   * 增量merge；merge是CH粒度，一个CH对应多个PG，消息处理是PG粒度；当部分PG状态不ready的时候，等待之
   * @param pgURL pg同步信息
   * @param chURL churl
   * @param pgSchema pg schema
   * @return boolean
   */
  public boolean wait4IncrementMergeAggData(String pgURL, String pgSchema, String chURL) {
    if (!CHContext.openMerge(allowMergeCHDB, chURL)) {
      log.warn("wait4MergeAggData chURL:{},do not allow merge", chURL);
      return false;
    }
    long minuteOfDay = LocalTime.now().getLong(ChronoField.MINUTE_OF_DAY);
    if (minuteOfDay >= this.mergeBeginTime && minuteOfDay <= this.mergeEndTime) {
      List<DBMergeInfoDO> dbSyncInfos = aggMergeDao.queryDBMergeInfoByChUrl(chURL);
      if (CollectionUtils.isEmpty(dbSyncInfos)) {
        log.warn("queryDBSyncInfoByChDB dbSyncInfos is empty chDB:{}", chURL);
        return false;
      }
      boolean bMergeAgg = true;
      //基于partition的merge机制可以多次执行，每个一个partition
      if(!GrayManager.isAllowByRule("merge_agg_in_situation_db", CHContext.getDBName(chURL))){
        bMergeAgg = aggMergeDao.canMergedTodayByDbMergeInfos(dbSyncInfos);
      }
      if (bMergeAgg) {
        Optional<Integer> statusOP = dbSyncInfos.stream()
                                                .map(DBMergeInfoDO::getStatus)
                                                .filter(status -> !Objects.equals(status, SyncStatusEnum.EXCHANGE_AGG.getStatus()))
                                                .findFirst();
        if(statusOP.isPresent()){
          return true;
        }
        try {
          long start = System.currentTimeMillis();
          ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.getInstance(chURL);
          boolean bOk;
          if (GrayManager.isAllowByRule("merge_agg_in_situation_db", CHContext.getDBName(chURL))) {
            //基于partition的新merge方式，直接在agg_data上原地merge，一次merge一个分区
            bOk = mergeTaskService.newMerge(clickhouseNodeInfo, newMergePageSize, chReadTimeOut);
          } else {
            bOk = mergeTaskService.merge(clickhouseNodeInfo, mergePageSize, chReadTimeOut, true, true);
          }
          if (bOk) {
            long mergeAggTime = new Date().getTime();
            int uSize = aggMergeDao.updateAggMergeTime(chURL, mergeAggTime, SyncStatusEnum.EXCHANGE_AGG.getStatus());
            if (dbSyncInfos.size() != uSize) {
              log.error("updateAggMergeTime chURL:{},mergeAggTime:{},size:{}", chURL, mergeAggTime, uSize);
            }
            //执行merge完毕后更新同步状态
            int resetSize = aggMergeDao.changeStatusToRunByChDbUrl(chURL, SyncStatusEnum.EXCHANGE_AGG.getStatus());
            log.info("wait4MergeAggData data merge finished, status: true , chURL:{},resetSize:{},cost:{} ms", chURL, resetSize, (
              System.currentTimeMillis() - start));
            return false;
          } else {
            //agg_data_swap的merge方式，如果全量merge任务在执行中，需要改状态为SYNC_ABLE
            if (!GrayManager.isAllowByRule("merge_agg_in_situation_db", CHContext.getDBName(chURL)) &&
              mergeTaskService.isMergeAllTaskRunning(clickhouseNodeInfo)) {
              aggMergeDao.changeStatusToRunByChDbUrl(chURL, SyncStatusEnum.EXCHANGE_AGG.getStatus());
              log.info("wait4MergeAggData data merge not execute, merge all task is running, chURL:{}, cost:{} ms", chURL, (
                System.currentTimeMillis() - start));
              return false;
            }
            log.info("wait4MergeAggData data merge not execute, status: false, chURL:{}, cost:{} ms", chURL, (System.currentTimeMillis() - start));
          }
          return true;
        } catch (Exception e) {
          aggMergeDao.changeStatusToRunByChDbUrl(chURL, SyncStatusEnum.EXCHANGE_AGG.getStatus());
          log.error("wait4MergeAggData reset status to SYNC_ABLE exception,ch:{}, pg:{}, pgSchema:{}", chURL, pgURL, pgSchema, e);
          return true;
        }
      }
    }
    return false;
  }

  /**
   * 增量同步表
   *
   * @param jdbcConnection
   * @param dbSyncInfo
   * @param tableName
   */
  public void doTransferByTable(JdbcConnection jdbcConnection,
                                DBSyncInfoBO dbSyncInfo,
                                String tableName,
                                List<String> tenantIdList,
                                Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap,
                                String partitionValue,
                                BatchUpdateDbTableSyncInfo batchUpdateDbTableSyncInfo,
                                long defaultMaxModifiedTime) throws Exception {
    List<String> currentIds = Lists.newArrayList(tenantIdList);
    StopWatch stopWatch = StopWatch.createUnStarted(String.format("transfer pg:%s:%s.%s", dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), tableName));
    DbTableSyncInfoDO dbTableSyncInfo = dbTableSyncInfoMap.get(tableName);
    String chDBName = CommonUtils.getDBName(dbSyncInfo.getChDb());
    boolean offline = false;
    if (dbTableSyncInfo == null) {
      List<DbTableSyncInfoDO> dbTableSyncInfos = dbTableSyncInfoService.queryDbTableSyncInfos(dbSyncInfo, tableName);
      if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
        dbTableSyncInfo = dbTableSyncInfos.getFirst();
      } else {
        offline = true;
        dbTableSyncInfo = DbTableSyncInfoDO.createFrom(dbSyncInfo, tableName);
      }
      dbTableSyncInfoMap.put(tableName, dbTableSyncInfo);
    }
    dbTableSyncInfo.setLastSyncTime(new Date().getTime());
    long nextBatchNum = dbSyncInfo.getBatchNum() + 1L;
    if (dbTableSyncInfo.getBatchNum() >= nextBatchNum) {
      log.warn("this table:{} has bean synced table batchNum:{},db:{} batchNum:{}", tableName, dbTableSyncInfo.getBatchNum(), dbSyncInfo.getPgDb(), dbSyncInfo.getBatchNum());
      return;
    } else if ((dbTableSyncInfo.getBatchNum() + 1L) < nextBatchNum && !offline) {
      //该表的同步版本+1还小于db版本并且是增量同步需要提示error
      log.warn("this table:{} batchNum:{},is lower than db batchNum:{},pgDb:{}", tableName, dbTableSyncInfo.getBatchNum(), dbSyncInfo.getBatchNum(), dbSyncInfo.getPgDb());
    }
    if (!chMetadataService.checkCHTableExists(dbSyncInfo.getChDb(), tableName)) {
      if (GrayManager.isAllowByRule("check_ch_table_exists", tableName)) {
        String createSQL = pgMetadataService.createCHTableFromPg(tenantIdList.getFirst(), tableName, chDBName, "{cluster}", CHContext.REPLICATED_REPLACING_MERGE_TREE);
        chdbService.executeUpsertOnCH(dbSyncInfo.getChDb(), new String[] {createSQL});
      } else if (pgMetadataService.isPaas2BiTable(tableName)) {
        String errorMsg = String.format("can not find this table:%s in chDB:%s from pgDB:%s", tableName, dbSyncInfo.getChDb(), dbSyncInfo.getPgDb());
        log.warn(errorMsg);
        QiXinNotifyService.sendTextMessage(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getChDb(), errorMsg);
        return;
      } else {
        log.warn("can not find this table:{} in chDB:{} from pgDB:{}", tableName, dbSyncInfo.getChDb(), dbSyncInfo.getPgDb());
        return;
      }
    }
    Optional<ClickhouseTable> clickhouseTableOP = chMetadataService.loadTable(dbSyncInfo.getPgSchema(), dbSyncInfo.getChDb(), tableName);
    if (clickhouseTableOP.isEmpty()) {
      log.error("loadTable clickhouseTable error table:{} in chDB:{} from pgDB:{}", tableName, dbSyncInfo.getChDb(), dbSyncInfo.getPgDb());
      return;
    }
    PGSchema pgSchema = this.loadSchemaIfInCache(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), tableName);
    String partValue = clickhouseTableOP.get().computePartitionValue(partitionValue);
    long currentMaxSysModifiedTime = clickhouseTableOP.get().existsSysModifiedTime() ? defaultMaxModifiedTime : defaultMaxModifiedTime/1000L;
    Map<String, Set<String>> apiNameEiMapper = Maps.newHashMap();
    if (dbSyncInfo.getAllowPaas2biStatus() == WarehouseConfig.CLOSE_PAAS2BI_STATUS || WarehouseConfig.allowBiPgCHTables.contains(tableName)) { //默认同步pg到ch
      Biz2CHConsumer biz2CHConsumer;
      Pair<Integer/*batchQueryBeforeSize*/, Integer/*savePointSize*/> sizePair = customThreshHold.get(tableName);//自定义同步阈值
      if (sizePair != null) {
        biz2CHConsumer = Biz2CHConsumer.getInstance(chClientService, clickhouseTableOP.get(), sizePair.second, nextBatchNum, chNodeService, chdbService, pgSchema.createOrderByList(), sizePair.first);
      } else {
        biz2CHConsumer = Biz2CHConsumer.getInstance(chClientService, clickhouseTableOP.get(), this.savePointSize, nextBatchNum, chNodeService, chdbService, pgSchema.createOrderByList(), this.batchQueryBeforeSize);
      }
      try {
        if (offline) {
          if (GrayManager.isAllowByRule("use_ch_ods_partition", chDBName) && clickhouseTableOP.get().existsPartitionKey() && Objects.equals(dbSyncInfo.getAllowIncPartition(), WarehouseConfig.OPEN_INC_PARTITION)) {
            this.dropPartitions(dbSyncInfo, tableName, nextBatchNum);
            partValue = WarehouseConfig.STOCK_PARTITION_NAME;
          }
          long maxTimeValue = this.syncByTenantIdOffline(currentIds, jdbcConnection, pgSchema, biz2CHConsumer, apiNameEiMapper, dbSyncInfo.getChDb(), null, partValue, dbSyncInfo.getAllowIncPartition(), dbSyncInfo.getPgDb());
          dbTableSyncInfo.setMaxSysModifiedTime(maxTimeValue);
        } else {
          if (this.hasSysEiTables.contains(tableName)) {
            currentIds.add("-1");
          }
          long fromSysModifiedTime = dbTableSyncInfo.findMaxSysModifiedTime();//获取当前表的sys modified time 和上次对比重置偏移量
          PgSysModifiedTimeInfo pgSysModifiedTimeInfo = pgCommonDao.findMaxSysModifiedTimeByTable(jdbcConnection, pgSchema, fromSysModifiedTime, stopWatch);
          long maxSysModifiedTime = pgSysModifiedTimeInfo.getMaxModifiedTime();
          List<String> offlineTenantIds = currentIds.stream().filter(dbSyncInfo::isNewTenant).toList();//过滤出需要全量同步的租户id
          this.doTransferNewTenants(offlineTenantIds, jdbcConnection, dbSyncInfo, pgSchema, chDBName, clickhouseTableOP.get(), apiNameEiMapper, biz2CHConsumer, Math.max(maxSysModifiedTime, fromSysModifiedTime), partValue);
          if (maxSysModifiedTime > fromSysModifiedTime) {
            dbTableSyncInfo.setMaxSysModifiedTime(maxSysModifiedTime);
            this.doTransferLastSyncTenants(currentIds, jdbcConnection, dbSyncInfo, tableName, pgSchema, apiNameEiMapper, biz2CHConsumer, Pair.build(fromSysModifiedTime, maxSysModifiedTime), partValue);
          }
        }
        biz2CHConsumer.save();
      } catch (Exception e) {
        if (Objects.equals("public", dbSyncInfo.getPgSchema()) && e.getMessage() != null && e.getMessage().contains("Cannot parse")) {
          chMetadataService.invalidateCHTableCache(dbSyncInfo.getChDb(), tableName);
        }
        throw new RuntimeException(String.format("sync error pg:%s,schema:%s,table:%s", dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), tableName), e);
      } finally {
        biz2CHConsumer.closeIfNeed();
        TraceUtils.removeContext();
      }
    }
    dbTableSyncInfo.setApiNameEiMap(JSON.toJSONString(apiNameEiMapper));
    dbTableSyncInfo.setLastModifiedTime(new Date().getTime());
    dbTableSyncInfo.setBatchNum(nextBatchNum);
    dbTableSyncInfo.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
    if(dbSyncInfo.getAllowPaas2biStatus() == WarehouseConfig.ALLOW_PAAS2BI_STATUS && !WarehouseConfig.allowBiPgCHTables.contains(tableName) && currentMaxSysModifiedTime > dbTableSyncInfo.getIncSysModifiedTimeTo() && clickhouseTableOP.get().existsPartitionKey()){
      Map<String, Set<String>> incApiNameEiMapper = chMetadataService.queryPaas2BICHIncInfo(dbSyncInfo,new long[]{0L},clickhouseTableOP.get(),dbTableSyncInfo.getIncSysModifiedTimeTo(),currentMaxSysModifiedTime,pgSchema.getEiName(),currentIds);
      dbTableSyncInfo.setIncSysModifiedTimeFrom(dbTableSyncInfo.getIncSysModifiedTimeTo());
      dbTableSyncInfo.setIncSysModifiedTimeTo(currentMaxSysModifiedTime);
      dbTableSyncInfo.setIncApiNameEiMap(JSON.toJSONString(incApiNameEiMapper));
    }
    if (offline) {
      dbTableSyncInfoService.batchUpsertDbTableSyncInfo(dbSyncInfo, Lists.newArrayList(dbTableSyncInfo));
    } else {
      if (GrayManager.isAllowByRule("batch_update_table_sync_info", chDBName)) {
        batchUpdateDbTableSyncInfo.append(dbTableSyncInfo);
      } else {
        dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfo, Lists.newArrayList(dbTableSyncInfo));
      }
    }
    stopWatch.stop();
    if (stopWatch.getTotalTimeMillis() > 5 * 1000L) {//大于5秒的打印一下日志
      log.info("doTransferByTable table:{},result:{}", tableName, stopWatch.prettyPrint());
    }
  }

  /**
   * 全量同步新增租户的明细数据
   * @param offlineTenantIds
   * @param jdbcConnection
   * @param dbSyncInfo
   * @param pgSchema
   * @param chDBName
   * @param clickhouseTable
   * @param apiNameEiMapper
   * @param biz2CHConsumer
   * @param maxModifiedTime
   * @param partValue
   */
  private void doTransferNewTenants(List<String> offlineTenantIds,
                                    JdbcConnection jdbcConnection,
                                    DBSyncInfoBO dbSyncInfo,
                                    PGSchema pgSchema,
                                    String chDBName,
                                    ClickhouseTable clickhouseTable,
                                    Map<String, Set<String>> apiNameEiMapper,
                                    Biz2CHConsumer biz2CHConsumer,
                                    Long maxModifiedTime,
                                    String partValue) {
    if (GrayManager.isAllowByRule("use_ch_ods_partition", chDBName) && clickhouseTable.existsPartitionKey()) {
      partValue = WarehouseConfig.STOCK_PARTITION_NAME;
    }
    for (String tenantId : offlineTenantIds) {
      TraceUtils.createTrace(tenantId, eieaConverter, ObjectId.get().toString(), "-1000");
      this.syncByTenantIdOffline(Lists.newArrayList(tenantId), jdbcConnection, pgSchema, biz2CHConsumer, apiNameEiMapper, dbSyncInfo.getChDb(), maxModifiedTime, partValue, dbSyncInfo.getAllowIncPartition(), dbSyncInfo.getPgDb());
    }
  }

  /**
   * 同步已同步企业的增量变更
   * @param currentIds
   * @param jdbcConnection
   * @param dbSyncInfo
   * @param tableName
   * @param pgSchema
   * @param apiNameEiMapper
   * @param biz2CHConsumer
   * @param sysTimeRange
   * @param partValue
   */
  private void doTransferLastSyncTenants(List<String> currentIds,
                                         JdbcConnection jdbcConnection,
                                         DBSyncInfoBO dbSyncInfo,
                                         String tableName,
                                         PGSchema pgSchema,
                                         Map<String, Set<String>> apiNameEiMapper,
                                         Biz2CHConsumer biz2CHConsumer,
                                         Pair<Long, Long> sysTimeRange,
                                         String partValue) {
    for (String tenantId : currentIds) {
      if (dbSyncInfo.isNewTenant(tenantId)) {
        log.info("tenantId:{},table:{},has all synced offline skip!", tenantId, tableName);
        continue;
      }
      TraceUtils.createTrace(tenantId, eieaConverter, ObjectId.get().toString(), "-1000");
      BITransferPageTag biTransferPageTag = this.transfer2CHByTenantId(jdbcConnection, pgSchema, Lists.newArrayList(tenantId), biz2CHConsumer, sysTimeRange, false, partValue, dbSyncInfo.getAllowIncPartition(), dbSyncInfo.getPgDb());
      if (!MapUtils.isNullOrEmpty(biTransferPageTag.getApiNameEiMapper())) {
        biTransferPageTag.getApiNameEiMapper().forEach((k, v) -> {
          apiNameEiMapper.computeIfAbsent(k, key -> Sets.newHashSet()).addAll(v);
        });
      }
    }
  }

  /**
   * 如果开启分区的话需要删除增量分区
   * @param dbSyncInfo
   * @param tableName
   */
  private void dropPartitions(DBSyncInfoBO dbSyncInfo, String tableName,long batchNum) {
    String jedisK = String.format("ods:%s:%s:all", dbSyncInfo.getId(),tableName);
    try (JedisLock jedisLock = new JedisLock(jedisCmd, jedisK, 30 * 60 * 1000)) {
      if (jedisLock.tryLock()) {
        List<String> partitions = dbSyncInfoFlowDao.queryPartitions(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), dbSyncInfo.getId());
        if (CollectionUtils.isNotEmpty(partitions)) {
          partitions.forEach(partition -> {
            chMetadataService.dropPartition(dbSyncInfo.getChDb(), tableName, partition);
            log.info("dropPartitions finish chDB:{},pgDB:{},table:{},batchNum:{},partition:{}", dbSyncInfo.getChDb(), dbSyncInfo.getPgDb(), tableName, batchNum, partition);
          });
        }
        if (GrayManager.isAllowByRule("ods_delete_stock_data", tableName)) {//如果支持删除存量分区的话，则删除存量分区
          chMetadataService.dropPartition(dbSyncInfo.getChDb(), tableName, WarehouseConfig.STOCK_PARTITION_NAME);
        }
      }else{
        throw new RuntimeException(String.format("execute insert sql tryLock fail table:%s,dhDB:%s,pgDB:%s,batchNum:%d", tableName, dbSyncInfo.getChDb(), dbSyncInfo.getPgDb(),batchNum));
      }
    } catch (Exception e) {
      throw new RuntimeException(String.format("dropPartitions tryLock error chDB:%s,pgDB:%s,table:%s,batchNum:%d", dbSyncInfo.getChDb(), dbSyncInfo.getPgDb(), tableName, batchNum), e);
    }
  }

  /**
   * 全量同步某个表中的所有租户数据
   *
   * @param currentIds      租户id
   * @param jdbcConnection  jdbc 连接
   * @param pgSchema        pg表schema信息
   * @param biz2CHConsumer  写入ch程序
   * @param apiNameEiMapper 变更apiName
   */
  public long syncByTenantIdOffline(List<String> currentIds,
                                    JdbcConnection jdbcConnection,
                                    PGSchema pgSchema,
                                    Biz2CHConsumer biz2CHConsumer,
                                    Map<String, Set<String>> apiNameEiMapper,
                                    String chDb,
                                    Long maxModifiedTime,
                                    String partitionValue,
                                    Integer allowIncPartition,
                                    String pgDB) {
    long maxTimeValue = 0L;
    Map<String, Long> tenantIdAndMaxTimeMap = Maps.newHashMap();
    //获取最大的变更时间戳（纳秒）
    for (String tenantId : currentIds) {
      long startOffline = System.currentTimeMillis();
      if (!"-1".equals(tenantId)) {
        TraceUtils.createTrace(tenantId, eieaConverter, ObjectId.get().toString(), "-1000");
      }
      Pair<String, String> schemaAndTableName = pgSchema.schemaAndTableName();
      if (!"-1".equals(tenantId) && schemaAndTableName.first.startsWith("sch_") && (StringUtils.equalsAny(schemaAndTableName.second, "dim_sys_area_gray", "dim_sys_date"))) {
        //如果是独立schema并且是日期维度和国家省市区维度则需要全量同步-1数据
        BITransferPageTag systemTransferPageTag = this.transfer2CHByTenantId(jdbcConnection, pgSchema, Lists.newArrayList("-1"), biz2CHConsumer, null, true, partitionValue, allowIncPartition, pgDB);
        if (systemTransferPageTag.getMaxTimeValue() > maxTimeValue) {
          maxTimeValue = systemTransferPageTag.getMaxTimeValue();
        }
      }
      BITransferPageTag biTransferPageTag = this.transfer2CHByTenantId(jdbcConnection, pgSchema, Lists.newArrayList(tenantId), biz2CHConsumer, null, true, partitionValue, allowIncPartition, pgDB);
      if (!MapUtils.isNullOrEmpty(biTransferPageTag.getApiNameEiMapper())) {
        biTransferPageTag.getApiNameEiMapper()
                         .forEach((k, v) -> apiNameEiMapper.computeIfAbsent(k, key -> Sets.newHashSet()).addAll(v));
      }
      tenantIdAndMaxTimeMap.put(tenantId, biTransferPageTag.getMaxTimeValue());
      if (biTransferPageTag.getMaxTimeValue() > maxTimeValue) {
        maxTimeValue = biTransferPageTag.getMaxTimeValue();
      }
      log.info("syncByTenantIdOffline pgDB:{},chDB:{},table:{},tenantId:{},maxTimeValue:{},cost:{}", pgDB, chDb, pgSchema.getName(), tenantId, biTransferPageTag.getMaxTimeValue(),System.currentTimeMillis()-startOffline);
    }
    if (maxModifiedTime != null && maxTimeValue < maxModifiedTime) {
      maxTimeValue = maxModifiedTime;
    }
    //需要往org_employee_user 同步一条系统数据
    if (Objects.equals("org_employee_user", pgSchema.schemaAndTableName().second)) {
      this.chdbService.batchInsertSysUser(currentIds, chDb);
    }
    //对于已经导入的租户数据如果，导入的时间偏移小于maxTimeValue 则需要补充发送，防止漏掉数据。
    for (Map.Entry<String, Long> eiAndMaxTime : tenantIdAndMaxTimeMap.entrySet()) {
      if (eiAndMaxTime.getValue() < maxTimeValue) {
        TraceUtils.createTrace(eiAndMaxTime.getKey(), eieaConverter, ObjectId.get().toString(), "-1000");
        BITransferPageTag biTransferPageTag = this.transfer2CHByTenantId(jdbcConnection, pgSchema, Lists.newArrayList(eiAndMaxTime.getKey()), biz2CHConsumer, Pair.build(eiAndMaxTime.getValue(), maxTimeValue), true, partitionValue, allowIncPartition, pgDB);
        if (!MapUtils.isNullOrEmpty(biTransferPageTag.getApiNameEiMapper())) {
          biTransferPageTag.getApiNameEiMapper()
                           .forEach((k, v) -> apiNameEiMapper.computeIfAbsent(k, key -> Sets.newHashSet()).addAll(v));
        }
      }
    }
    return maxTimeValue;
  }


  /**
   * 同步系统租户数据如：dim_sys_date,dim_sys_area_gray
   *
   * @param dbSyncIds
   * @param tableName
   */
  public void syncDataByTenantId(List<String> dbSyncIds, String tableName, List<String> tenantIds,String partitionValue) {
    List<DBSyncInfo> dbSyncInfos = pgCommonDao.queryDBSyncInfoById(dbSyncIds);
    for (DBSyncInfo dbSyncInfo : dbSyncInfos) {
      try (JdbcConnection jdbcConnection = pgDataSource.getConnectionByPgbouncerURL(dbSyncInfo.getPgDb(),dbSyncInfo.getPgSchema())) {
        PGSchema pgSchema = this.loadSchemaIfInCache(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), tableName);
        Optional<ClickhouseTable> clickhouseTableOP = chMetadataService.loadTable(dbSyncInfo.getPgSchema(),
          dbSyncInfo.getChDb(), tableName);
        if (clickhouseTableOP.isEmpty()) {
          log.error("create clickhouseTable error table:{} in chDB:{} from pgDB:{}", tableName, dbSyncInfo.getChDb(), dbSyncInfo.getPgDb());
          return;
        }
        Biz2CHConsumer biz2CHConsumer = Biz2CHConsumer.getInstance(chClientService, clickhouseTableOP.get(), this.savePointSize, dbSyncInfo.getBatchNum(), chNodeService, chdbService, pgSchema.createOrderByList(), this.batchQueryBeforeSize);
        Map<String, Set<String>> apiNameEiMapper = Maps.newHashMap();
        this.syncByTenantIdOffline(tenantIds, jdbcConnection, pgSchema, biz2CHConsumer, apiNameEiMapper, dbSyncInfo.getChDb(), null, partitionValue, null, dbSyncInfo.getPgDb());
        biz2CHConsumer.save();
      } catch (Exception e) {
        log.error("trans sys data error pgDb:{},chDB:{},schema:{},table:{}", dbSyncInfo.getPgDb(), dbSyncInfo.getChDb(), dbSyncInfo.getPgSchema(), tableName, e);
      }
    }
  }

  /**
   * 按照主键同步数据
   *
   * @param dbSyncId    db_sync_info.id
   * @param primaryKeys 要同步的业务表数据主键
   */
  public void syncDataByTenantIdPrimaryKey(String dbSyncId,
                                           String tableName,
                                           String tenantId,
                                           List<String> primaryKeys,
                                           boolean offline,
                                           String partitionValue) {
    List<DBSyncInfo> dbSyncInfos = pgCommonDao.queryDBSyncInfoById(Lists.newArrayList(dbSyncId));
    if (CollectionUtils.isNotEmpty(dbSyncInfos)) {
      DBSyncInfo dbSyncInfo = dbSyncInfos.getFirst();
      try (JdbcConnection jdbcConnection = pgDataSource.getConnectionByPgbouncerURL(dbSyncInfo.getPgDb(),dbSyncInfo.getPgSchema())) {
        PGSchema pgSchema = this.loadSchemaIfInCache(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema(), tableName);
        Optional<ClickhouseTable> clickhouseTableOP = chMetadataService.loadTable(dbSyncInfo.getPgSchema(),
          dbSyncInfo.getChDb(), tableName);
        if (clickhouseTableOP.isEmpty()) {
          log.error("create clickhouseTable error table:{} in chDB:{} from pgDB:{}", tableName, dbSyncInfo.getChDb(), dbSyncInfo.getPgDb());
          return;
        }
        Biz2CHConsumer biz2CHConsumer = Biz2CHConsumer.getInstance(chClientService, clickhouseTableOP.get(), this.savePointSize, dbSyncInfo.getBatchNum(), chNodeService, chdbService, pgSchema.createOrderByList(), this.batchQueryBeforeSize);
        this.transfer2CHByTenantIdPrimaryKey(jdbcConnection, pgSchema, Lists.newArrayList(tenantId), biz2CHConsumer, primaryKeys, offline, partitionValue);
        biz2CHConsumer.save();
      } catch (Exception e) {
        log.error("trans sys data error pgDb:{},chDB:{},schema:{},table:{}", dbSyncInfo.getPgDb(),
          dbSyncInfo.getChDb(), dbSyncInfo.getPgSchema(), tableName, e);
      }
    }
  }

  /**
   * 写入clickhouse
   * 支持按照企业拉取，也支持按照表拉取
   *
   * @param schema
   * @param tenantIdList
   * @param biz2CHConsumer
   */
  public BITransferPageTag transfer2CHByTenantId(JdbcConnection jdbcConnection,
                                                 PGSchema schema,
                                                 List<String> tenantIdList,
                                                 Biz2CHConsumer biz2CHConsumer,
                                                 Pair<Long, Long> sysTimeRange,
                                                 boolean offline,
                                                 String partitionName,
                                                 Integer allowIncPartition,
                                                 String pgDB) {
    long allBegin = System.currentTimeMillis();
    BITransferPageTag biTransferPageTag = new BITransferPageTag(null, this.batchSize, 0L, 0L, Maps.newHashMap());
    List<String> afterFilterEis = tenantIdList.stream()
                                           .filter(tenantId -> !GrayManager.isAllowByRule("skip_transfer_ei_table",
                                             tenantId + "^" + schema.findTableName()))
                                           .toList();
    if (CollectionUtils.isEmpty(afterFilterEis)) {
      log.warn("transfer2CHByTenantId pgDB:{},chDB:{},table:{},afterFilterEis is empty beforeFilter:{}",pgDB,biz2CHConsumer.getDbUrl(),schema.findTableName(),JSON.toJSONString(tenantIdList));
      return biTransferPageTag;
    }
    String sqlTemplate = schema.createQuerySQL(afterFilterEis, "ASC", sysTimeRange, this.batchSize);
    Pair<String, String> filterTime = schema.findFilterTimeColumn();
    AtomicLong counter = new AtomicLong(0L);
    boolean needBefore = !this.skipBeforeTableSet.contains(schema.schemaAndTableName().second);
    try {
      String startObjectId = "0";
      do {
        jdbcConnection.query(String.format(sqlTemplate, startObjectId), resultSet -> {
          try {
            this.dealWithResult(resultSet, biTransferPageTag, biz2CHConsumer, schema.getEiName(), schema.findPrimaryKeyId(), filterTime.first, offline, needBefore, partitionName, allowIncPartition);
            counter.addAndGet(biTransferPageTag.getRealSize());
          } catch (Exception e) {
            throw new RuntimeException(e);
          }
        });
        startObjectId = biTransferPageTag.getStartObjectId();
      } while (startObjectId != null);
    } catch (Exception e) {
      String errorMsg = String.format("transfer 2 ch error tableName:%s,chDbURL:%s,tenantId:%s,fromId:%s,cost:%d", schema.getName(), biz2CHConsumer.getDbUrl(), JSON.toJSONString(afterFilterEis),biTransferPageTag.getStartObjectId(),(System.currentTimeMillis() - allBegin));
      log.error(errorMsg, e);
      QiXinNotifyService.sendTextMessage(pgDB, schema.getName(), biz2CHConsumer.getDbUrl(), errorMsg);
      throw new RuntimeException(e);
    }
    long cost = (System.currentTimeMillis() - allBegin);
    String timeRange = sysTimeRange == null ? "all" : sysTimeRange.toString();
    if (counter.get() > 0) {
      log.info("transfer to clickhouse end,db:{},chDB:{},table:{},batchNum:{},timeRange:{},tenantIds:{},allTotal:{},totalCost:{}", pgDB, biz2CHConsumer.getDbUrl(), schema.getName(), biz2CHConsumer.getBatchNum(), timeRange, JSON.toJSONString(afterFilterEis), counter.get(), cost);
    }
    return biTransferPageTag;
  }

  /**
   * 按照primaryKey 同步CH
   *
   * @param jdbcConnection
   * @param schema
   * @param tenantIdList
   * @param biz2CHConsumer
   * @param primaryKeys
   * @param offline
   * @return
   */
  public BITransferPageTag transfer2CHByTenantIdPrimaryKey(JdbcConnection jdbcConnection,
                                                           PGSchema schema,
                                                           List<String> tenantIdList,
                                                           Biz2CHConsumer biz2CHConsumer,
                                                           List<String> primaryKeys,
                                                           boolean offline,
                                                           String partitionValue) {
    long allBegin = System.currentTimeMillis();
    String sql = schema.createQuerySQLByPrimaryKey(tenantIdList, "ASC", primaryKeys, this.batchSize);
    Pair<String, String> filterTime = schema.findFilterTimeColumn();
    BITransferPageTag biTransferPageTag = new BITransferPageTag(null, this.batchSize, 0L, 0L, Maps.newHashMap());
    AtomicLong counter = new AtomicLong(0L);
    boolean needBefore = !this.skipBeforeTableSet.contains(schema.schemaAndTableName().second);
    try {
      jdbcConnection.query(sql, resultSet -> {
        try {
          this.dealWithResult(resultSet, biTransferPageTag, biz2CHConsumer, schema.getEiName(), schema.findPrimaryKeyId(), filterTime.first, offline, needBefore, partitionValue, null);
          counter.addAndGet(biTransferPageTag.getRealSize());
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      });
    } catch (Exception e) {
      log.error("transfer 2 ch error tableName:{},chDbURL:{},tenantId:{}", schema.getName(),
        biz2CHConsumer.getDbUrl(), JSON.toJSONString(tenantIdList), e);
      throw new RuntimeException(e);
    }
    log.info("transfer to clickhouse end,db:{},table:{},tenantIds:{},allTotal:{},totalCost:{}", schema.getDb(), schema.getName(), JSON.toJSONString(tenantIdList), counter.get(),
      System.currentTimeMillis() - allBegin);
    return biTransferPageTag;
  }

  /**
   * 清洗pg数据，写入CH，Streaming方式写入，
   * 中间会后反查CH生成before数据
   *
   * @param resultSet         {@link java.sql.ResultSet}
   * @param biTransferPageTag 分页查询组件
   * @param biz2CHConsumer    写入CH组件
   * @param eiName            ei列名
   * @param primaryId         id列名
   * @throws Exception
   */
  private void dealWithResult(ResultSet resultSet,
                              final BITransferPageTag biTransferPageTag,
                              final Biz2CHConsumer biz2CHConsumer,
                              final String eiName,
                              final String primaryId,
                              String timeField,
                              boolean offline,
                              boolean needBefore,
                              String partitionValue,
                              Integer allowIncPartition) throws Exception {
    ResultSetMetaData metaData = resultSet.getMetaData();
    long maxTimeValue = 0L;
    String currentId = null;
    long curBatchSize = 0L;
    Map<String/*tenantId_objName*/, List<UdfObjFieldDO>> objFieldListMap = new HashMap<>();
    while (resultSet.next()) {
      Map<String, Object> recordMap = Maps.newHashMap();
      for (int i = 1; i <= metaData.getColumnCount(); i++) {
        recordMap.put(metaData.getColumnName(i), resultSet.getObject(i));
      }
      //ch中新增的两个固定列用于区分
      recordMap.put(CHContext.BI_SYS_FLAG, CHContext.BI_SYS_FLAG_AFTER);
      recordMap.put(CHContext.BI_SYS_BATCH_ID, biz2CHConsumer.getBatchNum());
      if (StringUtils.isNotBlank(partitionValue)) {
        recordMap.put(WarehouseConfig.ODS_PARTITION_KEY, partitionValue);
      }
      if (Objects.equals(WarehouseConfig.OPEN_INC_PARTITION, allowIncPartition)) {
        needBefore = false;
      }
      //ch中增加了一个固定列 bi_sys_is_deleted
      this.dealWithDelete(recordMap);
      if (recordMap.containsKey(primaryId)) {
        currentId = String.valueOf(recordMap.get(primaryId));
      }
      //获取BI对象变更记录,schema隔离的租户自定义对象的表名称全部小写了，
      String objectDescribeApiName = biz2CHConsumer.getTable();
      if (StringUtils.equalsAnyIgnoreCase(biz2CHConsumer.getTable(), CHContext.OBJECT_DATA, CHContext.BIZ_ACCOUNT, CHContext.GOAL_VALUE)) {
        String apiName = String.valueOf(recordMap.get(CHContext.OBJECT_DESCRIBE_API_NAME));
        switch (apiName) {
          case CHContext.AccountObj -> objectDescribeApiName = CHContext.BIZ_ACCOUNT;
          case CHContext.AccountMainDataObj -> objectDescribeApiName = CHContext.BIZ_ACCOUNT_MAIN_DATA;
          case CHContext.GoalValueObj -> objectDescribeApiName = CHContext.GOAL_VALUE;
          default -> objectDescribeApiName = apiName;
        }
      } else if (StringUtils.equalsAnyIgnoreCase(biz2CHConsumer.getTable(), CHContext.OBJECT_DATA_LANG)) {
        objectDescribeApiName = recordMap.get(CHContext.OBJECT_DESCRIBE_API_NAME) + CHContext._LANG;
      }
      //todo stage_runtime_new_opportunity 没有做单独处理处理不够精细
      Object ei = recordMap.get(eiName);
      if (objectDescribeApiName != null && ei != null) {
        biTransferPageTag.putApiAndEi(objectDescribeApiName, String.valueOf(ei));
      }
      if (Objects.equals(biz2CHConsumer.getTable(), CHContext.OBJECT_DATA)) {
        this.formatString(ei, recordMap, objFieldListMap);
      }
      //清洗标签表
      if ("mt_sub_tag".equalsIgnoreCase(biz2CHConsumer.getTable())) {
        String numKey = String.format("%s_%s", recordMap.get("num"), recordMap.get("key"));
        recordMap.put(CHContext.MT_SUB_TAG_EXT, numKey);
      }
      //topology table 没有必要同步 view sql 和 stat_json 两个大文本字段
      if (Objects.equals(biz2CHConsumer.getTable(), CHContext.BI_MT_TOPOLOGY_TABLE)) {
        recordMap.put("stat_list_json", "");
        recordMap.put("view_sql", "");
        recordMap.put("pg_detail_sql", "");
      }
      //获取最大的日期过滤值
      if (offline) {
        Object timeValue = recordMap.get(timeField);
        Long longValue = NumberHelper.parseLong(timeValue, " {} is not long", timeValue);
        if (longValue != null && longValue > maxTimeValue) {
          maxTimeValue = longValue;
        }
      }
      long beginQueueStart = System.currentTimeMillis();
      biz2CHConsumer.queue(new BizLog(recordMap), offline, needBefore);
      if(GrayManager.isAllowByRule("show_queue_log",ei+"^"+biz2CHConsumer.getTable())){
        log.info("before queue tenantId:{},table:{},currentId:{},cost:{}ms", ei,biz2CHConsumer.getTable(),currentId,System.currentTimeMillis()-beginQueueStart);
      }
      curBatchSize++;
    }
    biTransferPageTag.setRealSize(curBatchSize);
    biTransferPageTag.setMaxTimeValue(Math.max(maxTimeValue, biTransferPageTag.getMaxTimeValue()));
    //如果执行后的条数小于每页大小认为是最有一页直接将object_id 置为null
    if (biTransferPageTag.isLastPage()) {
      biTransferPageTag.setStartObjectId(null);
    } else {
      biTransferPageTag.setStartObjectId(currentId);
    }
  }

  /**
   * 判断本数据是否要在查询时候过滤掉
   *
   * @param recordMap 数据记录
   */
  private void dealWithDelete(Map<String, Object> recordMap) {
    recordMap.put(CHContext.BI_SYS_IS_DELETED, 0);
  }

  /**
   * 自定义对象的自定义字段和预置对象的扩展字段，将json数组格式或 | 分割格式数据转换为逗号连接字符串
   *
   * @param objFieldListMap objFieldListMap缓存，减少查库次数
   */
  private void formatString(Object ei,
                            Map<String, Object> recordMap,
                            Map<String, List<UdfObjFieldDO>> objFieldListMap) {
    String apiName = String.valueOf(recordMap.get(CHContext.OBJECT_DESCRIBE_API_NAME));
    String tenantId = String.valueOf(ei);
    List<Integer> udfObjFieldDOList;
    if (GrayManager.isAllowByRule("arrayFormatFieldGetFromDb", tenantId)) {
      // 查 udfObjFieldDao 中的缓存
      udfObjFieldDOList = objFieldListMap.computeIfAbsent(tenantId + "_" + apiName, k -> udfObjFieldDao.getNeedFormatFieldsByObjName(Integer.parseInt(tenantId), apiName))
                                         .stream()
                                         .map(UdfObjFieldDO::getFieldLocation)
                                         .map(Integer::parseInt)
                                         .toList();
    } else {
      udfObjFieldDOList = udfObjFieldDao.getFieldsByObjNameAndType(tenantId, apiName);
    }
    if (CollectionUtils.isEmpty(udfObjFieldDOList)) {
      // log.info("object_data formatString udfObjFieldDOList is null!, ei:{}, objName:{}", ei, apiName);
      return;
    }
    udfObjFieldDOList.forEach(fieldLocation -> {
      String columnName = "value" + fieldLocation;
      Object value = recordMap.get(columnName);
      if (Objects.nonNull(value) && StringUtils.isNotBlank(String.valueOf(value))) {
        String convert = String.valueOf(value).replaceAll("[\\[\\]{}\"']", "").replaceAll("\\|", ",");
        recordMap.put(columnName, convert);
      }
    });
  }

  /**
   * 公共库的 pgSchema信息是否可以只保留一份？
   * 这个方法只能在固定方法中调用，如果只涉及到主键，日期，ei名称
   *
   * @param db         padb
   * @param schemaName schema名称
   * @param tableName  表名称
   * @return {@link PGSchema}
   * @throws SQLException
   */
  private PGSchema loadSchemaIfInCache(String db, String schemaName, String tableName) throws SQLException {
    if (Objects.equals("public", schemaName)) {
      PGSchema pgSchema = publicTableSchema.get(schemaName + "_" + tableName, key -> {
        try {
          return pgMetadataService.loadSchema(db, schemaName + "." + tableName);
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      });
      if (pgSchema != null) {
        //因为从缓存中获取的，需要重新修正一下db信息,线程不安全
//        pgSchema.setDb(db);
        return pgSchema;
      }
    }
    return pgMetadataService.loadSchema(db, schemaName + "." + tableName);
  }

  /**
   * @param key
   */
  public void invalidatePgSchema(String key) {
    publicTableSchema.invalidate(key);
  }
}
