package com.fxiaoke.bi.warehouse.dws.exception;

/**
 * @Author:jief
 * @Date:2023/12/27
 */
public class ParseRuleException extends RuntimeException{
  public ParseRuleException() {
    super();
  }

  public ParseRuleException(String message) {
    super(message);
  }

  public ParseRuleException(String message, Throwable cause) {
    super(message, cause);
  }

  public ParseRuleException(Throwable cause) {
    super(cause);
  }

  protected ParseRuleException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
  }
}
