package com.fxiaoke.bi.warehouse.dws.agg.service.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.arg.AggRequestContext;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.dws.agg.bean.AggCalResult;
import com.fxiaoke.bi.warehouse.dws.agg.service.AbstractAggHandler;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBAggInfoDAO;
import com.fxiaoke.bi.warehouse.dws.model.DBUpdatedEvent;
import com.fxiaoke.bi.warehouse.dws.service.DWSComputeService;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * @Author:jief
 * @Date:2024/12/7
 */
@Slf4j
public class AggCal2StockBeforeHandler extends AbstractAggHandler<Boolean> {
  @Resource
  private DBAggInfoDAO dbAggInfoDAO;
  @Override
  public Boolean doHandler(AggRequestContext aggRequestContext) {
    DBUpdatedEvent event = aggRequestContext.getRequestArg("DBUpdatedEvent", DBUpdatedEvent.class);
    StopWatch stopWatch = aggRequestContext.getRequestArg("StopWatch", StopWatch.class);
    AggCalResult aggCalResult = aggRequestContext.getRequestArg("AggCalResult", AggCalResult.class);
    Preconditions.checkArgument(event != null, "DBUpdatedEvent is null!");
    Preconditions.checkArgument(stopWatch != null, "StopWatch is null!");
    stopWatch.start("AggCal2StockBeforeHandler");
    if (aggCalResult == null) {
      aggCalResult = this.prepare(event, stopWatch);
    }
    try {
      Integer result = dbAggInfoDAO.updateDBAggInfoStatus(event.getId(), SyncStatusEnum.CAL_2_STOCK_BEFORE_ING.getStatus(), event.getAggInfoVersion());
      if (result != null) {
        event.setAggInfoVersion(result);
        DWSComputeService.copyCal2StockBeforeIng.add(event.getId());
        this.doInsertSQLs(event, aggCalResult.getCalPrepares(), SyncStatusEnum.CAL_2_STOCK_BEFORE_ING.getStatus(), true);
        log.info("cal2StockBefore success,pgDB:{},chDB:{},pgSchema:{},batchNums:{}", event.getPgJdbcUrl(), event.getChJdbcUrl(), event.getPgSchema(), JSON.toJSONString(event.getBatchNums()));
        DWSComputeService.copyCal2StockBeforeIng.remove(event.getId());
        result = dbAggInfoDAO.updateDBAggInfoStatus(event.getId(), SyncStatusEnum.AGG_ING.getStatus(), event.getAggInfoVersion());
        if (result != null) {
          DWSComputeService.calculatePgDB.add(event.getId());
          event.setAggInfoVersion(result);
        } else {
          log.error("dbAggInfoDAO.updateDBAggInfoStatus yncStatusEnum.AGG_ING:{},id:{},version:{}",SyncStatusEnum.AGG_ING.getStatus(),event.getId(),event.getAggInfoVersion());
          return false;
        }
        if (this.getNextHandler() != null) {
          return this.getNextHandler().doHandler(aggRequestContext);
        }
        return true;
      }
    } finally {
      DWSComputeService.copyCal2StockBeforeIng.remove(event.getId());
      stopWatch.stop("AggCal2StockBeforeHandler");
    }
    return false;
  }
}
