package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.DashBoardGlobalFilterConfigDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author:jief
 * @Date:2023/7/10
 */
@Repository
public interface DashBoardGlobalFilterConfigMapper extends ICrudMapper<DashBoardGlobalFilterConfigDO>,
  IBatchMapper<DashBoardGlobalFilterConfigDO>, ITenant<DashBoardGlobalFilterConfigMapper> {

  @Select("select * from dash_board_global_filter_config where tenant_id=#{tenantId} and " +
    "dash_board_id=any(array[#{dash_board_id}]) and is_deleted=0 limit 1")
  List<DashBoardGlobalFilterConfigDO> queryByDashBoardId(@Param("tenantId") String tenantId,
                                                         @Param("dash_board_id") String[] dashBoardId);
}
