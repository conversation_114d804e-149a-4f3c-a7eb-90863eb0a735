package com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.mapper;

import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/4/2
 */
@Mapper
public interface AggDataSyncInfoMapper extends ITenant<AggDataSyncInfoMapper> {

  @Select("select tenant_id,view_id from agg_data_sync_info limit 1")
  List<Map<String,Object>> queryAggDataSyncInfo();

  /**
   * 根据企业Id，图表id，图表版本，同步批次号查询ch agg_data_sync_info信息
   */
  @Select("SELECT * FROM agg_data_sync_info WHERE tenant_id = #{tenantId} AND view_id = #{viewId} AND view_version = #{viewVersion} AND batch_num = #{batchNum}")
  AggDataSyncInfoDo queryViewAggDataSyncInfo(@Param("tenantId") String tenantId, @Param("viewId") String viewId, @Param("viewVersion") String viewVersion, @Param("batchNum") int batchNum);

  /**
   * 根据企业Id，图表id查询agg_data_sync_info信息
   */
  @Select("SELECT * FROM agg_data_sync_info WHERE tenant_id = #{tenantId} AND view_id = #{viewId} order by timestamp desc limit 1")
  List<AggDataSyncInfoDo> queryAggDataSyncByTenantIdAndViewId(@Param("tenantId") String tenantId, @Param("viewId") String viewId);

  /**
   * 根据企业Id,图表id，同步策略policy_id查询agg_data_sync_info
   */
  @Select("SELECT * FROM agg_data_sync_info WHERE tenant_id = #{tenantId} AND view_id = #{viewId} AND policy_id = #{policyId} order by timestamp desc limit 1")
  List<AggDataSyncInfoDo> queryAggDataSyncByPolicyId(@Param("tenantId") String tenantId, @Param("viewId") String viewId, @Param("policyId") String policyId);

  /**
   * 根据策略id查询所有的策略
   */
  @Select({
    "<script>",
    "SELECT *",
    "FROM agg_data_sync_info final ",
    "WHERE policy_id IN ",
    "<foreach item='policyId' collection='policyIds' open='(' separator=',' close=')'>",
    "#{policyId}",
    "</foreach>",
    "AND is_deleted = 0",
    "</script>"
  })
  List<AggDataSyncInfoDo> queryAggDataSyncByPolicyIds(@Param("policyIds") List<String> policyIds);


  /**
   * 根据企业Id，图表id，图表版本，同步批次号更新ch agg_data_sync_info status状态
   */
  @Update("UPDATE agg_data_sync_info SET status = #{aggDataSyncInfoDo.status} WHERE tenant_id = #{aggDataSyncInfoDo.tenantId} AND view_id = #{aggDataSyncInfoDo.viewId} AND view_version = #{aggDataSyncInfoDo.viewVersion} AND batch_num = #{aggDataSyncInfoDo.batchNum}")
  int updateAggDataSyncInfoStatus(@Param("aggDataSyncInfoDo") AggDataSyncInfoDo aggDataSyncInfoDo);

  /**
   * ch中用添加代替更新操作
   */
  @Insert("INSERT INTO agg_data_sync_info(tenant_id, view_id, view_version, batch_num, status, max_sync_timestamp, timestamp, is_deleted) VALUES (#{tenantId}, #{viewId}, #{viewVersion}, #{batchNum}, #{status}, #{maxSyncTimeStamp}, #{timestamp}, #{isDeleted})")
  int insertAggDataSyncInfo(AggDataSyncInfoDo aggDataSyncInfoDo);

  /**
   * ch中添加替代更新操作
   */
  @Insert("INSERT INTO agg_data_sync_info(tenant_id, view_id, policy_id, view_version, stat_view_unique_key, batch_num, status, max_sync_timestamp, timestamp, is_deleted) VALUES (#{tenantId}, #{viewId}, #{policyId}, #{viewVersion}, #{statViewUniqueKey}, #{batchNum}, #{status}, #{maxSyncTimeStamp}, #{timestamp}, #{isDeleted})")
  int insertAggDataSyncInfoByPolicyId(AggDataSyncInfoDo aggDataSyncInfoDo);

  /**
   * ch中添加替代更新操作 - 批量
   */
  @Insert("<script>" +
    " INSERT INTO agg_data_sync_info " +
    " (tenant_id, view_id, policy_id, view_version, stat_view_unique_key, batch_num, status, max_sync_timestamp, timestamp, is_deleted) " +
    " VALUES " +
    " <foreach collection='aggDataSyncInfoDoList' item='item' index='index' separator=','>" +
    " (#{item.tenantId}, #{item.viewId}, #{item.policyId}, #{item.viewVersion}, #{item.statViewUniqueKey}, #{item.batchNum}, #{item.status}, #{item.maxSyncTimeStamp}, #{item.timestamp}, #{item.isDeleted})" +
    " </foreach>" +
    "</script>")
  void insertAggDataSyncInfoBatch(@Param("aggDataSyncInfoDoList") List<AggDataSyncInfoDo> aggDataSyncInfoDoList);

  @Select("SELECT quantile(${calcFlowStatCostQuantile})(cost) / 1000 AS cost FROM bi_agg_log WHERE tenant_id = #{tenantId} AND view_id = #{viewId} AND field_id = #{fieldId} GROUP BY tenant_id, view_id, view_version, field_id ORDER BY view_version DESC LIMIT 1")
  Double queryCostFromBiAggLog(@Param("tenantId") String tenantId, @Param("viewId") String viewId, @Param("fieldId") String fieldId, @Param("calcFlowStatCostQuantile") double calcFlowStatCostQuantile);
}
