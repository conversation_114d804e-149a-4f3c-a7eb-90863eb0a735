package com.fxiaoke.bi.warehouse.core.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Author:jief
 * @Date:2024/10/10
 */
@Data
@Table(name = "bi_mt_database")
public class BIMtDatabaseDO {
  @Id
  @Column(name = "id")
  private String id;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "database_name")
  private String databaseName;
  @Column(name = "master")
  private String master;
  @Column(name = "slave")
  private String slave;
  @Column(name = "user_name")
  private String userName;
  @Column(name = "password")
  private String password;
  @Column(name = "create_time")
  private long createTime;
  @Column(name = "last_modified_time")
  private long lastModifiedTime;
  @Column(name = "parameters")
  private String parameters;
  @Column(name = "description")
  private String description;
  @Column(name = "status")
  private String status;
  @Column(name = "biz")
  private String biz;
  @Column(name = "created_by")
  private String createdBy;
  @Column(name = "last_modified_by")
  private String lastModifiedBy;
  @Column(name = "dialect")
  private String dialect;
  @Column(name = "slave_proxy_url")
  private String slaveProxyUrl;
  @Column(name = "master_proxy_url")
  private String masterProxyUrl;
  @Column(name = "display_name")
  private String displayName;
}
