package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.dws.exception.ParseRuleException;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.helper.StringHelper;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Getter
public enum FilterType {
  CONTAIN(1, "LIKE", "包含", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if (StringHelper.isNullOrBlank(value1)) {
        return null;
      }
      return columnAlias + " LIKE '%" + value1 + "%'";
    }
  }, NOTCONTAIN(2, "NOT_LIKE", "不包含", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if (StringHelper.isNullOrBlank(value1)) {
        return null;
      }
      return "(" + columnAlias + " IS NULL OR " + columnAlias + " NOT LIKE '%" + value1 + "%')";
    }
  },

  IS(3, "=", "是", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      if (FieldType.TRUE_OR_FALSE.equals(fieldType) || FieldType.SELECT_ONE.equals(fieldType)) {
        String s = parseRightIn(value1, columnType, uiType, isSingle);
        if (s == null) {
          return null;
        }
        if (s.contains("true")) {
          return columnAlias + " = 'true'";
        } else {
          return columnAlias + " = 'false'";
        }
      }
      return columnAlias + " = '" + CommonUtils.escapeSql(value1) + "'";
    }
  }, NOTIS(4, "<>", "不是", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return "(" + columnAlias + " IS NULL OR " + columnAlias + " <> '" + CommonUtils.escapeSql(value1) + "')";
    }
  }, BEGINWITH(5, "LIKE", "开始于", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " LIKE '" + value1 + "%'";
    }
  }, ENDWITH(6, "LIKE", "结束于", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return columnAlias + " LIKE '%" + value1 + "'";
    }
  }, ISNULL(7, "IS", "为空", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (columnType){
        case ARRAY_String,ARRAY_Int16,ARRAY_Int32,ARRAY_Int64->
          "(empty(" + columnAlias + ")=1 OR "+columnAlias+" IS NULL)";
        case Boolean -> columnAlias+" IS NULL";
        default ->   "("+columnAlias + " = '' OR "+columnAlias+" IS NULL)";
      };
    }
  }, ISNOTNULL(8, "IS NOT", "不为空", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (columnType) {
        case ARRAY_String, ARRAY_Int16, ARRAY_Int32, ARRAY_Int64 ->
          "notEmpty(" + columnAlias + ")=1 AND " + columnAlias + " IS NOT NULL";
        case Number, Int2, Int4, Int8, Date, Boolean -> columnAlias + " IS NOT NULL";
        default -> columnAlias + " <> '' AND " + columnAlias + " IS NOT NULL";
      };
    }
  }, EQUAL(9, "=", "等于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " = " + value1;
        case _Decimal -> columnAlias + " = toDecimal128OrNull('" + value1 + "',20)";
        default -> "toDecimal128OrNull(" + columnAlias + ",20) = toDecimal128OrNull('" + value1 + "',20)";
      };
    }
  }, NOTEQUAL(10, "<>", "不等于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> "(" + columnAlias + " IS NULL OR " + columnAlias + " <> " + value1 + ")";
        case _Decimal ->
          "(" + columnAlias + " IS NULL OR " + columnAlias + " <> toDecimal128OrNull('" + value1 + "',20))";
        default ->
          "(toDecimal128OrNull(" + columnAlias + ",20) IS NULL OR toDecimal128OrNull(" + columnAlias + ",20) <> toDecimal128OrNull('" + value1 +
            "',20))";
      };
    }
  }, GREATERTHAN(11, ">", "大于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " > " + value1;
        case _Decimal -> columnAlias + " > toDecimal128OrNull('" + value1 + "',20)";
        default -> "toDecimal128OrNull(" + columnAlias + ",20) > toDecimal128OrNull('" + value1 + "',20)";
      };
    }
  }, LOWERTHAN(12, "<", "小于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " < " + value1;
        case _Decimal -> columnAlias + " < toDecimal128OrNull('" + value1 + "',20)";
        default -> "toDecimal128OrNull(" + columnAlias + ",20) < toDecimal128OrNull('" + value1 + "',20)";
      };
    }
  }, GREATEREQUALTHAN(13, ">=", "大于等于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " >= " + value1;
        case _Decimal -> columnAlias + " >= toDecimal128OrNull('" + value1 + "',20)";
        default -> "toDecimal128OrNull(" + columnAlias + ",20) >= toDecimal128OrNull('" + value1 + "',20)";
      };
    }
  }, LOWEREQUALTHAN(14, "<=", "小于等于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " <= " + value1;
        case _Decimal -> columnAlias + " <= toDecimal128OrNull('" + value1 + "',20)";
        default -> "toDecimal128OrNull(" + columnAlias + ",20) <= toDecimal128OrNull('" + value1 + "',20)";
      };
    }
  }, ISNULL1(15, "IS", "为空", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int,_Decimal -> columnAlias + " IS NULL ";
        default -> "("+columnAlias + " = ''  OR " +columnAlias + " IS NULL )";
      };
//      return "toDecimal128OrNull(" + columnAlias + ",20) IS NULL";
    }
  }, ISNOTNULL1(16, "IS_NOT", "不为空", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int,_Decimal -> columnAlias + " IS NOT NULL ";
        default -> columnAlias + " <> ''  AND " +columnAlias + " IS NOT NULL";
      };
//    return "toDecimal128OrNull(" + columnAlias + ",20) IS NOT NULL";
    }
  }, EQUAL1(17, "=", "等于", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int ->//ch没有date类型的业务数据
          columnAlias + " = " + Utils.dateString2Long(value1, timeZone);
        case _Decimal -> columnAlias + " = toDecimal128(" + Utils.dateString2Long(value1, timeZone) + ",20)";
        default -> "toInt64OrNull(" + columnAlias + ") = toInt64(" + Utils.dateString2Long(value1, timeZone) + ")";
      };
    }
  }, NOTEQUAL1(18, "!=", "不等于", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " != " + Utils.dateString2Long(value1, timeZone);
        case _Decimal -> columnAlias + " != toDecimal128(" + Utils.dateString2Long(value1, timeZone) + ",20)";
        default -> "toInt64OrNull(" + columnAlias + ") != toInt64(" + Utils.dateString2Long(value1, timeZone) + ")";
      };
    }
  }, SOONERTHAN(19, "<", "早于", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " < " + Utils.dateString2Long(value1, timeZone);
        case _Decimal -> columnAlias + " < toDecimal128(" + Utils.dateString2Long(value1, timeZone) + ",20)";
        default -> "toInt64OrNull(" + columnAlias + ") < toInt64(" + Utils.dateString2Long(value1, timeZone) + ")";
      };
    }
  },SOONEREQUALTHAN(38,"<=", "早于等于", "Date"){
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " <= " + Utils.dateString2Long(value1, timeZone);
        case _Decimal -> columnAlias + " <= toDecimal128(" + Utils.dateString2Long(value1, timeZone) + ",20)";
        default -> "toInt64OrNull(" + columnAlias + ") < toInt64(" + Utils.dateString2Long(value1, timeZone) + ")";
      };
    }
    
  }, LATERTHAN(20, ">=", "晚于等于", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " >= " + Utils.dateString2Long(value1, timeZone);
        case _Decimal -> columnAlias + " >= toDecimal128(" + Utils.dateString2Long(value1, timeZone) + ",20)";
        default -> "toInt64OrNull(" + columnAlias + ") >= toInt64(" + Utils.dateString2Long(value1, timeZone) + ")";
      };
    }
  }, ISNULL2(21, "IS", "为空", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int, _Decimal -> columnAlias + " IS NULL";
        default -> columnAlias + " = '' ";
      };
    }
  }, ISNOTNULL2(22, "IS_NOT", "不为空", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int, _Decimal -> columnAlias + " IS NOT NULL";
        default -> columnAlias + " != '' ";
      };
    }
  }, OTHER(23, "DRANGE", "时间段", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      Long startTime = Utils.dateString2Long(value1, timeZone);
      Long endTime = Utils.dateAddDay(value2, Utils.dateString2Long(value2, timeZone));
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " >= " + startTime + " AND " + columnAlias + " <= " + endTime;
        case _Decimal -> columnAlias + " >= toDecimal128(" + startTime + ",20)" + " AND " + columnAlias + " <= toDecimal128(" + endTime + ",20)";
        default -> "toInt64OrNull(" + columnAlias + ") >= toInt64(" + startTime + ") AND toInt64OrNull(" + columnAlias + ") <= toInt64(" + endTime + ")";
      };
    }
  }, BETWEEN(24, "BETWEEN", "介于", "Number") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return switch (Objects.requireNonNull(columnType.trans2CHType())) {
        case _int -> columnAlias + " >= " + Utils.dateString2Long(value1, timeZone) + " AND " + columnAlias + " <= " +
          Utils.dateString2Long(value2, timeZone);
        case _Decimal ->
          columnAlias + " >= toDecimal128(" + Utils.dateString2Long(value1, timeZone) + ",20)" + " AND " + columnAlias +
            " <= toDecimal128(" + Utils.dateString2Long(value2, timeZone) + ",20)";
        default ->
          "toDecimal128OrNull(" + columnAlias + ",20) >= toDecimal128(" + Utils.dateString2Long(value1, timeZone) + ",20)" + " AND " +
            "toDecimal128OrNull(" + columnAlias + ",20) <= toDecimal128(" + Utils.dateString2Long(value2, timeZone) + ",20)";
      };
    }
  }, CUSTOM(25, "CUSTOM", "自定义", "Date") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      return null;
    }
  }, IN(26, "IN", "包含", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      String right = parseRightIn(value1, columnType, uiType, isSingle);
      if (null == right) {
        return null;
      }
      if (Constants.ALL.equals(right)) {
        return null;
      } else {
        switch (columnType) {
          case Int2:
          case Int4:
          case Int8:
          case Number:
          case String:
          case Date:
          case Boolean:
            if (isSingle) {
              if (right.contains("other")) {
                return "(" + columnAlias + " LIKE 'other%' OR " + columnAlias + " IN " + right + " ) ";
              } else {
                return columnAlias + " IN " + right;
              }
            } else {
              if (right.contains("other")) {
                return "(" + columnAlias + " LIKE '%other%' " +
                  " OR hasAny(splitByChar(',',ifNull(" + columnAlias + ",''))," + right + ")=1)";
              } else {
                return "hasAny(splitByChar(',',ifNull(" + columnAlias + ",''))," + right + ")=1";
              }
            }
          case ARRAY_String:
          case ARRAY_Int16:
          case ARRAY_Int32:
          case ARRAY_Int64:
          default:
            if (right.contains("other")) {
              return "(arrayStringConcat(" + columnAlias + ", ',') LIKE '%other%' OR hasAny(" + columnAlias + "," +
                right + ")=1)";
            }
            return "hasAny(" + columnAlias + "," + right + ")=1";
        }
      }
    }
  }, NOTIN(27, "NOT IN", "不包含", "String") {
    @Override
    public String buildBoolSQL(String table,
                               String column,
                               String columnAlias,
                               PGColumnType columnType,
                               String fieldType,
                               boolean isSingle,
                               String value1,
                               String value2,
                               String uiType,
                               String timeZone) {
      String right = parseRightIn(value1, columnType, uiType, isSingle);
      if (right == null) {
        return null;
      }
      if (Constants.ALL.equals(right)) {
        return "1!=2";
      } else {
        switch (columnType) {
          case Int2:
          case Int4:
          case Int8:
          case Number:
          case String:
          case Date:
          case Boolean:
            if (isSingle) {
              String result1 = "(" + columnAlias + " IS NULL OR ";
              //处理其他
              if (right.contains("other")) {
                result1 += " (" + columnAlias + " NOT IN " + right + " AND " + columnAlias + " NOT LIKE 'other%'))";
              } else {
                result1 += columnAlias + " NOT IN " + right + ")";
              }
              return result1;
            } else {
              String result2 = "(" + columnAlias + " IS NULL OR ";
              //处理其他, 文本不包含other 有可能数据'{"111","333"}'
              if (right.contains("other")) {
                result2 += "(hasAny(splitByChar(',',ifNull(" + columnAlias + ",''))," + right + ")=0 AND " + columnAlias + " NOT LIKE '%other%'))";
              } else {
                result2 += "hasAny(splitByChar(',',ifNull(" + columnAlias + ",''))," + right + ")=0)";
              }
              return result2;
            }
          case ARRAY_String:
          case ARRAY_Int16:
          case ARRAY_Int32:
          case ARRAY_Int64:
          default:
            if (right.contains("other")) {
              String result3 = "(" + columnAlias + " IS NULL OR ";
              result3 += "( hasAny(" + columnAlias + "," + right + ")=0 AND arrayStringConcat(" + columnAlias +
                ",',') NOT LIKE '%other%'))";
              return result3;
            } else {
              String result4 = "(" + columnAlias + " IS NULL OR ";
              result4 += " hasAny(" + columnAlias + "," + right + ")=0)";
              return result4;
            }
        }
      }
    }
  };

  private int id;
  private String operator;
  private String label;
  private String type;

  FilterType(int id, String operator, String label, String type) {
    this.id = id;
    this.operator = operator;
    this.label = label;
    this.type = type;
  }

  public String buildBoolSQL(String table,
                             String column,
                             String columnAlias,
                             PGColumnType columnType,
                             String fieldType,
                             boolean isSingle,
                             String value1,
                             String value2,
                             String uiType,
                             String timeZone) {
    return null;
  }

  public String parseRightIn(String value1, PGColumnType columnType, String uiType,  boolean isSingle) {
    if (StringHelper.isNullOrBlank(value1)) {
      return null;
    }
    if (value1.startsWith("[")) {
      List<String> options = Lists.newArrayList();
      JSONArray objects = JSON.parseArray(value1);
      for (Object object : objects) {
        if (object == null) {
          continue;
        }
        JSONObject optionJSON = (JSONObject) object;
        if (FieldType.UI_SELECTION.equalsIgnoreCase(uiType)) {
          options.add(String.valueOf(optionJSON.get("id")));
        } else {
          options.add(optionJSON.getString("optionCode"));
          JSONArray childList = optionJSON.getJSONArray("childList");
          if (childList != null) {
            options.addAll(getAllChildOptionCode(childList));
          }
        }
      }
      if(options.isEmpty()){
        throw new ParseRuleException(String.format("%s is empty",value1));
      }
      if (!isSingle) {
        switch (columnType) {
          case ARRAY_Int16 -> {
            String arrayItems = options.stream()
                                       .filter(StringUtils::isNotBlank)
                                       .map(it -> PGColumnType.ARRAY_Int16.createCHCastFunc() + "(" + it + ")")
                                       .collect(Collectors.joining(","));
            return "array(" + arrayItems + ")";
          }
          case ARRAY_Int32 -> {
            String arrayItems = options.stream()
                                       .filter(StringUtils::isNotBlank)
                                       .map(it -> PGColumnType.ARRAY_Int32.createCHCastFunc() + "(" + it + ")")
                                       .collect(Collectors.joining(","));
            return "array(" + arrayItems + ")";
          }
          case ARRAY_Int64 -> {
            String arrayItems = options.stream()
                                       .filter(StringUtils::isNotBlank)
                                       .map(it -> PGColumnType.ARRAY_Int64.createCHCastFunc() + "(" + it + ")")
                                       .collect(Collectors.joining(","));
            return "array(" + arrayItems + ")";
          }
          default -> {
            return "array(" + JoinHelper.joinSkipNullOrBlank(',', '\'', options) + ")";
          }
        }
      } else if (columnType == PGColumnType.Boolean) {
        return "(" + JoinHelper.joinSkipNullOrBlank(',', options) + ")";
      } else {
        return "(" + JoinHelper.joinSkipNullOrBlank(',', '\'', options) + ")";
      }
    } else if (!value1.startsWith("[") && !value1.startsWith("{")){
      return "(" + Arrays.stream(value1.split(";"))
                        .filter(StringUtils::isNotBlank)
                        .map(it -> "'" + it + "'")
                        .collect(Collectors.joining(",")) + ")";
    } else {
      JSONObject filterInfo = JSON.parseObject(value1);
      Object allObj = filterInfo.get("all");
      //全部
      if (allObj instanceof JSONArray) {
        if (((JSONArray) allObj).size() > 0) {
          //全部，永远为真，不用拼条件
          return Constants.ALL;
        }
      }
      Object memObj = filterInfo.get("member");
      Object groupObj = filterInfo.get("group");
      Object optObj = MoreObjects.firstNonNull(memObj, groupObj);
      if (optObj instanceof JSONArray) {
        List<String> members = Lists.newArrayList();
        for (Object object : (JSONArray) optObj) {
          members.add(object.toString());
        }
        if (!isSingle) {
          switch (columnType) {
            case ARRAY_Int16 -> {
              String arrayItems = members.stream()
                                         .filter(StringUtils::isNotBlank)
                                         .map(it -> PGColumnType.ARRAY_Int16.createCHCastFunc() + "(" + it + ")")
                                         .collect(Collectors.joining(","));
              return "array(" + arrayItems + ")";
            }
            case ARRAY_Int32 -> {
              String arrayItems = members.stream()
                                         .filter(StringUtils::isNotBlank)
                                         .map(it -> PGColumnType.ARRAY_Int32.createCHCastFunc() + "(" + it + ")")
                                         .collect(Collectors.joining(","));
              return "array(" + arrayItems + ")";
            }
            case ARRAY_Int64 -> {
              String arrayItems = members.stream()
                                         .filter(StringUtils::isNotBlank)
                                         .map(it -> PGColumnType.ARRAY_Int64.createCHCastFunc() + "(" + it + ")")
                                         .collect(Collectors.joining(","));
              return "array(" + arrayItems + ")";
            }
            default -> {
              return "array(" + JoinHelper.joinSkipNullOrBlank(',', '\"', members) + ")";
            }
          }
        } else if (columnType == PGColumnType.Boolean) {
          return "(" + JoinHelper.joinSkipNullOrBlank(',', members) + ")";
        } else {
          return "(" + JoinHelper.joinSkipNullOrBlank(',', '\'', members) + ")";
        }
      }
      log.warn("opt parse failed. {}", value1);
      return Constants.ALL;
    }
  }

  public static FilterType parseFromId(int id) {
    switch (id) {
      case 1:
        return CONTAIN;
      case 2:
        return NOTCONTAIN;
      case 3:
        return IS;
      case 4:
        return NOTIS;
      case 5:
        return BEGINWITH;
      case 6:
        return ENDWITH;
      case 7:
        return ISNULL;
      case 8:
        return ISNOTNULL;
      case 9:
        return EQUAL;
      case 10:
        return NOTEQUAL;
      case 11:
        return GREATERTHAN;
      case 12:
        return LOWERTHAN;
      case 13:
        return GREATEREQUALTHAN;
      case 14:
        return LOWEREQUALTHAN;
      case 15:
        return ISNULL1;
      case 16:
        return ISNOTNULL1;
      case 17:
        return EQUAL1;
      case 18:
        return NOTEQUAL1;
      case 19:
        return SOONERTHAN;
      case 20:
        return LATERTHAN;
      case 21:
        return ISNULL2;
      case 22:
        return ISNOTNULL2;
      case 23:
        return OTHER;
      case 24:
        return BETWEEN;
      case 25:
        return CUSTOM;
      case 26:
        return IN;
      case 27:
        return NOTIN;
      case 38:
        return SOONEREQUALTHAN;
    }
    throw new IllegalArgumentException(id + " is not FilterType#id");
  }


  public List<String> getAllChildOptionCode(JSONArray childList) {
    List<String> result = Lists.newArrayList();
    childList.forEach(obj -> {
      if (null != obj) {
        JSONObject optionJSON = (JSONObject) obj;
        result.add(optionJSON.getString("optionCode"));
        JSONArray childChildList = optionJSON.getJSONArray("childList");
        if (childChildList != null) {
          result.addAll(getAllChildOptionCode(childChildList));
        }
      }
    });
    return result;
  }
}
