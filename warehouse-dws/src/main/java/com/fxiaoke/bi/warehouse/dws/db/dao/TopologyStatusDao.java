package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.fxiaoke.bi.warehouse.common.db.entity.StatViewStatusEnum;
import com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyStatusMapper;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtTopologyStatusDO;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/1/27
 */
@Slf4j
@Repository
public class TopologyStatusDao {
  @Autowired
  private TopologyStatusMapper topologyStatusMapper;

  /**
   * 批量插入 bi_mt_topology_status 数据
   *
   * @param tenantId 租户id
   * @param list     topology status 集合
   * @return
   */
  public int batchUpsertStatViewStatus(String tenantId, List<BIMtTopologyStatusDO> list) {
    Set<String> primaryKey = Sets.newHashSet("tenant_id", "source_id");
    return topologyStatusMapper.setTenantId(tenantId).batchUpsertStatViewStatus(list, primaryKey);
  }

  /**
   * 批量更新统计图状态
   *
   * @param tenantId      主id
   * @param sourceIdArray 统计图id数组
   * @return
   */
  public int batchDisableStatViewStatus(String tenantId, String[] sourceIdArray, Integer sourceType) {
    return this.topologyStatusMapper.setTenantId(tenantId)
                                    .batchUpdateStatus(tenantId, sourceIdArray,
                                      StatViewStatusEnum.disabled4Idle.getStatus(), new Date().getTime(), sourceType);
  }

  /**
   * 根据viewId 反查停用的图id
   * @param tenantId
   * @param viewIds
   * @return
   */
  public List<BIMtTopologyStatusDO> findStopStatView(String tenantId, String[] viewIds) {
    return this.topologyStatusMapper.setTenantId(tenantId)
                                    .findStatViewStatusByStatus(tenantId, viewIds,
                                      new int[] {StatViewStatusEnum.disabled.getStatus(), StatViewStatusEnum.disabled4Idle.getStatus()});
  }

  /**
   * 根据viewId 反查启用或初始化中的图
   * @param tenantId 租户id
   * @param viewIds  图集合
   * @return
   */
  public List<BIMtTopologyStatusDO> findStartStatView(String tenantId, String[] viewIds){
    return this.topologyStatusMapper.setTenantId(tenantId)
                                    .findStatViewStatusByStatus(tenantId, viewIds,
                                      new int[] {StatViewStatusEnum.used.getStatus(), StatViewStatusEnum.init.getStatus()});
  }

  /**
   * 根据last_visit_time 判断是否活跃
   * @param tenantId 租户id
   * @param viewId 图id
   * @return
   */
  public boolean isActive(String tenantId, String viewId) {
    List<BIMtTopologyStatusDO> topologyStatusDOS = this.findStartStatView(tenantId, new String[] {viewId});
    if (CollectionUtils.isNotEmpty(topologyStatusDOS)) {
      return topologyStatusDOS.get(0).activeInDays(Constants.ACTIVE_DAYS_THRESHOLD);
    }
    return false;
  }
  /**
   * 批量更新topology status 不存在则插入一条状态
   *
   * @param tenantId  租户id
   * @param sourceIds 统计图id集合
   * @param status    状态
   * @return 更新数量
   */
  @Transactional
  public int batchUpdateStatViewStatus(String tenantId, List<String> sourceIds, int status) {
    if (CollectionUtils.isEmpty(sourceIds)) {
      return 0;
    }
    int size = 0;
    List<BIMtTopologyStatusDO> results = this.topologyStatusMapper.setTenantId(tenantId)
                                                                  .batchQueryTopologyStatus(tenantId,
                                                                    sourceIds.toArray(String[]::new));
    Set<String> upsertSourceId = Sets.newHashSet();
    if (CollectionUtils.isNotEmpty(results)) {
      results.forEach(tt -> upsertSourceId.add(tt.getSourceId()));
      int count1 = this.topologyStatusMapper.setTenantId(tenantId)
                                            .batchUpdateStatus(tenantId, upsertSourceId.toArray(String[]::new), status, new Date().getTime(), null);
      size += count1;
    }
    List<BIMtTopologyStatusDO> need2Upsert = sourceIds.stream()
                                                      .filter(sourceId -> !upsertSourceId.contains(sourceId))
                                                      .map(viewId -> {
                                                        BIMtTopologyStatusDO biMtTopologyStatusDO =
                                                          BIMtTopologyStatusDO.getInstance();
                                                        biMtTopologyStatusDO.setId(ObjectId.get().toString());
                                                        biMtTopologyStatusDO.setTenantId(tenantId);
                                                        biMtTopologyStatusDO.setStatus(status);
                                                        biMtTopologyStatusDO.setSourceId(viewId);
                                                        biMtTopologyStatusDO.setViewCount(0);
                                                        biMtTopologyStatusDO.setCreateTime(new Date().getTime());
                                                        biMtTopologyStatusDO.setLastVisitTime(0L);
                                                        biMtTopologyStatusDO.setLastModifiedTime(new Date().getTime());
                                                        biMtTopologyStatusDO.setIsDeleted(0);
                                                        return biMtTopologyStatusDO;
                                                      })
                                                      .toList();
    if (CollectionUtils.isNotEmpty(need2Upsert)) {
      int count2 = this.batchUpsertStatViewStatus(tenantId, need2Upsert);
      size += count2;
    }
    return size;
  }
}
