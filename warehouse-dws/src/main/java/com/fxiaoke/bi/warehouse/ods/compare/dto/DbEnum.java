package com.fxiaoke.bi.warehouse.ods.compare.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 是否是公共库和租户库
 */
@Getter
@AllArgsConstructor
public enum DbEnum {

    PUBLIC_ENUM("公共库"),
    SCHEMA_ENUM("隔离库");

    /**
     * 库类型
     */
    private String desc;

    public static DbEnum getDbEnum(String pgSchema) {
        return StringUtils.equals(pgSchema, "public") ? PUBLIC_ENUM : SCHEMA_ENUM;
    }
}
