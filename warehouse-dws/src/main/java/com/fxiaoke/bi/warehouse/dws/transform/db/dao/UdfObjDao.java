package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author:jief
 * @Date:2024/1/6
 */
@Slf4j
@Repository
public class UdfObjDao {
  @Autowired
  private UdfObjFieldMapper udfObjFieldMapper;

  public List<UdfObjFieldDO> batchQueryMDField(String[] objNames, String tenantId){
    return udfObjFieldMapper.setTenantId(tenantId).batchQueryMDField(objNames,Integer.parseInt(tenantId));
  }
}
