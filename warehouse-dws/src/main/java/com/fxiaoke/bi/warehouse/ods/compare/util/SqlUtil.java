package com.fxiaoke.bi.warehouse.ods.compare.util;

/**
 * !慎用这是比较clickhouse和postgresql数据量差异的查询语句
 */
public class SqlUtil {

    /**
     * clickhouse 查询语句
     */
    public static final String queryClickhouseTableName = "SELECT name FROM system.tables WHERE database='%s'";

    public static final String querySyncFieldClickhouseTableName = "SELECT table AS table_name FROM system.columns WHERE database = '%s' AND name = 'sys_modified_time' GROUP BY table ORDER BY table";

    public static final String queryClickhouseTableRows = "SELECT table,sum(rows) FROM system.parts WHERE active = 1 AND database='%s' GROUP BY table";

    public static final String queryClickhouseTableRowsAccurate = "SELECT count(1) FROM %s final where tenant_id = '%s'";

    public static final String queryClickhouseTableRowsPlus = "SELECT table,total_rows FROM system.tables WHERE database='%s'";

    public static final String queryClickhouseTableColumns = "SELECT name,type FROM system.columns WHERE database = '%s' AND table = '%s'";

    /**
     * 查询biz_account表
     */
    public static final String queryChBizAccountByTenantId = "SELECT COUNT(1) FROM biz_account final where tenant_id = '%s' and is_deleted = 0";

    /**
     * 查询org_employee_user表
     */
    public static final String queryChOrgEmployeeUserByTenantId = "SELECT COUNT(1) FROM org_employee_user final WHERE tenant_id = '%s' AND is_deleted = 0";

    /**
     * 查询org_dept表
     */
    public static final String queryChOrgDeptByTenantId = "SELECT COUNT(1) FROM org_dept final WHERE tenant_id = '%s' AND is_deleted = 0";

    /**
     * 查询dim_sys_date表
     */
    public static final String queryChDimSysDateByTenantId = "SELECT COUNT(1) FROM dim_sys_date final WHERE ei in (%s, '-1')";

    /**
     * 查询dim_sys_area_gray
     */
    public static final String queryChDimSysAreaGrayByTenantId = "SELECT COUNT(1) FROM dim_sys_area_gray final WHERE ei in (%s, '-1') AND is_deleted=0";

    /**
     * ------------------------------------------------------------------------------------------------------------------------------------------------------
     */

    /**
     * postgresql 查询语句
     */
    public static final String queryPostgreTableRows = """
      SELECT relname As name,reltuples::bigint AS total_rows
      FROM pg_class
      WHERE relname %s AND relkind = 'r'
      AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = '%s');
      """;

    public static final String queryPostgreTableColumns = """
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = '%s'
        AND table_schema = '%s';
      """;

    public static final String queryNotTriggerTable = """
      SELECT c.relname AS table_name
      FROM pg_class c
               JOIN
           pg_namespace n ON c.relnamespace = n.oid
      WHERE n.nspname = '%s'
        AND c.relname IN (%s)
        AND c.relkind = 'r'
        AND c.oid NOT IN (SELECT t.tgrelid
                          FROM pg_trigger t
                          WHERE t.tgname = 'x_system_changes'
                            AND t.tgenabled = 'O');
      """;

    /**
     * 查询biz_account表
     */
    public static final String queryPgBizAccountByTenantId = "SELECT COUNT(1) FROM biz_account where tenant_id = '%s' and is_deleted = 0";

    /**
     * 查询org_employee_user表
     */
    public static final String queryPgOrgEmployeeUserByTenantId = "SELECT COUNT(1) FROM org_employee_user WHERE tenant_id = '%s' AND is_deleted = 0";

    /**
     * 查询org_dept表
     */
    public static final String queryPgOrgDeptByTenantId = "SELECT COUNT(1) FROM org_dept WHERE tenant_id = '%s' AND is_deleted = 0";

    /**
     * 查询dim_sys_date表
     */
    public static final String queryPgDimSysDateByTenantId = "SELECT COUNT(1) FROM dim_sys_date WHERE ei in (%s, '-1')";

    /**
     * 查询dim_sys_area_gray
     */
    public static final String queryPgDimSysAreaGrayByTenantId = "SELECT COUNT(1) FROM dim_sys_area_gray WHERE ei in (%s, '-1') AND is_deleted=0";
}
