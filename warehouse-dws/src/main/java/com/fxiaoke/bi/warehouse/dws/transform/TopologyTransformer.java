package com.fxiaoke.bi.warehouse.dws.transform;

import com.fxiaoke.bi.warehouse.common.bean.UserCenterService;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.service.ClickHouseService;
import com.fxiaoke.bi.warehouse.dws.service.TopologyTableService;
import com.fxiaoke.bi.warehouse.dws.transform.model.TransformContext;
import com.fxiaoke.common.Pair;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 规则转换成bi_mt_topology_table的转换器
 */
@Slf4j
@Component
public abstract class TopologyTransformer {
  @Resource
  private ClickHouseService clickHouseService;
  private volatile int viewSqlCheckObjLimit;
  @Getter
  private volatile int viewSqlResultLimit;
  private volatile Map<String, Integer> customerSampleLimitMap;
  @Resource
  public TopologyTableService topologyTableService;
  @Resource
  public UserCenterService userCenterService;


  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", new IniChangeListener("common") {
      @Override
      public void iniChanged(IniConfig iniConfig) {
        viewSqlCheckObjLimit = iniConfig.getInt("viewSql_checkObj_limit_threshold", 1000000);
        viewSqlResultLimit = iniConfig.getInt("viewSql_limit_threshold", 2000);
        customerSampleLimitMap = Splitter.on(CharMatcher.anyOf(",|"))
                                         .omitEmptyStrings()
                                         .splitToList(iniConfig.get("ch_customer_sample_limit", ""))
                                         .stream()
                                         .collect(Collectors.toMap(limit -> limit.split("\\^")[0], limit -> {
                                           String num = limit.split("\\^")[1];
                                           if (StringUtils.isNumeric(num)) {
                                             return Integer.parseInt(num);
                                           }
                                           return viewSqlCheckObjLimit;
                                         }));
      }
    });
  }

  /**
   * @param tenantId 租户id
   * @return
   */
  public int calCheckObjLimit(String tenantId) {
    return customerSampleLimitMap.getOrDefault(tenantId, this.viewSqlCheckObjLimit);
  }

  /**
   * 针对考核对象抽样,
   * 只有object_data 做了对象区分，其他表没有做对象区分。
   *
   * @param topologyTable topologyTable
   * @return limit 数值
   */
  public int calSample(TopologyTable topologyTable, boolean standalone) {
    long start = System.currentTimeMillis();
    int limit = -1;
    if(StringUtils.isNotBlank(topologyTable.getDatabaseId())){
      //外部数据源的话不计算抽样
      return limit;
    }
    int customerLimit = this.calCheckObjLimit(topologyTable.getTenantId());
    List<Pair<String/*table*/, String/*apiName*/>> checkObjNames = topologyTable.findAllCheckObj();
    if (!standalone) {
      if (clickHouseService.checkSamplePublicData(topologyTable.getTenantId(), checkObjNames, customerLimit)) {
        limit = customerLimit;
      }
    } else {
      List<String> tableNames = checkObjNames.stream().map(Pair::getKey).toList();
      Map<String, Integer> tableSizeMap = clickHouseService.queryTableSize(topologyTable.getTenantId(), tableNames);
      if (tableSizeMap != null) {
        Optional<Integer> maxSizeOp = tableSizeMap.values().stream().max(Comparator.naturalOrder());
        if (maxSizeOp.orElse(0) > customerLimit) {
          limit = customerLimit;
        }
      }
    }
    log.info("calculate sample limit tenantId:{},viewId:{},cost:{}", topologyTable.getTenantId(), topologyTable.getViewId(),
      System.currentTimeMillis() - start);
    return limit;
  }
  
  /**
   * 统计图谓词下推
   * @param topologyTable
   * @param filters
   * @return
   */
  public Set<String> statViewPredicatePushDown(TopologyTable topologyTable,List<StatViewFilter> filters){
    String tenantId = topologyTable.getTenantId();
    if(GrayManager.isAllowByRule("support_predicate_pushDown",tenantId) && StringUtils.isNotBlank(topologyTable.getDatabaseId()) && !CollectionUtils.isEmpty(filters)){
      return topologyTable.tryToPushDownPredicate(filters);
    }
    return Sets.newHashSet();
  }

  public abstract TopologyTableDO transform(TransformContext context);

  public abstract void upsertTopologyTable(TransformContext context);

  public abstract StatViewPreSQL createStatViewPreSQL(StatViewPreArg statViewPreArg);

  /**
   * 如果状态为2，检测query detail 如果为1的话。走实时查询。
   * 如果0或null需要走抽样查询
   *
   * @param statViewPreArg 反查topology table 参数
   * @return
   */
  public TopologyTable findRealTimeQueryIfExists(StatViewPreArg statViewPreArg) {
    String sourceId = statViewPreArg.getSourceId();
    if (StringUtils.isNotBlank(sourceId)) {
      TopologyTable topologyTable = topologyTableService.findByTenantIdAndSourceId(statViewPreArg.getTenantId(), sourceId);
      if (topologyTable != null && topologyTable.getStatus() == TopologyTableStatus.NONeedCal.getValue()) {
        if (Objects.equals(statViewPreArg.getQueryDetailType(), 1)) {
          return topologyTable;
        } else {
          return null;
        }
      }
    }
    return null;
  }
}
