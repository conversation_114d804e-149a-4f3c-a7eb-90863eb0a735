package com.fxiaoke.bi.warehouse.dws.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/3/1
 */
@Slf4j
@Service
public class BizLogService {
  private String jdbcUrl;
  private String userName;
  private String password;
  private String bizLogTable;
  private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "bizLog-clickhouse", config -> {
      jdbcUrl = config.get("jdbcUrl");
      userName = config.get("username");
      password = config.get("password");
      boolean encrypt = config.getBool("encryptPassword", false);
      try {
        if (StringHelper.isNotNullOrEmpty(password) && encrypt) {
          password = PasswordUtil.decode(password);
        }
      } catch (Exception e) {
        log.error("decode password {} failed", password, e);
      }
      bizLogTable=config.get("logTable", Constants.BIZ_LOG_TABLE);
    });
  }

  /**
   * 获取业务日志clickhouse连接
   *
   * @return JdbcConnection
   */
  public JdbcConnection getBizLogJdbcConnection(long socketTimeOut) {
    return new JdbcConnection(jdbcUrl + "?socket_timeout=" +socketTimeOut, userName, password);
  }

  /**
   * 根据租户id查询获取图id
   *
   * @param tenantIds   租户id
   * @return List<String>
   */
  public Map<String, List<String>> queryActiveViewIds(List<String> tenantIds) {
    Map<String, List<String>> viewIds = Maps.newHashMap();
    String tenantIdIn = String.join("','", tenantIds);
    try (JdbcConnection jdbcConnection = getBizLogJdbcConnection(1000*60*30L)) {
      LocalDateTime localDateTime = LocalDateTime.now();
      localDateTime = localDateTime.plusDays(-Constants.ACTIVE_DAYS_THRESHOLD);
      String fromDate = localDateTime.format(DATE_TIME_FORMATTER);
      String sql = String.format("select tenantId,viewId from %s where tenantId in('%s') and day >= '%s' " +
        " and appName like '%s' and viewId <> '' group by tenantId,viewId", bizLogTable, tenantIdIn, fromDate, "fs-bi" +
        "-stat%");
      jdbcConnection.query(sql, rs -> {
        while (rs.next()) {
          String tenantId = rs.getString(1);
          String viewId = rs.getString(2);
          viewIds.computeIfAbsent(tenantId, k -> Lists.newArrayList()).add(viewId);
        }
      });
    } catch (Exception e) {
      log.error("queryActiveViewIds failed tenantIds:{}", JSON.toJSONString(tenantIds), e);
      throw new RuntimeException(e);
    }
    return viewIds;
  }
}
