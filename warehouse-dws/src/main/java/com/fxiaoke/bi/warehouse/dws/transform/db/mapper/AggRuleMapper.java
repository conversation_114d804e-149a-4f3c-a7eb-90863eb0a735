package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.AggRuleDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AggRuleMapper extends ICrudMapper<AggRuleDO>, IBatchMapper<AggRuleDO>, ITenant<AggRuleMapper> {
  @Select("select * from agg_rule where tenant_id=#{tenant_id} and field_id=#{field_id} and is_deleted=0 limit 1")
  AggRuleDO queryAggRuleByFieldId(@Param("field_id") String fieldId, @Param("tenant_id") String tenantId);

  @Select("select * from agg_rule where tenant_id=#{tenant_id} and field_id in (${field_ids}) and is_deleted=0")
  List<AggRuleDO> queryAggRuleByFieldIds(@Param("field_ids") String fieldIdSQL, @Param("tenant_id") String tenantId);
}
