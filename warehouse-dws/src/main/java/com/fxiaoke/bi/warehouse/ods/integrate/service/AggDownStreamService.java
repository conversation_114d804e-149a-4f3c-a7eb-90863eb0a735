package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import com.fxiaoke.bi.warehouse.ods.integrate.model.BIAggSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.model.DownStreamSyncInfo;
import com.fxiaoke.bi.warehouse.ods.integrate.model.TopologyTableIntegrateBO;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.BizEnterpriseRelationDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.StatFieldDao;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bi.warehouse.common.bean.TopologyTableAggDownStream;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncTableStatusEnum;
import com.fxiaoke.bi.warehouse.ods.service.DbTableSyncInfoService;
import com.fxiaoke.helper.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @Date 20240411
 * <AUTHOR>
 * @Desc 同步下游ch agg_data数据
 */
@Service
@Slf4j
public class AggDownStreamService {
    @Resource
    private AggDataSyncInfoService aggDataSyncInfoService;
    @Resource
    private BiMtTopologyTableService biMtTopologyTableService;
    @Resource
    private CHRouterPolicy chRouterPolicy;
    @Resource
    private BizEnterpriseRelationDao bizEnterpriseRelationDao;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private PgCommonDao pgCommonDao;
    @Resource
    private StatFieldDao statFieldDao;
    @Resource
    private DbTableSyncInfoService dbTableSyncInfoService;



    /**
     * 根据上游企业ID，同步下游ch agg_data数据
     */
    public void synchronizeDownstreamAggData(BIAggSyncInfoBO biAggSyncInfoBO,
                                             long nextBatchNum,
                                             DBSyncInfoBO dbSyncInfoCopy) {
        String upStreamTenantId = biAggSyncInfoBO.getTenantId();
        log.info("synchronization N downStream agg_data To 1 agg_downStream_data, tenantId : {}", upStreamTenantId);
        List<TopologyTableIntegrateBO> topologyTableIntegrateBOS = biMtTopologyTableService.queryAllDownstreamTopologyTables(upStreamTenantId);
        if (CollectionUtils.isEmpty(topologyTableIntegrateBOS)) {
            log.warn("topologyTableIntegrateBOS is empty, upStreamTenantId is {}", upStreamTenantId);
            return;
        }
        Set<TopologyTableAggDownStream.DownStreamInfo> downStreamInfos = biMtTopologyTableService.buildDownstreamAggTables(topologyTableIntegrateBOS);
        if (CollectionUtils.isEmpty(downStreamInfos)) {
            log.warn("downStreamInfos is empty :{}",upStreamTenantId);
            return;
        }
        //首先获取agg_downstream_data 表得同步信息
        List<DbTableSyncInfoDO> dbTableSyncInfos = dbTableSyncInfoService.queryDbTableSyncInfosBySyncId(dbSyncInfoCopy.getPgDb(),dbSyncInfoCopy.getPgSchema(),biAggSyncInfoBO.getId(), CHContext.AGG_DOWNSTREAM_DATA);
        DbTableSyncInfoDO dbTableSyncInfo;
        boolean offline = false;
        if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
            dbTableSyncInfo = dbTableSyncInfos.get(0);
            dbTableSyncInfo.setLastSyncTime(new Date().getTime());
        } else {
            dbTableSyncInfo = DbTableSyncInfoDO.createFrom(biAggSyncInfoBO, CHContext.AGG_DOWNSTREAM_DATA);
            dbTableSyncInfo.setLastSyncTime(new Date().getTime());
            offline = true;
        }
        AtomicLong syncCounts = new AtomicLong();
        RouterInfo upStreamTenantIdRouterInfo = chRouterPolicy.getRouterInfo(upStreamTenantId);
        List<Pair<String, String>> tenantIdToRouteInfoPairs = this.getDownStreamTenantIdList(upStreamTenantId);
        List<List<Pair<String, String>>> monitPartitions = Lists.partition(tenantIdToRouteInfoPairs, (int) Math.ceil(tenantIdToRouteInfoPairs.size() / 10d));
        final ExecutorService executorService = Executors.newFixedThreadPool(10);
        long currentTimeMillis = System.currentTimeMillis();
        try {
            CompletableFuture.allOf(monitPartitions.stream().map(tenantIdAndRoutes -> CompletableFuture.runAsync(() -> {
                tenantIdAndRoutes.forEach(tenantIdAndRoutePair -> {
                        RouterInfo downStreamTenantIdRouterInfo = chRouterPolicy.getRouterInfo(tenantIdAndRoutePair.first);
                        if (Objects.isNull(downStreamTenantIdRouterInfo)) {
                            log.warn("tenantId:{} getRouteInfo is null",tenantIdAndRoutePair.first);
                            return;
                        }
                        String downstreamTenantId = tenantIdAndRoutePair.first;
                        List<DownStreamSyncInfo> downsStreamSyncInfoList = biMtTopologyTableService.getDownsStreamSyncInfo(downstreamTenantId, downStreamInfos);
                        if (CollectionUtils.isEmpty(downsStreamSyncInfoList)) {
                            log.warn("getDownsStreamSyncInfo downsStreamSyncInfoList empty tenantId:{}",downstreamTenantId);
                            return;
                        }
                        Map<String, Map<String, String>> downStreamAggToUpStreamAggMap = getDownStreamAggToUpStreamAggMap(upStreamTenantId, downsStreamSyncInfoList);
                        for (DownStreamSyncInfo downStreamSyncInfo : downsStreamSyncInfoList) {
                            String viewId = downStreamSyncInfo.getViewId();
                            Map<String, String> downToUpMap = downStreamAggToUpStreamAggMap.get(viewId);
                            List<String> fieldLocalTionNotExisitList = downToUpMap.keySet().stream().filter(x -> !downStreamSyncInfo.getFieldLocaltionList().contains(x)).toList();
                            if (CollectionUtils.isNotEmpty(fieldLocalTionNotExisitList)) {
                                log.warn("upStream statField not find downStream fieldLocal,this statFieldList is : {}, viewId is {}", fieldLocalTionNotExisitList, viewId);
                            }
                            List<String> fieldLocalTionList = downStreamSyncInfo.getFieldLocaltionList().stream().filter(downToUpMap::containsKey).toList();
                            AggDataSyncInfoDo aggDataSyncInfoDo = aggDataSyncInfoService.queryAggDataSyncInfoByTenantIdAndViewId(upStreamTenantIdRouterInfo, tenantIdAndRoutePair.first, viewId);
                            if (aggDataSyncInfoDo.getBatchNum() >= nextBatchNum) {
                                continue;
                            }
                            //如果batchNum是0则为新创建的，需要进行全量同步
                            boolean isFullSynchronization = aggDataSyncInfoDo.getBatchNum() == 0;
                            //如果是图合并则version为0
                            if (downStreamSyncInfo.getIsStatMerge() == 0) {
                                aggDataSyncInfoDo.setViewVersion(downStreamSyncInfo.getVersion());
                            } else {
                                aggDataSyncInfoDo.setViewVersion(0);
                                aggDataSyncInfoDo.setViewId(downStreamSyncInfo.getStat_view_unique_key());
                            }
                            try {
                                long syncNums = aggDataSyncInfoService.synchronizationAggDataToAggDownStreamData(upStreamTenantId, aggDataSyncInfoDo, fieldLocalTionList, downToUpMap, isFullSynchronization, upStreamTenantIdRouterInfo, downStreamTenantIdRouterInfo, tenantIdAndRoutePair.second, currentTimeMillis, nextBatchNum, downStreamSyncInfo.getViewId(), null,null);
                                syncCounts.addAndGet(syncNums);
                                //同步完成更改同步状态,版本号就取用当前biAggSyncInfo的版本号
                                aggDataSyncInfoDo.setViewId(downStreamSyncInfo.getViewId());
                                aggDataSyncInfoDo.setBatchNum(nextBatchNum);
                                aggDataSyncInfoDo.setMaxSyncTimeStamp(currentTimeMillis);
                                aggDataSyncInfoService.updateAggDataSyncInfoByInsert(upStreamTenantId, aggDataSyncInfoDo, SyncStatusEnum.SYNC_ED);
                            } catch (Exception e) {
                                log.error("synchronization downStreamTenant agg_data error this tenantId:{}, this viewId is {}", downstreamTenantId, viewId, e);
                            }
                        }
                });
            }, executorService)).toArray(CompletableFuture[]::new)).join();
        } catch (Exception e) {
            log.error("synchronization N downStream agg_data To 1 agg_downStream_data error, tenantId : {}", upStreamTenantId);
        } finally {
            executorService.shutdown();
        }
        Map<String, Set<String>> apiNameEi = Maps.newHashMap();
        if (syncCounts.get() > 0L) {
            apiNameEi.put(CHContext.AGG_DOWNSTREAM_DATA, Sets.newHashSet(upStreamTenantId));
        }
        dbTableSyncInfo.setApiNameEiMap(JSON.toJSONString(apiNameEi));
        dbTableSyncInfo.setLastModifiedTime(new Date().getTime());
        dbTableSyncInfo.setBatchNum(nextBatchNum);
        dbTableSyncInfo.setMaxSysModifiedTime(currentTimeMillis);
        dbTableSyncInfo.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
        if (offline) {
            dbTableSyncInfoService.batchUpsertDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
        } else {
            dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
        }
    }

    /**
     * 根据上游企业ID，同步下游ch agg_data数据
     */
    public void synchronizeDownstreamAggDataNew(String tenantId,
                                             long nextBatchNum,
                                             DBSyncInfoBO dbSyncInfoCopy,String partitionName) {
        String upStreamTenantId = tenantId;
        log.info("synchronization N downStream agg_data To 1 agg_downStream_data, tenantId : {}", upStreamTenantId);
        List<TopologyTableIntegrateBO> topologyTableIntegrateBOS = biMtTopologyTableService.queryAllDownstreamTopologyTables(upStreamTenantId);
        if (CollectionUtils.isEmpty(topologyTableIntegrateBOS)) {
            log.warn("topologyTableIntegrateBOS is empty, upStreamTenantId is {}", upStreamTenantId);
            return;
        }
        Set<TopologyTableAggDownStream.DownStreamInfo> downStreamInfos = biMtTopologyTableService.buildDownstreamAggTables(topologyTableIntegrateBOS);
        if (CollectionUtils.isEmpty(downStreamInfos)) {
            log.warn("downStreamInfos is empty :{}",upStreamTenantId);
            return;
        }
        //首先获取agg_downstream_data 表得同步信息
        List<DbTableSyncInfoDO> dbTableSyncInfos = dbTableSyncInfoService.queryDbTableSyncInfosBySyncId(dbSyncInfoCopy.getPgDb(),dbSyncInfoCopy.getPgSchema(),dbSyncInfoCopy.getId(), CHContext.AGG_DOWNSTREAM_DATA);
        DbTableSyncInfoDO dbTableSyncInfo;
        boolean offline = false;
        if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
            dbTableSyncInfo = dbTableSyncInfos.getFirst();
            dbTableSyncInfo.setLastSyncTime(new Date().getTime());
        } else {
            dbTableSyncInfo = DbTableSyncInfoDO.createFrom(dbSyncInfoCopy, CHContext.AGG_DOWNSTREAM_DATA);
            dbTableSyncInfo.setLastSyncTime(new Date().getTime());
            offline = true;
        }
        AtomicLong syncCounts = new AtomicLong();
        RouterInfo upStreamTenantIdRouterInfo = chRouterPolicy.getRouterInfo(upStreamTenantId);
        List<Pair<String, String>> tenantIdToRouteInfoPairs = this.getDownStreamTenantIdList(upStreamTenantId);
        List<List<Pair<String, String>>> monitPartitions = Lists.partition(tenantIdToRouteInfoPairs, (int) Math.ceil(tenantIdToRouteInfoPairs.size() / 10d));
        final ExecutorService executorService = Executors.newFixedThreadPool(10);
        long currentTimeMillis = System.currentTimeMillis();
        try {
            CompletableFuture.allOf(monitPartitions.stream().map(tenantIdAndRoutes -> CompletableFuture.runAsync(() -> {
                tenantIdAndRoutes.forEach(tenantIdAndRoutePair -> {
                    RouterInfo downStreamTenantIdRouterInfo = chRouterPolicy.getRouterInfo(tenantIdAndRoutePair.first);
                    if (Objects.isNull(downStreamTenantIdRouterInfo)) {
                        log.warn("tenantId:{} getRouteInfo is null",tenantIdAndRoutePair.first);
                        return;
                    }
                    String downstreamTenantId = tenantIdAndRoutePair.first;
                    List<DownStreamSyncInfo> downsStreamSyncInfoList = biMtTopologyTableService.getDownsStreamSyncInfo(downstreamTenantId, downStreamInfos);
                    if (CollectionUtils.isEmpty(downsStreamSyncInfoList)) {
                        log.warn("getDownsStreamSyncInfo downsStreamSyncInfoList empty tenantId:{}",downstreamTenantId);
                        return;
                    }
                    Map<String, Map<String, String>> downStreamAggToUpStreamAggMap = getDownStreamAggToUpStreamAggMap(upStreamTenantId, downsStreamSyncInfoList);
                    for (DownStreamSyncInfo downStreamSyncInfo : downsStreamSyncInfoList) {
                        String viewId = downStreamSyncInfo.getViewId();
                        Map<String, String> downToUpMap = downStreamAggToUpStreamAggMap.get(viewId);
                        List<String> fieldLocalTionNotExisitList = downToUpMap.keySet().stream().filter(x -> !downStreamSyncInfo.getFieldLocaltionList().contains(x)).toList();
                        if (CollectionUtils.isNotEmpty(fieldLocalTionNotExisitList)) {
                            log.warn("upStream statField not find downStream fieldLocal,this statFieldList is : {}, viewId is {}", fieldLocalTionNotExisitList, viewId);
                        }
                        List<String> fieldLocalTionList = downStreamSyncInfo.getFieldLocaltionList().stream().filter(downToUpMap::containsKey).toList();
                        AggDataSyncInfoDo aggDataSyncInfoDo = aggDataSyncInfoService.queryAggDataSyncInfoByTenantIdAndViewId(upStreamTenantIdRouterInfo, tenantIdAndRoutePair.first, viewId);
                        if (aggDataSyncInfoDo.getBatchNum() >= nextBatchNum) {
                            continue;
                        }
                        //如果batchNum是0则为新创建的，需要进行全量同步
                        boolean isFullSynchronization = aggDataSyncInfoDo.getBatchNum() == 0;
                        //如果是图合并则version为0
                        if (downStreamSyncInfo.getIsStatMerge() == 0) {
                            aggDataSyncInfoDo.setViewVersion(downStreamSyncInfo.getVersion());
                        } else {
                            aggDataSyncInfoDo.setViewVersion(0);
                            aggDataSyncInfoDo.setViewId(downStreamSyncInfo.getStat_view_unique_key());
                        }
                        try {
                            long syncNums = aggDataSyncInfoService.synchronizationAggDataToAggDownStreamData(upStreamTenantId, aggDataSyncInfoDo, fieldLocalTionList, downToUpMap, isFullSynchronization, upStreamTenantIdRouterInfo, downStreamTenantIdRouterInfo, tenantIdAndRoutePair.second, currentTimeMillis, nextBatchNum, downStreamSyncInfo.getViewId(), null,partitionName);
                            syncCounts.addAndGet(syncNums);
                            //同步完成更改同步状态,版本号就取用当前biAggSyncInfo的版本号
                            aggDataSyncInfoDo.setViewId(downStreamSyncInfo.getViewId());
                            aggDataSyncInfoDo.setBatchNum(nextBatchNum);
                            aggDataSyncInfoDo.setMaxSyncTimeStamp(currentTimeMillis);
                            aggDataSyncInfoService.updateAggDataSyncInfoByInsert(upStreamTenantId, aggDataSyncInfoDo, SyncStatusEnum.SYNC_ED);
                        } catch (Exception e) {
                            log.error("synchronization downStreamTenant agg_data error this tenantId:{}, this viewId is {}", downstreamTenantId, viewId, e);
                        }
                    }
                });
            }, executorService)).toArray(CompletableFuture[]::new)).join();
        } catch (Exception e) {
            log.error("synchronization N downStream agg_data To 1 agg_downStream_data error, tenantId : {}", upStreamTenantId);
        } finally {
            executorService.shutdown();
        }
        Map<String, Set<String>> apiNameEi = Maps.newHashMap();
        if (syncCounts.get() > 0L) {
            apiNameEi.put(CHContext.AGG_DOWNSTREAM_DATA, Sets.newHashSet(upStreamTenantId));
        }
        dbTableSyncInfo.setApiNameEiMap(JSON.toJSONString(apiNameEi));
        dbTableSyncInfo.setLastModifiedTime(new Date().getTime());
        dbTableSyncInfo.setBatchNum(nextBatchNum);
        dbTableSyncInfo.setMaxSysModifiedTime(currentTimeMillis);
        dbTableSyncInfo.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
        if (offline) {
            dbTableSyncInfoService.batchUpsertDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
        } else {
            dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
        }
    }

    public List<Pair<String, String>> getDownStreamTenantIdList(String tenantId) {
        Map<String, String> eaToObjectMap = bizEnterpriseRelationDao.queryBizEnterpriseRelationMap(tenantId);
        if (MapUtils.isEmpty(eaToObjectMap)) {
            return Lists.newArrayList();
        }
        List<Pair<String, String>> objectToEiPair = Lists.newArrayList();
        List<String> downStreamTenantIdList = eaToObjectMap.keySet().stream().toList();
        Map<String, Integer> EAToEIMap = eieaConverter.enterpriseAccountToId(downStreamTenantIdList);
        if (MapUtils.isEmpty(EAToEIMap)) {
            return Lists.newArrayList();
        }
        for (Map.Entry<String, String> entry : eaToObjectMap.entrySet()) {
            if (EAToEIMap.containsKey(entry.getKey())) {
                objectToEiPair.add(Pair.of(String.valueOf(EAToEIMap.get(entry.getKey())),entry.getValue()));
//                objectToEiMap.put(String.valueOf(EAToEIMap.get(entry.getKey())), entry.getValue());
            }
        }
        return objectToEiPair;
    }


    /**
     * 获取下游图表槽位对应上游agg_downstream_data槽位
     */
    public Map<String, Map<String, String>> getDownStreamAggToUpStreamAggMap(String upStreamTenantId, List<DownStreamSyncInfo> downsStreamSyncInfoList) {
        if (CollectionUtils.isEmpty(downsStreamSyncInfoList)) {
            return Maps.newLinkedHashMap();
        }
        List<String> viewIdList = downsStreamSyncInfoList.stream().map(DownStreamSyncInfo::getViewId).toList();
        List<StatFieldDO> statFieldList = statFieldDao.findStatFieldList(upStreamTenantId, viewIdList);
        if (CollectionUtils.isEmpty(statFieldList)) {
            return Maps.newHashMap();
        }
        Map<String, Map<String, String>> downStreamAggToUpStreamAggMap = Maps.newHashMap();
        Map<String, String> fieldIdToLocationMap = statFieldList.stream().collect(Collectors.toMap(x -> x.getDownstreamViewId() + "_" + x.getDownstreamFieldId(), StatFieldDO::getDbFieldName, (item1, item2) -> item2));
        for (DownStreamSyncInfo downStreamSyncInfo : downsStreamSyncInfoList) {
            Map<String, String> aggDownstreamMap = Maps.newLinkedHashMap();
            Map<String, String> fieldIdToFieldLocaltionMap = downStreamSyncInfo.getFieldIdToFieldLocaltionMap();
            for (Map.Entry<String, String> entry : fieldIdToFieldLocaltionMap.entrySet()) {
                String key = downStreamSyncInfo.getViewId() + "_" + entry.getKey();
                String upLocation = fieldIdToLocationMap.get(key);
                if (StringUtils.isNotBlank(upLocation)) {
                    aggDownstreamMap.put(entry.getValue(), upLocation);
                }
            }
            downStreamAggToUpStreamAggMap.put(downStreamSyncInfo.getViewId(), aggDownstreamMap);
        }
        return downStreamAggToUpStreamAggMap;
    }

    /**
     * 判断下游数据是否进行同步
     */
    private boolean isOfflineSynchronizationTime(BIAggSyncInfoDO biAggSyncInfo) {
        Instant timestampInstant = Instant.ofEpochMilli(biAggSyncInfo.getLastSyncTime());
        Instant now = Instant.now();
        Duration duration = Duration.between(timestampInstant, now);
        return duration.toHours() > 24;
    }

    /**
     * 判断agg_data_sync_info那些状态能够进行同步
     */
    private boolean isAbleSynchronizationStatus(AggDataSyncInfoDo aggDataSyncInfoDo) {
        Set<SyncStatusEnum> ableEnumList = Sets.newHashSet(SyncStatusEnum.SYNC_ABLE, SyncStatusEnum.SYNC_ED);
        return ableEnumList.stream().anyMatch(x -> Objects.equals(aggDataSyncInfoDo.getStatus(), x.getStatus()));
    }
}
