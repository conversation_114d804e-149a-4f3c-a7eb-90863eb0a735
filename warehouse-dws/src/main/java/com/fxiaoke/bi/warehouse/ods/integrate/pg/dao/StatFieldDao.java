package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao;

import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.StatFieldMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Repository
public class StatFieldDao {

    @Resource
    private StatFieldMapper statFieldMapper;

    /**
     * 根据上游的tenantId和viewId查询同步虚拟指标信息
     */
    public List<StatFieldDO> findStatFieldList(String tenantId, List<String> viewIdList) {
        try {
            return statFieldMapper.setTenantId(tenantId).findStatFieldList(tenantId, viewIdList);
        } catch (Exception e) {
            log.error("findStatFiledList error tenantId is {}, viewIdList is {}", tenantId, viewIdList, e);
        }
        return Lists.newArrayList();
    }

    /**
     * 根据上游企业的tenantId查询企业下的虚拟指标
     */
    public List<StatFieldDO> findStatFieldListByTenantId(String tenantId) {
        try {
            return statFieldMapper.setTenantId(tenantId).getVirtualStatFieldList(tenantId);
        } catch (Exception e) {
            log.error("findStatFieldListByTenantId error tenantId is {}", tenantId, e);
        }
        return Lists.newArrayList();
    }
}
