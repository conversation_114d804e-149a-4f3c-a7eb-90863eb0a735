package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.facishare.converter.EIEAConverter;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.DataSourceEnterprise;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.SourceTenantInfo;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.BizEnterpriseRelationDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BizEnterpriseRelationDO;
import com.fxiaoke.enterpriserelation2.arg.*;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.*;
import com.fxiaoke.enterpriserelation2.result.data.TenantGroupDownstreamEnterpriseData;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.TenantGroupService;
import com.fxiaoke.enterpriserelation2.service.UpstreamService;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 互联平台相关service
 */
@Slf4j
@Service
public class EnterpriseRelationRpcService {

    @Resource
    private TenantGroupService tenantGroupService;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private BizEnterpriseRelationDao bizEnterpriseRelationDao;

    @Resource
    private FxiaokeAccountService fxiaokeAccountService;

    @Resource
    private UpstreamService upstreamService;

    /**
     * 分页查询每一页的数量
     */
    private static int PAGE_SIZE;

    /**
     * 最大分页数量
     */
    private static int MAX_PAGE_SIZE;

    /**
     * BI聚合数据同步服务互联应用APPId
     * 线上和线下配置不一样，默认为线上
     */
    private String biDataSyncAppId;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
            biDataSyncAppId = config.get("bi_data_sync_app_id", "FSAID_11491313");
        });

        ConfigFactory.getConfig("fs-bi-common", config -> {
            PAGE_SIZE = config.getInt("rpcTenantGroupIdPageSize", 5000);
            MAX_PAGE_SIZE = config.getInt("rpcTenantGroupIdMaxPageSize", 10);
        });
    }

    /**
     * 根据企业组id查询出互联企业
     */
    public List<String> getTenantIdListByTenantGroupId(String tenantGroupId, Integer ei) {
        HeaderObj headerObj = HeaderObj.newInstance(ei);
        ListTenantGroupDownstreamEnterprisesArg listTenantGroupDownstreamEnterprisesArg = new ListTenantGroupDownstreamEnterprisesArg();
        listTenantGroupDownstreamEnterprisesArg.setTenantGroupId(tenantGroupId);
        List<String> dataSourceTenantList = Lists.newArrayList();
        int pageNumber = 1;
        listTenantGroupDownstreamEnterprisesArg.setPageSize(PAGE_SIZE);
        List<String> tenantIdList;
        try {
            do {
                listTenantGroupDownstreamEnterprisesArg.setPageNumber(pageNumber);
                RestResult<ListTenantGroupDownstreamEnterprisesResult> result = tenantGroupService.listTenantGroupDownstreamEnterprises(headerObj, listTenantGroupDownstreamEnterprisesArg);
                List<TenantGroupDownstreamEnterpriseData> tenantGroupDownstreamEnterpriseVoList = result.getData().getTenantGroupDownstreamEnterpriseVos();
                tenantIdList = tenantGroupDownstreamEnterpriseVoList.stream()
                                                                    .map(TenantGroupDownstreamEnterpriseData::getOuterTenantId)
                                                                    .map(String::valueOf)
                                                                    .distinct()
                                                                    .collect(Collectors.toList());
                dataSourceTenantList.addAll(tenantIdList);
                pageNumber++;
                if (pageNumber > MAX_PAGE_SIZE) {
                    throw new RuntimeException(
                      "Reached maxPageSize, Possible infinite loop, this pageNumber is " + pageNumber);
                }
            } while (tenantIdList.size() == PAGE_SIZE);
        } catch (Exception e) {
            log.error("getTenantIdListByPageNumber error this ei:{}, tenantGroupId:{}", ei, tenantGroupId, e);
            return Lists.newArrayList();
        }
        return dataSourceTenantList;
    }

    /**
     * 根据互联控件获取互联企业
     */
    public List<String> getTenantIdList(List<DataSourceEnterprise> dataSourceEnterpriseList, String tenantId) {
        if (CollectionUtils.isEmpty(dataSourceEnterpriseList)) {
            return Lists.newArrayList();
        }
        try {
            Map<String, List<String>> typeToDataSourceEnterpriseMap = dataSourceEnterpriseList.stream()
                                                                                              .collect(Collectors.groupingBy(DataSourceEnterprise::getType, Collectors.mapping(DataSourceEnterprise::getId, Collectors.toList())));
            List<String> tenantIdList = typeToDataSourceEnterpriseMap.getOrDefault(DataSourceEnterprise.TENANT_TYPE, Lists.newArrayList());
            List<String> tenantGroupIdList = typeToDataSourceEnterpriseMap.getOrDefault(DataSourceEnterprise.TENANT_GROUP_TYPE, Lists.newArrayList());
            List<String> tenantIdByGroupIdList = tenantGroupIdList.stream().map(x -> getTenantIdListByTenantGroupId(x, Integer.parseInt(tenantId))).flatMap(Collection::stream).toList();
            tenantIdList.addAll(tenantIdByGroupIdList);
            return tenantIdList.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getTenantIdList by dataSourceEnterpriseList this tenantId is {}, this dataSourceEnterpriseList is {}", tenantId, dataSourceEnterpriseList, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 调用互联jar包根据企业名称批量查询ea
     */
    public Map<Long, String> getTenantEaByOutTenantIds(List<String> outEiList, String targetTenantId) {
        if (CollectionUtils.isEmpty(outEiList)) {
            return Maps.newHashMap();
        }
        List<Long> outerTenantIds = outEiList.stream().map(Long::parseLong).collect(Collectors.toList());
        HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(targetTenantId));
        BatchGetEaByOuterTenantIdArg batchGetEaByOuterTenantIdArg = new BatchGetEaByOuterTenantIdArg();
        batchGetEaByOuterTenantIdArg.setOuterTenantIds(outerTenantIds);
        try {
            RestResult<BatchGetEaByOuterTenantIdResult> batchGetEaByOuterTenantIdResultRestResult = fxiaokeAccountService.batchGetEaByOuterTenantId(headerObj, batchGetEaByOuterTenantIdArg);
            return batchGetEaByOuterTenantIdResultRestResult.getData().getOuterTenantId2EaMap();
        } catch (Exception e) {
            log.error("getTenantEaByOutTenantIds error this targetTenantId is {}", targetTenantId, e);
            return Maps.newHashMap();
        }
    }

    /**
     * 调用互联jar包根据企业tenantId查询互联企业id
     */
    public Map<String, Long> getOutTenantIdsByEas(List<String> eas, String targetTenantId) {
        if (CollectionUtils.isEmpty(eas)) {
            return Maps.newHashMap();
        }
        HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(targetTenantId));
        BatchGetOuterTenantIdByEaArg batchGetOuterTenantIdByEaArg = new BatchGetOuterTenantIdByEaArg();
        batchGetOuterTenantIdByEaArg.setEas(eas);
        try {
            RestResult<BatchGetOuterTenantIdByEaResult> batchGetOuterTenantIdByEaResultRestResult = fxiaokeAccountService.batchGetOuterTenantIdByEa(headerObj, batchGetOuterTenantIdByEaArg);
            return batchGetOuterTenantIdByEaResultRestResult.getData().getEa2OuterTenantIdMap();
        } catch (Exception e) {
            log.error("getOutTenantIdsByEas error this targetTenantId is {}", targetTenantId, e);
            return Maps.newHashMap();
        }
    }

    /**
     * 根据互联控件筛选器查询企业对应的mapper_account_id
     */
    public List<SourceTenantInfo> getSourceTenantToObject(List<DataSourceEnterprise> dataSourceEnterpriseList, String tenantId) {
        if (CollectionUtils.isEmpty(dataSourceEnterpriseList)) {
            return Lists.newArrayList();
        }
        List<String> outEiList = getTenantIdList(dataSourceEnterpriseList, tenantId);
        //根据outTenantId查询企业mapper_account_id
        List<BizEnterpriseRelationDO> bizEnterpriseRelationDOS = bizEnterpriseRelationDao.queryBizEnterpriseRelationByIds(tenantId, outEiList);
        Map<String, String> idToObjectIdMap = bizEnterpriseRelationDOS.stream().filter(x -> Objects.nonNull(x.getMapperAccountId()))
                .collect(Collectors.toMap(BizEnterpriseRelationDO::getId, BizEnterpriseRelationDO::getMapperAccountId, (item1, item2) -> item2));
        Map<String, String> idToNameMap = bizEnterpriseRelationDOS.stream()
                                                              .collect(Collectors.toMap(BizEnterpriseRelationDO::getId, BizEnterpriseRelationDO::getName, (item1, item2) -> item2));
        Map<Long, String> outTenantIdToEaMap = getTenantEaByOutTenantIds(outEiList, tenantId);
        List<String> eaList = outTenantIdToEaMap.values().stream().toList();
        Map<String, Integer> EaToEiMap = eieaConverter.enterpriseAccountToId(eaList);
        List<SourceTenantInfo> sourceTenantToObjectList = Lists.newArrayList();
        idToObjectIdMap.forEach((id, objectId) -> {
            String ea = outTenantIdToEaMap.get(Long.parseLong(id));
            String ei = String.valueOf(EaToEiMap.get(ea));
            String name = idToNameMap.get(id);
            SourceTenantInfo sourceTenantInfo = new SourceTenantInfo(ei, objectId, ea, name);
            sourceTenantToObjectList.add(sourceTenantInfo);
        });
        return sourceTenantToObjectList;
    }

    /**
     * 获取BI数据同步应用管理员
     */
    public List<Integer> getBiDataSyncAppAdminUserIdList(String tenantId) {
        try {
            int ei = Integer.parseInt(tenantId);
            String ea = eieaConverter.enterpriseIdToAccount(ei);
            HeaderObj headerObj = HeaderObj.newInstance(ei);
            ListAdminsArg listAdminsArg = new ListAdminsArg();
            listAdminsArg.setUpstreamEa(ea);
            listAdminsArg.setLinkAppId(biDataSyncAppId);
            RestResult<List<ListAdminsByLinkAppResult>> listRestResult = upstreamService.listAdminsByLinkAppId(headerObj, listAdminsArg);
            return listRestResult.getData().stream().map(ListAdminsByLinkAppResult::getFsUserId).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getBiDataSyncAppAdminUserIdList by upstreamService listAdminsByLinkAppId error this tenantId is {}", tenantId, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 获取BI数据同步应用状态 启用/停用状态
     */
    public boolean checkBiDataSyncAppStatus(String tenantId) {
        try {
            int ei = Integer.parseInt(tenantId);
            String ea = eieaConverter.enterpriseIdToAccount(ei);
            HeaderObj headerObj = HeaderObj.newInstance(ei);
            ListLinkAppStatusArg listLinkAppStatusArg = new ListLinkAppStatusArg();
            listLinkAppStatusArg.setUpstreamEa(ea);
            listLinkAppStatusArg.setLinkAppIds(Lists.newArrayList(biDataSyncAppId));
            RestResult<List<ListLinkAppStatusResult>> listRestResult = upstreamService.listLinkAppStatus(headerObj, listLinkAppStatusArg);
            return listRestResult.getData().stream().filter(x -> StringUtils.equals(x.getAppId(), biDataSyncAppId)).anyMatch(ListLinkAppStatusResult::isStatus);
        } catch (Exception e) {
            log.error("checkBiDataSyncAppStatus by upstreamService listLinkAppStatus error this tenantId is {}", tenantId, e);
            return false;
        }
    }

    /**
     * 根据互联企业查询mapper_account_id即object_id
     */
    public Map<String, String> getSourceTenantToObjectId(String targetTenantId, List<String> sourceTenantList) {
        if (CollectionUtils.isEmpty(sourceTenantList)) {
            return Maps.newHashMap();
        }
        Map<String, Integer> EaToEiMap = eieaConverter.enterpriseAccountToId(sourceTenantList);
        Map<String, String> eaToObjectIdMap = bizEnterpriseRelationDao.queryEaToObjectIdMap(targetTenantId, sourceTenantList);
        if (MapUtils.isEmpty(eaToObjectIdMap)) {
            return Maps.newHashMap();
        }
        Map<String, String> eiToObjectIdMap = Maps.newHashMap();
        EaToEiMap.forEach((ea, ei) -> {
            if (eaToObjectIdMap.containsKey(ea)) {
                eiToObjectIdMap.put(String.valueOf(EaToEiMap.get(ea)), eaToObjectIdMap.get(ea));
            }
        });
        return eiToObjectIdMap;
    }

    /**
     * 根据互联企业id查询互联企业的ea
     */
    public List<BizEnterpriseRelationDO> queryBizEnterpriseRelation(List<String> outEiList, String targetTenantId) {
        List<String> ids = outEiList.stream().map(String::valueOf).distinct().collect(Collectors.toList());
        return bizEnterpriseRelationDao.queryBizEnterpriseRelationByIds(targetTenantId, ids);
    }

}
