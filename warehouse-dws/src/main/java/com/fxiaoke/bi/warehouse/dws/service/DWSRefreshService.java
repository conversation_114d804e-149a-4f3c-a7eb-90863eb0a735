package com.fxiaoke.bi.warehouse.dws.service;

import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.bean.PreAggFilter;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.DateTimeUtil;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.dws.exception.RetryException;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.transform.model.GoalChangeType;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.dws.utils.sql.SQLUtils;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;


@Service
public class DWSRefreshService {
  private final static Logger refreshLog = LoggerFactory.getLogger("refresh");
  @Resource
  private TopologyTableService topologyTableService;
  @Resource
  private ClickHouseService clickHouseService;
  @Resource
  private CHDataSource chDataSource;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  private static final ThreadFactory factory = new ThreadFactoryBuilder().setDaemon(true).setNameFormat("ch-refresh-%d").build();
  private ExecutorService executor;
  @PostConstruct
  public void init() {
     executor = Executors.newFixedThreadPool(20,  factory);
  }

  public void refreshStatViewAsync(String tenantId, String viewId, int intervalDays, boolean delOld) {
    executor.submit(() -> this.refreshStatView(tenantId, viewId, intervalDays, true));
  }

  public void refreshStatView(String tenantId, String viewId, int intervalDays, boolean delOld) {
    StopWatch stopWatch = StopWatch.createStarted("begin");
    String lockKey = String.format("dws_refresh_%s_%s", tenantId, viewId);
    try (JedisLock jedisLock = new JedisLock(jedisCmd, lockKey, 1000 * 60 * 60 * 10)) {
      if(!jedisLock.tryLock()){
        refreshLog.error("lock error, tenantId={}, viewId={}", tenantId, viewId);
        return;
      }
      TopologyTable topologyTable = topologyTableService.findByTenantIdAndSourceId(tenantId, viewId);
      if (topologyTable == null) {
        refreshLog.error("topology table not found, tenantId={}, viewId={}", tenantId, viewId);
        return;
      }
      // change topology table status to NoNeedCal
      topologyTableService.changeTopologyTable2NoNeedCalByKey(tenantId, topologyTable.getStatViewUniqueKey());
      if (delOld) {
        try {
          stopWatch.start(String.format("deleteAggData:tenantId:%s,viewId:%s", tenantId, viewId));
          this.deleteAggData(tenantId, viewId, topologyTable.getStatViewUniqueKey(), topologyTable.getDatabaseId());
          stopWatch.stop();
        } catch (Exception e) {
          refreshLog.error("deleteStatView error, tenantId={}, viewId={}", tenantId, viewId, e);
        }
      }
      topologyTable.setStatus(TopologyTableStatus.Prepared.getValue());
      List<TopologyTableAggRule> statRuleList = topologyTable.getStatRuleList();
      Map<String, PreAggFilter> allAggWheres = null;
      if (GrayManager.isAllowByRule("support_merge_agg_rule", tenantId) && topologyTable.shouldMergeAllRule()) {
        TopologyTableAggRule baseTopologyTableAggRule = statRuleList.getFirst();
        List<TopologyTableAggRule> otherTopologyTableAggRules = statRuleList.subList(1, statRuleList.size());
        allAggWheres = baseTopologyTableAggRule.mergeOtherTopologyRule(otherTopologyTableAggRules);
        statRuleList = Lists.newArrayList(baseTopologyTableAggRule);
      }
      if (statRuleList.isEmpty()) {
        refreshLog.error("stat rule list is empty, tenantId={}, viewId={}", tenantId, viewId);
        return;
      }
      stopWatch.start(String.format("refreshStatView:tenantId:%s,viewId:%s", tenantId, viewId));
      long sysModifiedTime = System.currentTimeMillis();
      for (TopologyTableAggRule statRule : statRuleList) {
        this.refreshStatView(tenantId, viewId, topologyTable, statRule, allAggWheres, intervalDays, topologyTable.getTimezone(), topologyTable.getDatabaseId());
      }
      stopWatch.stop();
      topologyTableService.changeTopologyTable2CalByKey(tenantId, topologyTable.getStatViewUniqueKey(), 0, sysModifiedTime);
    } catch (Exception e) {
      refreshLog.error("lock error, tenantId={}, viewId={}", tenantId, viewId, e);
      throw new RuntimeException(e);
    }
    refreshLog.info("refreshStatView:tenantId:{},viewId:{},cost:{}", tenantId, viewId, stopWatch.prettyPrint());
  }

  /**
   *
   * @param tenantId 租户id
   * @param viewId 视图id
   * @param topologyTable 拓扑图
   * @param topologyTableAggRule 规则指标
   * @param allAggWheres 指标合并条件
   * @param intervalDays 日期间隔
   * @param timezone 时区
   * @param databaseId 外部数据源库id
   */
  public void refreshStatView(String tenantId,
                              String viewId,
                              TopologyTable topologyTable,
                              TopologyTableAggRule topologyTableAggRule,
                              Map<String, PreAggFilter> allAggWheres,
                              int intervalDays,
                              String timezone,
                              String databaseId) {
    ActionDateConfig actionDateConfig = ActionDateConfig.parse(topologyTableAggRule.getActionDateConfigString(), timezone);
    String tableAlias = actionDateConfig.getTableAlias();
    Map<String, NodeTable> actionNodeTables = topologyTableAggRule.findNodeTablesByAlias(Sets.newHashSet(tableAlias));
    NodeTable actionNodeTable = actionNodeTables.get(tableAlias);
    BIPair<String, String> dateRange = this.getActionDateRange(tenantId, actionNodeTable, actionDateConfig, databaseId);
    if (dateRange == null) {
      refreshLog.warn("date range is null, tenantId={}, viewId={}", tenantId, viewId);
      return;
    }
    String fieldId = topologyTableAggRule.getFieldId();
    long beginDate = DateTimeUtil.toEpochMilli(DateTimeUtil.toZonedDateTimeOfBeginDate(Long.parseLong(dateRange.first), ZoneId.of(timezone)));
    long endDate = DateTimeUtil.toEpochMilli(DateTimeUtil.toZonedDateTimeOfBeginDate(Long.parseLong(dateRange.second), ZoneId.of(timezone)).plusDays(1L));
    SQLUtils.pushDownActionDateFilterTemplate(actionNodeTable);
    TopologyTableAggRuleMonitor topologyTableAggRuleMonitor = topologyTableAggRule.toStatRuleMonitor(topologyTable, null, GoalChangeType.DATA_ALL, null, allAggWheres);
    long toDate = 0L;
    do {
      long startTime = System.currentTimeMillis();
      toDate = beginDate + (intervalDays * 24 * 60 * 60 * 1000L);
      String actionDateFilter = SQLUtils.createPushDownActionDateFilterSQL(actionNodeTable, actionDateConfig, BIPair.of(String.valueOf(beginDate), String.valueOf(toDate)));
      List<String> aggSQLs = topologyTableAggRuleMonitor.computeSQL(Map.of(SQLPlaceHolder.batch_num, 0L, SQLPlaceHolder.actionDateFilter, actionDateFilter));
      try {
        this.retryExecuteStatRule(tenantId, viewId, fieldId, aggSQLs, 3, databaseId);
        refreshLog.info("retryExecuteStatRule finish tenantId={}, viewId={},fieldId:{}, beginDate={}, toDate={} cost:{}ms", tenantId, viewId, fieldId, beginDate, toDate,
          System.currentTimeMillis() - startTime);
      } catch (RetryException e) {
        throw new RuntimeException(e);
      }
      beginDate = toDate;
    } while (toDate <= endDate);
    //计算日期为空的情况
    String actionDateFilter = SQLUtils.createPushDownActionDateFilterSQL(actionNodeTable, actionDateConfig, null);
    List<String> aggSQLs = topologyTableAggRuleMonitor.computeSQL(Map.of(SQLPlaceHolder.batch_num, 0L, SQLPlaceHolder.actionDateFilter, actionDateFilter));
    try {
      long startTime = System.currentTimeMillis();
      this.retryExecuteStatRule(tenantId, viewId, fieldId, aggSQLs, 3, databaseId);
      refreshLog.info("retryExecuteStatRule finish tenantId={}, viewId={},fieldId:{}, actionDate is null cost:{}ms", tenantId, viewId, fieldId,
        System.currentTimeMillis() - startTime);
    } catch (RetryException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   *
   * @param tenantId 租户id
   * @param nodeTable
   * @param actionDateConfig
   * @param databaseId
   * @return
   */
  public BIPair<String, String> getActionDateRange(String tenantId,
                                                   NodeTable nodeTable,
                                                   ActionDateConfig actionDateConfig,
                                                   String databaseId) {
    String chDBRule = chDataSource.findChURLByEi(tenantId, databaseId);
    String database = CommonUtils.getDBName(chDBRule);
    String sql = SQLUtils.createActionDateRangeSQL(tenantId, nodeTable, actionDateConfig, database);
    if (sql == null) {
      return null;
    }
    AtomicReference<BIPair<String, String>> result = new AtomicReference<>();
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDBRule)) {
      jdbcConnection.query(sql, resultSet -> {
        if (resultSet.next()) {
          Object startDate = resultSet.getObject("minDate");
          Object endDate = resultSet.getObject("maxDate");
          result.set(BIPair.of(String.valueOf(startDate), String.valueOf(endDate)));
        }
      });
    } catch (Exception e) {
      throw new RuntimeException("get action date range error", e);
    }
    return result.get();
  }

  public void deleteAggData(String tenantId,
                            String viewId,
                            String statViewUniqueKey,
                            String databaseId) {
    String sql = SQLUtils.createDeleteAggDataSQL(tenantId, statViewUniqueKey, databaseId);
    String chDBRule = chDataSource.findChURLByEi(tenantId, databaseId);
    if (StringUtils.isBlank(chDBRule)) {
      refreshLog.warn("findChURLByEi is blank tenantId:{},databaseId:{}", tenantId, databaseId);
    }
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chDBRule)) {
      jdbcConnection.executeUpdate(sql);
    } catch (Exception e) {
      refreshLog.error("deleteAggData error, tenantId:{},viewId:{},statViewUniqueKey:{},databaseId:{},sql:{}", tenantId, viewId, statViewUniqueKey, databaseId, sql, e);
    }
  }

  /**
   *
   * @param tenantId 租户id
   * @param viewId 视图id
   * @param fieldId 指标id
   * @param sqlList 指标sql列表
   * @param retryTimes 重试次数
   * @param databaseId 外部数据库id
   * @throws RetryException 重试失败异常
   */
  private void retryExecuteStatRule(String tenantId,
                                    String viewId,
                                    String fieldId,
                                    List<String> sqlList,
                                    int retryTimes,
                                    String databaseId) throws RetryException {
    String settings = Constants.JOIN_USE_NULLS_1_CACHE_1;
    String chDB = chDataSource.findChURLByEi(tenantId, databaseId);
    for (int rt = 0; rt < retryTimes; rt++) {
      try {
        clickHouseService.executeSQLWithJdbcUrl(chDB, sqlList.getFirst() + settings, 30 * 60 * 1000);
        break;
      } catch (Exception e) {
        if (rt < retryTimes - 1) {
          refreshLog.warn("executeStatRule error begin to retry tenantId:{},viewId:{},fieldId:{},retryTimes:{}", tenantId, viewId, fieldId, retryTimes, e);
          Uninterruptibles.sleepUninterruptibly(Utils.calculateWaitTime(rt, 10, 25), TimeUnit.SECONDS);
        } else {
          refreshLog.error("retryExecuteStatRule tenantId:{},viewId:{},fieldId:{},error", tenantId, viewId,fieldId, e);
          throw new RetryException(e);
        }
      }
    }
  }
}
