package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncTableStatusEnum;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableStatus;
import com.fxiaoke.bi.warehouse.dws.service.StatTopologyService;
import com.fxiaoke.bi.warehouse.dws.service.TopologyTableService;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.*;
import com.fxiaoke.bi.warehouse.ods.integrate.model.BIAggSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.BIMtTopologyTableDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.BiDataSyncPolicyDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.StatFieldDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BiDataSyncPolicyDo;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.TopologyTableIntegrateDO;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.bi.warehouse.ods.service.DbTableSyncInfoService;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BiDataSyncPolicyService {

    @Resource
    private BiDataSyncPolicyDao biDataSyncPolicyDao;

    @Resource
    private EnterpriseRelationRpcService enterpriseRelationRpcService;

    @Resource
    private CHRouterPolicy chRouterPolicy;

    @Resource
    private BIMtTopologyTableDao biMtTopologyTableDao;

    @Resource
    private StatFieldDao statFieldDao;

    @Resource
    private AggDataSyncInfoService aggDataSyncInfoService;

    @Resource
    private TopologyTableService topologyTableService;

    @Resource
    private RptMessageService rptMessageService;

    @Resource
    private CHDataSource chDataSource;

    @Resource
    private DbTableSyncInfoService dbTableSyncInfoService;
    @Resource
    private StatTopologyService statTopologyService;

    @Resource
    private AggDownStreamComplexService aggDownStreamComplexService;

    private List<String> grayPolicyIdList;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-bi-common", config -> {
            grayPolicyIdList = Splitter.on(",").splitToList(config.get("grayPolicyIdList", ""));
        });
    }

    /**
     * 根据同步策略进行1+N跨租户同步
     */
    public void synchronizeDownstreamAggData(BIAggSyncInfoBO biAggSyncInfoBO, long nextBatchNum, DBSyncInfoBO dbSyncInfoCopy,String partitionName) {
        String targetTenantId = biAggSyncInfoBO.getTenantId();
        //查询BI聚合数据同步服务应用状态
        if (!enterpriseRelationRpcService.checkBiDataSyncAppStatus(targetTenantId)) {
            log.error("checkBiDataSyncAppStatus false tenantId:{}",targetTenantId);
            return;
        }
        RouterInfo targetTenantRouterInfo = chRouterPolicy.getRouterInfo(targetTenantId);
        log.info("synchronization N downStream agg_data To 1 agg_downStream_data, tenantId : {}", targetTenantId);
        //首先清理bi_data_sync_info
        this.insertBeforeDataForBiDataSyncInfo(targetTenantRouterInfo, nextBatchNum,partitionName);
        //开始同步bi_data_sync_info
        List<BiDataSyncPolicyDo> biDataSyncPolicyDoList = biDataSyncPolicyDao.queryBiDataSyncPolicyList(targetTenantId);
        if (CollectionUtils.isEmpty(biDataSyncPolicyDoList)) {
            return;
        }
        //首先获取agg_downstream_data 表得同步信息
        List<DbTableSyncInfoDO> dbTableSyncInfos = dbTableSyncInfoService.queryDbTableSyncInfosBySyncId(dbSyncInfoCopy.getPgDb(),dbSyncInfoCopy.getPgSchema(),biAggSyncInfoBO.getId(), CHContext.AGG_DOWNSTREAM_DATA);
        DbTableSyncInfoDO dbTableSyncInfo;
        boolean offline = false;
        if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
            dbTableSyncInfo = dbTableSyncInfos.getFirst();
            dbTableSyncInfo.setLastSyncTime(new Date().getTime());
        } else {
            dbTableSyncInfo = DbTableSyncInfoDO.createFrom(biAggSyncInfoBO, CHContext.AGG_DOWNSTREAM_DATA);
            dbTableSyncInfo.setLastSyncTime(new Date().getTime());
            offline = true;
        }
        //获取同步消息通知的互联应用管理员
        List<Integer> biDataSyncAppAdminUserIdList = enterpriseRelationRpcService.getBiDataSyncAppAdminUserIdList(targetTenantId);
        Map<String, String> upLocationMap = getUpLocationMap(targetTenantId);
        AtomicLong syncCounts = new AtomicLong();
        //根据同步策略进行数据同步
        for (BiDataSyncPolicyDo biDataSyncPolicyDo : biDataSyncPolicyDoList) {
            AtomicBoolean policyExistExceptionFlag = new AtomicBoolean(false);
            String policyId = biDataSyncPolicyDo.getPolicyId();
            String dataSourceEnterpriseListJson = biDataSyncPolicyDo.getDataSourceEnterprise();
            //查询数据源企业
            List<DataSourceEnterprise> dataSourceEnterpriseList = JSON.parseArray(dataSourceEnterpriseListJson, DataSourceEnterprise.class);
            if (CollectionUtils.isEmpty(dataSourceEnterpriseList)) {
                continue;
            }
            List<SourceTenantInfo> sourceTenantToObjectIdList = enterpriseRelationRpcService.getSourceTenantToObject(dataSourceEnterpriseList, targetTenantId);
            List<List<SourceTenantInfo>> monitPartitions = Lists.partition(sourceTenantToObjectIdList, (int) Math.ceil(sourceTenantToObjectIdList.size() / 10d));
            long currentTimeMillis = System.currentTimeMillis();
            final ExecutorService executorService = Executors.newFixedThreadPool(10);
            try {
                CompletableFuture.allOf(monitPartitions.stream()
                                                              .map(sourceTenantPartition -> CompletableFuture.runAsync(() -> {
                                                                  sourceTenantPartition.forEach(sourceTenantInfo -> {
                                                                      //最好抽成一个类
                                                                      if (grayPolicyIdList.contains(policyId)) {
                                                                          aggDownStreamComplexService.syncComplexAggData(syncCounts, currentTimeMillis, nextBatchNum, sourceTenantInfo.getEi(), sourceTenantInfo.getObjectId(), targetTenantRouterInfo, biDataSyncPolicyDo,partitionName);
                                                                      } else {
                                                                          syncDataSourceTenant(syncCounts, policyExistExceptionFlag, currentTimeMillis, nextBatchNum, policyId, sourceTenantInfo.getEi(), sourceTenantInfo.getObjectId(), sourceTenantInfo.getName(), targetTenantRouterInfo, biDataSyncPolicyDo, biDataSyncAppAdminUserIdList,partitionName, upLocationMap);
                                                                      }
                                                                  });
                                                              }, executorService))
                                                              .toArray(CompletableFuture[]::new)).join();
            } catch (Exception e) {
                log.error("synchronization N downStream agg_data To 1 agg_downStream_data error, tenantId : {}", targetTenantId, e);
            } finally {
                executorService.shutdown();
            }
            if (!policyExistExceptionFlag.get() && !grayPolicyIdList.contains(policyId)) {
                aggDataSyncInfoService.insertBiDataSyncPolicyLog(targetTenantId, dataSourceEnterpriseListJson, policyId, PolicyLogEnum.SUCCESS.getLogType(), "");
            }
            Map<String, Set<String>> apiNameEi = Maps.newHashMap();
            if (syncCounts.get() > 0L) {
                apiNameEi.put(CHContext.AGG_DOWNSTREAM_DATA, Sets.newHashSet(targetTenantId));
            }
            dbTableSyncInfo.setApiNameEiMap(JSON.toJSONString(apiNameEi));
            dbTableSyncInfo.setLastModifiedTime(new Date().getTime());
            dbTableSyncInfo.setBatchNum(nextBatchNum);
            dbTableSyncInfo.setMaxSysModifiedTime(currentTimeMillis);
            dbTableSyncInfo.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
            if (offline) {
                dbTableSyncInfoService.batchUpsertDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
            } else {
                dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
            }
        }
    }

    /**
     * 根据同步策略进行1+N跨租户同步
     */
    public void synchronizeDownstreamAggDataNew(String tenantId, long nextBatchNum, DBSyncInfoBO dbSyncInfoCopy,String partitionName) {
        //查询BI聚合数据同步服务应用状态
        if (!enterpriseRelationRpcService.checkBiDataSyncAppStatus(tenantId)) {
            log.error("synchronizeDownstreamAggDataNew checkBiDataSyncAppStatus false tenantId:{}", tenantId);
            return;
        }
        RouterInfo targetTenantRouterInfo = chRouterPolicy.getRouterInfo(tenantId);
        log.info("synchronizeDownstreamAggDataNew agg_data To 1 agg_downStream_data, tenantId : {}", tenantId);
        //首先清理bi_data_sync_info
        this.insertBeforeDataForBiDataSyncInfo(targetTenantRouterInfo, nextBatchNum,partitionName);
        //开始同步bi_data_sync_info
        List<BiDataSyncPolicyDo> biDataSyncPolicyDoList = biDataSyncPolicyDao.queryBiDataSyncPolicyList(tenantId);
        if (CollectionUtils.isEmpty(biDataSyncPolicyDoList)) {
            return;
        }
        //首先获取agg_downstream_data 表得同步信息
        List<DbTableSyncInfoDO> dbTableSyncInfos = dbTableSyncInfoService.queryDbTableSyncInfosBySyncId(dbSyncInfoCopy.getPgDb(),dbSyncInfoCopy.getPgSchema(),dbSyncInfoCopy.getId(), CHContext.AGG_DOWNSTREAM_DATA);
        DbTableSyncInfoDO dbTableSyncInfo;
        boolean offline = false;
        if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
            dbTableSyncInfo = dbTableSyncInfos.getFirst();
            dbTableSyncInfo.setLastSyncTime(new Date().getTime());
        } else {
            dbTableSyncInfo = DbTableSyncInfoDO.createFrom(dbSyncInfoCopy, CHContext.AGG_DOWNSTREAM_DATA);
            dbTableSyncInfo.setLastSyncTime(new Date().getTime());
            offline = true;
        }
        //获取同步消息通知的互联应用管理员
        List<Integer> biDataSyncAppAdminUserIdList = enterpriseRelationRpcService.getBiDataSyncAppAdminUserIdList(tenantId);
        Map<String, String> upLocationMap = getUpLocationMap(tenantId);
        AtomicLong syncCounts = new AtomicLong();
        //根据同步策略进行数据同步
        for (BiDataSyncPolicyDo biDataSyncPolicyDo : biDataSyncPolicyDoList) {
            AtomicBoolean policyExistExceptionFlag = new AtomicBoolean(false);
            String policyId = biDataSyncPolicyDo.getPolicyId();
            String dataSourceEnterpriseListJson = biDataSyncPolicyDo.getDataSourceEnterprise();
            //查询数据源企业
            List<DataSourceEnterprise> dataSourceEnterpriseList = JSON.parseArray(dataSourceEnterpriseListJson, DataSourceEnterprise.class);
            if (CollectionUtils.isEmpty(dataSourceEnterpriseList)) {
                continue;
            }
            List<SourceTenantInfo> sourceTenantToObjectIdList = enterpriseRelationRpcService.getSourceTenantToObject(dataSourceEnterpriseList, tenantId);
            List<List<SourceTenantInfo>> monitPartitions = Lists.partition(sourceTenantToObjectIdList, (int) Math.ceil(sourceTenantToObjectIdList.size() / 10d));
            long currentTimeMillis = System.currentTimeMillis();
            final ExecutorService executorService = Executors.newFixedThreadPool(10);
            try {
                CompletableFuture.allOf(monitPartitions.stream()
                                                       .map(sourceTenantPartition -> CompletableFuture.runAsync(() -> {
                                                           sourceTenantPartition.forEach(sourceTenantInfo -> {
                                                               //最好抽成一个类
                                                               if (grayPolicyIdList.contains(policyId)) {
                                                                   aggDownStreamComplexService.syncComplexAggData(syncCounts, currentTimeMillis, nextBatchNum, sourceTenantInfo.getEi(), sourceTenantInfo.getObjectId(), targetTenantRouterInfo, biDataSyncPolicyDo,partitionName);
                                                               } else {
                                                                   syncDataSourceTenant(syncCounts, policyExistExceptionFlag, currentTimeMillis, nextBatchNum, policyId, sourceTenantInfo.getEi(), sourceTenantInfo.getObjectId(), sourceTenantInfo.getName(), targetTenantRouterInfo, biDataSyncPolicyDo, biDataSyncAppAdminUserIdList,partitionName,upLocationMap);
                                                               }
                                                           });
                                                       }, executorService))
                                                       .toArray(CompletableFuture[]::new)).join();
            } catch (Exception e) {
                log.error("synchronization N downStream agg_data To 1 agg_downStream_data error, tenantId : {}", tenantId, e);
            } finally {
                executorService.shutdown();
            }
            if (!policyExistExceptionFlag.get() && !grayPolicyIdList.contains(policyId)) {
                aggDataSyncInfoService.insertBiDataSyncPolicyLog(tenantId, dataSourceEnterpriseListJson, policyId, PolicyLogEnum.SUCCESS.getLogType(), "");
            }
            Map<String, Set<String>> apiNameEi = Maps.newHashMap();
            if (syncCounts.get() > 0L) {
                apiNameEi.put(CHContext.AGG_DOWNSTREAM_DATA, Sets.newHashSet(tenantId));
            }
            dbTableSyncInfo.setApiNameEiMap(JSON.toJSONString(apiNameEi));
            dbTableSyncInfo.setLastModifiedTime(new Date().getTime());
            dbTableSyncInfo.setBatchNum(nextBatchNum);
            dbTableSyncInfo.setMaxSysModifiedTime(currentTimeMillis);
            dbTableSyncInfo.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
            if (offline) {
                dbTableSyncInfoService.batchUpsertDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
            } else {
                dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfoCopy, Lists.newArrayList(dbTableSyncInfo));
            }
        }
    }

    /**
     * 清理已删除的bi_data_sync_policy
     */
    public void insertBeforeDataForBiDataSyncInfo(RouterInfo targetTenantRouterInfo, long nextBatchNum,String partitionName) {
        String dbName = targetTenantRouterInfo.getDbName();
        String tenantId = targetTenantRouterInfo.getTenantId();
        //清理已经删除的bi_data_sync_info
        List<BiDataSyncPolicyDo> biDataSyncPolicyDoList = biDataSyncPolicyDao.queryDeleteBiDataSyncPolicyList(tenantId);
        if (CollectionUtils.isEmpty(biDataSyncPolicyDoList)) {
            return;
        }
        //删除agg_data_sync_info
        List<String> policyIdList = biDataSyncPolicyDoList.stream().map(BiDataSyncPolicyDo::getPolicyId).toList();
        List<AggDataSyncInfoDo> aggDataSyncInfoDos = aggDataSyncInfoService.queryAggDataSyncInfoByPolicyId(tenantId, policyIdList);
        if (CollectionUtils.isEmpty(aggDataSyncInfoDos)) {
            return;
        }
        String policyIdWhere = biDataSyncPolicyDoList.stream().map(x -> "'" + x.getPolicyId() + "'").distinct().collect(Collectors.joining(","));
        String insertColumn = "tenant_id, policy_id, view_id, view_version, object_id, hash_code, bi_sys_flag, bi_sys_batch_id, bi_sys_is_deleted, bi_sys_version, is_deleted,action_date";
        String selectColumn="";
        String aggDownStreamSqlTemplateSql = """
          INSERT INTO %s.agg_downstream_data (%s)
          select before.tenant_id    AS tenant_id
               , before.policy_id    AS policy_id
               , before.view_id      AS view_id
               , before.view_version AS view_version
               , before.object_id    AS object_id
               , before.hash_code    AS hash_code
               , %d                  AS bi_sys_flag
               , %d                  AS bi_sys_batch_id
               , toUInt8(0)          AS bi_sys_is_deleted
               , now()               AS bi_sys_version
               , %d                  AS is_deleted
               , before.action_date  AS action_date
               %s
          from (
              SELECT tenant_id
                   , policy_id
                   , view_id
                   , view_version
                   , object_id
                   , hash_code
                   , action_date
              FROM %s.agg_downstream_data
              WHERE (tenant_id = '%s' AND policy_id in (%s) AND bi_sys_flag = 1 AND is_deleted = 0)
              ) before
              SETTINGS final = 1
             , do_not_merge_across_partitions_select_final = 1
             , optimize_move_to_prewhere_if_final = 1
             , mutations_sync = 1;
          """;
        if (StringUtils.isNotBlank(partitionName)) {
            insertColumn = insertColumn + ",bi_sys_ods_part";
            selectColumn = ",'" + partitionName + "' AS bi_sys_ods_part";
        }
        String insertBeforeSql = String.format(aggDownStreamSqlTemplateSql, dbName, insertColumn,0, nextBatchNum,selectColumn, 0, dbName, tenantId, policyIdWhere);
        String insertAfterSql = String.format(aggDownStreamSqlTemplateSql, dbName, insertColumn,1, nextBatchNum,selectColumn, -1, dbName, tenantId, policyIdWhere);
        try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chRouterPolicy.getChJdbcURL(targetTenantRouterInfo, true), 7200000L)) {
            if (StringUtils.isNotBlank(partitionName)) {
                jdbcConnection.executeUpdate(insertAfterSql);
            } else {
                jdbcConnection.executeUpdate(insertBeforeSql);
                jdbcConnection.executeUpdate(insertAfterSql);
            }
        } catch (Exception e) {
            log.error("insertBeforeDataForBiDataSyncInfo error tenantId:{}, policyId:{}", tenantId, policyIdWhere, e);
            throw new RuntimeException("insertBeforeDataForBiDataSyncInfo error", e);
        }
        aggDataSyncInfoService.updateAggDataSyncInfoBatch(tenantId, aggDataSyncInfoDos);
    }

    /**
     * 根据同步描述进行同步数据源企业输出错误原因
     */
    public void syncDataSourceTenant(AtomicLong syncCounts, AtomicBoolean policyExistExceptionFlag, long currentTimeMillis, long nextBatchNum, String policyId, String sourceTenantId, String objectId, String tenantName, RouterInfo targetTenantRouterInfo, BiDataSyncPolicyDo biDataSyncPolicyDo, List<Integer> biDataSyncAppAdminUserIdList,String partitionName,Map<String, String> upLocationMap) {
        if (!rptMessageService.querySourceTenantSyncAllowSwitch(sourceTenantId, targetTenantRouterInfo.getTenantId())) {
            log.warn("the source enterprises shuts down dataSync, this tenantId is {}", sourceTenantId);
            aggDataSyncInfoService.insertBiDataSyncPolicyLog(targetTenantRouterInfo.getTenantId(), sourceTenantId, policyId, PolicyLogEnum.EXCEPTION.getLogType(), "N端企业" + tenantName + "已关闭同步");//IgnoreI18n
            rptMessageService.pushRptMessage(Integer.valueOf(targetTenantRouterInfo.getTenantId()), biDataSyncAppAdminUserIdList, biDataSyncPolicyDo.getPolicyName(), PolicyLogEnum.EXCEPTION.getLogType(), "N端企业" + tenantName + "已关闭同步");//IgnoreI18n
            return;
        }
        String aggMappingRuleJson = biDataSyncPolicyDo.getAggMappingRule();
        List<AggMappingRule> aggMappingRuleList = JSON.parseArray(aggMappingRuleJson, AggMappingRule.class);
        if (CollectionUtils.isEmpty(aggMappingRuleList)) {
            policyExistExceptionFlag.set(true);
            return;
        }
        if (StringUtils.isBlank(objectId)) {
            log.warn("the source enterprises does not have objectId, this tenantId is {}", sourceTenantId);
            aggDataSyncInfoService.insertBiDataSyncPolicyLog(targetTenantRouterInfo.getTenantId(), sourceTenantId, policyId, PolicyLogEnum.EXCEPTION.getLogType(), "N端企业" + tenantName + "企业无上下游关联信息");//IgnoreI18n
            rptMessageService.pushRptMessage(Integer.valueOf(targetTenantRouterInfo.getTenantId()), biDataSyncAppAdminUserIdList, biDataSyncPolicyDo.getPolicyName(), PolicyLogEnum.EXCEPTION.getLogType(), "企业无上下游关联信息");//IgnoreI18n
            policyExistExceptionFlag.set(true);
            return;
        }
        RouterInfo sourceTenantRouterInfo = chRouterPolicy.getRouterInfo(sourceTenantId);
        if (Objects.isNull(sourceTenantRouterInfo)) {
            log.warn("the source enterprises does not have clickhouse routeInfo, this tenantId is {}", sourceTenantId);
            aggDataSyncInfoService.insertBiDataSyncPolicyLog(targetTenantRouterInfo.getTenantId(), sourceTenantId, policyId, PolicyLogEnum.EXCEPTION.getLogType(), "N端企业" + tenantName + "无ch路由");//IgnoreI18n
            rptMessageService.pushRptMessage(Integer.valueOf(targetTenantRouterInfo.getTenantId()), biDataSyncAppAdminUserIdList, biDataSyncPolicyDo.getPolicyName(), PolicyLogEnum.EXCEPTION.getLogType(), "企业无ch路由");//IgnoreI18n
            policyExistExceptionFlag.set(true);
            return;
        }
        List<String> sourceIdList = aggMappingRuleList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getAggMappingList())).map(AggMappingRule::getViewId).distinct().collect(Collectors.toList());
        List<TopologyTableIntegrateDO> topologyTableIntegrateDOList = biMtTopologyTableDao.batchQuerySourceTenantTopology(sourceTenantId, sourceIdList);

        if (CollectionUtils.isEmpty(topologyTableIntegrateDOList)) {
            log.warn("the source enterprise does not have topologyTableIntegrateDOList, this tenantId is {}", sourceTenantId);
            aggDataSyncInfoService.insertBiDataSyncPolicyLog(targetTenantRouterInfo.getTenantId(), sourceTenantId, policyId, PolicyLogEnum.EXCEPTION.getLogType(), "N端企业" + tenantName + "因无数据源统计图导致同步异常");//IgnoreI18n
            rptMessageService.pushRptMessage(Integer.valueOf(targetTenantRouterInfo.getTenantId()), biDataSyncAppAdminUserIdList, biDataSyncPolicyDo.getPolicyName(), PolicyLogEnum.EXCEPTION.getLogType(), "因无数据源统计图导致同步异常");//IgnoreI18n
            policyExistExceptionFlag.set(true);
            return;
        }
        Map<String, List<AggMapping>> viewIdToAggMapping = aggMappingRuleList.stream()
                                                                             .filter(x -> CollectionUtils.isNotEmpty(x.getAggMappingList()))
                                                                             .collect(Collectors.toMap(AggMappingRule::getViewId, AggMappingRule::getAggMappingList, (item1, item2) -> item2));
        Map<String, String> viewIdToNameMap = aggMappingRuleList.stream()
                                                                .filter(x -> CollectionUtils.isNotEmpty(x.getAggMappingList()))
                                                                .collect(Collectors.toMap(AggMappingRule::getViewId, AggMappingRule::getViewName, (item1, item2) -> item2));
        //依次同步下游图表企业
        for (TopologyTableIntegrateDO topologyTableIntegrateDO : topologyTableIntegrateDOList) {
            String sourceId = topologyTableIntegrateDO.getSourceId();
            String statViewUniqueKey = topologyTableIntegrateDO.getStatViewUniqueKey();
            int version = topologyTableIntegrateDO.getVersion();
            //图表的状态是1才可以进行同步
            if (topologyTableIntegrateDO.getStatus() != 1) {
                log.warn("the source enterprise stat view status exist error, this tenantId is {}, this viewId is {}", sourceTenantId, sourceId);
                //aggDataSyncInfoService.insertBiDataSyncPolicyLog(targetTenantRouterInfo.getTenantId(), sourceTenantId, policyId, PolicyLogEnum.EXCEPTION.getLogType(), "N端企业" + tenantName + "统计图计算状态异常");//IgnoreI18n
                //rptMessageService.pushRptMessage(Integer.valueOf(targetTenantRouterInfo.getTenantId()), biDataSyncAppAdminUserIdList, biDataSyncPolicyDo.getPolicyName(), PolicyLogEnum.EXCEPTION.getLogType(), "统计图计算状态异常");//IgnoreI18n
                policyExistExceptionFlag.set(true);
                continue;
            }
            List<AggMapping> aggMappingList = viewIdToAggMapping.get(topologyTableIntegrateDO.getSourceId());
            String viewName = viewIdToNameMap.get(topologyTableIntegrateDO.getSourceId());
            Map<String, String> aggAggStatField = topologyTableIntegrateDO.getAggAggStatField();
            Map<String, String> statFieldLocation = topologyTableIntegrateDO.getStatFieldLocation();
            if (!checkStatView(aggMappingList, aggAggStatField, statFieldLocation)) {
                log.warn("the source enterprise stat view agg exist lackAgg fieldLocation status error, this tenantId is {}, this viewId is {}", sourceTenantId, sourceId);
                aggDataSyncInfoService.insertBiDataSyncPolicyLog(targetTenantRouterInfo.getTenantId(), sourceTenantId, policyId, PolicyLogEnum.EXCEPTION.getLogType(), "N端企业" + tenantName + "统计图" + viewName + "指标描述变更");//IgnoreI18n
                rptMessageService.pushRptMessage(Integer.valueOf(targetTenantRouterInfo.getTenantId()), biDataSyncAppAdminUserIdList, biDataSyncPolicyDo.getPolicyName(), PolicyLogEnum.EXCEPTION.getLogType(), "N端企业" + tenantName + "统计图" + viewName + "指标描述变更");//IgnoreI18n
                policyExistExceptionFlag.set(true);
                continue;
            }
            //开始同步上下游数据
            Map<String, String> downToUpMap = downToUpMap(aggMappingList, upLocationMap, statFieldLocation);
            List<String> fieldLocalTionList = downToUpMap.keySet().stream().toList();
            AggDataSyncInfoDo aggDataSyncInfoDo = aggDataSyncInfoService.queryAggDataSyncInfoByPolicyId(targetTenantRouterInfo.getTenantId(), sourceTenantId, sourceId, policyId, statViewUniqueKey);
            if (aggDataSyncInfoDo.getBatchNum() >= nextBatchNum) {
                log.warn("the source enterprise stat view sync batchNum greater than current batchNum, this tenantId is {}, this viewId is {}", sourceTenantId, sourceId);
                //policyExistExceptionFlag.set(true);
                continue;
            }
            try {
                //根据图表的statViewUniqueKey和version判断图表是否重新计算，如果重新计算需要重跑图,这端逻辑最好抽出来
                aggDataSyncInfoService.insertBeforeAggDownStreamData(targetTenantRouterInfo, aggDataSyncInfoDo, version, statViewUniqueKey, objectId, nextBatchNum, policyId,partitionName);
                boolean isFullSynchronization = aggDataSyncInfoDo.getBatchNum() == 0;
                aggDataSyncInfoDo.setViewVersion(version);
                aggDataSyncInfoDo.setViewId(statViewUniqueKey);
                long syncNums = aggDataSyncInfoService.synchronizationAggDataToAggDownStreamData(targetTenantRouterInfo.getTenantId(), aggDataSyncInfoDo, fieldLocalTionList, downToUpMap, isFullSynchronization, targetTenantRouterInfo, sourceTenantRouterInfo, objectId, currentTimeMillis, nextBatchNum, sourceId, policyId,partitionName);
                syncCounts.addAndGet(syncNums);
                aggDataSyncInfoDo.setViewId(sourceId);
                aggDataSyncInfoDo.setBatchNum(nextBatchNum);
                aggDataSyncInfoDo.setMaxSyncTimeStamp(currentTimeMillis);
                aggDataSyncInfoService.updateAggDataSyncInfoByPolicyId(targetTenantRouterInfo.getTenantId(), aggDataSyncInfoDo, SyncStatusEnum.SYNC_ED);
            } catch (Exception e) {
                log.error("sync source enterprise agg_data error this tenantId:{}, this viewId is {}", sourceId, sourceId, e);
            }
        }
    }


    /**
     * 校验同步图表是否存在异常
     */
    public boolean checkStatView(List<AggMapping> aggMappingList, Map<String, String> aggAggStatField, Map<String, String> statFieldLocation) {
        if (CollectionUtils.isEmpty(aggMappingList) || MapUtils.isEmpty(aggAggStatField) ||
          MapUtils.isEmpty(statFieldLocation)) {
            return false;
        }
        for (AggMapping aggMapping : aggMappingList) {
            AggLocationEnum aggLocationEnum = AggLocationEnum.getAggLocationEnumByAggType(aggMapping.getAggType());
            if (!aggAggStatField.containsKey(aggMapping.getDownFieldId())) {
                log.warn("this source enterprise view exist agg lack");
                return false;
            }
            String status = aggAggStatField.get(aggMapping.getDownFieldId());
            if (!Sets.newHashSet("1", "2", "6").contains(status)) {
                log.warn("this source enterprise view exist agg status error");
                return false;
            }
            String aggFieldLocation = statFieldLocation.get(aggMapping.getDownFieldId());
            AggLocationEnum currentAggLocationEnum = AggLocationEnum.getAggLocationEnumByLocation(aggFieldLocation);
            if (aggLocationEnum != currentAggLocationEnum) {
                log.warn("this source enterprise view exist agg fieldLocation change");
                return false;
            }
        }
        return true;
    }

    /**
     * 构建上下游指标映射
     */
    private Map<String, String> downToUpMap(List<AggMapping> aggMappingList, Map<String, String> upLocationMap, Map<String, String> statFieldLocation) {
        if (CollectionUtils.isEmpty(aggMappingList) || MapUtils.isEmpty(upLocationMap)) {
            return Maps.newHashMap();
        }
        Map<String, String> downToUpMap = Maps.newHashMap();
        for (AggMapping aggMapping : aggMappingList) {
            String downFieldId = aggMapping.getDownFieldId();
            String upFieldId = aggMapping.getUpFieldId();
            String upAggLocation = upLocationMap.get(upFieldId);
            String downAggLocation = statFieldLocation.get(downFieldId);
            downToUpMap.put(downAggLocation, upAggLocation);
        }
        return downToUpMap;
    }

    /**
     * 获取虚拟指标映射
     */
    private Map<String, String> getUpLocationMap(String tenantId) {
        List<StatFieldDO> virtualStatFieldList = statFieldDao.findStatFieldListByTenantId(tenantId);
        return virtualStatFieldList.stream().collect(Collectors.toMap(StatFieldDO::getFieldId, StatFieldDO::getDbFieldName, (item1, item2) -> item2));
    }

    /**
     * 根据上游企业列表获取所有下游企业和图的对应关系
     * @param upstreamTenantIds
     * @return
     */
    public Map<String, Set<String>> findAllDownStreamView(List<String> upstreamTenantIds) {
        Map<String, Set<String>> eiAndViewIds = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(upstreamTenantIds)) {
            upstreamTenantIds.forEach(tenantId -> {
                List<BiDataSyncPolicyDo> biDataSyncPolicyDoList = biDataSyncPolicyDao.queryBiDataSyncPolicyList(tenantId);
                if (!biDataSyncPolicyDoList.isEmpty()) {
                    biDataSyncPolicyDoList.forEach(biDataSyncPolicyDo -> {
                        Map<String, Set<String>> dsViewMapper = findDownStreamViewMapper(tenantId, biDataSyncPolicyDo);
                        if (!dsViewMapper.isEmpty()) {
                            dsViewMapper.forEach((k, v) -> eiAndViewIds.computeIfAbsent(k, key -> Sets.newHashSet())
                                                                       .addAll(v));
                        }
                    });
                }
            });
        }
        return eiAndViewIds;
    }

    private Map<String, Set<String>> findDownStreamViewMapper(String tenantId,BiDataSyncPolicyDo biDataSyncPolicyDo) {
        Map<String, Set<String>> eiAndViewIds = Maps.newHashMap();
        List<AggMappingRule> aggMappingRuleList = JSON.parseArray(biDataSyncPolicyDo.getAggMappingRule(), AggMappingRule.class);
        List<String> viewIds = aggMappingRuleList.stream().map(AggMappingRule::getViewId).toList();
        List<DataSourceEnterprise> dataSourceEnterpriseList = JSONObject.parseArray(biDataSyncPolicyDo.getDataSourceEnterprise(), DataSourceEnterprise.class);
        List<SourceTenantInfo> sourceTenantInfoList = enterpriseRelationRpcService.getSourceTenantToObject(dataSourceEnterpriseList, tenantId);
        if (!sourceTenantInfoList.isEmpty()) {
            sourceTenantInfoList.forEach(sourceTenantInfo -> eiAndViewIds.computeIfAbsent(sourceTenantInfo.getEi(), key -> Sets.newHashSet())
                                                                    .addAll(viewIds));
        }
        return eiAndViewIds;
    }

    /**
     * 检测该策略在topology_table中的状态，如果使用再策略中需要改为预计算状态
     * @param tenantId 租户
     * @param policyId 策略id
     */
    public void checkDataSyncPolicyViewStatus(String tenantId, String policyId) {
        BiDataSyncPolicyDo biDataSyncPolicyDo = biDataSyncPolicyDao.getBiDataSyncPolicyById(tenantId, policyId);
        if (biDataSyncPolicyDo != null) {
            Map<String, Set<String>> dsViewMapper = findDownStreamViewMapper(tenantId, biDataSyncPolicyDo);
            if (!dsViewMapper.isEmpty()) {
                dsViewMapper.forEach((ei, viewIds) -> {
                    if (CollectionUtils.isNotEmpty(viewIds)) {
                        Map<String, JSONObject> topologyTableMap = Maps.newHashMap();
                        List<Map<String, Object>> topologyTables = topologyTableService.batchQueryFieldLocations(ei, Lists.newArrayList(viewIds));
                        if (CollectionUtils.isNotEmpty(topologyTables)) {
                            topologyTables.stream()
                                          .map(JSONObject::new)
                                          .forEach(obj -> topologyTableMap.put(obj.getString("source_id"), obj));
                        }
                        viewIds.forEach(viewId -> {
                            JSONObject tpJSONObject = topologyTableMap.get(viewId);
                            if (tpJSONObject != null) {
                                Integer status = tpJSONObject.getInteger("status");
                                if (status != null && status == TopologyTableStatus.NONeedCal.getValue()) {
                                    topologyTableService.changeTopologyTable2PreparedByKey(ei, tpJSONObject.getString("stat_view_unique_key"));
                                    log.info("changeTopologyTable2PreparedByKey tenantId:{},uniqueKey:{},status:{}", ei, tpJSONObject.getString("stat_view_unique_key"), TopologyTableStatus.Prepared.getValue());
                                }
                            } else {
                                try {
                                    statTopologyService.doCreateTopology(ei, viewId, 0, TopologyTableStatus.Prepared.getValue());
                                } catch (Exception e) {
                                    log.error("doCreateTopology error: tenantId:{},viewId:{}", ei, viewId, e);
                                }
                            }
                        });
                    }
                });
            }
        }
    }

    public int getPolicySizeByTenantId(String tenantId){
        return biDataSyncPolicyDao.getPolicySizeByTenantId(tenantId);
    }
}
