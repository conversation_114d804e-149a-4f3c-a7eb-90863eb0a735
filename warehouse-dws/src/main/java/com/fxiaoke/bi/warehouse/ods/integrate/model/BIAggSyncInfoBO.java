package com.fxiaoke.bi.warehouse.ods.integrate.model;

import com.fxiaoke.bi.warehouse.common.db.entity.SyncInfo;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import lombok.Data;

/**
 * @Author:jief
 * @Date:2024/4/13
 */
@Data
public class BIAggSyncInfoBO extends SyncInfo {
  private String tenantId;
  private String downstreamEis;
  private int version;

  public static BIAggSyncInfoBO from(BIAggSyncInfoDO biAggSyncInfoDO) {
    BIAggSyncInfoBO biAggSyncInfoBO = new BIAggSyncInfoBO();
    biAggSyncInfoBO.setId(biAggSyncInfoDO.getId());
    biAggSyncInfoBO.setVersion(biAggSyncInfoBO.getVersion());
    biAggSyncInfoBO.setDownstreamEis(biAggSyncInfoDO.getDownstreamEis());
    biAggSyncInfoBO.setTenantId(biAggSyncInfoDO.getTenantId());
    biAggSyncInfoBO.setBatchNum(biAggSyncInfoDO.getBatchNum());
    biAggSyncInfoBO.setCreateTime(biAggSyncInfoDO.getCreateTime());
    biAggSyncInfoBO.setStatus(biAggSyncInfoDO.getStatus());
    biAggSyncInfoBO.setIsDeleted(biAggSyncInfoDO.getIsDeleted());
    biAggSyncInfoBO.setLastSyncTime(biAggSyncInfoDO.getLastSyncTime());
    biAggSyncInfoBO.setLastModifiedTime(biAggSyncInfoDO.getLastModifiedTime());
    return biAggSyncInfoBO;
  }

}
