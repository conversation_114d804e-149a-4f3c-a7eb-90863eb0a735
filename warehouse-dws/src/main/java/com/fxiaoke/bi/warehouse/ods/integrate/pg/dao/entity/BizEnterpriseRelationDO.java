package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity;

import lombok.Data;

import javax.persistence.Column;

/**
 * 企业上下游关系部分字段
 *
 * @Author:jief
 * @Date:2024/4/7
 */
@Data
public class BizEnterpriseRelationDO {
  @Column(name = "id")
  private String id;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "enterprise_account")
  private String enterpriseAccount;
  @Column(name = "mapper_account_id")
  private String mapperAccountId;
  @Column(name = "register_type")
  private String registerType;
  @Column(name = "has_fs_account")
  private Boolean hasFsAccount;
  @Column(name = "mapper_status")
  private String mapperStatus;
  @Column(name = "name")
  private String name;
  @Column(name = "owner")
  private String owner;
  @Column(name = "life_status")
  private String lifeStatus;
  @Column(name = "record_type")
  private String recordType;
  @Column(name = "created_by")
  private String createdBy;
  @Column(name = "is_deleted")
  private int isDeleted;
}
