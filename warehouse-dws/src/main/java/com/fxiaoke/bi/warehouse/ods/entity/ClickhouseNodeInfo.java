package com.fxiaoke.bi.warehouse.ods.entity;

import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ClickhouseNodeInfo {
  private String jdbcUrl;
  private String cluster;
  private String database;
  private String user;
  private String password;
  public static ClickhouseNodeInfo getInstance(String chURL) {
    return ClickhouseNodeInfo.builder()
                             .database(CHContext.getDBName(chURL))
                             .jdbcUrl(chURL)
                             .cluster("{cluster}")
                             .build();
  }
}
