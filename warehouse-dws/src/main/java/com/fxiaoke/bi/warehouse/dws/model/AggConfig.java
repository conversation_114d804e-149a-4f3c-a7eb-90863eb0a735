package com.fxiaoke.bi.warehouse.dws.model;

import com.fxiaoke.bi.warehouse.common.db.er.ColumnTypeConfig;
import com.fxiaoke.bi.warehouse.common.db.er.TableAliasNaming;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/3
 */
@Getter
public class AggConfig {
  private String tableName;
  private String tableAlias;
  private String columnName;
  private String dstColumnName;
  private ColumnTypeConfig dstColumnTypeConfig;
  private ColumnTypeConfig sourceColumnTypeConfig;
  private String baseConfig;

  public static AggConfig parse(String config, String timeZone) {
    List<String> split = Splitter.on(":").splitToList(config);
    String tableAliasAndColumnNameString = split.getFirst();
    List<String> tableAliasAndColumnName = Splitter.on(".").splitToList(tableAliasAndColumnNameString);
    AggConfig aggConfig = new AggConfig();
    aggConfig.tableName = TableAliasNaming.tableName(tableAliasAndColumnName.get(0));
    aggConfig.tableAlias = tableAliasAndColumnName.get(0);
    aggConfig.columnName = tableAliasAndColumnName.get(1);
    String aggType = split.get(1);
    if ("countdistinct".equalsIgnoreCase(aggType)) {
      aggType = "discount";
    }
    aggConfig.dstColumnTypeConfig = new ColumnTypeConfig(ImmutableMap.of("aggType", aggType,
            ColumnTypeConfig._ActionDate.TIMEZONE, timeZone));
    aggConfig.dstColumnName = split.get(2);
    aggConfig.sourceColumnTypeConfig = new ColumnTypeConfig(Map.of());
    aggConfig.baseConfig=config;
    return aggConfig;
  }
}
