package com.fxiaoke.bi.warehouse.dws.interceptor;

import com.facishare.converter.EIEAConverter;
import com.fxiaoke.bi.warehouse.common.util.TraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 拦截接口生成 trace
 *
 * <AUTHOR>
 * @createTime 2023/10/8 18:29
 */
@Slf4j
@Component
public class TraceInterceptor implements HandlerInterceptor {

  @Autowired
  private EIEAConverter eieaConverter;

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
    String tenantId = request.getHeader("X-fs-Enterprise-Id");
    String userId = request.getHeader("X-fs-Employee-Id");
    String ea = request.getHeader("X-fs-Enterprise-Account");
    String traceId = StringUtils.isNotBlank(request.getHeader("x-trace-id")) ?
      request.getHeader("x-trace-id") : request.getHeader("X-Trace-Id");
    if (StringUtils.isBlank(tenantId)) {
      String requestURI = request.getRequestURI();
      String method = request.getMethod();
      log.info("lack user info, request method:{}, request uri:{}", method, requestURI);
      return true;
    }
    try {
      if (StringUtils.isBlank(ea)) {
        ea = this.eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
      }
    } catch (Exception e) {
      log.error("enterpriseIdToAccount error tenantId:{}", tenantId, e);
    }
    TraceUtils.createTrace(tenantId, ea, traceId, userId);
    return true;
  }

  @Override
  public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
    TraceUtils.removeContext();
  }
}
