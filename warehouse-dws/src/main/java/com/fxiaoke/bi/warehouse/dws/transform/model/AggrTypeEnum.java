package com.fxiaoke.bi.warehouse.dws.transform.model;

/**
 * 后续需要和DataSetUtil统一处理
 */
public enum AggrTypeEnum {
  count("计数", "COUNT", 1), sum("合计", "SUM", 2), max("最大值", "MAX", 3), min("最小值", "MIN", 4), avg("平均值", "AVG", 5), countdistinct("唯一计数", "DISCOUNT", 6), countuniq("唯一计数2", "copy", 7);

  public static final String AGG_DISTINCT_COUNT = "count distinct";

  //成员变量
  private String desc;
  private String name;
  private int index;

  //构造方法
  AggrTypeEnum(String desc, String name, int index) {
    this.desc = desc;
    this.name = name;
    this.index = index;
  }

  public static String getNameByIndex(int index) {
    for (AggrTypeEnum aggrTypeEnum : AggrTypeEnum.values()) {
      if (aggrTypeEnum.getIndex() == index) {
        return aggrTypeEnum.getName();
      }
    }
    return null;
  }

  public static String getDescByIndex(int index) {
    for (AggrTypeEnum aggrTypeEnum : AggrTypeEnum.values()) {
      if (aggrTypeEnum.getIndex() == index) {
        return aggrTypeEnum.getDesc();
      }
    }
    return null;
  }

  // get set 方法
  public String getName() {
    return name;
  }

  //    public void setName(String name) {
  //        this.name = name;
  //    }

  public int getIndex() {
    return index;
  }

  //    public void setIndex(int index) {
  //        this.index = index;
  //    }

  public String getDesc() {
    return desc;
  }

  //    public void setDesc(String desc) {
  //        this.desc = desc;
  //    }

  public static AggrTypeEnum parseFromIndex(int index) {
    switch (index) {
      case 1:
        return count;
      case 2:
        return sum;
      case 3:
        return max;
      case 4:
        return min;
      case 5:
        return avg;
      case 6:
        return countdistinct;
    }
    return count;
  }
}


