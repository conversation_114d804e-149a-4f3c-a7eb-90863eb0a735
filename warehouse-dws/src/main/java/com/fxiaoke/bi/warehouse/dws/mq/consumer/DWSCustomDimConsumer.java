package com.fxiaoke.bi.warehouse.dws.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.dws.model.CustomDimRefreshEvent;
import com.fxiaoke.bi.warehouse.dws.transform.impl.OldStatViewTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.mq.CalculateEventProducer;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 自定义维度变更消费
 */
@Slf4j
@Component
public class DWSCustomDimConsumer implements MessageListenerConcurrently, ApplicationListener<ContextRefreshedEvent> {
  private AutoConfMQPushConsumer consumer;
  @Resource
  private OldStatViewTopologyTransformer oldStatViewTopologyTransformer;
  @Resource(name = "mybatisTenantPolicy")
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Resource
  private CalculateEventProducer calculateEventProducer;

  @PostConstruct
  public void init() {
    consumer = new AutoConfMQPushConsumer("fs-bi-warehouse", "DWSCustomDimConsumer", this);
  }

  @Override
  public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
    Set<CustomDimRefreshEvent> customDimRefreshEventSet = Sets.newHashSet();
    msgs.stream().filter(msg -> !Objects.equals("bill_board_Error", msg.getTags())).forEach(msg -> {
      String topic = msg.getTopic();
      // 自定义维度
      CustomDimRefreshEvent customDimRefreshEvent = JSON.parseObject(msg.getBody(), CustomDimRefreshEvent.class);
      log.info("DWSCustomDimConsumer get msg topic:{},msgId:{},tag:{},queue:{},msgBody:{}", topic, msg.getMsgId(), msg.getTags(), msg.getQueueId(), new String(msg.getBody(), StandardCharsets.UTF_8));
      if (Objects.equals("custom-dim-refresh-nomon-biz", msg.getTags())) {
        if (Utils.useGrayTopic( customDimRefreshEvent.getTenantId(),mybatisTenantPolicy.getPgDbNameByTenantId(customDimRefreshEvent.getTenantId())) && !Objects.equals(topic, CHContext.BI_CUSTOM_DIM_MESSAGE_GRAY)) {
          msg.setTopic(CHContext.BI_CUSTOM_DIM_MESSAGE_GRAY);
          calculateEventProducer.sendMessage(msg, msg.getQueueId());
          log.info("DWSCustomDimConsumer this tenantId:{},is gray to topic:{}", customDimRefreshEvent.getTenantId(), CHContext.BI_CUSTOM_DIM_MESSAGE_GRAY);
          return;
        }
        if (GrayManager.isAllowByRule("use_ch_agg", customDimRefreshEvent.getTenantId()) ||
            GrayManager.isAllowByRule("use_ch_agg_pgdb", mybatisTenantPolicy.getPgDbNameByTenantId(customDimRefreshEvent.getTenantId()))) {
          customDimRefreshEventSet.add(customDimRefreshEvent);
        }
      }
    });
    try {
      oldStatViewTopologyTransformer.batchOnChangeCustomDimTheme(customDimRefreshEventSet);
    } catch (Exception e) {
      log.error("batchOnChangeCustomDimTheme error customDimEvent:{}", JSON.toJSONString(customDimRefreshEventSet), e);
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }

  @PreDestroy
  public void destroy() {
    if (null != consumer) {
      consumer.shutdown();
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (null == event.getApplicationContext().getParent() && consumer != null) {
      consumer.start();
    }
  }
}
