package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.api.CRMNotifyService;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.FileInfo;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.PolicyLogEnum;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.BizEnterpriseRelationDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BizEnterpriseRelationDO;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.fxiaoke.model.RemindRecordItem;
import com.fxiaoke.model.crmNotify.AddRemindRecordArg;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.fxiaoke.constant.CrmCategoryKey.BiReportMessage;


@Slf4j
@Service
public class RptMessageService {

    @Resource
    private BizConfClient bizConfClient;

    @Resource(name = "cRMNotifyService")
    private CRMNotifyService crmNotifyService;

    @Resource
    private BizEnterpriseRelationDao bizEnterpriseRelationDao;

    @Resource
    private EnterpriseRelationRpcService enterpriseRelationRpcService;

    @Resource
    private EIEAConverter eieaConverter;

    /**
     * 推送报表消息
     */
    public void pushRptMessage(Integer ei, List<Integer> userIds, String policyName, String logType, String msg) {
        String RptPushStatusFlag = queryTargetTenantRptPushStatus(String.valueOf(ei));
        if (StringUtils.isBlank(RptPushStatusFlag) || StringUtils.equals(RptPushStatusFlag, "0")) {
            return;
        }
        PolicyLogEnum policyLogEnum = PolicyLogEnum.getPolicyLogEnumByLogType(logType);
        try {
            RemindRecordItem item = new RemindRecordItem();
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            item.setCategory(BiReportMessage);
            item.setType(140);
            item.setSourceId(uuid);
            item.setUrlType(1);
            item.setReceiverIDs(userIds);
            item.setAppId("");
            item.setRemindSender(true);
            item.setTitle("1+N聚合数据同步服务");//IgnoreI18n
            item.setFullContent("同步日志");//IgnoreI18n
            FileInfo fileInfo = new FileInfo(policyName, "keyDataSync",
              policyLogEnum.getPushRptDesc() + ": " + msg, new Date());
            item.setUrlParameter(new HashMap<String, String>(2) {
                {
                    put("fileInfo", JSON.toJSONString(fileInfo));
                }
            });
            AddRemindRecordArg addRemindRecordArg = new AddRemindRecordArg();
            addRemindRecordArg.setRemindRecordItem(item);
            addRemindRecordArg.setEi(ei);
            addRemindRecordArg.setUuid(uuid);
            crmNotifyService.addRemindRecord(addRemindRecordArg);
        } catch (Exception e) {
            log.error("1+N push report message error this userIds is {}", userIds, e);
        }
    }

    /**
     * 查询1端是否开启报表推送
     */
    public String queryTargetTenantRptPushStatus(String tenantId) {
        try {
            QueryConfigByRankArg queryArg = new QueryConfigByRankArg();
            queryArg.setKey("bi_sync_policy_rpt_push");
            queryArg.setRank(Rank.TENANT);
            queryArg.setTenantId(tenantId);
            queryArg.setPkg("CRM");
            return bizConfClient.queryConfigByRank(queryArg);
        } catch (Exception e) {
            log.error("querySourceTenantSyncAllowSwitch error this tenantId is {}", tenantId, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 查询下游开启数据同步状态
     */
    public boolean querySourceTenantSyncAllowSwitch(String tenantId, String targetTenantId) {
        try {
            QueryConfigByRankArg queryArg = new QueryConfigByRankArg();
            queryArg.setKey("bi_sync_policy_switch");
            queryArg.setRank(Rank.TENANT);
            queryArg.setTenantId(tenantId);
            queryArg.setPkg("CRM");
            String allowSwitchFlag = bizConfClient.queryConfigByRank(queryArg);
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
            Map<String, Long> outTenantIdsByEas = enterpriseRelationRpcService.getOutTenantIdsByEas(Lists.newArrayList(ea), targetTenantId);
            Long outTenantId = outTenantIdsByEas.get(ea);
            BizEnterpriseRelationDO bizEnterpriseRelationDO = bizEnterpriseRelationDao.queryBizEnterpriseRelationById(String.valueOf(outTenantId), targetTenantId);
            return StringUtils.isBlank(allowSwitchFlag) ? StringUtils.equals("1", bizEnterpriseRelationDO.getRegisterType()) : StringUtils.equals("1", allowSwitchFlag);
        } catch (Exception e) {
            log.error("querySourceTenantSyncAllowSwitch error this tenantId is {}", tenantId, e);
            return false;
        }
    }

}
