package com.fxiaoke.bi.warehouse.ods.utils;

/**
 * @Author:jief
 * @Date:2023/11/11
 */
public class InitSQL {
  public static final String aggDataSQL = """
    CREATE TABLE IF NOT EXISTS %s ON CLUSTER '{cluster}'
    (
        `tenant_id` String,
        `view_id` String,
        `view_version` UInt32,
        `hash_code` UInt64,
    	  `hash_code_without_date` UInt64,
        `object_id` String,
        `action_date` LowCardinality(String),
        `owner` String,
        `out_owner` String,
        `life_status` LowCardinality(String),
        `create_time` String,
        `last_modified_time` String,
        `data_auth_code` String,
        `out_tenant_id` String,
        `out_data_auth_code` String,
        `out_data_own_organization` String,
        `out_data_own_department` String,
        `data_own_department` String,
        `data_own_organization` String,
        `dim_string_1` String,
        `dim_string_2` String,
        `dim_string_3` String,
        `dim_string_4` String,
        `dim_string_5` String,
        `dim_string_6` String,
        `dim_string_7` String,
        `dim_string_8` String,
        `dim_string_9` String,
        `dim_string_10` String,
        `dim_string_11` String,
        `dim_string_12` String,
        `dim_string_13` String,
        `dim_string_14` String,
        `dim_string_15` String,
        `dim_string_16` String,
        `dim_string_17` String,
        `dim_string_18` String,
        `dim_string_19` String,
        `dim_string_20` String,
        `dim_string_21` String,
        `dim_string_22` String,
        `dim_string_23` String,
        `dim_string_24` String,
        `dim_string_25` String,
        `dim_string_26` String,
        `dim_string_27` String,
        `dim_string_28` String,
        `dim_string_29` String,
        `dim_string_30` String,
        `dim_string_31` String,
        `dim_string_32` String,
        `dim_string_33` String,
        `dim_string_34` String,
        `dim_string_35` String,
        `dim_string_36` String,
        `dim_string_37` String,
        `dim_string_38` String,
        `dim_string_39` String,
        `dim_string_40` String,
        `dim_string_41` String,
        `dim_string_42` String,
        `dim_string_43` String,
        `dim_string_44` String,
        `dim_string_45` String,
        `dim_string_46` String,
        `dim_string_47` String,
        `dim_string_48` String,
        `dim_string_49` String,
        `dim_string_50` String,
        `dim_array_string_1` Array(String),
        `dim_array_string_2` Array(String),
        `dim_array_string_3` Array(String),
        `dim_array_string_4` Array(String),
        `dim_array_string_5` Array(String),
        `dim_array_string_6` Array(String),
        `dim_array_string_7` Array(String),
        `dim_array_string_8` Array(String),
        `dim_array_string_9` Array(String),
        `dim_array_string_10` Array(String),
        `dim_int_1` Nullable(Int64),
        `dim_int_2` Nullable(Int64),
        `dim_int_3` Nullable(Int64),
        `dim_int_4` Nullable(Int64),
        `dim_int_5` Nullable(Int64),
        `dim_int_6` Nullable(Int64),
        `dim_int_7` Nullable(Int64),
        `dim_int_8` Nullable(Int64),
        `dim_int_9` Nullable(Int64),
        `dim_int_10` Nullable(Int64),
        `dim_int_11` Nullable(Int64),
        `dim_int_12` Nullable(Int64),
        `dim_int_13` Nullable(Int64),
        `dim_int_14` Nullable(Int64),
        `dim_int_15` Nullable(Int64),
        `dim_int_16` Nullable(Int64),
        `dim_int_17` Nullable(Int64),
        `dim_int_18` Nullable(Int64),
        `dim_int_19` Nullable(Int64),
        `dim_int_20` Nullable(Int64),
        `dim_int_21` Nullable(Int64),
        `dim_int_22` Nullable(Int64),
        `dim_int_23` Nullable(Int64),
        `dim_int_24` Nullable(Int64),
        `dim_int_25` Nullable(Int64),
        `dim_int_26` Nullable(Int64),
        `dim_int_27` Nullable(Int64),
        `dim_int_28` Nullable(Int64),
        `dim_int_29` Nullable(Int64),
        `dim_int_30` Nullable(Int64),
        `dim_decimal_1` Nullable(Decimal(38, 22)),
        `dim_decimal_2` Nullable(Decimal(38, 22)),
        `dim_decimal_3` Nullable(Decimal(38, 22)),
        `dim_decimal_4` Nullable(Decimal(38, 22)),
        `dim_decimal_5` Nullable(Decimal(38, 22)),
        `dim_decimal_6` Nullable(Decimal(38, 22)),
        `dim_decimal_7` Nullable(Decimal(38, 22)),
        `dim_decimal_8` Nullable(Decimal(38, 22)),
        `dim_decimal_9` Nullable(Decimal(38, 22)),
        `dim_decimal_10` Nullable(Decimal(38, 22)),
        `dim_decimal_11` Nullable(Decimal(38, 22)),
        `dim_decimal_12` Nullable(Decimal(38, 22)),
        `dim_decimal_13` Nullable(Decimal(38, 22)),
        `dim_decimal_14` Nullable(Decimal(38, 22)),
        `dim_decimal_15` Nullable(Decimal(38, 22)),
        `dim_decimal_16` Nullable(Decimal(38, 22)),
        `dim_decimal_17` Nullable(Decimal(38, 22)),
        `dim_decimal_18` Nullable(Decimal(38, 22)),
        `dim_decimal_19` Nullable(Decimal(38, 22)),
        `dim_decimal_20` Nullable(Decimal(38, 22)),
        `dim_decimal_21` Nullable(Decimal(38, 22)),
        `dim_decimal_22` Nullable(Decimal(38, 22)),
        `dim_decimal_23` Nullable(Decimal(38, 22)),
        `dim_decimal_24` Nullable(Decimal(38, 22)),
        `dim_decimal_25` Nullable(Decimal(38, 22)),
        `dim_decimal_26` Nullable(Decimal(38, 22)),
        `dim_decimal_27` Nullable(Decimal(38, 22)),
        `dim_decimal_28` Nullable(Decimal(38, 22)),
        `dim_decimal_29` Nullable(Decimal(38, 22)),
        `dim_decimal_30` Nullable(Decimal(38, 22)),
        `dim_boolean_1` Nullable(Bool),
        `dim_boolean_2` Nullable(Bool),
        `dim_boolean_3` Nullable(Bool),
        `dim_boolean_4` Nullable(Bool),
        `dim_boolean_5` Nullable(Bool),
        `dim_boolean_6` Nullable(Bool),
        `dim_boolean_7` Nullable(Bool),
        `dim_boolean_8` Nullable(Bool),
        `dim_boolean_9` Nullable(Bool),
        `dim_boolean_10` Nullable(Bool),
        `agg_uniq_tag_1` String,
        `agg_uniq_tag_2` String,
        `agg_uniq_tag_3` String,
        `agg_uniq_tag_4` String,
        `agg_uniq_tag_5` String,
        `agg_count_1` Nullable(Int64),
        `agg_count_2` Nullable(Int64),
        `agg_count_3` Nullable(Int64),
        `agg_count_4` Nullable(Int64),
        `agg_count_5` Nullable(Int64),
        `agg_count_6` Nullable(Int64),
        `agg_count_7` Nullable(Int64),
        `agg_count_8` Nullable(Int64),
        `agg_count_9` Nullable(Int64),
        `agg_count_10` Nullable(Int64),
        `agg_count_11` Nullable(Int64),
        `agg_count_12` Nullable(Int64),
        `agg_count_13` Nullable(Int64),
        `agg_count_14` Nullable(Int64),
        `agg_count_15` Nullable(Int64),
        `agg_count_16` Nullable(Int64),
        `agg_count_17` Nullable(Int64),
        `agg_count_18` Nullable(Int64),
        `agg_count_19` Nullable(Int64),
        `agg_count_20` Nullable(Int64),
        `agg_count_21` Nullable(Int64),
        `agg_count_22` Nullable(Int64),
        `agg_count_23` Nullable(Int64),
        `agg_count_24` Nullable(Int64),
        `agg_count_25` Nullable(Int64),
        `agg_count_26` Nullable(Int64),
        `agg_count_27` Nullable(Int64),
        `agg_count_28` Nullable(Int64),
        `agg_count_29` Nullable(Int64),
        `agg_count_30` Nullable(Int64),
        `agg_sum_1` Nullable(Decimal(38, 20)),
        `agg_sum_1_merge` Nullable(UInt8),
        `agg_sum_2` Nullable(Decimal(38, 20)),
        `agg_sum_2_merge` Nullable(UInt8),
        `agg_sum_3` Nullable(Decimal(38, 20)),
        `agg_sum_3_merge` Nullable(UInt8),
        `agg_sum_4` Nullable(Decimal(38, 20)),
        `agg_sum_4_merge` Nullable(UInt8),
        `agg_sum_5` Nullable(Decimal(38, 20)),
        `agg_sum_5_merge` Nullable(UInt8),
        `agg_sum_6` Nullable(Decimal(38, 20)),
        `agg_sum_6_merge` Nullable(UInt8),
        `agg_sum_7` Nullable(Decimal(38, 20)),
        `agg_sum_7_merge` Nullable(UInt8),
        `agg_sum_8` Nullable(Decimal(38, 20)),
        `agg_sum_8_merge` Nullable(UInt8),
        `agg_sum_9` Nullable(Decimal(38, 20)),
        `agg_sum_9_merge` Nullable(UInt8),
        `agg_sum_10` Nullable(Decimal(38, 20)),
        `agg_sum_10_merge` Nullable(UInt8),
        `agg_sum_11` Nullable(Decimal(38, 20)),
        `agg_sum_11_merge` Nullable(UInt8),
        `agg_sum_12` Nullable(Decimal(38, 20)),
        `agg_sum_12_merge` Nullable(UInt8),
        `agg_sum_13` Nullable(Decimal(38, 20)),
        `agg_sum_13_merge` Nullable(UInt8),
        `agg_sum_14` Nullable(Decimal(38, 20)),
        `agg_sum_14_merge` Nullable(UInt8),
        `agg_sum_15` Nullable(Decimal(38, 20)),
        `agg_sum_15_merge` Nullable(UInt8),
        `agg_sum_16` Nullable(Decimal(38, 20)),
        `agg_sum_16_merge` Nullable(UInt8),
        `agg_sum_17` Nullable(Decimal(38, 20)),
        `agg_sum_17_merge` Nullable(UInt8),
        `agg_sum_18` Nullable(Decimal(38, 20)),
        `agg_sum_18_merge` Nullable(UInt8),
        `agg_sum_19` Nullable(Decimal(38, 20)),
        `agg_sum_19_merge` Nullable(UInt8),
        `agg_sum_20` Nullable(Decimal(38, 20)),
        `agg_sum_20_merge` Nullable(UInt8),
        `agg_sum_21` Nullable(Decimal(38, 20)),
        `agg_sum_21_merge` Nullable(UInt8),
        `agg_sum_22` Nullable(Decimal(38, 20)),
        `agg_sum_22_merge` Nullable(UInt8),
        `agg_sum_23` Nullable(Decimal(38, 20)),
        `agg_sum_23_merge` Nullable(UInt8),
        `agg_sum_24` Nullable(Decimal(38, 20)),
        `agg_sum_24_merge` Nullable(UInt8),
        `agg_sum_25` Nullable(Decimal(38, 20)),
        `agg_sum_25_merge` Nullable(UInt8),
        `agg_sum_26` Nullable(Decimal(38, 20)),
        `agg_sum_26_merge` Nullable(UInt8),
        `agg_sum_27` Nullable(Decimal(38, 20)),
        `agg_sum_27_merge` Nullable(UInt8),
        `agg_sum_28` Nullable(Decimal(38, 20)),
        `agg_sum_28_merge` Nullable(UInt8),
        `agg_sum_29` Nullable(Decimal(38, 20)),
        `agg_sum_29_merge` Nullable(UInt8),
        `agg_sum_30` Nullable(Decimal(38, 20)),
        `agg_sum_30_merge` Nullable(UInt8),
        `agg_uniq_1` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_1_merge` UInt8,
        `agg_uniq_2` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_2_merge` UInt8,
        `agg_uniq_3` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_3_merge` UInt8,
        `agg_uniq_4` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_4_merge` UInt8,
        `agg_uniq_5` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_5_merge` UInt8,
        `agg_uniq_6` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_6_merge` UInt8,
        `agg_uniq_7` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_7_merge` UInt8,
        `agg_uniq_8` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_8_merge` UInt8,
        `agg_uniq_9` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_9_merge` UInt8,
        `agg_uniq_10` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_10_merge` UInt8,
        `agg_uniq_11` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_11_merge` UInt8,
        `agg_uniq_12` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_12_merge` UInt8,
        `agg_uniq_13` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_13_merge` UInt8,
        `agg_uniq_14` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_14_merge` UInt8,
        `agg_uniq_15` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_15_merge` UInt8,
        `agg_uniq_16` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_16_merge` UInt8,
        `agg_uniq_17` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_17_merge` UInt8,
        `agg_uniq_18` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_18_merge` UInt8,
        `agg_uniq_19` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_19_merge` UInt8,
        `agg_uniq_20` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_20_merge` UInt8,
        `agg_uniq_21` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_21_merge` UInt8,
        `agg_uniq_22` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_22_merge` UInt8,
        `agg_uniq_23` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_23_merge` UInt8,
        `agg_uniq_24` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_24_merge` UInt8,
        `agg_uniq_25` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_25_merge` UInt8,
        `agg_uniq_26` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_26_merge` UInt8,
        `agg_uniq_27` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_27_merge` UInt8,
        `agg_uniq_28` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_28_merge` UInt8,
        `agg_uniq_29` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_29_merge` UInt8,
        `agg_uniq_30` AggregateFunction(uniqExact, Nullable(String)),
        `agg_uniq_30_merge` UInt8,
        `timestamp` DateTime64(9) default now64(9),
        `sys_modified_time` DateTime64(9) DEFAULT now64(9),
        `value_slot` String,
        `rule_id` String,
        `batch_num` UInt64,
        `is_deleted` UInt8,
    )
    ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', timestamp, is_deleted)
    PARTITION BY toYYYYMMDD(sys_modified_time)
    PRIMARY KEY (tenant_id, view_id, view_version, hash_code, value_slot)
    ORDER BY (tenant_id, view_id, view_version, hash_code, value_slot)
    SETTINGS  index_granularity = 8192;
    """;

  public static final String mtDataTagVSQL = """
    CREATE OR REPLACE VIEW bi_mt_data_tag_v ON CLUSTER '{cluster}'
                (
                 `data_id` String,
                 `object_describe_api_name` String,
                 `tenant_id` String,
                 `tag_range` Array(String),
                 `sub_tag_id_arr` Array(String),
                 `tag_id_arr` Array(String),
                 `mdtu_bi_sys_batch_id` Int64,
                 `mst_bi_sys_batch_id` Nullable(Int64),
                 `mtt_bi_sys_batch_id` Nullable(Int64),
                 `mdtu_bi_sys_flag` Int8,
                 `mst_bi_sys_flag` Nullable(Int8),
                 `mtt_bi_sys_flag` Nullable(Int8),
                 `is_deleted` Int16
                ) AS
    SELECT
        mdtu.data_id AS data_id,
        mdtu.object_describe_api_name AS object_describe_api_name,
        mdtu.tenant_id AS tenant_id,
        if(mtt.is_all, ['ALL'], coalesce(mtt.tag_range, [''])) AS tag_range,
        groupArray(mst.id) AS sub_tag_id_arr,
        groupArray(mst.tag_id) AS tag_id_arr,
        mdtu.bi_sys_batch_id AS mdtu_bi_sys_batch_id,
        mst.bi_sys_batch_id AS mst_bi_sys_batch_id,
        mtt.bi_sys_batch_id AS mtt_bi_sys_batch_id,
        mdtu.bi_sys_flag AS mdtu_bi_sys_flag,
        mst.bi_sys_flag AS mst_bi_sys_flag,
        mtt.bi_sys_flag AS mtt_bi_sys_flag,
        mdtu.is_deleted AS is_deleted
    FROM
        (
            SELECT
                tenant_id,
                data_id,
                object_describe_api_name,
                value1,
                bi_sys_flag,
                bi_sys_batch_id,
                is_deleted
            FROM mt_data_tag
                     LEFT ARRAY JOIN value1 PREWHERE bi_sys_ods_part in('s','c')
            WHERE (tenant_id = {tenantId:String}) AND (object_describe_api_name = {apiName:String})
            ) AS mdtu
            LEFT JOIN (select tenant_id,id,tag_id,bi_sys_batch_id,bi_sys_flag,num_key,is_deleted from mt_sub_tag PREWHERE bi_sys_ods_part in('s','c')) AS mst ON (mdtu.tenant_id = mst.tenant_id) AND (mdtu.value1 = mst.num_key)
            LEFT JOIN (select tenant_id,tag_id,is_all,tag_range,bi_sys_batch_id,bi_sys_flag,is_deleted from mt_tag PREWHERE bi_sys_ods_part in('s','c')) AS mtt ON (mst.tenant_id = mtt.tenant_id) AND (mtt.tag_id = mst.tag_id)
    WHERE (mst.is_deleted = false) AND (mtt.is_deleted = false)
    GROUP BY
        mdtu.data_id,
        mdtu.tenant_id,
        mdtu.object_describe_api_name,
        mtt.is_all,
        mtt.tag_range,
        mdtu.is_deleted,
        mdtu.bi_sys_batch_id,
        mst.bi_sys_batch_id,
        mtt.bi_sys_batch_id,
        mdtu.bi_sys_flag,
        mst.bi_sys_flag,
        mtt.bi_sys_flag
        SETTINGS final = 1, optimize_move_to_prewhere_if_final = 1, join_use_nulls = 1;
          """;

  public static final String mtDataTagVSQL2 = """
    CREATE OR REPLACE VIEW bi_mt_data_tag_v ON CLUSTER '{cluster}'
                (
                 `data_id` String,
                 `object_describe_api_name` String,
                 `tenant_id` String,
                 `tag_range` Array(String),
                 `sub_tag_id_arr` Array(String),
                 `tag_id_arr` Array(String),
                 `bi_sys_batch_id` Int64,
                 `bi_sys_flag` Int8,
                 `is_deleted` Int16
                ) AS
    SELECT
        mdtu.data_id AS data_id,
        mdtu.object_describe_api_name AS object_describe_api_name,
        mdtu.tenant_id AS tenant_id,
        if(mtt.is_all, ['ALL'], coalesce(mtt.tag_range, [''])) AS tag_range,
        groupArray(mst.id) AS sub_tag_id_arr,
        groupArray(mst.tag_id) AS tag_id_arr,
        max(greatest(ifNull(mdtu.bi_sys_batch_id,0),ifNull(mst.bi_sys_batch_id,0),ifNull(mtt.bi_sys_batch_id,0))) as bi_sys_batch_id,
        max(least(ifNull(mdtu.bi_sys_flag,1),ifNull(mst.bi_sys_flag,1),ifNull(mtt.bi_sys_flag,1))) as bi_sys_flag,
        mdtu.is_deleted AS is_deleted
    FROM
        (
            SELECT
                tenant_id,
                data_id,
                object_describe_api_name,
                value1,
                bi_sys_flag,
                bi_sys_batch_id,
                is_deleted
            FROM mt_data_tag
                     LEFT ARRAY JOIN value1 PREWHERE bi_sys_ods_part in('s','c')
            WHERE (tenant_id = {tenantId:String}) AND (object_describe_api_name = {apiName:String})
            ) AS mdtu
            LEFT JOIN (select tenant_id,id,tag_id,bi_sys_batch_id,bi_sys_flag,num_key,is_deleted from mt_sub_tag PREWHERE bi_sys_ods_part in('s','c')) AS mst ON (mdtu.tenant_id = mst.tenant_id) AND (mdtu.value1 = mst.num_key)
            LEFT JOIN (select tenant_id,tag_id,is_all,tag_range,bi_sys_batch_id,bi_sys_flag,is_deleted from mt_tag PREWHERE bi_sys_ods_part in('s','c')) AS mtt ON (mst.tenant_id = mtt.tenant_id) AND (mtt.tag_id = mst.tag_id)
    WHERE (mst.is_deleted = false) AND (mtt.is_deleted = false)
    GROUP BY
        mdtu.data_id,
        mdtu.tenant_id,
        mdtu.object_describe_api_name,
        mtt.is_all,
        mtt.tag_range,
        mdtu.is_deleted
        SETTINGS final = 1, optimize_move_to_prewhere_if_final = 1, join_use_nulls = 1;
          """;

  public static final String agg_downstream_data= """
    CREATE TABLE IF NOT EXISTS  agg_downstream_data on cluster '{cluster}'
     (
         `tenant_id` String,
         `policy_id` String,
         `view_id` String,
         `view_version` UInt32,
         `hash_code` UInt64,
         `hash_code_without_date` UInt64,
         `object_id` String,
         `action_date` LowCardinality(String),
         `owner` String,
         `out_owner` String,
         `life_status` LowCardinality(String),
         `create_time` String,
         `last_modified_time` String,
         `data_auth_code` String,
         `out_tenant_id` String,
         `out_data_auth_code` String,
         `out_data_own_organization` String,
         `out_data_own_department` String,
         `data_own_department` String,
         `data_own_organization` String,
         `ds_dim_string_1` String,
         `ds_dim_string_2` String,
         `ds_dim_string_3` String,
         `ds_dim_string_4` String,
         `ds_dim_string_5` String,
         `ds_dim_string_6` String,
         `ds_dim_string_7` String,
         `ds_dim_string_8` String,
         `ds_dim_string_9` String,
         `ds_dim_string_10` String,
         `ds_dim_string_11` String,
         `ds_dim_string_12` String,
         `ds_dim_string_13` String,
         `ds_dim_string_14` String,
         `ds_dim_string_15` String,
         `ds_dim_string_16` String,
         `ds_dim_string_17` String,
         `ds_dim_string_18` String,
         `ds_dim_string_19` String,
         `ds_dim_string_20` String,
         `ds_dim_string_21` String,
         `ds_dim_string_22` String,
         `ds_dim_string_23` String,
         `ds_dim_string_24` String,
         `ds_dim_string_25` String,
         `ds_dim_string_26` String,
         `ds_dim_string_27` String,
         `ds_dim_string_28` String,
         `ds_dim_string_29` String,
         `ds_dim_string_30` String,
         `ds_dim_string_31` String,
         `ds_dim_string_32` String,
         `ds_dim_string_33` String,
         `ds_dim_string_34` String,
         `ds_dim_string_35` String,
         `ds_dim_string_36` String,
         `ds_dim_string_37` String,
         `ds_dim_string_38` String,
         `ds_dim_string_39` String,
         `ds_dim_string_40` String,
         `ds_dim_string_41` String,
         `ds_dim_string_42` String,
         `ds_dim_string_43` String,
         `ds_dim_string_44` String,
         `ds_dim_string_45` String,
         `ds_dim_string_46` String,
         `ds_dim_string_47` String,
         `ds_dim_string_48` String,
         `ds_dim_string_49` String,
         `ds_dim_string_50` String,
         `ds_dim_array_string_1` Array(String),
         `ds_dim_array_string_2` Array(String),
         `ds_dim_array_string_3` Array(String),
         `ds_dim_array_string_4` Array(String),
         `ds_dim_array_string_5` Array(String),
         `ds_dim_array_string_6` Array(String),
         `ds_dim_array_string_7` Array(String),
         `ds_dim_array_string_8` Array(String),
         `ds_dim_array_string_9` Array(String),
         `ds_dim_array_string_10` Array(String),
         `ds_dim_int_1` Nullable(Int64),
         `ds_dim_int_2` Nullable(Int64),
         `ds_dim_int_3` Nullable(Int64),
         `ds_dim_int_4` Nullable(Int64),
         `ds_dim_int_5` Nullable(Int64),
         `ds_dim_int_6` Nullable(Int64),
         `ds_dim_int_7` Nullable(Int64),
         `ds_dim_int_8` Nullable(Int64),
         `ds_dim_int_9` Nullable(Int64),
         `ds_dim_int_10` Nullable(Int64),
         `ds_dim_int_11` Nullable(Int64),
         `ds_dim_int_12` Nullable(Int64),
         `ds_dim_int_13` Nullable(Int64),
         `ds_dim_int_14` Nullable(Int64),
         `ds_dim_int_15` Nullable(Int64),
         `ds_dim_int_16` Nullable(Int64),
         `ds_dim_int_17` Nullable(Int64),
         `ds_dim_int_18` Nullable(Int64),
         `ds_dim_int_19` Nullable(Int64),
         `ds_dim_int_20` Nullable(Int64),
         `ds_dim_int_21` Nullable(Int64),
         `ds_dim_int_22` Nullable(Int64),
         `ds_dim_int_23` Nullable(Int64),
         `ds_dim_int_24` Nullable(Int64),
         `ds_dim_int_25` Nullable(Int64),
         `ds_dim_int_26` Nullable(Int64),
         `ds_dim_int_27` Nullable(Int64),
         `ds_dim_int_28` Nullable(Int64),
         `ds_dim_int_29` Nullable(Int64),
         `ds_dim_int_30` Nullable(Int64),
         `ds_dim_decimal_1` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_2` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_3` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_4` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_5` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_6` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_7` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_8` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_9` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_10` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_11` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_12` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_13` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_14` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_15` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_16` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_17` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_18` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_19` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_20` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_21` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_22` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_23` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_24` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_25` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_26` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_27` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_28` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_29` Nullable(Decimal(38, 22)),
         `ds_dim_decimal_30` Nullable(Decimal(38, 22)),
         `ds_dim_boolean_1` Nullable(Bool),
         `ds_dim_boolean_2` Nullable(Bool),
         `ds_dim_boolean_3` Nullable(Bool),
         `ds_dim_boolean_4` Nullable(Bool),
         `ds_dim_boolean_5` Nullable(Bool),
         `ds_dim_boolean_6` Nullable(Bool),
         `ds_dim_boolean_7` Nullable(Bool),
         `ds_dim_boolean_8` Nullable(Bool),
         `ds_dim_boolean_9` Nullable(Bool),
         `ds_dim_boolean_10` Nullable(Bool),
         `ds_agg_uniq_tag_1` String,
         `ds_agg_count_1` Nullable(Int64),
         `ds_agg_count_2` Nullable(Int64),
         `ds_agg_count_3` Nullable(Int64),
         `ds_agg_count_4` Nullable(Int64),
         `ds_agg_count_5` Nullable(Int64),
         `ds_agg_count_6` Nullable(Int64),
         `ds_agg_count_7` Nullable(Int64),
         `ds_agg_count_8` Nullable(Int64),
         `ds_agg_count_9` Nullable(Int64),
         `ds_agg_count_10` Nullable(Int64),
         `ds_agg_count_11` Nullable(Int64),
         `ds_agg_count_12` Nullable(Int64),
         `ds_agg_count_13` Nullable(Int64),
         `ds_agg_count_14` Nullable(Int64),
         `ds_agg_count_15` Nullable(Int64),
         `ds_agg_count_16` Nullable(Int64),
         `ds_agg_count_17` Nullable(Int64),
         `ds_agg_count_18` Nullable(Int64),
         `ds_agg_count_19` Nullable(Int64),
         `ds_agg_count_20` Nullable(Int64),
         `ds_agg_count_21` Nullable(Int64),
         `ds_agg_count_22` Nullable(Int64),
         `ds_agg_count_23` Nullable(Int64),
         `ds_agg_count_24` Nullable(Int64),
         `ds_agg_count_25` Nullable(Int64),
         `ds_agg_count_26` Nullable(Int64),
         `ds_agg_count_27` Nullable(Int64),
         `ds_agg_count_28` Nullable(Int64),
         `ds_agg_count_29` Nullable(Int64),
         `ds_agg_count_30` Nullable(Int64),
         `ds_agg_sum_1` Nullable(Decimal(38, 20)),
         `ds_agg_sum_1_merge` Nullable(UInt8),
         `ds_agg_sum_2` Nullable(Decimal(38, 20)),
         `ds_agg_sum_2_merge` Nullable(UInt8),
         `ds_agg_sum_3` Nullable(Decimal(38, 20)),
         `ds_agg_sum_3_merge` Nullable(UInt8),
         `ds_agg_sum_4` Nullable(Decimal(38, 20)),
         `ds_agg_sum_4_merge` Nullable(UInt8),
         `ds_agg_sum_5` Nullable(Decimal(38, 20)),
         `ds_agg_sum_5_merge` Nullable(UInt8),
         `ds_agg_sum_6` Nullable(Decimal(38, 20)),
         `ds_agg_sum_6_merge` Nullable(UInt8),
         `ds_agg_sum_7` Nullable(Decimal(38, 20)),
         `ds_agg_sum_7_merge` Nullable(UInt8),
         `ds_agg_sum_8` Nullable(Decimal(38, 20)),
         `ds_agg_sum_8_merge` Nullable(UInt8),
         `ds_agg_sum_9` Nullable(Decimal(38, 20)),
         `ds_agg_sum_9_merge` Nullable(UInt8),
         `ds_agg_sum_10` Nullable(Decimal(38, 20)),
         `ds_agg_sum_10_merge` Nullable(UInt8),
         `ds_agg_sum_11` Nullable(Decimal(38, 20)),
         `ds_agg_sum_11_merge` Nullable(UInt8),
         `ds_agg_sum_12` Nullable(Decimal(38, 20)),
         `ds_agg_sum_12_merge` Nullable(UInt8),
         `ds_agg_sum_13` Nullable(Decimal(38, 20)),
         `ds_agg_sum_13_merge` Nullable(UInt8),
         `ds_agg_sum_14` Nullable(Decimal(38, 20)),
         `ds_agg_sum_14_merge` Nullable(UInt8),
         `ds_agg_sum_15` Nullable(Decimal(38, 20)),
         `ds_agg_sum_15_merge` Nullable(UInt8),
         `ds_agg_sum_16` Nullable(Decimal(38, 20)),
         `ds_agg_sum_16_merge` Nullable(UInt8),
         `ds_agg_sum_17` Nullable(Decimal(38, 20)),
         `ds_agg_sum_17_merge` Nullable(UInt8),
         `ds_agg_sum_18` Nullable(Decimal(38, 20)),
         `ds_agg_sum_18_merge` Nullable(UInt8),
         `ds_agg_sum_19` Nullable(Decimal(38, 20)),
         `ds_agg_sum_19_merge` Nullable(UInt8),
         `ds_agg_sum_20` Nullable(Decimal(38, 20)),
         `ds_agg_sum_20_merge` Nullable(UInt8),
         `ds_agg_sum_21` Nullable(Decimal(38, 20)),
         `ds_agg_sum_21_merge` Nullable(UInt8),
         `ds_agg_sum_22` Nullable(Decimal(38, 20)),
         `ds_agg_sum_22_merge` Nullable(UInt8),
         `ds_agg_sum_23` Nullable(Decimal(38, 20)),
         `ds_agg_sum_23_merge` Nullable(UInt8),
         `ds_agg_sum_24` Nullable(Decimal(38, 20)),
         `ds_agg_sum_24_merge` Nullable(UInt8),
         `ds_agg_sum_25` Nullable(Decimal(38, 20)),
         `ds_agg_sum_25_merge` Nullable(UInt8),
         `ds_agg_sum_26` Nullable(Decimal(38, 20)),
         `ds_agg_sum_26_merge` Nullable(UInt8),
         `ds_agg_sum_27` Nullable(Decimal(38, 20)),
         `ds_agg_sum_27_merge` Nullable(UInt8),
         `ds_agg_sum_28` Nullable(Decimal(38, 20)),
         `ds_agg_sum_28_merge` Nullable(UInt8),
         `ds_agg_sum_29` Nullable(Decimal(38, 20)),
         `ds_agg_sum_29_merge` Nullable(UInt8),
         `ds_agg_sum_30` Nullable(Decimal(38, 20)),
         `ds_agg_sum_30_merge` Nullable(UInt8),
         `ds_agg_uniq_1` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_1_merge` UInt8,
         `ds_agg_uniq_2` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_2_merge` UInt8,
         `ds_agg_uniq_3` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_3_merge` UInt8,
         `ds_agg_uniq_4` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_4_merge` UInt8,
         `ds_agg_uniq_5` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_5_merge` UInt8,
         `ds_agg_uniq_6` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_6_merge` UInt8,
         `ds_agg_uniq_7` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_7_merge` UInt8,
         `ds_agg_uniq_8` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_8_merge` UInt8,
         `ds_agg_uniq_9` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_9_merge` UInt8,
         `ds_agg_uniq_10` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_10_merge` UInt8,
         `ds_agg_uniq_11` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_11_merge` UInt8,
         `ds_agg_uniq_12` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_12_merge` UInt8,
         `ds_agg_uniq_13` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_13_merge` UInt8,
         `ds_agg_uniq_14` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_14_merge` UInt8,
         `ds_agg_uniq_15` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_15_merge` UInt8,
         `ds_agg_uniq_16` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_16_merge` UInt8,
         `ds_agg_uniq_17` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_17_merge` UInt8,
         `ds_agg_uniq_18` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_18_merge` UInt8,
         `ds_agg_uniq_19` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_19_merge` UInt8,
         `ds_agg_uniq_20` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_20_merge` UInt8,
         `ds_agg_uniq_21` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_21_merge` UInt8,
         `ds_agg_uniq_22` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_22_merge` UInt8,
         `ds_agg_uniq_23` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_23_merge` UInt8,
         `ds_agg_uniq_24` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_24_merge` UInt8,
         `ds_agg_uniq_25` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_25_merge` UInt8,
         `ds_agg_uniq_26` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_26_merge` UInt8,
         `ds_agg_uniq_27` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_27_merge` UInt8,
         `ds_agg_uniq_28` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_28_merge` UInt8,
         `ds_agg_uniq_29` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_29_merge` UInt8,
         `ds_agg_uniq_30` AggregateFunction(uniqExact, Nullable(String)),
         `ds_agg_uniq_30_merge` UInt8,
         `bi_sys_flag` Int8,
         `bi_sys_batch_id` Int64,
         `bi_sys_is_deleted` UInt8,
         `bi_sys_version` DateTime DEFAULT now(),
         `timestamp` DateTime64(9) DEFAULT now64(9),
         `value_slot` String,
         `is_deleted` UInt8,
         `sys_modified_time` Int64 DEFAULT toUnixTimestamp64Micro(now64(9)),
         `bi_sys_ods_part` String default 's'
     )
     ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version, bi_sys_is_deleted)
     PARTITION BY bi_sys_ods_part
     PRIMARY KEY (tenant_id, policy_id, view_id, view_version, object_id, bi_sys_flag,hash_code)
     ORDER BY (tenant_id, policy_id, view_id, view_version, object_id,bi_sys_flag,hash_code)
     TTL bi_sys_version + toIntervalMonth(1) WHERE (bi_sys_ods_part = 's') AND ((bi_sys_flag = 0) OR (is_deleted IN (-1, -2))), bi_sys_version + toIntervalWeek(1) WHERE bi_sys_ods_part in('i','c')
     SETTINGS index_granularity = 8192;
     """;

  public static final  String agg_data_sync_info= """
  CREATE TABLE IF NOT EXISTS agg_data_sync_info on cluster '{cluster}'
  (
    `tenant_id` String COMMENT '租户id',
    `view_id` String COMMENT '图id',
    `stat_view_unique_key` String COMMENT '图合并stat_view_unique_key',
    `policy_id` String COMMENT '同步策略的policy_id',
    `view_version` UInt32 COMMENT '统计图版本',
    `batch_num` UInt64 COMMENT '同步批次',
    `status` Int8 COMMENT 'sync_able:-2,sync_error:-1,sync_ing:0,sync_ed:1',
    `timestamp` DateTime64(9) DEFAULT now64(9),
    `is_deleted` UInt8,
    `max_sync_timestamp` UInt64
  )
    ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', timestamp)
        ORDER BY (tenant_id, policy_id,stat_view_unique_key,view_id, view_version)
        SETTINGS index_granularity = 8192;
    """;

  public static final String v_saleactionstage_sql = """
    CREATE OR REPLACE VIEW v_saleactionstage ON CLUSTER '{cluster}' AS
    SELECT ((
                    CASE
                        WHEN sale_action_stage.stage_order < 10 THEN '0'
                        ELSE ''
                        END || toString(sale_action_stage.stage_order)) || '. ') ||
           sale_action_stage.name AS ordered_name,
           sale_action_stage.ei,
           sale_action_stage.sale_action_stage_id,
           sale_action_stage.sale_action_id,
           sale_action_stage.name,
           sale_action_stage.description,
           sale_action_stage.stage_order,
           sale_action_stage.is_timeout_remind,
           sale_action_stage.remain_days,
           sale_action_stage.is_leader_confirm,
           sale_action_stage.is_finish_by_contacts,
           sale_action_stage.contact_count,
           sale_action_stage.type,
           sale_action_stage.as_is_that_day_remind,
           sale_action_stage.as_that_remain_days,
           sale_action_stage.as_is_timeout_remind,
           sale_action_stage.as_remain_days,
           sale_action_stage.update_time,
           sale_action_stage.weight,
           sale_action_stage.stage_flag,
           toString(sale_action_stage.ei) AS tenant_id,
           sale_action_stage.bi_sys_flag,
           sale_action_stage.bi_sys_batch_id,
           sale_action_stage.bi_sys_version,
           sale_action_stage.bi_sys_is_deleted,
           sale_action_stage.bi_sys_ods_part,
           toInt16(0) as is_deleted
    FROM sale_action_stage
    SETTINGS final = 1, join_use_nulls = 1;
    """;
  public static final String GOAL_VALUE_OBJ_SNAPSHOT = """
    CREATE TABLE IF NOT EXISTS goal_value_obj_snapshot ON CLUSTER '{cluster}'
    (
        `tenant_id` String COMMENT '租户id',
        `rule_id` String COMMENT '规则id',
        `view_version` UInt32 COMMENT '图的版本,快照版本',
        `batch_num` UInt64 COMMENT 'goal_value_obj批次号,快照批次',
        `hash_code` UInt64 COMMENT '考核维度hashcode',
        `check_dimension_value1` String COMMENT '第一考核维度',
        `timestamp` DateTime DEFAULT now(),
        `is_deleted` UInt8
    )
    ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', timestamp, is_deleted)
    PRIMARY KEY (tenant_id, rule_id, view_version, batch_num, hash_code)
    ORDER BY (tenant_id, rule_id, view_version, batch_num, hash_code)
    TTL timestamp + INTERVAL 1 WEEK DELETE WHERE is_deleted = 1
    SETTINGS index_granularity = 8192;
    """;
  public static final String BI_ERP_DATA_SCREEN = """
      CREATE TABLE IF NOT EXISTS bi_erp_data_screen on cluster '{cluster}'
         (
            `id` String,
            `tenantId` String,
            `name` String,
            `ployDetailId` String,
            `dataCenterId` String,
            `outSideObjApiName` String,
            `outSideObjId` String,
            `outDataCount` Nullable(Int32),
            `crmObjApiName` String,
            `crmObjId` String,
            `operationType` String,
            `operationTypeDetail` String,
            `sourceSystemType` String,
            `operateStatus` String,
            `historyDataType` Nullable(Int32),
            `executeTime` Nullable(Int64),
            `executeCost` Nullable(Int64),
            `createTime` Nullable(Int64),
            `updateTime` Nullable(Int64),
            `dataCreateTime` DateTime64(9) DEFAULT now64(9)
        )
        ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}')
        ORDER BY (tenantId, id)
        TTL toDate(CAST(dataCreateTime AS String)) + toIntervalDay(30)
        SETTINGS index_granularity = 8192;
    """;
  public static final String AGG_LOG_DATA = """
    CREATE TABLE IF NOT EXISTS agg_log_data ON CLUSTER '{cluster}'
      (
           `tenant_id` String,
           `view_id` String,
           `view_version` UInt32,
           `hash_code` UInt64,
           `hash_code_without_date` UInt64,
           `object_id` String,
           `action_date` LowCardinality(String),
           `owner` String,
           `life_status` LowCardinality(String),
           `create_time` String,
           `last_modified_time` String,
           `data_auth_code` String,
           `out_tenant_id` String,
           `out_data_auth_code` String,
           `dim_string_1` String,
           `dim_string_2` String,
           `dim_string_3` String,
           `dim_string_4` String,
           `dim_string_5` String,
           `dim_string_6` String,
           `dim_string_7` String,
           `dim_string_8` String,
           `dim_string_9` String,
           `dim_string_10` String,
           `dim_string_11` String,
           `dim_string_12` String,
           `dim_string_13` String,
           `dim_string_14` String,
           `dim_string_15` String,
           `dim_string_16` String,
           `dim_string_17` String,
           `dim_string_18` String,
           `dim_string_19` String,
           `dim_string_20` String,
           `dim_string_21` String,
           `dim_string_22` String,
           `dim_string_23` String,
           `dim_string_24` String,
           `dim_string_25` String,
           `dim_string_26` String,
           `dim_string_27` String,
           `dim_string_28` String,
           `dim_string_29` String,
           `dim_string_30` String,
           `dim_string_31` String,
           `dim_string_32` String,
           `dim_string_33` String,
           `dim_string_34` String,
           `dim_string_35` String,
           `dim_string_36` String,
           `dim_string_37` String,
           `dim_string_38` String,
           `dim_string_39` String,
           `dim_string_40` String,
           `dim_string_41` String,
           `dim_string_42` String,
           `dim_string_43` String,
           `dim_string_44` String,
           `dim_string_45` String,
           `dim_string_46` String,
           `dim_string_47` String,
           `dim_string_48` String,
           `dim_string_49` String,
           `dim_string_50` String,
           `dim_string_51` String,
           `dim_string_51_min_5` String,
           `dim_string_51_min_10` String,
           `dim_string_51_min_30` String,
           `dim_string_51_hour_1` String,
           `dim_array_string_1` Array(String),
           `dim_array_string_2` Array(String),
           `dim_array_string_3` Array(String),
           `dim_array_string_4` Array(String),
           `dim_array_string_5` Array(String),
           `dim_array_string_6` Array(String),
           `dim_array_string_7` Array(String),
           `dim_array_string_8` Array(String),
           `dim_array_string_9` Array(String),
           `dim_array_string_10` Array(String),
           `dim_int_1` Nullable(Int64),
           `dim_int_2` Nullable(Int64),
           `dim_int_3` Nullable(Int64),
           `dim_int_4` Nullable(Int64),
           `dim_int_5` Nullable(Int64),
           `dim_int_6` Nullable(Int64),
           `dim_int_7` Nullable(Int64),
           `dim_int_8` Nullable(Int64),
           `dim_int_9` Nullable(Int64),
           `dim_int_10` Nullable(Int64),
           `dim_int_11` Nullable(Int64),
           `dim_int_12` Nullable(Int64),
           `dim_int_13` Nullable(Int64),
           `dim_int_14` Nullable(Int64),
           `dim_int_15` Nullable(Int64),
           `dim_int_16` Nullable(Int64),
           `dim_int_17` Nullable(Int64),
           `dim_int_18` Nullable(Int64),
           `dim_int_19` Nullable(Int64),
           `dim_int_20` Nullable(Int64),
           `dim_int_21` Nullable(Int64),
           `dim_int_22` Nullable(Int64),
           `dim_int_23` Nullable(Int64),
           `dim_int_24` Nullable(Int64),
           `dim_int_25` Nullable(Int64),
           `dim_int_26` Nullable(Int64),
           `dim_int_27` Nullable(Int64),
           `dim_int_28` Nullable(Int64),
           `dim_int_29` Nullable(Int64),
           `dim_int_30` Nullable(Int64),
           `dim_decimal_1` Nullable(Decimal(38, 22)),
           `dim_decimal_2` Nullable(Decimal(38, 22)),
           `dim_decimal_3` Nullable(Decimal(38, 22)),
           `dim_decimal_4` Nullable(Decimal(38, 22)),
           `dim_decimal_5` Nullable(Decimal(38, 22)),
           `dim_decimal_6` Nullable(Decimal(38, 22)),
           `dim_decimal_7` Nullable(Decimal(38, 22)),
           `dim_decimal_8` Nullable(Decimal(38, 22)),
           `dim_decimal_9` Nullable(Decimal(38, 22)),
           `dim_decimal_10` Nullable(Decimal(38, 22)),
           `dim_decimal_11` Nullable(Decimal(38, 22)),
           `dim_decimal_12` Nullable(Decimal(38, 22)),
           `dim_decimal_13` Nullable(Decimal(38, 22)),
           `dim_decimal_14` Nullable(Decimal(38, 22)),
           `dim_decimal_15` Nullable(Decimal(38, 22)),
           `dim_decimal_16` Nullable(Decimal(38, 22)),
           `dim_decimal_17` Nullable(Decimal(38, 22)),
           `dim_decimal_18` Nullable(Decimal(38, 22)),
           `dim_decimal_19` Nullable(Decimal(38, 22)),
           `dim_decimal_20` Nullable(Decimal(38, 22)),
           `dim_decimal_21` Nullable(Decimal(38, 22)),
           `dim_decimal_22` Nullable(Decimal(38, 22)),
           `dim_decimal_23` Nullable(Decimal(38, 22)),
           `dim_decimal_24` Nullable(Decimal(38, 22)),
           `dim_decimal_25` Nullable(Decimal(38, 22)),
           `dim_decimal_26` Nullable(Decimal(38, 22)),
           `dim_decimal_27` Nullable(Decimal(38, 22)),
           `dim_decimal_28` Nullable(Decimal(38, 22)),
           `dim_decimal_29` Nullable(Decimal(38, 22)),
           `dim_decimal_30` Nullable(Decimal(38, 22)),
           `dim_boolean_1` Nullable(Bool),
           `dim_boolean_2` Nullable(Bool),
           `dim_boolean_3` Nullable(Bool),
           `dim_boolean_4` Nullable(Bool),
           `dim_boolean_5` Nullable(Bool),
           `dim_boolean_6` Nullable(Bool),
           `dim_boolean_7` Nullable(Bool),
           `dim_boolean_8` Nullable(Bool),
           `dim_boolean_9` Nullable(Bool),
           `dim_boolean_10` Nullable(Bool),
           `agg_uniq_tag_1` String,
           `agg_count_1` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_2` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_3` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_4` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_5` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_6` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_7` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_8` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_9` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_10` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_11` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_12` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_13` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_14` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_15` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_16` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_17` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_18` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_19` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_20` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_21` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_22` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_23` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_24` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_25` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_26` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_27` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_28` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_29` AggregateFunction(sum,Nullable(Int64)),
           `agg_count_30` AggregateFunction(sum,Nullable(Int64)),
           `agg_sum_1` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_2` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_3` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_4` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_5` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_6` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_7` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_8` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_9` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_10` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_11` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_12` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_13` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_14` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_15` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_16` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_17` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_18` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_19` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_20` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_21` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_22` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_23` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_24` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_25` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_26` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_27` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_28` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_29` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_sum_30` AggregateFunction(sum,Nullable(Decimal(38, 20))),
           `agg_uniq_1` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_2` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_3` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_4` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_5` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_6` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_7` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_8` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_9` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_10` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_11` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_12` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_13` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_14` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_15` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_16` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_17` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_18` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_19` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_20` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_21` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_22` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_23` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_24` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_25` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_26` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_27` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_28` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_29` AggregateFunction(uniqExact, Nullable(String)),
           `agg_uniq_30` AggregateFunction(uniqExact, Nullable(String)),
           `timestamp` DateTime64(9) DEFAULT now64(9),
           `is_deleted` UInt8
       )
       ENGINE = ReplicatedAggregatingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}')
       PARTITION BY cityHash64(tenant_id,
        view_id,
        view_version) % 10
       ORDER BY (tenant_id,
        view_id,
        view_version,
        hash_code)
       TTL toDate(CAST(action_date AS String)) + toIntervalDay(30)
       SETTINGS index_granularity = 8192;                               
    """;
  //计算视图1  读外部系统次数 agg_count_1，读外部系统耗时 agg_sum_1，读外部系统数据量 agg_sum_2
  public static final String AGG_LOG_DATA_V1 = """
    CREATE MATERIALIZED VIEW IF NOT EXISTS agg_log_data_v1 ON CLUSTER '{cluster}' TO agg_log_data
            (
             `tenant_id` String,
             `view_id` String,
             `view_version` UInt32,
             `hash_code` UInt64,
             `dim_string_1` String,
             `dim_string_2` String,
             `dim_string_3` String,
             `dim_string_4` String,
             `dim_string_5` String,
             `dim_string_6` String,
             `dim_string_7` String,
             `dim_string_8` String,
             `dim_string_9` String,
             `dim_string_51` String,
             `dim_string_51_min_5` String,
             `dim_string_51_min_10` String,
             `dim_string_51_min_30` String,
             `dim_string_51_hour_1` String,
             `action_date` LowCardinality(String),
             `agg_count_1` AggregateFunction(sum,Nullable(Int64)),
             `agg_sum_1` AggregateFunction(sum,Nullable(Decimal(38, 20))),
             `agg_sum_2` AggregateFunction(sum,Nullable(Decimal(38, 20)))
            )
    AS SELECT
            tenantId AS tenant_id,
            'BI_65e058af8ab8e892af0323b3' AS view_id,
            0 AS view_version,
            cityHash64(dim_string_1,dim_string_2,dim_string_3,dim_string_4,dim_string_5,dim_string_6,dim_string_7,dim_string_8,dim_string_9,dim_string_51,dim_string_51_min_5,dim_string_51_min_10,dim_string_51_min_30,dim_string_51_hour_1,action_date) AS hash_code,
            coalesce(dataCenterId,'') AS dim_string_1,
            coalesce(outSideObjApiName,'') AS dim_string_2,
            coalesce(operateStatus,'') AS dim_string_3,
            coalesce(operationType,'') AS dim_string_4,
            coalesce(crmObjApiName,'') AS dim_string_5,
            coalesce(sourceSystemType,'') AS dim_string_6,
            coalesce(operationTypeDetail,'') AS dim_string_7,
            coalesce(ployDetailId,'') AS dim_string_8,
            case when historyDataType=1 then 'true' when historyDataType=2 then 'false' else '' end AS dim_string_9,
            coalesce(substring(toString(toYYYYMMDDhhmmss(fromUnixTimestamp(toInt64(executeTime/1000)), 'Asia/Shanghai')), 1, 12),'000000000000') AS dim_string_51,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfFiveMinute(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_5,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfTenMinutes(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_10,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 30 minute), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_30,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 1 hour), 'Asia/Shanghai')),1,10),'0000000000') AS dim_string_51_hour_1,
            coalesce(toString(toYYYYMMDD(fromUnixTimestamp(toInt64(executeTime/1000)),'Asia/Shanghai')),'00000000')   AS action_date,
            sumState(CAST(1 as Nullable(Int64))) as agg_count_1,
            sumState(if(executeCost = null,CAST(0 as Nullable(Decimal(38, 20))),CAST(executeCost as Nullable(Decimal(38, 20))))) as agg_sum_1,
            sumState(if(outDataCount = null,CAST(0 as Nullable(Decimal(38, 20))),CAST(outDataCount as Nullable(Decimal(38, 20))))) as agg_sum_2
       from bi_erp_data_screen
       where operationType = 'read' and sourceSystemType = '2'
       group by tenantId,
                dim_string_1,
                dim_string_2,
                dim_string_3,
                dim_string_4,
                dim_string_5,
                dim_string_6,
                dim_string_7,
                dim_string_8,
                dim_string_9,
                dim_string_51,
                dim_string_51_min_5,
                dim_string_51_min_10,
                dim_string_51_min_30,
                dim_string_51_hour_1,
                action_date;
    """;
  //计算视图2  写入外部系统数据量 agg_uniq_1，写外部系统耗时 agg_sum_1, 写外部系统次数 agg_uniq_2
  public static final String AGG_LOG_DATA_V2 = """
    CREATE MATERIALIZED VIEW IF NOT EXISTS agg_log_data_v2 ON CLUSTER '{cluster}' TO agg_log_data
            (
             `tenant_id` String,
             `view_id` String,
             `view_version` UInt32,
             `hash_code` UInt64,
             `dim_string_1` String,
             `dim_string_2` String,
             `dim_string_3` String,
             `dim_string_4` String,
             `dim_string_5` String,
             `dim_string_6` String,
             `dim_string_7` String,
             `dim_string_8` String,
             `dim_string_9` String,
             `dim_string_51` String,
             `dim_string_51_min_5` String,
             `dim_string_51_min_10` String,
             `dim_string_51_min_30` String,
             `dim_string_51_hour_1` String,
             `action_date` LowCardinality(String),
             `agg_count_2` AggregateFunction(sum,Nullable(Int64)),
             `agg_uniq_1` AggregateFunction(uniqExact, Nullable(String)),
             `agg_sum_3` AggregateFunction(sum,Nullable(Decimal(38, 20)))
            )
    AS SELECT
            tenantId AS tenant_id,
            'BI_65e058af8ab8e892af0323b3' AS view_id,
            0 AS view_version,
            cityHash64(dim_string_1,dim_string_2,dim_string_3,dim_string_4,dim_string_5,dim_string_6,dim_string_7,dim_string_8,dim_string_9,dim_string_51,dim_string_51_min_5,dim_string_51_min_10,dim_string_51_min_30,dim_string_51_hour_1,action_date) AS hash_code,
            coalesce(dataCenterId,'') AS dim_string_1,
            coalesce(outSideObjApiName,'') AS dim_string_2,
            coalesce(operateStatus,'') AS dim_string_3,
            coalesce(operationType,'') AS dim_string_4,
            coalesce(crmObjApiName,'') AS dim_string_5,
            coalesce(sourceSystemType,'') AS dim_string_6,
            coalesce(operationTypeDetail,'') AS dim_string_7,
            coalesce(ployDetailId,'') AS dim_string_8,
            case when historyDataType=1 then 'true' when historyDataType=2 then 'false' else '' end AS dim_string_9,
            coalesce(substring(toString(toYYYYMMDDhhmmss(fromUnixTimestamp(toInt64(executeTime/1000)), 'Asia/Shanghai')), 1, 12),'000000000000') AS dim_string_51,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfFiveMinute(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_5,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfTenMinutes(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_10,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 30 minute), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_30,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 1 hour), 'Asia/Shanghai')),1,10),'0000000000') AS dim_string_51_hour_1,
            coalesce(toString(toYYYYMMDD(fromUnixTimestamp(toInt64(executeTime/1000)),'Asia/Shanghai')),'00000000')   AS action_date,
            sumState(CAST(1 as Nullable(Int64))) as agg_count_2,
            uniqExactState(CAST(if(outSideObjId = '',null,outSideObjId) as Nullable(String))) as agg_uniq_1,
            sumState(if(executeCost = null,CAST(0 as Nullable(Decimal(38, 20))),CAST(executeCost as Nullable(Decimal(38, 20))))) as agg_sum_3
       from bi_erp_data_screen
       where operationType = 'write' and sourceSystemType = '1'
       group by tenantId,
                dim_string_1,
                dim_string_2,
                dim_string_3,
                dim_string_4,
                dim_string_5,
                dim_string_6,
                dim_string_7,
                dim_string_8,
                dim_string_9,
                dim_string_51,
                dim_string_51_min_5,
                dim_string_51_min_10,
                dim_string_51_min_30,
                dim_string_51_hour_1,
                action_date;
    """;
  //计算视图3  读CRM次数 agg_count_6
  public static final String AGG_LOG_DATA_V3 = """
    CREATE MATERIALIZED VIEW IF NOT EXISTS agg_log_data_v3 ON CLUSTER '{cluster}' TO agg_log_data
            (
             `tenant_id` String,
             `view_id` String,
             `view_version` UInt32,
             `hash_code` UInt64,
             `dim_string_1` String,
             `dim_string_2` String,
             `dim_string_3` String,
             `dim_string_4` String,
             `dim_string_5` String,
             `dim_string_6` String,
             `dim_string_7` String,
             `dim_string_8` String,
             `dim_string_9` String,
             `dim_string_51` String,
             `dim_string_51_min_5` String,
             `dim_string_51_min_10` String,
             `dim_string_51_min_30` String,
             `dim_string_51_hour_1` String,
             `action_date` LowCardinality(String),
             `agg_count_6` AggregateFunction(sum,Nullable(Int64))
            )
    AS SELECT
            tenantId AS tenant_id,
            'BI_65e058af8ab8e892af0323b3' AS view_id,
            0 AS view_version,
            cityHash64(dim_string_1,dim_string_2,dim_string_3,dim_string_4,dim_string_5,dim_string_6,dim_string_7,dim_string_8,dim_string_9,dim_string_51,dim_string_51_min_5,dim_string_51_min_10,dim_string_51_min_30,dim_string_51_hour_1,action_date) AS hash_code,
            coalesce(dataCenterId,'') AS dim_string_1,
            coalesce(outSideObjApiName,'') AS dim_string_2,
            coalesce(operateStatus,'') AS dim_string_3,
            coalesce(operationType,'') AS dim_string_4,
            coalesce(crmObjApiName,'') AS dim_string_5,
            coalesce(sourceSystemType,'') AS dim_string_6,
            coalesce(operationTypeDetail,'') AS dim_string_7,
            coalesce(ployDetailId,'') AS dim_string_8,
            case when historyDataType=1 then 'true' when historyDataType=2 then 'false' else '' end AS dim_string_9,
            coalesce(substring(toString(toYYYYMMDDhhmmss(fromUnixTimestamp(toInt64(executeTime/1000)), 'Asia/Shanghai')), 1, 12),'000000000000') AS dim_string_51,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfFiveMinute(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_5,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfTenMinutes(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_10,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 30 minute), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_30,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 1 hour), 'Asia/Shanghai')),1,10),'0000000000') AS dim_string_51_hour_1,
            coalesce(toString(toYYYYMMDD(fromUnixTimestamp(toInt64(executeTime/1000)),'Asia/Shanghai')),'00000000')   AS action_date,
            sumState(CAST(1 as Nullable(Int64))) as agg_count_6
       from bi_erp_data_screen
       where operationType = 'read' and sourceSystemType='1'
       group by tenantId,
                dim_string_1,
                dim_string_2,
                dim_string_3,
                dim_string_4,
                dim_string_5,
                dim_string_6,
                dim_string_7,
                dim_string_8,
                dim_string_9,
                dim_string_51,
                dim_string_51_min_5,
                dim_string_51_min_10,
                dim_string_51_min_30,
                dim_string_51_hour_1,
                action_date;
    """;
  //计算视图4  写CRM接口次数 agg_count_3 , 写入CRM数据量 agg_uniq_2
  public static final String AGG_LOG_DATA_V4 = """
    CREATE MATERIALIZED VIEW IF NOT EXISTS agg_log_data_v4 ON CLUSTER '{cluster}' TO agg_log_data
            (
             `tenant_id` String,
             `view_id` String,
             `view_version` UInt32,
             `hash_code` UInt64,
             `dim_string_1` String,
             `dim_string_2` String,
             `dim_string_3` String,
             `dim_string_4` String,
             `dim_string_5` String,
             `dim_string_6` String,
             `dim_string_7` String,
             `dim_string_8` String,
             `dim_string_9` String,
             `dim_string_51` String,
             `dim_string_51_min_5` String,
             `dim_string_51_min_10` String,
             `dim_string_51_min_30` String,
             `dim_string_51_hour_1` String,
             `action_date` LowCardinality(String),
             `agg_count_3` AggregateFunction(sum,Nullable(Int64)),
             `agg_uniq_2` AggregateFunction(uniqExact, Nullable(String))
            )
    AS SELECT
            tenantId AS tenant_id,
            'BI_65e058af8ab8e892af0323b3' AS view_id,
            0 AS view_version,
            cityHash64(dim_string_1,dim_string_2,dim_string_3,dim_string_4,dim_string_5,dim_string_6,dim_string_7,dim_string_8,dim_string_9,dim_string_51,dim_string_51_min_5,dim_string_51_min_10,dim_string_51_min_30,dim_string_51_hour_1,action_date) AS hash_code,
            coalesce(dataCenterId,'') AS dim_string_1,
            coalesce(outSideObjApiName,'') AS dim_string_2,
            coalesce(operateStatus,'') AS dim_string_3,
            coalesce(operationType,'') AS dim_string_4,
            coalesce(crmObjApiName,'') AS dim_string_5,
            coalesce(sourceSystemType,'') AS dim_string_6,
            coalesce(operationTypeDetail,'') AS dim_string_7,
            coalesce(ployDetailId,'') AS dim_string_8,
            case when historyDataType=1 then 'true' when historyDataType=2 then 'false' else '' end AS dim_string_9,
            coalesce(substring(toString(toYYYYMMDDhhmmss(fromUnixTimestamp(toInt64(executeTime/1000)), 'Asia/Shanghai')), 1, 12),'000000000000') AS dim_string_51,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfFiveMinute(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_5,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfTenMinutes(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_10,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 30 minute), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_30,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 1 hour), 'Asia/Shanghai')),1,10),'0000000000') AS dim_string_51_hour_1,
            coalesce(toString(toYYYYMMDD(fromUnixTimestamp(toInt64(executeTime/1000)),'Asia/Shanghai')),'00000000')   AS action_date,
            sumState(CAST(1 as Nullable(Int64))) as agg_count_3,
            uniqExactState(CAST(if(crmObjId = '',null,crmObjId) as Nullable(String))) as agg_uniq_2
       from bi_erp_data_screen
       where operationType = 'write' and sourceSystemType='2'
       group by tenantId,
                dim_string_1,
                dim_string_2,
                dim_string_3,
                dim_string_4,
                dim_string_5,
                dim_string_6,
                dim_string_7,
                dim_string_8,
                dim_string_9,
                dim_string_51,
                dim_string_51_min_5,
                dim_string_51_min_10,
                dim_string_51_min_30,
                dim_string_51_hour_1,
                action_date;
    """;
  //计算视图5  写入外部的CRM数据量 agg_uniq_3
  public static final String AGG_LOG_DATA_V5 = """
    CREATE MATERIALIZED VIEW IF NOT EXISTS agg_log_data_v5 ON CLUSTER '{cluster}' TO agg_log_data
            (
             `tenant_id` String,
             `view_id` String,
             `view_version` UInt32,
             `hash_code` UInt64,
             `dim_string_1` String,
             `dim_string_2` String,
             `dim_string_3` String,
             `dim_string_4` String,
             `dim_string_5` String,
             `dim_string_6` String,
             `dim_string_7` String,
             `dim_string_8` String,
             `dim_string_9` String,
             `dim_string_51` String,
             `dim_string_51_min_5` String,
             `dim_string_51_min_10` String,
             `dim_string_51_min_30` String,
             `dim_string_51_hour_1` String,
             `action_date` LowCardinality(String),
             `agg_uniq_3` AggregateFunction(uniqExact, Nullable(String))
            )
    AS SELECT
            tenantId AS tenant_id,
            'BI_65e058af8ab8e892af0323b3' AS view_id,
            0 AS view_version,
            cityHash64(dim_string_1,dim_string_2,dim_string_3,dim_string_4,dim_string_5,dim_string_6,dim_string_7,dim_string_8,dim_string_9,dim_string_51,dim_string_51_min_5,dim_string_51_min_10,dim_string_51_min_30,dim_string_51_hour_1,action_date) AS hash_code,
            coalesce(dataCenterId,'') AS dim_string_1,
            coalesce(outSideObjApiName,'') AS dim_string_2,
            coalesce(operateStatus,'') AS dim_string_3,
            coalesce(operationType,'') AS dim_string_4,
            coalesce(crmObjApiName,'') AS dim_string_5,
            coalesce(sourceSystemType,'') AS dim_string_6,
            coalesce(operationTypeDetail,'') AS dim_string_7,
            coalesce(ployDetailId,'') AS dim_string_8,
            case when historyDataType=1 then 'true' when historyDataType=2 then 'false' else '' end AS dim_string_9,
            coalesce(substring(toString(toYYYYMMDDhhmmss(fromUnixTimestamp(toInt64(executeTime/1000)), 'Asia/Shanghai')), 1, 12),'000000000000') AS dim_string_51,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfFiveMinute(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_5,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfTenMinutes(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_10,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 30 minute), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_30,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 1 hour), 'Asia/Shanghai')),1,10),'0000000000') AS dim_string_51_hour_1,
            coalesce(toString(toYYYYMMDD(fromUnixTimestamp(toInt64(executeTime/1000)),'Asia/Shanghai')),'00000000')   AS action_date,
            uniqExactState(CAST(if(crmObjId = '',null,crmObjId) as Nullable(String))) as agg_uniq_3
       from bi_erp_data_screen
       where operationType = 'write' and sourceSystemType='1'
       group by tenantId,
                dim_string_1,
                dim_string_2,
                dim_string_3,
                dim_string_4,
                dim_string_5,
                dim_string_6,
                dim_string_7,
                dim_string_8,
                dim_string_9,
                dim_string_51,
                dim_string_51_min_5,
                dim_string_51_min_10,
                dim_string_51_min_30,
                dim_string_51_hour_1,
                action_date;
    """;
  //计算视图6  函数异常次数 agg_uniq_1
  public static final String AGG_LOG_DATA_V6 = """
    CREATE MATERIALIZED VIEW IF NOT EXISTS agg_log_data_v6 ON CLUSTER '{cluster}' TO agg_log_data
            (
             `tenant_id` String,
             `view_id` String,
             `view_version` UInt32,
             `hash_code` UInt64,
             `dim_string_1` String,
             `dim_string_2` String,
             `dim_string_3` String,
             `dim_string_4` String,
             `dim_string_5` String,
             `dim_string_6` String,
             `dim_string_7` String,
             `dim_string_8` String,
             `dim_string_9` String,
             `dim_string_51` String,
             `dim_string_51_min_5` String,
             `dim_string_51_min_10` String,
             `dim_string_51_min_30` String,
             `dim_string_51_hour_1` String,
             `action_date` LowCardinality(String),
             `agg_count_4` AggregateFunction(sum,Nullable(Int64))
            )
    AS SELECT
            tenantId AS tenant_id,
            'BI_65e058af8ab8e892af0323b3' AS view_id,
            0 AS view_version,
            cityHash64(dim_string_1,dim_string_2,dim_string_3,dim_string_4,dim_string_5,dim_string_6,dim_string_7,dim_string_8,dim_string_9,dim_string_51,dim_string_51_min_5,dim_string_51_min_10,dim_string_51_min_30,dim_string_51_hour_1,action_date) AS hash_code,
            coalesce(dataCenterId,'') AS dim_string_1,
            coalesce(outSideObjApiName,'') AS dim_string_2,
            coalesce(operateStatus,'') AS dim_string_3,
            coalesce(operationType,'') AS dim_string_4,
            coalesce(crmObjApiName,'') AS dim_string_5,
            coalesce(sourceSystemType,'') AS dim_string_6,
            coalesce(operationTypeDetail,'') AS dim_string_7,
            coalesce(ployDetailId,'') AS dim_string_8,
            case when historyDataType=1 then 'true' when historyDataType=2 then 'false' else '' end AS dim_string_9,
            coalesce(substring(toString(toYYYYMMDDhhmmss(fromUnixTimestamp(toInt64(executeTime/1000)), 'Asia/Shanghai')), 1, 12),'000000000000') AS dim_string_51,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfFiveMinute(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_5,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfTenMinutes(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_10,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 30 minute), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_30,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 1 hour), 'Asia/Shanghai')),1,10),'0000000000') AS dim_string_51_hour_1,
            coalesce(toString(toYYYYMMDD(fromUnixTimestamp(toInt64(executeTime/1000)),'Asia/Shanghai')),'00000000')   AS action_date,
            sumState(CAST(1 as Nullable(Int64))) as agg_count_4
       from bi_erp_data_screen
       where operationType = 'func' and operateStatus = '2'
       group by tenantId,
                dim_string_1,
                dim_string_2,
                dim_string_3,
                dim_string_4,
                dim_string_5,
                dim_string_6,
                dim_string_7,
                dim_string_8,
                dim_string_9,
                dim_string_51,
                dim_string_51_min_5,
                dim_string_51_min_10,
                dim_string_51_min_30,
                dim_string_51_hour_1,
                action_date;
    """;
  //计算视图7  日志量 agg_count_5
  public static final String AGG_LOG_DATA_V7 = """
    CREATE MATERIALIZED VIEW IF NOT EXISTS agg_log_data_v7 ON CLUSTER '{cluster}' TO agg_log_data
            (
             `tenant_id` String,
             `view_id` String,
             `view_version` UInt32,
             `hash_code` UInt64,
             `dim_string_1` String,
             `dim_string_2` String,
             `dim_string_3` String,
             `dim_string_4` String,
             `dim_string_5` String,
             `dim_string_6` String,
             `dim_string_7` String,
             `dim_string_8` String,
             `dim_string_9` String,
             `dim_string_51` String,
             `dim_string_51_min_5` String,
             `dim_string_51_min_10` String,
             `dim_string_51_min_30` String,
             `dim_string_51_hour_1` String,
             `action_date` LowCardinality(String),
             `agg_count_5` AggregateFunction(sum,Nullable(Int64))
            )
    AS SELECT
            tenantId AS tenant_id,
            'BI_65e058af8ab8e892af0323b3' AS view_id,
            0 AS view_version,
            cityHash64(dim_string_1,dim_string_2,dim_string_3,dim_string_4,dim_string_5,dim_string_6,dim_string_7,dim_string_8,dim_string_9,dim_string_51,dim_string_51_min_5,dim_string_51_min_10,dim_string_51_min_30,dim_string_51_hour_1,action_date) AS hash_code,
            coalesce(dataCenterId,'') AS dim_string_1,
            coalesce(outSideObjApiName,'') AS dim_string_2,
            coalesce(operateStatus,'') AS dim_string_3,
            coalesce(operationType,'') AS dim_string_4,
            coalesce(crmObjApiName,'') AS dim_string_5,
            coalesce(sourceSystemType,'') AS dim_string_6,
            coalesce(operationTypeDetail,'') AS dim_string_7,
            coalesce(ployDetailId,'') AS dim_string_8,
            case when historyDataType=1 then 'true' when historyDataType=2 then 'false' else '' end AS dim_string_9,
            coalesce(substring(toString(toYYYYMMDDhhmmss(fromUnixTimestamp(toInt64(executeTime/1000)), 'Asia/Shanghai')), 1, 12),'000000000000') AS dim_string_51,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfFiveMinute(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_5,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfTenMinutes(fromUnixTimestamp(toInt64(executeTime/1000))), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_10,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 30 minute), 'Asia/Shanghai')),1,12),'000000000000') AS dim_string_51_min_30,
            coalesce(substring(toString(toYYYYMMDDhhmmss(toStartOfInterval(fromUnixTimestamp(toInt64(executeTime/1000)), INTERVAL 1 hour), 'Asia/Shanghai')),1,10),'0000000000') AS dim_string_51_hour_1,
            coalesce(toString(toYYYYMMDD(fromUnixTimestamp(toInt64(executeTime/1000)),'Asia/Shanghai')),'00000000')   AS action_date,
            sumState(CAST(1 as Nullable(Int64))) as agg_count_5
       from bi_erp_data_screen
       group by tenantId,
                dim_string_1,
                dim_string_2,
                dim_string_3,
                dim_string_4,
                dim_string_5,
                dim_string_6,
                dim_string_7,
                dim_string_8,
                dim_string_9,
                dim_string_51,
                dim_string_51_min_5,
                dim_string_51_min_10,
                dim_string_51_min_30,
                dim_string_51_hour_1,
                action_date;
    """;

  public static String stage_runtime_new_opportunity= """
    CREATE OR REPLACE VIEW stage_runtime_new_opportunity ON CLUSTER '{cluster}'
    as
    SELECT stage_runtime.out_data_auth_code,
           stage_runtime.dimension_d2,
           stage_runtime.mc_exchange_rate,
           stage_runtime.out_owner,
           stage_runtime.out_tenant_id,
           stage_runtime.dimension_d3,
           stage_runtime.mc_exchange_rate_version,
           stage_runtime.out_data_auth_id,
           stage_runtime.dimension_d1,
           stage_runtime.mc_currency,
           stage_runtime.mc_functional_currency,
           stage_runtime.fact_time_out_time,
           stage_runtime.data_own_department,
           stage_runtime.instance_status,
           stage_runtime.is_deleted,
           stage_runtime.package,
           stage_runtime.end_time,
           stage_runtime.created_by,
           stage_runtime.is_terminal,
           stage_runtime.source_stage_id,
           stage_runtime.create_time,
           stage_runtime.version,
           stage_runtime.data_auth_code,
           stage_runtime.object_describe_id,
           stage_runtime.source_workflow_id,
           stage_runtime.name,
           stage_runtime.stage_status,
           stage_runtime.workflow_instance_id,
           stage_runtime.last_modified_by,
           stage_runtime.stage_id,
           stage_runtime.remind_latency,
           stage_runtime.stage_field_api_name,
           stage_runtime.object_id,
           stage_runtime.entity_id,
           stage_runtime.id,
           stage_runtime.last_modified_time,
           stage_runtime.workflow_id,
           stage_runtime.order_id,
           stage_runtime.source_order_id,
           stage_runtime.change_type,
           'StageRuntimeNewOpportunityObj' AS object_describe_api_name,
            stage_runtime.start_time,
           stage_runtime.tenant_id,
           stage_runtime.is_time_out,
           stage_runtime.accumulate_time,
           stage_runtime.oppor_stage_id,
           stage_runtime.stage_sub_status,
           stage_runtime.extend_obj_data_id,
           stage_runtime.out_data_own_organization,
           stage_runtime.out_data_own_department,
           stage_runtime.data_own_organization,
           stage_runtime.bi_sys_flag
    FROM stage_runtime final
    WHERE stage_runtime.entity_id = 'NewOpportunityObj' AND stage_runtime.instance_status='';
    """;
  public static String stage_runtime_task_new_opportunity= """
    CREATE OR REPLACE VIEW stage_runtime_task_new_opportunity ON CLUSTER '{cluster}'
    as
    SELECT COALESCE(stage_task_1.id, stage_runtime_1.id)                  AS id,
           stage_task_1.id                                                AS stage_task_id,
           stage_runtime_1.tenant_id                                      AS tenant_id,
           'StageRuntimeTaskNewOpportunityObj'                            AS object_describe_api_name,
           stage_task_1.accumulate_time,
           stage_task_1.is_time_out,
           stage_task_1.remind_latency,
           stage_task_1.timeout_time,
           stage_task_1.total_spent_time,
           stage_task_1.recent_enter_time,
           stage_task_1.name,
           stage_task_1.owner,
           COALESCE(stage_task_1.is_deleted, stage_runtime_1.is_deleted) AS is_deleted,
           stage_task_1.source_workflow_id,
           stage_task_1.workflow_name,
           stage_task_1.stage_field_api_name,
           stage_task_1.stage_id,
           stage_task_1.object_api_name,
           stage_task_1.object_data_id,
           stage_task_1.workflow_id,
           stage_task_1.activity_id,
           stage_task_1.workflow_instance_id,
           stage_task_1.start_time,
           stage_task_1.end_time,
           stage_task_1.state,
           stage_task_1.life_status,
           stage_task_1.record_type,
           stage_task_1.created_by,
           stage_task_1.create_time,
           stage_task_1.last_modified_by,
           stage_task_1.last_modified_time,
           stage_task_1.extend_obj_data_id,
           stage_task_1.package,
           stage_task_1.object_describe_id,
           stage_task_1.version,
           stage_task_1.candidate_ids,
           stage_task_1.processor_ids,
           stage_task_1.lock_user,
           stage_task_1.lock_rule,
           stage_task_1.life_status_before_invalid,
           stage_task_1.is_enable,
           stage_task_1.is_default,
           stage_task_1.lock_status,
           stage_task_1.out_tenant_id,
           stage_task_1.out_owner,
           stage_task_1.data_own_department,
           stage_task_1.change_type,
           stage_task_1.data_auth_code,
           stage_task_1.out_data_auth_code,
           stage_task_1.link_app_id,
           stage_task_1.link_app_type,
           stage_task_1.order_by,
           stage_task_1.read_employee,
           stage_task_1.data_auth_id,
           stage_task_1.out_data_auth_id,
           stage_task_1.data_own_organization,
           stage_task_1.current_candidate_ids,
           stage_task_1.display_name,
           stage_task_1.dimension_d1,
           stage_task_1.dimension_d2,
           stage_task_1.dimension_d3,
           stage_task_1.mc_currency,
           stage_task_1.mc_exchange_rate,
           stage_task_1.mc_functional_currency,
           stage_task_1.mc_exchange_rate_version,
           stage_task_1.origin_source,
           stage_task_1.sys_modified_time,
           stage_task_1.object_record_type,
           stage_task_1.out_data_own_department,
           stage_task_1.out_data_own_organization,
           stage_runtime_1.bi_sys_flag as bi_sys_flag,
           stage_runtime_1.bi_sys_batch_id as bi_sys_batch_id,
           stage_runtime_1.id                                                     AS stage_runtime_id,
           stage_runtime_1.tenant_id                                              AS stage_runtime_tenant_id,
           stage_runtime_1.record_type                                            AS stage_runtime_record_type,
           stage_runtime_1.created_by                                             AS stage_runtime_created_by,
           stage_runtime_1.create_time                                            AS stage_runtime_create_time,
           stage_runtime_1.last_modified_by                                       AS stage_runtime_last_modified_by,
           stage_runtime_1.last_modified_time                                     AS stage_runtime_last_modified_time,
           stage_runtime_1.extend_obj_data_id                                     AS stage_runtime_extend_obj_data_id,
           stage_runtime_1.package                                                AS stage_runtime_package,
           stage_runtime_1.object_describe_id                                     AS stage_runtime_object_describe_id,
           stage_runtime_1.object_describe_api_name                               AS stage_runtime_object_describe_api_name,
           stage_runtime_1.version                                                AS stage_runtime_version,
           stage_runtime_1.is_deleted                                             AS stage_runtime_is_deleted,
           stage_runtime_1.out_tenant_id                                          AS stage_runtime_out_tenant_id,
           stage_runtime_1.out_owner                                              AS stage_runtime_out_owner,
           stage_runtime_1.stage_id                                               AS stage_runtime_stage_id,
           stage_runtime_1.stage_status                                           AS stage_runtime_stage_status,
           stage_runtime_1.start_time                                             AS stage_runtime_start_time,
           stage_runtime_1.end_time                                               AS stage_runtime_end_time,
           stage_runtime_1.source_workflow_id                                     AS stage_runtime_source_workflow_id,
           stage_runtime_1.workflow_id                                            AS stage_runtime_workflow_id,
           stage_runtime_1.workflow_instance_id                                   AS stage_runtime_workflow_instance_id,
           stage_runtime_1.entity_id                                              AS stage_runtime_entity_id,
           stage_runtime_1.object_id                                              AS stage_runtime_object_id,
           stage_runtime_1.is_time_out                                            AS stage_runtime_is_time_out,
           stage_runtime_1.fact_time_out_time                                     AS stage_runtime_fact_time_out_time,
           stage_runtime_1.accumulate_time                                        AS stage_runtime_accumulate_time,
           stage_runtime_1.name                                                   AS stage_runtime_name,
           stage_runtime_1.remind_latency                                         AS stage_runtime_remind_latency,
           stage_runtime_1.data_own_department                                    AS stage_runtime_data_own_department,
           stage_runtime_1.lock_user                                              AS stage_runtime_lock_user,
           stage_runtime_1.life_status_before_invalid                             AS stage_runtime_life_status_before_invalid,
           stage_runtime_1.lock_status                                            AS stage_runtime_lock_status,
           stage_runtime_1.lock_rule                                              AS stage_runtime_lock_rule,
           stage_runtime_1.order_id                                               AS stage_runtime_order_id,
           stage_runtime_1.oppor_stage_id                                         AS stage_runtime_oppor_stage_id,
           stage_runtime_1.data_auth_code                                         AS stage_runtime_data_auth_code,
           stage_runtime_1.change_type                                            AS stage_runtime_change_type,
           stage_runtime_1.out_data_auth_code                                     AS stage_runtime_out_data_auth_code,
           stage_runtime_1.source_stage_id                                        AS stage_runtime_source_stage_id,
           stage_runtime_1.source_order_id                                        AS stage_runtime_source_order_id,
           stage_runtime_1.order_by                                               AS stage_runtime_order_by,
           stage_runtime_1.stage_field_api_name                                   AS stage_runtime_stage_field_api_name,
           stage_runtime_1.is_terminal                                            AS stage_runtime_is_terminal,
           stage_runtime_1.instance_status                                        AS stage_runtime_instance_status,
           stage_runtime_1.stage_sub_status                                       AS stage_runtime_stage_sub_status,
           stage_runtime_1.data_auth_id                                           AS stage_runtime_data_auth_id,
           stage_runtime_1.out_data_auth_id                                       AS stage_runtime_out_data_auth_id,
           stage_runtime_1.mc_currency                                            AS stage_runtime_mc_currency,
           stage_runtime_1.mc_exchange_rate                                       AS stage_runtime_mc_exchange_rate,
           stage_runtime_1.mc_functional_currency                                 AS stage_runtime_mc_functional_currency,
           stage_runtime_1.mc_exchange_rate_version                               AS stage_runtime_mc_exchange_rate_version,
           stage_runtime_1.data_own_organization                                  AS stage_runtime_data_own_organization,
           stage_runtime_1.display_name                                           AS stage_runtime_display_name,
           stage_runtime_1.origin_source                                          AS stage_runtime_origin_source,
           stage_runtime_1.sys_modified_time                                      AS stage_runtime_sys_modified_time,
           stage_runtime_1.dimension_d1                                           AS stage_runtime_dimension_d1,
           stage_runtime_1.dimension_d2                                           AS stage_runtime_dimension_d2,
           stage_runtime_1.dimension_d3                                           AS stage_runtime_dimension_d3
    FROM
        (select  id,
                 tenant_id,
                 accumulate_time,
                 is_time_out,
                 remind_latency,
                 timeout_time,
                 total_spent_time,
                 recent_enter_time,
                 name,
                 owner,
                 is_deleted,
                 source_workflow_id,
                 workflow_name,
                 stage_field_api_name,
                 stage_id,
                 object_api_name,
                 object_data_id,
                 workflow_id,
                 activity_id,
                 workflow_instance_id,
                 start_time,
                 end_time,
                 state,
                 life_status,
                 record_type,
                 created_by,
                 create_time,
                 last_modified_by,
                 last_modified_time,
                 extend_obj_data_id,
                 package,
                 object_describe_id,
                 version,
                 candidate_ids,
                 processor_ids,
                 lock_user,
                 lock_rule,
                 life_status_before_invalid,
                 is_enable,
                 is_default,
                 lock_status,
                 out_tenant_id,
                 out_owner,
                 data_own_department,
                 change_type,
                 data_auth_code,
                 out_data_auth_code,
                 link_app_id,
                 link_app_type,
                 order_by,
                 read_employee,
                 data_auth_id,
                 out_data_auth_id,
                 data_own_organization,
                 current_candidate_ids,
                 display_name,
                 dimension_d1,
                 dimension_d2,
                 dimension_d3,
                 mc_currency,
                 mc_exchange_rate,
                 mc_functional_currency,
                 mc_exchange_rate_version,
                 origin_source,
                 sys_modified_time,
                 object_record_type,
                 out_data_own_department,
                 out_data_own_organization,
                 bi_sys_flag
         from stage_task where object_api_name='NewOpportunityObj' and bi_sys_flag=1
            )as stage_task_1
            RIGHT JOIN (select
                            id,
                            tenant_id,
                            record_type,
                            created_by,
                            create_time,
                            last_modified_by,
                            last_modified_time,
                            extend_obj_data_id,
                            package,
                            object_describe_id,
                            object_describe_api_name,
                            version,
                            is_deleted,
                            out_tenant_id,
                            out_owner,
                            stage_id,
                            stage_status,
                            start_time,
                            end_time,
                            source_workflow_id,
                            workflow_id,
                            workflow_instance_id,
                            entity_id,
                            object_id,
                            is_time_out,
                            fact_time_out_time,
                            accumulate_time,
                            name,
                            remind_latency,
                            data_own_department,
                            lock_user,
                            life_status_before_invalid,
                            lock_status,
                            lock_rule,
                            order_id,
                            oppor_stage_id,
                            data_auth_code,
                            change_type,
                            out_data_auth_code,
                            source_stage_id,
                            source_order_id,
                            order_by,
                            stage_field_api_name,
                            is_terminal,
                            instance_status,
                            stage_sub_status,
                            data_auth_id,
                            out_data_auth_id,
                            mc_currency,
                            mc_exchange_rate,
                            mc_functional_currency,
                            mc_exchange_rate_version,
                            data_own_organization,
                            display_name,
                            origin_source,
                            sys_modified_time,
                            dimension_d1,
                            dimension_d2,
                            dimension_d3,
                            bi_sys_flag,
                            bi_sys_batch_id
                        from stage_runtime where entity_id= 'NewOpportunityObj' AND bi_sys_flag=1 AND instance_status=''
            ) as stage_runtime_1 ON stage_task_1.tenant_id = stage_runtime_1.tenant_id AND stage_task_1.stage_id = stage_runtime_1.stage_id AND
                                    stage_task_1.object_data_id = stage_runtime_1.object_id AND
                                    stage_task_1.object_api_name = stage_runtime_1.entity_id AND
                                    stage_task_1.workflow_instance_id =stage_runtime_1.workflow_instance_id
                                    SETTINGS final = 1, optimize_move_to_prewhere_if_final = 1, join_use_nulls = 1;
    """;

  public static  String bi_data_sync_policy_log= """
    create table if not exists bi_data_sync_policy_log on cluster '{cluster}'
    (
    id String,
    tenant_id String comment '1端企业',
    policy_id String comment '同步策略id',
    log_type LowCardinality(String) comment '同步策略同步信息日志类型',
    msg String comment '同步策略异常信息',
    is_deleted UInt8,
    timestamp DateTime default now(),
    source_tenant_id String
    )
    engine = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', timestamp)
    ORDER BY (id, tenant_id, policy_id)
    SETTINGS index_granularity = 8192;
    """;

  public static String biz_user_bi_operation_ods= """
    CREATE TABLE if not exists biz_user_bi_operation_ods on cluster '{cluster}'
        (
            `id` String,
            `name` String,
            `tenant_id` String,
            `operator` String,
            `operate_type` String,
            `operator_dept` String,
            `operate_time` Int64,
            `api_name` String,
            `object_id` String,
            `resource_type` String,
            `resource_name` String,
            `app_name` String,
            `terminal_type` String,
            `target_rule` String,
            `last_modified_time` Int64,
            `last_modified_by` String,
            `create_time` Int64,
            `created_by` String,
            `data_own_department` String,
            `owner` String,
            `owner_dept` String,
            `connected_enterprise` String,
            `out_owner` String,
            `out_data_own_department` String,
    		    `out_data_own_organization` String,
            `data_create_time` DateTime64(9) DEFAULT now64(9),
            `sys_modified_time` Int64 DEFAULT toUnixTimestamp64Micro(now64(9)),
            `dt` String,
            `subscription_type` String,
            `auth_flag` String,
            `data_own_organization` String,
            `bi_sys_version` DateTime DEFAULT now()
        )
        ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}')
        PARTITION BY toYYYYMM(data_create_time)
        ORDER BY (tenant_id, id)
        TTL bi_sys_version + toIntervalDay(30)
        SETTINGS index_granularity = 8192;
    """;

  public static String biz_user_bi_operation= """
    CREATE TABLE if not exists biz_user_bi_operation on cluster '{cluster}'
        (
            `id` String,
            `name` String,
            `tenant_id` String,
            `operator` String,
            `operate_type` String,
            `operator_dept` String,
            `operate_time` Int64,
            `api_name` String,
            `object_id` String,
            `resource_type` String,
            `resource_name` String,
            `app_name` String,
            `terminal_type` String,
            `target_rule` String,
            `last_modified_time` Int64,
            `last_modified_by` String,
            `create_time` Int64,
            `created_by` String,
            `data_own_department` String,
            `owner` String,
            `owner_dept` String,
            `connected_enterprise` String,
            `out_owner` String,
            `out_tenant_id` String,
            `data_auth_code` String,
            `out_data_auth_code` String,
            `is_deleted` Int8,
            `bi_sys_flag` Int8,
            `bi_sys_batch_id` Int64,
            `bi_sys_is_deleted` UInt8,
            `bi_sys_version` DateTime DEFAULT now(),
            `sys_modified_time` Int64 DEFAULT toUnixTimestamp64Micro(now64(9)),
            `bi_sys_ods_part` String DEFAULT 's',
            `subscription_type` String,
            `auth_flag` String,
            `data_own_organization` String,
            `out_data_own_department` String,
    		`out_data_own_organization` String,
            INDEX idx_owner owner TYPE set(1000) GRANULARITY 1
        )
        ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_is_deleted)
        PARTITION BY bi_sys_ods_part
        ORDER BY (tenant_id, name, bi_sys_flag, id)
        TTL bi_sys_version + toIntervalWeek(1) WHERE bi_sys_ods_part = 'i'
        SETTINGS index_granularity = 8192;
    """;

  public static String dimNameData = """
    CREATE TABLE if not exists dim_name_data on cluster '{cluster}'
    (
        `id` String,
        `data_id` String,
        `describe_api_name` String,
        `tenant_id` String,
        `name` String,
        `lang` String,
        `last_modified_time` Int64,
        `i18n_key` String,
        `sys_modified_time` Int64,
        `bi_sys_flag` Int8,
        `bi_sys_batch_id` Int64,
        `bi_sys_is_deleted` UInt8,
        `bi_sys_version` DateTime DEFAULT now(),
        `timestamp` DateTime64(9) DEFAULT now64(9)
    )
    ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', timestamp, bi_sys_is_deleted)
    ORDER BY (tenant_id, data_id)
    SETTINGS index_granularity = 8192;
    """;

  public static String biAggLog= """
    create table if not exists bi_agg_log on cluster '{cluster}'
    (
        tenant_id    String comment '租户id',
        view_id      String comment '统计图去重key',
        view_version Int32 comment '图版本',
        batch_num    Int64 comment '批次',
        field_id     String comment '指标id',
        start_time   DateTime default now('Asia/Shanghai'),
        cost         Int64 comment '指标计算耗时',
        is_deleted   Int8     default 0
    ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}')
          ORDER BY (tenant_id, view_id, field_id)
          TTL start_time + toIntervalWeek(1)
          SETTINGS index_granularity = 8192;
    """;

  public static String OBJECT_DATA_LANG = """
                CREATE table if not exists object_data_lang on cluster '{cluster}' (
                	id String,
                	tenant_id String,
                	object_describe_api_name String,
                	data_id String,
                	create_time Int64,
                	last_modified_time Int64,
                	lang LowCardinality(String),
                	name_l String,
                    display_name_l String,
                    is_deleted Bool,
                    sys_modified_time Int64 NOT NULL DEFAULT last_modified_time * 1000,
                	value1_l String,
                	value2_l String,
                	value3_l String,
                	value4_l String,
                	value5_l String,
                	value6_l String,
                	value7_l String,
                	value8_l String,
                	value9_l String,
                	value10_l String,
                	value11_l String,
                	value12_l String,
                	value13_l String,
                	value14_l String,
                	value15_l String,
                	value16_l String,
                	value17_l String,
                	value18_l String,
                	value19_l String,
                	value20_l String,
                	value21_l String,
                	value22_l String,
                	value23_l String,
                	value24_l String,
                	value25_l String,
                	value26_l String,
                	value27_l String,
                	value28_l String,
                	value29_l String,
                	value30_l String,
                	value31_l String,
                	value32_l String,
                	value33_l String,
                	value34_l String,
                	value35_l String,
                	value36_l String,
                	value37_l String,
                	value38_l String,
                	value39_l String,
                	value40_l String,
                	value41_l String,
                	value42_l String,
                	value43_l String,
                	value44_l String,
                	value45_l String,
                	value46_l String,
                	value47_l String,
                	value48_l String,
                	value49_l String,
                	value50_l String,
                	value51_l String,
                	value52_l String,
                	value53_l String,
                	value54_l String,
                	value55_l String,
                	value56_l String,
                	value57_l String,
                	value58_l String,
                	value59_l String,
                	value60_l String,
                	value61_l String,
                	value62_l String,
                	value63_l String,
                	value64_l String,
                	value65_l String,
                	value66_l String,
                	value67_l String,
                	value68_l String,
                	value69_l String,
                	value70_l String,
                	value71_l String,
                	value72_l String,
                	value73_l String,
                	value74_l String,
                	value75_l String,
                	value76_l String,
                	value77_l String,
                	value78_l String,
                	value79_l String,
                	value80_l String,
                	value81_l String,
                	value82_l String,
                	value83_l String,
                	value84_l String,
                	value85_l String,
                	value86_l String,
                	value87_l String,
                	value88_l String,
                	value89_l String,
                	value90_l String,
                	value91_l String,
                	value92_l String,
                	value93_l String,
                	value94_l String,
                	value95_l String,
                	value96_l String,
                	value97_l String,
                	value98_l String,
                	value99_l String,
                	value100_l String,
                	value101_l String,
                	value102_l String,
                	value103_l String,
                	value104_l String,
                	value105_l String,
                	value106_l String,
                	value107_l String,
                	value108_l String,
                	value109_l String,
                	value110_l String,
                	value111_l String,
                	value112_l String,
                	value113_l String,
                	value114_l String,
                	value115_l String,
                	value116_l String,
                	value117_l String,
                	value118_l String,
                	value119_l String,
                	value120_l String,
                	value121_l String,
                	value122_l String,
                	value123_l String,
                	value124_l String,
                	value125_l String,
                	value126_l String,
                	value127_l String,
                	value128_l String,
                	value129_l String,
                	value130_l String,
                	value131_l String,
                	value132_l String,
                	value133_l String,
                	value134_l String,
                	value135_l String,
                	value136_l String,
                	value137_l String,
                	value138_l String,
                	value139_l String,
                	value140_l String,
                	value141_l String,
                	value142_l String,
                	value143_l String,
                	value144_l String,
                	value145_l String,
                	value146_l String,
                	value147_l String,
                	value148_l String,
                	value149_l String,
                	value150_l String,
                	value151_l String,
                	value152_l String,
                	value153_l String,
                	value154_l String,
                	value155_l String,
                	value156_l String,
                	value157_l String,
                	value158_l String,
                	value159_l String,
                	value160_l String,
                	value161_l String,
                	value162_l String,
                	value163_l String,
                	value164_l String,
                	value165_l String,
                	value166_l String,
                	value167_l String,
                	value168_l String,
                	value169_l String,
                	value170_l String,
                	value171_l String,
                	value172_l String,
                	value173_l String,
                	value174_l String,
                	value175_l String,
                	value176_l String,
                	value177_l String,
                	value178_l String,
                	value179_l String,
                	value180_l String,
                	value181_l String,
                	value182_l String,
                	value183_l String,
                	value184_l String,
                	value185_l String,
                	value186_l String,
                	value187_l String,
                	value188_l String,
                	value189_l String,
                	value190_l String,
                	value191_l String,
                	value192_l String,
                	value193_l String,
                	value194_l String,
                	value195_l String,
                	value196_l String,
                	value197_l String,
                	value198_l String,
                	value199_l String,
                	value200_l String,
                	value201_l String,
                	value202_l String,
                	value203_l String,
                	value204_l String,
                	value205_l String,
                	value206_l String,
                	value207_l String,
                	value208_l String,
                	value209_l String,
                	value210_l String,
                	value211_l String,
                	value212_l String,
                	value213_l String,
                	value214_l String,
                	value215_l String,
                	value216_l String,
                	value217_l String,
                	value218_l String,
                	value219_l String,
                	value220_l String,
                	value221_l String,
                	value222_l String,
                	value223_l String,
                	value224_l String,
                	value225_l String,
                	value226_l String,
                	value227_l String,
                	value228_l String,
                	value229_l String,
                	value230_l String,
                	value231_l String,
                	value232_l String,
                	value233_l String,
                	value234_l String,
                	value235_l String,
                	value236_l String,
                	value237_l String,
                	value238_l String,
                	value239_l String,
                	value240_l String,
                	value241_l String,
                	value242_l String,
                	value243_l String,
                	value244_l String,
                	value245_l String,
                	value246_l String,
                	value247_l String,
                	value248_l String,
                	value249_l String,
                	value250_l String,
                	value251_l String,
                	value252_l String,
                	value253_l String,
                	value254_l String,
                	value255_l String,
                	value256_l String,
                	value257_l String,
                	value258_l String,
                	value259_l String,
                	value260_l String,
                	value261_l String,
                	value262_l String,
                	value263_l String,
                	value264_l String,
                	value265_l String,
                	value266_l String,
                	value267_l String,
                	value268_l String,
                	value269_l String,
                	value270_l String,
                	value271_l String,
                	value272_l String,
                	value273_l String,
                	value274_l String,
                	value275_l String,
                	value276_l String,
                	value277_l String,
                	value278_l String,
                	value279_l String,
                	value280_l String,
                	value281_l String,
                	value282_l String,
                	value283_l String,
                	value284_l String,
                	value285_l String,
                	value286_l String,
                	value287_l String,
                	value288_l String,
                	value289_l String,
                	value290_l String,
                	value291_l String,
                	value292_l String,
                	value293_l String,
                	value294_l String,
                	value295_l String,
                	value296_l String,
                	value297_l String,
                	value298_l String,
                	value299_l String,
                	value300_l String,
                	value301_l String,
                	value302_l String,
                	value303_l String,
                	value304_l String,
                	value305_l String,
                	value306_l String,
                	value307_l String,
                	value308_l String,
                	value309_l String,
                	value310_l String,
                	value311_l String,
                	value312_l String,
                	value313_l String,
                	value314_l String,
                	value315_l String,
                	value316_l String,
                	value317_l String,
                	value318_l String,
                	value319_l String,
                	value320_l String,
                	value321_l String,
                	value322_l String,
                	value323_l String,
                	value324_l String,
                	value325_l String,
                	value326_l String,
                	value327_l String,
                	value328_l String,
                	value329_l String,
                	value330_l String,
                	value331_l String,
                	value332_l String,
                	value333_l String,
                	value334_l String,
                	value335_l String,
                	value336_l String,
                	value337_l String,
                	value338_l String,
                	value339_l String,
                	value340_l String,
                	value341_l String,
                	value342_l String,
                	value343_l String,
                	value344_l String,
                	value345_l String,
                	value346_l String,
                	value347_l String,
                	value348_l String,
                	value349_l String,
                	value350_l String,
                	value351_l String,
                	value352_l String,
                	value353_l String,
                	value354_l String,
                	value355_l String,
                	value356_l String,
                	value357_l String,
                	value358_l String,
                	value359_l String,
                	value360_l String,
                	value361_l String,
                	value362_l String,
                	value363_l String,
                	value364_l String,
                	value365_l String,
                	value366_l String,
                	value367_l String,
                	value368_l String,
                	value369_l String,
                	value370_l String,
                	value371_l String,
                	value372_l String,
                	value373_l String,
                	value374_l String,
                	value375_l String,
                	value376_l String,
                	value377_l String,
                	value378_l String,
                	value379_l String,
                	value380_l String,
                	value381_l String,
                	value382_l String,
                	value383_l String,
                	value384_l String,
                	value385_l String,
                	value386_l String,
                	value387_l String,
                	value388_l String,
                	value389_l String,
                	value390_l String,
                	value391_l String,
                	value392_l String,
                	value393_l String,
                	value394_l String,
                	value395_l String,
                	value396_l String,
                	value397_l String,
                	value398_l String,
                	value399_l String,
                	value400_l String,
                	value401_l String,
                	value402_l String,
                	value403_l String,
                	value404_l String,
                	value405_l String,
                	value406_l String,
                	value407_l String,
                	value408_l String,
                	value409_l String,
                	value410_l String,
                	value411_l String,
                	value412_l String,
                	value413_l String,
                	value414_l String,
                	value415_l String,
                	value416_l String,
                	value417_l String,
                	value418_l String,
                	value419_l String,
                	value420_l String,
                	value421_l String,
                	value422_l String,
                	value423_l String,
                	value424_l String,
                	value425_l String,
                	value426_l String,
                	value427_l String,
                	value428_l String,
                	value429_l String,
                	value430_l String,
                	value431_l String,
                	value432_l String,
                	value433_l String,
                	value434_l String,
                	value435_l String,
                	value436_l String,
                	value437_l String,
                	value438_l String,
                	value439_l String,
                	value440_l String,
                	value441_l String,
                	value442_l String,
                	value443_l String,
                	value444_l String,
                	value445_l String,
                	value446_l String,
                	value447_l String,
                	value448_l String,
                	value449_l String,
                	value450_l String,
                	value451_l String,
                	value452_l String,
                	value453_l String,
                	value454_l String,
                	value455_l String,
                	value456_l String,
                	value457_l String,
                	value458_l String,
                	value459_l String,
                	value460_l String,
                	value461_l String,
                	value462_l String,
                	value463_l String,
                	value464_l String,
                	value465_l String,
                	value466_l String,
                	value467_l String,
                	value468_l String,
                	value469_l String,
                	value470_l String,
                	value471_l String,
                	value472_l String,
                	value473_l String,
                	value474_l String,
                	value475_l String,
                	value476_l String,
                	value477_l String,
                	value478_l String,
                	value479_l String,
                	value480_l String,
                	value481_l String,
                	value482_l String,
                	value483_l String,
                	value484_l String,
                	value485_l String,
                	value486_l String,
                	value487_l String,
                	value488_l String,
                	value489_l String,
                	value490_l String,
                	value491_l String,
                	value492_l String,
                	value493_l String,
                	value494_l String,
                	value495_l String,
                	value496_l String,
                	value497_l String,
                	value498_l String,
                	value499_l String,
                	value500_l String,
                    bi_sys_flag Int8 ,
                    bi_sys_batch_id Int64 ,
                    bi_sys_is_deleted UInt8 ,
                    bi_sys_version DateTime DEFAULT now() ,
                	bi_sys_ods_part	String	DEFAULT	's'
                )
                ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version, bi_sys_is_deleted)
                PARTITION BY bi_sys_ods_part
                ORDER BY (tenant_id, object_describe_api_name, lang, bi_sys_flag, data_id)
                TTL bi_sys_version + INTERVAL 1 MONTH DELETE WHERE bi_sys_ods_part = 's' AND  (bi_sys_flag = 0 OR is_deleted IN (-1, -2)),
                bi_sys_version + INTERVAL 3 DAY DELETE WHERE bi_sys_ods_part in ('i','c')
                SETTINGS index_granularity = 8192;
          """;
  public static String OBJECT_DATA = """
                CREATE TABLE IF NOT EXISTS object_data on cluster '{cluster}' (
                  `_id` String,
                  `tenant_id` String,
                  `object_describe_api_name` String,
                  `object_describe_id` String,
                  `package` String,
                  `created_by` String,
                  `last_modified_by` String,
                  `version` Nullable(Int32),
                  `create_time` Int64,
                  `last_modified_time` Int64,
                  `name` String,
                  `record_type` String,
                  `is_deleted` Int16,
                  `value0` String,
                  `value1` String,
                  `value2` String,
                  `value3` String,
                  `value4` String,
                  `value5` String,
                  `value6` String,
                  `value7` String,
                  `value8` String,
                  `value9` String,
                  `value10` String,
                  `value11` String,
                  `value12` String,
                  `value13` String,
                  `value14` String,
                  `value15` String,
                  `value16` String,
                  `value17` String,
                  `value18` String,
                  `value19` String,
                  `value20` String,
                  `value21` String,
                  `value22` String,
                  `value23` String,
                  `value24` String,
                  `value25` String,
                  `value26` String,
                  `value27` String,
                  `value28` String,
                  `value29` String,
                  `value30` String,
                  `value31` String,
                  `value32` String,
                  `value33` String,
                  `value34` String,
                  `value35` String,
                  `value36` String,
                  `value37` String,
                  `value38` String,
                  `value39` String,
                  `value40` String,
                  `value41` String,
                  `value42` String,
                  `value43` String,
                  `value44` String,
                  `value45` String,
                  `value46` String,
                  `value47` String,
                  `value48` String,
                  `value49` String,
                  `value50` String,
                  `value51` String,
                  `value52` String,
                  `value53` String,
                  `value54` String,
                  `value55` String,
                  `value56` String,
                  `value57` String,
                  `value58` String,
                  `value59` String,
                  `value60` String,
                  `value61` String,
                  `value62` String,
                  `value63` String,
                  `value64` String,
                  `value65` String,
                  `value66` String,
                  `value67` String,
                  `value68` String,
                  `value69` String,
                  `value70` String,
                  `value71` String,
                  `value72` String,
                  `value73` String,
                  `value74` String,
                  `value75` String,
                  `value76` String,
                  `value77` String,
                  `value78` String,
                  `value79` String,
                  `value80` String,
                  `value81` String,
                  `value82` String,
                  `value83` String,
                  `value84` String,
                  `value85` String,
                  `value86` String,
                  `value87` String,
                  `value88` String,
                  `value89` String,
                  `value90` String,
                  `value91` String,
                  `value92` String,
                  `value93` String,
                  `value94` String,
                  `value95` String,
                  `value96` String,
                  `value97` String,
                  `value98` String,
                  `value99` String,
                  `value100` String,
                  `value101` String,
                  `value102` String,
                  `value103` String,
                  `value104` String,
                  `value105` String,
                  `value106` String,
                  `value107` String,
                  `value108` String,
                  `value109` String,
                  `value110` String,
                  `value111` String,
                  `value112` String,
                  `value113` String,
                  `value114` String,
                  `value115` String,
                  `value116` String,
                  `value117` String,
                  `value118` String,
                  `value119` String,
                  `value120` String,
                  `value121` String,
                  `value122` String,
                  `value123` String,
                  `value124` String,
                  `value125` String,
                  `value126` String,
                  `value127` String,
                  `value128` String,
                  `value129` String,
                  `value130` String,
                  `value131` String,
                  `value132` String,
                  `value133` String,
                  `value134` String,
                  `value135` String,
                  `value136` String,
                  `value137` String,
                  `value138` String,
                  `value139` String,
                  `value140` String,
                  `value141` String,
                  `value142` String,
                  `value143` String,
                  `value144` String,
                  `value145` String,
                  `value146` String,
                  `value147` String,
                  `value148` String,
                  `value149` String,
                  `value150` String,
                  `value151` String,
                  `value152` String,
                  `value153` String,
                  `value154` String,
                  `value155` String,
                  `value156` String,
                  `value157` String,
                  `value158` String,
                  `value159` String,
                  `value160` String,
                  `value161` String,
                  `value162` String,
                  `value163` String,
                  `value164` String,
                  `value165` String,
                  `value166` String,
                  `value167` String,
                  `value168` String,
                  `value169` String,
                  `value170` String,
                  `value171` String,
                  `value172` String,
                  `value173` String,
                  `value174` String,
                  `value175` String,
                  `value176` String,
                  `value177` String,
                  `value178` String,
                  `value179` String,
                  `value180` String,
                  `value181` String,
                  `value182` String,
                  `value183` String,
                  `value184` String,
                  `value185` String,
                  `value186` String,
                  `value187` String,
                  `value188` String,
                  `value189` String,
                  `value190` String,
                  `value191` String,
                  `value192` String,
                  `value193` String,
                  `value194` String,
                  `value195` String,
                  `value196` String,
                  `value197` String,
                  `value198` String,
                  `value199` String,
                  `value200` String,
                  `value201` String,
                  `value202` String,
                  `value203` String,
                  `value204` String,
                  `value205` String,
                  `value206` String,
                  `value207` String,
                  `value208` String,
                  `value209` String,
                  `value210` String,
                  `value211` String,
                  `value212` String,
                  `value213` String,
                  `value214` String,
                  `value215` String,
                  `value216` String,
                  `value217` String,
                  `value218` String,
                  `value219` String,
                  `value220` String,
                  `value221` String,
                  `value222` String,
                  `value223` String,
                  `value224` String,
                  `value225` String,
                  `value226` String,
                  `value227` String,
                  `value228` String,
                  `value229` String,
                  `value230` String,
                  `value231` String,
                  `value232` String,
                  `value233` String,
                  `value234` String,
                  `value235` String,
                  `value236` String,
                  `value237` String,
                  `value238` String,
                  `value239` String,
                  `value240` String,
                  `value241` String,
                  `value242` String,
                  `value243` String,
                  `value244` String,
                  `value245` String,
                  `value246` String,
                  `value247` String,
                  `value248` String,
                  `value249` String,
                  `value250` String,
                  `value251` String,
                  `value252` String,
                  `value253` String,
                  `value254` String,
                  `value255` String,
                  `value256` String,
                  `value257` String,
                  `value258` String,
                  `value259` String,
                  `value260` String,
                  `value261` String,
                  `value262` String,
                  `value263` String,
                  `value264` String,
                  `value265` String,
                  `value266` String,
                  `value267` String,
                  `value268` String,
                  `value269` String,
                  `value270` String,
                  `value271` String,
                  `value272` String,
                  `value273` String,
                  `value274` String,
                  `value275` String,
                  `value276` String,
                  `value277` String,
                  `value278` String,
                  `value279` String,
                  `value280` String,
                  `value281` String,
                  `value282` String,
                  `value283` String,
                  `value284` String,
                  `value285` String,
                  `value286` String,
                  `value287` String,
                  `value288` String,
                  `value289` String,
                  `value290` String,
                  `value291` String,
                  `value292` String,
                  `value293` String,
                  `value294` String,
                  `value295` String,
                  `value296` String,
                  `value297` String,
                  `value298` String,
                  `value299` String,
                  `value300` String,
                  `value301` String,
                  `value302` String,
                  `value303` String,
                  `value304` String,
                  `value305` String,
                  `value306` String,
                  `value307` String,
                  `value308` String,
                  `value309` String,
                  `value310` String,
                  `value311` String,
                  `value312` String,
                  `value313` String,
                  `value314` String,
                  `value315` String,
                  `value316` String,
                  `value317` String,
                  `value318` String,
                  `value319` String,
                  `value320` String,
                  `value321` String,
                  `value322` String,
                  `value323` String,
                  `value324` String,
                  `value325` String,
                  `value326` String,
                  `value327` String,
                  `value328` String,
                  `value329` String,
                  `value330` String,
                  `value331` String,
                  `value332` String,
                  `value333` String,
                  `value334` String,
                  `value335` String,
                  `value336` String,
                  `value337` String,
                  `value338` String,
                  `value339` String,
                  `value340` String,
                  `value341` String,
                  `value342` String,
                  `value343` String,
                  `value344` String,
                  `value345` String,
                  `value346` String,
                  `value347` String,
                  `value348` String,
                  `value349` String,
                  `value350` String,
                  `value351` String,
                  `value352` String,
                  `value353` String,
                  `value354` String,
                  `value355` String,
                  `value356` String,
                  `value357` String,
                  `value358` String,
                  `value359` String,
                  `value360` String,
                  `value361` String,
                  `value362` String,
                  `value363` String,
                  `value364` String,
                  `value365` String,
                  `value366` String,
                  `value367` String,
                  `value368` String,
                  `value369` String,
                  `value370` String,
                  `value371` String,
                  `value372` String,
                  `value373` String,
                  `value374` String,
                  `value375` String,
                  `value376` String,
                  `value377` String,
                  `value378` String,
                  `value379` String,
                  `value380` String,
                  `value381` String,
                  `value382` String,
                  `value383` String,
                  `value384` String,
                  `value385` String,
                  `value386` String,
                  `value387` String,
                  `value388` String,
                  `value389` String,
                  `value390` String,
                  `value391` String,
                  `value392` String,
                  `value393` String,
                  `value394` String,
                  `value395` String,
                  `value396` String,
                  `value397` String,
                  `value398` String,
                  `value399` String,
                  `value400` String,
                  `value401` String,
                  `value402` String,
                  `value403` String,
                  `value404` String,
                  `value405` String,
                  `value406` String,
                  `value407` String,
                  `value408` String,
                  `value409` String,
                  `value410` String,
                  `value411` String,
                  `value412` String,
                  `value413` String,
                  `value414` String,
                  `value415` String,
                  `value416` String,
                  `value417` String,
                  `value418` String,
                  `value419` String,
                  `value420` String,
                  `value421` String,
                  `value422` String,
                  `value423` String,
                  `value424` String,
                  `value425` String,
                  `value426` String,
                  `value427` String,
                  `value428` String,
                  `value429` String,
                  `value430` String,
                  `value431` String,
                  `value432` String,
                  `value433` String,
                  `value434` String,
                  `value435` String,
                  `value436` String,
                  `value437` String,
                  `value438` String,
                  `value439` String,
                  `value440` String,
                  `value441` String,
                  `value442` String,
                  `value443` String,
                  `value444` String,
                  `value445` String,
                  `value446` String,
                  `value447` String,
                  `value448` String,
                  `value449` String,
                  `value450` String,
                  `value451` String,
                  `value452` String,
                  `value453` String,
                  `value454` String,
                  `value455` String,
                  `value456` String,
                  `value457` String,
                  `value458` String,
                  `value459` String,
                  `value460` String,
                  `value461` String,
                  `value462` String,
                  `value463` String,
                  `value464` String,
                  `value465` String,
                  `value466` String,
                  `value467` String,
                  `value468` String,
                  `value469` String,
                  `value470` String,
                  `value471` String,
                  `value472` String,
                  `value473` String,
                  `value474` String,
                  `value475` String,
                  `value476` String,
                  `value477` String,
                  `value478` String,
                  `value479` String,
                  `value480` String,
                  `value481` String,
                  `value482` String,
                  `value483` String,
                  `value484` String,
                  `value485` String,
                  `value486` String,
                  `value487` String,
                  `value488` String,
                  `value489` String,
                  `value490` String,
                  `value491` String,
                  `value492` String,
                  `value493` String,
                  `value494` String,
                  `value495` String,
                  `value496` String,
                  `value497` String,
                  `value498` String,
                  `value499` String,
                  `value500` String,
                  `out_tenant_id` String,
                  `out_owner` String,
                  `owner` String,
                  `data_own_department` String,
                  `change_type` Nullable(Int32),
                  `data_auth_code` String,
                  `out_data_auth_code` String,
                  `order_by` Nullable(Int32),
                  `data_auth_id` Nullable(Int32),
                  `out_data_auth_id` Nullable(Int32),
                  `dimension_d1` Array(String),
                  `dimension_d2` Array(String),
                  `dimension_d3` Array(String),
                  `data_own_organization` String,
                  `mc_currency` String,
                  `mc_exchange_rate` Nullable(Decimal(38, 20)),
                  `mc_functional_currency` String,
                  `mc_exchange_rate_version` String,
                  `value501` String,
                  `value502` String,
                  `value503` String,
                  `value504` String,
                  `value505` String,
                  `value506` String,
                  `value507` String,
                  `value508` String,
                  `value509` String,
                  `value510` String,
                  `value511` String,
                  `value512` String,
                  `value513` String,
                  `value514` String,
                  `value515` String,
                  `value516` String,
                  `value517` String,
                  `value518` String,
                  `value519` String,
                  `value520` String,
                  `value521` String,
                  `value522` String,
                  `value523` String,
                  `value524` String,
                  `value525` String,
                  `value526` String,
                  `value527` String,
                  `value528` String,
                  `value529` String,
                  `value530` String,
                  `value531` String,
                  `value532` String,
                  `value533` String,
                  `value534` String,
                  `value535` String,
                  `value536` String,
                  `value537` String,
                  `value538` String,
                  `value539` String,
                  `value540` String,
                  `value541` String,
                  `value542` String,
                  `value543` String,
                  `value544` String,
                  `value545` String,
                  `value546` String,
                  `value547` String,
                  `value548` String,
                  `value549` String,
                  `value550` String,
                  `value551` String,
                  `value552` String,
                  `value553` String,
                  `value554` String,
                  `value555` String,
                  `value556` String,
                  `value557` String,
                  `value558` String,
                  `value559` String,
                  `value560` String,
                  `value561` String,
                  `value562` String,
                  `value563` String,
                  `value564` String,
                  `value565` String,
                  `value566` String,
                  `value567` String,
                  `value568` String,
                  `value569` String,
                  `value570` String,
                  `value571` String,
                  `value572` String,
                  `value573` String,
                  `value574` String,
                  `value575` String,
                  `value576` String,
                  `value577` String,
                  `value578` String,
                  `value579` String,
                  `value580` String,
                  `value581` String,
                  `value582` String,
                  `value583` String,
                  `value584` String,
                  `value585` String,
                  `value586` String,
                  `value587` String,
                  `value588` String,
                  `value589` String,
                  `value590` String,
                  `value591` String,
                  `value592` String,
                  `value593` String,
                  `value594` String,
                  `value595` String,
                  `value596` String,
                  `value597` String,
                  `value598` String,
                  `value599` String,
                  `value600` String,
                  `display_name` String,
                  `origin_source` String,
                  `sys_modified_time` Int64 DEFAULT last_modified_time * 1000,
                  `industry_ext` String,
                  `biz_reg_name` Nullable(Bool),
                  `out_data_own_organization` String,
                  `out_data_own_department` String,
                  `version_number` String,
                  `changed_by` String,
                  `changed_time` Nullable(Int64),
                  `changed_reason` String,
                  `changed_status` String,
                  `bi_sys_flag` Int8,
                  `bi_sys_batch_id` Int64,
                  `bi_sys_is_deleted` UInt8,
                  `bi_sys_version` DateTime DEFAULT now(),
                  `bi_sys_ods_part` String DEFAULT 's',
                  `related_approval_instance_obj_id` String,
                  INDEX idx_owner owner TYPE
                  set(1000) GRANULARITY 1
                ) ENGINE = ReplicatedReplacingMergeTree(
                  '/clickhouse/tables/{shard}/{database}/{uuid}/',
                  '{replica}',
                  bi_sys_version,
                  bi_sys_is_deleted
                ) PARTITION BY bi_sys_ods_part
                ORDER BY
                  (
                    tenant_id,
                    object_describe_api_name,
                    bi_sys_flag,
                    _id
                  ) TTL bi_sys_version + toIntervalMonth(1)
                WHERE
                  (bi_sys_ods_part = 's')
                  AND (
                    (bi_sys_flag = 0)
                    OR (is_deleted IN (-1, -2))
                  ),
                  bi_sys_version + toIntervalDay(3)
                WHERE
                  bi_sys_ods_part IN ('i', 'c') SETTINGS index_granularity = 8192;
          """;
}
