package com.fxiaoke.bi.warehouse.dws.transform.db.entity;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Date;

@Table(name = "stat_view_property")
@Data
public class StatViewPropertyDO {
  @Id
  @Column(name = "property_id")
  private String propertyId;
  @Column(name = "view_id")
  private String viewId;
  @Column(name = "chart_property_id")
  private String chartPropertyId;
  @Column(name = "value")
  private String value;
  @Column(name = "creator")
  private int creator;
  @Column(name = "create_time")
  private Date createTime;
  @Column(name = "updator")
  private int updator;
  @Column(name = "update_time")
  private Date updateTime;
  @Column(name = "is_delete")
  private int isDelete;
  @Column(name = "ei")
  private int ei;

  /**
   * 从stat_view_property 获取到数据并从value
   * 字段解析出 field_id
   *
   * @return
   */
  public String findFieldIdFromValue() {
    if (StringUtils.isNotBlank(value)) {
      try {
        JSONObject jsonObject = JSON.parseObject(this.value);
        if (jsonObject != null) {
          Object value = jsonObject.get("value");
          if (value instanceof JSONObject valueObj) {
            return valueObj.getString("fieldId") != null ?
              valueObj.getString("fieldId") :
              valueObj.getString("fieldID");
          }
        }
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }
    return null;
  }
}
