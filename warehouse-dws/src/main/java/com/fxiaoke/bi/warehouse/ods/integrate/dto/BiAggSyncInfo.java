package com.fxiaoke.bi.warehouse.ods.integrate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BiAggSyncInfo {

    /**
     * 主键
     */
    private String id;

    /**
     * 待同步的1端企业
     */
    private String tenantId;

    /**
     * 同步的下游企业集合
     */
    private String downstreamEis;

    /**
     * 同步状态
     */
    private Integer status;

    /**
     * 同步批次号
     */
    private Long batchNum;

    /**
     * 最后一次同步时间
     */
    private long lastSyncTime;

    /**
     * 是否被删除 0没有删除 1删除
     */
    private int isDeleted;

    /**
     * 版本号
     */
    private int version;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 最后一次
     */
    private long lastModifiedTime;
}
