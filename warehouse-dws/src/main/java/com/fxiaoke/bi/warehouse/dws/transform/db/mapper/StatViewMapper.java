package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/04/06
 * @Description
 */
@Mapper
public interface StatViewMapper extends IBatchMapper<StatViewDO>, ITenant<StatViewMapper> {
  @Select("select * from stat_view where (ei=#{ei} or ei=-1) and view_id=#{view_id}")
  StatViewDO queryStatView(@Param("ei") int ei, @Param("view_id") String viewId);

  @Select("select * from stat_view where (ei=#{ei} or ei=-1) and is_delete=0 and update_time>'${fromDateTime}'")
  List<StatViewDO> batchQueryStatViewByEi(@Param("ei") int ei, @Param("fromDateTime") String fromDateTime);

  @Select("select * from stat_view where (ei=#{ei} or ei=-1) and schema_id in(${schemaIds}) and is_delete=0 and update_time>'${fromDateTime}'")
  List<StatViewDO> batchQueryStatViewByEiSchemaId(@Param("ei") int ei, @Param("schemaIds") String schemaIds, @Param("fromDateTime") String fromDateTime);

  @Select("select *  from stat_view where (ei=#{ei} or ei=-1) and view_id=any(array[#{viewIds}])")
  List<StatViewDO> batchQueryStatViewByViewIds(@Param("ei") Integer ei, @Param("viewIds") String[] viewId);

  @Select("select ei,view_id,view_name,template_id,schema_id,is_delete,source,goal_theme_api_name,data_source " +
    " from stat_view where ei=#{ei} or ei= -1  and is_delete=0 ")
  List<StatViewDO> queryAllStatViewByTenantId(@Param("ei") Integer ei);

  /**
   * 批量查询用户自定义图
   * @param ei 租户id
   * @param fromDateTime 起始时间
   * @return
   */
  @Select("select * from stat_view where ei=#{ei} and is_delete=0 and update_time>'${fromDateTime}'")
  List<StatViewDO> batchQueryCustomStatViewByEi(@Param("ei") int ei, @Param("fromDateTime") String fromDateTime);

  /**
   * 批量查询目标图
   * @param ei 租户id
   * @param fromDateTime 起始时间
   * @return
   */
  @Select("select * from stat_view where (ei=#{ei} or ei=-1) and is_delete=0 and update_time>'${fromDateTime}' and " +
    "schema_id not in ('BI_5d9ff333e9a0d3741c9f09fd', 'BI_5d9ff333e9a0d3741c9f09fc', 'BI_5d9ff333e9a0d3741c9f09fe') and " +
    "view_id in (select view_id from stat_view_filter where ei=#{ei} and " +
    "field_id in ('BI_5d9ff3331b9ad40001873713', 'BI_5d9ff3331b9ad40001873778', 'BI_f1763fde9d8c8be29674150b86ce5e9f') and is_delete=0)")
  List<StatViewDO> batchQueryGoalStatViewByEi(@Param("ei") int ei, @Param("fromDateTime") String fromDateTime);

}
