package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.fxiaoke.bi.warehouse.common.util.SQLUtil;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtDimensionDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.BIMtDimensionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> zzh
 * @createTime : [2024/8/23 14:45]
 */
@Service
public class BiMtDimensionDao {

  @Autowired
  private BIMtDimensionMapper biMtDimensionMapper;

  public List<BIMtDimensionDO> batchQueryDimensionByIds(String ei, Set<String> dimensionIds, Integer[] allStatus) {
    List<String> idList = List.copyOf(dimensionIds);
    return biMtDimensionMapper.setTenantId(ei).findDimByDimensionIds(ei, SQLUtil.generateInExpress(idList), allStatus);
  }
}
