package com.fxiaoke.bi.warehouse.dws.transform.db.mapper.handler;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.bean.TopologyTableAggDownStream;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @Author: zhaomh
 * @Description:
 * @Date: Created in 2024/4/11
 * @Modified By:
 */
public class AggDownStreamHandler extends BaseTypeHandler<TopologyTableAggDownStream> {
  @Override
  public void setNonNullParameter(PreparedStatement ps, int i, TopologyTableAggDownStream parameter, JdbcType jdbcType) throws SQLException {
    ps.setString(i, JSON.toJSONString(parameter));
  }

  @Override
  public TopologyTableAggDownStream getNullableResult(ResultSet rs, String columnName) throws SQLException {
    String st = rs.getString(columnName);
    return JSON.parseObject(st, TopologyTableAggDownStream.class);
  }

  @Override
  public TopologyTableAggDownStream getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
    String st = rs.getString(columnIndex);
    return JSON.parseObject(st, TopologyTableAggDownStream.class);
  }

  @Override
  public TopologyTableAggDownStream getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
    String st = cs.getString(columnIndex);
    return JSON.parseObject(st, TopologyTableAggDownStream.class);
  }
}
