package com.fxiaoke.bi.warehouse.dws.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Author:jief
 * @Date:2024/8/6
 */
@Data
@Table(name = "db_agg_info")
public class DBAggInfoDO {
  @Id
  @Column(name = "id")
  private String id;
  @Column(name = "sync_flows")
  private String syncFlows;
  @Column(name = "sync_batch_nums")
  private String syncBatchNums;
  @Column(name = "create_time")
  private long createTime;
  @Column(name = "last_modified_time")
  private long lastModifiedTime;
  @Column(name = "status")
  private Integer status;
  @Column(name = "version")
  private Integer version;
  @Column(name = "api_name_ei_map")
  private String apiNameEiMap;
  @Column(name = "is_deleted")
  private int isDeleted;
  /**
   * 是否开启计算分区
   */
  @Column(name = "allow_cal_partition")
  private int allowCalPartition;
  /**
   * 是否开启paas2bi同步
   */
  @Column(name = "allow_paas2bi_status")
  private int allowPaas2biStatus;
  /**
   * 增量同步的sys_modified_time范围
   */
  @Column(name = "inc_sys_modified_time_range")
  private String incSysModifiedTimeRange;
}
