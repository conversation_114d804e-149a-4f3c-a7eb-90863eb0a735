package com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 20240924
 * @desc 1+N跨租户同步策略表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BiDataSyncPolicyDo {

    /**
     * 策略id
     */
    private String policyId;

    /**
     * 策略名称
     */
    private String policyName;

    /**
     * 策略描述
     */
    private String policyDesc;

    /**
     * 目标企业
     */
    private String tenantId;

    /**
     * 数据源企业 企业/企业组
     */
    private String dataSourceEnterprise;

    /**
     * 同步主题
     */
    private String syncStatSchemaId;

    /**
     * N端统计图指标映射到1端主题指标规则
     */
    private String aggMappingRule;

    /**
     * 策略状态 0表示停用 1表示启用
     */
    private int status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private long lastModifiedTime;

    /**
     * 是否删除
     */
    private int isDeleted;
}
