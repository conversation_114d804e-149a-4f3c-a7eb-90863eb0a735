package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.bean.TopologyTableAggDownStream;
import com.fxiaoke.bi.warehouse.common.db.entity.StatFieldStatusEnum;
import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.common.util.*;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.StatFieldMapper;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import com.fxiaoke.bi.warehouse.dws.model.StatFieldArg;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.sqlgenerator.MtTagViewTable;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.*;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.*;
import com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper.GoalRuleMapper;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.Pair;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author:jief
 * @Date:2023/8/16
 */
@Slf4j
@Repository
public class StatViewDao {
  @Autowired
  private StatViewMapper statViewMapper;
  @Autowired
  private StatViewFieldMapper statViewFieldMapper;
  @Autowired
  private StatViewFilterMapper statViewFilterMapper;
  @Autowired
  private StatCalcFieldMapper statCalcFieldMapper;
  @Autowired
  private StatViewPropertyMapper statViewPropertyMapper;
  @Autowired
  private StatFieldMapper statFieldMapper;
  @Autowired
  private MybatisTenantPolicy mybatisTenantPolicy;
  @Autowired
  private AggRuleMapper aggRuleMapper;
  @Autowired
  private GoalRuleMapper goalRuleMapper;
  @Autowired
  private StatDimFieldInfoMapper statDimFieldInfoMapper;
  @Autowired
  private StatSchemaMapper statSchemaMapper;
  @Autowired
  private UdfObjFieldMapper udfObjFieldMapper;
  @Resource
  private MappingService mappingService;
  @Resource
  private PgDataSource pgDataSource;
  @Resource
  private BIMtDimensionMapper bimtDimensionMapper;

  //ch 强制添加的固定维度 如：owner，data_auth_code 等
  private Set<String> forceDbFieldNames = Sets.newHashSet();
  private static Map<String, String> STAT_SPE_FIELDID_TO_FIELD_MAP = Maps.newHashMap();
  private static Set<String> USER_SEGMENT_FILTER_FIELD_ID_SET = Sets.newHashSet();

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
      String dimFixField = config.get("new_agg_data_fix_dim", "");
      forceDbFieldNames = Splitter.on(",").splitToStream(dimFixField).collect(Collectors.toSet());
      //统计图特殊字段
      STAT_SPE_FIELDID_TO_FIELD_MAP = Splitter.on(',')
                                              .trimResults()
                                              .omitEmptyStrings()
                                              .withKeyValueSeparator(":")
                                              .split(config.get("stat_spe_fieldid_to_field_map", ""));
    });
    ConfigFactory.getConfig("fs-bi-common", config -> {
      String eventDataClickStatFieldIdList = config.get("eventDataClickStatFieldIdList", "");
      USER_SEGMENT_FILTER_FIELD_ID_SET = Splitter.on(",").splitToStream(eventDataClickStatFieldIdList).collect(Collectors.toSet());
    });
  }

  /**
   * 根据租户反查统计图对象
   * @param tenantId 租户id
   * @param viewId 图id
   * @return
   */
  public StatViewDO queryStatView(String tenantId, String viewId) {
    return statViewMapper.setTenantId("-1").queryStatView(Integer.parseInt(tenantId), viewId);
  }

  /**
   * 计算该统计图中是否包含目标规则
   * @param tenantId
   * @param viewId
   * @return
   */
  public int countOfGoalFields(String tenantId, String viewId) {
    return statViewFilterMapper.setTenantId("-1")
                               .countOfGoalFields(Integer.parseInt(tenantId), viewId, Constants.OLD_STAT_VIEW_GOAL_RULE_IDS.toArray(new String[0]));
  }

  /**
   * 批量统计统计图中包含目标规则的图
   * @param tenantId
   * @param viewIds
   * @return
   */
  public List<String> queryViewIdsGoalFields(String tenantId, List<String> viewIds) {
    return statViewFilterMapper.setTenantId("-1")
                               .queryViewIdsGoalFields(Integer.parseInt(tenantId), viewIds.toArray(new String[0]), Constants.OLD_STAT_VIEW_GOAL_RULE_IDS.toArray(new String[0]));
  }

  /**
   * 根据租户id批量查询所有统计图
   *
   * @param tenantId     租户id
   * @param fromDateTime 起始日期
   * @return
   */
  public List<StatViewDO> batchQueryStatViewByEi(String tenantId, String fromDateTime) {
    List<StatViewDO> statViewDOList = statViewMapper.setTenantId("-1")
                                                    .batchQueryStatViewByEi(Integer.parseInt(tenantId), fromDateTime);
    if (CollectionUtils.isEmpty(statViewDOList)) {
      log.warn("batchQueryStatViewByEi statView tenantId:{},-1,fromDateTime:{},empty", tenantId, fromDateTime);
    }
    return statViewDOList;
  }

  /**
   * 根据租户id批量查询所有包含目标的灰度多维度目标之前的统计图
   *
   * @param tenantId     租户id
   * @param fromDateTime 起始日期
   * @return
   */
  public List<StatViewDO> batchQueryGoalStatViewByEi(String tenantId, String fromDateTime) {
    List<StatViewDO> statViewDOList = statViewMapper.setTenantId("-1")
                                                    .batchQueryGoalStatViewByEi(Integer.parseInt(tenantId), fromDateTime);
    if (CollectionUtils.isEmpty(statViewDOList)) {
      log.warn("batchQueryGoalStatViewByEi statView tenantId:{},-1,fromDateTime:{},empty", tenantId, fromDateTime);
    }
    return statViewDOList;
  }

  /**
   * 批量查询用胡自定义统计图
   * @param tenantId
   * @param fromDateTime
   * @return
   */
  public List<StatViewDO> batchQueryCustomStatViewByEi(String tenantId, String fromDateTime){
    List<StatViewDO> statViewDOList = statViewMapper.setTenantId("-1")
                                                    .batchQueryCustomStatViewByEi(Integer.parseInt(tenantId), fromDateTime);
    if (CollectionUtils.isEmpty(statViewDOList)) {
      log.warn("batchQueryStatViewByEi statView tenantId:{},-1,fromDateTime:{},empty", tenantId, fromDateTime);
    }
    return statViewDOList;
  }

  public List<StatViewDO> batchQueryStatViewByEiSchemaId(String tenantId,
                                                         List<String> schemaIdList,
                                                         String fromDateTime) {
    String inSchemaIds = "'" + String.join("','", schemaIdList) + "'";
    List<StatViewDO> statViewDOList = statViewMapper.setTenantId("-1")
                                                    .batchQueryStatViewByEiSchemaId(Integer.parseInt(tenantId),
                                                      inSchemaIds, fromDateTime);
    if (CollectionUtils.isEmpty(statViewDOList)) {
      log.warn("batchQueryStatViewByEi statView tenantId:{},-1,fromDateTime:{}", tenantId, fromDateTime);
    }
    return statViewDOList;
  }

  /**
   * 加载详情页统计图维度
   *
   * @param tenantId 租户id
   * @param viewId   图id
   * @return
   */
  public void transObjDetailStatViewDimFields(String tenantId, String viewId, final List<String> statViewFieldIds) {
    List<StatDimFieldInfoDO> statDimFieldInfoDOS = statDimFieldInfoMapper.setTenantId(tenantId)
                                                                         .queryStatDetailInfoByViewId(tenantId,
                                                                           new String[] {viewId});
    if (CollectionUtils.isNotEmpty(statDimFieldInfoDOS)) {
      statDimFieldInfoDOS.stream()
                         .map(StatDimFieldInfoDO::getFieldIds)
                         .filter(StringUtils::isNotBlank)
                         .flatMap(fields -> Splitter.on(CharMatcher.anyOf(",")).splitToList(fields).stream())
                         .forEach(statViewFieldIds::add);
    }
  }

  /**
   * 跟据被联动的图表的id反查所有，联动图的联动维度
   *
   * @param tenantId
   * @param followId
   * @return
   */
  public List<String> findOwnersByFollowId(String tenantId, String followId) {
    List<String> owners = Lists.newArrayList();
    String schema = "public";
    if (pgDataSource.standalone(tenantId)) {
      schema = "sch_" + tenantId;
    }
    String sql = String.format("select owner_id from %s.dash_board_link," +
      "  jsonb_to_recordset(follows::jsonb) as f(\"viewId\" varchar(64), type varchar(64))" +
      "where  \"viewId\" = '%s' and f.type = 'table' and tenant_id = '%s'", schema, followId, tenantId);
    if (GrayManager.isAllowByRule("fix_follow_type", tenantId)) {
      sql = String.format("select owner_id from %s.dash_board_link," +
        "  jsonb_to_recordset(follows::jsonb) as f(\"viewId\" varchar(64), type varchar(64))" +
        "where  \"viewId\" = '%s' and tenant_id = '%s'", schema, followId, tenantId);
    }
    try (JdbcConnection jdbcConnection = pgDataSource.getJdbcConnection(tenantId)) {
      jdbcConnection.query(sql, rs -> {
        while (rs.next()) {
          String ownerId = rs.getString("owner_id");
          if (StringUtils.isNotBlank(ownerId)) {
            owners.add(ownerId);
          }
        }
      });
    } catch (Exception e) {
      log.error("query owner error tenantId:{},followId:{}", tenantId, followId, e);
    }
    return owners.stream().distinct().toList();
  }

  /**
   * 获取图受联动维度集合
   *
   * @param tenantId         租户id
   * @param followId         被联动图id
   * @param statViewFieldIds 保存所有维度的fieldId
   * @return
   */
  public void queryAllLinkFields(String tenantId, String followId, final List<String> statViewFieldIds) {
    List<String> viewIds = this.findOwnersByFollowId(tenantId, followId);
    if (viewIds != null) {
      List<String> allFields = Lists.newArrayList();
      List<StatViewDO> statViewDOs = statViewMapper.setTenantId("-1")
                                                   .batchQueryStatViewByViewIds(Integer.parseInt(tenantId),
                                                     viewIds.toArray(new String[0]));
      if (CollectionUtils.isEmpty(statViewDOs)) {
        log.warn("queryAllLinkFields can not find statView by tenantId:{},viewIds:{}", tenantId,
          JSON.toJSONString(viewIds));
        return;
      }
      //获取下钻维度
      statViewDOs.forEach(statViewDO -> this.formatDrillField(statViewDO, allFields));
      List<StatViewFieldDO> statViewFieldDOS = statViewFieldMapper.setTenantId("-1")
                                                                  .getStatFieldByViewIds(Integer.parseInt(tenantId),
                                                                    viewIds.toArray(new String[0]));
      if (CollectionUtils.isNotEmpty(statViewFieldDOS)) {
        this.formatStatViewField(tenantId, statViewFieldDOS, allFields);
      }
      if(CollectionUtils.isEmpty(allFields)){
        log.warn("queryAllLinkFields allFields is empty tenantId:{},followId:{}",tenantId,followId);
        return;
      }
      String inSQL = allFields.stream().distinct().map(field -> "'" + field + "'").collect(Collectors.joining(","));
      List<StatFieldDO> statFieldDOS = statFieldMapper.setTenantId(tenantId)
                                                      .queryFieldBase(tenantId, inSQL, new String[] {"dim"},
                                                        new int[] {1, 2, 6});
      if (CollectionUtils.isNotEmpty(statFieldDOS)) {
        statFieldDOS.forEach(statFieldDo -> statViewFieldIds.add(statFieldDo.getFieldId()));
      }
    }
  }

  /**
   * 批量查询统计图用到的字段
   *
   * @param tenantId 租户id
   * @param viewIds  视图id集合
   * @return
   */
  public Map<String, Set<String>> batchQueryStatViewBaseField(String tenantId, List<String> viewIds) {
    List<StatViewDO> statViewDOs = statViewMapper.setTenantId("-1")
                                                 .batchQueryStatViewByViewIds(Integer.parseInt(tenantId),
                                                   viewIds.toArray(new String[0]));
    if (CollectionUtils.isEmpty(statViewDOs)) {
      log.error("can not find statView by tenantId:{},viewIds:{}", tenantId, JSON.toJSONString(viewIds));
      return null;
    }
    Map<String, Set<String>> compareFromFieldLocations = Maps.newHashMap();
    for (StatViewDO statViewDO : statViewDOs) {
      StatViewEntity statViewEntity = this.findStatViewById(tenantId, statViewDO);
      if (statViewEntity != null) {
        compareFromFieldLocations.put(statViewDO.getViewId(), statViewEntity.findAllFieldIds());
      }
    }
    return compareFromFieldLocations;
  }

  /**
   * 获取图中所有的基础维度和指标字段
   *
   * @param tenantId            租户id
   * @param statViewDO          图对象
   * @param statSchemaDO        主题对象
   * @param statViewFieldDOList 维度+指标
   * @param statViewFilterDOS   筛选器字段
   * @return
   */
  public StatViewBaseField createStatViewBaseField(String tenantId,
                                                   StatViewDO statViewDO,
                                                   StatSchemaDO statSchemaDO,
                                                   List<StatViewFieldDO> statViewFieldDOList,
                                                   List<StatViewFilterDO> statViewFilterDOS) {
    String viewId = statViewDO.getViewId();
    if (statSchemaDO == null) {
      throw new RuntimeException(String.format("can not find schema tenantId:%s,viewId:%s", tenantId, viewId));
    }
    String themeApiName = statSchemaDO.getSchemaEnName();
    List<String> statViewFieldIds = Lists.newArrayList();
    //获取下钻维度字段
    this.formatDrillField(statViewDO, statViewFieldIds);
    if (CollectionUtils.isEmpty(statViewFieldDOList)) {
      throw new RuntimeException(String.format("can not find StatViewField by tenantId:%s,viewId:%s", tenantId,
        viewId));
    }
    //清洗stat_view_field
    this.formatStatViewField(tenantId, statViewFieldDOList, statViewFieldIds);
    //stat_view_property表中清洗field_id
    List<String> propertyFields = this.fillFieldIdsFromProperty(tenantId, viewId);
    if (CollectionUtils.isNotEmpty(propertyFields)) {
      statViewFieldIds.addAll(propertyFields);
    }
    //处理筛选条件
    List<String> statViewFilterFieldIds = this.formatStatViewFilter(statViewFilterDOS);
    //详情页
    this.transObjDetailStatViewDimFields(tenantId, viewId, statViewFieldIds);
    //获取下钻维度  获取图受联动维度集合
    this.queryAllLinkFields(tenantId, viewId, statViewFieldIds);
    return StatViewBaseField.builder()
                            .tenantId(tenantId)
                            .viewName(statViewDO.getViewName())
                            .viewId(viewId)
                            .schemaId(statViewDO.getSchemaId())
                            .timeZone(statViewDO.getTimeZone())
                            .themeApiName(themeApiName)
                            .statViewFields(statViewFieldIds)
                            .statViewFilters(statViewFilterFieldIds)
                            .build();
  }

  /**
   * 封装统计图中用到的所有维度和指标。包含驾驶舱的全局筛选条件
   * stat_view_field,stat_view_filter,stat_agg_calc_field,stat_view_property
   *
   * @param tenantId   租户id
   * @param statViewDO 统计图对象bean
   * @return
   */
  public StatViewEntity findStatViewById(String tenantId, StatViewDO statViewDO) {
    StatSchemaDO statSchemaInfo = statSchemaMapper.setTenantId(tenantId)
                                                  .queryStatSchemaBySchemaId(tenantId, statViewDO.getSchemaId());
    if (statSchemaInfo == null) {
      log.warn(String.format("can not find schema tenantId:%s,viewId:%s", tenantId, statViewDO.getViewId()));
      return null;
    }
    if (Objects.equals("goal_value_obj", statSchemaInfo.getSchemaEnName())) {
      log.warn("no support goal_value_obj schema tenantId:{},viewId:{},schemaId:{}", tenantId, statViewDO.getViewId(), statViewDO.getSchemaId());
      return null;
    }
    String viewId = statViewDO.getViewId();
    //首先筛选指标和维度stat_view_field
    List<StatViewFieldDO> statViewFieldDOS = statViewFieldMapper.setTenantId("-1")
                                                                .getStatFieldByViewIds(Integer.parseInt(tenantId), new String[] {viewId});
    //检测stat_view_filter 数据找出做筛选条件的维度和指标
    List<StatViewFilterDO> statViewFilterDOS = statViewFilterMapper.setTenantId("-1")
                                                                   .getStatViewFilters(Integer.parseInt(tenantId), new String[] {viewId});
    //获取下钻详情页等field
    StatViewBaseField statViewBaseField = this.createStatViewBaseField(tenantId, statViewDO, statSchemaInfo, statViewFieldDOS, statViewFilterDOS);
    //检测特殊场景的fieldId
    if (statViewDO.getEi() < 0) {
      this.replaceStatFieldId(tenantId, statViewBaseField.getStatViewFields());
      this.replaceStatFieldId(tenantId, statViewBaseField.getStatViewFilters());
    }
    //统计图联合目标分析需要提取出checkCyCle
    String checkCyCle = "";
    for (StatViewFilterDO svf : statViewFilterDOS) {
      if (Constants.OLD_STAT_VIEW_GOAL_RULE_IDS.contains(svf.getFieldId()) && StringUtils.isBlank(checkCyCle)) {
        checkCyCle = this.findCheckCyCle(svf, tenantId);
      }
    }
    return this.findStatViewEntityByFieldIds(tenantId, viewId, statViewDO.getViewName(), statViewBaseField.getThemeApiName(), statViewDO.getSchemaId(), statViewBaseField.getStatViewFields(), statViewBaseField.getStatViewFilters(), statViewDO.getTimeZone(), checkCyCle, statSchemaInfo.getDatabaseId());
  }

  /**
   * 检测是否含有特殊场景的fieldId只有预设图有这种情况
   *
   * @param tenantId   租户id
   * @param statFields fieldIds集合
   */
  public void replaceStatFieldId(String tenantId, final List<String> statFields) {
    if (CollectionUtils.isNotEmpty(statFields)) {
      Map<String, String> fieldMappers = Maps.newHashMap();
      statFields.forEach(fieldId -> {
        String speField = STAT_SPE_FIELDID_TO_FIELD_MAP.get(fieldId);
        if (speField != null) {
          String[] split = speField.split("-");
          if (split.length >= 3) {
            String rFieldId = statFieldMapper.setTenantId(tenantId)
                                             .queryFieldIdByFieldNameObjectNameAggDimType(tenantId, split[0],
                                               split[1], split[2]);
            if (StringUtils.isEmpty(rFieldId)) {
              log.warn("queryFieldIdByFieldNameObjectNameAggDimType error tenantId:{},speField:{}", tenantId,
                speField);
              return;
            }
            fieldMappers.put(fieldId, rFieldId);
          }
        }
      });
      if (!fieldMappers.isEmpty()) {
        statFields.replaceAll(fieldId -> fieldMappers.getOrDefault(fieldId, fieldId));
      }
    }
  }

  /**
   * 清洗筛选条件Field
   *
   * @param statViewFilterDOS
   * @return Map<String field_id, String checkCyCle>
   */
  private List<String> formatStatViewFilter(List<StatViewFilterDO> statViewFilterDOS) {
    //检测stat_view_filter 数据找出做筛选条件的维度和指标
    List<String> statViewFilterFieldIds = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(statViewFilterDOS)) {
      statViewFilterDOS.forEach(svf -> {
        if (!USER_SEGMENT_FILTER_FIELD_ID_SET.contains(svf.getFieldId())) {
          statViewFilterFieldIds.add(svf.getFieldId());
        }
      });
    }
    return statViewFilterFieldIds;
  }

  /**
   * 提取checkCyCle 年季月周
   * @param svf
   * @return
   */
  public String findCheckCyCle(StatViewFilterDO svf, String tenantId) {
    try {
      //联合目标主题分析的统计图->提取checkCyCle
      if (StringUtils.isNotBlank(svf.getValue1()) && svf.getValue1().startsWith("[{")) {
        JSONArray value1 = JSON.parseArray(svf.getValue1());
        for (Object object : value1) {
          JSONObject filterJson = (JSONObject) object;
          if (StringUtils.isNotBlank(filterJson.getString("checkCycle"))) {
            return filterJson.getString("checkCycle");
          }
          String goalRuleId = filterJson.getString("optionCode");
          if (StringUtils.isNotBlank(goalRuleId)) {
            GoalRuleDO goalRuleDO = goalRuleMapper.setTenantId(tenantId).getRuleEntityById(String.valueOf(svf.getEi()), goalRuleId);
            if (goalRuleDO != null) {
              return goalRuleDO.checkCyCle;
            }
          }
        }
      }
    } catch (RuntimeException e) {
      log.error("formatStatViewFilter find checkCyCle is error, StatViewFilterDO:{}", JSON.toJSONString(svf), e);
    }
    return "month";
  }

  public StatViewFilterDO findStatViewFilterDO(String tenantId, String viewId, String fieldId) {
    return statViewFilterMapper.setTenantId("-1").getStatViewFilterByFieldId(Integer.parseInt(tenantId), viewId, fieldId);
  }

  /**
   * 清洗维度字段和指标字段
   *
   * @param tenantId
   * @param statViewFieldDOS
   * @param statViewFieldIds
   */
  private void formatStatViewField(String tenantId,
                                   List<StatViewFieldDO> statViewFieldDOS,
                                   List<String> statViewFieldIds) {
    List<String> calculateFields = Lists.newArrayList();
    statViewFieldDOS.forEach(statField -> {
      //老目标在指标里过滤掉,用筛选器的指标
      if (Constants.OLD_GOAL_GOAL_FIELD_ID.equals(statField.getFieldId())) {
        return;
      }
      if (Constants.AGG_CALC_TYPE.equals(statField.getAggrType())) {
        calculateFields.add(statField.getFieldId());
      } else {
        statViewFieldIds.add(statField.getFieldId());
        //针对日期层级字段把parentId添加集合中
        if (StringUtils.isNotBlank(statField.getParentId())) {
          statViewFieldIds.add(statField.getParentId());
        }
      }
    });
    //处理指标作为计算字段
    Optional<List<String>> calculateAggFieldsOP = this.findAggFieldsAsCalculate(tenantId, calculateFields);
    calculateAggFieldsOP.ifPresent(statViewFieldIds::addAll);
  }

  /**
   * 获取下钻维度fieldId
   *
   * @param statViewDO       统计图对象
   * @param statViewFieldIds fieldId 集合
   */
  public void formatDrillField(StatViewDO statViewDO, List<String> statViewFieldIds) {
    //获取下钻维度字段
    String drillDownPath = statViewDO.getDrillDownPath();
    if (StringUtils.isNotBlank(drillDownPath)) {
      Set<String> fields = Splitter.on(",")
                                   .splitToList(drillDownPath)
                                   .stream()
                                   .filter(path -> !Objects.equals("-1", path))
                                   .flatMap(dim -> {
                                     if (dim.contains(":")) {
                                       return Stream.of(dim.split(":"));
                                     }
                                     return Stream.of(dim.split("[#\\$]")[0]);
                                   })
                                   .collect(Collectors.toSet());
      statViewFieldIds.addAll(fields);
    }
  }

  /**
   * 处理标签 维度和筛选器
   *
   * @param allStatViewFields
   * @param statViewFilters
   * @return
   */
  public List<DisplayField> createTagDisplayFields(final List<String> allStatViewFields,
                                                   final List<String> statViewFilters) {
    Map<String, DisplayField> tagDataFieldMapper = allStatViewFields.stream()
                                                                    .map(fieldId -> MtTagViewTable.createTagDisplayField(fieldId, DisplayField.DisplayType.group))
                                                                    .filter(Optional::isPresent)
                                                                    .map(Optional::get)
                                                                    .collect(Collectors.toMap(DisplayField::getStatFieldId, Function.identity()));
    List<DisplayField> filterDisFields = statViewFilters.stream()
                                                        .map(fieldId -> MtTagViewTable.createTagDisplayField(fieldId,
                                                          DisplayField.DisplayType.filter))
                                                        .filter(Optional::isPresent)
                                                        .map(Optional::get)
                                                        .toList();
    if (CollectionUtils.isNotEmpty(filterDisFields)) {
      filterDisFields.forEach(field -> tagDataFieldMapper.computeIfAbsent(field.getStatFieldId(), k -> field));
    }
    return Lists.newArrayList(tagDataFieldMapper.values());
  }

  /**
   * 根据基础指标和维度，加上扩展维度，id转name等封装最后的统计图实体对象
   *
   * @param tenantId        租户id
   * @param viewId          图id
   * @param viewName        图名称
   * @param themeApiName    主题名称
   * @param schemaId        主题id
   * @param statViewFields  维度+指标+计算字段+property
   * @param statViewFilters 筛选器字段
   * @param timeZone        时区
   * @param databaseId      数据源id
   * @return {@link StatViewEntity}
   */
  public StatViewEntity findStatViewEntityByFieldIds(String tenantId,
                                                     String viewId,
                                                     String viewName,
                                                     String themeApiName,
                                                     String schemaId,
                                                     List<String> statViewFields,
                                                     List<String> statViewFilters,
                                                     String timeZone,
                                                     String checkCyCle,
                                                     String databaseId) {
    String masterApiName = null;
    List<UdfObjFieldDO> masterDbObjLookupField = udfObjFieldMapper.setTenantId(tenantId)
                                                                  .findUdfDbFieldInfoByType(tenantId, themeApiName, mappingService.getApiXName(themeApiName), FieldType.MASTER_DETAIL);
    if (CollectionUtils.isNotEmpty(masterDbObjLookupField)) {
      masterApiName = masterDbObjLookupField.getFirst().getRefObjName();
    }
    List<AggRuleDO> aggRuleIds = Lists.newArrayList();
    //所有aggRule
    List<AggRuleBO> aggRuleBOList = Lists.newArrayList();
    //指标做筛选条件集合
    List<AggRuleDO> aggRuleFilterIds = Lists.newArrayList();
    //所有的维度字段
    Set<DisplayField> allDimFieldSet = Sets.newHashSet();
    //根据主题获取额外的维度字段
    if(StringUtils.isBlank(databaseId)){ //外部数据源不增加额外维度字段
      statViewFields.addAll(this.createExtFields(tenantId, themeApiName, schemaId,masterApiName));
    }
    //标签作为维度
    List<DisplayField> tagDimFields = this.createTagDisplayFields(statViewFields, statViewFilters);
    //指标和维度
    this.dealWithStatField(tenantId, themeApiName, statViewFields,false ,aggRuleIds, aggRuleBOList, aggRuleFilterIds, allDimFieldSet, masterApiName);
    //筛选器
    this.dealWithStatField(tenantId, themeApiName, statViewFilters,true ,aggRuleIds, aggRuleBOList, aggRuleFilterIds, allDimFieldSet, masterApiName);
    List<DisplayField> customDimFields = new ArrayList<>();
    if (GrayManager.isAllowByRule("supportCustomDim", tenantId)) {
      customDimFields = createCustomDisplayFields(tenantId, statViewFields, statViewFilters);
    }
    List<DisplayField> allDimFields = Lists.newArrayList(allDimFieldSet);
    //检测是否要加上object_id
    this.checkObjectIdField(tenantId, allDimFields, themeApiName, !aggRuleFilterIds.isEmpty());
    return StatViewEntity.builder()
                         .tenantId(tenantId)
                         .viewId(viewId)
                         .viewName(viewName)
                         .aggRuleIds(aggRuleIds)
                         .dimFields(allDimFields)
                         .aggRuleFilterIds(aggRuleFilterIds)
                         .tagDimFields(Lists.newArrayList(tagDimFields))
                         .customDimFields(customDimFields)
                         .timeZone(timeZone)
                         .schemaId(schemaId)
                         .themeName(themeApiName)
                         .standalone(mybatisTenantPolicy.standalone(tenantId))
                         .allAggRules(aggRuleBOList)
                         .checkCyCle(checkCyCle)
                         .databaseId(databaseId)
                         .build();
  }

  public void dealWithStatField(String tenantId,
                                String themeApiName,
                                List<String> fieldList,
                                boolean filter,
                                List<AggRuleDO> aggRuleIds,
                                List<AggRuleBO> aggRuleBOList,
                                List<AggRuleDO> aggRuleFilterIds,
                                Set<DisplayField> allDimFieldSet,
                                String masterApiName) {
    if (CollectionUtils.isEmpty(fieldList)) {
      return;
    }
    Pair<List<StatFieldDO>, List<AggRuleBO>> dimAndRuleStatViewField = this.findDimAndAgg(tenantId, fieldList, filter ? themeApiName : null);
    dimAndRuleStatViewField.second.forEach(aggRuleBO -> {
      if (StatFieldStatusEnum.isValidStatus(aggRuleBO.getStatFieldStatus())) {
        if (filter) {
          aggRuleFilterIds.add(aggRuleBO.getAggRuleDO());
        }else{
          aggRuleIds.add(aggRuleBO.getAggRuleDO());
        }
      }
      aggRuleBOList.add(aggRuleBO);
    });
    List<DisplayField> dimStatFields = this.findAndCheckDimFields(tenantId, themeApiName, dimAndRuleStatViewField.first, DisplayField.DisplayType.group, masterApiName);
    if (CollectionUtils.isNotEmpty(dimStatFields)) {
      allDimFieldSet.addAll(dimStatFields);
    }
  }

  /**
   * 找到自定义维度
   * todo 自定义维度表 bi_mt_dimension 列修改添加需要兼容
   */
  private List<DisplayField> createCustomDisplayFields(String tenantId,
                                                       List<String> statViewFieldIds,
                                                       List<String> statViewFilters) {
    List<DisplayField> customDimFields = new ArrayList<>();
    HashSet<String> fieldIdSet = new HashSet<>(statViewFieldIds);
    if (CollectionUtils.isNotEmpty(statViewFilters)) {
      fieldIdSet.addAll(statViewFilters);
    }
    List<BIMtDimensionDO> mtDimensionDOS = bimtDimensionMapper.setTenantId(tenantId)
                                                              .findDimByFieldIds(tenantId, SQLUtil.generateInExpress(List.copyOf(fieldIdSet)));
    Optional.ofNullable(mtDimensionDOS).orElse(new ArrayList<>()).forEach(mtDimensionDO -> {
      String dimensionField = mtDimensionDO.getDimensionField();
      DisplayField displayField = DisplayField.builder()
                                              .dbFieldName(dimensionField)
                                              .dimensionConfig(mtDimensionDO.getDimensionConfig())
                                              .describeApiName(mtDimensionDO.getDescribeApiName())
                                              .statFieldId(mtDimensionDO.getFieldId())
                                              .sourceDimensionId(mtDimensionDO.getSourceDimensionId())
                                              .dimensionId(mtDimensionDO.getDimensionId())
                                              .customType(mtDimensionDO.getCustomType())
                                              .disPlayType(DisplayField.DisplayType.group)
                                              .relationFieldId(mtDimensionDO.getRelationFieldId())
                                              .build();

      customDimFields.add(displayField);
    });
    return customDimFields;
  }


  /**
   * 添加目标筛选规则,两个
   * 旧的和新的目标都要生成一个rule,因为关联goal_value的条件不一样
   */
  private List<AggRuleBO> checkGoalRule(String themeApiName) {
    List<AggRuleBO> aggRuleDOList = Lists.newArrayList();
    AggRuleDO newAggRuleDO = new AggRuleDO();
    newAggRuleDO.setRuleId(Constants.MULTI_GOAL_RULE_ID);
    newAggRuleDO.setFieldId(Constants.MULTI_GOAL_RULE_ID);
    aggRuleDOList.add(AggRuleBO.builder()
                               .aggRuleDO(newAggRuleDO)
                               .statFieldStatus(StatFieldStatusEnum.enable.getCode())
                               .build());
    //如果是人员主题，需要再生成一个部门的虚拟指标（主属性维度），部门+人员
    if (Constants.employee_api_name.equals(themeApiName)) {
      AggRuleDO newUserAggRuleDO = new AggRuleDO();
      newUserAggRuleDO.setRuleId(Constants.MULTI_GOAL_RULE_ID_DEPT);
      newUserAggRuleDO.setFieldId(Constants.MULTI_GOAL_RULE_ID_DEPT);
      aggRuleDOList.add(AggRuleBO.builder()
                                 .aggRuleDO(newUserAggRuleDO)
                                 .statFieldStatus(StatFieldStatusEnum.enable.getCode())
                                 .build());
    }
    return aggRuleDOList;
  }

  /**
   * 针对主题对象判断增加object_id 维度列
   *
   * @param tenantId     租户id
   * @param allDimFields 图中用到的所有维度字段
   */
  private void addObjectIdField(String tenantId, List<DisplayField> allDimFields) {
    List<DisplayField> objectIdDimFields = Lists.newArrayList();
    //检测是否要把object_id 加入到displayField中
    Map<String, List<DisplayField>> apiNameAndFieldsMap = allDimFields.stream()
                                                                      .collect(Collectors.groupingBy(DisplayField::getDescribeApiName));
    apiNameAndFieldsMap.forEach((apiName, displayFields) -> {
      Optional<DisplayField> nameFieldOP = displayFields.stream()
                                                        .filter(displayField -> "name".equals(displayField.getDbFieldName()))
                                                        .findFirst();
      if (nameFieldOP.isPresent()) {
        StatFieldDO objectIdField = statFieldMapper.setTenantId(tenantId)
                                                   .queryObjectIdFieldByApiName(tenantId, apiName);
        if (objectIdField != null) {
          boolean hasObjectIdField = displayFields.stream()
                                                  .anyMatch(displayField -> Objects.equals(objectIdField.getFieldId()
                                                    , displayField.getStatFieldId()));

          if (!hasObjectIdField) {
            objectIdDimFields.add(DisplayField.builder()
                                              .dbFieldName(objectIdField.getDbFieldName())
                                              .statFieldId(objectIdField.getFieldId())
                                              .describeApiName(apiName)
                                              .lookupName(false)
                                              .build());
          }
        }
      }
    });
    allDimFields.addAll(objectIdDimFields);
  }

  /**
   * 根据schemaId 获取statSchema对象
   *
   * @param tenantId
   * @param schemaId
   * @return
   */
  public JSONObject findStatSchemaById(String tenantId, String schemaId) {
    Map<String, Object> statSchemaMap = statSchemaMapper.setTenantId(tenantId)
                                                        .findStatSchemaBySchemaId(tenantId, schemaId);
    if (MapUtils.isEmpty(statSchemaMap)) {
      log.error("findSchemaBySchemaId is empty tenantId:{},schemaId:{}", tenantId, schemaId);
      return new JSONObject();
    }
    return new JSONObject(statSchemaMap);
  }

  /**
   * 针对主题对象对象或主对象用到了name字段
   *
   * @param tenantId     租户id
   * @param allDimFields 图中用到的所有维度字段
   */
  private void checkObjectIdField(String tenantId,
                                  List<DisplayField> allDimFields,
                                  String themeApiName,
                                  boolean withFilterAgg) {
    List<DisplayField> objectIdDimFields = Lists.newArrayList();
    //检测是否要把object_id 加入到displayField中
    Map<String, List<DisplayField>> apiNameAndFieldsMap = allDimFields.stream()
                                                                      .collect(Collectors.groupingBy(DisplayField::getDescribeApiName));
    apiNameAndFieldsMap.forEach((apiName, displayFields) -> {
      Optional<DisplayField> nameFieldOP = displayFields.stream()
                                                        .filter(displayField -> "name".equals(displayField.getDbFieldName()))
                                                        .findFirst();
      //如果是主题对象，并且有指标做筛选条件需要将主题对象id也一并落库到agg_data
      if ((apiName.equals(themeApiName) && withFilterAgg) || nameFieldOP.isPresent()) {
        StatFieldDO objectIdField = statFieldMapper.setTenantId(tenantId)
                                                   .queryObjectIdFieldByApiName(tenantId, apiName);
        if (objectIdField != null) {
          boolean hasObjectIdField = displayFields.stream()
                                                  .anyMatch(displayField -> Objects.equals(objectIdField.getFieldId()
                                                    , displayField.getStatFieldId()));

          if (!hasObjectIdField) {
            DisplayField.DisplayType type = null;
            if ((apiName.equals(themeApiName) && withFilterAgg)) {
              type = DisplayField.DisplayType.aggFilter;
            } else {
              type = nameFieldOP.get().getDisPlayType();
            }
            objectIdDimFields.add(DisplayField.builder()
                                              .dbFieldName(objectIdField.getDbFieldName())
                                              .statFieldId(objectIdField.getFieldId())
                                              .describeApiName(apiName)
                                              .lookupName(false)
                                              .dstColumnName(Objects.equals(apiName, themeApiName) ?
                                                Constants.OBJECT_ID :
                                                null)
                                              .disPlayType(type)
                                              .build());
          }
        }
      }
    });
    allDimFields.addAll(objectIdDimFields);
  }

  /**
   * 获取所有作为计算字段的指标字段集合
   *
   * @param tenantId        租户id
   * @param calculateFields 计算字段id集合
   * @return
   */
  private Optional<List<String>> findAggFieldsAsCalculate(String tenantId, List<String> calculateFields) {
    //处理指标作为计算字段
    if (!calculateFields.isEmpty()) {
      List<String> statViewFieldIds = Lists.newArrayList();
      List<StatCalcFieldDO> statAggCalcFieldEntities = statCalcFieldMapper.setTenantId(tenantId)
                                                                          .queryAggCalcFields(Integer.parseInt(tenantId), calculateFields.toArray(new String[0]));
      log.info("queryAggCalcFields tenantId:{},aggCalcField:{},statAggCalcFieldEntities:{}", tenantId,
        JSON.toJSONString(calculateFields), JSON.toJSONString(statAggCalcFieldEntities));
      if (CollectionUtils.isNotEmpty(statAggCalcFieldEntities)) {
        //清洗出所有指标作为计算
        statAggCalcFieldEntities.forEach(statAggCalcFieldEntity -> {
          List<String> formulaFields = statAggCalcFieldEntity.findAllFormulaFields();
          if (CollectionUtils.isNotEmpty(formulaFields)) {
            statViewFieldIds.addAll(formulaFields);
          }
        });
      }
      return Optional.of(statViewFieldIds);
    }
    return Optional.empty();
  }

  /**
   * 获取作为维度的Field列表，并标记是否是查找关联字段，如果是查找关联字段需要联查name字段。
   *
   * @param tenantId      租户id
   * @param dimStatFields dim statField集合
   * @param masterApiName 主对象apiName
   * @return
   */
  public List<DisplayField> findAndCheckDimFields(String tenantId,
                                                  String themeApiName,
                                                  List<StatFieldDO> dimStatFields,
                                                  DisplayField.DisplayType displayType,
                                                  String masterApiName) {
    List<DisplayField> result = Lists.newArrayList();
    Map<String, List<StatFieldDO>> schemaStatFieldMap = dimStatFields.stream()
                                                                     .collect(Collectors.groupingBy(StatFieldDO::getObjectDescribeApiName));
    schemaStatFieldMap.forEach((k, v) -> {
      if (CollectionUtils.isNotEmpty(v)) {
        String[] dbFieldNames = v.stream().map(StatFieldDO::getDbFieldName).toArray(String[]::new);
        List<UdfObjFieldDO> udfObjFieldDOS = udfObjFieldMapper.setTenantId(tenantId)
                                                              .batchQueryFieldByObjNameAndDbFieldName(Integer.parseInt(tenantId), k, ObjectConfigManager.getExtendObjName(k), dbFieldNames);
        if (CollectionUtils.isNotEmpty(udfObjFieldDOS)) {
          Map<String, UdfObjFieldDO> udfObjFieldDOMap = udfObjFieldDOS.stream()
                                                                      .collect(Collectors.toMap(UdfObjFieldDO::getDbFieldName, Function.identity()));
          v.forEach(statFieldDO -> {
            DisplayField displayField = DisplayField.builder()
                                                    .statFieldId(statFieldDO.getFieldId())
                                                    .describeApiName(statFieldDO.getObjectDescribeApiName())
                                                    .dbFieldName(statFieldDO.getDbFieldName())
                                                    .lookupName(false)
                                                    .disPlayType(displayType)
                                                    .build();
            //如果是object_id 槽位则写入固定槽位，并且是主题对象
            if (Constants.OBJECT_ID.equals(statFieldDO.getFieldLocation()) && Objects.equals(themeApiName, k)) {
              displayField.setDstColumnName(Constants.OBJECT_ID);
            }
            UdfObjFieldDO udfObjFieldDO = udfObjFieldDOMap.get(statFieldDO.getDbFieldName());
            if (udfObjFieldDO != null) {
              displayField.setIsSingle(udfObjFieldDO.getIsSingle());
              result.add(displayField);
            } else {
              displayField.setIsSingle(1);
              result.add(displayField);
            }
          });
        }
      }
    });
    //检测预设的维度是否齐全,如果不齐需要从系统库反查剩余的
    if (StringUtils.isNotBlank(masterApiName)) {
      List<StatFieldDO> statFieldDOS = schemaStatFieldMap.get(masterApiName);
      this.repairDimStatFields(tenantId, masterApiName, statFieldDOS, displayType, result);
    } else {
      List<StatFieldDO> statFieldDOS = schemaStatFieldMap.get(themeApiName);
      this.repairDimStatFields(tenantId, themeApiName, statFieldDOS, displayType, result);
    }
    return result;
  }

  /**
   * 修复可能缺失的维度字段
   * @param tenantId 租户id
   * @param themeApiName 被检测对象apiName
   * @param statFieldDOS 维度字段集合
   * @param displayType 查询类型
   * @param result 返回结果
   */
  public void repairDimStatFields(String tenantId,
                                  String themeApiName,
                                  List<StatFieldDO> statFieldDOS,
                                  DisplayField.DisplayType displayType,
                                  List<DisplayField> result) {
    if (CollectionUtils.isNotEmpty(statFieldDOS)) {
      Set<String> themeDbFieldNames = statFieldDOS.stream()
                                                  .map(StatFieldDO::getDbFieldName)
                                                  .collect(Collectors.toSet());
      List<String> lessDimFields = WarehouseConfig.forceDbFieldNames.stream()
                                                                    .filter(fieldName -> !themeDbFieldNames.contains(fieldName))
                                                                    .toList();
      if (CollectionUtils.isNotEmpty(lessDimFields)) {
        List<StatFieldDO> sysTemDbFields = statFieldMapper.setTenantId("-1")
                                                          .querySystemStatField("-1", themeApiName,
                                                            lessDimFields.toArray(String[]::new));
        if (CollectionUtils.isNotEmpty(sysTemDbFields)) {
          sysTemDbFields.forEach(statFieldDO -> {
            log.info("repairDimStatFields tenantId:{},themeApiName:{},sysDbFieldName:{}",tenantId,themeApiName,statFieldDO.getDbFieldName());
            DisplayField displayField = DisplayField.builder()
                                                    .statFieldId(statFieldDO.getFieldId())
                                                    .describeApiName(statFieldDO.getObjectDescribeApiName())
                                                    .dbFieldName(statFieldDO.getDbFieldName())
                                                    .lookupName(false)
                                                    .disPlayType(displayType)
                                                    .build();
            result.add(displayField);
          });
        }
      }
    }
  }

  /**
   * 根据field id获取dim 和 agg规则
   *
   * @param tenantId 租户id
   * @param fieldIds statFieldId
   * @return
   */
  public Pair<List<StatFieldDO>/*dimFields*/, List<AggRuleBO>/*aggRuleIds*/> findDimAndAgg(String tenantId,
                                                                                           List<String> fieldIds,
                                                                                           String themeApiName) {
    List<AggRuleBO> aggRuleIds = Lists.newArrayList();
    List<StatFieldDO> dimFields = Lists.newArrayList();
    if (CollectionUtils.isEmpty(fieldIds)) {
      return Pair.build(dimFields, aggRuleIds);
    }
    //如果是明细主题分析目标需要添加虚拟指标,即添加主题对象的主属性维度(object_id)
    if (StringUtils.isNotBlank(themeApiName) &&
      CollectionUtils.containsAny(fieldIds, Constants.OLD_STAT_VIEW_GOAL_RULE_IDS)) {
      //添加虚拟指标
      aggRuleIds.addAll(this.checkGoalRule(themeApiName));
      if (Constants.employee_api_name.equals(themeApiName) && !fieldIds.contains(Constants.MAIN_DEPARTMENT_FIELD_ID)) {
        fieldIds.add(Constants.MAIN_DEPARTMENT_FIELD_ID);
      }
    }
    String inSQL = JoinHelper.joinSkipNullOrBlank(",", "'", fieldIds);
    //获取维度字段 包含所有状态
    List<StatFieldDO> statFieldDOS = statFieldMapper.setTenantId(tenantId)
                                                    .queryFieldBase(tenantId, inSQL,
                                                      new String[] {Constants.AGG_DIM_TYPE_DIM,
                                                        Constants.AGG_DIM_TYPE_AGG, Constants.AGG_DIM_TYPE_BASE_AGG,
                                                        Constants.AGG_DIM_TYPE_GOAL_AGG,
                                                        Constants.AGG_DIM_TYPE_DOWNSTREAM_AGG}, new int[] {0, 1, 2, 3
                                                        , 5, 6});
    List<StatFieldDO> aggFields = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(statFieldDOS)) {
      statFieldDOS.forEach(statFieldDO -> {
        switch (statFieldDO.getAggDimType()) {
          case Constants.AGG_DIM_TYPE_AGG, Constants.AGG_DIM_TYPE_BASE_AGG, Constants.AGG_DIM_TYPE_GOAL_AGG ->
            aggFields.add(statFieldDO);
          case Constants.AGG_DIM_TYPE_DIM -> {
            // 一些固定维度字段不校验状态
            if (StatFieldStatusEnum.isValidStatus(statFieldDO.getStatus()) || WarehouseConfig.forceDbFieldNames.contains(statFieldDO.getDbFieldName())) {
              dimFields.add(statFieldDO);
            }
          }
          case Constants.AGG_DIM_TYPE_DOWNSTREAM_AGG -> aggRuleIds.add(this.buildAggRuleDO(statFieldDO));
        }
      });
    }
    if (!aggFields.isEmpty()) {
      Map<String, StatFieldDO> aggStatFieldMapper = aggFields.stream()
                                                             .collect(Collectors.toMap(StatFieldDO::getFieldId,
                                                               Function.identity()));
      String aggRuleInSQL = JoinHelper.joinSkipNullOrBlank(",", "'", aggStatFieldMapper.keySet());
      List<AggRuleDO> aggRules = aggRuleMapper.setTenantId(tenantId).queryAggRuleByFieldIds(aggRuleInSQL, tenantId);
      if (CollectionUtils.isNotEmpty(aggRules)) {
        aggRules.forEach(aggRuleDO -> {
          StatFieldDO statFieldDO = aggStatFieldMapper.get(aggRuleDO.getFieldId());
          int statStatus = statFieldDO != null ? statFieldDO.getStatus() : StatFieldStatusEnum.enable.getCode();
          aggRuleIds.add(AggRuleBO.builder().aggRuleDO(aggRuleDO).statFieldStatus(statStatus).build());
        });
      }
    }
    return Pair.build(dimFields, aggRuleIds);
  }

  /**
   * 下游指标在上游企业里需要虚拟出一个新的指标
   * @param statFieldDO
   * @return
   */
  private AggRuleBO buildAggRuleDO(StatFieldDO statFieldDO) {
    AggRuleDO aggRuleDO = new AggRuleDO();
    //虚拟指标,没有rule_id,统一用field_id代替
    aggRuleDO.setRuleId(statFieldDO.getFieldId());
    aggRuleDO.setFieldId(statFieldDO.getFieldId());
    aggRuleDO.setCheckObjectApiName(statFieldDO.getObjectDescribeApiName());
    aggRuleDO.setType(statFieldDO.getAggDimType());
    return AggRuleBO.builder().aggRuleDO(aggRuleDO).statFieldStatus(StatFieldStatusEnum.enable.getCode()).build();
  }

  /**
   * @param tenantId
   * @param viewId
   * @return
   */
  public List<String> fillFieldIdsFromProperty(String tenantId, String viewId) {
    List<StatViewPropertyDO> viewPropertyDOs = statViewPropertyMapper.setTenantId("-1")
                                                                     .findAggFieldFromPropertyByVids(Integer.valueOf(tenantId), new String[] {viewId});
    if (CollectionUtils.isEmpty(viewPropertyDOs)) {
      log.info("have no property field tenantId:{}, viewId:{}", tenantId, viewId);
      return null;
    }
    return viewPropertyDOs.stream()
                          .map(StatViewPropertyDO::findFieldIdFromValue)
                          .filter(Objects::nonNull)
                          .collect(Collectors.toList());
  }

  /**
   * 根据分析主题
   * 如果主题有主对象，权限字段和owner字段要从主对象获取
   *
   * @param tenantId     租户id
   * @param themeApiName 主题apiName
   * @return
   */
  public List<String> createExtFields(String tenantId, String themeApiName, String schemaId,String masterApiName) {
    Set<String> dbFieldName = Sets.newHashSet(this.forceDbFieldNames);
    //支持各种场景
    switch (themeApiName) {
      case "org_dept" -> dbFieldName.add("name");
      case "org_employee_user" -> dbFieldName.add("user_id");
      case "biz_leads" -> dbFieldName.addAll(Sets.newHashSet("is_overtime", "leads_status"));
      case "biz_product" -> dbFieldName.add("product_status");
      case "biz_opportunity" -> dbFieldName.add("status");
      case "biz_contact" -> dbFieldName.add("account_id");
    }
    if (!dbFieldName.isEmpty()) {
      String inDBFieldNames = dbFieldName.stream().map(name -> "'" + name + "'").collect(Collectors.joining(","));
      //获取依赖master对象主题的维度,org_dept,org_employee_user 没有主对象因此这么搞没问题
      if (StringUtils.isNotBlank(masterApiName)) {
        String dbObjName = masterApiName;
        StatSchemaDO statSchemaDO = statSchemaMapper.setTenantId(tenantId)
                                                    .findStatSchemaBySchemaName(tenantId, masterApiName);
        if (statSchemaDO != null) {
          themeApiName = dbObjName;
          schemaId = statSchemaDO.getSchemaId();
        }
      }
      List<StatFieldDO> statFieldDOS = null;
      if (GrayManager.isAllowByRule("no_join_dim_rule", tenantId)) {
        statFieldDOS = statFieldMapper.setTenantId(tenantId)
                                      .queryDimFieldByFieldNamePlus(tenantId, inDBFieldNames, schemaId);
      } else {
        statFieldDOS = statFieldMapper.setTenantId(tenantId)
                                      .queryDimFieldByFieldName(tenantId, inDBFieldNames, schemaId, themeApiName);
      }
      if (CollectionUtils.isNotEmpty(statFieldDOS)) {
        return statFieldDOS.stream().map(StatFieldDO::getFieldId).collect(Collectors.toList());
      }
    }
    return Lists.newArrayList();
  }

  /**
   * 构建
   * 下游图列信息，图、指标
   * 上游用到的表
   *
   * @param downViewIds
   */
  public TopologyTableAggDownStream buildAggDownStream(String tenantId,
                                                       Set<String> downViewIds,
                                                       Set<String> upTables) {
    List<StatFieldDO> statFieldDOS = statFieldMapper.setTenantId(tenantId)
                                                    .batchQueryStatFieldByDownStreamViewIds(tenantId,
                                                      downViewIds.toArray(new String[] {}));
    Map<String, List<TopologyTableAggDownStream.DownStreamField>> downStreamFieldMap = Maps.newHashMap();
    statFieldDOS.forEach(statFieldDO -> {
      TopologyTableAggDownStream.DownStreamField downStreamField = new TopologyTableAggDownStream.DownStreamField();
      downStreamField.setDownFieldId(statFieldDO.getDownstreamFieldId());
      downStreamField.setDownCHColumnType(Constants.typeToCHType(statFieldDO.getType()));
      downStreamField.setUpDdFieldName(statFieldDO.getDbFieldName());
      downStreamField.setUpFieldId(statFieldDO.getFieldId());
      downStreamFieldMap.computeIfAbsent(statFieldDO.getDownstreamViewId(), viewId -> Lists.newArrayList()).add(downStreamField);
    });
    List<TopologyTableAggDownStream.DownStreamInfo> downStreamInfoList = Lists.newArrayList();
    downStreamFieldMap.forEach((key, value) -> {
      TopologyTableAggDownStream.DownStreamInfo downStreamInfo = new TopologyTableAggDownStream.DownStreamInfo();
      downStreamInfo.setDownViewId(key);
      downStreamInfo.setDownFieldList(value);
      downStreamInfoList.add(downStreamInfo);
    });
    TopologyTableAggDownStream aggDownStream = new TopologyTableAggDownStream();
    aggDownStream.setDownInfoList(downStreamInfoList);
    aggDownStream.setUpTables(upTables);
    return aggDownStream;
  }

  /**
   * 更新1端下游指标信息
   */
  public int upsertStatFieldByFieldId(StatFieldArg statFieldArg) {
    return statFieldMapper.setTenantId(statFieldArg.getTenantId())
                          .upsertStatFieldByFieldId(statFieldArg.getTenantId(), statFieldArg.getType(),
                            statFieldArg.getIsDeleted(),statFieldArg.getAggDimType(),statFieldArg.getObjectDescribeApiName(),
                            statFieldArg.getSchemaId(),statFieldArg.getDbObjName(),statFieldArg.getFieldId(),
                            statFieldArg.getFieldName(),statFieldArg.getFormatStr(),statFieldArg.getDbFieldName(),
                            statFieldArg.getFieldType(),statFieldArg.getStatus(),statFieldArg.getDownstreamFieldId(),
                            statFieldArg.getDownstreamViewId(),statFieldArg.getDownstreamIsNullActionDate());
  }

  /**
   * 提取1端下游指标信息,生成指标过滤条件
   */
  public Map<String/*fieldId*/, String/*subWhereSql*/> queryDownStreamFields(String tenantId, String[] fieldIds) {
    List<StatFieldDO> statFieldDOS = statFieldMapper.setTenantId(tenantId).queryDownStreamFields(tenantId, fieldIds);
    Map<String, String> inSqlMap= Maps.newHashMap();
    statFieldDOS.forEach(statField -> {
      if (StringUtils.isBlank(statField.getDownstreamPolicyStructure())) {
        return;
      }
      List<DownStreamPolicyStructure> structures = JSON.parseObject(
        statField.getDownstreamPolicyStructure(), new TypeReference<>() {
        });
      if (CollectionUtils.isEmpty(structures)) {
        return;
      }
      String inSql = structures.stream()
                               .filter(structure -> StringUtils.isNoneBlank(structure.getPolicyId(),
                                   structure.getDownstreamViewId()))
                               .map(structure -> String.format("('%s','%s')", structure.getPolicyId(),
                                   structure.getDownstreamViewId()))
                               .collect(Collectors.joining(","));
      if (StringUtils.isBlank(inSql)) {
        return;
      }
      inSqlMap.put(statField.getFieldId(), String.format(" AND (policy_id, view_id) IN (%s)", inSql));
    });

    return inSqlMap;
  }
}
