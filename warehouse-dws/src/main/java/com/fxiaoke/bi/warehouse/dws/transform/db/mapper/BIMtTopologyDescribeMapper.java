package com.fxiaoke.bi.warehouse.dws.transform.db.mapper;

import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtTopologyDesDO;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface BIMtTopologyDescribeMapper extends IBatchMapper<BIMtTopologyDesDO>, ITenant<BIMtTopologyDescribeMapper> {

  @Select("select * from bi_mt_topology_describe where tenant_id=#{tenantId} and source_id=#{sourceId} and source=#{source}")
  BIMtTopologyDesDO findBiMtTopologyDes(@Param("tenantId") String tenantId, @Param("sourceId") String sourceId, @Param("source") Integer source);

  @Select("select * from bi_mt_topology_describe where tenant_id=#{tenantId} and source_id=#{topologyDescribeId} and is_deleted=false ")
  BIMtTopologyDesDO findBiMtTopologyDesById(@Param("tenantId") String tenantId, @Param("topologyDescribeId") String topologyDescribeId);

  @Select("select topology_describe_id from bi_mt_topology_describe where tenant_id=#{tenantId} and topology_describe_id like #{topologyDescribeId} and source=2 and is_deleted=false")
  List<String> findSubGoalTopologyById(@Param("tenantId") String tenantId, @Param("topologyDescribeId") String topologyDescribeId);

  @Select("select distinct split_part(topology_describe_id,'|',1) as topology_describe_id " +
    "from bi_mt_topology_describe where tenant_id=#{tenantId} and split_part(topology_describe_id,'|',1)=any(array[#{topologyDescribeId}]) " +
    "and source=#{source} and is_deleted=false")
  List<String> findGoalRuleIdByEi(@Param("tenantId") String tenantId, @Param("topologyDescribeId") String[] topologyDescribeId, @Param("source") int source);

  @Select("select source_id from bi_mt_topology_describe where tenant_id=#{tenantId} and source=#{source}")
  List<String> findBiMtSourceIdsByEi(@Param("tenantId") String tenantId, @Param("source") Integer source);

  @Select("SELECT source_id FROM bi_mt_topology_describe WHERE tenant_id=#{tenantId} AND source in(1,2) " +
          "AND replace(source_id,'_dept','') IN (" +
          "SELECT CASE WHEN grd.goal_rule_id is NULL THEN gr.id ELSE concat(grd.goal_rule_id,'|',grd.id) END AS source_id " +
          "FROM " +
          "(SELECT id FROM goal_rule WHERE tenant_id=#{tenantId} AND is_deleted=0 AND status in('1','2','6')) gr " +
          "LEFT JOIN " +
          "(SELECT id, goal_rule_id FROM goal_rule_detail WHERE tenant_id=#{tenantId} AND is_deleted=0) grd " +
          "ON gr.id=grd.goal_rule_id )")
  List<String> findGoalBiMtSourceIdsByEi(@Param("tenantId") String tenantId);

}
