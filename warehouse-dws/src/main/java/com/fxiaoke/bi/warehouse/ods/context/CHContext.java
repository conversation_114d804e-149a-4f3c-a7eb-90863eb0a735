package com.fxiaoke.bi.warehouse.ods.context;

import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.helper.Pair;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.jedis.support.JedisCmd;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.*;
import java.time.LocalTime;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.regex.Pattern;

@Slf4j
public class CHContext {

  public static final String KEY = "fs-bi-clickhouse";

  public static final Pattern numericArrayRex = Pattern.compile("^numeric\\((\\d+),(\\d+)\\)\\[\\]");

  public static final int DEFAULT_SCALE = 20;

  public static final String CH_JDBC_URL_TEMPLATE = "jdbc:clickhouse://%s:%d/%s";

  public static final String NO = "NO";

  public static final String YES = "YES";

  public static final String BI_SYS_FLAG = "bi_sys_flag";

  public static final String BI_SYS_BATCH_ID = "bi_sys_batch_id";
  public static final String BI_SYS_IS_DELETED = "bi_sys_is_deleted";
  public static final String BI_SYS_VERSION = "bi_sys_version";
  public static final String BI_SYS_ODS_PART = "bi_sys_ods_part";
  public static final String MT_SUB_TAG_EXT = "num_key";
  public static final int BI_SYS_FLAG_BEFORE = 0;

  public static final int BI_SYS_FLAG_AFTER = 1;

  public static final String PUBLIC = "public";

  public static final String SCH_ = "sch_";

  public static final String OBJECT_DESCRIBE_API_NAME = "object_describe_api_name";

  public static final String REPLACING_MERGE_TREE = "ReplacingMergeTree";
  public static final String REPLICATED_REPLACING_MERGE_TREE = "ReplicatedReplacingMergeTree";

  public static final String ODS_SYNC_EVENT = "ods_sync_event";
  public static final String DWS_CALC_EVENT = "dws_calc_event";
  public static final String BI_WAREHOUSE_EVENT_TOPIC = "bi-warehouse-event";
  public static final String BI_WAREHOUSE_EVENT_TOPIC_VIP = "bi-warehouse-event-vip";
  public static final String BI_WAREHOUSE_EVENT_TOPIC_GRAY = "bi-warehouse-event-gray";
  /**
   * 维度变更呢发送灰度topic名称
   */
  public static final String BI_DIM_SLOW_MESSAGE_GRAY = "bi_dim_operate";
  public static final String BI2BI_OFFLINE_GRAY = "bi2bi_offline_gray";
  public static final String BI_CUSTOM_DIM_MESSAGE_GRAY = "bi_custom_dim_message_gray";
  public static final String BASE_CRMFEEDRELATION = "base_crmfeedrelation";
  public static final String OBJECT_DATA = "object_data";
  public static final String OBJECT_DATA_LANG = "object_data_lang";
  public static final String BI_MT_TOPOLOGY_TABLE = "bi_mt_topology_table";
  public static final String BIZ_ACCOUNT = "biz_account";
  public static final String GOAL_VALUE = "goal_value";
  public static final String BIZ_ACCOUNT_MAIN_DATA = "biz_account_main_data";
  public static final String AccountObj = "AccountObj";
  public static final String GoalValueObj = "GoalValueObj";
  public static final String AccountMainDataObj = "AccountMainDataObj";
  public static final String _LANG = "_lang";

  public static final Pattern NaturalNumbersRex = Pattern.compile("^[0-9]\\d*$");

  public static final String AGG_DOWNSTREAM_DATA = "agg_downstream_data";
  public static final String DOWNSTREAM = "downstream";

  public static Set<Class<? extends Serializable>> PRIMITIVE_TYPE_SET = Set.of(byte.class, Byte.class, Byte[].class, short.class, Short.class, Short[].class, int.class, Integer.class, Integer[].class, long.class, Long.class, Long[].class, float.class, Float.class, Float[].class, double.class, Double.class, Double[].class, char.class, Character.class, Character[].class, boolean.class, Boolean.class, Boolean[].class, String[].class, int[].class, long[].class, float[].class, double[].class, char[].class, boolean[].class);
  public static final String[] times = {"last_modified_time", "last_modify_time", "mtime", "create_time", "created_time", "ctime"};
  public static String getCHJdbcUrl(String ip, int port, String db) {
    return String.format(CH_JDBC_URL_TEMPLATE, ip, port, db);
  }

  public static String getDBName(String dbURL) {
    String db;
    int index = dbURL.lastIndexOf("/");
    if (dbURL.contains("?")) {
      db = dbURL.substring(index + 1, dbURL.indexOf("?"));
    } else {
      db = dbURL.substring(index + 1);
    }
    if (StringUtils.isNotBlank(db)) {
      return db;
    }
    return "default";
  }

  /**
   * @param dbURL
   * @return
   */
  public static Pair<String, Integer> parseIpAndPort(String dbURL) {
    if (dbURL.startsWith("jdbc:clickhouse://")) {
      int index = dbURL.lastIndexOf("/");
      String ipAndPort = dbURL.substring(18, index);
      String ip = ipAndPort.split(":")[0];
      Integer port = Integer.parseInt(ipAndPort.split(":")[1]);
      return Pair.of(ip, port);
    } else {
      throw new RuntimeException("not support this url:" + dbURL);
    }
  }

  public static final int SYS_DEFAULT_EI = -1;

  public static final String LAST_MODIFIED_TIME = "last_modified_time";
  public static final String CREATE_TIME = "create_time";
  public static final String CREATE_DATE = "create_date";

  public static final String SYS_MODIFIED_TIME = "sys_modified_time";

  public static String getCHTableColumnCacheKey(String chDB, String tableName) {
    return String.format("ch:%s:%s:public", chDB, tableName);
  }


  //清洗后存储介质
  public enum ETLTarget {
    CSV, OBJECT
  }

  public static Float[] createQuantilesLevel(int level) {
    switch (level) {
      case 10 -> {
        return new Float[] {0.1f, 0.2f, 0.3f, 0.4f, 0.5f, 0.6f, 0.7f, 0.8f, 0.9f};
      }
      case 100 -> {
        Float[] levels = new Float[99];
        for (int i = 0; i < 99; i++) {
          levels[i] = (i + 1) / 100.0f;
        }
        return levels;
      }
      case 1000 -> {
        Float[] levels = new Float[999];
        for (int i = 0; i < 999; i++) {
          levels[i] = (i + 1) / 1000.0f;
        }
        return levels;
      }
      default -> {
        throw new RuntimeException("no support this level");
      }
    }
  }

  /**
   * 动态生成分位数数组集合 用于merge agg_data
   *
   * @param total     表数据量
   * @param batchSize 分位间隔大小
   * @return 分位数 系数集合
   */
  public static List<BigDecimal> calQuantiles(long total, long batchSize) {
    if (batchSize == 0L) {
      throw new RuntimeException("batchSize is 0");
    }
    List<BigDecimal> levels = Lists.newArrayList();
    int maxMultiple;
    int multiple = (int) Math.round((double) total / batchSize);
    if (multiple <= 1) {
      return Lists.newArrayList();
    } else if (multiple <= 10) {
      maxMultiple = 9;
    } else if (multiple <= 100) {
      maxMultiple = 99;
    } else if (multiple <= 1000) {
      maxMultiple = 999;
    } else {
      throw new RuntimeException("too many multiple " + total + "/" + batchSize);
    }
    BigDecimal bigDecimal = BigDecimal.valueOf((double) batchSize / total).setScale(3, RoundingMode.HALF_UP);
    log.info(String.valueOf(bigDecimal.floatValue()));
    for (int i = 1; i <= maxMultiple; i++) {
      BigDecimal level = bigDecimal.multiply(BigDecimal.valueOf(i));
      if (level.compareTo(BigDecimal.valueOf(0.999f)) > 0) {
        break;
      }
      levels.add(level.setScale(3, RoundingMode.HALF_UP));
    }
    return levels;
  }

  public static <T> T queryOne(String sql, JdbcConnection jdbcConnection, Class<T> clazz, Object... args) {
    List<T> list = queryMany(sql, jdbcConnection, clazz, args);
    return list.isEmpty() ? null : list.get(0);
  }

  /**
   * 查询结果列名需要与 clazz 字段相对应
   *
   * @param clazz 属于{@linkplain CHContext#PRIMITIVE_TYPE_SET} 中的类时会直接取SQL第一列结果返回
   */
  public static <T> List<T> queryMany(String sql, JdbcConnection jdbcConnection, Class<T> clazz, Object... args) {
    List<T> list = new ArrayList<>();
    try (Connection conn = jdbcConnection.connection(); PreparedStatement ps = conn.prepareStatement(sql)) {
      for (int i = 0; i < args.length; i++) {
        ps.setObject(i + 1, args[i]);
      }
      ResultSet rs = ps.executeQuery();
      ResultSetMetaData rsmd = rs.getMetaData();
      int columnCount = rsmd.getColumnCount();
      while (rs.next()) {
        if (PRIMITIVE_TYPE_SET.contains(clazz)) {
          Object value = rs.getObject(1); // 假设字符串值在第一列中
          list.add(clazz.cast(value));
        } else {
          T t = clazz.getDeclaredConstructor().newInstance();
          for (int i = 0; i < columnCount; i++) {
            String fieldName = rsmd.getColumnLabel(i + 1);
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = rs.getObject(fieldName);
            if (value != null) {
              field.set(t, value);
            }
          }
          list.add(t);
        }
      }
    } catch (Exception e) {
      log.error("SQL查询执行失败：{}，参数：{}!", sql, Arrays.toString(args), e);
      throw new RuntimeException(e);
    }
    return list;
  }
  /**
   * 根据表名称生成同步的相关阈值，包含before数据插入的集合size
   * savePoint size大小
   * @param config 配置内容
   * @return
   */
  public static Map<String, com.fxiaoke.common.Pair<Integer, Integer>> createCustomThreshold(String config) {
    Map<String, com.fxiaoke.common.Pair<Integer, Integer>> tmpMap = Maps.newHashMap();
    if (StringUtils.isNotBlank(config)) {
      List<String> limits = Splitter.on(CharMatcher.anyOf(",|")).omitEmptyStrings().splitToList(config);
      if (CollectionUtils.isNotEmpty(limits)) {
        limits.forEach(limit -> {
          String[] values = limit.split("\\^");
          if (values.length == 3) {
            try {
              tmpMap.put(values[0], com.fxiaoke.common.Pair.of(Integer.parseInt(values[1]), Integer.parseInt(values[2])));
            } catch (Exception e) {
              log.error("createCustomThreshold error:{}", config, e);
            }
          }
        });
      }
    }
    return tmpMap;
  }

  /**
   * 判断是否开启ch merge
   * @param allowMergeCHDB
   * @param chURL
   * @return
   */
  public static boolean openMerge(Set<String> allowMergeCHDB, String chURL) {
    String chDbName = CHContext.getDBName(chURL);
    return ((allowMergeCHDB.contains("*") || allowMergeCHDB.contains(chDbName)) &&
      !GrayManager.isAllowByRule("skip_merge_ch", chDbName) &&
      !GrayManager.isAllowByRule("skip_merge_ch_jdbc", chURL.replace("jdbc:clickhouse://","").replaceAll("[:\\./]","_")));
  }

  /**
   * 检测是否可以进入merge 状态
   * @param chDBUrl
   * @return
   */
  public static boolean shouldMerge(String chDBUrl) {
    if (!CHContext.openMerge(WarehouseConfig.allowMergeCHDB, chDBUrl)) {
      log.warn("shouldWait4IncrementMergeMergeAggData wait4MergeAggData chURL:{},do not allow merge", chDBUrl);
      return false;
    }
    long minuteOfDay = LocalTime.now().getLong(ChronoField.MINUTE_OF_DAY);
    if (minuteOfDay < WarehouseConfig.mergeBeginTime || minuteOfDay > WarehouseConfig.mergeEndTime) {
      log.info("shouldMerge ? currentTime:{} is not in merge time range {},{}", minuteOfDay, WarehouseConfig.mergeBeginTime, WarehouseConfig.mergeEndTime);
      return false;
    }
    return true;
  }

  public static void sqlErrDelCache(Exception exception, JedisCmd jedisCmd, String chDBUrl, String tableName) {
    Throwable cause = exception.getCause();
    if (Objects.nonNull(cause) && cause instanceof SQLException && (StringUtils.isNotBlank(cause.getMessage()) && cause.getMessage().contains("No such column"))) {
      String chDB = CHContext.getDBName(chDBUrl);
      String key = CHContext.getCHTableColumnCacheKey(chDB, tableName);
      try {
        jedisCmd.del(key);
      } catch (Exception ex) {
        log.error("Run insertSQL exception del redis cache error, key:{}", key, ex);
      }
    }
  }
}
