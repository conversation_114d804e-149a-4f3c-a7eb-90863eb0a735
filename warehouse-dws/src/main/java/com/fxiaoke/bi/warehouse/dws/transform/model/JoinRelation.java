package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.common.db.er.PGColumnType;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.helper.StringHelper;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.Setter;

@Builder
@EqualsAndHashCode(exclude = "fieldType")
public class JoinRelation {
  /**
   * 引用列 source_table.column=apiName.id
   */
  public String column;
  /**
   * udf_obj_field type object_reference,employee, department, master_detail,reference(引用类型自定义维度)
   */
  public final String fieldType;
  /**
   * 被引用的数据所在的对象,apiName,例如：object_data XXX__c,
   * mt_data_tag表中的object_describe_api_name,
   */
  public final String apiName;
  @Setter
  public AggJoinType joinType;

  public final PGColumnType columnType;

  public JoinRelation(@NonNull String column,
                      String fieldType,
                      @NonNull String apiName,
                      AggJoinType joinType,
                      PGColumnType columnType) {
    this.column = column;
    this.fieldType = fieldType;
    this.apiName = apiName;
    this.joinType = joinType;
    this.columnType = columnType;
  }

  public String joinColumn(String table) {
    String columnName = column;
    if ("bpm_instance".equals(table)) {
      if ("relatedObject".equals(column)) {
        columnName = "objectDataId";
      }
    }
    if ("bpm_task".equals(table)) {
      if ("relatedObject".equals(column)) {
        columnName = "objectDataId";
      }
    }
    if (StringHelper.containUpperChar(columnName)) {
      columnName = "\"" + columnName + "\"";
    }
    return columnName;
  }

  /**
   * 返回 relation apiName被lookup字段，eg. org_employee_user.user_id
   * feed_relation.source_data_id
   * @param standalone
   * @param tenantId
   * @return
   */
  public String referentColumn(boolean standalone, String tenantId) {
    switch (apiName) {
      case "bi_mt_data_tag_v" -> {
        return "data_id";
      }
      case "v_saleactionstage" -> {
        return "sale_action_stage_id";
      }
      //active_record join feed_relation
      case "feed_relation", "biz_journal_feed_relation", "callcenter_relation", "service_log_feed_relation",
        "biz_schedule_feed_relation", "biz_behavior_record_relation" -> {
        return "source_data_id";
      }
      //虚拟指标人员主题部门规则用和org_employee_user.main_department关联
//      case Constants.ORG_EMPLOYEE_USER_GOAL_VALUE  -> {
//        return "main_department";
//      }
      case "product_category" -> {
        return "category".equals(column) ? "code" : "id";
      }
      case "stage_runtime" -> {
        return "stage_id".equals(column) ? "stage_id" : "id";
      }
      default -> {
        if (apiName.endsWith("__c")) {
          return standalone ? "id" : "_id";
        }
        if (Constants.employee_api_name.equals(apiName)) {
          if ("owner".equalsIgnoreCase(column)) {
            return "user_id";
          } else if (FieldType.OBJECT_REFERENCE.equals(fieldType)) {
            return "id";
          } else {
            return "user_id";
          }
        }
        if (Constants.department_api_name.equals(apiName)) {
          if (FieldType.OBJECT_REFERENCE.equals(fieldType)) {
            return "id";
          } else {
            return "dept_id";
          }
        }
      }
    }
    return "id";
  }
}
