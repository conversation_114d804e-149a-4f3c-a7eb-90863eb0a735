package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.common.db.er.JoinType;

/**
 * 对象引用关系
 * e.g. 比如以B对象为第一视角
 * <p>
 * A对象 -left-> B对象 -right-> C对象
 *
 * <AUTHOR>
 * @since 2022/10/30
 */
public enum AggJoinType {
  /**
   * 被其他对象引用
   */
  LEFT{
    @Override
    public JoinType toJoinType() {
      return JoinType.LEFT_JOIN;
    }
  },
  /**
   * 引用其他对象
   */
  RIGHT{
    @Override
    public JoinType toJoinType() {
      return JoinType.RIGHT_JOIN;
    }
  },
  /**
   *
   */
  INNER{
    @Override
    public JoinType toJoinType() {
      return JoinType.INNER_JOIN;
    }
  };

  public JoinType toJoinType(){
    return JoinType.LEFT_JOIN;
  }
}
