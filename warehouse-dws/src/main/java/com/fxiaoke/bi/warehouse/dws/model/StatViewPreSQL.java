package com.fxiaoke.bi.warehouse.dws.model;

import com.fxiaoke.bi.warehouse.dws.service.DbTableSyncInfoInterface;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2023/6/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatViewPreSQL {
  //租户id
  private String tenantId;
  /**
   * 图表类型 0：agg；1：老目标；2：多维目标；
   */
  private int sourceType;
  /**
   * 图表id或目标id,可以为空
   */
  private String sourceId;
  /**
   * 维度和指标槽位
   */
  private Map<String, String> statFieldLocation;
  /**
   * 去重计数列槽位
   */
  private Map<String, String> uniqFieldLocation;
  /**
   * 预览sql
   */
  private String preViewSQL;
  /**
   * 抽样类型:0:全量，1:抽样
   */
  private Integer sampleType;
  /**
   * 明细表的最大更新时间
   */
  private long maxModifiedTime;
  /**
   * 统计图指标明细查询需要返回的相关信息
   */
  private DbTableSyncInfoInterface.FieldAggDelayInfo fieldAggDelayInfo;
  /**
   * 统计图谓词下推
   */
  private Set<String> pushDownFields;
}
