package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.db.entity.StatFieldStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.er.*;
import com.fxiaoke.bi.warehouse.common.util.FieldType;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.ObjectConfigManager;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import com.fxiaoke.bi.warehouse.dws.exception.ParseRuleException;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.transform.model.WhereRule;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.helper.CollectionHelper;
import com.fxiaoke.helper.StringHelper;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 加载AggRule
 */
@Slf4j
@Repository
public class AggRuleDao extends RuleDao {
  @Resource
  private UdfObjFieldMapper udfObjFieldMapper;
  @Resource
  private MappingService mappingService;
  private Set<String> skipRules = Sets.newHashSet();
  private String multiCurrencyUrl;
  //ch agg data 固定槽位列如：data_auth_code
  private Set<String> chAggDataFixSlot = Sets.newHashSet();

  /**
   * {@see <a href='https://oss.foneshare.cn/cms/edit/config/17121'>bi-statictic-agg</a>}
   */
  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("bi-statictic-agg", new IniChangeListener("agg_data2_refactor") {
      @Override
      public void iniChanged(IniConfig iniConfig) {
        String skipRulesConfig = iniConfig.get("agg.skip.rules");
        if (StringHelper.isNullOrBlank(skipRulesConfig)) {
          skipRules = Sets.newHashSet();
        } else {
          skipRules = Sets.newHashSet(Splitter.on(CharMatcher.anyOf(","))
                                              .trimResults()
                                              .omitEmptyStrings()
                                              .splitToList(skipRulesConfig));
        }
        multiCurrencyUrl = iniConfig.get("multi_currency_url");
        String chAggFixSlot = iniConfig.get("ch.agg.fix.slot", "owner,data_auth_code");
        chAggDataFixSlot = Sets.newHashSet(Splitter.on(",").splitToList(chAggFixSlot));
      }
    });
  }


  /**
   * 生成aggRule
   *
   * @param tenantId
   * @param ruleInfo
   * @param standalone
   * @param themeApiName
   * @param ruleId
   * @param cachedTableDefinitions
   * @param aggType
   * @return
   */
  public AggRule createAggRule(String tenantId,
                               JSONObject ruleInfo,
                               boolean standalone,
                               String themeApiName,
                               String ruleId,
                               Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions,
                               String aggType) {
    String fieldId = ruleInfo.getString("field_id");
    String aggApiName = ruleInfo.getString("check_object_api_name");
    String timeZone = Constants.getTimeZoneOrDefault(ruleInfo.getString("time_zone"), Constants.Asia_Shanghai);
    //构造AggRule
    ObjectIdRule objectIdRule = this.buildObjectIdRule(tenantId, standalone, themeApiName, aggApiName, ruleInfo,
      cachedTableDefinitions);
    if (objectIdRule == null) {
      log.error("objectIdRule is null tenantId:{},ruleId:{}", tenantId, ruleId);
      throw new ParseRuleException(String.format("objectIdRule is null tenantId:%s,ruleId:%s", tenantId, ruleId));
    }
    TimeRule timeRule = buildTimeRule(tenantId, standalone, themeApiName, aggApiName, ruleInfo, cachedTableDefinitions);
    if (timeRule == null) {
      throw new ParseRuleException(String.format("%s:%s time rule collapsed", tenantId, ruleId));
    }
    ValueRule valueRule = this.buildValueRule(tenantId, standalone, themeApiName, aggApiName, ruleInfo,
      cachedTableDefinitions);
    if (valueRule == null) {
      throw new ParseRuleException(String.format("%s:%s value rule collapsed", tenantId, ruleId));
    }
    WhereRules whereRules = this.buildWheres(tenantId, standalone, themeApiName, aggApiName, ruleInfo,
      cachedTableDefinitions);
    return new AggRule(tenantId, standalone, fieldId, ruleId, themeApiName, false, aggType, aggApiName, null, null,
      objectIdRule, valueRule, timeRule, whereRules, timeZone, mappingService);
  }

  /**
   * 解析aggRule 生成统计图join 规则树
   *
   * @param tenantId            租户id
   * @param ruleId              规则id
   * @param statViewDimBO       统计图信息封装
   * @param aggAliasMapper      指标别名cache
   * @param dimsMapper          维度别名计算cache
   * @param dimFieldAliasMapper 维度字段fieldId和别名映射关系
   * @return
   */
  public TopologyTableAggRule parseAggRuleFromMap(String tenantId,
                                                  String ruleId,
                                                  StatViewDimBO statViewDimBO,
                                                  Map<String, AtomicInteger> aggAliasMapper,
                                                  Map<String, AtomicInteger> dimsMapper,
                                                  Map<String, String/*dim字段别名*/> dimFieldAliasMapper) {
    log.info("parse agg rule {}:{}", tenantId, ruleId);
    //计算别名
    Map<String, AtomicInteger> aliasMapper = Maps.newHashMap();
    //缓存列类型
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    Map<String, Object> ruleInfoMap = this.findAggRuleMapFromDB(tenantId, ruleId);
    if (null == ruleInfoMap) {
      throw new RuntimeException(String.format("findAggRuleMapFromDB error tenantId:%s,ruleId:%s", tenantId, ruleId));
    }
    JSONObject ruleInfo = new JSONObject(ruleInfoMap);
    String themeApiName = ruleInfo.getString("theme_api_name");
    String fieldId = ruleInfo.getString("field_id");
    String aggApiName = ruleInfo.getString("check_object_api_name");
    String checkApiNameLookupField = ruleInfo.getString("check_apiname_lookup_field");
    String aggDimType = ruleInfo.getString("agg_dim_type");
    String timeZone = Constants.getTimeZoneOrDefault(ruleInfo.getString("time_zone"), Constants.Asia_Shanghai);
    boolean standalone = this.standalone(tenantId);
    Map<String, String> fieldLocationMap = Maps.newHashMap();
    AggRule aggRule = this.createAggRule(tenantId, ruleInfo, standalone, themeApiName, ruleId, cachedTableDefinitions, aggDimType);
    aggRule.init();
    NodeTable aggObjNodeTable = this.createAggNodeTable(standalone, aggApiName, aliasMapper);
    //获取theme table
    NodeTable themeNodeTable = this.joinObjectIdQuoteNodeTableAndGet(tenantId, standalone, aggObjNodeTable, aliasMapper, themeApiName, checkApiNameLookupField, aggRule.objectIdRule, cachedTableDefinitions);
    themeNodeTable.setObjectIdTable(true);
    //构建dim
    List<String> dimConfigStringList = Lists.newArrayList();
    List<CustomDimField> customerDimList = Lists.newArrayList();
    if (statViewDimBO != null) {
      List<DimRule> themeDimRules = statViewDimBO.getThemeDimRules();
      if (CollectionUtils.isNotEmpty(themeDimRules)) {
        this.buildDims(tenantId, standalone, themeDimRules, themeNodeTable, aliasMapper, dimFieldAliasMapper, dimsMapper, dimConfigStringList, fieldLocationMap);
      }
      //补充主对象维度
      if (CollectionUtils.isNotEmpty(statViewDimBO.getMasterDimRules())) {
        JoinRelation joinRelation = this.createRelation(tenantId, standalone, themeApiName,
          statViewDimBO.getLookupField(), statViewDimBO.getMasterApiName(), cachedTableDefinitions, AggJoinType.INNER);
        if (joinRelation == null) {
          log.error("can not find master obj lookup field tenantId:{},slave apiName:{}", tenantId, themeApiName);
          return null;
        }
        NodeTable masterNodeTable = this.appendLookUpNodeTable(tenantId, standalone, themeNodeTable, aliasMapper, joinRelation);
        this.buildDims(tenantId, standalone, statViewDimBO.getMasterDimRules(), masterNodeTable, aliasMapper,
          dimFieldAliasMapper, dimsMapper, dimConfigStringList, fieldLocationMap);
      }
      // 自定义维度
      List<DimRule> customDimRules = statViewDimBO.getCustomDimRules();
      if (CollectionUtils.isNotEmpty(statViewDimBO.getCustomDimRules())) {
        customerDimList = this.buildCustomDims(tenantId, standalone, customDimRules, themeNodeTable, aliasMapper, dimFieldAliasMapper, dimsMapper, dimConfigStringList, fieldLocationMap);
      }
    }
    //action_date
    this.joinQuoteNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.timeRule, true, false);
    String actionDateConfig = aggRule.timeRule.createActionDateConfig();
    //指标
    NodeTable valueNodeTable = this.joinQuoteNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.valueRule, true, true);
    String aggColumnName = aggRule.valueRule.createAggColumnName(aggAliasMapper);
    String aggConfigString = String.format("%s:%s", aggRule.valueRule.createAggConfigString(), aggColumnName);
    fieldLocationMap.put(fieldId, aggColumnName);
    //where 条件
    List<WhereConfig> preWhereConfigList = this.createPreWhereConfigList(tenantId, aggObjNodeTable, aggRule.valueRule);
    List<String> usedTables = Lists.newArrayList();
    NodeTable.findAggRuleTableAlias(aggObjNodeTable, usedTables);
    List<List<WhereConfig>> whereConfigList = this.dealWithWhereConfig(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.whereRules, timeZone);
    dimConfigStringList.sort(Comparator.nullsFirst(Comparator.naturalOrder()));
    customerDimList.sort(Comparator.comparing(CustomDimField::getFieldId));
    TopologyTableAggRule statRule = new TopologyTableAggRule(fieldId, TopologyTableStatus.Prepared.getValue(), aggObjNodeTable, dimConfigStringList, actionDateConfig, Lists.newArrayList(aggConfigString), whereConfigList, preWhereConfigList, fieldLocationMap, null, null, null, customerDimList, null);
    //判断是否走inner join
    this.useInnerJoin(statRule, usedTables);
    //检测去重计数列
    this.checkUniqField(tenantId, standalone, aggRule.valueRule, statRule, aliasMapper, aggAliasMapper, dimsMapper, dimFieldAliasMapper, statViewDimBO, valueNodeTable);
    //检测是否有预设对象关联扩展对象
    Utils.checkExtendObjDataExist(tenantId,standalone,statRule);
    return statRule;
  }

  /**
   * 创建涉及目标的统计图的TopologyTableAggRule
   * @param tenantId
   * @param ruleId
   * @param statViewDimBO
   * @param aggAliasMapper
   * @param dimsMapper
   * @param dimFieldAliasMapper
   * @param themeApiName
   * @return
   */
  public TopologyTableAggRule parseGoalRuleFromMap(String tenantId,
                                                   String ruleId,
                                                   StatViewDimBO statViewDimBO,
                                                   Map<String, AtomicInteger> aggAliasMapper,
                                                   Map<String, AtomicInteger> dimsMapper,
                                                   Map<String, String/*dim字段别名*/> dimFieldAliasMapper,
                                                   String themeApiName,
                                                   String checkCyCle,
                                                   boolean isPreSql) {
    //计算别名
    Map<String, AtomicInteger> aliasMapper = Maps.newHashMap();
    //缓存列类型
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    boolean standalone = this.standalone(tenantId);
    Map<String, String> fieldLocationMap = Maps.newHashMap();
    String lookupColumn = Constants.CHECK_DIMENSION_VALUE1;
    String aggApiName = Constants.findGoalValueObjTable(checkCyCle);
    NodeTable aggObjNodeTable = this.createAggNodeTable(standalone, aggApiName, aliasMapper);
    //虚出的人员主题的部门规则,关联条件要调整
    if (Constants.MULTI_GOAL_RULE_ID_DEPT.equals(ruleId)) {
      lookupColumn = Constants.CHECK_DIMENSION_VALUE2;
      aggObjNodeTable.appendColumns(Constants.CHECK_DIMENSION_VALUE1);
    }
    AggRule aggRule = this.createGoalRule(tenantId, themeApiName, standalone, themeApiName, ruleId, lookupColumn);
    //获取theme table
    NodeTable themeNodeTable = this.createGoalJoinNodeTable(tenantId, aggObjNodeTable, standalone, aggRule.objectIdRule.joinRelation,
            themeApiName, aliasMapper, lookupColumn, ruleId);
    //构建dim
    List<String> dimConfigStringList = Lists.newArrayList();
    if (statViewDimBO != null) {
      List<DimRule> themeDimRules = statViewDimBO.getThemeDimRules();
      if (CollectionUtils.isNotEmpty(themeDimRules)) {
        this.buildDims(tenantId, standalone, themeDimRules, themeNodeTable, aliasMapper, dimFieldAliasMapper,
                dimsMapper, dimConfigStringList, fieldLocationMap);
      }
      //补充主对象维度
      if (CollectionUtils.isNotEmpty(statViewDimBO.getMasterDimRules())) {
        JoinRelation joinRelation = this.createRelation(tenantId, standalone, themeApiName,
                statViewDimBO.getLookupField(), statViewDimBO.getMasterApiName(), cachedTableDefinitions, AggJoinType.INNER);
        if (joinRelation == null) {
          log.error("can not find master obj lookup field tenantId:{},slave apiName:{}", tenantId, themeApiName);
          return null;
        }
        NodeTable masterNodeTable = this.appendLookUpNodeTable(tenantId, standalone, themeNodeTable, aliasMapper,
                joinRelation);
        this.buildDims(tenantId, standalone, statViewDimBO.getMasterDimRules(), masterNodeTable, aliasMapper,
                dimFieldAliasMapper, dimsMapper, dimConfigStringList, fieldLocationMap);
      }
    }
    //action_date
    this.joinQuoteNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.timeRule, true, false);
    String actionDateConfig = aggRule.timeRule.createActionDateConfig();
    //指标
    this.joinQuoteNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.valueRule, true, true);
    String aggColumnName = aggRule.valueRule.createAggColumnName(aggAliasMapper);
    String aggConfigString = String.format("%s:%s", aggRule.valueRule.createAggConfigString(), aggColumnName);
    fieldLocationMap.put(ruleId, aggColumnName);
    //where 条件
    List<WhereConfig> preWhereConfigList = this.createPreWhereConfigList(tenantId, aggObjNodeTable, aggRule.valueRule);
    if (Constants.MULTI_GOAL_RULE_ID_DEPT.equals(ruleId)) {
      preWhereConfigList.add(WhereConfig.builder()
              .whereExpr("(" + aggObjNodeTable.getAlias() + ".check_dimension_value1=" + themeNodeTable.getAlias() + ".main_department " +
                      "OR empty(" + aggObjNodeTable.getAlias() + ".check_dimension_value2))")
              .build());
    }
    String goalSubWhereSql = Constants.subWhereGoalValueObj(tenantId, themeApiName, ruleId, checkCyCle);
    Constants.appendSubWhereSQL(aggObjNodeTable, aggObjNodeTable.getAlias(), goalSubWhereSql);
    aggObjNodeTable.appendSubWheres(" AND notEmpty(check_dimension_value1) ");
    dimConfigStringList.sort(Comparator.nullsFirst(Comparator.naturalOrder()));
    return new TopologyTableAggRule(ruleId, TopologyTableStatus.Prepared.getValue(), aggObjNodeTable, dimConfigStringList,
      actionDateConfig, Lists.newArrayList(aggConfigString), Lists.newArrayList(), preWhereConfigList, fieldLocationMap,
      null, null, null, null, null);
  }

  /**
   * 创建涉及下游指标的TopologyTableAggRule
   * @param tenantId
   * @param ruleId
   * @param fieldId
   * @param statViewDimBO
   * @param aggAliasMapper
   * @param dimsMapper
   * @param dimFieldAliasMapper
   * @param themeApiName
   * @param downViewIds 用于TopologyTable.TopologyTableAggDownStream
   * @return
   */
  public TopologyTableAggRule parseDownStreamRuleFromMap(String tenantId,
                                                         String ruleId,
                                                         String fieldId,
                                                         StatViewDimBO statViewDimBO,
                                                         Map<String, AtomicInteger> aggAliasMapper,
                                                         Map<String, AtomicInteger> dimsMapper,
                                                         Map<String, String/*dim字段别名*/> dimFieldAliasMapper,
                                                         String themeApiName,
                                                         Set<String> downViewIds,
                                                         Set<String> upTables,
                                                         boolean isPreSql) {
    log.info("parse down stream rule {}:{}", tenantId, fieldId);
    //计算别名
    Map<String, AtomicInteger> aliasMapper = Maps.newHashMap();
    //缓存列类型
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    Map<String, Object> statFieldInfoMap = this.findStatFieldMapFromDB(tenantId, fieldId);
    if (null == statFieldInfoMap) {
      throw new RuntimeException(String.format("findStatFieldMapFromDB error tenantId:%s,fieldId:%s", tenantId, fieldId));
    }

    JSONObject statFieldInfo = new JSONObject(statFieldInfoMap);
    String aggApiName = "agg_downstream_data";
    String downstreamViewId = statFieldInfo.getString("downstream_view_id");
    if (StringUtils.isBlank(downstreamViewId)) {
      throw new RuntimeException(String.format("downstream_view_id error tenantId:%s,fieldId:%s", tenantId, fieldId));
    }
    downViewIds.add(downstreamViewId);
    String timeZone = Constants.getTimeZoneOrDefault(statFieldInfo.getString("time_zone"), Constants.Asia_Shanghai);
    boolean standalone = this.standalone(tenantId);
    Map<String, String> fieldLocationMap = Maps.newHashMap();
    AggRule aggRule = this.createDownStreamRule(tenantId, aggApiName, statFieldInfo, standalone, themeApiName, fieldId, ruleId, downstreamViewId);
    aggRule.init();
    NodeTable aggObjNodeTable = this.createAggNodeTable(standalone, aggApiName, aliasMapper);
    //获取theme table
    NodeTable themeNodeTable = this.joinObjectIdQuoteNodeTableAndGet(tenantId, standalone, aggObjNodeTable,
      aliasMapper, themeApiName, "object_id", aggRule.objectIdRule, cachedTableDefinitions);
    themeNodeTable.setObjectIdTable(true);
    //构建dim
    List<String> dimConfigStringList = Lists.newArrayList();
    if (statViewDimBO != null) {
      List<DimRule> themeDimRules = statViewDimBO.getThemeDimRules();
      if (CollectionUtils.isNotEmpty(themeDimRules)) {
        this.buildDims(tenantId, standalone, themeDimRules, themeNodeTable, aliasMapper, dimFieldAliasMapper,
          dimsMapper, dimConfigStringList, fieldLocationMap);
      }
      //补充主对象维度
      if (CollectionUtils.isNotEmpty(statViewDimBO.getMasterDimRules())) {
        JoinRelation joinRelation = this.createRelation(tenantId, standalone, themeApiName,
          statViewDimBO.getLookupField(), statViewDimBO.getMasterApiName(), cachedTableDefinitions, AggJoinType.INNER);
        if (joinRelation == null) {
          log.error("can not find master obj lookup field tenantId:{},slave apiName:{}", tenantId, themeApiName);
          return null;
        }
        NodeTable masterNodeTable = this.appendLookUpNodeTable(tenantId, standalone, themeNodeTable, aliasMapper,
          joinRelation);
        this.buildDims(tenantId, standalone, statViewDimBO.getMasterDimRules(), masterNodeTable, aliasMapper,
          dimFieldAliasMapper, dimsMapper, dimConfigStringList, fieldLocationMap);
      }
    }
    //action_date
    this.joinQuoteNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.timeRule, true, false);
    String actionDateConfig = aggRule.timeRule.createActionDateConfig();
    //指标
    NodeTable valueNodeTable = this.joinQuoteNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.valueRule, true, true);
    String aggColumnName = aggRule.valueRule.createAggColumnName(aggAliasMapper);
    String aggConfigString = String.format("%s:%s", aggRule.valueRule.createAggConfigString(), aggColumnName);
    fieldLocationMap.put(fieldId, aggColumnName);
    //where 条件
    List<WhereConfig> preWhereConfigList = this.createPreWhereConfigList(tenantId, aggObjNodeTable, aggRule.valueRule);
    List<String> usedTables = Lists.newArrayList();
    NodeTable.findAggRuleTableAlias(aggObjNodeTable, usedTables);
    List<List<WhereConfig>> whereConfigList = this.dealWithWhereConfig(tenantId, standalone, aggObjNodeTable, aliasMapper, aggRule.whereRules, timeZone);
    dimConfigStringList.sort(Comparator.nullsFirst(Comparator.naturalOrder()));
    TopologyTableAggRule statRule = new TopologyTableAggRule(fieldId, StatFieldStatusEnum.Initialize.getCode(), aggObjNodeTable, dimConfigStringList, actionDateConfig, Lists.newArrayList(aggConfigString), whereConfigList, preWhereConfigList, fieldLocationMap, null, null, null, null, null);
    //判断是否走inner join
    this.useInnerJoin(statRule, usedTables);
    //检测去重计数列
    this.checkUniqField(tenantId, standalone, aggRule.valueRule, statRule, aliasMapper, aggAliasMapper, dimsMapper, dimFieldAliasMapper, statViewDimBO, valueNodeTable);
    //1.如果是生成topologyTable所有设计表名的地方加后缀，并获取原表名;2.如果是预览sql则直接走原明细表
    if (!isPreSql) {
      upTables.addAll(this.replaceAllTableName(statRule));
    }
    return statRule;
  }



  /**
   * 设计表名的地方加固定后缀 Constants.DOWNSTREAM_SUFFIX
   * @param statRule
   * @return
   */
  private Set<String> replaceAllTableName(TopologyTableAggRule statRule) {
    Set<String> upTables = Sets.newHashSet();
    statRule.getDimConfigStringList().replaceAll(dim -> {
      int index = dim.indexOf(":");
      String tableAlias = dim.substring(0, index);
      String tableName = TableAliasNaming.tableName(tableAlias);
      tableAlias = tableAlias.replace(tableName, tableName + Constants.DOWNSTREAM_SUFFIX);
      return tableAlias + dim.substring(index);
    });
    statRule.getWhereConfigsList().replaceAll(whereConfigs -> {
      whereConfigs.forEach(where -> {
        if (!Constants.AGG_DOWNSTREAM_DATA.equals(where.getTableName())) {
          where.setWhereExpr(where.getWhereExpr()
                                  .replace(where.getTableName(), where.getTableName() + "_downstream"));
        }
      });
      return whereConfigs;
    });
    upTables.add(statRule.getRootNodeTable().getName());
    upTables.addAll(this.findAndRepalceUpTables(statRule.getRootNodeTable().getJoinSet()));
    return upTables;
  }

  /**
   * 涉及表名的地方加固定后缀 Constants.DOWNSTREAM_SUFFIX,并获取原表名
   * @param nodeJoins
   * @return
   */
  private Set<String> findAndRepalceUpTables(Set<NodeJoin> nodeJoins) {
    Set<String> upTables = Sets.newHashSet();
    for (NodeJoin nodeJoin : nodeJoins) {
      NodeTable table = nodeJoin.getTable();
      upTables.add(table.getName());
      table.setAlias(table.getAlias()
                          .replace(table.getName(), table.getName() + Constants.DOWNSTREAM_SUFFIX));
      table.setName(table.getName() + Constants.DOWNSTREAM_SUFFIX);
      if (!table.getJoinSet().isEmpty()) {
        upTables.addAll(this.findAndRepalceUpTables(table.getJoinSet()));
      }
    }
    return upTables;
  }

  /**
   * 创建虚拟指标JoinTableNode
   * @param tenantId
   * @param aggObjNodeTable
   * @param standalone
   * @param aliasMapper
   * @return
   */
  public NodeTable createGoalJoinNodeTable(String tenantId,
                                            NodeTable aggObjNodeTable,
                                            boolean standalone,
                                            JoinRelation joinRelation,
                                            String apiName,
                                            Map<String, AtomicInteger> aliasMapper,
                                            String lookupColumn,
                                           String ruleId) {
    NodeTable themeNodeTable = NodeTable.of(Constants.table(apiName, null, standalone), null,
            Lists.newArrayList(), Sets.newTreeSet(), apiName, lookupColumn, true, null, null);
    NodeJoin themeTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation,
            aggObjNodeTable, themeNodeTable, mappingService);
    //如果是人员主题,且是源规则不是_dept规则的时候,需要将关联条件更正一下
//    if (!Constants.MULTI_GOAL_RULE_ID_DEPT.equals(ruleId) && Constants.employee_api_name.equals(apiName)) {
//      List<String> equalPairs = Lists.newArrayList(themeTableJoin.getOnCondition().getEqualPairs());
//      String[] newEqualPairs = equalPairs.stream()
//                                 .map(pair ->
//                                   pair.contains("${lt}.check_dimension_value1") && pair.contains("${rt}.user_id") ?
//                                   "${lt}.check_dimension_value1=${rt}.main_department" :
//                                   pair)
//                                 .toArray(String[]::new);
//      themeTableJoin.getOnCondition().setEqualPairs(newEqualPairs);
//    }
    return aggObjNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
  }

  /**
   * 创建涉及目标的统计图的AggRule
   * @param tenantId
   * @param aggApiName
   * @param standalone
   * @param themeApiName
   * @param ruleId
   * @return
   */
  public AggRule createGoalRule(String tenantId,
                                String aggApiName,
                                boolean standalone,
                                String themeApiName,
                                String ruleId,
                                String lookupColumn) {
    JoinRelation joinRelation = JoinRelation.builder()
            .joinType(AggJoinType.LEFT)
            .apiName(themeApiName)
            .column(lookupColumn)
            .columnType(PGColumnType.String)
            .fieldType(FieldType.TEXT)
            .build();
    ObjectIdRule objectIdRule = ObjectIdRule.builder()
            .describeApiName(themeApiName)
            .column(Constants.getIdName(standalone, themeApiName))
            .joinRelation(joinRelation)
            .isSingle(false)
            .isUnique(true)
            .build();
    TimeRule timeRule = TimeRule.builder()
            .column(Constants.ACTION_DATE)
            .columnType(PGColumnType.Int8)
            .fieldType("date2")
            .isSingle(true)
            .build();
    ValueRule valueRule = ValueRule.builder()
            .column(Constants.CHECK_DIMENSION_VALUE1)
            .columnType(PGColumnType.String)
            .isSingle(false)
            .isUnique(true)
            .aggType(AggType.count)
            .build();
    return new AggRule(tenantId, standalone, ruleId, ruleId, themeApiName, false, "goal_agg", aggApiName, null, null,
            objectIdRule, valueRule, timeRule, null, null, mappingService);
  }

  /**
   * 创建涉及下游指标的统计图的AggRule
   * @param tenantId
   * @param aggApiName
   * @param standalone
   * @param themeApiName
   * @param ruleId
   * @return
   */
  public AggRule createDownStreamRule(String tenantId,
                                      String aggApiName,
                                      JSONObject statFieldInfo,
                                      boolean standalone,
                                      String themeApiName,
                                      String fieldId,
                                      String ruleId,
                                      String downstreamViewId) {
    String aggDimType = statFieldInfo.getString("agg_dim_type");
    String dbFieldName = statFieldInfo.getString("db_field_name");
    String type = statFieldInfo.getString("type");
    ObjectIdRule objectIdRule = ObjectIdRule.builder()
                                            .describeApiName(aggApiName)
                                            .column("object_id")
                                            .isSingle(false)
                                            .isUnique(true)
                                            .build();
    TimeRule timeRule = TimeRule.builder()
                                .column("action_date")
                                .columnType(PGColumnType.String)
                                .fieldType(FieldType.TEXT)
                                .isSingle(true)
                                .build();
    ValueRule valueRule = ValueRule.builder()
                                   .column(dbFieldName)
                                   .columnType(PGColumnType.parseStatFieldType(type))
                                   .isSingle(false)
                                   .isUnique(true)
                                   .aggType(AggType.parseFromDbFieldType(type))
                                   .build();
    WhereRules whereRules = null;
    if (!GrayManager.isAllowByRule("biDataSyncPolicyGray", tenantId)) {
      WhereRule whereRule = WhereRule.builder()
                                     .column("view_id")
                                     .columnType(PGColumnType.String)
                                     .fieldType(FieldType.TEXT)
                                     .isSingle(false)
                                     .filterType(FilterType.IS)
                                     .value1(downstreamViewId)
                                     .build();
      whereRules = WhereRules.builder()
                             .whereRulesList(Lists.newArrayList(Collections.singleton(Lists.newArrayList(whereRule))))
                             .build();
    }
    return new AggRule(tenantId, standalone, fieldId, ruleId, themeApiName, false, aggDimType, aggApiName, null, null,
      objectIdRule, valueRule, timeRule, whereRules, null, mappingService);
  }

  /**
   * 检测是否需要采用 inner join
   *
   * @param statRule 规则
   * @return Boolean
   */
  public void useInnerJoin(TopologyTableAggRule statRule, List<String> usedTables) {
    Map<String, List<String>> filters = statRule.allFilterTableAlias();
    if (!filters.isEmpty()) {
      /*
       * 1、判断标准，筛选器对象不在已使用对象列表，如果在的话join条件已经固定延用即可。
       * 2、并且不包含为null的条件，如果包含的话。沿用已有的join条件即可，对象数量大于1则需要用left join
       * 3、如果筛选条件中用到了OR 条件并且OR 条件中只用到了右表。
       */
      Set<String> asOnlyFilterTables = filters.entrySet()
                                              .stream()
                                              .filter(filter ->
                                                !CollectionHelper.hasIntersection(filter.getValue(),
                                                  Constants.filterWithNull) &&
                                                  !usedTables.contains(filter.getKey()))
                                              .map(Map.Entry::getKey)
                                              .collect(Collectors.toSet());
      if (CollectionUtils.isNotEmpty(asOnlyFilterTables) && asOnlyFilterTables.size() == 1 && !statRule.haveOR()) {
        statRule.changeJoinType(asOnlyFilterTables, JoinType.INNER_JOIN);
      }
    }
  }

  /**
   * 检测是否有多值类型做维度
   *
   * @param statViewDimBO
   * @return
   */
  public boolean haveMultipleValue(StatViewDimBO statViewDimBO) {
    if (statViewDimBO == null) {
      return false;
    }
    return isMultipleValue(statViewDimBO.getThemeDimRules()) || isMultipleValue(statViewDimBO.getMasterDimRules());
  }

  /**
   * 校验是否需要去重列，以及指标计算是否由count 改为 uniqState
   *
   * @param tenantId       租户id
   * @param standalone     是否schema隔离
   * @param valueRule      agg规则
   * @param statRule       拓扑图对象
   * @param aliasMapper    dim别名计算cache
   * @param aggAliasMapper agg别名计算cache
   * @param valueNodeTable 真正的指标对象
   */
  public void checkUniqField(String tenantId,
                             boolean standalone,
                             ValueRule valueRule,
                             TopologyTableAggRule statRule,
                             Map<String, AtomicInteger> aliasMapper,
                             Map<String, AtomicInteger> aggAliasMapper,
                             Map<String, AtomicInteger> dimsMapper,
                             Map<String, String/*dim字段别名*/> dimFieldAliasMapper,
                             StatViewDimBO statViewDimBO,
                             NodeTable valueNodeTable) {
    //去掉灰度后的写法
//    if (statRule.hasDataExplosive(standalone, statRule.getRootNodeTable()) || (valueRule.joinRelation != null && !Constants.QUOTE.equals(valueRule.joinRelation.fieldType)))
    boolean isValueUniq = valueRule.joinRelation != null && (!GrayManager.isAllowByRule("check_uniq_field_quote_eis", tenantId) ||
      (GrayManager.isAllowByRule("check_uniq_field_quote_eis", tenantId) && !Constants.QUOTE.equals(valueRule.joinRelation.fieldType)));
    if (statRule.hasDataExplosive(standalone, statRule.getRootNodeTable()) || isValueUniq) {
      switch (Objects.requireNonNull(valueRule).getAggType()) {
        case count, countdistinct -> {
          //需要改成uniqStat
          valueRule.setAggType(AggType.countuniq);
          String aggColumnName = valueRule.createAggColumnName(aggAliasMapper);
          statRule.getFieldLocationMap().put(statRule.getFieldId(), aggColumnName);
          String aggConfigString = valueRule.createAggConfigString() + ":" + aggColumnName;
          statRule.getAggConfigStringList().remove(0);
          statRule.getAggConfigStringList().add(aggConfigString);
        }
        case sum -> {
          //需要加上去重列
          DimRule keyDimRule = this.createKeyDimRule(tenantId, standalone, valueNodeTable.objectDescribeApiName(),
            null);
          this.joinQuoteNodeTable(tenantId, standalone, valueNodeTable, aliasMapper, keyDimRule.getDimFieldRule(),
            true, false);
          DimConfig dimConfig = keyDimRule.createDimConfig(null, dimFieldAliasMapper, dimsMapper);
          //如果不在共享维度列中需要单独占用一列 agg_uniq_tag,其他指标也要占用这一列只不过是空串。
          if (!statRule.getDimConfigStringList().contains(dimConfig.toConfigString())) {
            dimConfig.setDstColumnName(Constants.AGG_UNIQ_TAG_1);
          }
          Map<String, String> uniqDimConfigMapTmp = Maps.newHashMap();
          uniqDimConfigMapTmp.put(statRule.getFieldId(), dimConfig.toConfigString());
          statRule.setUniqDimConfigMap(uniqDimConfigMapTmp);
          //修改aggType 由sum 改为sum2
          String aggConfigStr = statRule.getAggConfigStringList().remove(0);
          aggConfigStr=aggConfigStr.replace(":sum:",":sum2:");
          statRule.getAggConfigStringList().add(aggConfigStr);
        }
      }
    }
  }

  /**
   * @param tenantId
   * @param standalone
   * @param aggNodeTable
   * @param aliasMapper
   * @param whereRules
   * @param timeZone
   * @return
   */
  public List<List<WhereConfig>> dealWithWhereConfig(String tenantId,
                                                     boolean standalone,
                                                     NodeTable aggNodeTable,
                                                     Map<String, AtomicInteger> aliasMapper,
                                                     WhereRules whereRules,
                                                     String timeZone) {
    List<List<WhereConfig>> whereConfigList = Lists.newArrayList();
    if (whereRules != null) {
      ArrayList<ArrayList<WhereRule>> whereList = whereRules.getWhereRulesList();
      for (ArrayList<WhereRule> rules : whereList) {
        List<WhereConfig> whereConfigs = Lists.newArrayList();
        rules.forEach(wr -> {
          NodeTable whereNodeTable = this.joinQuoteNodeTable(tenantId, standalone, aggNodeTable, aliasMapper, wr,
            false, false);
          WhereConfig whereConfig = new WhereConfig();
          whereConfig.setObjectApiName(whereNodeTable.getObjectDescribeApiName());
          whereConfig.setFilterType(wr.getFilterType().getId());
          whereConfig.setTableName(wr.tableName);
          whereConfig.setTableAlias(wr.alias);
          whereConfig.setWhereExpr(wr.whereSQL(whereNodeTable, wr.tableName, wr.alias, timeZone));
          whereConfigs.add(whereConfig);
        });
        whereConfigList.add(whereConfigs);
      }
    }
    return whereConfigList;
  }

  /**
   * 通用在关系树中添加维度字段的方法
   *
   * @param tenantId            租户id
   * @param standalone          是否是schema隔离企业
   * @param dimRules            维度规则
   * @param themeNodeTable      需要添加的table对象
   * @param aliasMapper         表别名缓存
   * @param dimFieldAliasMapper 维度字段名称和槽位映射规则
   * @param dimsMapper          维度字段别名缓存
   * @param dimConfigStringList 用于存储dim字段到agg_data 字段的清洗规则
   * @param fieldLocationMap
   */
  public void buildDims(String tenantId,
                        boolean standalone,
                        List<DimRule> dimRules,
                        NodeTable themeNodeTable,
                        Map<String, AtomicInteger> aliasMapper,
                        Map<String, String/*dim字段别名*/> dimFieldAliasMapper,
                        Map<String, AtomicInteger> dimsMapper,
                        List<String> dimConfigStringList,
                        Map<String, String> fieldLocationMap) {
    dimRules.forEach(dimRule -> {
      DimFieldRule dimFieldRule = dimRule.getDimFieldRule();
      NodeTable quoteNodeTable = this.joinQuoteNodeTable(tenantId, standalone, themeNodeTable, aliasMapper,
        dimFieldRule, true, false);
      DimConfig dimConfig = dimRule.createDimConfig(chAggDataFixSlot, dimFieldAliasMapper, dimsMapper);
      //由于有补充维度+主从对象作为维度得情况导致维度重复需要去重
      if (!dimConfigStringList.contains(dimConfig.toConfigString())) {
        dimConfigStringList.add(dimConfig.toConfigString());
      }
      fieldLocationMap.put(dimFieldRule.fieldId, dimConfig.getDstColumnName());
      if (dimFieldRule.enableMultiLang && GrayManager.isAllowByRule("stat_view_enable_multi_lang_eis", tenantId)) {
        this.joinLangNodeTable(tenantId, quoteNodeTable, aliasMapper, dimFieldRule, standalone);
        if (!dimConfigStringList.contains(dimConfig.toLangConfigString())) {
          dimConfigStringList.add(dimConfig.toLangConfigString());
        }
        if (!dimConfigStringList.contains(dimConfig.toLangColumnConfigString())) {
          dimConfigStringList.add(dimConfig.toLangColumnConfigString());
          fieldLocationMap.put(Constants.LANG, Constants.LANG);
        }
      }
    });
  }

  /**
   * 通用在关系树中添加自定义维度字段的方法
   *
   * @param tenantId            租户id
   * @param standalone          是否是schema隔离企业
   * @param dimRules            维度规则
   * @param themeNodeTable      需要添加的table对象
   * @param aliasMapper         表别名缓存
   * @param dimFieldAliasMapper 维度字段名称和槽位映射规则
   * @param dimsMapper          维度字段别名缓存
   * @param dimConfigStringList 用于存储dim字段到agg_data 字段的清洗规则
   * @param fieldLocationMap
   */
  public List<CustomDimField> buildCustomDims(String tenantId,
                                              boolean standalone,
                                              List<DimRule> dimRules,
                                              NodeTable themeNodeTable,
                                              Map<String, AtomicInteger> aliasMapper,
                                              Map<String, String/*dim字段别名*/> dimFieldAliasMapper,
                                              Map<String, AtomicInteger> dimsMapper,
                                              List<String> dimConfigStringList,
                                              Map<String, String> fieldLocationMap) {
    List<CustomDimField> customDimFields = new ArrayList<>();
    for (DimRule dimRule : dimRules) {
      DimFieldRule dimFieldRule = dimRule.getDimFieldRule();
      String dimensionId = dimFieldRule.dimensionId;
      this.joinQuoteNodeTable(tenantId, standalone, themeNodeTable, aliasMapper, dimFieldRule, true, false);
      DimConfig dimConfig = dimRule.createDimConfig(chAggDataFixSlot, dimFieldAliasMapper, dimsMapper);
      //由于有补充维度+主从对象作为维度得情况导致维度重复需要去重
      if (!dimConfigStringList.contains(dimConfig.toConfigString())) {
        dimConfigStringList.add(dimConfig.toConfigString());
      }
      fieldLocationMap.put(dimensionId, dimConfig.getDstColumnName());
      CustomDimField customDimField = CustomDimField.builder()
                                                    .fieldId(dimFieldRule.fieldId)
                                                    .dimensionId(dimensionId)
                                                    .dimensionConfigString(dimConfig.toConfigString())
                                                    .dimFormula(dimFieldRule.dimensionConfig)
                                                    .type(dimFieldRule.customType)
                                                    .isSingle(dimFieldRule.isSingle)
                                                    .fieldType(dimFieldRule.fieldType)
                                                    .pgColumnType(dimFieldRule.columnType)
                                                    .build();
      customDimFields.add(customDimField);
    }
    return customDimFields;
  }

  /**
   * 获取object id table 对象
   *
   * @param tenantId
   * @param standalone   是否是schema隔离
   * @param aggNodeTable
   * @param aliasMapper
   * @param quoteOfAgg
   */
  public NodeTable joinObjectIdQuoteNodeTableAndGet(String tenantId,
                                                    boolean standalone,
                                                    NodeTable aggNodeTable,
                                                    Map<String, AtomicInteger> aliasMapper,
                                                    String themeApiName,
                                                    String checkApiNameLookupField,
                                                    QuoteOfAgg<AggRule> quoteOfAgg,
                                                    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    String aggApiName = aggNodeTable.getObjectDescribeApiName();
    String objectIdApiName;
    NodeTable objectIdNodeTable;
    String lookupFieldName;
    JoinRelation joinRelation = quoteOfAgg.joinRelation;
    if (joinRelation == null) {
      //如果 agg data直接反查主题表
      objectIdApiName = aggApiName;
      lookupFieldName = checkApiNameLookupField;
      String aggObjectIdColumn = quoteOfAgg.column;
      //判断是否是扩展字段
      if (!standalone && !aggApiName.endsWith("__c") && Constants.slotRegex.matcher(aggObjectIdColumn).matches()) {
        //扩展字段需要关联扩展对象
        NodeJoin sourceExtNodeJoin = this.createExtNodeTable(aggApiName);
        objectIdNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
        aggNodeTable.appendColumns("extend_obj_data_id","id");
      } else {
        objectIdNodeTable = aggNodeTable;
      }
    } else {
      //objectId字段是查找关联其他对象的字段、判断objectId字段是否是扩展对象上的字段
      String targetApiName = joinRelation.apiName;
      objectIdApiName = targetApiName;
      if (objectIdApiName.equals("feed_relation") && "target_data_id".equals(quoteOfAgg.column)) {
        lookupFieldName = "target_data_id";
      } else {
        lookupFieldName = checkApiNameLookupField;
        if (checkApiNameLookupField.contains(".")) {
          lookupFieldName = checkApiNameLookupField.substring(checkApiNameLookupField.indexOf(".") + 1);
        }
      }
      String targetPreTableName = Constants.table(targetApiName, null, standalone);
      if (!standalone && !targetApiName.endsWith("__c") && Constants.slotRegex.matcher(quoteOfAgg.column).matches()) {
        //引用字段是扩展对象上的字段,首先 agg表join预置对象表
        NodeTable quoteNodeTable = NodeTable.of(targetPreTableName, null, Lists.newArrayList(), Sets.newTreeSet(), targetApiName, joinRelation.column, false, null, null);
        //添加和ObjectId对象关系这儿采用inner join
        joinRelation.setJoinType(AggJoinType.INNER);
        //构建和被引用对象的join 关系
        //判断是否需要通过扩展字段查找关联被引用对象
        if (!aggApiName.endsWith("__c") && Constants.slotRegex.matcher(joinRelation.column).matches()) {
          //通过扩展字段查找关联引用对象。
          NodeJoin sourceExtNodeJoin = this.createExtNodeTable(aggApiName);
          NodeTable sourceExtTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
          aggNodeTable.appendColumns("extend_obj_data_id","id");
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, false, joinRelation, sourceExtTable, quoteNodeTable, mappingService);
          quoteNodeTable = sourceExtTable.addJoinSetSingle(aliasMapper, quoteTableJoin, true);
        } else {
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, false, joinRelation, aggNodeTable, quoteNodeTable, mappingService);
          quoteNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, true);
        }
        //join 扩展对象的扩展字段
        NodeJoin targetExtNodeJoin = this.createExtNodeTable(targetApiName);
        objectIdNodeTable = quoteNodeTable.addJoinSetSingle(aliasMapper, targetExtNodeJoin, true);
        quoteNodeTable.appendColumns("extend_obj_data_id","id");
      } else {
        //引用字段不是扩展对象上的字段
        NodeTable quoteNodeTable = NodeTable.of(targetPreTableName, null, Lists.newArrayList(), Sets.newTreeSet(), targetApiName, joinRelation.column, false, null, null);
        //添加和ObjectId对象关系这儿采用inner join
        joinRelation.setJoinType(AggJoinType.INNER);
        //构建和被引用对象的join 关系
        //判断是否需要通过扩展字段查找关联被引用对象
        if (!standalone && !aggApiName.endsWith("__c") && Constants.slotRegex.matcher(joinRelation.column).matches()) {
          //通过扩展字段查找关联引用对象。
          NodeJoin sourceExtNodeJoin = this.createExtNodeTable(aggApiName);
          NodeTable sourceExtNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, sourceExtNodeJoin, true);
          aggNodeTable.appendColumns("extend_obj_data_id","id");
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, false, joinRelation, sourceExtNodeTable, quoteNodeTable, mappingService);
          objectIdNodeTable = sourceExtNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, true);
        } else {
          NodeJoin quoteTableJoin = Constants.onJoinPlus(tenantId, standalone, joinRelation, aggNodeTable, quoteNodeTable, mappingService);
          objectIdNodeTable = aggNodeTable.addJoinSetSingle(aliasMapper, quoteTableJoin, true);
        }
      }
    }
    String id = "id";
    if (GrayManager.isAllowByRule("agg_rule_what_object_data_id_eis", tenantId) &&
      themeApiName.endsWith("__c") && !standalone) {
      id = "value0";
    }
    if (StringUtils.equalsAny(objectIdApiName, "feed_relation", "biz_behavior_record_relation",
        "biz_journal_feed_relation", "service_log_feed_relation", "callcenter_relation") &&
        "target_data_id".equals(lookupFieldName)) {
      JoinRelation objectIdThemeObjJoinRelation = new JoinRelation("target_data_id", null, themeApiName, AggJoinType.INNER, null);
      NodeTable themeNodeTable = NodeTable.of(Constants.table(themeApiName, null, standalone), null, Lists.newArrayList(), Sets.newTreeSet(), themeApiName, "target_data_id", false, null, null);
      NodeJoin themeTableJoin = Constants.onJoinPlus(tenantId, standalone, objectIdThemeObjJoinRelation, objectIdNodeTable, themeNodeTable, mappingService);
      return objectIdNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
    } else if (Objects.equals(objectIdApiName, "stage_runtime") && "object_id".equals(lookupFieldName)) {
      JoinRelation objectIdThemeObjJoinRelation = new JoinRelation("object_id", null, themeApiName, AggJoinType.INNER, null);
      NodeTable themeNodeTable = NodeTable.of(Constants.table(themeApiName, null, standalone), null, Lists.newArrayList(), Sets.newTreeSet(), themeApiName, "object_id", false, null, null);
      NodeJoin themeTableJoin = Constants.onJoinPlus(tenantId, standalone, objectIdThemeObjJoinRelation, objectIdNodeTable, themeNodeTable, mappingService);
      objectIdNodeTable.appendSubWheres(String.format(" AND entity_id = '%s'", mappingService.paasApiName(themeApiName)));
      return objectIdNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
    }else if (Objects.equals(objectIdApiName,  "agg_downstream_data") && "object_id".equals(lookupFieldName)) {
      JoinRelation objectIdThemeObjJoinRelation = new JoinRelation("object_id", null, themeApiName, AggJoinType.INNER, null);
      NodeTable themeNodeTable = NodeTable.of(Constants.table(themeApiName, null, standalone), null, Lists.newArrayList(), Sets.newTreeSet(), themeApiName, "object_id", false, null, null);
      NodeJoin themeTableJoin = Constants.onJoinPlus(tenantId, standalone, objectIdThemeObjJoinRelation, objectIdNodeTable, themeNodeTable, mappingService);
      return objectIdNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
    } else if (StringUtils.equalsAny(objectIdApiName, "bpm_task", "bpm_instance") && "objectDataId".equals(lookupFieldName)) {
      JoinRelation objectIdThemeObjJoinRelation = new JoinRelation("objectDataId", null, themeApiName, AggJoinType.INNER, null);
      NodeTable themeNodeTable = NodeTable.of(Constants.table(themeApiName, null, standalone), null, Lists.newArrayList(), Sets.newTreeSet(), themeApiName, "objectDataId", false, null, null);
      NodeJoin themeTableJoin = Constants.onJoinPlus(tenantId, standalone, objectIdThemeObjJoinRelation, objectIdNodeTable, themeNodeTable, mappingService);
      return objectIdNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
    } else if (objectIdApiName.equals("base_crmfeedrelation") && "data_id".equals(lookupFieldName)) {
      JoinRelation objectIdThemeObjJoinRelation = new JoinRelation("data_id", null, themeApiName, AggJoinType.INNER, null);
      NodeTable themeNodeTable = NodeTable.of(Constants.table(themeApiName, null, standalone), null, Lists.newArrayList(), Sets.newTreeSet(), themeApiName, "data_id", false, null, null);
      NodeJoin themeTableJoin = Constants.onJoinPlus(tenantId, standalone, objectIdThemeObjJoinRelation, objectIdNodeTable, themeNodeTable, mappingService);
      return objectIdNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
    } else if (objectIdApiName.equals("new_opportunity") && "object_id".equals(lookupFieldName) &&
      "stage_runtime_new_opportunity".equals(themeApiName)) {
      NodeTable themeNodeTable = NodeTable.of(Constants.table(themeApiName, null, standalone), null, Lists.newArrayList(), Sets.newTreeSet(), themeApiName, "id", false, null, null);
      Set<String> onFilter = Sets.newHashSet(Constants.EI_ON_CONDITIONS, Constants.RIGHT_ON_DELETED_0);
      onFilter.add("${lt}.id = ${rt}.object_id");
      onFilter.add("${rt}.entity_id = 'NewOpportunityObj'");
      onFilter.add("${rt}.instance_status = ''");
      NodeOnCondition nodeOnCondition = new NodeOnCondition(onFilter.toArray(new String[0]), null, null);
      NodeJoin themeTableJoin = NodeJoin.of(JoinType.INNER_JOIN, themeNodeTable, nodeOnCondition);
      objectIdNodeTable.appendColumns("id");
      themeNodeTable.appendColumns("object_id", "entity_id", "instance_status","is_deleted");
      return objectIdNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
    } else if (objectIdApiName.equals("flow_task_handle_time_detail") && StringUtils.equalsAny(lookupFieldName,"task_id", "relatedObject")) {
      NodeTable themeNodeTable = NodeTable.of(Constants.table(themeApiName, null, standalone), null, Lists.newArrayList(), Sets.newTreeSet(), themeApiName, "task_id", false, null, null);
      Set<String> onFilter = Sets.newHashSet(Constants.EI_ON_CONDITIONS, Constants.RIGHT_ON_DELETED_0);
      onFilter.add("${lt}.task_id = ${rt}." + id);
      objectIdNodeTable.appendSubWheres(String.format(" AND task_api_name = '%s'",mappingService.paasApiName(themeApiName)));
      NodeOnCondition nodeOnCondition = new NodeOnCondition(onFilter.toArray(new String[0]), null, null);
      NodeJoin themeTableJoin = NodeJoin.of(JoinType.INNER_JOIN, themeNodeTable, nodeOnCondition);
      objectIdNodeTable.appendColumns("id", "task_id","task_api_name");
      themeNodeTable.appendColumns(id,"is_deleted");
      return objectIdNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
    } else if (StringUtils.equalsAny(objectIdApiName, "approval_task", "approval_instance") &&
      StringUtils.equalsAny(lookupFieldName, "object_data_id", "relatedObject")) {
      NodeTable themeNodeTable = NodeTable.of(Constants.table(themeApiName, null, standalone), null, Lists.newArrayList(), Sets.newTreeSet(), themeApiName, "object_data_id", false, null, null);
      Set<String> onFilter = Sets.newHashSet(Constants.EI_ON_CONDITIONS, Constants.RIGHT_ON_DELETED_0);
      onFilter.add("${lt}.object_data_id = ${rt}." + id);
      objectIdNodeTable.appendSubWheres(String.format(" AND object_api_name = '%s'", mappingService.paasApiName(themeApiName)));
      NodeOnCondition nodeOnCondition = new NodeOnCondition(onFilter.toArray(new String[0]), null, null);
      NodeJoin themeTableJoin = NodeJoin.of(JoinType.INNER_JOIN, themeNodeTable, nodeOnCondition);
      objectIdNodeTable.appendColumns("id", "object_data_id", "object_api_name");
      themeNodeTable.appendColumns(id, "is_deleted");
      return objectIdNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
    } else {
      UdfObjFieldDO lookupField = udfObjFieldMapper.setTenantId(tenantId)
                                                   .findUdfObjFieldDOByFieldName(tenantId, objectIdApiName, mappingService.getApiXName(objectIdApiName), lookupFieldName);
      if (lookupField == null) {
        throw new ParseRuleException(String.format("findFieldInfoByFieldName error tenantId:%s,apiName:%s," +
          "dbFieldName:%s,aggNodeTable:%s,themeApiName:%s,quoteOfAgg:%s", tenantId, objectIdApiName, lookupFieldName,JSON.toJSONString(aggNodeTable),themeApiName,JSON.toJSONString(quoteOfAgg)));
      }
      String fieldType = lookupField.getType();
      if (Constants.BI_LOOKUP_TYPE.contains(fieldType)) {
        //如果考核对象是人员，并且主题对象也是人员，并且查找关联关系用人员名称则不需要用user_id 再join一次人员主题
        if (Objects.equals(Constants.employee_api_name, objectIdApiName) &&
          Objects.equals("user_id", lookupFieldName) && Objects.equals(Constants.employee_api_name, themeApiName)) {
          return objectIdNodeTable;
        }
        NodeTable themeNodeTable = NodeTable.of(Constants.table(themeApiName, null, standalone), null, Lists.newArrayList(), Sets.newTreeSet(), themeApiName, quoteOfAgg.column, false, null, null);
        JoinRelation objectIdThemeObjJoinRelation = this.createRelation(tenantId, standalone, objectIdApiName, lookupField, themeApiName, cachedTableDefinitions, AggJoinType.INNER);
        NodeJoin themeTableJoin = Constants.onJoinPlus(tenantId, standalone, objectIdThemeObjJoinRelation, objectIdNodeTable, themeNodeTable, mappingService);
        return objectIdNodeTable.addJoinSetSingle(aliasMapper, themeTableJoin, true);
      } else {
        if (themeApiName.equals(objectIdApiName)) {
          return objectIdNodeTable;
        } else {
          throw new ParseRuleException(String.format("can not find theme node table tenantId:%s,aggApiName:%s,themeApiName:%s",tenantId,aggApiName, themeApiName));
        }
      }
    }
  }

  /**
   * 加载object_id规则
   *
   * @param tenantId
   * @param aggApiName
   * @param ruleInfo
   * @return
   */
  public ObjectIdRule buildObjectIdRule(String tenantId,
                                        boolean standalone,
                                        String themeApiName,
                                        String aggApiName,
                                        JSONObject ruleInfo,
                                        Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    String referenceFieldName = ruleInfo.getString("md_field_name");
    String objectIdColumnName = ruleInfo.getString("check_apiname_lookup_field_location");
    String objectIdFieldName = ruleInfo.getString("check_apiname_lookup_field");
    PGColumnType columnType;
    String refKeyField = null;
    String fieldType = null;
    JoinRelation objectJoinRelation = null;
    boolean isSingle = true;
    AggJoinType joinType = AggJoinType.LEFT;
    //特殊逻辑处理阶段推进器，what类型
    if (themeApiName.equals("stage_runtime_new_opportunity") && aggApiName.equals("new_opportunity")) {
      if (objectIdFieldName.equals("object_id")) {
        objectIdFieldName = "id";
        objectIdColumnName = "id";
        joinType = AggJoinType.RIGHT;
      }
    }
    //引用 存在md关系
    if (StringUtils.isNotBlank(referenceFieldName)) {
      String objectIdApiNameAndTargetFieldName = objectIdFieldName;
      int splitIdx = objectIdApiNameAndTargetFieldName.indexOf('.');
      String objectIdApiName;
      if (splitIdx > 0) {
        objectIdApiName = objectIdApiNameAndTargetFieldName.substring(0, splitIdx);
        String targetFieldName = objectIdApiNameAndTargetFieldName.substring(splitIdx + 1);
        Map<String, Object> targetField = this.findFieldInfoByFieldName(tenantId, objectIdApiName, targetFieldName);
        if (targetField != null) {
          JSONObject targetFieldInfo = new JSONObject(targetField);
          fieldType = String.valueOf(targetField.get("type"));
          if (null != targetField.get("ref_key_field")) {
            refKeyField = String.valueOf(targetField.get("ref_key_field"));
          }
          Integer fieldLocation = targetFieldInfo.getInteger("field_location");
          if (null != fieldLocation && fieldLocation > 0) {
            objectIdColumnName = "value" + fieldLocation;
          }
          isSingle = isSingleValue(fieldType, objectIdColumnName, targetFieldInfo.getInteger("is_single"));
        }
      } else {
        return null;
      }
      //通过扩展字段做的关联，需要解析
      if (referenceFieldName.endsWith("__c")) {
        Map<String, Object> objectIdJoinField = this.findFieldInfoByFieldName(tenantId, aggApiName, referenceFieldName);
        if (null == objectIdJoinField) {
          return null;
        }
        JSONObject objectIdJoinFieldInfo = new JSONObject(objectIdJoinField);
        Integer fieldLocation = objectIdJoinFieldInfo.getInteger("field_location");
        if (null != fieldLocation && fieldLocation > 0) {
          referenceFieldName = "value" + fieldLocation;
        }
      }
      objectJoinRelation = JoinRelation.builder()
                                       .apiName(objectIdApiName)
                                       .column(referenceFieldName)
                                       .joinType(joinType)
                                       .build();
    } else {
      String objectIdApiName = aggApiName;
      Map<String, Object> objectIdField = this.findFieldInfoByFieldName(tenantId, objectIdApiName, objectIdFieldName);
      if (objectIdField == null) {
        return null;
      }
      JSONObject objectIdFieldInfo = new JSONObject(objectIdField);
      fieldType = objectIdFieldInfo.getString("type");
      Integer objectIdFieldLocation = objectIdFieldInfo.getInteger("field_location");
      if (objectIdFieldLocation != null && objectIdFieldLocation > 0) {
        objectIdColumnName = "value" + objectIdFieldLocation;
      }
      isSingle = isSingleValue(fieldType, objectIdColumnName, objectIdFieldInfo.getInteger("is_single"));
      //员工部门有的预置的描述不对
      if (Constants.pre_employee_cols.contains(objectIdColumnName)) {
        fieldType = FieldType.EMPLOYEE;
      }
      if (Constants.pre_department_cols.contains(objectIdColumnName)) {
        fieldType = FieldType.DEPARTMENT;
      }
      //特殊处理员工部门的引用
      if (!GrayManager.isAllowByRule("skip_ref_employee",tenantId) && FieldType.OBJECT_REFERENCE.equals(fieldType)) {
        String targetApiName = objectIdFieldInfo.getString("ref_obj_name");
        if ("org_employee_user".equals(targetApiName)) {
          if ("user_id".equals(refKeyField)) {
            fieldType = FieldType.EMPLOYEE;
          } else {
            fieldType = FieldType.OBJECT_REFERENCE;
          }
          objectJoinRelation = JoinRelation.builder()
                                           .column(objectIdColumnName)
                                           .fieldType(fieldType)
                                           .apiName("org_employee_user")
                                           .joinType(joinType)
                                           .build();
          objectIdColumnName = "user_id";
          columnType = PGColumnType.String;
          fieldType = FieldType.TEXT;
        }
        if ("org_dept".equals(targetApiName)) {
          objectJoinRelation = JoinRelation.builder()
                                           .column(objectIdColumnName)
                                           .fieldType(FieldType.OBJECT_REFERENCE)
                                           .apiName("org_dept")
                                           .joinType(joinType)
                                           .build();
          objectIdColumnName = "dept_id";
          columnType = PGColumnType.String;
          fieldType = FieldType.TEXT;
        }
      }
    }
    //todo 需要通用适配
    if ("active_record".equals(aggApiName) && "related_object".equals(objectIdColumnName)) {
      objectIdColumnName = "target_data_id";
      objectJoinRelation = JoinRelation.builder().apiName("feed_relation").column("id").joinType(joinType).build();
    }
    if ("biz_journal".equals(aggApiName) && "related_object".equals(objectIdColumnName)) {
      objectIdColumnName = "target_data_id";
      objectJoinRelation = JoinRelation.builder()
                                       .apiName("biz_journal_feed_relation")
                                       .column("id")
                                       .joinType(joinType)
                                       .build();
    }
    String table = objectJoinRelation == null ? aggApiName : objectJoinRelation.apiName;
    if (table.endsWith("__c") && !standalone) {
      table = "object_data";
    }
    Map<String, ColumnDefinition> tableColumnDefinitions = this.findTableColumnDefinitions(tenantId, standalone,
      table, cachedTableDefinitions);

    if (CollectionHelper.isEmpty(tableColumnDefinitions)) {
      log.warn("table not found {}:{}", tenantId, table);
      return null;
    }
    ColumnDefinition definition = tableColumnDefinitions.get(objectIdColumnName);
    if (definition == null) {
      columnType = PGColumnType.String;
    } else {
      columnType = definition.columnType();
    }

    return ObjectIdRule.builder()
                       .joinRelation(objectJoinRelation)
                       .column(objectIdColumnName)
                       .columnType(columnType)
                       .fieldType(fieldType)
                       .isSingle(isSingle)
                       .build();
  }

  /**
   * 加载 time 规则
   * 日期对象的查找关联关系采用inner join
   *
   * @param tenantId
   * @param standalone
   * @param aggApiName
   * @param ruleInfo
   * @return
   */
  private TimeRule buildTimeRule(String tenantId,
                                 boolean standalone,
                                 String themeApiName,
                                 String aggApiName,
                                 JSONObject ruleInfo,
                                 Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    JoinRelation timeJoinRelation = null;
    String refColumn = ruleInfo.getString("check_count_time_lookup_field_location");
    String countTimeLookupField = ruleInfo.getString("count_time_lookup_field_name");
    String timeColumnType = ruleInfo.getString("count_time_api_name_type");
    PGColumnType columnType = PGColumnType.Int8;
    String timeColumn = ruleInfo.getString("count_time_api_name_field_location");
    if (StringUtils.isNotBlank(refColumn)) {
      String timeApiName = ruleInfo.getString("count_time_api_name_field");
      int splitIdx = timeApiName.indexOf('.');
      if (splitIdx > 0) {
        String timeField = timeApiName.substring(splitIdx + 1);
        timeApiName = timeApiName.substring(0, splitIdx);
        UdfObjFieldDO refField = this.findUdfObjFieldDOByFieldName(tenantId, aggApiName, countTimeLookupField);
        if (null == refField) {
          log.error("findUdfObjFieldDOByFieldName empty tenantId:{},aggApiName:{},countTimeLookupField:{}", tenantId, aggApiName, countTimeLookupField);
          return null;
        }
        QuoteTargetField quoteTargetField = this.buildQuteTargetByUdfObjField(tenantId, standalone, aggApiName,
          refField, cachedTableDefinitions);
        refColumn = quoteTargetField.column;
        //解析日期字段
        UdfObjFieldDO timeFieldDO = this.findUdfObjFieldDOByFieldName(tenantId, timeApiName, timeField);
        if (null == timeFieldDO) {
          log.error("findUdfObjFieldDOByFieldName empty tenantId:{},timeApiName:{},timeField:{}", tenantId, timeApiName, timeField);
          return null;
        }
        QuoteTargetField timeQuoteTargetField = this.buildQuteTargetByUdfObjField(tenantId, standalone, timeApiName,
          timeFieldDO, cachedTableDefinitions);
        if (StringUtils.equalsAny(timeQuoteTargetField.fieldType, "quote")) {
          if (GrayManager.isAllowByRule("quote_field_location", tenantId) &&
            Constants.slotRegex.matcher(timeQuoteTargetField.column).matches()) {
            timeColumn = timeQuoteTargetField.column;
            columnType = timeQuoteTargetField.columnType;
          } else {
            log.error("action date field can not support quote type tenantId:{},timeApiName:{},timeField:{}", tenantId, timeApiName, timeField);
            return null;
          }
        } else {
          timeColumn = timeQuoteTargetField.column;
          columnType = timeQuoteTargetField.columnType;
        }
      } else {
        UdfObjFieldDO refField = this.findUdfObjFieldDOByFieldName(tenantId, aggApiName, refColumn);
        if (null == refField) {
          log.error("findUdfObjFieldDOByFieldName empty tenantId:{},aggApiName:{},countTimeLookupField:{}", tenantId, aggApiName, refColumn);
          return null;
        }
        timeApiName = refField.getRefObjName();
        if (timeApiName == null) {
          return null;
        }
        Map<String, Object> timeField = this.findFieldInfoByFieldName(tenantId, timeApiName, ruleInfo.getString("count_time_api_name_field"));
        if (timeField == null) {
          return null;
        }
      }
      if ("create_time".equals(timeApiName)) {
        return null;
      }
      //特殊处理一下what类型
      if (StringUtils.equalsAny(aggApiName, "approval_task", "approval_instance") &&
        "relatedObject".equals(refColumn)) {
        refColumn = "object_data_id";
      }
      timeJoinRelation = new JoinRelation(refColumn, null, timeApiName, AggJoinType.LEFT, null);
    } else {
      String timeFieldInDefine = ruleInfo.getString("count_time_api_name_field");
      if (timeFieldInDefine == null) {
        if (Utils.isSlotColumn(timeColumn)) {
          return TimeRule.builder()
                         .column(timeColumn)
                         .columnType(PGColumnType.String)
                         .fieldType(FieldType.DATE)
                         .isSingle(true)
                         .build();
        }
      }
      //就是这么怪异， 两个时间列，优先用field
      if (timeFieldInDefine != null) {
        timeColumn = timeFieldInDefine;
      }
      Map<String, Object> timeField = this.findFieldInfoByFieldName(tenantId, aggApiName, timeColumn);
      if (timeField == null) {
        return null;
      }
      JSONObject timeFieldInfo = new JSONObject(timeField);
      String timeJoinColumn = timeFieldInfo.getString("relation_key_field");
      if (timeJoinColumn != null) {
        String timeApiName = timeFieldInfo.getString("ref_obj_name");
        if (timeApiName == null) {
          return null;
        }
        timeApiName = mappingService.udfApiName2ApiName(timeApiName);
        timeJoinRelation = new JoinRelation(timeJoinColumn, null, timeApiName, AggJoinType.LEFT, null);
        String refTargetField = timeFieldInfo.getString("ref_target_field");
        timeColumn = refTargetField;
        //被引用字段是自定义字段
        if (refTargetField.endsWith("__c")) {
          timeField = this.findFieldInfoByFieldName(tenantId, timeApiName, refTargetField);
          if (null == timeField) {
            return null;
          }
          timeFieldInfo = new JSONObject(timeField);
          Integer fieldLocation = timeFieldInfo.getInteger("field_location");
          timeColumnType = timeFieldInfo.getString("type");
          if (null != fieldLocation && fieldLocation > 0) {
            timeColumn = "value" + fieldLocation;
          }
          String table = timeApiName;
          if (table.endsWith("__c") && !standalone) {
            table = "object_data";
          }
          columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions, timeColumn,
            timeColumnType);
        }
      } else {
        timeColumnType = timeFieldInfo.getString("type");
        Integer fieldLocation = timeFieldInfo.getInteger("field_location");
        if (null != fieldLocation && fieldLocation > 0) {
          timeColumn = "value" + fieldLocation;
        }
        String table = aggApiName;
        if (table.endsWith("__c") && !standalone) {
          table = "object_data";
        }
        columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions, timeColumn,
          timeColumnType);
      }
    }
    if (!standalone && Utils.isSlotColumn(timeColumn)) {
      columnType = PGColumnType.String;
    }
    //ch中将timestamp 类型转成int64类型
    String timeApiName = timeJoinRelation == null ? aggApiName : timeJoinRelation.apiName;
    if (!standalone && timeApiName.endsWith("__c") && !Utils.isSlotColumn(timeColumn)) {
      columnType = PGColumnType.Int8;
    }
    //特殊类型的字段
    if (Objects.equals(timeApiName, "wechat_group") && Objects.equals(timeColumn, "chat_delete_time")) {
      columnType = PGColumnType.String;
    }
    if (timeJoinRelation != null) {
      if (aggApiName.equals("stage_runtime")) {
        if (timeJoinRelation.column.equals("relatedObject")) {
          timeJoinRelation.column = "object_id";
        }
      }
    }
    return TimeRule.builder()
                   .joinRelation(timeJoinRelation)
                   .column(timeColumn)
                   .columnType(columnType)
                   .fieldType(timeColumnType)
                   .isSingle(true)
                   .build();
  }

  /**
   * 加载value规则
   *
   * @param tenantId
   * @param aggApiName
   * @param ruleInfo
   * @return
   */
  private ValueRule buildValueRule(String tenantId,
                                   boolean standalone,
                                   String themeApiName,
                                   String aggApiName,
                                   JSONObject ruleInfo,
                                   Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    String valueFieldName = ruleInfo.getString("check_field_api_name");
    UdfObjFieldDO valueFieldUdf = this.findUdfObjFieldDOByFieldName(tenantId, aggApiName, valueFieldName);
    //字段已经被删除
    if (null == valueFieldUdf) {
      log.warn("can not findUdfObjFieldDOByFieldName udfObjField tenantId:{},valueFieldName:{},apiName:{}", tenantId,
        valueFieldName, aggApiName);
      return null;
    }
    int fieldLocation =Integer.parseInt(valueFieldUdf.getFieldLocation());// valueFieldInfo.getInteger("field_location");
    String fieldType = valueFieldUdf.getType();//valueFieldInfo.getString("type");
    Integer isSingle = valueFieldUdf.getIsSingle();//valueFieldInfo.getInteger("is_single");
    Boolean isUnique = valueFieldUdf.getIsUnique();//valueFieldInfo.getBoolean("is_unique");
    if (Utils.isExtend(aggApiName, valueFieldName) && fieldLocation > 0) {
      valueFieldName = "value" + fieldLocation;
    }
    if ("value0".equals(valueFieldName) && standalone && aggApiName.endsWith("__c")) {
      valueFieldName = "id";
    }
    String refColumn = valueFieldUdf.getRelationKeyField();//valueFieldInfo.getString("relation_key_field");
    int aggTypeIndex = ruleInfo.getInteger("check_field_aggregate_type");
    AggType aggType = AggType.parseFromIndex(aggTypeIndex);
    if (refColumn != null) {
      String targetFieldName = valueFieldUdf.getRefTargetField();//valueFieldInfo.getString("ref_target_field");
      String valueApiName = valueFieldUdf.getRefObjName();//valueFieldInfo.getString("ref_obj_name");
      if (valueApiName == null) {
        return null;
      }
      if (valueApiName.endsWith("_udef")) {
        valueApiName = ObjectConfigManager.getPreObjName(valueApiName);
      }
      String targetColumn = targetFieldName;
      Map<String, Object> targetField = this.findFieldInfoByFieldName(tenantId, valueApiName, targetFieldName);
      if (targetField == null) {
        return null;
      }
      JSONObject targetFieldInfo = new JSONObject(targetField);
      Integer targetFieldLocation = targetFieldInfo.getInteger("field_location");
      String targetFieldType = valueFieldUdf.getType();//valueFieldInfo.getString("type");
      isSingle = targetFieldInfo.getInteger("is_single");
      isUnique = targetFieldInfo.getBoolean("is_unique");
      if (Utils.isExtend(valueApiName, targetFieldName) && targetFieldLocation > 0) {
        targetColumn = "value" + targetFieldLocation;
      }
      String table = valueApiName;
      if (table.endsWith("__c") && !standalone) {
        table = "object_data";
      }
      PGColumnType columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions, targetColumn
        , targetFieldType);
      JoinRelation valueJoinRelation = new JoinRelation(refColumn, targetFieldType, valueApiName, AggJoinType.INNER,
        columnType);
      boolean iss = this.isSingleValue(targetFieldType, targetColumn, isSingle);
      //如果是计数字段并且不是唯一值的
      if (aggType == AggType.count && Objects.equals(isUnique,false)) {
        aggType = AggType.countuniq;
      }
      return ValueRule.builder()
                      .joinRelation(valueJoinRelation)
                      .column(targetColumn)
                      .columnType(columnType)
                      .isSingle(iss)
                      .isUnique(isUnique != null && isUnique)
                      .aggType(aggType)
                      .build();
    } else {
      String relationTable = valueFieldUdf.getRelationTable();//valueFieldInfo.getString("relation_table");
      if (StringUtils.isNotBlank(relationTable) && "related_object".equals(valueFieldName)) {
        JoinRelation valueJoinRelation = this.createRelation(tenantId, standalone, aggApiName, valueFieldUdf, null,
          cachedTableDefinitions, AggJoinType.INNER);
        return ValueRule.builder()
                        .joinRelation(valueJoinRelation)
                        .column("target_data_id")
                        .columnType(PGColumnType.String)
                        .isSingle(true)
                        .isUnique(false)
                        .aggType(aggType)
                        .build();
      }
      String table = aggApiName;
      if (table.endsWith("__c") && !standalone) {
        table = "object_data";
      }
      PGColumnType columnType = this.findColumnType(tenantId, standalone, table, cachedTableDefinitions,
        valueFieldName, fieldType);
      boolean iss = this.isSingleValue(fieldType, valueFieldName, isSingle);
      //如果是计数字段并且不是唯一值的
      if (aggType == AggType.count && Objects.equals(isUnique,false)) {
        aggType = AggType.countuniq;
      }
      return ValueRule.builder()
                      .column(valueFieldName)
                      .columnType(columnType)
                      .isSingle(iss)
                      .isUnique(isUnique != null && isUnique)
                      .aggType(aggType)
                      .build();
    }
  }

  /**
   * 构建WhereRules
   *
   * @param tenantId
   * @param ruleInfo
   * @return
   */
  public WhereRules buildWheres(String tenantId,
                                boolean standalone,
                                String themeApiName,
                                String aggApiName,
                                JSONObject ruleInfo,
                                Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    String wheresString = ruleInfo.getString("wheres");
    if (StringHelper.isNullOrBlank(wheresString)) {
      return null;
    }
    AtomicInteger counter = new AtomicInteger(0);
    ArrayList<ArrayList<WhereRule>> list = Lists.newArrayList();
    JSONArray wheresInfo = JSON.parseArray(wheresString);
    if (wheresInfo.isEmpty()) {
      return null;
    }
    for (Object whereListInfo : wheresInfo) {
      ArrayList<WhereRule> whereRuleList = buildWhereRuleList(tenantId, standalone, aggApiName,
        (JSONObject) whereListInfo, counter, cachedTableDefinitions);
      if (CollectionHelper.isNotEmpty(whereRuleList)) {
        list.add(whereRuleList);
      }
    }
    if (CollectionHelper.isEmpty(list)) {
      return null;
    }
    return WhereRules.builder().whereRulesList(list).build();
  }

  /**
   * 拼where list
   *
   * @param tenantId
   * @param aggApiName
   * @param whereListInfo
   * @param counter
   * @return
   */
  public ArrayList<WhereRule> buildWhereRuleList(String tenantId,
                                                 boolean standalone,
                                                 String aggApiName,
                                                 JSONObject whereListInfo,
                                                 AtomicInteger counter,
                                                 Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {
    ArrayList<WhereRule> result = Lists.newArrayList();
    JSONArray filters = whereListInfo.getJSONArray("filters");
    for (Object filter : filters) {
      WhereRule whereRule = buildWhereRule(tenantId, standalone, aggApiName, (JSONObject) filter, counter,
        cachedTableDefinitions);
      if (null == whereRule) {
        log.warn("where rule init failed. {}:{}", tenantId, filter);
        continue;
      }
      result.add(whereRule);
    }
    return result;
  }

  /**
   * 拼单条where规则
   *
   * @param tenantId
   * @param whereInfo
   * @param counter
   * @return
   */
  public WhereRule buildWhereRule(String tenantId,
                                  boolean standalone,
                                  String aggApiName,
                                  JSONObject whereInfo,
                                  AtomicInteger counter,
                                  Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions) {

    //被引用对象
    String targetApiName = whereInfo.getString("dbObjName");
    //字段名称
    String lookupFieldName = whereInfo.getString("dbFieldName");
    //引用关系字段JoinRelation#field
    String referenceFieldName = whereInfo.getString("parentDbFieldName");
    //where字段元数据类型
    String originalType = whereInfo.getString("originalType");
    if (null == originalType) {
      originalType = FieldType.TEXT;
    }
    int operator = whereInfo.getInteger("operator");
    QuoteOfAgg quoteOfAgg = null;
    //如果是筛选条件是属于不属于
    if (FieldType.IN_NOT_IN.contains(operator) && Objects.equals(lookupFieldName, this.getMainField(targetApiName))) {
      lookupFieldName = targetApiName.endsWith("__c") ? "value0" : Constants.getIdName(standalone, targetApiName);
    }
    //lookup字段（非quote类型的字段,根据引用关系页面指定的字段）
    if (referenceFieldName != null && !StringUtils.equalsAny(originalType, "employee", "department")) {
      quoteOfAgg = this.buildFromQuote(tenantId, standalone, aggApiName, targetApiName, referenceFieldName,
        lookupFieldName, cachedTableDefinitions);
    } else {
      UdfObjFieldDO lookupField = udfObjFieldMapper.setTenantId(tenantId)
                                                   .findUdfObjFieldDOByFieldName(tenantId, aggApiName,
                                                     mappingService.getApiXName(aggApiName), lookupFieldName);
      if (lookupField == null) {
        return null;
      }
      //lookup字段，quote类型的
      if ("quote".equals(lookupField.getType())) {
        targetApiName = lookupField.getRefObjName();
        targetApiName = mappingService.udfApiName2ApiName(targetApiName);
        referenceFieldName = lookupField.getRelationKeyField();
        lookupFieldName = lookupField.getRefTargetField();
        quoteOfAgg = this.buildFromQuote(tenantId, standalone, aggApiName, targetApiName, referenceFieldName,
          lookupFieldName, cachedTableDefinitions);
      } else if (FieldType.GROUP.equals(lookupField.getType())) {
        //针对 active_record related_obj where条件
        JoinRelation relation = null;
        String relationTable = lookupField.getRelationTable();
        if (StringUtils.isNotBlank(relationTable)) {
          relation = JoinRelation.builder()
                                 .column(Constants.getIdName(standalone, aggApiName))
                                 .apiName(relationTable)
                                 .joinType(AggJoinType.LEFT)
                                 .fieldType(FieldType.GROUP)
                                 .columnType(PGColumnType.String)
                                 .build();
        }
        if (relation != null) {
          quoteOfAgg = QuoteOfAgg.builder()
                                 .joinRelation(relation)
                                 .column("target_data_id")
                                 .columnType(PGColumnType.String)
                                 .isSingle(true)
                                 .fieldType(FieldType.TEXT)
                                 .build();
        }
      } else {
        QuoteTargetField quoteTargetField = this.buildQuteTargetByUdfObjField(tenantId, standalone, aggApiName,
          lookupField, cachedTableDefinitions);
        quoteOfAgg = QuoteOfAgg.builder()
                               .joinRelation(null)
                               .column(quoteTargetField.column)
                               .columnType(quoteTargetField.columnType)
                               .fieldType(quoteTargetField.fieldType)
                               .isSingle(quoteTargetField.isSingle)
                               .build();
      }
    }
    if (quoteOfAgg == null) {
      return null;
    }
    String value1 = whereInfo.getString("value1");
    String value2 = whereInfo.getString("value2");
    String uiType = whereInfo.getString("type");
    FilterType filterType = FilterType.parseFromId(operator);
    //隔离自定义对象查描述用value0,实际列名是id
    if (FieldType.IN_NOT_IN.contains(operator) && standalone && targetApiName.endsWith("__c")
      && "value0".equals(quoteOfAgg.column)) {
      quoteOfAgg.column = "id";
    }
    return WhereRule.builder()
                    .id(counter.incrementAndGet())
                    .joinRelation(quoteOfAgg.joinRelation)
                    .column(quoteOfAgg.column)
                    .columnType(quoteOfAgg.columnType)
                    .fieldType(quoteOfAgg.fieldType)
                    .isSingle(quoteOfAgg.isSingle)
                    .filterType(filterType)
                    .value1(value1)
                    .value2(value2)
                    .uiType(uiType)
                    .build();
  }
}
