package com.fxiaoke.bi.warehouse.dws.transform.model;

public enum AggType {
  count("计数", "COUNT", 1, "COUNT"),
  /**
   * 求和
   */
  sum("合计", "SUM", 2, "SUM"),
  /**
   * max
   */
  max("最大值", "MAX", 3, "MAX"),
  /**
   * min
   */
  min("最小值", "MIN", 4, "MIN"),
  /**
   * 平均值
   */
  avg("平均值", "AVG", 5, "AVG"),
  /**
   * 使用count(distinct($column_name))
   */
  countdistinct("唯一计数", "DISCOUNT", 6, "COUNT"),
  /**
   * object_id列就是主键列，不用group by
   */
  countuniq("唯一计数2", "uniq", 61, "UNIQ"),

  /**
   * object_id列就是value列，需要group_by
   */
  countuniq2("唯一计数2", "uniq2", 62, "UNIQ"),
  /**
   * CH中aggFunction类型，需要group_by
   */
  countuniq3("唯一计数3", "uniq3", 63, "UNIQ");

  public static final String AGG_DISTINCT_COUNT = "count distinct";

  //成员变量
  private String desc;
  private String name;
  private int index;
  private String chAggColumn;


  //构造方法
  AggType(String desc, String name, int index, String chAggColumn) {
    this.desc = desc;
    this.name = name;
    this.index = index;
    this.chAggColumn = chAggColumn;
  }

  public static String getNameByIndex(int index) {
    for (AggrTypeEnum aggrTypeEnum : AggrTypeEnum.values()) {
      if (aggrTypeEnum.getIndex() == index) {
        return aggrTypeEnum.getName();
      }
    }
    return null;
  }

  public static String getDescByIndex(int index) {
    for (AggrTypeEnum aggrTypeEnum : AggrTypeEnum.values()) {
      if (aggrTypeEnum.getIndex() == index) {
        return aggrTypeEnum.getDesc();
      }
    }
    return null;
  }

  public String getChAggColumn() {
    return chAggColumn;
  }

  public String getName() {
    return name;
  }

  public int getIndex() {
    return index;
  }

  public String getDesc() {
    return desc;
  }

  public static AggType parseFromIndex(int index) {
    switch (index) {
      case 1, 6:
        return count;
      case 2:
        return sum;
      case 3:
        return max;
      case 4:
        return min;
      case 5:
        return avg;
      case 61:
        return countuniq;
    }
    return count;
  }

  public static AggType parseFromOp(String op) {
    return switch (op.toLowerCase()) {
      case "count" -> count;
      case "discount","uniq","countdistinct" -> countuniq;
      case "sum" -> sum;
      case "max" -> max;
      case "min" -> min;
      case "avg" -> avg;
      default -> throw new RuntimeException("can not support this agg type " + op);
    };
  }

  public static AggType parseFromDbFieldType(String type) {
    switch (type) {
      case "count", "sum" -> {
        return sum;
      }
      case "uniq" -> {
        return countuniq3;
      }
      default -> throw new RuntimeException("can not support this agg type, type:" + type);

    }
  }

}
