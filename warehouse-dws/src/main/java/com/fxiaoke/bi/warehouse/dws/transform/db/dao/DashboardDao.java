package com.fxiaoke.bi.warehouse.dws.transform.db.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.DashBoardGlobalFilterConfigDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.DashBoardViewDO;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.DashBoardGlobalFilterConfigMapper;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.DashBoardViewMapper;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2023/7/10
 */
@Slf4j
@Service
public class DashboardDao {
  @Autowired
  private DashBoardGlobalFilterConfigMapper dashBoardGlobalFilterConfigMapper;
  @Autowired
  private DashBoardViewMapper dashBoardViewMapper;
  @Autowired
  private DimRuleDao dimRuleDao;


  /**
   * 获取该图中用到的所有维度集合
   *
   * @param tenantId
   * @param viewId   统计图实体
   * @return
   */
  public Set<String> findAllFieldUsedInDashBoard(String tenantId, String viewId, String schemaId, String themeApiName) {
    List<DashBoardViewDO> dashBoardViewDOS = dashBoardViewMapper.setTenantId(tenantId)
                                                                .queryDashBoardViewsByViewId(tenantId, viewId);
    if (CollectionUtils.isEmpty(dashBoardViewDOS)) {
      log.info("this view not used in dashboard tenantId:{},viewId:{}", tenantId, viewId);
      return null;
    }
    List<String> dashBordIds = dashBoardViewDOS.stream().map(DashBoardViewDO::getDashBoardId).toList();
    if (CollectionUtils.isNotEmpty(dashBordIds)) {
      String[] dashboardIdArray = dashBordIds.toArray(new String[0]);
      List<DashBoardGlobalFilterConfigDO> dbGlobalFilterConfig = dashBoardGlobalFilterConfigMapper.setTenantId(tenantId)
                                                                                                  .queryByDashBoardId(tenantId, dashboardIdArray);
      StatFieldDO actionDateStatField = dimRuleDao.findActionDateStatField(tenantId, schemaId);
      if (actionDateStatField == null) {
        log.error("can not find action_date field tenantId:{},schemaId:{},themeApiName:{}", tenantId, schemaId,
          themeApiName);
        return null;
      }
      StatFieldDO ownerStatField = dimRuleDao.findOwnerStatField(tenantId, schemaId, themeApiName);
      if (ownerStatField == null) {
        log.error("can not find owner field tenantId:{},schemaId:{},themeApiName:{}", tenantId, schemaId, themeApiName);
        return null;
      }
      if (CollectionUtils.isNotEmpty(dbGlobalFilterConfig)) {
        Set<String> addFields = Sets.newHashSet();
        //处理日期 处理人员，部门
        dbGlobalFilterConfig.forEach(filterConfig -> {
          //判断日期是否开启
          if (filterConfig.getIsObsDataFilter() == 1) {
            this.requireAction(viewId, actionDateStatField.getFieldId(), addFields, filterConfig.getLocalDateFilter());
          }
          //判断人员是否开启
          if (filterConfig.getIsObsEmpFilter() == 1) {
            this.requireOwnerDept(viewId, ownerStatField.getFieldId(), addFields, filterConfig.getLocalEmpFilter());
          }
        });
        return addFields;
      }
    }
    return null;
  }

  /**
   * 检测是否需要查询 owner
   *
   * @param viewId
   * @param ownerFieldId
   * @param fields
   * @param localEmpFilter
   */
  private void requireOwnerDept(String viewId, String ownerFieldId, Set<String> fields, String localEmpFilter) {
    if (StringUtils.isBlank(localEmpFilter)) {
      return;
    }
    JSONArray localEmpFilterJsonArray = JSON.parseArray(localEmpFilter);
    if (!localEmpFilterJsonArray.isEmpty()) {
      Map<String, JSONObject> viewFilterObjMap = localEmpFilterJsonArray.stream()
                                                                        .map(viewFilter -> ((JSONObject) viewFilter))
                                                                        .filter(vf -> StringUtils.isNotBlank(vf.getString("viewId")))
                                                                        .collect(Collectors.toMap(item -> item.getString("viewId"), Function.identity()));
      JSONObject viewFilterObj = viewFilterObjMap.get(viewId);
      if (viewFilterObj == null) {
        fields.add(ownerFieldId);
      }
    }
  }

  /**
   * 检测是否需要查询action_date
   *
   * @param viewId
   * @param actionDateFieldId
   * @param fields
   * @param localDateFilter
   */
  private void requireAction(String viewId, String actionDateFieldId, Set<String> fields, String localDateFilter) {
    if (StringUtils.isBlank(localDateFilter)) {
      return;
    }
    JSONArray localDateFilterJsonArray = JSON.parseArray(localDateFilter);
    if (!localDateFilterJsonArray.isEmpty()) {
      Map<String, JSONObject> viewFilterObjMap = localDateFilterJsonArray.stream()
                                                                         .map(viewFilter -> ((JSONObject) viewFilter))
                                                                         .filter(vf -> StringUtils.isNotBlank(vf.getString("viewId")))
                                                                         .collect(Collectors.toMap(item -> item.getString("viewId"), Function.identity()));
      JSONObject viewFilterObj = viewFilterObjMap.get(viewId);
      if (viewFilterObj == null) {
        fields.add(actionDateFieldId);
      }
    }
  }
}
