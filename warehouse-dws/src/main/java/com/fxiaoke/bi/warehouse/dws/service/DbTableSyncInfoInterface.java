package com.fxiaoke.bi.warehouse.dws.service;


import lombok.Data;

import java.util.List;

/**
 * @Author:jief
 * @Date:2024/2/26
 */
public interface DbTableSyncInfoInterface {

  /**
   *
   * @param viewId 统计图 id
   * @param fieldId 指标 id
   * @param forceFresh true 不查缓存
   * @return 返回统计图最近更新时间 {@link com.fxiaoke.bi.warehouse.dws.service.DbTableSyncInfoInterface.FieldAggDelayInfo}
   */
  FieldAggDelayInfo findDetailUpdateTime(String tenantId, String viewId, String fieldId, boolean forceFresh, String timezone);

  @Data
  class DbSyncAggInfo {
    private String dbSyncId;
    private Integer batchNum;
    private Integer currentBatchNum;
    private Long lastSyncTime;
  }


  /**
   * 统计图最近更新时间，延迟和最近计算批次
   */
  @Data
  class FieldAggDelayInfo {
    /**
     * 延迟
     */
    private Integer refreshDelay;
    /**
     * 最近更新时间
     */
    private String updateTime;
    /**
     * 最近计算的批次
     */
    private Integer batchNum;
    /**
     * prewhere 要查的分区
     */
    private List<String> partitions;
  }
}
