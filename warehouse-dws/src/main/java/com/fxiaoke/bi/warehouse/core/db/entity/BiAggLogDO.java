package com.fxiaoke.bi.warehouse.core.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;

/**
 * @Author:jief
 * @Date:2024/12/11
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BiAggLogDO {
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "view_id")
  private String viewId;
  @Column(name = "view_version")
  private int viewVersion;
  @Column(name = "batch_num")
  private long batchNum;
  @Column(name = "field_id")
  private String fieldId;
  @Column(name = "cost")
  private long cost;
}
