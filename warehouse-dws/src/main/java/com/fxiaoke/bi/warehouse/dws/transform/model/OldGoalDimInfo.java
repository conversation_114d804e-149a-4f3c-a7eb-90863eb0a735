package com.fxiaoke.bi.warehouse.dws.transform.model;

import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author:jief
 * @Date:2023/7/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OldGoalDimInfo {
  /**
   * 每个目标对应一个统计图的图。
   */
  private String viewId;
  /**
   * 主题对象上的维度
   */
  private List<DimRule> themeDimRules;
  /**
   * 考核对象查找关联子目标对象关系
   */
  private UdfObjFieldDO lookupField;
  /**
   * 子目标维度
   */
  private DimRule subGoalDimRule;

  private String subGoalObjName;
}
