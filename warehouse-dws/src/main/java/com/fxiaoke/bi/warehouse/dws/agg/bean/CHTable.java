package com.fxiaoke.bi.warehouse.dws.agg.bean;

import lombok.Builder;
import lombok.Data;

import com.fxiaoke.bi.warehouse.common.bean.BIPair;

import java.util.List;

/**
 * @Author:jief
 * @Date:2024/8/15
 */
@Builder
@Data
public class CHTable {
  private String tableName;
  private List<String> columnNames;
  private List<String> sortedKeys;
  private BIPair<String/*columnName*/,String/*columnType lowercase*/> filterTimeColumn;
}
