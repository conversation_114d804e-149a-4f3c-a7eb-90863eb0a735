package com.fxiaoke.bi.warehouse.ods.bean;

import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.service.DbTableSyncInfoService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.concurrent.NotThreadSafe;
import java.util.List;

@NotThreadSafe
@Slf4j
public class BatchUpdateDbTableSyncInfo {
  private final List<DbTableSyncInfoDO> dbTableSyncInfoCache = Lists.newArrayList();
  private DBSyncInfoBO dbSyncInfoCopy;
  private DbTableSyncInfoService dbTableSyncInfoService;
  private int threshold;

  public static BatchUpdateDbTableSyncInfo createInstance(DBSyncInfoBO dbSyncInfoCopy,
                                                          DbTableSyncInfoService dbTableSyncInfoService,
                                                          int threshold) {
    return new BatchUpdateDbTableSyncInfo(dbSyncInfoCopy, dbTableSyncInfoService, threshold);
  }

  private BatchUpdateDbTableSyncInfo(DBSyncInfoBO dbSyncInfoCopy, DbTableSyncInfoService dbTableSyncInfoService, int threshold) {
    this.dbSyncInfoCopy = dbSyncInfoCopy;
    this.dbTableSyncInfoService = dbTableSyncInfoService;
    this.threshold = threshold;
  }

  private BatchUpdateDbTableSyncInfo() {
  }

  public void append(DbTableSyncInfoDO dbTableSyncInfoDO) {
    dbTableSyncInfoCache.add(dbTableSyncInfoDO);
    if (dbTableSyncInfoCache.size() >= this.threshold) {
      int result = dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfoCopy, dbTableSyncInfoCache);
      log.info("append batchUpsertDbTableSyncInfo pgDb:{},pgSchema:{},chDb:{},size:{}", dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getPgSchema(), dbSyncInfoCopy.getChDb(), result);
      dbTableSyncInfoCache.clear();
    }
  }

  public void flush() {
    if (!dbTableSyncInfoCache.isEmpty()) {
      int result = dbTableSyncInfoService.batchUpdateDbTableSyncInfo(dbSyncInfoCopy, dbTableSyncInfoCache);
      log.info("flush batchUpsertDbTableSyncInfo pgDb:{},pgSchema:{},chDb:{},size:{}", dbSyncInfoCopy.getPgDb(), dbSyncInfoCopy.getPgSchema(), dbSyncInfoCopy.getChDb(), result);
      dbTableSyncInfoCache.clear();
    }
  }
}
