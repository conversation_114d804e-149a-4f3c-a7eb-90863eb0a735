package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;

import com.facishare.bi.metadata.context.dto.dw.RptViewDwContext;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2023/6/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatViewPreArg {
  private String tenantId;
  /**
   * 图表类型 0：agg；2：老目标；1：多维目标；
   */
  private int sourceType;
  /**
   * 图表id或目标id,可以为空
   */
  private String sourceId;
  //主题id
  private String schemaId;
  //时区
  private String timeZone;
  /**
   * 包含维度，筛选器，指标，固定下钻，自由下钻，图表联动,计算指标等
   */
  private List<String> allStatFieldIds;
  /**
   * 指标做筛选器 fieldId
   */
  private List<String> aggFilterFieldIds;
  /**
   * 0/null: 抽样明细
   * 1     : 实时查SQL(status = 2)
   */
  private Integer queryDetailType;
  /**
   * 目标和报表需要传的信息
   */
  private RptViewDwContext rptViewDwContext;
  /**
   * 财年类型，周，月，季，年
   * 只有联合目标分析的时候才有值
   */
  private String checkCyCle;
  /**
   * 如果图中选了虚拟指标,需要涉及的目标规则传递过来
   * goal_rule.id
   */
  private List<String> goalRuleIds;
  /**
   * 如果是目标查看明细,需要指定action_date的id -> BiMtDetailDO.detailId
   */
  private String goalRuleActionDateMtDetailId;
  /**
   * 企业分群筛选器
   */
  private UserSegmentFilter userSegmentFilter;
  /**
   * 目标完成值查看明细时,goal_value_obj设置的考核维度需要匹配到第几层
   */
  private int goalDetailMatchLevel;
  /**
   * 统计图谓词下推
   */
  private List<StatViewFilter> statFilter;
  
  public String toJSONString() {
    return JSON.toJSONString(this);
  }

  public String findTimeZone() {
    return StringUtils.isBlank(this.timeZone) ? Constants.DEFAULT_TIME_ZONE : this.timeZone;
  }

  /**
   * 整合所有指标加维度
   * @return
   */
  public Set<String> mergeAllFields() {
    Set<String> fields = Sets.newHashSet();
    if (CollectionUtils.isNotEmpty(allStatFieldIds)) {
      fields.addAll(allStatFieldIds);
    }
    if (CollectionUtils.isNotEmpty(aggFilterFieldIds)) {
      fields.addAll(aggFilterFieldIds);
    }
    return fields;
  }


  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class UserSegmentFilter {
    /**
     * 分群id
     */
    private List<String> userSegmentIdList;

    /**
     * 分群筛选器比较符
     */
    private int operator;
  }
}
