package com.fxiaoke.bi.warehouse.ods.compare.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-07-26
 * @desc ch和pg表的数量差异，数量差异在一定阈值之内的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComparePgToChCountResult {

    /**
     * 表名
     */
    private String tableName;

    /**
     * ch表的数量
     */
    private long chTableCount;

    /**
     * pg表的数量
     */
    private long pgTableCount;
}
