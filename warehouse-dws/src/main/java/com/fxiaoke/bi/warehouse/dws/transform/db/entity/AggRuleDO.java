package com.fxiaoke.bi.warehouse.dws.transform.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;

/**
 * 聚合指标规则（agg_rule）
 *
 * <AUTHOR>
 * @date 2018.8.13
 */
@Table(name = "agg_rule")
@Data
public class AggRuleDO {
  /**
   * 企业ID
   */
  @Column(name = "tenant_id")
  private String tenantId;

  /**
   * 规则ID
   */
  @Column(name = "rule_id")
  private String ruleId;

  /***
   * 字段id
   */
  @Column(name = "field_id")
  private String fieldId;

  /**
   * 主题名称
   */
  @Column(name = "theme_api_name")
  private String themeApiName;

  /**
   * 指标对象
   */
  @Column(name = "check_object_api_name")
  private String checkObjectApiName;

  /**
   * 指标对象展示名
   */
  @Column(name = "check_object_api_display_name")
  private String checkObjectApiDisplayName;

  /**
   * 指标对象从对象名称
   */
  @Column(name = "check_object_api_name_slave")
  private String checkObjectApiNameSlave;

  /**
   * 指标对象从对象展示名称
   */
  @Column(name = "check_object_api_display_name_slave")
  private String checkObjectApiDisplayNameSlave;


  /**
   * 关联apiName的逻辑列名
   */
  @Column(name = "check_apiname_lookup_field")
  private String checkApinameLookupField;

  /**
   * 关联apiName的物理列名
   */
  @Column(name = "check_apiname_lookup_field_location")
  private String checkApinameLookupFieldLocation;


  /**
   * 指标字段
   */
  @Column(name = "check_field_api_name")
  private String checkFieldApiName;

  /**
   * 指标字段展示名
   */
  @Column(name = "check_field_api_display_name")
  private String checkFieldApiDisplayName;

  /**
   * 指标的中文描述
   */
  @Column(name = "display_name")
  private String displayName;

  /**
   * 时间标尺逻辑列名
   */
  @Column(name = "count_time_api_name_field")
  private String countTimeApiNameField;

  /**
   * 时间标尺逻辑列名展示名
   */
  @Column(name = "count_time_api_display_name_field")
  private String countTimeApiDisplayNameField;

  /**
   * 时间标尺lookup字段
   */
  @Column(name = "count_time_lookup_field_name")
  private String countTimeLookupFieldName;

  /**
   * 时间标尺lookup字段展示名
   */
  @Column(name = "count_time_lookup_field_display_name")
  private String countTimeLookupFieldDisplayName;

  /**
   * 时间标尺物理列名
   */
  @Column(name = "count_time_api_name_field_location")
  private String countTimeApiNameFieldLocation;

  /**
   * 指标时间标尺的类型
   */
  @Column(name = "count_time_api_name_type")
  private String countTimeApiNameType;

  /**
   * 指标对象和时间标尺对象的lookup字段名，如果时间标尺所属对象就是指标对象的话，这个字段为空。这个字段存放的是物理列名
   */
  @Column(name = "check_count_time_lookup_field_location")
  private String checkCountTimeLookupFieldLocation;

  /**
   * 指标的聚合类型
   */
  @Column(name = "check_field_aggregate_type")
  private Integer checkFieldAggregateType;

  /**
   * 指标的聚合条件
   */
  @Column(name = "wheres")
  private String wheres;

  /**
   * 创建时间
   */
  @Column(name = "create_time")
  private Date createTime;

  /**
   * 创建人
   */
  @Column(name = "creator")
  private String creator;

  /**
   * 最后修改时间
   */
  @Column(name = "last_modified_time")
  private Date lastModifiedTime;

  /**
   * 最后更改人
   */
  @Column(name = "last_modifier")
  private String lastModifier;

  /**
   * 是否删除
   */
  @Column(name = "is_deleted")
  private int isDeleted = -1;

  /**
   * 表明是基础字段还是聚合字段
   */
  @Column(name = "type")
  private String type;

  @Column(name = "is_pre")
  private int isPre;

  /**
   * 规则描述
   */
  @Column(name = "description")
  private String description;

  /**
   * 主从关联字段
   */
  @Column(name = "md_field_name")
  private String mdFieldName;

  //目标二期字段
  @Column(name = "fiscal_years")
  private String countFiscalYear;
  @Column(name = "start_month")
  private int startMonth;

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AggRuleDO aggRuleDO = (AggRuleDO) o;
    return tenantId.equals(aggRuleDO.tenantId) && ruleId.equals(aggRuleDO.ruleId) &&
      fieldId.equals(aggRuleDO.fieldId) && themeApiName.equals(aggRuleDO.themeApiName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tenantId, ruleId, fieldId, themeApiName);
  }
}
