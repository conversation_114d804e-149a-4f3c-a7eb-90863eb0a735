package com.fxiaoke.bi.warehouse.ods.args;

import lombok.Data;

import java.util.List;

/**
 * ClickHouse和PostgreSQL数据对比参数
 */
@Data
public class ComparePgChDataArg {
  /**
   * 企业标识
   */
  private String tenantId;

  /**
   * 表名
   */
  private String tableName;

  /**
   * 数据主键列表，非必填
   */
  private List<String> primaryIds;

  /**
   * 分页大小
   */
  private Integer pageSize;

  /**
   * 批次ID，非必填
   * 用于过滤特定批次的数据
   */
  private String batchId;

  /**
   * 对象API名称，非必填
   * 用于过滤特定对象的数据
   */
  private String objectDescribeApiName;
} 