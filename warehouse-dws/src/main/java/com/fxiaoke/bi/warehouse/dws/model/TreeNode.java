package com.fxiaoke.bi.warehouse.dws.model;

import com.fxiaoke.bi.warehouse.common.db.er.NodeJoin;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import lombok.*;

import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/3/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Generated
public class TreeNode {
  private Integer parentId;
  private NodeTable nodeTable;
  private boolean rootNode;
  public Integer nodeId() {
    return nodeTable.createNodeId();
  }

  public String nodeName() {
    return nodeTable.getName() + "^" + nodeTable.objectDescribeApiName();
  }

  /**
   * 获取joinSet
   *
   * @return
   */
  public Set<NodeJoin> joinSet() {
    return nodeTable.getJoinSet();
  }
}
