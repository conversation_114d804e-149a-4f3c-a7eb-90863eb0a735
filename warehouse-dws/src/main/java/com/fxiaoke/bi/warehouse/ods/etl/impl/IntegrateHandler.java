package com.fxiaoke.bi.warehouse.ods.etl.impl;

import com.fxiaoke.bi.warehouse.common.arg.AggRequestContext;
import com.fxiaoke.bi.warehouse.ods.etl.AbstractETLHandler;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.IntegrateServiceImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author:jief
 * @Date:2024/12/25
 */
@Service
public class IntegrateHandler extends AbstractETLHandler<Boolean> {
  @Resource
  private IntegrateServiceImpl integrateService;

  public IntegrateHandler(@Qualifier("bich2CHHandler") BICH2CHHandler bich2CHHandler) {
    this.setNextHandler(bich2CHHandler);
  }

  @Override
  public Boolean doHandler(AggRequestContext aggRequestContext) {
    return null;
  }
}
