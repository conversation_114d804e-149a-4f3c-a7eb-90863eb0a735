package com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper;

import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * BI PostgreSQL数据库操作接口
 */
@Repository
public interface PaasPgMapper extends ITenant<PaasPgMapper> {

  /**
   * 根据对象名称获取字段信息
   *
   * @param tenantId 租户ID
   * @param objName  对象名称
   * @return 字段信息列表
   */
  @Select("SELECT field_id, tenant_id, api_name, field_num FROM mt_field WHERE tenant_id = #{tenantId} AND describe_api_name = #{objName}")
  List<Map<String, Object>> getFieldsByObjName(@Param("tenantId") String tenantId, @Param("objName") String objName);

  /**
   * 根据对象名称和字段类型获取字段信息
   *
   * @param tenantId    租户ID
   * @param objName     对象名称
   * @param typeExpress 类型表达式
   * @return 字段信息列表
   */
  @Select("SELECT field_num FROM mt_field WHERE tenant_id = #{tenantId} AND field_num IS NOT NULL AND describe_api_name = #{objName} AND type ${typeExpress}")
  List<Integer> getFieldsByObjNameAndType(@Param("tenantId") String tenantId,
                                          @Param("objName") String objName,
                                          @Param("typeExpress") String typeExpress);
}