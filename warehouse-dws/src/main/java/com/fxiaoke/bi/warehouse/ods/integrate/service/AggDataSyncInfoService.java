package com.fxiaoke.bi.warehouse.ods.integrate.service;

import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;

import java.util.List;
import java.util.Map;

public interface AggDataSyncInfoService {

    /**
     * 根据tenantId, viewId查询ch agg_data_sync_info信息,如果没有查询到就生成一个新的agg_data_sync_info,查询到了取第一个
     */
    AggDataSyncInfoDo queryAggDataSyncInfoByTenantIdAndViewId(RouterInfo upStreamTenantIdRouterInfo, String tenantId, String viewId);

    /**
     * 根据策略Id, tenantId, viewId查询ch agg_data_sync_info信息,如果没有查询到就生成一个新的agg_data_sync_info,查询到了取第一个
     */
    AggDataSyncInfoDo queryAggDataSyncInfoByPolicyId(String upTenantId, String downTenantId, String viewId, String policyId, String statViewUniqueKey);

    /**
     * 根据策略Id，policyId查询agg_data_sync_info
     */
    List<AggDataSyncInfoDo> queryAggDataSyncInfoByPolicyId(String upTenantId, List<String> policyIdList);

    /**
     * 修改agg_data_sync_info状态根据，根据传入的status
     */
    void updateAggDataSyncInfo(AggDataSyncInfoDo aggDataSyncInfoDo, SyncStatusEnum syncStatusEnum);

    /**
     * ch中使用已增代删的方式修改agg_data_sync_info状态
     */
    void updateAggDataSyncInfoByInsert(String upStreamTenantId, AggDataSyncInfoDo aggDataSyncInfoDo, SyncStatusEnum syncStatusEnum);

    /**
     * ch中使用已增代删的方式修改agg_data_sync_info状态 - 批量
     */
    void updateAggDataSyncInfoBatch(String upStreamTenantId, List<AggDataSyncInfoDo> aggDataSyncInfoDos);

    /**
     * 下游图表变更，将下游的agg_downstream_data数据设置为1，清空agg_downstream_data表数据
     */
    void insertBeforeAggDownStreamData(RouterInfo upStreamTenantIdRouterInfo, AggDataSyncInfoDo aggDataSyncInfoDo, int version, String statViewUniqueKey, String objectId, long nextBatchNum, String policyId,String partitionName);

    /**
     * ch中使用已增代删的方式修改agg_data_sync_info状态-同步策略
     */
    void updateAggDataSyncInfoByPolicyId(String upStreamTenantId, AggDataSyncInfoDo aggDataSyncInfoDo, SyncStatusEnum syncStatusEnum);

    void insertBiDataSyncPolicyLog(String tenantId, String sourceTenantId, String policyId, String logType, String msg);

    /**
     * 根据agg_data_sync进行数据同步
     */
    long synchronizationAggDataToAggDownStreamData(String upStreamTenantId, AggDataSyncInfoDo aggDataSyncInfoDo, List<String> fieldList, Map<String, String> downToUpMap, boolean isFullSynchronization, RouterInfo upRouterInfo, RouterInfo downStreamRouterInfo, String objectId, long currentTimeMillis, long nextBatchNum, String viewId, String policyId,String partitionName);
}
