import networkx as nx
import matplotlib.pyplot as plt
import json

with open('D:\\常用脚本\\widetable\\biz_account.json', 'r') as file:
    data = json.load(file)

print(data)

dagNodes=[]
for node in data['dagNodes']:
    dagNodes.append(node['id'])
# 创建一个有向图
G = nx.DiGraph()

# 添加节点
G.add_nodes_from(dagNodes)

# 添加有向边并指定权重和描述
edges_with_descriptions=[]

for edge in data['dagEdges']:
    edges_with_descriptions.append((edge['from'],edge['to'],{'description':"%s:%d" % ('weight', edge['weight'])}))

# 添加边到图中
G.add_edges_from(edges_with_descriptions)
# G.add_edge(1, 2)
# 绘制图形
# pos = nx.circular_layout(G)  # 使用 spring 布局
pos = nx.shell_layout(G)
# 绘制节点
nx.draw_networkx_nodes(G, pos, node_size=500, node_color='lightblue')

# 绘制边，带箭头
nx.draw_networkx_edges(G, pos, arrowstyle='-|>', arrowsize=20, edge_color='gray',arrows=True)

# 绘制节点标签
nx.draw_networkx_labels(G, pos, font_size=10, font_weight='bold')

# 绘制边权重和描述
edge_labels = nx.get_edge_attributes(G, 'description')
nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=10)

# 获取当前轴对象
ax = plt.gca()

# 设置边框宽度
ax.spines['top'].set_linewidth(2)
ax.spines['bottom'].set_linewidth(2)
ax.spines['left'].set_linewidth(2)
ax.spines['right'].set_linewidth(2)

# 设置边框颜色
ax.spines['top'].set_color('black')
ax.spines['bottom'].set_color('black')
ax.spines['left'].set_color('black')
ax.spines['right'].set_color('black')

plt.title("Directed Acyclic Graph (DAG) with Arrows and Descriptions")
plt.axis('on')  # 关闭坐标轴
plt.show()