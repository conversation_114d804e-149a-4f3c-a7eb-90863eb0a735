<configuration scan="false" scanPeriod="60 seconds" debug="false">
  <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
        java.lang.Thread,
        javassist,
        sun.reflect,
        org.springframework,
        org.apache,
        org.eclipse.jetty,
        $Proxy,
        java.net,
        java.io,
        javax.servlet,
        org.junit,
        com.mysql,
        com.sun,
        org.mybatis.spring,
        cglib,
        CGLIB,
        java.util.concurrent,
        okhttp,
        org.jboss,
        }%n
      </pattern>
    </encoder>
  </appender>
  <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${catalina.home:-../}/logs/fs-app.log</file>
    <!-- 可让每天产生一个日志文件，自动回滚 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${catalina.home:-../}/logs/fs-app-%d{yyyyMMdd}.%i.log.zip</fileNamePattern>
      <maxFileSize>500MB</maxFileSize>
      <maxHistory>60</maxHistory>
      <totalSizeCap>10GB</totalSizeCap>
    </rollingPolicy>

    <encoder>
      <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
        java.lang.Thread,
        javassist,
        sun.reflect,
        org.springframework,
        org.apache,
        org.eclipse.jetty,
        $Proxy,
        java.net,
        java.io,
        javax.servlet,
        org.junit,
        com.mysql,
        com.sun,
        org.mybatis.spring,
        cglib,
        CGLIB,
        java.util.concurrent,
        okhttp,
        org.jboss,
        }%n
      </pattern>
    </encoder>
  </appender>

  <appender name="RefreshAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${catalina.home}/logs/refresh-%d{yyyy-MM-dd}.log.zip</fileNamePattern>
      <maxHistory>7</maxHistory>
    </rollingPolicy>
    <encoder>
      <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
      <pattern>%d{HH:mm:ss} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
        java.lang.Thread,
        javassist,
        sun.reflect,
        org.springframework,
        org.apache,
        org.eclipse.jetty,
        $Proxy,
        java.net,
        java.io,
        javax.servlet,
        org.junit,
        com.mysql,
        com.sun,
        org.mybatis.spring,
        cglib,
        CGLIB,
        java.util.concurrent,
        okhttp,
        org.jboss,
        org.codehaus.groovy,
        groovy.lang,
        java.lang.reflect,
        }%n
      </pattern>
    </encoder>
  </appender>
  <!-- 异步输出 -->
  <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
    <queueSize>512</queueSize>
    <appender-ref ref="RollingFile"/>
  </appender>

  <logger name="druid.sql" level="INFO"/>
  <logger name="org.hibernate" level="WARN"/>
  <logger name="org.springframework" level="WARN"/>
  <logger name="com.opensymphony" level="WARN"/>
  <logger name="org.apache" level="WARN"/>
  <logger name="p6spy" level="WARN"/>

  <logger name="refresh" level="INFO" addtivity="false">
    <appender-ref ref="RefreshAppender"/>
  </logger>

  <root level="INFO">
    <appender-ref ref="ASYNC"/>
  </root>
</configuration>
