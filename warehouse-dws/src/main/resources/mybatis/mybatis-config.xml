<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
  PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
  <settings>
    <setting name="cacheEnabled" value="false"/>
    <setting name="lazyLoadingEnabled" value="true"/>
    <setting name="multipleResultSetsEnabled" value="true"/>
    <setting name="useColumnLabel" value="true"/>
    <setting name="useGeneratedKeys" value="false"/>
    <setting name="autoMappingBehavior" value="PARTIAL"/>
    <setting name="defaultExecutorType" value="SIMPLE"/>
    <!--<setting name="defaultStatementTimeout" value="120"/>-->
    <setting name="safeRowBoundsEnabled" value="false"/>
    <setting name="mapUnderscoreToCamelCase" value="true"/>
    <setting name="localCacheScope" value="STATEMENT"/>
    <setting name="jdbcTypeForNull" value="OTHER"/>
    <setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString"/>
  </settings>
  <typeHandlers>
    <typeHandler javaType="string" handler="com.github.mybatis.handler.StringTypeUtf8mb4Handler"/>
    <!--用于把数据库中json数据转成相应的java对象-->
  </typeHandlers>
  <plugins>
    <plugin interceptor="com.github.mybatis.interceptor.MasterSlaveInterceptor"/>
    <plugin interceptor="com.github.mybatis.interceptor.PaginationAutoMapInterceptor">
      <property name="dialect" value="postgresql"/>
    </plugin>
    <!-- com.github.pagehelper为PageHelper类所在包名 -->
    <plugin interceptor="com.github.pagehelper.PageHelper">
      <property name="dialect" value="postgresql"/>
      <!-- 该参数默认为false -->
      <!-- 设置为true时，会将RowBounds第一个参数offset当成pageNum页码使用 -->
      <!-- 和startPage中的pageNum效果一样-->
      <property name="offsetAsPageNum" value="true"/>
      <!-- 该参数默认为false -->
      <!-- 设置为true时，使用RowBounds分页会进行count查询 -->
      <property name="rowBoundsWithCount" value="true"/>

      <!-- 设置为true时，如果pageSize=0或者RowBounds.limit = 0就会查询出全部的结果 -->
      <!-- （相当于没有执行分页查询，但是返回结果仍然是Page类型）
      <property name="pageSizeZero" value="true"/>-->

      <!-- 3.3.0版本可用 - 分页参数合理化，默认false禁用 -->
      <!-- 启用合理化时，如果pageNum<1会查询第一页，如果pageNum>pages会查询最后一页 -->
      <!-- 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据 -->
      <property name="reasonable" value="true"/>
      <!-- 3.5.0版本可用 - 为了支持startPage(Object params)方法 -->
      <!-- 增加了一个`params`参数来配置参数映射，用于从Map或ServletRequest中取值 -->
      <!-- 可以配置pageNum,pageSize,count,pageSizeZero,reasonable,不配置映射的用默认值 -->
      <!-- 不理解该含义的前提下，不要随便复制该配置
      <property name="params" value="pageNum=start;pageSize=limit;"/>    -->
    </plugin>
    <!--支持schema隔离带插件-->
    <plugin interceptor="com.github.mybatis.interceptor.InjectSchemaInterceptor">
      <property name="dialect" value="postgresql"/>
      <property name="tenantPolicyBeanName" value="mybatisTenantPolicy"/>
    </plugin>
  </plugins>
  <mappers>
    <package name="com.github.mybatis.mapper"/>
  </mappers>

</configuration>
