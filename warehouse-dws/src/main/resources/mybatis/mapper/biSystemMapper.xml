<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.bi.warehouse.ods.dao.mapper.BISystemMapper">

  <delete id="deleteTableSyncInfoByDbSyncId">
    delete from db_table_sync_info where db_sync_id=any(array[#{dbSyncIds}])
    <if test="tables != null">
       and table_name=any(array[#{tables}])
    </if>
  </delete>

  <update id="updateDbSyncInfoStatus">
    update db_sync_info set status=#{status},last_modified_time=#{lastModifiedTime}
    <if test="lastSyncTime != null">
      ,last_sync_time=#{lastSyncTime}
    </if>
    where id=#{id}
  </update>

  <update id="batchUpdateDbTableSyncInfo" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
      update db_table_sync_info set max_sys_modified_time = #{item.maxSysModifiedTime},
      last_sync_time = #{item.lastSyncTime},
      api_name_ei_map = #{item.apiNameEiMap},
      last_modified_time = #{item.lastModifiedTime},
      is_deleted = #{item.isDeleted},
      batch_num = #{item.batchNum},
      status = #{item.status}
      where id = #{item.id}
    </foreach>
  </update>

  <update id="updateDbSyncInfo">
    update db_sync_info
    set status=#{status}, batch_num=#{batchNum}, last_modified_time=#{lastModifiedTime}, is_deleted=#{isDeleted}, last_sync_time=#{lastSyncTime}, last_merge_agg_time=#{lastMergeAggTime}, last_sync_eis=#{lastSyncEis}
    where ch_db=#{chDb}
      and pg_db=#{pgDb}
      and pg_schema=#{pgSchema}
  </update>
<!--  <update id="updateDbSyncInfoWithPartition">-->
<!--    update db_sync_info-->
<!--    set status=#{status}, batch_num=#{batchNum}, last_modified_time=#{lastModifiedTime}, is_deleted=#{isDeleted}, last_sync_time=#{lastSyncTime}, last_merge_agg_time=#{lastMergeAggTime}, last_sync_eis=#{lastSyncEis}-->
<!--    where ch_db=#{chDb}-->
<!--      and pg_db=#{pgDb}-->
<!--      and pg_schema=#{pgSchema}-->
<!--  </update>-->
</mapper>