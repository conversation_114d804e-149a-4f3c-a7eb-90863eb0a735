<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.bi.warehouse.dws.transform.db.mapper.CustomStatisticAggFieldMapper">

  <select id="findAggRuleByFieldId" resultType="map">
    SELECT ar.tenant_id,
           ar.theme_api_name,
           ar.check_object_api_name,
           ar.check_apiname_lookup_field,
           ar.check_apiname_lookup_field_location,
           ar.check_field_api_name,
           ar.display_name,
           ar.count_time_api_name_field,
           ar.count_time_api_name_field_location,
           ar.count_time_api_name_type,
           ar.check_count_time_lookup_field_location,
           ar.check_field_aggregate_type,
           ar.type,
           ar.wheres,
           ar.fiscal_years,
           ar.start_month,
           ar.is_deleted,
           ar.count_time_lookup_field_name,
           ar.md_field_name,
           sf.status,
           sf.field_id,
           sf.field_location
    FROM stat_field sf
           INNER JOIN agg_rule ar ON sf.tenant_id = ar.tenant_id AND sf.field_id = ar.field_id
    WHERE sf.tenant_id = #{tenantId}
      AND sf.field_id = #{fieldId}
      AND ar.theme_api_name = #{themeApiName}
      AND sf.agg_dim_type IN ('agg', 'base_agg')
  </select>
</mapper>
