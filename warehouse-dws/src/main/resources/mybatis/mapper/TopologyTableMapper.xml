<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyTableMapper">

  <update id="updateTopologyStatusByUniqueKey">
    update bi_mt_topology_table set batch_num=#{batchNum}
    <if test="status != null">
      ,status=#{status}
    </if>
    <if test="maxModifiedTime != null">
      ,latest_agg_time=#{maxModifiedTime}
    </if>
    <if test="isUpdateStatRule == true and statListJson != null">
      ,stat_list_json=#{statListJson}
    </if>
    <if test="incVersion == true">
      ,version=version+1
    </if>
    where tenant_id=#{tenantId} and stat_view_unique_key=#{uniqueKey}
  </update>

  <update id="updateTopologyStatusByViewId">
    update bi_mt_topology_table set batch_num=#{batchNum}
    <if test="status != null">
      ,status=#{status}
    </if>
    <if test="maxModifiedTime != null">
      ,latest_agg_time=#{maxModifiedTime}
    </if>
    <if test="isUpdateStatRule == true and statListJson != null">
      ,stat_list_json=#{statListJson}
    </if>
    where tenant_id=#{tenantId} and source_id=#{viewId} and version=#{version}
  </update>

  <update id="resetTopologyByEi">
    update bi_mt_topology_table set status=#{status} where tenant_id=#{tenantId}
    <if test="sourceIds!= null">
       and source_id = any(array[#{sourceIds}])
    </if>
    and source=#{source} and is_deleted=0
  </update>

  <update id="batchDeleteTopologyTableBySourceId">
    update bi_mt_topology_table set is_deleted=#{isDeleted} where tenant_id=#{tenantId}
    <if test="viewIds!= null">
      and source_id=any(array[#{viewIds}])
    </if>
    <if test="source!= null">
      and source=#{source}
    </if>
  </update>

</mapper>