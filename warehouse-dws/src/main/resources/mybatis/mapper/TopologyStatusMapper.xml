<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyStatusMapper">

  <update id="batchUpdateStatus">
    update bi_mt_topology_status set status=#{status},last_modified_time=#{lastModifiedTime} where tenant_id=#{tenantId}
    <if test="viewIds != null">
      and source_id=any(array[#{viewIds}])
    </if>
    <if test="source != null">
      and source_id=any(select source_id from bi_mt_topology_table where tenant_id=#{tenantId} and source=#{source})
    </if>
  </update>
</mapper>