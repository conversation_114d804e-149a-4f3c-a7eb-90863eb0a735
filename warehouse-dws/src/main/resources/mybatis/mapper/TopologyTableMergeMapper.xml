<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyTableMergeMapper">

  <update id="batchDeleteTopologyTableMerge">
    update bi_mt_topology_table_merge set is_deleted=1,version=version+1 where tenant_id=#{tenantId}
    <if test="uniqueKey!= null">
      and id = any(array[#{uniqueKey}])
    </if>
  </update>

  <update id="updateTopologyTableMergeStatusAndVersion">
    update bi_mt_topology_table_merge set batch_num=#{batchNum},version=version+1,last_modified_time=#{lastModifiedTime}
    <if test="status != null">
      ,status=#{status}
    </if>
    <if test="maxModifiedTime != null">
      ,latest_agg_time=#{maxModifiedTime}
    </if>
    where id=#{id} and tenant_id=#{tenantId}
  </update>

  <update id="batchDeleteTopologyTableMergeBySource">
    update bi_mt_topology_table_merge set is_deleted=1,version=version+1 where tenant_id=#{tenantId}
    <if test="source != null">
      and id = any(select stat_view_unique_key from bi_mt_topology_table where tenant_id=#{tenantId} and source=#{source})
    </if>
  </update>

  <update id="updateTopologyTableMergeByStatus">
    update bi_mt_topology_table_merge set last_modified_time=#{lastModifiedTime},batch_num=#{batchNum}
    <if test="maxModifiedTime != null">
      ,latest_agg_time=#{maxModifiedTime}
    </if>
    where tenant_id=#{tenantId} and id=#{id} and status=#{status}
  </update>

  <update id="updateTopologyTableMergeStatus">
    update bi_mt_topology_table_merge set status=#{status},last_modified_time=#{lastModifiedTime},batch_num=#{batchNum}
    <if test="maxModifiedTime != null">
      ,latest_agg_time=#{maxModifiedTime}
    </if>
    where tenant_id=#{tenantId} and id=#{id}
  </update>

</mapper>