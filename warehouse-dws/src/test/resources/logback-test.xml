<configuration scan="false" scanPeriod="60 seconds" debug="false">
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern> %d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %msg%n</pattern>
    </encoder>
  </appender>

  <logger name="druid.sql" level="INFO"/>
  <logger name="org.hibernate" level="WARN"/>
  <logger name="org.springframework" level="WARN"/>
  <logger name="p6spy" level="WARN"/>
  <!--<logger name="com.opensymphony" level="WARN"/>-->
  <!--<logger name="org.apache" level="WARN"/>-->

  <root level="INFO">
    <appender-ref ref="STDOUT"/>
  </root>
</configuration>
