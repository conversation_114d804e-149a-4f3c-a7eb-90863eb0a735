{"aggEffectApiNames": {"BI_67e4fef4a6085300014047e6": ["event_data_click", "dim_enterprise_info"], "BI_67e4fed5a6085300014047df": ["event_data_click", "dim_enterprise_info"], "BI_67e4edaca60853000140458b": ["event_data_click", "dim_enterprise_info"]}, "apiName": "event_data_click", "batchNum": 0, "commonDimList": ["data_auth_code", "out_data_auth_code", "out_tenant_id", "dim_int_1"], "database": "fsbidb044003004", "databaseId": "58eae1c17bf620588fab4267b013c3ea", "detailSource": 0, "dimUniqFieldMapper": {}, "goalDetailMatchLevel": 0, "needCheckTables": false, "source": 0, "statFieldLocation": {"BI_67e4fef4a6085300014047e6": "agg_sum_1", "BI_67e4fed5a6085300014047df": "agg_uniq_2", "BI_67e4bbec6c15e500012e0778": "out_tenant_id", "BI_67e4edaca60853000140458b": "agg_uniq_1", "BI_67e4bbec6c15e500012e07b1": "action_date", "BI_67e4bbec6c15e500012e0762": "data_auth_code", "BI_67e4bbec6c15e500012e079c": "dim_int_1", "BI_67e4bbec6c15e500012e07a8": "out_data_auth_code"}, "statRuleList": [{"actionDateConfigString": "event_data_click_1.event_date", "aggConfigStringList": ["event_data_click_1.ei:uniq:agg_uniq_1"], "customerDimList": [], "dimConfigStringList": ["event_data_click_1:data_auth_code:data_auth_code:_String:text", "dim_enterprise_info_1:crm_activated:dim_int_1:_int:number", "event_data_click_1:out_tenant_id:out_tenant_id:_String:text", "event_data_click_1:out_data_auth_code:out_data_auth_code:_String:text"], "fieldId": "BI_67e4edaca60853000140458b", "preWhereConfigList": [{"filterType": 0, "whereExpr": "event_data_click_1.tenant_id='71570'"}], "rootNodeTable": {"alias": "event_data_click_1", "arrayJoinColumn": {}, "columnList": [{"enableMultiLang": false, "fieldType": "text", "isSingle": 1, "name": "data_auth_code", "type": "_String"}, {"enableMultiLang": false, "fieldType": "text", "isSingle": 1, "name": "out_tenant_id", "type": "_String"}, {"enableMultiLang": false, "fieldType": "text", "isSingle": 1, "name": "out_data_auth_code", "type": "_String"}, {"enableMultiLang": false, "fieldType": "date", "isSingle": 1, "name": "event_date", "type": "_int"}, {"enableMultiLang": false, "isSingle": 1, "name": "ei", "type": "_String"}], "filterColumns": ["tenant_id", "bi_sys_flag", "ei", "data_auth_code", "out_data_auth_code", "event_date", "out_tenant_id", "bi_sys_batch_id"], "joinSet": [{"joinType": "LEFT_JOIN", "onCondition": {"equalPairs": ["${lt}.ei=${rt}.id", "${lt}.tenant_id = ${rt}.tenant_id"]}, "table": {"alias": "dim_enterprise_info_1", "arrayJoinColumn": {}, "columnList": [{"enableMultiLang": false, "fieldType": "number", "isSingle": 1, "name": "crm_activated", "type": "_int"}], "filterColumns": ["tenant_id", "id", "bi_sys_flag", "bi_sys_batch_id", "crm_activated"], "joinSet": [], "lookupColumn": "ei", "name": "dim_enterprise_info", "objectDescribeApiName": "dim_enterprise_info", "objectIdTable": false, "pgSubWheres": [], "selectInvalid": false, "subWheres": [], "tableType": "table"}}], "name": "event_data_click", "objectDescribeApiName": "event_data_click", "objectIdTable": true, "pgSubWheres": [], "selectInvalid": false, "subWheres": [], "tableType": "table"}, "status": 0, "whereConfigsList": []}, {"actionDateConfigString": "event_data_click_1.event_date", "aggConfigStringList": ["event_data_click_1.ei_userid:uniq:agg_uniq_2"], "customerDimList": [], "dimConfigStringList": ["event_data_click_1:data_auth_code:data_auth_code:_String:text", "dim_enterprise_info_1:crm_activated:dim_int_1:_int:number", "event_data_click_1:out_tenant_id:out_tenant_id:_String:text", "event_data_click_1:out_data_auth_code:out_data_auth_code:_String:text"], "fieldId": "BI_67e4fed5a6085300014047df", "preWhereConfigList": [{"filterType": 0, "whereExpr": "event_data_click_1.tenant_id='71570'"}], "rootNodeTable": {"alias": "event_data_click_1", "arrayJoinColumn": {}, "columnList": [{"enableMultiLang": false, "fieldType": "text", "isSingle": 1, "name": "data_auth_code", "type": "_String"}, {"enableMultiLang": false, "fieldType": "text", "isSingle": 1, "name": "out_tenant_id", "type": "_String"}, {"enableMultiLang": false, "fieldType": "text", "isSingle": 1, "name": "out_data_auth_code", "type": "_String"}, {"enableMultiLang": false, "fieldType": "date", "isSingle": 1, "name": "event_date", "type": "_int"}, {"enableMultiLang": false, "isSingle": 1, "name": "ei_userid", "type": "_String"}], "filterColumns": ["tenant_id", "bi_sys_flag", "ei", "data_auth_code", "out_data_auth_code", "event_date", "ei_userid", "out_tenant_id", "bi_sys_batch_id"], "joinSet": [{"joinType": "LEFT_JOIN", "onCondition": {"equalPairs": ["${lt}.ei=${rt}.id", "${lt}.tenant_id = ${rt}.tenant_id"]}, "table": {"alias": "dim_enterprise_info_1", "arrayJoinColumn": {}, "columnList": [{"enableMultiLang": false, "fieldType": "number", "isSingle": 1, "name": "crm_activated", "type": "_int"}], "filterColumns": ["tenant_id", "id", "bi_sys_flag", "bi_sys_batch_id", "crm_activated"], "joinSet": [], "lookupColumn": "ei", "name": "dim_enterprise_info", "objectDescribeApiName": "dim_enterprise_info", "objectIdTable": false, "pgSubWheres": [], "selectInvalid": false, "subWheres": [], "tableType": "table"}}], "name": "event_data_click", "objectDescribeApiName": "event_data_click", "objectIdTable": true, "pgSubWheres": [], "selectInvalid": false, "subWheres": [], "tableType": "table"}, "status": 0, "whereConfigsList": []}, {"actionDateConfigString": "event_data_click_1.event_date", "aggConfigStringList": ["event_data_click_1.pv:sum:agg_sum_1"], "customerDimList": [], "dimConfigStringList": ["event_data_click_1:data_auth_code:data_auth_code:_String:text", "dim_enterprise_info_1:crm_activated:dim_int_1:_int:number", "event_data_click_1:out_tenant_id:out_tenant_id:_String:text", "event_data_click_1:out_data_auth_code:out_data_auth_code:_String:text"], "fieldId": "BI_67e4fef4a6085300014047e6", "preWhereConfigList": [{"filterType": 0, "whereExpr": "event_data_click_1.tenant_id='71570'"}], "rootNodeTable": {"alias": "event_data_click_1", "arrayJoinColumn": {}, "columnList": [{"enableMultiLang": false, "fieldType": "text", "isSingle": 1, "name": "data_auth_code", "type": "_String"}, {"enableMultiLang": false, "fieldType": "text", "isSingle": 1, "name": "out_tenant_id", "type": "_String"}, {"enableMultiLang": false, "fieldType": "text", "isSingle": 1, "name": "out_data_auth_code", "type": "_String"}, {"enableMultiLang": false, "fieldType": "date", "isSingle": 1, "name": "event_date", "type": "_int"}, {"enableMultiLang": false, "isSingle": 1, "name": "pv", "type": "_Decimal"}], "filterColumns": ["tenant_id", "bi_sys_flag", "ei", "data_auth_code", "out_data_auth_code", "pv", "event_date", "out_tenant_id", "bi_sys_batch_id"], "joinSet": [{"joinType": "LEFT_JOIN", "onCondition": {"equalPairs": ["${lt}.ei=${rt}.id", "${lt}.tenant_id = ${rt}.tenant_id"]}, "table": {"alias": "dim_enterprise_info_1", "arrayJoinColumn": {}, "columnList": [{"enableMultiLang": false, "fieldType": "number", "isSingle": 1, "name": "crm_activated", "type": "_int"}], "filterColumns": ["tenant_id", "id", "bi_sys_flag", "bi_sys_batch_id", "crm_activated"], "joinSet": [], "lookupColumn": "ei", "name": "dim_enterprise_info", "objectDescribeApiName": "dim_enterprise_info", "objectIdTable": false, "pgSubWheres": [], "selectInvalid": false, "subWheres": [], "tableType": "table"}}], "name": "event_data_click", "objectDescribeApiName": "event_data_click", "objectIdTable": true, "pgSubWheres": [], "selectInvalid": false, "subWheres": [], "tableType": "table"}, "status": 0, "whereConfigsList": []}], "statViewUniqueKey": "64c3f1a603d21f44402676785f4c4548", "status": 2, "tenantId": "71570", "timezone": "Asia/Shanghai", "version": 4, "viewId": "BI_67ebc80cc40c6300018c829b", "withActionDate": false}