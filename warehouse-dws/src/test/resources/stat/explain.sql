SELECT  any('1')                                                                                                                    AS tenant_id
        ,any('null')                                                                                                                 AS view_id
        ,any(0)                                                                                                                      AS view_version
        ,cityHash64(out_data_auth_code,dim_array_string_1,out_owner,data_auth_code,dim_string_1,owner,out_tenant_id,action_date)     AS hash_code
        ,cityHash64(out_data_auth_code,dim_array_string_1,out_owner,data_auth_code,dim_string_1,owner,out_tenant_id)                 AS hash_code_without_date
        ,coalesce(object_w20vp__c_1.out_data_auth_code,'')                                                                           AS out_data_auth_code
        ,if(notEmpty(biz_account_1.value297),biz_account_1.value297,[''])                                                            AS dim_array_string_1
        ,coalesce(object_w20vp__c_1.out_owner,'')                                                                                    AS out_owner
        ,coalesce(object_w20vp__c_1.data_auth_code,'')                                                                               AS data_auth_code
        ,coalesce(object_w20vp__c_1.value30,'')                                                                                      AS dim_string_1
        ,coalesce(object_w20vp__c_1.owner,'')                                                                                        AS owner
        ,coalesce(object_w20vp__c_1.out_tenant_id,'')                                                                                AS out_tenant_id
        ,coalesce(toString(toYYYYMMDD(fromUnixTimestamp64Milli(toInt64(object_w20vp__c_1.create_time)),'Asia/Shanghai')),'********') AS action_date
        ,null                                                                                                                        AS agg_count_1
        ,null                                                                                                                        AS agg_count_2
        ,coalesce(COUNT(object_w20vp__c_1.id),0)                                                                                     AS agg_count_3
        ,null                                                                                                                        AS agg_count_4
        ,null                                                                                                                        AS agg_count_5
        ,null                                                                                                                        AS agg_count_6
        ,null                                                                                                                        AS agg_count_7
FROM
    (
    SELECT  tenant_id
        ,owner
        ,value30
        ,bi_sys_flag
        ,out_data_auth_code
        ,create_time
        ,out_tenant_id
        ,value9
        ,bi_sys_batch_id
        ,is_deleted
        ,data_auth_code
        ,out_owner
        ,id
        ,value12
    FROM fsbidb044090001.object_w20vp__c
    WHERE tenant_id = '1'
    AND bi_sys_flag = 1
    ) AS object_w20vp__c_1
    LEFT JOIN
    (
    SELECT  tenant_id
        ,is_deleted
        ,bi_sys_flag
        ,object_describe_api_name
        ,id
        ,bi_sys_batch_id
        ,value297
    FROM fsbidb044090001.biz_account
    WHERE tenant_id = '1'
    AND object_describe_api_name = 'AccountObj'
    AND bi_sys_flag = 1
    ) biz_account_1
ON object_w20vp__c_1.tenant_id = biz_account_1.tenant_id AND object_w20vp__c_1.value12 = biz_account_1.id AND biz_account_1.is_deleted = 0
WHERE object_w20vp__c_1.tenant_id = '1'
  AND object_w20vp__c_1.is_deleted = 0
  AND ((object_w20vp__c_1.value9 IN ('XnKM2aaTy', '7bbOi7lDR', 'j74aSgI46', 'qJm1saBgY', 'c58b7pL83')) )
GROUP BY  out_data_auth_code
        ,dim_array_string_1
        ,out_owner
        ,data_auth_code
        ,dim_string_1
        ,owner
        ,out_tenant_id
        ,action_date;


INSERT INTO fsbidb044090001.agg_data(tenant_id, view_id, view_version, rule_id, batch_num, hash_code, hash_code_without_date,
                                     out_data_auth_code, dim_array_string_1, out_owner, data_auth_code, dim_string_1, owner,
                                     out_tenant_id, action_date, value_slot, agg_count_2)
SELECT  '1'
     ,'8a848bac2a76a02c7f1eac187907cca1'
     ,0
     ,rule_id
     ,batch_num
     ,hash_code
     ,hash_code_without_date
     ,out_data_auth_code
     ,dim_array_string_1
     ,out_owner
     ,data_auth_code
     ,dim_string_1
     ,owner
     ,out_tenant_id
     ,action_date
     ,'agg_count_2'
     ,agg_count_3
FROM fsbidb044090001.agg_data
WHERE tenant_id = '1'
  AND view_id = '939f7c5354d0aa814cd4bced98190e1f'
  AND view_version = 2
  AND rule_id = 'BI_5df775271728550001722be0'
  AND batch_num = 11639
    SETTINGS final = 1, do_not_merge_across_partitions_select_final = 1,
optimize_move_to_prewhere_if_final = 1, join_use_nulls = 1;



SELECT  any('1')                                                                                                                    AS tenant_id
        ,any('null')                                                                                                                 AS view_id
        ,any(0)                                                                                                                      AS view_version
        ,cityHash64(out_data_auth_code,dim_array_string_1,out_owner,data_auth_code,dim_string_1,owner,out_tenant_id,action_date)     AS hash_code
        ,cityHash64(out_data_auth_code,dim_array_string_1,out_owner,data_auth_code,dim_string_1,owner,out_tenant_id)                 AS hash_code_without_date
        ,coalesce(object_w20vp__c_1.out_data_auth_code,'')                                                                           AS out_data_auth_code
        ,if(notEmpty(biz_account_1.value297),biz_account_1.value297,[''])                                                            AS dim_array_string_1
        ,coalesce(object_w20vp__c_1.out_owner,'')                                                                                    AS out_owner
        ,coalesce(object_w20vp__c_1.data_auth_code,'')                                                                               AS data_auth_code
        ,coalesce(object_w20vp__c_1.value30,'')                                                                                      AS dim_string_1
        ,coalesce(object_w20vp__c_1.owner,'')                                                                                        AS owner
        ,coalesce(object_w20vp__c_1.out_tenant_id,'')                                                                                AS out_tenant_id
        ,coalesce(toString(toYYYYMMDD(fromUnixTimestamp64Milli(toInt64(object_w20vp__c_1.create_time)),'Asia/Shanghai')),'********') AS action_date
        ,null                                                                                                                        AS agg_count_1
        ,coalesce(COUNT(object_w20vp__c_1.id),0)                                                                                     AS agg_count_2
        ,null                                                                                                                        AS agg_count_3
        ,null                                                                                                                        AS agg_count_4
        ,null                                                                                                                        AS agg_count_5
        ,null                                                                                                                        AS agg_count_6
FROM
    (
    SELECT  tenant_id
        ,owner
        ,value30
        ,bi_sys_flag
        ,out_data_auth_code
        ,create_time
        ,out_tenant_id
        ,value9
        ,bi_sys_batch_id
        ,is_deleted
        ,data_auth_code
        ,out_owner
        ,id
        ,value12
    FROM fsbidb044090001.object_w20vp__c
    WHERE tenant_id = '1'
    AND bi_sys_flag = 1
    ) AS object_w20vp__c_1
    LEFT JOIN
    (
    SELECT  tenant_id
        ,is_deleted
        ,bi_sys_flag
        ,object_describe_api_name
        ,id
        ,bi_sys_batch_id
        ,value297
    FROM fsbidb044090001.biz_account
    WHERE tenant_id = '1'
    AND object_describe_api_name = 'AccountObj'
    AND bi_sys_flag = 1
    ) biz_account_1
ON object_w20vp__c_1.tenant_id = biz_account_1.tenant_id AND object_w20vp__c_1.value12 = biz_account_1.id AND biz_account_1.is_deleted = 0
WHERE object_w20vp__c_1.tenant_id = '1'
  AND object_w20vp__c_1.is_deleted = 0
  AND ((object_w20vp__c_1.value9 IN ('XnKM2aaTy', '7bbOi7lDR', 'j74aSgI46', 'qJm1saBgY', 'c58b7pL83')) )
GROUP BY  out_data_auth_code
        ,dim_array_string_1
        ,out_owner
        ,data_auth_code
        ,dim_string_1
        ,owner
        ,out_tenant_id
        ,action_date;