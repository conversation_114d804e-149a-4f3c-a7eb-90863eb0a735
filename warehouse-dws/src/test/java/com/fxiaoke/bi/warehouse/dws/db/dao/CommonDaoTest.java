package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.BiAggLogCHDao;
import com.fxiaoke.bi.warehouse.core.db.entity.BiAggLogDO;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBAggInfoDO;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.model.DBUpdatedEvent;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author:jief
 * @Date:2024/8/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class CommonDaoTest {
  @Resource
  private DBUpdateEventDao dbUpdateEventDao;
  @Resource
  private DBAggInfoDAO dbAggInfoDAO;
  @Resource
  private BiAggLogCHDao biAggLogCHDao;

  @Test
  public void testBatchInsert(){
    String jdbcURL="***************************************************";
    List<BiAggLogDO> biAggLogDOS = Lists.newArrayList();
    BiAggLogDO a1= BiAggLogDO.builder().tenantId("85145").viewId("BI_6710c931a5acc70001925c58").viewVersion(0).batchNum(100L).fieldId("BI_6705f84d1060650001553a3a").cost(10000).build();
    BiAggLogDO a2= BiAggLogDO.builder().tenantId("85145").viewId("BI_6710c931a5acc70001925c58").viewVersion(0).batchNum(101L).fieldId("BI_6705f84d1060650001553a3a").cost(20000).build();
    BiAggLogDO a3= BiAggLogDO.builder().tenantId("85145").viewId("BI_6710c931a5acc70001925c58").viewVersion(0).batchNum(102L).fieldId("BI_6705f84d1060650001553a3a").cost(40000).build();
    BiAggLogDO a4= BiAggLogDO.builder().tenantId("85145").viewId("BI_6710c931a5acc70001925c58").viewVersion(0).batchNum(103L).fieldId("BI_6705f84d1060650001553a3a").cost(40000).build();
    biAggLogDOS.add(a1);
    biAggLogDOS.add(a2);
    biAggLogDOS.add(a3);
    biAggLogDOS.add(a4);
    biAggLogCHDao.batchInsertAggLog(jdbcURL, biAggLogDOS);
  }

  @Test
  public void testQueryOrInitDBAggInfo(){
    String id="6412b13f5cd44942982c91b7";
    DBSyncInfoDO a = dbUpdateEventDao.findSyncById(id);
    DBAggInfoDO b=dbAggInfoDAO.queryOrInitDBAggInfo(a);
    System.out.println(JSON.toJSONString(b));
  }

  @Test
  public void testCreateDbUpdateEvent(){
    String id="6412b13f5cd44942982c91b7";
    DBSyncInfoDO dbSyncInfoDO = dbUpdateEventDao.findSyncById(id);
    DBUpdatedEvent dbUpdatedEvent= dbUpdateEventDao.createDbUpdateEvent( dbSyncInfoDO,87388L, null,0L);
    System.out.println(JSON.toJSONString(dbUpdatedEvent.getTenantIdObjectDescribeApiNamesMap()));
  }

  @Test
  public void testReturning(){
    String dbSyncId="6412b13f5cd44942982c91b7";
    int status=7;
    int preVersion=7;
   Integer result = dbAggInfoDAO.updateDBAggInfoStatus(dbSyncId,status,preVersion);
    System.out.println(JSON.toJSONString(result));
  }
}
