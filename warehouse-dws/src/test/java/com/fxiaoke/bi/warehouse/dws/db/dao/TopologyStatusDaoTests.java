package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.db.entity.StatViewStatusEnum;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtTopologyStatusDO;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author:jief
 * @Date:2024/1/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TopologyStatusDaoTests {
  @Resource
  private TopologyStatusDao topologyStatusDao;

  @Test
  public void testBatchUpdate() {
    topologyStatusDao.batchUpdateStatViewStatus("85145", Lists.newArrayList("BI_654b0055d8b366000168a409"),
      StatViewStatusEnum.used.getStatus());
  }
@Test
  public void  findStopStatView(){
  String tenantId="85145";
  String[] viewIds=new String[]{"BI_6597a34ce4a9f30001b61eed"};
  List<BIMtTopologyStatusDO> result=topologyStatusDao.findStopStatView(tenantId,viewIds);
  System.out.println(JSON.toJSONString(result));
}
@Test
public void testIsActive(){
  System.out.println(topologyStatusDao.isActive("85145","BI_6597a34ce4a9f30001b61eed"));
}
}
