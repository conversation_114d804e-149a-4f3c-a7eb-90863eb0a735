package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyTableMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Author:jief
 * @Date:2023/11/2
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TopologyTableDaoTests {

  @Resource
 private TopologyTableDao topologyTableDao;

  @Resource
  private TopologyTableMapper topologyTableMapper;

  @Test
  public void testUpdateTopologyTable(){
  int result=  topologyTableDao.updateTopologyStatusByViewId("85145","BI_58acfc2537aa1badf31d169a",null,0,9999L,null,
    false, null);
    System.out.println(result);
  }

  @Test
  public void testUpdateTopologyTableStatusByUniqueKey(){
    int result=topologyTableDao.updateTopologyStatusByUniqueKey("85145",null,"014859300e3da6fb8b3ea91af66d311a",65610L,new Date().getTime(),
      false, null,true);
    System.out.println("result============"+result);
  }

  @Test
  public void testCreateMonitor(){

  }

  @Test
  public void testUpdateEffectApiNames(){
    String tenantId="85145";
    List<TopologyTableDO> topologyTableDOList = topologyTableMapper.setTenantId(tenantId)
                                                                   .findNeedCalculateList(tenantId, new int[]{0,1,2});
    System.out.println(topologyTableDOList.size());
  }
  @Test
  public void testFindAggTimeAndEffectApiBySourceId(){
    JSONObject jsonObject= topologyTableDao.findAggTimeAndEffectApiBySourceId("85145","BI_58acfc2537aa1badf31d169a");
    String effectApi = jsonObject.getString("agg_effect_api_names");
    Long aggBatchNum = (Long)jsonObject.getOrDefault("batch_num",0L);
    System.out.println("effectApi:"+effectApi);
    System.out.println("aggBatchNum:"+aggBatchNum);
  }
  @Test
  public void testDeleteTopologyMergeByEI(){
    topologyTableDao.deleteTopologyMergeByEI("85529");
  }

}
