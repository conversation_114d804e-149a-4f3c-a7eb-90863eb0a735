package com.fxiaoke.bi.warehouse.dws.compute.service;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.mq.message.DBUpdateMessage;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.ObjectConfigManager;
import com.fxiaoke.bi.warehouse.dws.agg.bean.AggCalResult;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBUpdateEventDao;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyTableDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.service.ClickHouseService;
import com.fxiaoke.bi.warehouse.dws.service.DWSComputeService;
import com.fxiaoke.bi.warehouse.dws.service.TopologyTableService;
import com.fxiaoke.bi.warehouse.dws.transform.model.GoalChangeType;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.StopWatch;
import com.github.autoconf.helper.ConfigHelper;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.Uninterruptibles;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Author:jief
 * @Date:2023/8/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class DWSComputeServiceTest {

  @Resource
  private DWSComputeService dwsComputeService;
  @Resource
  private DBUpdateEventDao dbUpdateEventDao;
  @Resource
  private TopologyTableDao topologyTableDao;
  @Resource
  private TopologyTableService topologyTableService;
  @Resource
  private ClickHouseService clickHouseService;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;

  @Test
  public void testRedisHset(){
    String key="agg:cal:v1:123";
    jedisCmd.hset(key,"fieldId1","0");
    jedisCmd.expire(key,500L);
    System.out.println(jedisCmd.hget(key,"fieldId1"));
    jedisCmd.hset(key,"fieldId1","1");
    jedisCmd.expire(key,500L);
    System.out.println(jedisCmd.hget(key,"fieldId1"));
    jedisCmd.hdel(key,"fieldId1");
  }

  @Test
  public void testRedisHdel(){
    String key="agg:cal:v1:123";
    jedisCmd.hset(key,"f1","0");
    jedisCmd.hset(key,"f2","1");
    jedisCmd.hset(key,"f3","2");
    jedisCmd.expire(key,500L);
    List<String> a = Lists.newArrayList("f1","f2","f3");
//    System.out.println(JSON.toJSON(jedisCmd.hmget(key,a.toArray(String[]::new))));
//    jedisCmd.hdel(key,a.toArray(String[]::new));
    System.out.println(JSON.toJSON(jedisCmd.hgetAll(key)));
    System.out.println(jedisCmd.hget(key,"f1"));
  }

  @Test
  public void testRedisExpire(){
    String key="agg:cal:v1:123";
    jedisCmd.hset(key,"f1","0");
    jedisCmd.expire(key,3L);
    jedisCmd.hset(key,"f1","1");
    Uninterruptibles.sleepUninterruptibly(10, TimeUnit.SECONDS);
//    List<String> a = Lists.newArrayList("f1","f2","f3");
//    System.out.println(JSON.toJSON(jedisCmd.hmget(key,a.toArray(String[]::new))));
//    jedisCmd.hdel(key,a.toArray(String[]::new));
    Assert.isNull(jedisCmd.hget(key,"f1"));
//    System.out.println();
  }

  @Test
  public void testDataUpdated() {
    String json = """
      {
          "batchNum": 62363,
          "chDB": "jdbc:clickhouse://************:9090/fsbidb044003002",
          "id": "64ee0f5dbfbe303944dd65b6",
          "pgDB": "***********************************************",
          "schema": "sch_82958"
        }
      """;
    DBUpdateMessage dbUpdateMessage = JSON.parseObject(json, DBUpdateMessage.class);
    dwsComputeService.dbDataUpdated(dbUpdateMessage);
  }
  @Test
  public void testTopologyStatus() {
//    System.out.println(RestProxySwitch.useHttpSupport());
    System.out.println(ConfigHelper.getProcessInfo().getProfile());
  }

  @Test
  public void testSkipView(){
   boolean ok = GrayManager.isAllowByRule("skip_stat_view_unique_key","1^4576945affbe163360541322965feee0");
    System.out.println(ok);
  }

  //生成增量计算的sql
  @Test
  public void testTopologyMonitor() {
    String tenantId = "85145";
    String viewId = "6670e070d2dbfe0001f5df25";
    TopologyTable statView = topologyTableDao.findByTenantIdAndSourceId(tenantId, viewId);
    if (null == statView) {
      throw new RuntimeException("statView is null");
    }
    topologyTableService.addDownStreamRuleWhereSql(statView);
    statView.setStatus(1);
    //    System.out.println(JSON.toJSONString(statView.getStatRuleList().get(0).getUniqDimConfig()));
    //    System.out.println(statView.toViewSQL(-1,-1));
    boolean isDSCalculate = topologyTableService.downStreamAggIsCalculate(statView);
    List<TopologyTableAggRuleMonitor> statRuleMonitorList = statView.toStatViewMonitor(null, isDSCalculate,
            null, GoalChangeType.GOAL_ALL_DATA_INC, topologyTableService.getTableKeys(statView),false).getStatRuleMonitorList();
    for (TopologyTableAggRuleMonitor topologyTableAggRuleMonitor : statRuleMonitorList) {
//      clickHouseService.executeSQL(tenantId, topologyTableAggRuleMonitor.computeSQL(313L).get(0), 600000000);
//      System.out.println(topologyTableAggRuleMonitor.computeSQL(313L).get(0));
      topologyTableAggRuleMonitor.computeSQL(313L).forEach(System.out::println);
    }
  }


  @Test
  public void testUpdateStatusOrVersionWithUnique()throws Exception{
    String tenantId = "85145";
    String viewId = "BI_64f9c216e8a01a00015659aa";
    boolean standalone = false;
    TopologyTable statView = topologyTableDao.findByTenantIdAndSourceId(tenantId, viewId);
    if (null == statView) {
      throw new RuntimeException("statView is null");
    }
    statView.setStatus(0);
    //    System.out.println(JSON.toJSONString(statView.getStatRuleList().get(0).getUniqDimConfig()));
    //    System.out.println(statView.toViewSQL(-1,-1));
//    boolean isDSCalculate = topologyTableService.downStreamAggIsCalculate(statView);
    TopologyTableMonitor statViewMonitor = statView.toStatViewMonitor(null, false,null, GoalChangeType.DATA_ALL, topologyTableService.getTableKeys(statView),standalone);
    dwsComputeService.updateStatusOrVersionWithUnique(tenantId, statViewMonitor,
     10L,
    new Date().getTime());
  }

  @Test
  public void testDeleteBeforeData() {
    String chDBUrl = "jdbc:clickhouse://************:9090/fsbidb044003001";
    String tableName = "sale_action_stage";
    long batchNum = 36666L;
    StopWatch stopWatch = StopWatch.createStarted("testDeleteBeforeData");
    String eiName = "ei";
    Set<String> tenantIds = Sets.newHashSet("85145");
    dwsComputeService.deleteBeforeData(chDBUrl, tableName, batchNum, eiName, tenantIds);
  }

  @Test
  public void testComputeData() {
    DBUpdatedEvent dbUpdatedEvent = new DBUpdatedEvent();
    dbUpdatedEvent.setChJdbcUrl("jdbc:clickhouse://************:9090/fsbidb044003001");
    dbUpdatedEvent.setPgJdbcUrl("*****************************************");
    dbUpdatedEvent.setBatchNum(87851);
    dbUpdatedEvent.setStatus(0);
    StopWatch stopWatch = StopWatch.createStarted("testDeleteBeforeData");
    Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap = Maps.newHashMap();
    Set<String> apiNames = Sets.newHashSet();
    //apiNames.add("object_KXtal__c");
    apiNames.add("biz_account");
    tenantIdObjectDescribeApiNamesMap.put("85145", apiNames);
    dbUpdatedEvent.setTenantIdObjectDescribeApiNamesMap(tenantIdObjectDescribeApiNamesMap);
    dwsComputeService.computeData(dbUpdatedEvent, stopWatch);
  }

  @Test
  public void testCopyPartition(){
    DBSyncInfoDO dbSyncInfoDO = dbUpdateEventDao.findSyncById("6412b13f5cd44942982c91b7");
    DBUpdatedEvent result = new DBUpdatedEvent();
    result.setId(dbSyncInfoDO.getId());
    result.setStatus(SyncStatusEnum.SYNC_ED.getStatus());
    result.setAggInfoVersion(1);
    result.setChJdbcUrl(dbSyncInfoDO.getChDB());
    result.setPgJdbcUrl(dbSyncInfoDO.getPgDB());
    result.setPgSchema(dbSyncInfoDO.getPgSchema());
    result.setLastModifiedTime(new Date().getTime());
    result.setBatchNum(1000L);
    result.setAllowIncPartition(dbSyncInfoDO.getAllowIncPartition());
    result.setAllowCalPartition(1);
    result.setBatchNums(new Long[]{999L,1000L});
    result.setLastSyncEis(dbSyncInfoDO.getLastSyncEis());
    result.setSyncFlows("1,2");
    Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap = Maps.newHashMap();
    tenantIdObjectDescribeApiNamesMap.put("85145",Sets.newHashSet("biz_account"));
    result.setTenantIdObjectDescribeApiNamesMap(tenantIdObjectDescribeApiNamesMap);
    Map<String, Set<String>> tableTenantIdSetMap = Maps.newHashMap();
    tableTenantIdSetMap.put("biz_account",Sets.newHashSet("85145"));
    result.setTableTenantIdSetMap(tableTenantIdSetMap);
    List<String> tableNames= Lists.newArrayList("biz_account","object_data","org_employee_user");
    long[] batchNums = new long[]{1L,2L,3L,4L,5L};
    StopWatch stopWatch= StopWatch.createStarted("start");
    AggCalResult aggCalResult =  dwsComputeService.prepare(result,stopWatch);
    List<AggCalResult.CalPrepare> calPrepares = aggCalResult.getCalPrepares();
    calPrepares.forEach(calPrepare -> {
      System.out.println(JSON.toJSON(calPrepare));
      System.out.println("====================================");
    });
  }

  @Test
  public void testDbUpdateEventPlus() {
    DBSyncInfoDO dbSyncInfoDO = dbUpdateEventDao.findSyncById("64ee0f5dbfbe303944dd65b6");
    DBUpdatedEvent event = dbUpdateEventDao.createDbUpdateEventPlus(dbSyncInfoDO, 12089L, null, 0L);
    // StopWatch stopWatch = StopWatch.createStarted("dbUpdateEvent:test");
    //    dwsComputeService.before(event,stopWatch);
    System.out.println(JSON.toJSONString(event));
  }

  @Test
  public void testJsonParse() {
    String biPairStr = JSON.toJSONString(Map.of("biz_account", Pair.build(1742010876230000L, 1742011489298000L)));
    Map<String, BIPair<Long, Long>> biPairMap = JSON.parseObject(biPairStr, new TypeReference<>() {});
    System.out.println("(biPairMap) = " + JSON.toJSONString(biPairMap));
  }

  @Test
  public void testSelectChangeGoals() {
    DBUpdatedEvent dbUpdatedEvent = new DBUpdatedEvent();
    dbUpdatedEvent.setChJdbcUrl("jdbc:clickhouse://************:9090/fsbidb044003001");
    dbUpdatedEvent.setPgJdbcUrl("*****************************************");
    dbUpdatedEvent.setId("6412b13f5cd44942982c91b7");
    dbUpdatedEvent.setBatchNum(87632);
    dbUpdatedEvent.setStatus(0);
    Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap = Maps.newHashMap();
    Set<String> apiNames = Sets.newHashSet();
    apiNames.add("goal_value_obj");
    apiNames.add("goal_value_obj_week");
    apiNames.add("goal_value_obj_quarter");
    apiNames.add("goal_value_obj_year");
    apiNames.add("goal_value_obj_xxx");
    Set<String> apiNames2 = Sets.newHashSet();
    apiNames2.add("goal_value_obj");
    apiNames2.add("goal_value_obj_week");
    apiNames2.add("goal_value_obj_xxxxxx");
    tenantIdObjectDescribeApiNamesMap.put("85145", apiNames);
    tenantIdObjectDescribeApiNamesMap.put("85529", apiNames2);
    dbUpdatedEvent.setTenantIdObjectDescribeApiNamesMap(tenantIdObjectDescribeApiNamesMap);
    dwsComputeService.selectChangeGoals(dbUpdatedEvent);
  }

  @Test
  public void testBefore(){
    DBUpdatedEvent result = new DBUpdatedEvent();
    result.setChJdbcUrl("jdbc:clickhouse://************:9090/fsbidb044003002");
    result.setPgJdbcUrl("***********************************************");
    result.setId("64ee0f5dbfbe303944dd65b6");
    result.setBatchNum(87632);
    result.setStatus(0);
    result.setAggInfoVersion(0);
    result.setPgSchema("sch_82958");
    result.setLastModifiedTime(new Date().getTime());
    result.setBatchNum(54192L);
    result.setAllowIncPartition(1);
    result.setAllowCalPartition(0);
    result.setBatchNums(new Long[]{54192L});
    result.setLastSyncEis("82958");
    result.setSyncFlows("677ccf022a17ab452589f733");
    Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap = Maps.newHashMap();
    result.setTenantIdObjectDescribeApiNamesMap(tenantIdObjectDescribeApiNamesMap);
    Map<String, Set<String>> tableTenantIdSetMap = Maps.newHashMap();
    result.setTableTenantIdSetMap(tableTenantIdSetMap);
    String tableApiNameEiMaps = "{\"org_employee_user_downstream\":{\"org_employee_user\":[\"82958\"]},\"biz_account_downstream\":{\"biz_account\":[\"82958\"]},\"biz_user_api_name_operation\":{\"biz_user_api_name_operation\":[\"82958\"]},\"agg_downstream_data\":{\"agg_downstream_data\":[\"82958\"]}}";
    if(StringUtils.isNotBlank(tableApiNameEiMaps) && !Objects.equals("{}",tableApiNameEiMaps)){
      Map<String, Map<String, Set<String>>> combineSyncFlowApiEiMaps = JSON.parseObject(tableApiNameEiMaps, new TypeReference<>() {});
      if (combineSyncFlowApiEiMaps != null) {
        combineSyncFlowApiEiMaps.forEach((tableName, objectDescribeApiNameEiMap) -> this.parseApiNameEIMaps(objectDescribeApiNameEiMap, tableName, tenantIdObjectDescribeApiNamesMap, tableTenantIdSetMap));
        //获取所有需要全量计算图的租户id merge 到增量计算的受影响租户中
        List<String> tenantIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tenantIds)) {
          tenantIds.forEach(tenantId -> tenantIdObjectDescribeApiNamesMap.computeIfAbsent(tenantId, key -> Sets.newHashSet()));
        }
      }
    }
    StopWatch stopWatch = StopWatch.createStarted("dbUpdateEvent:test");
    stopWatch.start("before");
    dwsComputeService.before(result, stopWatch);
    stopWatch.stop("before");
  }
  @Test
  public void testCompute(){
    DBUpdatedEvent result = new DBUpdatedEvent();
    result.setChJdbcUrl("jdbc:clickhouse://************:9090/fsbidb044003002");
    result.setPgJdbcUrl("***********************************************");
    result.setId("64ee0f5dbfbe303944dd65b6");
    result.setBatchNum(87632);
    result.setStatus(0);
    result.setAggInfoVersion(0);
    result.setPgSchema("sch_82958");
    result.setLastModifiedTime(new Date().getTime());
    result.setBatchNum(54192L);
    result.setAllowIncPartition(1);
    result.setAllowCalPartition(0);
    result.setBatchNums(new Long[]{54192L});
    result.setLastSyncEis("82958");
    result.setSyncFlows("677ccf022a17ab452589f733");
    Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap = Maps.newHashMap();
    result.setTenantIdObjectDescribeApiNamesMap(tenantIdObjectDescribeApiNamesMap);
    Map<String, Set<String>> tableTenantIdSetMap = Maps.newHashMap();
    result.setTableTenantIdSetMap(tableTenantIdSetMap);
    String tableApiNameEiMaps = "{\"org_employee_user_downstream\":{\"org_employee_user\":[\"82958\"]},\"biz_account_downstream\":{\"biz_account\":[\"82958\"]},\"biz_user_api_name_operation\":{\"biz_user_api_name_operation\":[\"82958\"]},\"agg_downstream_data\":{\"agg_downstream_data\":[\"82958\"]}}";
    if(StringUtils.isNotBlank(tableApiNameEiMaps) && !Objects.equals("{}",tableApiNameEiMaps)){
      Map<String, Map<String, Set<String>>> combineSyncFlowApiEiMaps = JSON.parseObject(tableApiNameEiMaps, new TypeReference<>() {});
      if (combineSyncFlowApiEiMaps != null) {
        combineSyncFlowApiEiMaps.forEach((tableName, objectDescribeApiNameEiMap) -> this.parseApiNameEIMaps(objectDescribeApiNameEiMap, tableName, tenantIdObjectDescribeApiNamesMap, tableTenantIdSetMap));
        //获取所有需要全量计算图的租户id merge 到增量计算的受影响租户中
        List<String> tenantIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tenantIds)) {
          tenantIds.forEach(tenantId -> tenantIdObjectDescribeApiNamesMap.computeIfAbsent(tenantId, key -> Sets.newHashSet()));
        }
      }
    }
    System.out.println(JSON.toJSON(tenantIdObjectDescribeApiNamesMap));
    StopWatch stopWatch = StopWatch.createStarted("dbUpdateEvent:test");
    stopWatch.start("before");
    dwsComputeService.compute(result, stopWatch);
    stopWatch.stop();
  }

  private void parseApiNameEIMaps(Map<String, Set<String>> objectDescribeApiNameEiMap,
                                  String tableName,
                                  Map<String, Set<String>> tenantIdObjectDescribeApiNamesMap,
                                  Map<String, Set<String>> tableTenantIdSetMap) {
    Set<String> tenantIdSet = Sets.newHashSet();
    objectDescribeApiNameEiMap.forEach((apiName, apiTenantIdSet) -> {
      tenantIdSet.addAll(apiTenantIdSet);
      apiTenantIdSet.forEach(tenantId -> {
        String objectApiName = tableName;
        if (StringUtils.equalsAny(tableName, "biz_account", "object_data", "object_data_downstream")) {
          objectApiName = apiName;
          if (objectApiName.endsWith("_udef")) {
            tenantIdObjectDescribeApiNamesMap.computeIfAbsent(tenantId, k -> Sets.newHashSet())
                                             .add(ObjectConfigManager.getPreObjName(objectApiName));
          }
        }
        tenantIdObjectDescribeApiNamesMap.computeIfAbsent(tenantId, k -> Sets.newHashSet()).add(objectApiName);
      });
    });
    tableTenantIdSetMap.put(tableName, tenantIdSet);
  }
}
