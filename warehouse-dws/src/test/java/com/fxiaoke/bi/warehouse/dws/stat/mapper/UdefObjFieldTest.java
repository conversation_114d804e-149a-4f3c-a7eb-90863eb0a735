package com.fxiaoke.bi.warehouse.dws.stat.mapper;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.entity.UdfObjFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjFieldMapper;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Author:jief
 * @Date:2023/7/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class UdefObjFieldTest {
  @Resource
  private UdfObjFieldMapper udfObjFieldMapper;
  @Resource
  private MappingService mappingService;
  @Test
  public void testMapperService(){
    String xApiName=mappingService.getApiXName("biz_sales_order");
    System.out.println(xApiName);
  }
  @Test
  public void testUdfObjFieldType(){
    String dbObjName="biz_sales_order_product";
    String xApiName=mappingService.getApiXName(dbObjName);
    String ei="71570";
    List<UdfObjFieldDO> udfObjFieldDOS = udfObjFieldMapper.setTenantId("71570").findUdfDbFieldInfoByType(ei,dbObjName,xApiName,"master_detail");
    System.out.println("hahahah"+JSON.toJSONString(udfObjFieldDOS));
  }
  @Test
  public void testUdfObjField(){
    String dbObjName="biz_account";
    String xApiName=mappingService.getApiXName(dbObjName);
    String ei="71570";
    List<UdfObjFieldDO> a=udfObjFieldMapper.setTenantId("71570").batchQueryFieldByObjNameAndDbFieldName(Integer.parseInt(ei), dbObjName,
     xApiName, new String[]{"account_no","account_path"});
    for (UdfObjFieldDO udfObjFieldDO : a) {
      System.out.println(udfObjFieldDO.getIsUnique());
      System.out.println(JSON.toJSONString(udfObjFieldDO));
    }
  }
}
