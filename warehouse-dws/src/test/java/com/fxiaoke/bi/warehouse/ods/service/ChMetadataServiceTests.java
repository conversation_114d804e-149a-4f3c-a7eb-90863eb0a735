package com.fxiaoke.bi.warehouse.ods.service;

import com.alibaba.fastjson2.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.ods.args.CHPublicCreatorArg;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/3/3
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class ChMetadataServiceTests {
  @Resource
  private CHMetadataService chMetadataService;

  @Test
  public void testMovePart(){
    List<CHPublicCreatorArg> chPublicCreatorArgs = Lists.newArrayList();
    CHPublicCreatorArg chPublicCreatorArg= new CHPublicCreatorArg();
    chPublicCreatorArg.setChDBName("***************************************************");
    chPublicCreatorArg.setTables(Lists.newArrayList("20250111","20250112"));
    chPublicCreatorArgs.add(chPublicCreatorArg);
    chMetadataService.repairPartition(chPublicCreatorArgs);
  }

  @Test
  public void testBatchDDLTable() {
    CHPublicCreatorArg chPublicCreatorArg = new CHPublicCreatorArg();
    chPublicCreatorArg.setChDBName("***************************************************");
    chPublicCreatorArg.setTables(Lists.newArrayList("biz_log_erpdatascreen"));
    String op = "truncate";
    chMetadataService.batchDDLTableOnCh(chPublicCreatorArg, op);
  }

  @Test
  public void testCompareAndUpdateTable(){
    String chJdbcUrl="***************************************************";
    String fromTableName="org_employee_user";
    String newTableName="org_employee_user_downstream";
    List<String> result = chMetadataService.compareAndUpdateTable(chJdbcUrl, fromTableName,  newTableName);
    System.out.println(JSON.toJSONString(result));
  }

  @Test
  public void testCreateTable(){
    String chJdbcUrl="***************************************************";
    String fromTableName="new_opportunity";
    String newTableName="new_opportunity_downstream";
    chMetadataService.createFromExistsTable(chJdbcUrl,  fromTableName,  newTableName);
  }
  @Test
  public void  testBatchModifyCustomChTableTTL(){
    System.setProperty("catalina.home","D:\\常用脚本\\clickhouse帮助目录\\公共库脚本");
    List<CHPublicCreatorArg> chPublicCreatorArgs= Lists.newArrayList();
    CHPublicCreatorArg chPublicCreatorArg= new CHPublicCreatorArg();
    chPublicCreatorArg.setChDBName("***************************************************");
    chPublicCreatorArg.setNeedCreate(true);
//    chPublicCreatorArg.setTables(Lists.newArrayList("biz_account","biz_contract"));
    chPublicCreatorArgs.add(chPublicCreatorArg);
    chMetadataService.batchModifyCustomChTableTTL(chPublicCreatorArgs);
  }

  @Test
  public void testCreateQueryApiAndEIsSQL() {
    Optional<ClickhouseTable> clickhouseTableOptional = chMetadataService.loadTable("sch_82958","***************************************************", "biz_account");
    ClickhouseTable clickhouseTable=clickhouseTableOptional.get();
    long fromValue = 1672531200000000L; // Example timestamp for 2023-01-01 00:00:00
    long toValue = 1675123200000000L;   // Example timestamp for 2023-02-01 00:00:00
    long[] batchNums = {1L, 2L, 3L};
    String eiColumnName = "tenant_id";
    String sql = clickhouseTable.createQueryApiAndEIsSQL(fromValue, toValue, batchNums, eiColumnName, Lists.newArrayList("82958"));
    System.out.println(sql);
  }

  @Test
  public void  testQueryPaas2BICHIncInfo(){
    DBSyncInfoBO dbSyncInfo = new DBSyncInfoBO();
    dbSyncInfo.setChDb("***************************************************");
    dbSyncInfo.setPgDb("*****************************************");
    dbSyncInfo.setPgSchema("71570");
    long fromModifiedTime = 1672531200000000L; // Example timestamp for 2023-01-01 00:00:00
    long toModifiedTime = 1747926000000000L;   // Example timestamp for 2023-02-01 00:00:00
    String eiColumnName = "tenant_id";
    Optional<ClickhouseTable> clickhouseTableOP = chMetadataService.loadTable(dbSyncInfo.getPgSchema(), dbSyncInfo.getChDb(), "biz_account");
//    java.util.Map<String, Set<String>> bb = chMetadataService.queryPaas2BICHIncInfo(dbSyncInfo, new long[] {0L}, clickhouseTableOP.get(), fromModifiedTime, toModifiedTime, eiColumnName, Lists.newArrayList("71570"));
    java.util.Map<String, Set<String>> bb = chMetadataService.queryPaas2BICHIncInfo(dbSyncInfo, null, clickhouseTableOP.get(), fromModifiedTime, toModifiedTime, eiColumnName, Lists.newArrayList("71570"));
    System.out.println(JSON.toJSONString(bb));
  }
}
