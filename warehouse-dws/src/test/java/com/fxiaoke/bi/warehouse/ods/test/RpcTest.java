package com.fxiaoke.bi.warehouse.ods.test;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.bean.ApiResult;
import com.fxiaoke.bi.warehouse.common.bean.EnterPriseLevelEnum;
import com.fxiaoke.bi.warehouse.common.bean.InitChDbRouteArg;
import com.fxiaoke.bi.warehouse.common.component.RpcPaasService;

import java.util.regex.Pattern;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
@Slf4j
public class RpcTest {

    @Resource
    private RpcPaasService rpcPaasService;

    @Resource
    private DbRouterClient dbRouterClient;

    @Test
    public void testDbRouteInfo() {
        InitChDbRouteArg initChDbRouteArg = InitChDbRouteArg.builder()
                                                            .dbName("fsbidb044003001")
                                                            .activityLevel(EnterPriseLevelEnum.COMMON__ENTERPRISE.getStatus())
                                                            .podRouter(InitChDbRouteArg.PodRouter.builder()
                                                                                                 .biz("BI")
                                                                                                 .tenantId("91939")
                                                                                                 .dialect("clickhouse")
                                                                                                 .standalone(false)
                                                                                                 .build())
                                                            .build();
        System.out.println(JSON.toJSONString(initChDbRouteArg));
        ApiResult apiResult = rpcPaasService.initChDbRoute(initChDbRouteArg);
        System.out.println(ApiResult.IsSuccess(apiResult));
    }

    @Test
    public void testMatch() {
        String initChDb = StringUtils.EMPTY;
        String chDb = "************************************";
        try {
            String[] parts = chDb.split("/");
            initChDb = String.join("/", java.util.Arrays.copyOfRange(parts, 4, parts.length));
        } catch (Exception e) {
            log.error("get chDb name error,chDb:{}", chDb);
        }
        System.out.println(initChDb);
    }

    @Test
    public void test1() {
        String tenantId = "91939";
        RouterInfo chRouterInfo = dbRouterClient.queryRouterInfo(tenantId, "BI", "fs-bi-stat-calculate", "clickhouse");
        RouterInfo dbRouteInfo = dbRouterClient.queryRouterInfo(tenantId, "BI", "fs-bi-stat", "postgresql");
        System.out.println(chRouterInfo);
        System.out.println(dbRouteInfo);
    }

    @Test
    public void testMatchCustomTable() {
        Pattern customTableRegx = Pattern.compile("^.*__c$");
        boolean matches = customTableRegx.matcher("object_kc76k__c111").matches();
        if (matches) {
            System.out.println("匹配成功");
        } else {
            System.out.println("匹配失败");
        }
    }
}
