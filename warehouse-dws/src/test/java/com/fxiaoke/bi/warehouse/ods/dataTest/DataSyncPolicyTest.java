package com.fxiaoke.bi.warehouse.ods.dataTest;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.api.CRMNotifyService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.DataSourceEnterprise;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BiDataSyncPolicyDo;
import com.fxiaoke.bi.warehouse.ods.integrate.service.AggDownStreamComplexService;
import com.fxiaoke.bi.warehouse.ods.integrate.service.EnterpriseRelationRpcService;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.bean.ValueType;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.fxiaoke.enterpriserelation2.arg.BatchGetEaByOuterTenantIdArg;
import com.fxiaoke.enterpriserelation2.arg.BatchGetShortNamesArg;
import com.fxiaoke.enterpriserelation2.arg.ListTenantGroupDownstreamEnterprisesArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.BatchGetEaByOuterTenantIdResult;
import com.fxiaoke.enterpriserelation2.result.ListTenantGroupDownstreamEnterprisesResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.TenantGroupService;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.model.RemindRecordItem;
import com.fxiaoke.model.crmNotify.AddRemindRecordArg;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.spockframework.runtime.model.INameable;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.DoubleStream;
import java.util.stream.IntStream;

import static com.fxiaoke.constant.CrmCategoryKey.BiReportMessage;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class DataSyncPolicyTest {

    @Resource
    private TenantGroupService tenantGroupService;

    @Resource
    private EnterpriseRelationService enterpriseRelationService;

    @Resource(name = "cRMNotifyService")
    private CRMNotifyService crmNotifyService;

    @Resource
    private BizConfClient bizConfClient;

    @Resource
    private FxiaokeAccountService fxiaokeAccountService;

    @Resource
    private EnterpriseRelationRpcService enterpriseRelationRpcService;

    @Resource
    private AggDownStreamComplexService aggDownStreamComplexService;

    @Resource
    private CHRouterPolicy chRouterPolicy;

    @Test
    public void testBizEnterpriseRelation() {
        DataSourceEnterprise dataSourceEnterprise = new DataSourceEnterprise();
        dataSourceEnterprise.setId("64dde6abdae138000137ac22");
        dataSourceEnterprise.setType("tg");
        List<DataSourceEnterprise> dataSourceEnterpriseList = Lists.newArrayList(dataSourceEnterprise);
        enterpriseRelationRpcService.getSourceTenantToObject(dataSourceEnterpriseList, "82958");
    }

    @Test
    public void testGetEisByTenantIds() {
        HeaderObj headerObj = HeaderObj.newInstance(82958);
        BatchGetEaByOuterTenantIdArg batchGetEaByOuterTenantIdArg = new BatchGetEaByOuterTenantIdArg();
        batchGetEaByOuterTenantIdArg.setOuterTenantIds(Lists.newArrayList(300112627L, 300112202L));
        RestResult<BatchGetEaByOuterTenantIdResult> batchGetEaByOuterTenantIdResultRestResult = fxiaokeAccountService.batchGetEaByOuterTenantId(headerObj, batchGetEaByOuterTenantIdArg);
        System.out.println(batchGetEaByOuterTenantIdResultRestResult);
    }

    /**
     * 根据应用的appId查询应用的状态
     */
    @Test
    public void testAppId() {
        boolean status = enterpriseRelationRpcService.checkBiDataSyncAppStatus("82958");
        System.out.println(status);
    }

    /**
     * 获取BI数据同步应用管理员
     */
    @Test
    public void testGetListAdminByLinkAppId() {
        List<Integer> biDataSyncAppAdminUserIdList = enterpriseRelationRpcService.getBiDataSyncAppAdminUserIdList("82958");
        System.out.println(biDataSyncAppAdminUserIdList);
    }

    /**
     * 根据互联控件企业组id查询互联企业
     */
    @Test
    public void testTenantGroup() {
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(82958);
        ListTenantGroupDownstreamEnterprisesArg listTenantGroupDownstreamEnterprisesArg = new ListTenantGroupDownstreamEnterprisesArg();
        listTenantGroupDownstreamEnterprisesArg.setTenantGroupId("64dde6abdae138000137ac22");
        RestResult<ListTenantGroupDownstreamEnterprisesResult> listTenantGroupDownstreamEnterprisesResultRestResult = tenantGroupService.listTenantGroupDownstreamEnterprises(headerObj, listTenantGroupDownstreamEnterprisesArg);
        System.out.println(listTenantGroupDownstreamEnterprisesResultRestResult);
    }

    /**
     * 根据互联企业查询互联企业名称
     */
    @Test
    public void test() {
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(82958);
        BatchGetShortNamesArg batchGetShortNamesArg = new BatchGetShortNamesArg();
        batchGetShortNamesArg.setUpstreamTenantId(82958);
        batchGetShortNamesArg.setOuterTenantIds(Lists.newArrayList(89386L));
        RestResult<Map<Long, String>> mapRestResult = enterpriseRelationService.batchGetShortNames(headerObj, batchGetShortNamesArg);
        System.out.println(mapRestResult);
    }

    /**
     * 推送报表消息
     */
    @Test
    public void testPushReport() throws Exception {
        RemindRecordItem item = new RemindRecordItem();
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        item.setCategory(BiReportMessage);
        item.setType(140);
        item.setSourceId(uuid);
        item.setUrlType(1);
        item.setReceiverIDs(Lists.newArrayList(1000));
        item.setAppId("");
        item.setRemindSender(true);
        item.setTitle("xxxx");
        item.setFullContent("xxxxxx");
        FileInfo fileInfo = new FileInfo("策略名称", "keyDataSync", "异常原因：这是1+N测试", new Date());
        item.setUrlParameter(new HashMap<String, String>(2) {
            {
                put("fileInfo", JSON.toJSONString(fileInfo));
            }
        });
        AddRemindRecordArg addRemindRecordArg = new AddRemindRecordArg();
        addRemindRecordArg.setRemindRecordItem(item);
        addRemindRecordArg.setEi(82958);
        addRemindRecordArg.setUuid(uuid);
        MessageResponse messageResponse = crmNotifyService.addRemindRecord(addRemindRecordArg);
    }

    /**
     * 使用paas侧配置中心获取用户是否开启同步开关
     */
    @Test
    public void testUsePaasConfig() throws Exception {
        String configValueStr = null;
        QueryConfigByRankArg queryArg = new QueryConfigByRankArg();
        queryArg.setKey("bi_sync_policy_rpt_push");
        queryArg.setRank(Rank.TENANT);
        queryArg.setTenantId("82958");
        queryArg.setPkg("CRM");
        configValueStr = bizConfClient.queryConfigByRank(queryArg);
        System.out.println(configValueStr);
    }

    /**
     * 使用配置中心保存用户同步开关
     */
    @Test
    public void testSavePaasConfig() throws Exception {
        ConfigArg isUpdatedConfig = ConfigArg.builder()
                                             .tenantId("82958")
                                             .pkg("CRM")
                                             .rank(Rank.TENANT)
                                             .valueType(ValueType.STRING)
                                             .value("1")
                                             .key("bi_sync_policy_rpt_push")
                                             .build();
        bizConfClient.upsertConfig(isUpdatedConfig);
    }

    @Test
    public void testAggMapping() {
        String json = """
          [{"aggMappingList":[{"aggType":"uniq","downFieldId":"BI_677b950d825c8c00014403a9","downFieldName":"拜访客户（1+N）","nullActionDate":false,"upFieldId":"BI_677bac0d33e188431631a6a1","upFieldName":"拜访客户（1+N）"},{"aggType":"uniq","downFieldId":"BI_677b94fc825c8c00014403a5","downFieldName":"拜访外勤次数（1+N）","nullActionDate":false,"upFieldId":"BI_677bac0d33e188431631a6a2","upFieldName":"拜访外勤次数（1+N）"},{"aggType":"count","downFieldId":"BI_677b9523b1a6b3000177cdd0","downFieldName":"外勤数（1+N）","nullActionDate":false,"upFieldId":"BI_677bac0d33e188431631a6a3","upFieldName":"外勤数（1+N）"}],"dimFiledIdList":[{"fieldId":"BI_5bfe0042ba92330001264f62","fieldName":"客户","dbFieldName":"customer_id","objectDescribeApiName":"checkins_data","fieldType":"String","subFieldType":"","dbObjName":"dim_data","aggDimType":"dim","refObjName":"biz_account","refTargetField":"name","type":"object_reference"},{"fieldId":"BI_5c2dfed7e6fd560930629234","fieldName":"日期（月）","dbFieldName":"f_month","objectDescribeApiName":"checkins_data","fieldType":"Date","subFieldType":"","dbObjName":"dim_sys_date","aggDimType":"dim","refObjName":"","refTargetField":"","type":"date"}],"filterList":[{"filterFieldList":[{"fieldId":"BI_677b94fc825c8c00014403a5","fieldName":"拜访次数","dbFiledName":"finish_date","objectDescribeApiName":"checkins_data","fieldType":"Date","subFieldType":"","dbObjName":"agg_data","aggDimType":"base_agg","refObjName":"","refTargetField":"","type":"","operator":"13","value1":"1","value2":""}]}],"viewId":"BI_677b95d0c632e00001529bf4","viewName":"拜访1次的客户"}]
          """;
        BiDataSyncPolicyDo biDataSyncPolicyDo = new BiDataSyncPolicyDo();
        biDataSyncPolicyDo.setPolicyId("BI_677baf7c33e188451f2e8007");
        biDataSyncPolicyDo.setPolicyName("0106同步策略");
        biDataSyncPolicyDo.setPolicyDesc("");
        biDataSyncPolicyDo.setTenantId("802931");
        biDataSyncPolicyDo.setDataSourceEnterprise("[{\"id\":\"301943076\",\"type\":\"t\"},{\"id\":\"301948709\",\"type\":\"t\"}]");
        biDataSyncPolicyDo.setSyncStatSchemaId("BI_5bcebcdc3060e20001e79977");
        biDataSyncPolicyDo.setAggMappingRule(json);
        biDataSyncPolicyDo.setStatus(1);
        biDataSyncPolicyDo.setCreateBy("-10000");
        biDataSyncPolicyDo.setCreateTime(new Date().getTime());
        biDataSyncPolicyDo.setLastModifiedBy("-10000");
        biDataSyncPolicyDo.setLastModifiedTime(new Date().getTime());
        biDataSyncPolicyDo.setIsDeleted(0);

        /**
         * 生成策略插入sql
         */
        String insertPolicySql = """
          INSERT INTO %s.bi_data_sync_policy (policy_id, policy_name, policy_desc, tenant_id, data_source_enterprise,
                                                     sync_stat_schema_id, agg_mapping_rule, status, create_by, create_time,
                                                     last_modified_by, last_modified_time, is_deleted)
          VALUES ('%s', '%s', '', '%s', '%s', 'BI_5bcebcdc3060e20001e79977', '%s', 1, '-10000', now(), '-10000', now(), 0);
          """;

        System.out.println(String.format(insertPolicySql, "sch_802931", biDataSyncPolicyDo.getPolicyId(), biDataSyncPolicyDo.getPolicyName(), biDataSyncPolicyDo.getTenantId(), biDataSyncPolicyDo.getDataSourceEnterprise(), "s",biDataSyncPolicyDo.getAggMappingRule()));
    }

    /**
     * 1224 新case同步虚拟指标
     */
    @Test
    public void testCreateVirtualFieldId() {
        IntStream.range(0, 3).forEach(x -> System.out.println("BI_" + IdGenerator.get()));
    }

    @Test
    public void testQueryOutTenantId() {
        Map<String, Long> outTenantIdsByEas = enterpriseRelationRpcService.getOutTenantIdsByEas(Lists.newArrayList("90052", "92537"), "82958");
        System.out.println(outTenantIdsByEas);
    }

    /**
     * 1224 新case测试同步
     */
    @Test
    public void testAggMapping2() {
        String json = """
          [
                {
                    "aggMappingList": [
                        {
                            "aggType": "uniq",
                            "downFieldId": "BI_676919f2b888690001063783",
                            "downFieldName": "拜访次数",
                            "nullActionDate": false,
                            "upFieldId": "BI_676a5e9f2d6fe875610691d7",
                            "upFieldName": "1224 1+N 拜访次数"
                        },
                        {
                            "aggType": "uniq",
                            "downFieldId": "BI_67691a1fb88869000106378c",
                            "downFieldName": "客户数",
                            "nullActionDate": false,
                            "upFieldId": "BI_676a5e9f2d6fe875610691d8",
                            "upFieldName": "1224 1+N 客户数"
                        },
                        {
                            "aggType": "count",
                            "downFieldId": "BI_67691a44b888690001063795",
                            "downFieldName": "客户数",
                            "nullActionDate": false,
                            "upFieldId": "BI_676a5e9f2d6fe875610691d9",
                            "upFieldName": "1224 1+N 客户数"
                        }
                    ],
                    "dimFiledIdList": [
                        {
                            "fieldId": "BI_5bfe0042ba92330001264f62",
                            "fieldName": "客户",
                            "dbFieldName": "customer_id",
                            "objectDescribeApiName": "checkins_data",
                            "fieldType": "String",
                            "subFieldType": "",
                            "dbObjName": "dim_data",
                            "aggDimType": "dim",
                            "refObjName": "biz_account",
                            "refTargetField": "name",
                            "type": "object_reference"
                        },
                        {
                            "fieldId": "BI_5c2dfed7e6fd560930629234",
                            "fieldName": "日期（月）",
                            "dbFieldName": "f_month",
                            "objectDescribeApiName": "checkins_data",
                            "fieldType": "Date",
                            "subFieldType": "",
                            "dbObjName": "dim_sys_date",
                            "aggDimType": "dim",
                            "refObjName": "",
                            "refTargetField": "",
                            "type": "date"
                        }
                    ],
                    "filterList": [
                        {
                            "filterFieldList": [
                                {
                                    "fieldId": "BI_676919f2b888690001063783",
                                    "fieldName": "拜访次数",
                                    "dbFiledName": "finish_date",
                                    "objectDescribeApiName": "checkins_data",
                                    "fieldType": "Date",
                                    "subFieldType": "",
                                    "dbObjName": "agg_data",
                                    "aggDimType": "base_agg",
                                    "refObjName": "",
                                    "refTargetField": "",
                                    "type": "",
                                    "operator": "13",
                                    "value1": "1",
                                    "value2": ""
                                }
                            ]
                        }
                    ],
                    "viewId": "BI_67691ac9b8886900010637ab",
                    "viewName": "外勤主题：拜访次做数据范围"
                }
            ]
          """;
        BiDataSyncPolicyDo biDataSyncPolicyDo = new BiDataSyncPolicyDo();
        biDataSyncPolicyDo.setPolicyId("BI_676a4fa32d6fe86d6f1f24da");
        biDataSyncPolicyDo.setPolicyName("1224case 同步带维度和数据范围的聚合数据");
        biDataSyncPolicyDo.setPolicyDesc("");
        biDataSyncPolicyDo.setTenantId("82958");
        biDataSyncPolicyDo.setDataSourceEnterprise("[{\"id\":\"300111632\",\"type\":\"t\"},{\"id\":\"300119355\",\"type\":\"t\"}]");
        biDataSyncPolicyDo.setSyncStatSchemaId("BI_5bcebcdc3060e20001e79977");
        biDataSyncPolicyDo.setAggMappingRule(json);
        biDataSyncPolicyDo.setStatus(1);
        biDataSyncPolicyDo.setCreateBy("1000");
        biDataSyncPolicyDo.setCreateTime(new Date().getTime());
        biDataSyncPolicyDo.setLastModifiedBy("1000");
        biDataSyncPolicyDo.setLastModifiedTime(new Date().getTime());
        biDataSyncPolicyDo.setIsDeleted(0);
        RouterInfo targetTenantRouterInfo = chRouterPolicy.getRouterInfo("82958");
        aggDownStreamComplexService.syncComplexAggData(new AtomicLong(0), new Date().getTime(), 52909L, "90052", "6751229b08e1d4000182821f", targetTenantRouterInfo, biDataSyncPolicyDo,"s");
        //aggDownStreamComplexService.syncComplexAggData(new AtomicLong(0), new Date().getTime(), 52908L, "92537", "6768d82771cf0a0001c01f7e", targetTenantRouterInfo, biDataSyncPolicyDo);
    }
}
