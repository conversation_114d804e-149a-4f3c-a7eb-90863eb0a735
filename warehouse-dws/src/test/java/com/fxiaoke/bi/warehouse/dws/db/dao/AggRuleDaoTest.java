package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.AggRuleDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewDO;
import com.fxiaoke.bi.warehouse.dws.transform.model.StatViewDimBO;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author:jief
 * @Date:2024/8/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class AggRuleDaoTest {
  @Resource
  AggRuleDao aggRuleDao;

  @Test
  public void testStatRule(){
    String tenantId="71570";
    String viewId="BI_64b89e7c2f14210001f5e872";
    String ruleId="BI_60ab4888abc5d200015d23b2";
    final Map<String, AtomicInteger> aggAliasMapper = Maps.newHashMap();
    final Map<String, AtomicInteger> dimsMapper = Maps.newHashMap();
    final Map<String, String/*dim字段别名*/> dimFieldAliasMapper = Maps.newHashMap();
    StatViewDO statViewDO = null;// statViewDao.queryStatView(tenantId, viewId);
    //    TopologyTable statView = statTopologyService.createStatView(tenantId, statViewDO);
    //    StatViewEntity statViewEntity = statViewDao.findStatViewById(tenantId, statViewDO);
    StatViewDimBO statViewDimBO =null;//dimRuleDao.buildStatViewDimBo(tenantId, statViewEntity);
    TopologyTableAggRule statRule = aggRuleDao.parseAggRuleFromMap(tenantId, ruleId, statViewDimBO, aggAliasMapper,
      dimsMapper, dimFieldAliasMapper);

    //    statRule.changeJoinType(JoinType.INNER_JOIN);
    System.out.println(JSON.toJSONString(statRule));
    //    List<String> tables=Lists.newArrayList();
    //    NodeTable.findAggRuleTableAlias(statRule.getRootNodeTable(),tables);
    //    System.out.println(JSON.toJSONString(tables));
    //    System.out.println(NodeTable.findParentTableAlias(statRule.getRootNodeTable(),"object_data_1"));
    //    System.out.println(statView.toViewSQL(-1));
  }
}
