package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.bean.UserCenterService;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyTableDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.service.ClickHouseService;
import com.fxiaoke.bi.warehouse.dws.service.CompareService;
import com.fxiaoke.bi.warehouse.dws.service.StatTopologyService;
import com.fxiaoke.bi.warehouse.dws.service.TopologyTableService;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.AggRuleDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.DimRuleDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.FiscalDateConvertDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.StatViewDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewDO;
import com.fxiaoke.bi.warehouse.dws.transform.impl.OldStatViewTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Date 2023/04/06
 * @Description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
@Slf4j
//@Transactional  // 2
//@Rollback
public class StatTests {
  @Resource
  TopologyTableDao topologyTableDao;
  @Resource
  AggRuleDao aggRuleDao;
  @Resource
  DimRuleDao dimRuleDao;
  @Resource
  StatTopologyService statTopologyService;
  @Resource
  OldStatViewTopologyTransformer oldStatViewTopologyTransformer;
  @Resource
  private TopologyTableService topologyTableService;
  @Resource
  StatViewDao statViewDao;
  @Resource
  CompareService compareService;
//  @Resource(name = "orgAdapterApiEnterpriseConfigService")
//  private EnterpriseConfigService enterpriseConfigService;
  @Resource
  private FiscalDateConvertDao fiscalDateConvertDao;

  @Resource(name="userCenterService")
  private UserCenterService userCenterService;
  @Resource
  ClickHouseService clickHouseService;

  @BeforeClass
  public static void init(){
    System.setProperty("--add-opens java.base/java.lang","ALL-UNNAMED");
    System.setProperty("--add-opens java.base/java.lang","ALL-UNNAMED");
  }

  @Test
  public void testPreViewSql() {
    String enumArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_5cac8f2c0492437ee2bef8a2\", \"BI_5a26a82598984d5eb3e3ae1064d2ea7b\"],\"queryDetailType\":0,\"schemaId\":\"BI_5bcebcdc3060e20001e79977\",\"sourceId\":\"BI_5cd6a95937aa1b9ff04a4864\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String enumMultiOptionArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_66e3a2e66bf52700011fd61e\", \"BI_test02\", \"BI_c9291fd39ce94b28b05db73a76ebd676\", \"BI_eb783ae41c274d65abc3971e29fed676\"],\"queryDetailType\":0,\"schemaId\":\"BI_bf093e5c614b607ce7ef7012\",\"sourceId\":\"BI_5cd6a95937aa1b9ff04a4864\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String enumArg1 = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_8a61e01c71234b7ab1c7e23b1c86c4e0\",\"BI_5bcebcddcab2980001ee2364\"],\"queryDetailType\":0,\"schemaId\":\"BI_5bcebcdc3060e20001e79977\",\"sourceId\":\"BI_66e166bb5fcc3f0001eee56b\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String enumArg2 = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_571e25db41c448eab6f0262df5bbe820\",\"BI_5bcebcddcab2980001ee2364\"],\"queryDetailType\":0,\"schemaId\":\"BI_5bcebcdc3060e20001e79977\",\"sourceId\":\"BI_66e166bb5fcc3f0001eee56b\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String referenceArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_5cac8f2c0492437ee2bef8a2\",\"BI_5bceda90dedd2c0001c2f54e\",\"BI_test02\"],\"queryDetailType\":0,\"schemaId\":\"BI_5bcebcdc3060e20001e79977\",\"sourceId\":\"BI_5cd6a95937aa1b9ff04a4864\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String referenceNewArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_66d7d84585f3ac00010754ae\",\"BI_66e4059200fb370001ad0c25\",\"BI_66d52c1381ac390001c54439\",\"BI_559f0e9370fc4851af6538ef0e09802f\", \"BI_d27d80f3253a446dbc9b695551c4d078\"],\"queryDetailType\":0,\"schemaId\":\"BI_5f2901b4711f2c97c377a97b\",\"sourceId\":\"BI_5cd6a95937aa1b9ff04a4864\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String referenceNewArg1 = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_66e4059200fb370001ad0c25\",\"BI_e2761b1f92db474ba119329781c665f4\",\"BI_559f0e9370fc4851af6538ef0e09802f\"],\"queryDetailType\":0,\"schemaId\":\"BI_5f2901b4711f2c97c377a97b\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    // 小米粥自定义对象引用人员自定义字段日期类型
    String referenceNewArg2 = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_2efffc1003644994b78fb24dcdfe2550\",\"BI_66d7d84585f3ac00010754ae\", \"BI_5d4a75e06973e300011a3107\"],\"queryDetailType\":0,\"schemaId\":\"BI_5f2901b4711f2c97c377a97b\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String emptyGroupArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_5cac8f2c0492437ee2bef8a2\",\"BI_5bceda90dedd2c0001c2f54e\",\"BI_0396249a657d45cf8698b008bb5f8c5e\",\"BI_5bcebcddcab2980001ee2366\"],\"queryDetailType\":0,\"schemaId\":\"BI_5bcebcdc3060e20001e79977\",\"sourceId\":\"BI_5cd6a95937aa1b9ff04a4864\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String twoEnumGroupArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_5bcebcddcab2980001ee2364\",\"BI_test01\",\"BI_10aa72fd7a6e49bfa3aede81117fd679\",\"BI_5bcebcddcab2980001ee22d5\",\"BI_5bcebcddcab2980001ee22b3\",\"BI_22775e92b4a840478a4b22798629569b\"],\"queryDetailType\":0,\"schemaId\":\"BI_5bcebcdc3060e20001e79977\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String noCustomHaveNormalDimArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_5bcebcddcab2980001ee2364\",\"BI_5bcebcddcab2980001ee22d5\",\"BI_5bcebcddcab2980001ee22b3\"],\"queryDetailType\":0,\"schemaId\":\"BI_5bcebcdc3060e20001e79977\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String noDimArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_657010ec38c8a10001831615\",\"BI_5d4a75e06973e300011a3107\"],\"queryDetailType\":0,\"schemaId\":\"BI_5bcec11f56fc11160c8c8271\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String oneCustomDimArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_5bcebcddcab2980001ee2364\", \"BI_14e9c5c6b1334aa4aeb557c464738290\"],\"queryDetailType\":0,\"schemaId\":\"BI_5bcebcdc3060e20001e79977\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    String whatListArg = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_5a68bc24e5e1907296f62b4a22660\", \"BI_5e4d54ec15fd1700017c5ba6abc92\", \"BI_5e4d54ec15fd1700017c5ba80017c\"],\"queryDetailType\":0,\"schemaId\":\"BI_55479c68f4922c26cbadda8265b2f\",\"sourceType\":0,\"tenantId\":\"85145\",\"timeZone\":\"Asia/Shanghai\"}";
    String arg1 = "{\"aggFilterFieldIds\":[],\"allStatFieldIds\":[\"BI_66d7d84585f3ac00010754ae\",\"BI_80dbff1d9862463eab6f2656b943eb29\"],\"queryDetailType\":0,\"schemaId\":\"BI_5f2901b4711f2c97c377a97b\",\"sourceType\":0,\"tenantId\":\"84439\",\"timeZone\":\"Asia/Shanghai\"}";
    StatViewPreArg statViewPreArg = JSON.parseObject(arg1, StatViewPreArg.class);
    StatViewPreSQL statViewPreSQL= statTopologyService.createStatViewPreSQL(statViewPreArg);
    System.out.println("statViewPreSQL = " + JSON.toJSONString(statViewPreSQL));
    String preViewSQL = statViewPreSQL.getPreViewSQL();
    System.out.println("preViewSQL = " + preViewSQL);
    // clickHouseService.executeQuerySQL("84439", preViewSQL, Function.identity());
  }
  @Test
  public void testCheckOverTime(){
    TransferEvent transferEvent= TransferEvent.builder().dbURL("aaa").chDbURL("ddd").build();
    String timeKey= "compyAfter_ing";
    String errorMsg= "error";
    Utils.checkOverTime(SyncStatusEnum.COPY_AFTER_ING, transferEvent, timeKey, 1734181200000L, 10L, errorMsg, (delayTime) -> {
      System.out.println("run task delay:"+delayTime);
    });

  }
  @Test
  public void testUcRest()throws Exception{
//    PaasFiscalConfig paasFiscalConfig= fiscalDateConvertDao.getFiscalConfig("71570");
//    System.out.println(JSON.toJSONString(paasFiscalConfig));

    String a = "";
    String[] profiles = a.split(",");
    if (StringUtils.equalsAny(ConfigHelper.getProcessInfo().getProfile(), profiles)) {
      System.out.println("skip");
    }
  }

  @Test
  public void testGray2(){
    System.out.println(GrayManager.isAllowByRule("test_mengniu","EI.40165001"));
  }

  @Test
  public void testCheckAggRule(){
    String tenantId="85145";
    String fieldId="BI_6578276535a80e00014c49b1";
    Set<AggRuleChangedEvent> aggRuleChangedEvents= Sets.newHashSet();
    AggRuleChangedEvent aggRuleChangedEvent= AggRuleChangedEvent.builder().tenantId("85145").ruleId("BI_659cae70a623820001072ae5").build();
    aggRuleChangedEvents.add(aggRuleChangedEvent);
    oldStatViewTopologyTransformer.batchOnChangeAggRule(aggRuleChangedEvents);
  }
  @Test
  public void testCheckTheme(){
    String tenantId="85145";
    String fieldId="BI_6578276535a80e00014c49b1";
    Set<DimRefreshEvent> dimEventList= Sets.newHashSet();
    DimRefreshEvent aggRuleChangedEvent= DimRefreshEvent.builder().tenantId("85145").themeApiName("object_y5651__c").build();
    dimEventList.add(aggRuleChangedEvent);
    oldStatViewTopologyTransformer.batchOnChangeTheme(dimEventList);
  }
  @Test
  public void testCreatePreViewSQL() throws IOException {
    File file = ResourceUtils.getFile("classpath:stat/StatView_1.json");
    String json = Files.readString(file.toPath());
    StatViewPreArg statViewPreArg=JSON.parseObject(json,StatViewPreArg.class);
    StatViewPreSQL statViewPreSQL= statTopologyService.createStatViewPreSQL(statViewPreArg);
    System.out.println(statViewPreSQL.getPreViewSQL());
    System.out.println(JSON.toJSONString(statViewPreSQL.getPushDownFields()));
  }

  @Test
  public void testAggRule(){
    String tenantId="82958";
    String ruleId="BI_63ea2dc09ac2c7000187be76";
    boolean standalone = true;
    //计算别名
    //缓存列类型
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    Map<String, Object> ruleInfoMap = aggRuleDao.findAggRuleMapFromDB(tenantId, ruleId);
    JSONObject ruleInfo = new JSONObject(ruleInfoMap);
    String themeApiName = ruleInfo.getString("theme_api_name");
    String fieldId = ruleInfo.getString("field_id");
    String aggApiName = ruleInfo.getString("check_object_api_name");
    String checkApiNameLookupField = ruleInfo.getString("check_apiname_lookup_field");
    String aggDimType = ruleInfo.getString("agg_dim_type");
    String timeZone = Constants.getTimeZoneOrDefault(ruleInfo.getString("time_zone"), Constants.Asia_Shanghai);
    AggRule aggRule = aggRuleDao.createAggRule(tenantId, ruleInfo, standalone, themeApiName, ruleId, cachedTableDefinitions
      , aggDimType);
    aggRule.init();
    System.out.println(JSON.toJSONString(aggRule.valueRule));
  }

  @Test
  public void testStat() {
//    List<StatViewDO> statViewDO = statViewMapper.setTenantId("-1").batchQueryStatViewByViewIds(71570, new String[]{"BI_6433e901376a0c0001bf627b"});
//    System.out.println(statViewDO.size());
    Map<String, Set<String>>  statViewBaseFields = statViewDao.batchQueryStatViewBaseField("71570", Lists.newArrayList("BI_6433e901376a0c0001bf627b"));
    System.out.println(statViewBaseFields.size());
    System.out.println(JSON.toJSONString(statViewBaseFields));
//["BI_cebe50322bb7180e7fd7b5b00e8a1"]
  }

  @Test
  public void testLinked(){
    String tenantId="85145";
    String followId="BI_6551d40bedade50001699443";
//    List<String> a= Lists.newArrayList();
//     statViewDao.queryAllLinkFields(tenantId, followId,a);
//    System.out.println(JSON.toJSONString(a));
    List<String> b = statViewDao.findOwnersByFollowId( tenantId,  followId);
    System.out.println(JSON.toJSONString(b));
  }

  @Test
  public void testCreateTopology() {
    String tenantId = "90970";
//    ArrayList<String> viewIds = Lists.newArrayList("BI_65795b358c51a4000100b589","BI_655da827b43a6900019aa440","BI_65e13035be6b4f000169ee93","BI_63e5b4629424de000102f7a8","BI_6523be5d842dd9000173ca71","BI_65e12b99be6b4f000169eccc","BI_65e12c40be6b4f000169ed2b","BI_65e12c5dbe6b4f000169ed69","BI_66f03b9da76f0000015f39c9","BI_655dba79f9a9a90001f390d4","BI_66f0d831a76f0000015f7db2","BI_66f3cafe0a0b03000142ca84","BI_65f032e0ed69830001ddfb67","BI_66f3c0cd0a0b03000142c889","BI_65f40c624653dd0001d49ab9","BI_66e54f2b7d297e0001f1b63f","BI_66f3844d6b2ee60001893d78","BI_656d842884561500013b0514","BI_66f379516b2ee60001893472","BI_66f3797c6b2ee600018934d4","BI_66f37abb764d4e2770029459","BI_66f387b7764d4e1ff0978f0a","BI_66f3959f764d4e1ff0978fa8","BI_66f1543fa76f0000015fa53e","BI_58b001ae2b90174bfad7d46b","BI_65b0b1844fd4a40001128f14","BI_655dab2ab43a6900019aa4d7","BI_66f3a8200a0b03000142b9d9","BI_66f2a5c76b2ee6000188c6be","BI_65f032f4ed69830001ddfbb9","BI_66c45519c37e29000164c076","BI_65fa935d063ada0001f63736","BI_65f94fac063ada0001f5d5bc","BI_66f4c9318e16330001bc8259","BI_655dba3ef9a9a90001f39048","BI_655dbaa9f9a9a90001f39151","BI_655dba2af9a9a90001f38ffd","BI_66f374b36b2ee600018931a1","BI_657030230d39df0001580d89","BI_66e3dc580633c90001882be2","BI_66f3df030a0b03000142d1ba","BI_6400538c6ab0600001a72a38","BI_655db9e1f9a9a90001f38fde","BI_66f14692a76f0000015fa295","BI_66d2f67353f40e00015962c8","BI_58b001ae2b90174bfad7d455","BI_65e129d6be6b4f000169e979","BI_66e4fc27a28da300017975e5","BI_66e4f01fa28da30001797225","BI_66f2854c946cc200011000be","BI_66f3ae785de5d5000168d0ab","BI_65f950da063ada0001f5d67b","BI_66f0d9dfa76f0000015f821e","BI_66e2b8525fcc3f0001efcfe2","BI_66e1809b5fcc3f0001eee9ff","BI_66f37a69764d4e277002940c","BI_66f3861e764d4e205c66aea4","BI_66f3898c764d4e1ff0978f5a","BI_66f395b7764d4e1ff0978ff5","BI_66f400ca2531dc0001d95ba3","BI_66e166bb5fcc3f0001eee56b","BI_5eb5450ae6835e00010a894b","BI_66e52a777d297e0001f1aaae","BI_58b001ae2b90174bfad7d441","BI_66bb2e6bb9b1f90001a196d9","BI_66e1864a5fcc3f0001eeeb40","BI_66f373756b2ee6000189302c","BI_66d2ddff53f40e0001595eea","BI_66d2dbf553f40e0001595e5f","BI_66f0e131215b9600013333b3","BI_66e28cd55fcc3f0001efb7c7","BI_66f375fc6b2ee60001893317","BI_657013970533ff000141da9b","BI_66f411e12531dc0001d95e12","BI_655efc8cf9a9a90001f3d704","BI_63e5b4239424de000102f5e2","BI_65f03398ed69830001ddfca7","BI_657426af16950000012b1b89","BI_58acfc2537aa1badf31d169a","BI_66f3d7010a0b03000142d046","BI_66d2ecac53f40e000159612c","BI_65b0b1724fd4a40001128ec9","BI_62285d4d97043d0001c95eca","BI_66f0e6c6a76f0000015f8482","BI_66f289715c41ce30c0b362b0","BI_62e88e3fb907cd0001613fc5","BI_66f10547a76f0000015f8a77","BI_63edfef9a074350001d24482","BI_65e12af4be6b4f000169ec44","BI_66f284236b2ee6000188bf75","BI_66e2858a46d8fb00015db076","BI_66e2b82c5fcc3f0001efcf8d","BI_66f4c5b38e16330001bc80ef","BI_655dba68f9a9a90001f39093","BI_6576823d16950000012b9d7d","BI_657682cf16950000012b9ddd","BI_655db308b43a6900019aa662","BI_655db327b43a6900019aa6ab","BI_655dab4fb43a6900019aa521","BI_6401b335b9b6e9000116ff8c","BI_66f2ae076b2ee6000188c953","BI_65702f370d39df0001580d22","BI_66f380836b2ee60001893c38","BI_66f1474ea76f0000015fa2fc","BI_65aa2deddbdc8c0001ac1fab","BI_657ac6a86a1ac000017e4e03","BI_657ec3e2366cef00019e51dd","BI_6571287a0d39df0001585b5b","BI_6576b04816950000012ba4dd","BI_657964c48c51a4000100b7b8","BI_5a6873f237aa1b3d4865838b","BI_66e543cb7d297e0001f1b463","BI_5a68738337aa1b3d4865836e","BI_655dbab7f9a9a90001f3919d","BI_66e265985fcc3f0001efad8d","BI_66f3ae8c764d4e2dbcd102c3","BI_66f2aeea6b2ee6000188c9be","BI_66e2cf604205c50001f168d2","BI_66e2aeb302381c00018839d4","BI_66e2d3ed6bf52700011fb9a0","BI_66e2d5814205c50001f16a52","BI_655db8eaf9a9a90001f38f95","BI_655dba89f9a9a90001f39110","BI_66f3adec764d4e2dbcd10276","BI_66dfb3151a77910001c342b0","BI_66d689b253f40e00015a8e50","BI_66ebc18738069300015b9170","BI_5a68730437aa1b3d48658351","BI_65e12986be6b4f000169e761","BI_65e12ac3be6b4f000169eb86","BI_65e12a28be6b4f000169ea01","BI_65e1297fbe6b4f000169e716","BI_65e129aebe6b4f000169e87a","BI_65e129bfbe6b4f000169e8f5","BI_65e12aadbe6b4f000169eb2d","BI_65e1299ebe6b4f000169e7f4","BI_65e12aa9be6b4f000169eaff","BI_65e12adebe6b4f000169ebe5","BI_66e404f50633c90001883833","BI_63e5b41e9424de000102f58a","BI_66f2851f946cc2000110006e");
    List<String> viewIds = Lists.newArrayList("BI_67b54cecce2f1600019c117f");
    for (String viewId : viewIds) {
      try {
        statTopologyService.doCreateTopology(tenantId, viewId, 0, TopologyTableStatus.Prepared.getValue());
      } catch (Exception e) {
        System.out.println("doCreateTopology error : viewId:" + viewId);
        log.error("doCreateTopology error : viewId:{}", viewId, e);
      }
    }
  }

  @Test
  public void testStatViewCompare(){
    StatViewBatchArg statViewBatchArg= new StatViewBatchArg();
    statViewBatchArg.setTenantId("85145");
    StatViewPreArg statViewPreArg= new StatViewPreArg();
    statViewPreArg.setSourceId("BI_6570352d2009810001705e5d");
    statViewPreArg.setSourceType(0);
    statViewBatchArg.setStatViewArgList(Lists.newArrayList(statViewPreArg));
    List<Map<String, Object>> result =  statTopologyService.batchCompareStatView(statViewBatchArg);
    System.out.println(JSON.toJSONString(result));
  }

  @Test
  public void testStatTopologyService(){
//    StatView statView= statTopologyService.findByViewId("71570","BI_6433e901376a0c0001bf627b");
//    statView.setDatabase("bi_112");
//    System.out.println(JSON.toJSON(statView));
    List<TopologyTableDO> topologyTableDOS = topologyTableDao.findNeedCalculateList("82958",
      new int[] {TopologyTableStatus.Prepared.getValue(), TopologyTableStatus.Calculating.getValue()});
    System.out.println(JSON.toJSONString(topologyTableDOS));
  }

  @Test
  public void testEis(){
    String tenantId="85145";
    String viewId="BI_6673d4a7dcc6600001879540";
    String ruleId="BI_63e7505d9ac2c70001872a67";
    StatViewDO statViewDO = statViewDao.queryStatView(tenantId, viewId);
    StatViewEntity statViewEntity = statViewDao.findStatViewById(tenantId, statViewDO);
    System.out.println(statViewEntity.getAggRuleFilterIds());
    System.out.println(statViewEntity.getAggRuleIds());
    statViewEntity.getDimFields().forEach(displayField -> System.out.println(displayField.getStatFieldId()));
  }
@Test
  public void testStatViewFilter(){
    String tenantId="85145";
    String viewId="BI_58acfb6137aa1badf31d1633";
  System.out.println(statViewDao.countOfGoalFields(tenantId, viewId));
  }
  @Test
  public void testCustomStatView(){
    String tenantId="85145";
    String fromDateTime="1970-01-01 00:00:00";
    List<StatViewDO> a = statViewDao.batchQueryCustomStatViewByEi(tenantId, fromDateTime);
    System.out.println(a.size());
  }

  @Test
  public void testStatViewSQL() {
    String tenantId="85145";
    String viewId="BI_6756d30f5a50b00001c4a95b";
//    String viewId="BI_66a21bbf0305860001e571ab";
    StatViewDO statViewDO = statViewDao.queryStatView(tenantId, viewId);
    TopologyTable statView = oldStatViewTopologyTransformer.createStatView(tenantId, statViewDO);
    List<TopologyTableAggRule> statRuleList = statView.getStatRuleList();
    topologyTableService.addDownStreamRuleWhereSql(statView);
    TopologyTableAggRule statRule = statRuleList.get(0);
//    System.out.println("hasDataExplosive===>"+statRule.hasDataExplosive(false,statRule.getRootNodeTable()));
//    System.out.println("statRule json ===>"+JSON.toJSONString(statRuleList));
    Map<String, Map<String,String>> rightLargeTables = Maps.newHashMap();
    Map<String,String> tableSamples = Maps.newHashMap();
    tableSamples.put("object_data","1000");
    rightLargeTables.put("BI_6454a0775f585a0001efcc85", tableSamples);
    Map<String, List<String>> tableKeys = Maps.newHashMap();
    tableKeys.put("org_employee_user", Arrays.asList("tenant_id","bi_sys_flag","id"));
    tableKeys.put("object_data", Arrays.asList("tenant_id","object_describe_api_name","bi_sys_flag","id"));
    tableKeys.put("goal_value_obj", Arrays.asList("tenant_id","bi_sys_flag","id"));
    System.out.println("statView.toViewSQL ===>"+statView.toViewSQL(-1,-1, rightLargeTables, tableKeys));
//    System.out.println("getStatFieldLocation ===>"+statView.getDimUniqFieldMapper());
//    System.out.println("dimFields ===>"+JSON.toJSONString(statView.getCommonDimList()));

//    TopologyTableMonitor viewMonitor = statView.toStatViewMonitor();
//    List<TopologyTableAggRuleMonitor> statRuleMonitorList= viewMonitor.getStatRuleMonitorList();
//    statRuleMonitorList.forEach(statRuleMonitor -> {
//      System.out.println(statRuleMonitor.getComputeSQL());
//    });
  }


  @Test
  public void testStatRule(){
    String tenantId="82958";
    String viewId="BI_64b89e7c2f14210001f5e872";
    String ruleId="BI_66444e1facbee100015b73ff";
    final Map<String, AtomicInteger> aggAliasMapper = Maps.newHashMap();
    final Map<String, AtomicInteger> dimsMapper = Maps.newHashMap();
    final Map<String, String/*dim字段别名*/> dimFieldAliasMapper = Maps.newHashMap();
    StatViewDO statViewDO = null;// statViewDao.queryStatView(tenantId, viewId);
//    TopologyTable statView = statTopologyService.createStatView(tenantId, statViewDO);
//    StatViewEntity statViewEntity = statViewDao.findStatViewById(tenantId, statViewDO);
    StatViewDimBO statViewDimBO =null;//dimRuleDao.buildStatViewDimBo(tenantId, statViewEntity);
    TopologyTableAggRule statRule = aggRuleDao.parseAggRuleFromMap(tenantId, ruleId, statViewDimBO, aggAliasMapper,
      dimsMapper, dimFieldAliasMapper);

//    statRule.changeJoinType(JoinType.INNER_JOIN);
    System.out.println(JSON.toJSONString(statRule));
//    List<String> tables=Lists.newArrayList();
//    NodeTable.findAggRuleTableAlias(statRule.getRootNodeTable(),tables);
//    System.out.println(JSON.toJSONString(tables));
//    System.out.println(NodeTable.findParentTableAlias(statRule.getRootNodeTable(),"object_data_1"));
//    System.out.println(statView.toViewSQL(-1));
  }

  @Test
  public void testDashBoard(){
    String tenantId="85145";
    String viewId="BI_64c8a47d71f8890001e588fe";
    StatViewDO statViewDO = statViewDao.queryStatView(tenantId, viewId);
    StatViewEntity statViewEntity = statViewDao.findStatViewById(tenantId, statViewDO);
    System.out.println(JSON.toJSONString(statViewEntity));
//    Set<String> fieldIds = dashboardDao.findAllFieldUsedInDashBoard(tenantId,viewId,statViewEntity.getSchemaId(),statViewEntity.getThemeName());
//    System.out.println("result=================="+JSON.toJSONString(fieldIds));
  }
  @Test
  public void testDimStatView(){
    String tenantId="85145";
    String viewId="BI_64c8a47d71f8890001e588fe";
    StatViewDO statViewDO = statViewDao.queryStatView(tenantId, viewId);
    StatViewEntity statViewEntity = statViewDao.findStatViewById(tenantId, statViewDO);
//    StatViewDimBO statViewDimBO = dimRuleDao.buildStatViewDimBo(tenantId, statViewEntity);
//    System.out.println(JSON.toJSONString(statViewEntity));
    System.out.println(JSON.toJSONString(statViewEntity.findAllAggFieldsWithStatus()));
  }

  @Test
  public void testDimRuleTopology() {
//    List<String> fieldIds = Lists.newArrayList("BI_0a5feb8c2265a84dfa73f612a2bba", "BI_478f358db03f53381a5de9a960c60", "BI_630899ddc0261800017047de", "BI_098990b4639238d5d817a3f7d306c", "BI_630899ddc026180001704841", "BI_630899ddc02618000170481d", "BI_5bcebcddcab2980001ee22ad", "BI_5bcebcddcab2980001ee22b8", "BI_5c271e05c8ca95336c03f9b9");
//    List<DimRule> dimRules = dimRuleDao.queryDimField("71570", "biz_account", fieldIds,"");
////    dimRules.forEach(dimRule -> System.out.println(JSON.toJSON(dimRule.getDimFieldRule())));
//    Map<String, AtomicInteger> aliasMapper = Maps.newHashMap();
//    NodeTable aggObjNodeTable = aggRuleDao.createAggNodeTable(false,"biz_account",aliasMapper) ;
//    //NodeTable.of("biz_account",null,Lists.newArrayList(), Sets.newHashSet(),"biz_account",null);
//    List<String> dimConfigStringList = Lists.newArrayList();
//    if (CollectionUtils.isNotEmpty(dimRules)) {
////      dimRules.forEach(dimRule -> dimRule.dimRulePlantTrees(aggObjNodeTable, aliasMapper, false));
//      dimRules.forEach(dimRule -> {
//        aggRuleDao.joinQuoteNodeTable("71570",false,aggObjNodeTable,aliasMapper,dimRule.getDimFieldRule());
//        System.out.println(dimRule.getDimFieldRule().alias+":"+dimRule.getDimFieldRule().column+":"+dimRule.getDimFieldRule().columnType.trans2CHType());
//      });
//    }
//    System.out.println(JSON.toJSON(aggObjNodeTable));
  }
  @Test
  public void testObjectIdRule(){
    boolean standalone = false;
    String tenantId="71570";
//    String ruleId="BI_5fcdcfed95b6610001f5608d";
//    String ruleId="BI_63611427779072000184cd9f";
//    String ruleId="BI_5ebb49b87bfe2600017624c4";
//    String ruleId="BI_6420fe6450c6d90001618e4b";
//    String ruleId="BI_5bceda90dedd2c0001c2f54f";
    String ruleId="BI_60f5379b151843000199cf54";
    Map<String, Map<String, ColumnDefinition>> tableColumnDefinitionMap = Maps.newHashMap();
    Map<String, AtomicInteger> aliasMapper = Maps.newHashMap();
    Map<String, Object> ruleInfoMap = aggRuleDao.findAggRuleMapFromDB(tenantId, ruleId);
    JSONObject ruleInfo = new JSONObject(ruleInfoMap);
    String aggApiName = ruleInfo.getString("check_object_api_name");
    String checkApiNameLookupField = ruleInfo.getString("check_apiname_lookup_field");
    String themeApiName = ruleInfo.getString("theme_api_name");
    NodeTable aggObjNodeTable = aggRuleDao.createAggNodeTable(standalone, aggApiName, aliasMapper);
    ObjectIdRule objectIdRule = aggRuleDao.buildObjectIdRule(tenantId, standalone, themeApiName, aggApiName, ruleInfo, tableColumnDefinitionMap);
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions=Maps.newHashMap();
    //获取objectId table
    NodeTable objectIdNodeTable = aggRuleDao.joinObjectIdQuoteNodeTableAndGet(tenantId, standalone, aggObjNodeTable, aliasMapper, themeApiName, checkApiNameLookupField, objectIdRule,cachedTableDefinitions);
    //构建dim
    System.out.println(JSON.toJSON(aggObjNodeTable));
  }

  @Test
  public void testWhere(){
    boolean standalone = false;
    String tenantId="71570";
    String ruleId="BI_60f5379b151843000199cf54";
    //计算别名
    Map<String, AtomicInteger> aliasMapper = Maps.newHashMap();
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    Map<String, Object> ruleInfoMap = aggRuleDao.findAggRuleMapFromDB(tenantId, ruleId);
    JSONObject ruleInfo = new JSONObject(ruleInfoMap);
    String themeApiName = ruleInfo.getString("theme_api_name");
    String aggApiName = ruleInfo.getString("check_object_api_name");
    String checkApiNameLookupField = ruleInfo.getString("check_apiname_lookup_field");
    String timeZone = Constants.getTimeZoneOrDefault(ruleInfo.getString("time_zone"), Constants.Asia_Shanghai);
    //where 条件
    List<WhereConfig> preWhereConfigList = Lists.newArrayList();
    List<List<WhereConfig>> whereConfigList = Lists.newArrayList();
    NodeTable aggObjNodeTable = aggRuleDao.createAggNodeTable(standalone, aggApiName, aliasMapper);
    WhereRules whereRules = aggRuleDao.buildWheres(tenantId, standalone, themeApiName, aggApiName, ruleInfo, cachedTableDefinitions);
    if (whereRules != null) {
      ArrayList<ArrayList<WhereRule>> whereList = whereRules.getWhereRulesList();
      for (ArrayList<WhereRule> rules : whereList) {
        List<WhereConfig> whereConfigs = Lists.newArrayList();
        rules.forEach(wr -> {
          NodeTable nodeTable=  aggRuleDao.joinQuoteNodeTable(tenantId, standalone, aggObjNodeTable, aliasMapper, wr,true,false);
          WhereConfig whereConfig = new WhereConfig();
          whereConfig.setTableName(wr.tableName);
          whereConfig.setTableAlias(wr.alias);
          System.out.println("where sql is:"+wr.whereSQL(nodeTable,wr.tableName,wr.alias,timeZone));
          //whereConfigList.add(whereConfigs);
        });
      }
    }
  }

  @Test
  public void testStatView(){
//    "85145", "BI_6466eca700584b0001260b2c"
    String tenantId="82958";
    TopologyTable statView =new TopologyTable();
    statView.setViewId("BI_637faaf15d905600016a2b2c");
    statView.setApiName("stage_runtime_new_opportunity");
    statView.setTimezone("Asia/Shanghai");
    statView.setTenantId(tenantId);
//    List<String> ruleIds=Lists.newArrayList("BI_5bceda90dedd2c0001c2f54f","BI_5fcdcfed95b6610001f5608d");
    List<String> ruleIds = Lists.newArrayList("BI_63e7505d9ac2c70001872a67");
    List<TopologyTableAggRule> statRuleList = Lists.newArrayListWithCapacity(ruleIds.size());
    //客户维度
//    List<String> fieldIds = Lists.newArrayList("BI_0a5feb8c2265a84dfa73f612a2bba", "BI_478f358db03f53381a5de9a960c60", "BI_630899ddc0261800017047de", "BI_098990b4639238d5d817a3f7d306c", "BI_630899ddc026180001704841", "BI_630899ddc02618000170481d", "BI_5bcebcddcab2980001ee22ad", "BI_5bcebcddcab2980001ee22b8", "BI_5c271e05c8ca95336c03f9b9");
    //订单维度
//    List<String> fieldIds = Lists.newArrayList("BI_630899dec0261800017048b6");
//    List<DimRule> dimRules = dimRuleDao.queryDimField(tenantId, statView.getApiName(), fieldIds);
    Map<String, AtomicInteger> aggAliasMapper= Maps.newHashMap();
    Map<String, AtomicInteger> dimsMapper= Maps.newHashMap();
    Map<String, String/*dim字段别名*/> dimFieldAliasMapper = Maps.newHashMap();
    ruleIds.forEach(ruleId->{
//      StatRule statRule= aggRuleDao.parseFromMap(tenantId,ruleId,null,aggAliasMapper,dimsMapper,dimFieldAliasMapper);
//      statRuleList.add(statRule);
    });
    statView.setStatRuleList(statRuleList);
    System.out.println(JSON.toJSON(statView));
  }

  @Test
  public void testCompareView(){
    CheckDiffArg diffArg = new CheckDiffArg();
    diffArg.setTenantId("85145");
    diffArg.setViewId("BI_67499e03c34c1e0001d0dbc9");
//    diffArg.setFieldId("BI_5be3b1412563710001ab1b83");
    diffArg.setCheckBeginTime(1703174400000L);
    diffArg.setCheckEndTime(1732951055062L);
    Map<String, Object> result = compareService.compareView(diffArg);
    System.out.println("aggDiffDOList:" + JSON.toJSONString(result));
  }

  @Test
  public void testCompareByTenant() {
    CheckDiffArg diffArg = new CheckDiffArg();
    diffArg.setTenantIds(Lists.newArrayList("85529","85145"));
//    diffArg.setSchemaIds(Lists.newArrayList("85145:BI_5bcec11f56fc11160c8c8271,BI_634e8724b30eeb0001f9d611"));
//    diffArg.setSchemaIds(Lists.newArrayList("85529:BI_5bcebcdc3060e20001e79977^10"));
//    diffArg.setTenantIdsPercent(Lists.newArrayList("85145^1"));
    diffArg.setSourceType(1);
//    diffArg.setCommonViewIds(Lists.newArrayList("BI_5da14ad1be848a0001ffdc2d","BI_622ffdfd7bdad60001d93e83","BI_5de4fe433bc8d6000113f257"));
//    diffArg.setViewId("BI_62b909a9480b120001971b1b");
//    diffArg.setRuleId("BI_5be3b1412563710001ab1b83");
//    diffArg.setCheckBeginTime(1672502400000L);
//    diffArg.setCheckEndTime(1704038400000L);
//    diffArg.setViewIds(Lists.newArrayList("85145:BI_654b0055d8b366000168a409,BI_64f1563df15d9d0001d25ccd,BI_6377033a691b620001d84c9d","85529:BI_5ec3ac8d991f3900dwzdbabf,BI_5d6e294696564d00010744cc"));
    Map<String, Object> result = compareService.compareViewByTenantIds(diffArg);
    System.out.println("diffResult:" + JSON.toJSONString(result));
  }

  @Test
  public void testCheckStatus(){
    String tenantId="85145";
    String sourceId="BI_62b909a9480b120001971972";
    TopologyTableDO topologyTableDO= topologyTableDao.queryTopologyBySourceId( tenantId, sourceId);
    oldStatViewTopologyTransformer.checkStatus(tenantId,"BI_5cbd2b2cae225300016fa2ca",topologyTableDO);
  }
}
