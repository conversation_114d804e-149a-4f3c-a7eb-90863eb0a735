package com.fxiaoke.bi.warehouse.dws.stat.mapper;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableMergeDO;
import com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyTableMapper;
import com.fxiaoke.bi.warehouse.dws.db.mapper.TopologyTableMergeMapper;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTable;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableStatus;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.mapper.StatViewMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2023/7/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TopologyMapperTest {

  @Autowired
  TopologyTableMapper topologyTableMapper;
  @Autowired
  StatViewMapper statViewMapper;
  @Autowired
  TopologyTableMergeMapper topologyTableMergeMapper;

  @Test
  public void testCalculate(){
//    String tenantId="85145";
//    List<TopologyTableMergeDO> topologyTableMergeDOS= topologyTableMergeMapper.setTenantId(tenantId).findNeedCalculateTopologyList(tenantId);
//    System.out.println(JSON.toJSONString(topologyTableMergeDOS));
  }
  @Test
  public void testTopologyTableMerge(){
    List<TopologyTableDO> topologyTableDOList = topologyTableMapper.setTenantId("86369")
                                                                   .findCalcFromTableMergeList("86369",
                                                                     new int[] {TopologyTableStatus.Prepared.getValue(), TopologyTableStatus.Calculating.getValue()});
    topologyTableDOList.forEach(topologyTableDO -> {
      System.out.print("|status:"+topologyTableDO.getStatus());
      System.out.print("|version:"+topologyTableDO.getVersion());
      System.out.println("|batchNum:"+topologyTableDO.getBatchNum());
    });
  }
  @Test
  public void testDateTimeFilter(){
  List<StatViewDO> a=  statViewMapper.setTenantId("-1").batchQueryStatViewByEi(85145,"2023-07-01 00:00:00");
  System.out.println(a.size());
  }

  @Test
  public void testCreateUniqueKey(){
//      String sourceId="BI_659d2941e180160001ee03cb";
//      String tenantId="85145";
//      TopologyTableDO topologyTableDO= topologyTableMapper.setTenantId(tenantId).findByTenantIdAndSourceId(tenantId,sourceId);
//      TopologyTable.fromTopologyTableDO(topologyTableDO).createMd5Key();
////    'BI_65b4a24a0004ba00017b0d92','BI_659d2941e180160001ee03cb'
//    TopologyTableMergeDO topologyTableMergeDO= new TopologyTableMergeDO();
//    topologyTableMergeDO.setId(topologyTableDO.getStatViewUniqueKey());
//    topologyTableMergeDO.setTenantId(topologyTableDO.getTenantId());
//    topologyTableMergeDO.setVersion(1);
//    topologyTableMergeDO.setApiName(topologyTableDO.getApiName());
//    topologyTableMergeDO.setBatchNum(4L);
//    topologyTableMergeDO.setCommonDims(topologyTableDO.getCommonDims());
//    topologyTableMergeDO.setStatus(1);
//    topologyTableMergeDO.setAggEffectApiNames(topologyTableDO.getAggEffectApiNames());
//    topologyTableMergeDO.setSource(0);
//    topologyTableMergeDO.setIsDeleted(0);
//    topologyTableMergeDO.setCreateTime(new Date().getTime());
//    topologyTableMergeDO.setLastModifiedTime(new Date().getTime());
//    topologyTableMergeDO.setStatFieldLocation(topologyTableDO.getStatFieldLocation());
//    topologyTableMergeDO.setStatRuleList(topologyTableDO.getStatRuleList());
////    topologyTableMergeMapper.setTenantId(tenantId).upsertTopologyMergeInfo(Lists.newArrayList(topologyTableMergeDO),Sets.newHashSet("id","tenant_id"));
//    topologyTableMergeMapper.setTenantId(tenantId).upsertTopologyMergeWithVersion(0,topologyTableMergeDO,Map.of("id","id","tenant_id","tenantId"));
  }

  @Test
  public void testTopologyUpdate(){
    String sourceId="BI_654e0be86b253f0001ca9afd";
    String tenantId="85145";
    TopologyTableDO topologyTableDO= topologyTableMapper.setTenantId(tenantId).findByTenantIdAndSourceId(tenantId,sourceId);
    topologyTableDO.setLastModifiedTime(new Date().getTime());
    TopologyTable statView= TopologyTable.fromTopologyTableDO(topologyTableDO);
    Map<String, Set<String>> aggEffects = statView.findAggEffectApiNameMapper();
    topologyTableDO.setAggEffectApiNames(aggEffects);
    //    int result= topologyTableMapper.setTenantId(tenantId).upsertTopologyWithVersion(1, topologyTableDO);
    System.out.println(JSON.toJSONString(aggEffects));
    topologyTableMapper.setTenantId(tenantId)
                       .upsertTopologyInfo(Lists.newArrayList(topologyTableDO), Sets.newHashSet("tenant_id", "source_id"
                       ));
  }

  @Test
  public void testTopologyJSONB(){
    String fieldId="BI_5bcebcddcab2980001ee22b8";
    String tenantId="85145";
    List<TopologyTableDO> topologyTableDO= topologyTableMapper
      .setTenantId(tenantId).queryTopologyTableFromAllAgg(tenantId,new int[]{1,0},new String[]{"BI_64817f19da1237000139fb5b"});
    topologyTableDO.forEach(tt -> {
      System.out.println(tt.getStatFieldLocation());
    });
  }


  @Test
  public void testAggEffectUpdate(){
    String sourceId="BI_64bf38e6b8ee8c000177534c";
    String tenantId="85145";
    List<TopologyTableDO> topologyTableDOs = topologyTableMapper.setTenantId(tenantId)
                                                                .findNeedCalculateList(tenantId,
                                                                  new int[] {TopologyTableStatus.Calculating.getValue(), TopologyTableStatus.Prepared.getValue()});
    for(TopologyTableDO tableDO:topologyTableDOs){
      System.out.println(JSON.toJSONString(tableDO.getAggEffectApiNames()));
//      tableDO.setLastModifiedTime(new Date().getTime());
//      TopologyTable statView= TopologyTable.fromTopologyTableDO(tableDO);
//      int result = topologyTableMapper.setTenantId(tenantId).updateStatViewUniqueKey(tenantId,statView.getViewId(),statView.getVersion(),statView.createMd5Key());
//      if(result==0){
//        System.out.println(String.format("update error tenantId:%s,sourceId:%s",tenantId,statView.getViewId()));
//      }
//     Map<String, Set<String>> aggEffects = statView.findAggEffectApiNameMapper();
//     System.out.println(JSON.toJSONString(aggEffects));
//     topologyTableMapper.setTenantId(tenantId).updateEffectApiNames(tenantId,statView.getViewId(),statView.getVersion(),JSON.toJSONString(aggEffects));
    }
  }

  @Test
  public void testUpsert2(){
    TopologyTableMergeDO topologyTableMergeDO= new TopologyTableMergeDO();
    topologyTableMergeDO.setId("014859300e3da6fb8b3ea91af66d311b");
    topologyTableMergeDO.setStatus(1);
    topologyTableMergeDO.setVersion(0);
    topologyTableMergeDO.setCreateTime(new Date().getTime());
    topologyTableMergeDO.setLastModifiedTime(new Date().getTime());
    topologyTableMergeDO.setBatchNum(0L);
    topologyTableMergeDO.setLatestAggTime(0L);
    topologyTableMergeDO.setTenantId("85145");
    int result = topologyTableMergeMapper.setTenantId("85145").initTopologyTableMergeOrIncVersion(topologyTableMergeDO);
    System.out.println(result);
  }
}
