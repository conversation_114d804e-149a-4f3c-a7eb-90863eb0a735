package com.fxiaoke.bi.warehouse.ods.dataTest;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.AggRuleDO;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TestVirtualStatField {

    private List<StatFieldDO> statFieldDOList = Lists.newArrayList();

    @Before
    public void setUp() {
        String filePath1 = "/Users/<USER>/Desktop/1+N聚合数据同步指标详情.txt";
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath1))) {
            StringBuilder json = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                json.append(line);
            }
            statFieldDOList = JSON.parseArray(json.toString(), StatFieldDO.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCreateAggRule() {
        System.out.println(statFieldDOList);
    }

    @Test
    public void createVirtualAggRule() {
        String aggRuleSqlTemplate = """
          INSERT INTO agg_rule (tenant_id, rule_id, field_id, theme_api_name, display_name, create_time, creator,last_modified_time, last_modifier, is_deleted, type, description, time_zone)
          VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', %d, '%s', '%s', '%s')
          ON CONFLICT DO NOTHING;
          """;
        List<AggRuleDO> aggRuleDOList = statFieldDOList.stream().map(this::getSyncAggRule).toList();
        aggRuleDOList.forEach(x -> System.out.println(String.format(aggRuleSqlTemplate, x.getTenantId(), x.getRuleId(), x.getFieldId(), x.getThemeApiName(), x.getDisplayName(), x.getCreateTime(), x.getCreator(), x.getLastModifiedTime(), x.getLastModifier(), x.getIsDeleted(), x.getType(), x.getDescription(), "Asia/Shanghai")));
    }

    private AggRuleDO getSyncAggRule(StatFieldDO statFieldDO) {
        AggRuleDO aggRule = new AggRuleDO();
        aggRule.setRuleId("BI_" + IdGenerator.get());
        aggRule.setFieldId(statFieldDO.getFieldId());
        aggRule.setThemeApiName(statFieldDO.getObjectDescribeApiName());
        aggRule.setTenantId(statFieldDO.getTenantId());
        aggRule.setDisplayName(statFieldDO.getFieldName());
        aggRule.setCreateTime(statFieldDO.getCreateTime());
        aggRule.setCreator(String.valueOf(statFieldDO.getCreator()));
        aggRule.setLastModifiedTime(statFieldDO.getLastModifiedTime());
        aggRule.setLastModifier(statFieldDO.getLastModifier());
        aggRule.setIsDeleted(statFieldDO.getIsDeleted());
        aggRule.setType(statFieldDO.getAggDimType());
        aggRule.setDescription("");
        return aggRule;
    }
}
