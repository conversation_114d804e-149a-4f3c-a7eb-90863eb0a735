package com.fxiaoke.bi.warehouse.ods.integreate.dao;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.BIMtTopologyTableDao;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.TopologyTableIntegrateDO;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author:jief
 * @Date:2024/4/23
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TestBiMtTopologyTableDao {

  @Resource
  private BIMtTopologyTableDao biMtTopologyTableDao;

  @Test
  public void testQueryAllDownstreamTables() {
    List<TopologyTableIntegrateDO> tables= biMtTopologyTableDao.queryAllDownstreamTopologyTables("82958");
    System.out.println(JSON.toJSONString(tables));
  }

  @Test
  public void testBatchQueryFieldLocationByViewId() {
    List<TopologyTableIntegrateDO> topologyTableIntegrateDOS= biMtTopologyTableDao
      .batchQueryFieldLocationByViewId("82958", Lists.newArrayList("BI_660d0b5c35b5d6000167a6d7"));
    System.out.println(JSON.toJSONString(topologyTableIntegrateDOS));
  }
}
