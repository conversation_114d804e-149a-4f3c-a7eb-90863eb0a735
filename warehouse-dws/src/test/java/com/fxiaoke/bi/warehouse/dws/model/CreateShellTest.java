package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import junit.framework.TestCase;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;

import java.io.*;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2024/10/30
 */

public class CreateShellTest extends TestCase {
  @Test
  public void testCheckTTL() throws Exception{
    File file = ResourceUtils.getFile("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\foneshare_db_sync_info.js");
    String statFieldLocationJson = Files.readString(file.toPath());
    JSONArray jsonArray = JSON.parseArray(statFieldLocationJson);
    File outPutFile = new File("D:\\常用脚本\\clickhouse帮助目录\\公共库脚本\\foneshare_check_ttl.sh");
    if(outPutFile.exists()){
      outPutFile.delete();
    }
    BufferedWriter bufferedWriter=new BufferedWriter(new FileWriter(outPutFile));
    jsonArray.forEach(chDB->{
      try{
        JSONObject jsonObject=(JSONObject)chDB;
        String chURL = jsonObject.getString("ch_db");
        String ip = CommonUtils.extractIpFromJdbcUrl(chURL);
        String dbName = CommonUtils.getDBName(chURL);
        String echo1 = "echo \""+chURL+"\" >> logs/ttl.txt";
//        String echo2 = String.format(curl,dbName,dbName.substring(0, dbName.length() - 3),ip);
        String echo3 = "echo \"select name  from system.tables where database ='"+dbName+"' and  create_table_query like '%AND (bi_sys_flag OR%';\" | curl 'http://fs_chdb_b_u_bi_aggcalc_"+dbName.substring(0, dbName.length() - 3)+":Ht4Vh9Wvs1U2Rh8Br8N@"+ip+":9090/' --data-binary @-  >> logs/ttl.txt";
//        System.out.println(echo3);
        bufferedWriter.write(echo1);
        bufferedWriter.write("\n");
        bufferedWriter.write(echo3);
        bufferedWriter.write("\n");
      }catch (Exception e){
        e.printStackTrace();
      }
    });
    bufferedWriter.flush();
    bufferedWriter.close();
  }
  @Test
  public void testCheckTable()throws Exception{
    File file = ResourceUtils.getFile("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\foneshare_db_sync_info_1.js");
    String statFieldLocationJson = Files.readString(file.toPath());
    JSONArray jsonArray = JSON.parseArray(statFieldLocationJson);
    File outPutFile = new File("D:\\常用脚本\\clickhouse帮助目录\\公共库脚本\\foneshare_check_table.sh");
    if(outPutFile.exists()){
      outPutFile.delete();
    }
    BufferedWriter bufferedWriter=new BufferedWriter(new FileWriter(outPutFile));
    jsonArray.forEach(chDB->{
      try{
        JSONObject jsonObject=(JSONObject)chDB;
        String chURL = jsonObject.getString("ch_db");
        String ip = CommonUtils.extractIpFromJdbcUrl(chURL);
        String dbName = CommonUtils.getDBName(chURL);
        String echo1 = "echo \""+chURL+"\" >> logs/check_tables.txt";
        //        String echo2 = String.format(curl,dbName,dbName.substring(0, dbName.length() - 3),ip);
        String echo3 = "echo \"select count()  from system.tables where database ='"+dbName+"' and table in('biz_user_api_name_operation','biz_user_login_online_operation','biz_user_bi_operation');\" | curl 'http://fs_chdb_b_u_bi_aggcalc_"+dbName.substring(0, dbName.length() - 3)+":Ht4Vh9Wvs1U2Rh8Br8N@"+ip+":9090/' --data-binary @- >> logs/check_tables.txt";
        //        System.out.println(echo3);
        bufferedWriter.write(echo1);
        bufferedWriter.write("\n");
        bufferedWriter.write(echo3);
        bufferedWriter.write("\n");
      }catch (Exception e){
        e.printStackTrace();
      }
    });
    bufferedWriter.flush();
    bufferedWriter.close();
  }

  @Test
  public void testRepairTTL()throws Exception{
    File file = ResourceUtils.getFile("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\foneshare_whithout_del_ttl.txt");
    BufferedReader reader = new BufferedReader(new FileReader(file));
    File outPutFile = new File("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\fonesahre_repair_ttl.sh");
    if(outPutFile.exists()){
      outPutFile.delete();
    }
    Map<String,Set<String>> urlTableMapper= Maps.newHashMap();
    BufferedWriter bufferedWriter=new BufferedWriter(new FileWriter(outPutFile));
    String jdbCURL= null;
    String line = null;
    while ((line = reader.readLine()) != null) {
      if(line.contains("jdbc")){
        jdbCURL= line;
        continue;
      }
      urlTableMapper.computeIfAbsent(jdbCURL,key->Sets.newHashSet()).add(line);
    }
    urlTableMapper.forEach((chUrl,tables)->{
      String ip = CommonUtils.extractIpFromJdbcUrl(chUrl);
      String dbName = CommonUtils.getDBName(chUrl);
      String userName= "fs_chdb_b_u_bi_aggcalc_"+dbName.substring(0, dbName.length() - 3);
      tables.forEach(tableName -> {
        try {
          if(tableName.contains("__del_")){
            return;
          }
          String echo1 = "echo \"ALTER TABLE " + dbName + "." + tableName +
            " ON CLUSTER '{cluster}' MODIFY TTL bi_sys_version + toIntervalMonth(1) WHERE (bi_sys_ods_part = 's') AND ((bi_sys_flag = 0) OR (is_deleted IN (-1, -2))), bi_sys_version + toIntervalWeek(1) WHERE bi_sys_ods_part in('i','c');\" | curl 'http://" +
            userName + ":Ht4Vh9Wvs1U2Rh8Br8N@" + ip + ":9090/' --data-binary @-";
          bufferedWriter.write(echo1);
          bufferedWriter.write("\n");
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      });
    });
    bufferedWriter.flush();
    bufferedWriter.close();
  }

  @Test
  public void testDeleteDbTableSyncInfoTTL()throws Exception{
    File dbSyncInfo = ResourceUtils.getFile("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\foneshare_db_sync_info.js");
    String statFieldLocationJson = Files.readString(dbSyncInfo.toPath());
    JSONArray jsonArray = JSON.parseArray(statFieldLocationJson);
    Map<String,Set<String>> chDbIdMapper = Maps.newHashMap();
    jsonArray.stream().map(item-> (JSONObject) item).forEach(info->{
      String chDB = info.getString("ch_db");
      chDbIdMapper.computeIfAbsent(chDB,key->Sets.newHashSet()).add(info.getString("id"));
    });
    File file = ResourceUtils.getFile("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\foneshare_whithout_del_ttl.txt");
    BufferedReader reader = new BufferedReader(new FileReader(file));
    File outPutFile = new File("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\foneshare_delete_db_table_sync_info.sh");
    if(outPutFile.exists()){
      outPutFile.delete();
    }
    BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(outPutFile));
    Map<String,Set<String>> idTablesMap= Maps.newHashMap();
    String jdbCURL= null;
    String line = null;
    while ((line = reader.readLine()) != null) {
      if(line.contains("jdbc")){
        jdbCURL= line;
        continue;
      }
      String tableName = line;
      Set<String>  ids = chDbIdMapper.get(jdbCURL);
      if(CollectionUtils.isEmpty(ids)){
        throw new RuntimeException(jdbCURL+"can not find db syncId");
      }
      ids.forEach(id->{
        idTablesMap.computeIfAbsent(id,key-> Sets.newHashSet()).add(tableName);
      });
    }
    if(!idTablesMap.isEmpty()){
      idTablesMap.forEach((k,v)->{
        try{
          String curl="curl -X POST --location \"http://localhost/warehouse-ods/batchDelDbTableSyncInfo\"  -H \"Content-Type: application/json\"  -d '{\"dbSyncIds\":[\""+k+"\"],\"tables\":"+
            JSON.toJSONString(v)+"}'";
          bufferedWriter.write(curl);
          bufferedWriter.write("\n");
        }catch (Exception e){
          throw new RuntimeException(e);
        }
      });
    }
    bufferedWriter.flush();
    bufferedWriter.close();
  }


  @Test
  public void testRepairAgg()throws Exception{
    File dbSyncInfo = ResourceUtils.getFile("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\foneshare_db_sync_info_1.js");
    String statFieldLocationJson = Files.readString(dbSyncInfo.toPath());
    JSONArray jsonArray = JSON.parseArray(statFieldLocationJson);
    Map<String,String> dbEIMapper = jsonArray.stream().map(item-> (JSONObject) item).collect(Collectors.toMap(info->info.getString("ch_db"),info->info.getString("last_sync_eis")));
    File file = ResourceUtils.getFile("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\foneshare_whithout_del_ttl.txt");
    BufferedReader reader = new BufferedReader(new FileReader(file));
    File outPutFile = new File("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\foneshare_repair_agg_data.sql");
    if(outPutFile.exists()){
      outPutFile.delete();
    }
    BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(outPutFile));
    Map<String,Set<String>> idTablesMap= Maps.newHashMap();
    String jdbCURL= null;
    String line = null;
    while ((line = reader.readLine()) != null) {
      if(line.contains("jdbc")){
        jdbCURL= line;
        continue;
      }
      String tableName=line;
      String  ei = dbEIMapper.get(jdbCURL);
      if(StringUtils.isBlank(ei)){
        throw new RuntimeException(jdbCURL+"can not find db syncId");
      }
      idTablesMap.computeIfAbsent(ei,key-> Sets.newHashSet()).add(tableName);
    }
    if(!idTablesMap.isEmpty()){
      idTablesMap.forEach((k,v)->{
        try{
         String orSQL = v.stream().map(tb->"view_sql like '%"+tb+"%'").collect(Collectors.joining(" OR "));
          String sql="select source_id from bi_mt_topology_table where tenant_id='"+k+"' and is_deleted=0 and status=1 and ("+orSQL+")";
          bufferedWriter.write(sql);
          bufferedWriter.write("\n");
        }catch (Exception e){
          throw new RuntimeException(e);
        }
      });
    }
    bufferedWriter.flush();
    bufferedWriter.close();
  }
  /**
   *
   * @throws Exception
   */
  @Test
  public void testCreateArgs() throws Exception {
    String url="curl -X POST http://localhost/warehouse-dws/batchCreateAggTopology  -H 'Cache-Control: no-cache' -H 'Content-Type: application/json' -d '%s'";
    File file = ResourceUtils.getFile("classpath:stat/tenantId_view.txt");
    BufferedReader reader = new BufferedReader(new FileReader(file));
    File outPutFile = new File("D:\\tmp\\logs\\flush.txt");
    if(outPutFile.exists()){
      outPutFile.delete();
    }
    outPutFile.createNewFile();
    BufferedWriter bufferedWriter=new BufferedWriter(new FileWriter(outPutFile));
    String line = null;
    Map<String, Set<String>> eiAndViewIdsMapper = Maps.newHashMap();
    while ((line = reader.readLine()) != null) {
      if(line.contains("jdbc")){
        List<StatViewBatchArg> statViewBatchArgs = Lists.newArrayList();
        eiAndViewIdsMapper.forEach((k, v) -> {
          StatViewBatchArg statViewBatchArg = new StatViewBatchArg();
          List<StatViewPreArg> statViewArgList = Lists.newArrayList();
          statViewBatchArg.setTenantId(k);
          v.forEach(viewId -> {
            //            statViewArgList.add(StatViewPreArg);
          });
          //          statViewBatchArg.setStatViewArgList(statViewArgList);
          statViewBatchArgs.add(statViewBatchArg);
        });
        statViewBatchArgs.forEach(args->{
          try{
            bufferedWriter.write(String.format(url, JSON.toJSONString(args)));
            bufferedWriter.newLine();
          }catch (Exception e){
            throw new RuntimeException(e);
          }
        });
        bufferedWriter.write(JSON.toJSONString(eiAndViewIdsMapper.keySet()));
        bufferedWriter.newLine();
        bufferedWriter.write(line);
        bufferedWriter.newLine();
        eiAndViewIdsMapper.clear();
        continue;
      }
      String[] eiAndViewId = line.split(",");
      if(eiAndViewId.length<2){
        System.out.println(line);
        continue;
      }
      if(StringUtils.isNotBlank(eiAndViewId[1])){
        eiAndViewIdsMapper.computeIfAbsent(eiAndViewId[0], key -> Sets.newHashSet()).add(eiAndViewId[1]);
      }
    }
    reader.close();
    List<StatViewBatchArg> statViewBatchArgs = Lists.newArrayList();
    eiAndViewIdsMapper.forEach((k, v) -> {
      StatViewBatchArg statViewBatchArg = new StatViewBatchArg();
      List<StatViewPreArg> statViewArgList = Lists.newArrayList();
      statViewBatchArg.setTenantId(k);
      v.forEach(viewId -> {
        //        statViewArgList.add(StatViewPreArg.builder().sourceId(viewId).build());
      });
      //      statViewBatchArg.setStatViewArgList(statViewArgList);
      statViewBatchArgs.add(statViewBatchArg);
    });
    statViewBatchArgs.forEach(args->{
      try{
        bufferedWriter.write(String.format(url,JSON.toJSONString(args)));
        bufferedWriter.newLine();
      }catch (Exception e){
        throw new RuntimeException(e);
      }
    });
    bufferedWriter.write(JSON.toJSONString(eiAndViewIdsMapper.keySet()));
    bufferedWriter.newLine();
    bufferedWriter.flush();
    bufferedWriter.close();
  }

  /**
   *
   * @throws Exception
   */
  @Test
  public void testCreateTable() throws Exception {
    String url="curl -X POST --location \"http://localhost/warehouse-dws/createChTableSQL\" -H \"Content-Type: application/json\" -d '{\"tenantId\": \"%s\",\"pgDbUrl\":\"%s\",\"chCluster\":\"{cluster}\",\"tableEngine\":\"ReplicatedReplacingMergeTree\",\"schemaName\":\"%s\",\"chDBName\":\"%s\",\"needCreate\":true,\"tables\":[\"v_saleactionstage\"]}'";
    File file = ResourceUtils.getFile("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\create_table_db_sync_info.js");
    String statFieldLocationJson = Files.readString(file.toPath());
    JSONArray jsonArray = JSON.parseArray(statFieldLocationJson);
    File outPutFile = new File("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\v_saleactionstage.sh");
    if(outPutFile.exists()){
      outPutFile.delete();
    }
    outPutFile.createNewFile();
    BufferedWriter bufferedWriter=new BufferedWriter(new FileWriter(outPutFile));
    jsonArray.forEach(jsonObj->{
      try{
        JSONObject dbSyncInfo = (JSONObject) jsonObj;
        String curl = String.format(url,dbSyncInfo.getString("string_to_array"),dbSyncInfo.getString("pg_db"),dbSyncInfo.getString("pg_schema"),dbSyncInfo.getString("ch_db"));
        bufferedWriter.write(curl);
        bufferedWriter.write("\n");
      }catch (Exception e){
        throw new RuntimeException(e);
      }
    });
    bufferedWriter.flush();
    bufferedWriter.close();
  }

}
