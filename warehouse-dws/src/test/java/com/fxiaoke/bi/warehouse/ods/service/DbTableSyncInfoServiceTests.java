package com.fxiaoke.bi.warehouse.ods.service;

import com.fxiaoke.bi.warehouse.ServerApplication;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author:jief
 * @Date:2024/10/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class DbTableSyncInfoServiceTests {

  @Resource
  private DbTableSyncInfoService dbTableSyncInfoService;

  @Test
  public void testDeleteTableSyncInfoByDbSyncId() {
    List<String> dbSyncIds= Lists.newArrayList("64ee0f5dbfbe303944dd65b6");
    List<String> tables = Lists.newArrayList("object_t1703056186354__c");
    dbTableSyncInfoService.deleteTableSyncInfoByDbSyncId(dbSyncIds, tables);
  }

}
