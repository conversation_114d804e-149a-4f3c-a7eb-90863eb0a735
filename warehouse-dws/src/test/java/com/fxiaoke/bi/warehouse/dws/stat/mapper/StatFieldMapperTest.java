package com.fxiaoke.bi.warehouse.dws.stat.mapper;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.StatFieldMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class StatFieldMapperTest {

  @Autowired
  private StatFieldMapper statFieldMapper;

  @Test
  public void testQueryStatField() {
//    List<StatFieldDO> statFieldDO= statFieldMapper.setTenantId("-1").queryStatField(new String[]{"BI_623827c777c691000199ff53"}, "71570");
//    Assert.assertNotNull(statFieldDO);
  }

  @Test
  public void testQueryStatFieldByIdsPlus1(){
    String tenantId="71570";
    String schemaId="BI_af35f7e07459c8e7a02942f6e65c2";
    String apiName="biz_sales_order_product";
    String fields ="'action_date','created_by','actual_unit'";
    List<StatFieldDO> statFieldDOS = statFieldMapper.setTenantId(tenantId).queryDimFieldByFieldNamePlus(tenantId,fields,schemaId);
    System.out.println(JSON.toJSONString(statFieldDOS));
  }

  @Test
  public void testQueryStatFieldByIdsPlus2(){
//    String tenantId="71570";
//    String schemaId="BI_af35f7e07459c8e7a02942f6e65c2";
//    String apiName="biz_sales_order_product";
//    String fields ="'BI_a0d420a8ad99eaee9f7fe7e29b263','BI_e9bd5e85bb97777df188825848975','BI_e0339ef9c22b7426f676806346b3a'";
//    List<StatFieldDO> statFieldDOS = statFieldMapper.setTenantId(tenantId).queryDimFieldByFieldIdsPlus(tenantId,fields,schemaId);
//    System.out.println(JSON.toJSONString(statFieldDOS));
  }

}