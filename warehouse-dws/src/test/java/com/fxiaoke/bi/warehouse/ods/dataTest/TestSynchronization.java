package com.fxiaoke.bi.warehouse.ods.dataTest;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.model.BIAggSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.model.IntegrateEvent;
import com.fxiaoke.bi.warehouse.core.db.BiAggSyncInfoDao;
import com.fxiaoke.bi.warehouse.ods.integrate.service.AggDownStreamService;
import com.fxiaoke.bi.warehouse.ods.integrate.service.BiDataSyncPolicyService;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.IntegrateServiceImpl;
import com.github.jedis.support.JedisCmd;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TestSynchronization {

    @Resource
    private IntegrateServiceImpl iIntegrateService;

    @Resource
    private AggDownStreamService aggDownStreamService;
    @Resource
    private BiAggSyncInfoDao biAggSyncInfoDao;

    @Resource
    private BiDataSyncPolicyService biDataSyncPolicyService;

    @Resource(name = "jedisFactory")
    private JedisCmd jedisCmd;

    @Resource
    private PgCommonDao pgCommonDao;

    @Test
    public void test() {
        jedisCmd.del("bi:integrate:82958");
        IntegrateEvent integrateEvent = new IntegrateEvent();
        integrateEvent.setTenantId("82958");
//        iIntegrateService.consumeIntegrateEvent(integrateEvent);
    }

    @Test
    public void testAggDownStreamData() {
        BIAggSyncInfoDO biAggSyncInfoDO = biAggSyncInfoDao.queryAggSyncByEi("82958");
        BIAggSyncInfoBO biAggSyncInfoBO = BIAggSyncInfoBO.from(biAggSyncInfoDO);
//        aggDownStreamService.synchronizeDownstreamAggData(biAggSyncInfoBO, 11734);
    }

    @Test
    public void testSyncAggDownStreamDataByPolicy() {
        DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo("***************************************************", "***********************************************", "sch_82958");
        BIAggSyncInfoDO biAggSyncInfoDO = biAggSyncInfoDao.queryAggSyncByEi("82958");
        BIAggSyncInfoBO biAggSyncInfoBO = BIAggSyncInfoBO.from(biAggSyncInfoDO);
        DBSyncInfoBO dbSyncInfoBO = DBSyncInfoBO.createInstanceOf(dbSyncInfo);
//        dbSyncInfo.getBatchNum()+1L
        biDataSyncPolicyService.synchronizeDownstreamAggData(biAggSyncInfoBO,  dbSyncInfo.getBatchNum()+1L, dbSyncInfoBO,"s");
    }
    @Test
    public void testFindAllDownStreamView(){
       Map<String, Set<String>> result= biDataSyncPolicyService.findAllDownStreamView(Lists.newArrayList("82958"));
        System.out.println(JSON.toJSON(result));
    }
    @Test
    public void testCheckDataSyncPolicyViewStatus(){
        String tenantId="82958";
        String policyId="BI_673737e962399800011b4bbf";
        biDataSyncPolicyService.checkDataSyncPolicyViewStatus(tenantId, policyId);
    }
}
