package com.fxiaoke.bi.warehouse.ods.dataTest;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.mapper.AggDataSyncInfoMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class AggDataSyncInfoTest {

    @Resource
    private AggDataSyncInfoMapper aggDataSyncInfoMapper;

    @Test
    public void test() {
        AggDataSyncInfoDo aggDataSyncInfoDo = new AggDataSyncInfoDo();
        aggDataSyncInfoDo.setTenantId("90502");
        aggDataSyncInfoDo.setViewId("BI_660b82180e96bd0001367a07");
        aggDataSyncInfoDo.setViewVersion(0);
        aggDataSyncInfoDo.setBatchNum(1L);
        aggDataSyncInfoDo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
        aggDataSyncInfoDo.setMaxSyncTimeStamp(new Date().getTime());
        aggDataSyncInfoDo.setTimestamp(new Date());
        aggDataSyncInfoDo.setIsDeleted(0);
        aggDataSyncInfoMapper.setTenantId("90502").insertAggDataSyncInfo(aggDataSyncInfoDo);
    }
}
