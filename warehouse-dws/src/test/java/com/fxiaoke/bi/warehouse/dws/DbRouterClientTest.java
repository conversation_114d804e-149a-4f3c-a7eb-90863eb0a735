package com.fxiaoke.bi.warehouse.dws;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.google.common.collect.Sets;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 测试 DbRouterClient
 */

public class DbRouterClientTest extends TestCase {

  @Test
  public void testRoute() throws Exception {
    Set<String> pgs= Sets.newHashSet("***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************","***************************************************");
    Set<String> pgSets = pgs.stream().map(CommonUtils::getDBName).collect(Collectors.toSet());
    //受影响得
    File route = ResourceUtils.getFile("C:\\Users\\<USER>\\Downloads\\route.txt");
    String aeaJson = Files.readString(route.toPath());
    JSONObject aggEffectApiNames = JSON.parseObject(aeaJson);
    JSONObject result = aggEffectApiNames.getJSONObject("result");
    Map<String, List<String>> grayMap=new HashMap<>();
    result.getInnerMap().forEach((k,v)->{
//      System.out.println(k);
      if(pgSets.contains(CommonUtils.getDBName(k))){
        grayMap.put(k,(List<String>)v);
      }
    });
    System.out.println(JSON.toJSONString(grayMap));
  }

}
