package com.fxiaoke.bi.warehouse.ods.service;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseNodeInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TestMergeService {
    @Resource
    private MergeTaskService mergeTaskService;

    @Test
    public void testHasAggCalculatingInProgress(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                .database("fsbidb044003001")
                .jdbcUrl("***************************************************")
                .cluster("{cluster}")
                .build();
        boolean bOk = mergeTaskService.hasAggCalculatingInProgress(clickhouseNodeInfo);
        System.out.println(String.format("testHasAggCalculatingInProgress: %s", bOk));
    }

    @Test
    public void testIsNeedMerge(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                .database("fsbidb044003001")
                .jdbcUrl("***************************************************")
                .cluster("{cluster}")
                .build();
        boolean bOk = mergeTaskService.isNeedMerge(clickhouseNodeInfo);
        System.out.println(String.format("testIsNeedMerge: %s", bOk));
    }

    @Test
    public void testGetAggDistinctCount(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                .database("fsbidb044003001")
                .jdbcUrl("***************************************************")
                .cluster("{cluster}")
                .build();
        long lRet = mergeTaskService.getAggDistinctCount(clickhouseNodeInfo);
        System.out.println(String.format("testGetAggDistinctCount: %d", lRet));
    }

    @Test
    public void testMergeAll(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                .database("fsbidb044003001")
                .jdbcUrl("***************************************************")
                .cluster("{cluster}")
                .build();
        boolean bOk = mergeTaskService.merge(clickhouseNodeInfo, 5000000, 6000000,false, false);
        System.out.println(String.format("test merge all: %s", bOk));
    }

    @Test
    public void testMergeIncrement(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                .database("fsbidb044003001")
                .jdbcUrl("***************************************************")
                .cluster("{cluster}")
                .build();
        boolean bOk = mergeTaskService.merge(clickhouseNodeInfo, 5000000, 6000000,true, true);
        System.out.println(String.format("test merge all: %s", bOk));
    }

    @Test
    public void testCreatePartitionQuantileSqlList(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                .database("fsbidb044003001")
                .jdbcUrl("***************************************************")
                .cluster("{cluster}")
                .build();
        String partition = mergeTaskService.getMinPartition(clickhouseNodeInfo, "agg_data");
        List<String> sqlList = mergeTaskService.createPartitionQuantileSqlList(clickhouseNodeInfo,1000000 ,partition);
        for(String sql: sqlList){
            System.out.println(String.format("testCreatePartitionQuantileSqlList: %s",sql));
        }
    }

    @Test
    public void testNewMergeAggDataTable(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                .database("fsbidb044003001")
                .jdbcUrl("***************************************************")
                .cluster("{cluster}")
                .build();
        boolean bOk = mergeTaskService.newMerge(clickhouseNodeInfo, 1000000, 6000000);
        System.out.println(String.format("testNewMergeAggDataTable: %s", bOk));
    }

    @Test
    public void testSwapAggDataAggDataSwapByRename(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                .database("fsbidb044003002")
                .jdbcUrl("***************************************************")
                .cluster("{cluster}")
                .build();
        boolean bOk = mergeTaskService.swapAggDataAggDataSwapByRename(clickhouseNodeInfo);
        System.out.println(String.format("testSwapAggDataAggDataSwapByRename: %s", bOk));
    }

    @Test
    public void testDropAndCreateAggSwapTable(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                .database("fsbidb044003002")
                .jdbcUrl("***************************************************")
                .cluster("{cluster}")
                .build();
        mergeTaskService.dropAndCreateAggSwapTable(clickhouseNodeInfo, true);
    }
    @Test
    public void testShouldExchangeTable(){
        ClickhouseNodeInfo clickhouseNodeInfo = ClickhouseNodeInfo.builder()
                                                                  .database("fsbidb044003001")
                                                                  .jdbcUrl("***************************************************")
                                                                  .cluster("{cluster}")
                                                                  .build();
        System.out.println(mergeTaskService.shouldExchangeTable(clickhouseNodeInfo,false));
    }
}

