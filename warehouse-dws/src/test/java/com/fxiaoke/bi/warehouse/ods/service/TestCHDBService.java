package com.fxiaoke.bi.warehouse.ods.service;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Maps;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * @Author:jief
 * @Date:2024/4/15
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TestCHDBService {
  @Resource
  CHDBService chdbService;
  @Resource
  CHMetadataService chMetadataService;
  @Resource
  private CHDataSource chDataSource;

  @Test
  public void testInsertSQL(){
    String tenantId="85145";
    String fromTable="org_employee_user";
    long batchNum=1000L;
    StopWatch stopWatch= StopWatch.createStarted("begin");
    String chJdbcUrl=chDataSource.findChURLByEi(tenantId);
    Optional<ClickhouseTable> fromClickhouseTableOP = chMetadataService.loadTableFromDB(chJdbcUrl, fromTable);
    Optional<ClickhouseTable> toClickhouseTableOP = chMetadataService.loadTableFromDB(chJdbcUrl, "org_employee_user_downstream");
    Pair<Long, Long> sysTimeRange= Pair.build(null,1000000000L);
    chdbService.insertBizData(Lists.newArrayList(tenantId),fromClickhouseTableOP.get(),toClickhouseTableOP.get(),batchNum,sysTimeRange,stopWatch,null);
    System.out.println(stopWatch.prettyPrint());
  }

  @Test
  public void testBeforeSQL(){
    String tenantId="82958";
    String fromTable="biz_account";
    long batchNum=1000L;
    StopWatch stopWatch= StopWatch.createStarted("begin");
    String chJdbcUrl=chDataSource.findChURLByEi(tenantId);
    Optional<ClickhouseTable> fromClickhouseTableOP = chMetadataService.loadTableFromDB(chJdbcUrl, fromTable);
    Optional<ClickhouseTable> toClickhouseTableOP = chMetadataService.loadTableFromDB(chJdbcUrl, "biz_account_downstream");
    Pair<Long, Long> sysTimeRange= Pair.build(null,1000000000L);
    chdbService.insertBeforeData( tenantId, fromClickhouseTableOP.get(), toClickhouseTableOP.get(), batchNum, sysTimeRange,
     stopWatch,"");
    System.out.println(stopWatch.prettyPrint());
  }
//**************************************************
  @Test
  public void testRoute(){
//    Map<Integer, String> eiToEaMap = Maps.newHashMap();
//    eiToEaMap.put("77996","77996");
//    chMetadataService.createChRoute("77996","**************************************************");
//    System.out.println(chDataSource.findChURLByEi("77996"));
//    System.out.println(chDataSource.findChURLByEi("78060"));
  }
}
