package com.fxiaoke.bi.warehouse.ods.dataTest;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.BiAggSyncInfoMapper;
import com.fxiaoke.bi.warehouse.core.db.mapper.StatFieldMapper;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.entity.AggDataSyncInfoDo;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.mapper.AggDataSyncInfoMapper;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.*;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BiDataSyncPolicyDo;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper.BizEnterpriseRelationMapper;
import com.fxiaoke.bi.warehouse.ods.integrate.service.DataSyncLicenseService;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.AggDataSyncInfoServiceImpl;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
@Slf4j
public class DataTest {

    @Resource
    private AggDataSyncInfoMapper aggDataSyncInfoMapper;

    @Resource
    private BiAggSyncInfoMapper biAggSyncInfoMapper;

    @Resource
    private BizEnterpriseRelationMapper bizEnterpriseRelationMapper;

    @Resource
    private AggDataSyncInfoServiceImpl aggDataSyncInfoService;

    @Resource
    private DbRouterClient dbRouterClient;

    @Resource
    private CHDataSource chDataSource;
    @Resource
    private CHRouterPolicy chRouterPolicy;

    @Resource
    private StatFieldMapper statFieldMapper;

    @Resource
    private DataSyncLicenseService dataSyncLicenseService;

    @Test
    public void testHashMerge() {
        String json = """
          [{"tenant_id":"802931","last_modified_time":1731997584450,"policy_id":"BI_673c2f90260bd9000170ca58","create_time":1731080022933,"policy_name":"1108策略","data_source_enterprise":"[{\\"id\\":\\"6729f96239fddd00015f0e6a\\",\\"type\\":\\"tg\\"}]","agg_mapping_rule":"[{\\"aggMappingList\\":[{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672ddc6cf86c350001197387\\",\\"downFieldName\\":\\"1108-客户名称\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656b8\\",\\"upFieldName\\":\\"1108-客户名称\\"},{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_672ddc89a9d57800015f520b\\",\\"downFieldName\\":\\"1108-订单金额\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656b9\\",\\"upFieldName\\":\\"1108-订单金额\\"},{\\"aggType\\":\\"uniq\\",\\"downFieldId\\":\\"BI_672dde51f86c3500011973d2\\",\\"downFieldName\\":\\"1108-完成拜访客户数\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656ba\\",\\"upFieldName\\":\\"1108-完成拜访客户数\\"},{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672dde624d7cc60001d531b8\\",\\"downFieldName\\":\\"1108-外勤总次数\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656bb\\",\\"upFieldName\\":\\"1108-外勤总次数\\"},{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_672dde8b8bd5260001316a70\\",\\"downFieldName\\":\\"1108-商机金额\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656bc\\",\\"upFieldName\\":\\"1108-商机金额\\"}],\\"viewId\\":\\"BI_672ddfc4b8475400013126fb\\",\\"viewName\\":\\"1108-客户\\"},{\\"aggMappingList\\":[{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672ddec38bd5260001316a7a\\",\\"downFieldName\\":\\"人员-1108-客户数\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656bd\\",\\"upFieldName\\":\\"人员-1108-客户数\\"},{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672ddee2a9d57800015f5258\\",\\"downFieldName\\":\\"人员-1108-员工总数\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656be\\",\\"upFieldName\\":\\"人员-1108-员工总数\\"},{\\"aggType\\":\\"uniq\\",\\"downFieldId\\":\\"BI_672ddf018bd5260001316a8e\\",\\"downFieldName\\":\\"人员-1108-外勤拜访客户数\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656bf\\",\\"upFieldName\\":\\"人员-1108-外勤拜访客户数\\"},{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672ddf0ff86c3500011973f2\\",\\"downFieldName\\":\\"人员-1108-外勤总数\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656c0\\",\\"upFieldName\\":\\"人员-1108-外勤总数\\"},{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_672ddf2ca9d57800015f526c\\",\\"downFieldName\\":\\"人员-1108-订单金额\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656c1\\",\\"upFieldName\\":\\"人员-1108-订单金额\\"},{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_672de00ef86c350001197414\\",\\"downFieldName\\":\\"人员-1108-商机金额\\",\\"upFieldId\\":\\"BI_672e2f56cff55a00014656c2\\",\\"upFieldName\\":\\"人员-1108-商机金额\\"}],\\"viewId\\":\\"BI_672de054b847540001312759\\",\\"viewName\\":\\"1105-人员\\"}]","last_modified_by":"1000","create_by":"1000","sync_stat_schema_id":"BI_5bcebcdc3060e20001e79977","policy_desc":"1108建立的策略","is_deleted":0,"status":1},{"tenant_id":"802931","last_modified_time":1731997643494,"policy_id":"BI_673c2fc82049060001320427","create_time":1731997640569,"policy_name":"测试","data_source_enterprise":"[{\\"id\\":\\"301943076\\",\\"type\\":\\"t\\"},{\\"id\\":\\"301943075\\",\\"type\\":\\"t\\"}]","agg_mapping_rule":"[{\\"aggMappingList\\":[{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672ddc6cf86c350001197387\\",\\"downFieldName\\":\\"1108-客户名称\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc82049060001320428\\",\\"upFieldName\\":\\"1108-客户名称1\\"},{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_672ddc89a9d57800015f520b\\",\\"downFieldName\\":\\"1108-订单金额\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc82049060001320429\\",\\"upFieldName\\":\\"1108-订单金额1\\"},{\\"aggType\\":\\"uniq\\",\\"downFieldId\\":\\"BI_672dde51f86c3500011973d2\\",\\"downFieldName\\":\\"1108-完成拜访客户数\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc8204906000132042a\\",\\"upFieldName\\":\\"1108-完成拜访客户数1\\"},{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672dde624d7cc60001d531b8\\",\\"downFieldName\\":\\"1108-外勤总次数\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc8204906000132042b\\",\\"upFieldName\\":\\"1108-外勤总次数1\\"},{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_672dde8b8bd5260001316a70\\",\\"downFieldName\\":\\"1108-商机金额\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc8204906000132042c\\",\\"upFieldName\\":\\"1108-商机金额1\\"}],\\"viewId\\":\\"BI_672ddfc4b8475400013126fb\\",\\"viewName\\":\\"1108-客户\\"},{\\"aggMappingList\\":[{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672ddec38bd5260001316a7a\\",\\"downFieldName\\":\\"人员-1108-客户数\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc8204906000132042d\\",\\"upFieldName\\":\\"人员-1108-客户数1\\"},{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672ddee2a9d57800015f5258\\",\\"downFieldName\\":\\"人员-1108-员工总数\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc8204906000132042e\\",\\"upFieldName\\":\\"人员-1108-员工总数1\\"},{\\"aggType\\":\\"uniq\\",\\"downFieldId\\":\\"BI_672ddf018bd5260001316a8e\\",\\"downFieldName\\":\\"人员-1108-外勤拜访客户数\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc8204906000132042f\\",\\"upFieldName\\":\\"人员-1108-外勤拜访客户数1\\"},{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_672ddf0ff86c3500011973f2\\",\\"downFieldName\\":\\"人员-1108-外勤总数\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc82049060001320430\\",\\"upFieldName\\":\\"人员-1108-外勤总数1\\"},{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_672ddf2ca9d57800015f526c\\",\\"downFieldName\\":\\"人员-1108-订单金额\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc82049060001320431\\",\\"upFieldName\\":\\"人员-1108-订单金额1\\"},{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_672de00ef86c350001197414\\",\\"downFieldName\\":\\"人员-1108-商机金额\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673c2fc82049060001320432\\",\\"upFieldName\\":\\"人员-1108-商机金额1\\"}],\\"viewId\\":\\"BI_672de054b847540001312759\\",\\"viewName\\":\\"1105-人员\\"}]","last_modified_by":"1000","create_by":"1000","sync_stat_schema_id":"BI_5bcebcdc3060e20001e79977","policy_desc":"","is_deleted":0,"status":0},{"tenant_id":"802931","last_modified_time":1732181220384,"policy_id":"BI_673efce42bb499000136ced6","create_time":1732181220266,"policy_name":"1121","data_source_enterprise":"[{\\"id\\":\\"301948709\\",\\"type\\":\\"t\\"},{\\"id\\":\\"301943075\\",\\"type\\":\\"t\\"},{\\"id\\":\\"301943076\\",\\"type\\":\\"t\\"}]","agg_mapping_rule":"[{\\"aggMappingList\\":[{\\"aggType\\":\\"count\\",\\"downFieldId\\":\\"BI_62319b145ea98a0001fc9c9d\\",\\"downFieldName\\":\\"门店拜访次数\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673efce42bb499000136ced7\\",\\"upFieldName\\":\\"门店拜访次数1+N\\"}],\\"viewId\\":\\"BI_673ed801aeff090001d3115c\\",\\"viewName\\":\\"门店拜访次数\\"},{\\"aggMappingList\\":[{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_5bceda90dedd2c0001c2f54e\\",\\"downFieldName\\":\\"订单金额\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_673efce42bb499000136ced8\\",\\"upFieldName\\":\\"订单金额1+N\\"}],\\"viewId\\":\\"BI_673ed7d5aeff090001d3110a\\",\\"viewName\\":\\"订单金额\\"}]","last_modified_by":"1000","create_by":"1000","sync_stat_schema_id":"BI_5bcebcdc3060e20001e79977","policy_desc":"","is_deleted":0,"status":1},{"tenant_id":"802931","last_modified_time":1732617668799,"policy_id":"BI_6745a5c43bc7b3000168d57d","create_time":1732617668716,"policy_name":"每日拜访客户数","data_source_enterprise":"[{\\"id\\":\\"6729f96239fddd00015f0e6a\\",\\"type\\":\\"tg\\"},{\\"id\\":\\"6733110e39fddd000160883b\\",\\"type\\":\\"tg\\"}]","agg_mapping_rule":"[{\\"viewId\\":\\"BI_673ed801aeff090001d3115c\\",\\"viewName\\":\\"门店拜访次数\\"},{\\"aggMappingList\\":[{\\"aggType\\":\\"sum\\",\\"downFieldId\\":\\"BI_5bceda90dedd2c0001c2f54e\\",\\"downFieldName\\":\\"订单金额\\",\\"nullActionDate\\":false,\\"upFieldId\\":\\"BI_6745a5c43bc7b3000168d57e\\",\\"upFieldName\\":\\"订单金额1111\\"}],\\"viewId\\":\\"BI_673ed7d5aeff090001d3110a\\",\\"viewName\\":\\"订单金额\\"}]","last_modified_by":"1000","create_by":"1000","sync_stat_schema_id":"BI_5bcebcdc3060e20001e79977","policy_desc":"","is_deleted":0,"status":1}]
          """;
        List<BiDataSyncPolicyDo> biDataSyncPolicyDoList = JSON.parseArray(json, BiDataSyncPolicyDo.class);
        biDataSyncPolicyDoList.forEach(biDataSyncPolicyDo -> {
            String aggMappingRuleJson = biDataSyncPolicyDo.getAggMappingRule();
            List<AggMappingRule> aggMappingRuleList = JSON.parseArray(aggMappingRuleJson, AggMappingRule.class);
            Map<String, List<AggMapping>> viewIdToAggMapping = aggMappingRuleList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getAggMappingList())).collect(Collectors.toMap(AggMappingRule::getViewId, AggMappingRule::getAggMappingList, (item1, item2) -> item2));
            Map<String, String> viewIdToNameMap = aggMappingRuleList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getAggMappingList())).collect(Collectors.toMap(AggMappingRule::getViewId, AggMappingRule::getViewName, (item1, item2) -> item2));
            System.out.println(viewIdToAggMapping);
            System.out.println(viewIdToNameMap);
        });
    }

    @Test
    public void testStatFieldMapper() {
        String tenantId = "85529";
        List<String> viewIdList = Lists.newArrayList("BI_660b82180e96bd0001367a07");
        List<StatFieldDO> statField = statFieldMapper.setTenantId("85529").findStatFieldList(tenantId, viewIdList);
        System.out.println(statField);
    }

    @Test
    public void testChDataSource() {
        AggDataSyncInfoDo aggDataSyncInfo = aggDataSyncInfoMapper.setTenantId("82958").queryViewAggDataSyncInfo("82958", "v1", "0", 100);
        System.out.println(aggDataSyncInfo);
    }

    @Test
    public void testChAggSyncInfo() {
        List<AggDataSyncInfoDo> aggDataSyncInfoList = aggDataSyncInfoMapper.setTenantId("90502").queryAggDataSyncByTenantIdAndViewId("90502", "BI_660b82180e96bd0001367a07");
        System.out.println(aggDataSyncInfoList);
    }

    @Test
    public void testBiPgDataSource() {
        BIAggSyncInfoDO biAggSyncInfoDO = biAggSyncInfoMapper.setTenantId("-1").queryBiAggSyncInfo("82958");
        System.out.println(biAggSyncInfoDO);
    }

    @Test
    public void testPassPgDataSource() {
        List<String> downStreamEnterpriseAccount = bizEnterpriseRelationMapper.setTenantId("82958").queryBizEnterpriseRelation("82958");
        System.out.println(downStreamEnterpriseAccount);
    }

    /**
     * 测试查询ch路由
     */
    @Test
    public void testQueryChRouter() {
        String tenantId = "71750";
        RouterInfo routerInfo;
        try {
            routerInfo = dbRouterClient.queryRouterInfo(tenantId, "BI", "fs-bi-stat-calculate", "clickhouse");
        } catch (Exception e) {
            log.error("查询企业CH路由失败,企业tenantId:{}", tenantId);
            routerInfo = null;
        }
        System.out.println(routerInfo);
    }

    /**
     * 测试数据同步
     */
    @Test
    public void testSynchronizationAggData() {
        RouterInfo upStreamTenantIdRouterInfo = dbRouterClient.queryRouterInfo("85145", "BI", "fs-bi-stat-calculate", "clickhouse");
        RouterInfo downStreamTenantIdRouterInfo = dbRouterClient.queryRouterInfo("90502", "BI", "fs-bi-stat-calculate", "clickhouse");
        SynchronizationReadSqlArg synchronizationReadSqlArg = SynchronizationReadSqlArg.builder()
                                                                                       .upStreamTenantId("82958")
                                                                                       .downStreamTenantId("90502")
                                                                                       .viewId("BI_660b82180e96bd0001367a07")
                                                                                       .version("0")
                                                                                       .actionDate("20240418")
                                                                                       .timeStamp(new Date().getTime())
                                                                                       .aggDataHashCode("0")
                                                                                       .objectId("65e9a4aabada240001774418")
                                                                                       .fieldList(Lists.newArrayList("agg_count_1", "agg_uniq_1", "agg_uniq_2"))
                                                                                       .isFullSynchronization(true)
                                                                                       .build();

        SynchronizationAggDataArg synchronizationAggDataArg = SynchronizationAggDataArg.builder()
                                                                                       .upStreamTenantId("82958")
                                                                                       .downStreamTenantId("71750")
                                                                                       .upStreamRouterInfo(upStreamTenantIdRouterInfo)
                                                                                       .downStreamRouterInfo(downStreamTenantIdRouterInfo)
                                                                                       .synchronizationReadSqlArg(synchronizationReadSqlArg)
                                                                                       .build();
        //aggDataSyncInfoService.synchronizationAggDataToAggDownStreamData(synchronizationAggDataArg);
    }

    /**
     * 测试插入before数据
     */
    @Test
    public void testToBeforeAggData() {
        RouterInfo upStreamTenantIdRouterInfo = dbRouterClient.queryRouterInfo("82958", "BI", "fs-bi-stat-calculate", "clickhouse");
        RouterInfo downStreamTenantIdRouterInfo = dbRouterClient.queryRouterInfo("90502", "BI", "fs-bi-stat-calculate", "clickhouse");
        List<String> fieldList = Lists.newArrayList("agg_count_1", "agg_uniq_1", "agg_uniq_2");
        SynchronizationBeforeAggDataArg downStreamArg = SynchronizationBeforeAggDataArg.builder()
                                                                                     .downStreamTenantId("90502")
                                                                                     .routerInfo(downStreamTenantIdRouterInfo)
                                                                                     .dbName("fsbidb044003001")
                                                                                     .viewId("BI_660b82180e96bd0001367a07")
                                                                                     .lastTimestamp(1713257319000L)
                                                                                     .currentTimestamp(1714301359000L)
                                                                                     .viewVersion(0)
                                                                                     .actionDate("00000000")
                                                                                     .build();

        SynchronizationInsertBeforeAggDataArg upstreamArg = SynchronizationInsertBeforeAggDataArg.builder()
                                                                                                   .upStreamTenantId("82958")
                                                                                                   .routerInfo(upStreamTenantIdRouterInfo)
                                                                                                   .batchNum(0L)
                                                                                                   .viewId("BI_660b82180e96bd0001367a07")
                                                                                                   .viewVersion(0)
                                                                                                   .dbName("fsbidb044003001")
                                                                                                   .fieldList(fieldList)
                                                                                                   .build();

        aggDataSyncInfoService.synchronizationBeforeAggData(downStreamArg, upstreamArg);
    }

    /**
     * 测试写sql
     */
    @Test
    public void testToTransferWriteSql() {
        RouterInfo upStreamTenantIdRouterInfo = dbRouterClient.queryRouterInfo("82958", "BI", "fs-bi-stat-calculate", "clickhouse");
        List<String> fieldList = Lists.newArrayList("agg_count_1", "agg_uniq_1", "agg_uniq_2");
        String writeSql = aggDataSyncInfoService.toTransferWriteSql(fieldList, upStreamTenantIdRouterInfo.getDbName());
        System.out.println(writeSql);
    }

    /**
     * 测试读sql
     */
    @Test
    public void testToTransferReadSql() {
        List<String> fieldList = Lists.newArrayList("agg_count_1", "agg_uniq_1", "agg_uniq_2");

        SynchronizationReadSqlArg fullSynchronizationReadSqlArg = SynchronizationReadSqlArg.builder()
                                                                                           .upStreamTenantId("82958")
                                                                                           .downStreamTenantId("90502")
                                                                                           .viewId("BI_660b82180e96bd0001367a07")
                                                                                           .version("0")
                                                                                           .timeStamp(1704038400L)
                                                                                           .objectId("65e9a4aabada240001774418")
                                                                                           .fieldList(fieldList)
                                                                                           .isFullSynchronization(true)
                                                                                           .actionDate("20240307")
                                                                                           .build();

        SynchronizationReadSqlArg incSynchronizationReadSqlArg = SynchronizationReadSqlArg.builder()
                                                                                          .upStreamTenantId("82958")
                                                                                          .downStreamTenantId("90502")
                                                                                          .viewId("BI_660b82180e96bd0001367a07")
                                                                                          .version("0")
                                                                                          .timeStamp(1704038400L)
                                                                                          .aggDataHashCode("0")
                                                                                          .objectId("65e9a4aabada240001774418")
                                                                                          .fieldList(fieldList)
                                                                                          .isFullSynchronization(false)
                                                                                          .actionDate("20240307")
                                                                                          .build();
        //全量同步
        String fullSql = aggDataSyncInfoService.toTransferReadSql(fullSynchronizationReadSqlArg);

        //增量同步
        String incSql = aggDataSyncInfoService.toTransferReadSql(incSynchronizationReadSqlArg);
        System.out.println(fullSql);
        System.out.println(incSql);
    }

    @Test
    public void testReadSql() {
        List<String> fieldList = Lists.newArrayList("agg_count_1", "agg_uniq_1", "agg_uniq_2");
        SynchronizationReadSqlArg fullSynchronizationReadSqlArg = SynchronizationReadSqlArg.builder()
                                                                                           .upStreamTenantId("82958")
                                                                                           .downStreamTenantId("90502")
                                                                                           .viewId("BI_660b82180e96bd0001367a07")
                                                                                           .version("0")
                                                                                           .timeStamp(System.currentTimeMillis())
                                                                                           .objectId("65e9a4aabada240001774418")
                                                                                           .fieldList(fieldList)
                                                                                           .isFullSynchronization(true)
                                                                                           .actionDate("20240307")
                                                                                           .aggDataHashCode("0")
                                                                                           .build();

        SynchronizationReadSqlArg incSynchronizationReadSqlArg = SynchronizationReadSqlArg.builder()
                                                                                          .upStreamTenantId("82958")
                                                                                          .downStreamTenantId("90502")
                                                                                          .viewId("BI_660b82180e96bd0001367a07")
                                                                                          .version("0")
                                                                                          .timeStamp(System.currentTimeMillis())
                                                                                          .aggDataHashCode("0")
                                                                                          .objectId("65e9a4aabada240001774418")
                                                                                          .fieldList(fieldList)
                                                                                          .isFullSynchronization(false)
                                                                                          .actionDate("20240307")
                                                                                          .build();
        //全量同步
        String fullSql = aggDataSyncInfoService.toTransferReadSqlV2(fullSynchronizationReadSqlArg,"s");

        //增量同步
        String incSql = aggDataSyncInfoService.toTransferReadSqlV2(incSynchronizationReadSqlArg,"s");
        System.out.println(fullSql);
        System.out.println(incSql);
    }

    @Test
    public void testDate() {
        long timestamp = 1704038400;
        Instant instant = Instant.ofEpochSecond(timestamp);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = dateTime.format(formatter);
        System.out.println(formattedDateTime);
    }

    /**
     * 测试before数据生成的hash_code
     */
    @Test
    public void testSynchronizationBeforeData() {
        List<String> fieldList = Lists.newArrayList("agg_count_1", "agg_uniq_1", "agg_uniq_2");
        SynchronizationReadSqlArg fullSynchronizationReadSqlArg = SynchronizationReadSqlArg.builder()
                                                                                           .upStreamTenantId("82958")
                                                                                           .downStreamTenantId("90502")
                                                                                           .viewId("BI_660b82180e96bd0001367a07")
                                                                                           .version("0")
                                                                                           .timeStamp(1704038400L)
                                                                                           .objectId("65e9a4aabada240001774418")
                                                                                           .fieldList(fieldList)
                                                                                           .isFullSynchronization(true)
                                                                                           .actionDate("20240307")
                                                                                           .build();
        //String beforeAggData = aggDataSyncInfoService.toTransferBeforeDownStreamAggDataSql(fullSynchronizationReadSqlArg);
        //System.out.println(beforeAggData);
    }

    /**
     * 测试before数据生成的hash_code
     */
    @Test
    public void testBeforeDataHashCode() {
        String upStreamTenantId = "82958";
        List<String> fieldList = Lists.newArrayList("agg_count_1", "agg_uniq_1", "agg_uniq_2");
        int batchNum = 10;
        String viewId = "BI_660b82180e96bd0001367a07";
        int viewVersion = 0;
        List<String> hashCodeList = Lists.newArrayList("20240422", "20240421", "20240420", "20240439");
        String dbName = "fsbidb044003001";
        //String beforeDataHashCode = aggDataSyncInfoService.toTransferBeforeSql(upStreamTenantId, fieldList, batchNum, viewId, viewVersion, hashCodeList, dbName);
        //System.out.println(beforeDataHashCode);
    }

    /**
     * 使用jdbc的方式进行插入数据
     */
    @Test
    public void testJdbc() throws SQLException {
        RouterInfo upStreamTenantIdRouterInfo = dbRouterClient.queryRouterInfo("85145", "BI", "fs-bi-stat-calculate", "clickhouse");
        RouterInfo downStreamTenantIdRouterInfo = dbRouterClient.queryRouterInfo("90502", "BI", "fs-bi-stat-calculate", "clickhouse");

        //读sql
        List<String> fieldList = Lists.newArrayList("agg_count_1", "agg_uniq_1", "agg_uniq_2");

        SynchronizationReadSqlArg fullSynchronizationReadSqlArg = SynchronizationReadSqlArg.builder()
                                                                                           .upStreamTenantId("82958")
                                                                                           .downStreamTenantId("90502")
                                                                                           .viewId("BI_660b82180e96bd0001367a07")
                                                                                           .version("0")
                                                                                           .timeStamp(1704038400L)
                                                                                           .objectId("65e9a4aabada240001774418")
                                                                                           .fieldList(fieldList)
                                                                                           .isFullSynchronization(true)
                                                                                           .actionDate("20240307")
                                                                                           .build();

        SynchronizationReadSqlArg incSynchronizationReadSqlArg = SynchronizationReadSqlArg.builder()
                                                                                          .upStreamTenantId("82958")
                                                                                          .downStreamTenantId("90502")
                                                                                          .viewId("BI_660b82180e96bd0001367a07")
                                                                                          .version("0")
                                                                                          .timeStamp(1704038400L)
                                                                                          .aggDataHashCode("0")
                                                                                          .objectId("65e9a4aabada240001774418")
                                                                                          .fieldList(fieldList)
                                                                                          .isFullSynchronization(false)
                                                                                          .actionDate("20240307")
                                                                                          .build();
        //全量同步
        String fullSql = aggDataSyncInfoService.toTransferReadSql(fullSynchronizationReadSqlArg);
        //String writeSql = "insert into agg_downstream_data(tenant_id, view_id, view_version, object_id, action_date, ds_agg_count_1, ds_agg_uniq_1, ds_agg_uniq_2) values (?, ?, ?, ?, ?, ?, ?, ?)";
        String writeSql = "insert into agg_downstream_data select tenant_id,view_id,ds_agg_uniq_1 from input('tenant_id String, view_id String, ds_agg_uniq_1 AggregateFunction(uniqExact, Nullable(String))')";
        JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(chRouterPolicy.getChJdbcURL(downStreamTenantIdRouterInfo, false));
        JdbcConnection jdbcConnectionWrite = chDataSource.getJdbcConnection(chRouterPolicy.getChJdbcURL(upStreamTenantIdRouterInfo, false));
        PreparedStatement preparedStatement = jdbcConnectionWrite.connection().prepareStatement(writeSql);
    }

    @Test
    public void testTime() {
        Timestamp timestamp = new Timestamp(1704038400L);
        String s = timestamp.toString();
        System.out.println(s);
    }

    @Test
    public void testTimeStampToDateTime() {
        Instant instant = Instant.ofEpochMilli(1714038365000L);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        System.out.println(dateTime.format(formatter));
    }

    @Test
    public void testLicense() {
        boolean license = dataSyncLicenseService.checkoutDataSyncLicense("90502");
        System.out.println(license);
    }
}
