package com.fxiaoke.bi.warehouse.dws.db.dao;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.AggMergeDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Author:jief
 * @Date:2024/10/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class AggMergeDaoTest {
  @Resource
  AggMergeDao aggMergeDao;

  @Test
  public void testU(){

  }
}
