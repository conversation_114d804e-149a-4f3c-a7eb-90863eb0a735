package com.fxiaoke.bi.warehouse.dws.transferm.db.mapper;

import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.MybatisTenantPolicy;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/2/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class AggRuleMapperTest {

  @Resource(name = "mybatisTenantPolicy")
  private MybatisTenantPolicy mybatisTenantPolicy;

  @Test
  public void testGetRoute() {
    Map<String, RouterInfo> routerInfo = mybatisTenantPolicy.batchQueryRouterInfo(List.of("91400"));
    System.out.println("routerInfo = " + routerInfo);
  }

  @Test
  public void testRouter(){
//    String jdbcURL=mybatisTenantPolicy.getPgBouncerJdbcURL("85145",false);
    System.out.println(CommonUtils.getDBName("clickhouse://chproxy-sc.kube-public:9090/fsbidb044002009"));
  }

  @Test
  public void testRouter2(){
    System.out.println(mybatisTenantPolicy.getDBURLAndSchema("85145"));
  }
}
