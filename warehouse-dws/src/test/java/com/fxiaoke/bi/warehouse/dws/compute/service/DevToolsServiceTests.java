package com.fxiaoke.bi.warehouse.dws.compute.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.service.BizLogService;
import com.fxiaoke.jdbc.JdbcConnection;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/3/4
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class DevToolsServiceTests {
  @Resource
  private BizLogService bizLogService;
  @Test
  public void testQuery(){
    try(JdbcConnection jdbcConnection = bizLogService.getBizLogJdbcConnection(1000*60*30L)) {
      jdbcConnection.query("select viewId from biz_log_bi limit 10", rs -> {
        while (rs.next()) {
          System.out.println(rs.getString(1));
        }
      });
    }catch (Exception e){
      e.printStackTrace();
    }
  }

  @Test
  public void testViewIds(){
    Map<String,List<String>> viewIds = bizLogService.queryActiveViewIds(Lists.newArrayList("82958"));
    System.out.println(JSON.toJSONString(viewIds));
  }
}
