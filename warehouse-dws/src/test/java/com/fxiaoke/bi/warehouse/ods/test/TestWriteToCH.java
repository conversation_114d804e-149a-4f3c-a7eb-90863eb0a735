package com.fxiaoke.bi.warehouse.ods.test;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.ClickHouseColumnType;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.PGMetadataService;
import com.fxiaoke.helper.NumberHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Author:jief
 * @Date:2023/5/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TestWriteToCH {
  @Resource
  private CHDBService chdbService;
  @Resource
  private PGMetadataService pgMetadataService;
  @Resource
  private CHMetadataService chMetadataService;

  @Test
  public void testDeleteCHData() throws Exception {
    String tableName = "biz_account";
    String chDbUrl = "***************************************************";
    long batchNum = 1L;
    Optional<ClickhouseTable> clickhouseTable = chMetadataService.loadTable("public", chDbUrl, tableName);
    clickhouseTable.ifPresent(table -> System.out.println(table.getOrderByColumns()));
  }
  @Test
  public void testWriteToCH() throws Exception {
    Object result= ClickHouseColumnType.DOUBLE.format(Long.valueOf(-1602999992367186000L), CHContext.ETLTarget.CSV, null);
    System.out.println(result);
    //    PGSchema schema = pgMetadataService.loadSchema("jdbc:postgresql://
  }

  @Test
  public void testDecimal(){
    Long a=-1602999992367186000l;
    Double doubleValue = NumberHelper.parseDouble(a, " {} is not double", a);
    System.out.println(doubleValue< (-1 * 10^(38 - 10)));
    System.out.println(doubleValue> (-1 * 10^(38 - 10)));
    System.out.println(doubleValue< (1 * 10^(38 - 10)));
    System.out.println(doubleValue> (1 * 10^(38 - 10)));
  }
}