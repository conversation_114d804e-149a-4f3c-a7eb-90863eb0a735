package com.fxiaoke.bi.warehouse.ods.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.bean.UserCenterService;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.util.List;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2023/12/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class PgCommonServiceTests {
  @Resource
  private CHDBService chdbService;
  @Resource
  private PgCommonService pgCommonService;
  @Resource
  private CHDataSource chDataSource;
  @Resource
  private UserCenterService userCenterService;
  @Resource
  PGMetadataService pgMetadataService;
  @Test
  public void testInsertSysUser(){
    chdbService.batchInsertSysUser(Lists.newArrayList("90508"), "***************************************************");
    System.out.println(chDataSource.findChURLByEi("90508"));
  }

  @Test
  public void testRout(){
    //fsbidb044003001
    System.out.println(chDataSource.findChURLByEi("90508"));
  }

  @Test
  public void testUc(){
    System.out.println(userCenterService.isValidateStatus("82958"));
  }
  @Test
  public void testDeleteTableSyncInfoByDbSyncId(){
    int result= pgCommonService.deleteTableSyncInfoByDbSyncId(Lists.newArrayList("64ee0f5dbfbe303944dd65b6"), Lists.newArrayList("object_t1702622386013__c"));
    System.out.println("result=============="+result);
  }

  @Test
  public void testTables()throws Exception{
    TransferEvent transferEvent = new TransferEvent("", "*****************************************", "public");
    List<String> tableList = com.google.common.collect.Lists.newArrayList(pgMetadataService.findNeededToSyncTables(transferEvent));
    Set<String> biPgTables= Sets.newHashSet(tableList);
    File aea = ResourceUtils.getFile("classpath:ods/tableNames.txt");
    String tableStr = Files.readString(aea.toPath());
    List<String> notHave= Splitter.on(",").omitEmptyStrings().splitToList(tableStr).stream().filter(table->!biPgTables.contains(table)).toList();
    System.out.println(JSON.toJSONString(notHave));
  }
  @Test
  public void testPaas2BiTables() {
    System.out.println(pgMetadataService.isPaas2BiTable("biz_account"));
  }
}
