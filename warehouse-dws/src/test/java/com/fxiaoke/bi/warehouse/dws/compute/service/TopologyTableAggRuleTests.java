package com.fxiaoke.bi.warehouse.dws.compute.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTable;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRuleMonitor;
import com.fxiaoke.bi.warehouse.dws.transform.model.GoalChangeType;
import com.google.common.collect.Maps;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/4/8
 */
public class TopologyTableAggRuleTests extends TestCase {

  @Test
  public void testCreatePreViewSQL() throws IOException {
    //common dim
    File commonDimFile = ResourceUtils.getFile("classpath:stat/common_dim.json");
    String commonDimJson = Files.readString(commonDimFile.toPath());
    List<String> commonDimList = JSON.parseObject(commonDimJson, new TypeReference<>() {
    });
    //受影响得
    File aea = ResourceUtils.getFile("classpath:stat/agg_effect_apiName.json");
    String aeaJson = Files.readString(aea.toPath());
    Map<String, Set<String>> aggEffectApiNames = JSON.parseObject(aeaJson, new TypeReference<>() {
    });
    //去重列
    File uniqFieldLocationsField = ResourceUtils.getFile("classpath:stat/uniq_field_locations.json");
    String uniqFieldLocationsJson = Files.readString(uniqFieldLocationsField.toPath());
    Map<String, String> uniqFieldLocationMapper = JSON.parseObject(uniqFieldLocationsJson, new TypeReference<>() {
    });
    //槽位
    File statFieldLocation = ResourceUtils.getFile("classpath:stat/stat_field_location.json");
    String statFieldLocationJson = Files.readString(statFieldLocation.toPath());
    Map<String, String> statFieldLocationMapper = JSON.parseObject(statFieldLocationJson, new TypeReference<>() {
    });
    //
    File file = ResourceUtils.getFile("classpath:stat/StatView_1.json");
    String json = Files.readString(file.toPath());
    List<TopologyTableAggRule> topologyTableAggRules = JSON.parseObject(json, new TypeReference<>() {
    });
    TopologyTable statView = new TopologyTable();
    statView.setViewId("BI_66d2dbf553f40e0001595e5f");
    statView.setTenantId("84439");
    statView.setApiName("");
    statView.setTimezone("Asia/Shanghai");
    statView.setStatus(1);
    statView.setCommonDimList(commonDimList);
    statView.setStatRuleList(topologyTableAggRules);
    //fieldId和受影响对象的映射关系
    statView.setAggEffectApiNames(aggEffectApiNames);
    statView.setVersion(0);
    statView.setStatFieldLocation(statFieldLocationMapper);
    statView.setDimUniqFieldMapper(uniqFieldLocationMapper);
    statView.setBatchNum(1);
    statView.setSource(0);
    statView.setStatViewUniqueKey("8c6b1bd98facec557c327c8478bb3b26");

    topologyTableAggRules.forEach(topologyTableAggRule -> {
      String fieldId = topologyTableAggRule.getFieldId();
      if (fieldId.equals("BI_5bceda90dedd2c0001c2f54e")) {
        Map<String, List<String>> tableKeys = Maps.newHashMap();
        tableKeys.put("biz_account", Arrays.asList("tenant_id","bi_sys_flag","id"));
        TopologyTableAggRuleMonitor topologyTableAggRuleMonitor = topologyTableAggRule.toStatRuleMonitor(statView, null, GoalChangeType.DATA_INC, tableKeys,null);
        List<String> sqls = topologyTableAggRuleMonitor.computeSQL(1L);
        System.out.println(sqls.get(0));
      }
    });

  }
}
