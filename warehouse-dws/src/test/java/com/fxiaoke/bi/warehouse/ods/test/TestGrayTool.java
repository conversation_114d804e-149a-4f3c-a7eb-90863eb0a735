package com.fxiaoke.bi.warehouse.ods.test;

import java.util.concurrent.*;

/**
 * @Author:jief
 * @Date:2024/4/20
 */
public class TestGrayTool{
  public static void main(String[] args) {
    ExecutorService executor = Executors.newFixedThreadPool(3);

    CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
      try {
        // Simulate a long-running task
        TimeUnit.SECONDS.sleep(20);
        System.out.println("Task 1 completed");
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
    }, executor);

    CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
      try {
        // Simulate a task that throws an exception
        TimeUnit.SECONDS.sleep(1);
        throw new RuntimeException("Task 2 failed");
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
    }, executor);

    CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
      try {
        // Simulate a long-running task
        TimeUnit.SECONDS.sleep(30);
        System.out.println("Task 3 completed");
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
    }, executor);

    CompletableFuture<Void> allFutures = CompletableFuture.allOf(future1, future2, future3);

    allFutures.exceptionally(ex -> {
      System.out.println("Exception occurred: " + ex.getMessage());
      // Cancel all futures if any exception occurs
      future1.cancel(true);
      future2.cancel(true);
      future3.cancel(true);
      return null;
    });
    System.out.println("start to get....");
    try {
      allFutures.get();
    } catch (InterruptedException | ExecutionException e) {
      System.out.println("Execution exception: " + e.getMessage());
    }
    System.out.println("executor shutdown");
    executor.shutdown();
  }
}
