package com.fxiaoke.bi.warehouse.dws;

import com.facishare.bi.util.context.RequestContextManagerBase;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.dws.service.DbTableSyncInfoInterface;
import com.fxiaoke.bi.warehouse.dws.service.DbTableSyncInfoInterfaceImpl;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.mapper.AggDataSyncInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> zzh
 * @createTime   : [2024/12/12 10:44] 
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
@Slf4j
public class StatDetailTimeServiceTest {

  @Autowired
  DbTableSyncInfoInterfaceImpl dbTableSyncInfoInterface;
  @Autowired
  AggDataSyncInfoMapper aggDataSyncInfoMapper;

  @Test
  public void testGetStatDetailTime() {
    RequestContextManagerBase.setContext("85145", "1000");
    DbTableSyncInfoInterface.FieldAggDelayInfo detailUpdateTime = dbTableSyncInfoInterface.findDetailUpdateTime(RequestContextManagerBase.getEi(), "BI_67666ffa80db740001bc62c6", "BI_6764d42b48ed9200012b21f9", false, null);
    System.out.println("detailUpdateTime = " + detailUpdateTime);
  }

  @Test
  public void testQuantile() {
    String tenantId = "85145";
    Double cost = aggDataSyncInfoMapper.setTenantId(tenantId)
                                       .queryCostFromBiAggLog(tenantId, "BI_674d7b1747fef600015e6814", "BI_65250ad2535e3200019b3dac", WarehouseConfig.calcFlowStatCostQuantile);
    System.out.println("cost = " + cost);
  }
}
