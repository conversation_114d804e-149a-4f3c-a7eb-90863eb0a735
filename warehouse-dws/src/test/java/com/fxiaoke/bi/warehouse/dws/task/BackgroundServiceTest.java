package com.fxiaoke.bi.warehouse.dws.task;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.service.BackgroundTaskService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class BackgroundServiceTest {
    @Autowired
    private BackgroundTaskService backgroundTaskService;

    @Test
    public void testExplainTask() {
        JSONObject jsonObject = JSONObject.parseObject("{\"ch_db\": \"****************************************************\"}");
        String chDB = jsonObject.getString("ch_db");
        List<String> chUrl = Lists.newArrayList();
        chUrl.add(chDB);

        backgroundTaskService.explainTask();
    }

    @Test
    public void testExplainViewsCost(){
        backgroundTaskService.explainViewsCost(Lists.newArrayList("***************************************************"));
    }

    @Test
    public void testExplainSql() {
        backgroundTaskService.explainSql("***************************************************","74745","38c61183f2abbc3b90a1e554b6a6ad4b","select ");
    }

    @Test
    public void testAuditLargeTables() {
        List<String> chList = Lists.newArrayList();
        chList.add("***************************************************");
        backgroundTaskService.auditLargeTables(chList);
    }
}
