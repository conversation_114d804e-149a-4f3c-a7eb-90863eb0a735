package com.fxiaoke.bi.warehouse.ods.integreate.service;


import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.ods.integrate.model.IntegrateEvent;
import com.fxiaoke.bi.warehouse.core.db.BiAggSyncInfoDao;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.IntegrateServiceImpl;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.github.jedis.support.JedisCmd;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @Author:jief
 * @Date:2024/4/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class IntegrateServiceTest {
 @Resource
  IntegrateServiceImpl integrateService;
 @Resource
  BiAggSyncInfoDao biAggSyncInfoDao;
  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  @Resource
  CHDBService chdbService;
 @Test
 public void testTransfer(){
   AtomicLong nextBatchNum=new AtomicLong(3);
   IntegrateEvent integrateEvent= new IntegrateEvent();
   integrateEvent.setTenantId("82958");
   BIAggSyncInfoDO biAggSyncInfoDO = biAggSyncInfoDao.queryAggSyncByEi("82958");
//   integrateService.doTranslate(nextBatchNum, biAggSyncInfoDO,System.currentTimeMillis());
 }

 @Test
 public void testConsumerEvent(){
   IntegrateEvent integrateEvent = IntegrateEvent.of("82958");
   String transferKey = String.format("bi:%s:%s", "integrate", integrateEvent.getTenantId());
   jedisCmd.del(transferKey);
//   integrateService.consumeIntegrateEvent(integrateEvent);
 }
@Test
 public void testFindMaxSysModifiedTimeByTable(){
//   chdbService.findMaxSysModifiedTimeByTable(jdbcConnection, fromCHTable, startTimestamp, stopWatch)
 }

 @Test
 public void testGray(){
   String pgDbName = CommonUtils.getDBName("***********************************************");
   System.out.println(GrayManager.isAllowByRule("query_from_tenant_db", String.format("%s^%s",pgDbName,"sch_82958")));
 }
}
