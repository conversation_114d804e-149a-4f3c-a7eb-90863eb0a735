package com.fxiaoke.bi.warehouse.ods.test;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.component.MybatisBITenantPolicy;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.ods.args.CHPublicCreatorArg;
import com.fxiaoke.bi.warehouse.ods.bean.DBColumnType;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.bean.PGSchema;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.dao.MetaDataDao;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.dao.UdfObjFieldDao;
import com.fxiaoke.bi.warehouse.ods.dao.mapper.BISystemMapper;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjMapper;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.service.PGMetadataService;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.jedis.support.JedisCmd;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.sql.Connection;
import java.sql.Statement;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class PGTests {
  @Resource
  PGMetadataService pgMetadataService;
  @Resource
  PgCommonDao pgCommonDao;
  @Autowired
  UdfObjMapper udfObjMapper;
  @Autowired
  BISystemMapper biSystemMapper;

  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;
  @Autowired
  private DbRouterClient dbRouterClient;
  @Resource
  private PgDataSource pgDataSource;

  @Resource
  private MetaDataDao metaDataDao;
  @Resource
  private UdfObjFieldDao udfObjFieldDao;
  private MybatisBITenantPolicy mybatisBITenantPolicy;
  @Test
  public void testCreateCHInitSqL(){
    System.setProperty("catalina.home","D:\\FxiaoWork\\chTableDDL");
    CHPublicCreatorArg chPublicCreatorArg = new CHPublicCreatorArg();
    chPublicCreatorArg.setChDBName("***************************************************");
    chPublicCreatorArg.setNeedCreate(false);
    chPublicCreatorArg.setTenantId("85145");
    chPublicCreatorArg.setChCluster("{cluster}");
    chPublicCreatorArg.setPgDbUrl("*******************************************");
    chPublicCreatorArg.setSchemaName("public");
    chPublicCreatorArg.setTableEngine("ReplicatedReplacingMergeTree");
    chPublicCreatorArg.setTables(Lists.newArrayList("goal_value_obj", "goal_value_obj_year", "goal_value_obj_week", "goal_value_obj_quarter"));
    pgMetadataService.createCHInitSqL(chPublicCreatorArg,"");
  }

  @Test
  public void testCreateViewSQL()throws Exception{
    String chDB="***************************************************";
    String sql = pgMetadataService.createAllCHTableFromPg("85145", "biz_account_lang", CommonUtils.getDBName(chDB), "{cluster}",
      CHContext.REPLICATED_REPLACING_MERGE_TREE,true);
    System.out.println(sql);
  }
  @Test
  public void testJdbcConnection()throws Exception{
    JdbcConnection jdbcConnection = pgDataSource.getJdbcConnection("85145");
    jdbcConnection.query("select id from biz_account where tenant_id='85145' limit 1", (conn, v1, v2)->{
      try{
        Statement statement=conn.createStatement();
        statement.execute("set ");
        return statement;
      }catch (Exception e){
        e.printStackTrace();
      }
      return null;
    }, rs->{
      while (rs.next()){
        System.out.println(rs.getString(1));
      }
    });
  }

  @Test
  public void testJdbcConnection2()throws Exception{
    JdbcConnection jdbcConnection = pgDataSource.getJdbcConnection("85145");
    jdbcConnection.query("select id from biz_account where tenant_id='85145' limit 1", Connection::createStatement, rs->{
      while (rs.next()){
        System.out.println(rs.getString(1));
      }
    });
  }

  @Test
  public void testUdfObjField(){
    List<Integer> objectKX3IE__c = udfObjFieldDao.getFieldsByObjNameAndType("85145", "object_kX3IE__c");
    System.out.println("(objectKX3IE__c) = " + JSON.toJSONString(objectKX3IE__c));
  }
  public void testPgType() {
    String tenantId = "82958";
    String table = "fmcg_tpm_budget_carry_forward_detail";
    String schemaName = "sch_82958";
    List<DBColumnType> dbColumnTypes = metaDataDao.findAllPgTypeByTable(tenantId, table, schemaName);
  }

  @Test
  public void testSchema() {
    System.out.println(pgDataSource.isStandalone("-1"));
  }

  @Test
  public void testRout() {
    String tenantID = "82958";
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, MybatisBITenantPolicy.BIZ,
      MybatisBITenantPolicy.APPLICATION, MybatisBITenantPolicy.DIALECT);
    System.out.println(JSON.toJSONString(routerInfo));
    //    String jdbcUrl = routerInfo.getSlaveUrl();
    //    System.out.println(dbRouterClient.convertPgBouncerUrl(MyBatisRoutePolicy.BIZ, jdbcUrl, false));
  }

  @Test
  public void testPage() {
    Page<DBSyncInfo> page = new Page<DBSyncInfo>(1, 2);
    String startId = "0";
    do {
      biSystemMapper.setTenantId("-1").pagination3(page, "id >'" + startId + "' and is_deleted=0", "id");
      if (page.size() == 2) {
        for (DBSyncInfo dbSyncInfo : page) {
          System.out.println("send message " + dbSyncInfo.getId());
          startId = dbSyncInfo.getId();
        }
        page = new Page<DBSyncInfo>(1, 2);
      } else {
        for (DBSyncInfo dbSyncInfo : page) {
          System.out.println("send message " + dbSyncInfo.getId());
        }
        startId = null;
      }

    } while (StringUtils.isNotBlank(startId));
  }

  @Test
  public void testUdfObj() {
    System.out.println(udfObjMapper.setTenantId("82958").findObjNameByTable(82958, "object_data"));
  }

  @Test
  public void testPKey() throws Exception {
    String tables = "" + "goal_value_obj_week,goal_value_obj_year";
    //    String tables="object_data";
    List<String> tableList = Splitter.on(",").splitToList(tables);
    BufferedWriter indexFile = new BufferedWriter(new FileWriter(new File(
      "D:\\clickhouse_workspace\\create_index" + ".sql")));
    for (String table : tableList) {
      PGSchema schema = pgMetadataService.loadSchema("*******************************************", "public." + table);
      //      boolean haveSysModifiedTime=schema.getColumns().contains("sys_modified_time");
      String index = schema.createIndex();
      //      System.out.println("table:"+table+",index:"+ index);
      if (StringUtils.isNotBlank(index)) {
        indexFile.write(schema.createIndex());
        indexFile.write("\n");
      }
    }
    indexFile.flush();
    indexFile.close();
  }



  @Test
  public void testDbEis() {
    //    String tenantID = "82958";
    //    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, MyBatisRoutePolicy.BIZ,
    //      MyBatisRoutePolicy.APPLICATION, MyBatisRoutePolicy.DIALECT,true);
    //    System.out.println(routerInfo.getJdbcUrl());
    //
    //    List<SimpleRouterInfo> simpleRouterInfos= dbRouterClient.queryRoutersByDb("10.112.52.251:5432/bi_112",
    //    MyBatisRoutePolicy.BIZ,MyBatisRoutePolicy.DIALECT);
    //   if(simpleRouterInfos!=null){
    //     System.out.println(simpleRouterInfos.stream().map(SimpleRouterInfo::getTenantId).toList());
    //   }
    //    System.out.println(simpleRouterInfos.size());
    TransferEvent transferEvent = new TransferEvent();
    transferEvent.setSchema("public");
    transferEvent.setDbURL("*****************************************");
    List<String> eis = pgMetadataService.findValidaTenantId(transferEvent);
    System.out.println(JSON.toJSONString(eis));
  }

  @Test
  public void testEiCheck(){
    System.out.println(pgMetadataService.isValidate("fsbidb001005","88298"));
  }

  @Test
  public void testFindAllPgTableWithSmt() {
    List<String> tables = pgMetadataService.findAllPgTableWithSmt("***********************************************",
      "sch_82958");
    System.out.println("aaaaaa->"+tables.size());
    List<String> table2 = pgMetadataService.findAllPgTableWithSmt("*****************************************",
      "public");
    System.out.println("bbbbbb->"+table2.size());
  }

  @Test
  public void testBatchUpsert() {
    DBSyncInfo dbSyncInfo = new DBSyncInfo();
    dbSyncInfo.setId(ObjectId.get().toString());
    dbSyncInfo.setChDb("*****************************************");
    dbSyncInfo.setBatchNum(1L);
    dbSyncInfo.setCreateTime(new Date().getTime());
    dbSyncInfo.setPgDb("*******************************************");
    dbSyncInfo.setPgSchema("public");
    dbSyncInfo.setIsDeleted(0);
    dbSyncInfo.setStatus(null);
    dbSyncInfo.setLastModifiedTime(new Date().getTime());
    dbSyncInfo.setLastSyncTime(new Date().getTime());
    dbSyncInfo.setLastMergeAggTime(new Date().getTime());
    dbSyncInfo.setLastSyncEis(null);
    pgCommonDao.batchUpsertDbSyncInfo(Lists.newArrayList(dbSyncInfo));
  }
@Test
  public void testUpdateStatus(){
  DBSyncInfo dbSyncInfo = new DBSyncInfo();
  dbSyncInfo.setId("11");
  dbSyncInfo.setCreateTime(100000L);
  dbSyncInfo.setStatus(5);
  dbSyncInfo.setLastModifiedTime(100000L);
  dbSyncInfo.setLastSyncTime(60000L);
  pgCommonDao.updateDBSyncInfoStatus(dbSyncInfo);
  }

@Test
  public void testDeleted(){
  pgCommonDao.batchDeletedDbSyncInfo(Lists.newArrayList("6492e157400f5929c5bd1cd7"));
  }

  @Test
  public void testUpsert() {
    DBSyncInfo dbSyncInfo = new DBSyncInfo();
    dbSyncInfo.setId(ObjectId.get().toString());
    dbSyncInfo.setChDb("ch");
    dbSyncInfo.setBatchNum(1L);
    dbSyncInfo.setCreateTime(new Date().getTime());
    dbSyncInfo.setPgDb("pg");
    dbSyncInfo.setPgSchema("public");
    dbSyncInfo.setIsDeleted(1);
    dbSyncInfo.setStatus(2);
    dbSyncInfo.setLastModifiedTime(new Date().getTime());
    dbSyncInfo.setLastSyncTime(new Date().getTime());
    dbSyncInfo.setLastMergeAggTime(new Date().getTime());
    dbSyncInfo.setLastSyncEis(null);
    pgCommonDao.updateDbSyncInfo(dbSyncInfo);
  }
  @Test
  public void testUPsertWithPartition(){
    DBSyncInfo dbSyncInfo = new DBSyncInfo();
    dbSyncInfo.setId("11");
    dbSyncInfo.setChDb("ch");
    dbSyncInfo.setBatchNum(1L);
    dbSyncInfo.setCreateTime(new Date().getTime());
    dbSyncInfo.setPgDb("pg");
    dbSyncInfo.setPgSchema("public");
    dbSyncInfo.setIsDeleted(0);
    dbSyncInfo.setStatus(null);
    dbSyncInfo.setLastModifiedTime(new Date().getTime());
    dbSyncInfo.setLastSyncTime(new Date().getTime());
    dbSyncInfo.setLastMergeAggTime(new Date().getTime());
    dbSyncInfo.setLastSyncEis(null);
    dbSyncInfo.setAllowIncPartition(WarehouseConfig.OPEN_INC_PARTITION);
    pgCommonDao.updateDbSyncInfo(dbSyncInfo);
  }

  @Test
  public void testPwd() {
    //    System.out.println(PasswordUtil.decode("9C1D478736F6CF4F10C12F793967281A08A64F9A166D1FFCF0D2761A4702BE5E"));
    System.out.println(PasswordUtil.decode("2220EC6040679DEAC6A3D0FAD3D4D1FD07DBDF114A075151E061D962392A1357"));
  }

  @Test
  public void testRedis() {
    jedisCmd.set("test_aaa", "123");
    jedisCmd.expire("test_aaa", 10000L);
    System.out.println(jedisCmd.get("test_aaa"));
  }
}
