package com.fxiaoke.bi.warehouse.ods.test;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseNodeInfo;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.DBTransferService;
import com.fxiaoke.bi.warehouse.ods.service.MergeTaskService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2023/9/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class MergeTaskServiceTest {
  @Resource
  MergeTaskService mergeTaskService;
  @Resource
  DBTransferService dbTransferService;
  @Resource
  CHMetadataService chMetadataService;

  @Test
  public void testMerge(){
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    mergeTaskService.createOrReplaceAggSwapTable(clickhouseNodeInfo, true);
//   System.out.println( dbTransferService.wait4MergeAggData(chUrl));
  }
  @Test
  public void testCreateTable(){
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    mergeTaskService.createAggHistoryTable(clickhouseNodeInfo);
  }

  @Test
  public void testMerge2(){
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    mergeTaskService.merge(clickhouseNodeInfo,10000000,30*60*1000,false,false);
    mergeTaskService.merge(clickhouseNodeInfo,10000000,30*60*1000,true,true);
//    System.out.println( dbTransferService.wait4MergeAggData(chUrl));
  }
  @Test
  public void testExplainCost(){
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    mergeTaskService.explainViewsCost(clickhouseNodeInfo);
//    System.out.println( dbTransferService.wait4MergeAggData(chUrl));
  }

@Test
  public void testMerge3(){
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    System.out.println(mergeTaskService.createMergeSQL(clickhouseNodeInfo, "", false, false, ""));
    System.out.println(mergeTaskService.createMergeSQL(clickhouseNodeInfo, "", true, false, ""));
    System.out.println(mergeTaskService.createMergeSQL(clickhouseNodeInfo, "", true, true, ""));
    System.out.println(mergeTaskService.createMergeSQL(clickhouseNodeInfo, "", false, true, ""));
  }

  @Test
  public void testGenerateSql() {
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    List<String> sqlList = new ArrayList<>();
    Map<String, Long> tableSizeMap = chMetadataService.queryTableSize(chUrl, Lists.newArrayList("agg_data"));
    if (tableSizeMap != null && tableSizeMap.get("agg_data") != null) {
      Long size = tableSizeMap.get("agg_data");
      List<BigDecimal> quantileList = CHContext.calQuantiles(size, 10_00000);
      List<String> quantileCodeList = chMetadataService.queryTableQuantile(chUrl, quantileList, "agg_data", " 1=1 ");
      System.out.println(JSON.toJSONString(quantileCodeList));
      for (int i = 0; i < quantileCodeList.size() - 1; i++) {
        String from = quantileCodeList.get(i);
        String end = quantileCodeList.get(i + 1);
        String quantileSql = "AND (hash_code > %s AND hash_code <= %s) ".formatted(from, end);
        String sql = mergeTaskService.createMergeSQL(clickhouseNodeInfo, quantileSql, false, true, "");
        sqlList.add(sql);
      }
      String quantileSql = "AND (hash_code > %s) ".formatted(quantileCodeList.get(quantileCodeList.size() - 1));
      String sql = mergeTaskService.createMergeSQL(clickhouseNodeInfo, quantileSql, false, true, "");
      sqlList.add(sql);
    }
    System.out.println("sqlList = " + sqlList);
  }

  @Test
  public void testMergeAggDataTable() {
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    mergeTaskService.mergeAggDataTable(clickhouseNodeInfo,10000000,30*60*1000,false);
    mergeTaskService.mergeAggDataTable(clickhouseNodeInfo,10000000,30*60*1000,true);
  }

  @Test
  public void testCanStartMergeAllTask() {
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    System.out.println(mergeTaskService.canStartMergeAllTask(clickhouseNodeInfo));
  }

  @Test
  public void testCheckMergeResult() {
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    System.out.println(mergeTaskService.checkMergeResult(clickhouseNodeInfo));
  }

  @Test
  public void testCanStartMergeIncrementTask() {
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    System.out.println(mergeTaskService.canStartMergeIncrementTask(clickhouseNodeInfo));
  }

  @Test
  public void testStartMergeTaskAll() {
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    System.out.println(mergeTaskService.startMergeTaskAll(clickhouseNodeInfo));
  }

  @Test
  public void testFinishMergeTaskAll() {
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    System.out.println(mergeTaskService.finishMergeTaskAll(clickhouseNodeInfo));
  }

  @Test
  public void testStartMergeTaskIncrement(){
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    System.out.println(mergeTaskService.startMergeIncrementTask(clickhouseNodeInfo));
  }


  @Test
  public void testFinishMergeTaskIncrement() {
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    System.out.println(mergeTaskService.finishMergeTaskIncrement(clickhouseNodeInfo));
  }

  @Test
  public void testGetViewVersionFilter() {
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
//    System.out.println(mergeTaskService.getViewVersionFilter(clickhouseNodeInfo, mergeTaskService.getMinPartition(clickhouseNodeInfo,"agg_data")));
  }

  @Test
  public void testCreateQuantileSqlList(){
    String chUrl="***************************************************";
    ClickhouseNodeInfo clickhouseNodeInfo=ClickhouseNodeInfo.builder().cluster("{cluster}").jdbcUrl(chUrl).database("fsbidb044003001").build();
    System.out.println(String.format("case 1: %s",mergeTaskService.createQuantileSqlList(clickhouseNodeInfo, 17000000, 10000000, "1=1", false)));
    System.out.println(String.format("case 2: %s",mergeTaskService.createQuantileSqlList(clickhouseNodeInfo, 10000, 10000000, "1=1", false)));
    System.out.println(String.format("case 3: %s",mergeTaskService.createQuantileSqlList(clickhouseNodeInfo, 11000000, 10000000, "1=1", false)));
    System.out.println(String.format("case 4: %s",mergeTaskService.createQuantileSqlList(clickhouseNodeInfo, 23000000, 10000000, "1=1", false)));
  }
}
