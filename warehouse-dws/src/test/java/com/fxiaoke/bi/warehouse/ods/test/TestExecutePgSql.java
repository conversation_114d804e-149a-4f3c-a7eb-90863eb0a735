package com.fxiaoke.bi.warehouse.ods.test;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.ods.service.ExecutePgSqlService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TestExecutePgSql {

    @Resource
    private ExecutePgSqlService executePgSqlService;

    @Test
    public void testEscapeSql() {
        String executePgSql = """
            INSERT INTO sch_802931.bi_data_sync_policy (policy_id, policy_name, policy_desc, tenant_id, data_source_enterprise, sync_stat_schema_id, agg_mapping_rule, status, create_by, create_time, last_modified_by, last_modified_time, is_deleted) VALUES ('BI_677baf7c33e188451f2e8007', '0106同步策略', '', '802931', '[{"id":"301943076","type":"t"},{"id":"301948709","type":"t"}]', 'BI_5bcebcdc3060e20001e79977', e'[{"aggMappingList":[{"aggType":"uniq","downFieldId":"BI_677b950d825c8c00014403a9","downFieldName":"拜访客户（1+N）","nullActionDate":false,"upFieldId":"BI_677bac0d33e188431631a6a1","upFieldName":"拜访客户（1+N）"},{"aggType":"uniq","downFieldId":"BI_677b94fc825c8c00014403a5","downFieldName":"拜访外勤次数（1+N）","nullActionDate":false,"upFieldId":"BI_677bac0d33e188431631a6a2","upFieldName":"拜访外勤次数（1+N）"},{"aggType":"count","downFieldId":"BI_677b9523b1a6b3000177cdd0","downFieldName":"外勤数（1+N）","nullActionDate":false,"upFieldId":"BI_677bac0d33e188431631a6a3","upFieldName":"外勤数（1+N）"}],"dimFiledIdList":[{"fieldId":"BI_5bfe0042ba92330001264f62","fieldName":"客户","dbFieldName":"customer_id","objectDescribeApiName":"checkins_data","fieldType":"String","subFieldType":"","dbObjName":"dim_data","aggDimType":"dim","refObjName":"biz_account","refTargetField":"name","type":"object_reference"},{"fieldId":"BI_5c2dfed7e6fd560930629234","fieldName":"日期（月）","dbFieldName":"f_month","objectDescribeApiName":"checkins_data","fieldType":"Date","subFieldType":"","dbObjName":"dim_sys_date","aggDimType":"dim","refObjName":"","refTargetField":"","type":"date"}],"filterList":[{"filterFieldList":[{"fieldId":"BI_677b94fc825c8c00014403a5","fieldName":"拜访次数","dbFiledName":"finish_date","objectDescribeApiName":"checkins_data","fieldType":"Date","subFieldType":"","dbObjName":"agg_data","aggDimType":"base_agg","refObjName":"","refTargetField":"","type":"","operator":"13","value1":"1","value2":""}]}],"viewId":"BI_677b95d0c632e00001529bf4","viewName":"拜访1次的客户"}]', 1, '-10000', *************, '-10000', *************, 0);  
          """;
        String formatExecutePgSql = executePgSqlService.escapeSql(executePgSql);
        System.out.println(formatExecutePgSql);
    }
}
