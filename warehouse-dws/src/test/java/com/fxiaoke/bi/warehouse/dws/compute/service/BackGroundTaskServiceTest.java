package com.fxiaoke.bi.warehouse.dws.compute.service;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.service.BackgroundTaskService;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/7/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class BackGroundTaskServiceTest {
  @Resource
  private BackgroundTaskService backgroundTaskService;
  @Test
  public void testExplain()throws Exception{
    String jdbcUrl="***************************************************";
    String tenantId="85145";
    String viewId="v1";
    File aea = ResourceUtils.getFile("classpath:stat/explain.sql");
    String vSql = Files.readString(aea.toPath());
    backgroundTaskService.explainSql(jdbcUrl, tenantId, viewId, vSql);
  }

  @Test
  public void testSkipExplainTask(){
    String ei="90974";
    String statViewUniqueKey = "93fa6ba02f314e106c2a2719afb4e782";
    Set<String> downstreamViews= Sets.newHashSet("BI_67124887ab26b20001aff85e","BI_67124850ab26b20001aff809");
    System.out.println(backgroundTaskService.skipExplainTask(ei,  statViewUniqueKey,  downstreamViews));
  }

}
