package com.fxiaoke.bi.warehouse.dws.compute.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.bean.CHColumn;
import com.fxiaoke.bi.warehouse.common.component.ClickHouseUtilService;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.db.dao.DbSyncInfoDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.DBSyncInfoDO;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.dws.service.ClickHouseService;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.fxiaoke.common.Pair;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.base.Splitter;
import com.google.common.collect.Multimap;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.Map;

/**
 * 测试clickhouse服务
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class ClickHouseServiceTest {

  @Resource
  private ClickHouseService clickHouseService;
  @Resource
  private DbSyncInfoDao dbSyncInfoDao;

  @Resource
  private ClickHouseUtilService clickHouseUtilService;

  @Test
  public void testQueryDbSyncInfoByTenantId(){
    Multimap<String,DBSyncInfoDO> dbSyncInfoDOS = dbSyncInfoDao.queryDbSyncInfoByTenantId(Lists.newArrayList("85145","71570","82958"));
    System.out.println(JSON.toJSONString(dbSyncInfoDOS));
  }

  @Test
  public void testCheckRouter() {
    clickHouseService.checkAndAddChRouter(Lists.newArrayList("92344"));
  }

  @Test
  public void testExcludeChAgg795960() {
    String lastEis60 = "799321,799111,798892,798667,798445,798223,798001,797763,797538,797324,789872,789702,790150,790322,789896,789741,789770,789791,789819,789847,789935,790342,789955,790602,789983,790004,790033,790048,790626,790082,790364,791084,790390,790099,790646,790134,790416,790664,790457,790736,790686,790479,790504,790709,790530,790551,791104,791272,790580,790766,790788,790813,790853,790880,790833,790932,790952,790973,790898,791135,791292,791005,791026,790912,791044,790176,790192,791065,790222,791155,791308,790244,790267,791176,791376,790296,791218,791331,791198,791252,791408,791356,791732,792022,791426,791462,791446,791490,792162,791515,791757,791533,791556,791777,791575,791600,792041,791804,791619,791648,791670,791693,791836,791715,792063,791854,791875,791895,792184,791924,791950,791966,792002,792096,792200,792113,792135,792560,792232,792252,792351,792266,792710,792384,792597,792406,792428,792450,792295,792477,792496,792519,792622,792645,792540,792984,792314,793232,792662,792681,792741,793209,793006,792763,792786,792334,792807,792829,792866,793033,792888,792910,792956,792941,793062,793401,793742,793093,793256,793119,793139,793162,793190,793449,793306,793428,793520,793337,793354,793376,793480,793498,793540,793570,793594,793683,793702,793880,793763,793722,793786,793613,793636,793663,793818,793839,793905,793858,793928,62042832,62042811,794259,793977,793996,794015,794466,794874,795097,794679,795301,795773,795991,795511,796215,796467,796904,796696,797110,789678,787651,787676,787696,787717,787759,787776,787789,787812,787839,787861,787896,787918,787944,787961,787982,788008,788023,788060,788073,788217,788107,788122,788161,788172,788196,788256,788284,788302,788313,788345,788370,788400,788411,788442,788457,788490,788503,788542,788562,788584,788605,788620,788658,788666,788687,788721,788731,788752,788773,788802,788812,788844,788860,788891,788910,788930,788954,788971,789054,789013,789041,789077,789121,789143,789165,789248,789186,789207,789230,789274,789307,789330,789347,789373,789390,789430,789449,789471,789491,789515,789542,789560,789584,789600,789641,789651";
    List<String> strings = Splitter.on("\",\"").splitToList(lastEis60);
    for (String needAggCalcEis : strings) {
      String curlUrl = "curl -X POST 'http://localhost/warehouse-dws/batchCreateAggTopology' -H 'Cache-Control: no-cache' -H 'Content-Type: application/json' -d '{\"tenantId\":\"%s\"}'";
      // 排除掉 ${use_ch_agg} < 795960 返回后的结果
      String useChAgg = "[750076, 775276, 734678, 734675, 683661, 590172, 753923, 782557, 715669, 707281, 1, 448555, 652848, 247390, 732375, 692099, 548489, 399260, 154113, 729957, 734249, 713549, 718299, 718741, 721104, 719424, 148999, 605599, 672397, 630074, 500529, 358226, 704115, 721787, 736684, 703638, 322246, 472252, 689436, 730173, 702081, 687779, 650993, 457482, 704833, 773625, 760132, 683639, 770410, 720375, 747119, 679449, 754125, 785893, 772026, 770352, 787529, 590270, 754565, 794884, 753054, 716476, 739651, 782892, 747926, 781620, 777933, 757088, 781121, 781684, 790735, 793455, 771058, 755470, 744964, 767157, 782892, 756682, 756309, 757097, 744379, 767156, 768595, 767151, 757088, 780439, 769897, 771797, 784217, 744958, 775710, 777907, 767148, 784053, 754483, 744198, 758482, 774532, 741056, 760388, 769469, 756409, 743063, 746614, 779067, 781276, 775533, 773992, 750023, 771991, 756321, 749824, 743993, 772948, 758691, 779463, 779472, 760170, 742196, 737799, 783077, 741550, 753888, 795575, 739017, 737459, 770275, 774776, 740250, 742041, 742201, 766633, 742522, 737952, 741875, 742200, 738828, 742192, 737796, 760592, 741386, 774964, 738035, 737953, 740732, 740572, 740412, 741879, 769857, 737795, 742518, 774570, 755894, 744378, 776717, 761231, 737798, 758687, 740576, 782851, 778291, 741382, 740568, 741871, 740569, 742521, 740737, 741392, 741393, 741377, 742355, 740251, 737274, 757079, 740577, 746973, 753442, 778599, 780493, 768062, 781121, 747125, 776114, 758744, 737126, 771466, 744805, 747213, 750366, 779099, 762159, 758736, 777933, 760210, 748653, 737406, 737754, 743759, 742069, 771839, 760650, 742550, 740605, 737593, 780499, 777951, 784099, 753946, 772518, 742390, 740369, 758329, 761285, 784387, 757032, 785781, 737989, 737756, 774311, 742475, 772047, 761129, 738791, 750653, 781258, 741100, 766912, 761701, 738447, 774443, 758741, 748743, 741347, 743847, 742157, 737407, 775214, 752736, 760751, 780429, 740931, 737994, 752466, 737830, 783101, 775482, 754358, 740766, 786943, 758427, 737408, 778274, 780281, 742229, 742391, 773302, 742551, 741421, 737829, 740529, 770304, 739774, 747879, 779720, 754753, 741184, 779783, 794123, 742392, 736732, 741997, 747975, 768546, 774799, 744040, 741354, 781620, 772026, 738156, 740532, 756844, 742477, 779304, 742231, 778145, 750176, 785592, 740214, 740116, 767010, 774439, 737752, 741194, 742002, 785594, 749224, 782413, 746561, 741908, 740689, 785213, 786117, 741748, 738872, 750174, 741675, 741839, 741996, 747878, 740692, 749127, 750175, 784175, 742484, 774818, 751240, 742317, 742235, 741832, 751160, 741666, 779718, 740212, 754653, 741352, 752941, 740370, 782335, 775196, 774997, 741587, 741747, 777959, 780917, 752652, 776978, 741183, 779329, 741187, 742481, 741995, 740698, 737921, 782994, 748938, 741586, 740368, 737763, 749610, 738078, 741830, 756456, 737410, 742072, 740770, 740367, 778804, 740853, 740213, 742639, 740112, 751890, 741426, 741991, 741909, 781683, 741019, 742232, 737761, 740695, 742073, 738077, 737601, 740537, 738151, 741103, 760853, 768460, 751159, 771653, 743943, 742394, 768083, 776792, 740771, 737762, 757897, 741746, 741259, 771468, 740934, 742552, 758736, 760121, 742160, 740694, 740203, 759376, 739062, 740767, 768160, 741018, 761260, 738076, 742389, 782406, 741419, 758742, 741014, 741674, 740693, 795066, 757223, 741262, 742158, 740696, 761564, 742479, 741422, 740539, 742155, 742316, 744240, 741016, 740373, 783401, 741511, 785823, 723797, 754122, 726368, 769602, 781542, 726694, 724968, 743621, 700854, 475601, 680306, 589227, 746745, 711466, 753215, 757485, 598075, 749545, 741476, 740821, 742446, 740340, 774461, 741316, 761930, 737725, 762320, 741156, 756774, 740987, 750891, 741811, 737893, 756958, 740829, 741484, 778954, 740348, 776199, 779345, 742614, 782555, 785729, 740509, 762515, 742133, 730173, 741810, 780521, 775234, 742453, 741323, 778955, 780318, 737732, 740347, 742613, 781562, 770740, 741971, 753371, 741163, 738052, 746686, 737731, 767806, 740346, 777845, 776878, 781424, 742612, 766935, 740667, 744110, 741970, 752972, 784884, 742131, 741162, 783223, 785888, 737890, 770225, 777655, 742451, 741481, 741644, 772932, 740666, 773327, 776701, 761797, 769840, 740506, 740992, 741161, 785079, 755545, 751281, 742450, 756966, 768804, 782834, 737729, 738938, 747421, 741320, 771742, 760047, 742610, 742290, 741968, 780828, 740180, 741160, 741806, 773740, 784202, 742449, 741479, 754192, 777848, 785226, 762585, 787541, 747611, 741967, 794777, 779414, 740179, 740504, 740990, 738406, 752399, 795445, 769120, 758358, 742448, 756395, 737887, 740342, 781771, 741318, 747613, 775632, 758993, 740663, 741641, 787201, 752769, 776816, 738217, 746496, 741477, 752583, 759416, 737726, 740341, 749720, 741317, 777391, 753178, 740662, 760479, 787580, 742126, 741801, 762190, 740175, 770228, 742445, 779024, 738045, 772742, 737884, 741315, 777849, 737724, 778237, 759845, 742605, 779417, 776704, 741155, 758157, 753381, 740500, 740174, 742444, 741474, 746357, 779807, 737883, 741314, 737723, 741637, 747618, 761592, 741154, 778424, 753477, 776335, 742204, 772198, 743626, 754098, 739029, 737643, 783270, 742525, 738125, 737804, 742044, 741720, 786363, 781470, 741395, 740740, 770230, 742443, 783816, 774918, 786426, 760975, 741313, 772940, 737722, 742283, 747069, 742603, 761594, 741961, 780832, 741153, 757744, 783432, 740984, 786165, 760690, 740172, 770231, 740817, 781844, 741472, 738042, 783027, 740336, 753785, 741798, 780205, 769637, 741635, 739651, 771366, 757170, 741152, 742121, 740171, 775125, 781845, 742441, 753579, 737880, 740335, 741311, 741797, 780206, 737720, 750123, 741634, 769848, 794562, 742120, 741151, 741959, 737555, 740815, 742440, 741470, 762396, 741310, 795822, 761805, 787315, 742600, 772354, 784979, 757174, 741150, 737554, 742439, 741469, 759427, 740333, 780323, 738039, 737718, 757565, 741632, 786360, 768508, 742118, 742599, 741957, 739648, 768734, 773866, 742438, 778175, 770563, 744295, 741308, 741794, 774463, 738038, 752781, 777395, 740332, 740653, 741631, 781357, 762521, 742117, 741148, 742437, 741467, 783367, 741307, 771877, 784128, 780326, 738037, 750129, 742116, 777206, 740830, 741812, 750889, 755754, 742455, 741325, 746346, 737734, 775430, 740349, 771866, 747604, 738596, 787445, 737385, 740510, 741973, 758143, 740185]";
      List<Integer> useChAggEiList = JSON.parseArray(useChAgg, Integer.class);
      // 替换为 last_sync_eis
      // String needAggCalcEis = "[673946,676006,673574,675156,674754,675581,674364,709146,676441,676877,678805,709678,678309,628284,679065,710213,679912,679471,707576,680348,680795,681253,683186,682721,684124,684596,685065,685536,686536,687013,687963,687491,686067,688438,683654,690343,689386,689864,690820,691298,66345,691777,700944,692249,700468,708100,711752,711233,710753,712258,712779,713325,714353,714885,713866,715914,715409,716448,717058,717597,718665,718171,719182,720301,719743,721374,721924,720839,723514,722470,722992,724046,725639,724571,726190,726729,725101,727263,727798,728363,729412,728893,729934,730485,731014,731592,732162,732694,733200,733632,734188,735827,734726,735271,736364,701944,702421,701422,703392,703866,702894,704344,705476,704938,706018,706561,707099,708646,373556,393533,393621,378132,341724,579300,587904,499180,463437,522688,393520,514924,497424,87769,614858,632228,601527,607324,609727,627809,634140,613353,615875,629253,625834,642771,627335,629736,631225,620700,635579,644210,646882,643732,646959,643251,641337,640858,644688,645971,642294,654568,641815,645161,655038,656014,653591,645607,651195,649301,648341,651676,646447,652153,654067,649776,652636,647863,650234,655528,650708,648821,656539,653112,638981,640377,639427,564671,424637,439861,99123,405062,416312,401612,489355,471027,407501,274661,548099,519603,92515,401403,534115,405779,87740,493439,36912,387683,424385,420817,35076,312470,559330,59934,467552,56877,65921,447900,452876,454069,86262,532920,522631,48923,277271,281753,49982,541549,291157,572288,591792,311005,378684,382345,302794]";
      List<Integer> eiList = JSON.parseArray("[" + needAggCalcEis + "]", Integer.class);
      List<Integer> needAggCalcEiList = eiList.stream().filter(ei -> !useChAggEiList.contains(ei)).toList();
      // System.out.println("needAggCalcEis = " + JSON.toJSONString(needAggCalcEiList));
      System.out.println("need execute url: " + curlUrl.formatted(needAggCalcEiList.stream()
                                                                                   .map(Objects::toString)
                                                                                   .collect(Collectors.joining(","))));
    }
  }

  @Test
  public void testClickHouseUtil() {
    Map<Integer, String> eiToEaMap = clickHouseUtilService.getEiToEaMap(com.google.common.collect.Lists.newArrayList("90502"));
    clickHouseUtilService.createChRoute("90502", "fsbidb044003001", false, eiToEaMap);
  }

  @Test
  public void testCheckSample(){
    List<Pair<String, String>> tableApiNamePair= Lists.newArrayList();
    tableApiNamePair.add(Pair.build("biz_account","biz_account"));
    tableApiNamePair.add(Pair.build("object_data","object_KXtal__c"));
    tableApiNamePair.add(Pair.build("object_data","object_1VPj9__c"));
    tableApiNamePair.add(Pair.build("stage_runtime", Constants.STAGE_RUNTIME_NEW_OPPORTUNITY));
   boolean result= clickHouseService.checkSamplePublicData("85145", tableApiNamePair, 1000000);
   System.out.println(result);
  }
  @Test
  public void executeSQLTest() throws IOException {
    File file = ResourceUtils.getFile("classpath:stat/StatView_3.json");
    String json = Files.readString(file.toPath());
    List<TopologyTableAggRule> statRuleList= JSON.parseObject(json,new TypeReference<>(){});
//    statRuleList.get(0).toStatRuleViewSQL()
//    TopologyTableMonitor viewMonitor = statView.toStatViewMonitor();
//    viewMonitor.getStatRuleMonitorList().forEach(statRuleMonitor -> {
//     List<String> sqls= statRuleMonitor.getComputeSQL();
//     sqls.forEach(computeSQL->{
//       String sql = TemplateUtil.replace(computeSQL, ImmutableMap.of("batch_id", "1"));
//       System.out.println(sql);
//     });
//      clickHouseService.executeSQL("71570", sql);
//    });
  }

  @Test
  public void teatQueryCHTableColumns(){
    String chDBUrl="***************************************************";
    String tableName="biz_account";
//    List<String> pkColumns= clickHouseService.queryCHTablePkColumns(chDBUrl,tableName);
    List<String> pkColumns= clickHouseService.queryCHTablePkColumnsByEI("85145",tableName);
    System.out.println(JSON.toJSONString(pkColumns));
  }
@Test
  public void testQueryCHColumns(){
  String chDBUrl="***************************************************";
  String tableName="biz_account";
  List<CHColumn> b=clickHouseService.loadTableCHColumn(chDBUrl,"sch_82958", tableName);
  System.out.println(b.size());
  System.out.println(JSON.toJSONString(b));
  }
  @Test
  public void testBatchQueryCHColumn(){
    String chDBUrl="***************************************************";
    List<String> tableName= Lists.newArrayList("biz_account","org_employee_user");
    Map<String,List<CHColumn>> result= clickHouseService.batchLoadColumn(chDBUrl,"fsbidb044003001",tableName);
    System.out.println(JSON.toJSONString(result));
  }
  /**
   * 测试创建clickhouse连接
   */
  @Test
  public void createTest() throws Exception{
    String tenantId = "71570";
    JdbcConnection connection= clickHouseService.createJdbcConnection(tenantId,********);
    connection.query("select count() from biz_account",resultSet -> {
      while (resultSet.next()) {
        System.out.println(resultSet.getString(1));
      }
    });
    connection.close();
  }

  @Test
  public void testCreateView(){
    String chJdbcUrl="***************************************************";
//    clickHouseService.executeSQLWithJdbcUrl(chJdbcUrl, InitSQL.mtDataTagVSQL,30*60*1000);
    String insertSQL="INSERT INTO object_t1701123171841__c (id,tenant_id,bi_sys_flag, bi_sys_is_deleted, bi_sys_version) SELECT id,tenant_id,bi_sys_flag, toUInt8(1) AS bi_sys_is_deleted,now() AS bi_sys_version FROM object_t1701123171841__c WHERE tenant_id IN ('590172') AND bi_sys_flag = '0'  AND bi_sys_batch_id = 4335 SETTINGS mutations_sync=1";
    String tableName="object_t1701123171841__c";
    long batchNum=1L;
    try {
      clickHouseService.executeSQLWithJdbcUrl(chJdbcUrl, insertSQL, 30*60*1000);
    } catch (Exception e) {
      if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("UNKNOWN_TABLE")) {
        clickHouseService.invalidatePkColumnsCache(chJdbcUrl, tableName);
        System.out.printf("deleteBeforeData sql error chDBurl:%s,tableName:%s,batchNum:%s%n", insertSQL, tableName, batchNum);
      } else {
        throw e;
      }
    }
  }
@Test
  public void testReadOnly(){
    String jdbcUrl="***************************************************";
    int readTimeOut=600000;
    try(JdbcConnection jdbcConnection=clickHouseService.createJdbcConnection( jdbcUrl,  readTimeOut,true)){
      jdbcConnection.query("select count() from biz_account where tenant_id='85145'",rs->{
        if(rs.next()){
          System.out.println("aaaaa:"+rs.getInt(1));
        }
      });
    }catch (Exception e){
      e.printStackTrace();
    }
  }

  @Test
  public void testQuerySchemaDBSyncInfoByCHDB(){
    String chDB="***************************************************";
    DBSyncInfoDO a=  dbSyncInfoDao.querySchemaDBSyncInfoByCHDB(chDB);
    System.out.println(JSON.toJSON(a));
  }
}

