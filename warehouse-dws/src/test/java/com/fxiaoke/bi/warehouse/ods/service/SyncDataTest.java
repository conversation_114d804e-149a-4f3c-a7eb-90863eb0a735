package com.fxiaoke.bi.warehouse.ods.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.ods.args.ComparePgChDataArg;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> zzh
 * @createTime : [2024/6/7 14:17]
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class SyncDataTest {

  @Resource
  DBTransferService dbTransferService;
  @Resource
  private DataCompareService dataCompareService;

  @Test
  public void testDataCompareService() {
    ComparePgChDataArg comparePgChDataArg = new ComparePgChDataArg();
    comparePgChDataArg.setTableName("goal_value_obj");
    comparePgChDataArg.setTenantId("82958");
    // comparePgChDataArg.setObjectDescribeApiName("object_describe_api_name");
    // comparePgChDataArg.setPrimaryIds(List.of("asfsgddfsfgdfdgfdgfd"));
    dataCompareService.compareAndMarkDeletedData(comparePgChDataArg);
  }

  @Test
  public void testSendQiXinMsg() {
    TransferEvent transferEvent = new TransferEvent();
    transferEvent.setChDbURL("***************************************************");
    transferEvent.setDbURL("*****************************************");
    transferEvent.setSchema("public");
    QiXinNotifyService.sendTextMessage(SyncStatusEnum.AGG_ING, transferEvent, 100000, 100000, 1000000);
  }

  @Test
  public void testSendQiXinOnlyMsg() {
    TransferEvent transferEvent = new TransferEvent();
    transferEvent.setChDbURL("***************************************************");
    transferEvent.setDbURL("*****************************************");
    transferEvent.setSchema("public");
    QiXinNotifyService.sendTextMessage("bi_112", "public", "ch", JSON.toJSONString(transferEvent));
  }

  @Test
  public void testSendQiXinAlarm() {
    QiXinNotifyService.alarm("9408", "赵正豪", "测试", "1");
  }

  @Test
  public void test() {
    String lastSyncEis = "85145";
    dbTransferService.syncDataByTenantId(List.of("6412b13f5cd44942982c91b7"), "goal_value_obj", List.of(lastSyncEis.split(",")), "s");
    dbTransferService.syncDataByTenantId(List.of("6412b13f5cd44942982c91b7"), "goal_value_obj_week", List.of(lastSyncEis.split(",")), "s");
    dbTransferService.syncDataByTenantId(List.of("6412b13f5cd44942982c91b7"), "goal_value_obj_quarter", List.of(lastSyncEis.split(",")), "s");
    dbTransferService.syncDataByTenantId(List.of("6412b13f5cd44942982c91b7"), "goal_value_obj_year", List.of(lastSyncEis.split(",")), "s");
  }
}
