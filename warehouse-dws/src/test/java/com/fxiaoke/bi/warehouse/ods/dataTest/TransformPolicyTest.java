package com.fxiaoke.bi.warehouse.ods.dataTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.entity.StatFieldDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.AggRuleDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewDO;
import com.fxiaoke.bi.warehouse.dws.transform.model.DownStreamPolicyStructure;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.AggMapping;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.AggMappingRule;
import com.fxiaoke.bi.warehouse.ods.integrate.dto.DataSourceEnterprise;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.entity.BiDataSyncPolicyDo;
import com.google.common.collect.Maps;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TransformPolicyTest {

    //1端企业虚拟指标
    private List<StatFieldDO> statFieldDOList;

    //N端企业槽位类型
    private List<TopologyTable> topologyTableList;

    //下游指标
    private List<StatFieldDO> downStreamStatFieldList;

    //下游图表
    private List<StatViewDO> downStreamStatViewList;

    @Before
    public void setUp() {
        String filePath1 = "/Users/<USER>/Desktop/蒙牛1+N统计图指标详情.txt";
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath1))) {
            StringBuilder json = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                json.append(line);
            }
            statFieldDOList = JSON.parseArray(json.toString(), StatFieldDO.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        String filePath2 = "/Users/<USER>/Desktop/伍子醉1+N统计图指标类型.txt";
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath2))) {
            StringBuilder json = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                json.append(line);
            }
            topologyTableList = JSON.parseArray(json.toString(), TopologyTable.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        String filePath3 = "/Users/<USER>/Desktop/伍子醉1+N下游图表.txt";
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath3))) {
            StringBuilder json = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                json.append(line);
            }
            downStreamStatViewList = JSON.parseArray(json.toString(), StatViewDO.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        String filePath4 = "/Users/<USER>/Desktop/伍子醉1+N下游指标.txt";
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath4))) {
            StringBuilder json = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                json.append(line);
            }
            downStreamStatFieldList = JSON.parseArray(json.toString(), StatFieldDO.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成虚拟指标的agg_rule
     */
    @Test
    public void createVirtualAggRule() {
        String aggRuleSqlTemplate = """
          INSERT INTO agg_rule (tenant_id, rule_id, field_id, theme_api_name, display_name, create_time, creator,
                                last_modified_time, last_modifier, is_deleted, type, description, time_zone)
          VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', % d, '%s', '%s', '%s');
          """;
        List<AggRuleDO> aggRuleDOList = statFieldDOList.stream().map(this::getSyncAggRule).toList();
        aggRuleDOList.forEach(x -> System.out.println(String.format(aggRuleSqlTemplate, x.getTenantId(), x.getRuleId(), x.getFieldId(), x.getThemeApiName(), x.getDisplayName(), x.getCreateTime(), x.getCreator(), x.getLastModifiedTime(), x.getLastModifier(), x.getIsDeleted(), x.getType(), x.getDescription(), "Asia/Shanghai")));
    }

    /**
     * 下游的viewId
     */
    @Test
    public void syncViewIds() {
        List<String> downStreamViewIdList = statFieldDOList.stream().map(StatFieldDO::getDownstreamViewId).distinct().toList();
        List<String> downStreamFieldIdList = statFieldDOList.stream().map(StatFieldDO::getDownstreamFieldId).distinct().toList();
        String viewIdWhereSql = downStreamViewIdList.stream().map(x -> "'" + x + "'").collect(Collectors.joining(","));
        String fieldIdWhereSql = downStreamFieldIdList.stream().map(x -> "'" + x + "'").collect(Collectors.joining(","));
        System.out.println(viewIdWhereSql);
        System.out.println(fieldIdWhereSql);
    }

    //获取每一个图表的槽位置类型放入到aggRule中
    @Test
    public void transformPolicy() {
        List<AggMappingRule> aggMappingRuleList = Lists.newArrayList();
        Map<String, String> viewIdToNameMap = downStreamStatViewList.stream().collect(Collectors.toMap(StatViewDO::getViewId, StatViewDO::getViewName, (item1, item2) -> item2));
        Map<String, String> fieldIdToNameMap = downStreamStatFieldList.stream().collect(Collectors.toMap(StatFieldDO::getFieldId, StatFieldDO::getFieldName, (item1, item2) -> item2));
        Map<String, AggLocationEnum> fieldIdToAggLocationEnumMap = getFieldIdToAggLocationEnumMap(topologyTableList);
        Map<String, List<String>> viewIdToFieldIdList = statFieldDOList.stream().collect(Collectors.groupingBy(StatFieldDO::getDownstreamViewId, Collectors.mapping(StatFieldDO::getDownstreamFieldId, Collectors.toList())));
        Map<String, String> downStreamFieldIdToVirtualFieldIdMap = statFieldDOList.stream().collect(Collectors.toMap(StatFieldDO::getDownstreamFieldId, StatFieldDO::getFieldId, (item1, item2) -> item2));
        Map<String, String> virtualFieldIdToNameMap = statFieldDOList.stream().collect(Collectors.toMap(StatFieldDO::getFieldId, StatFieldDO::getFieldName, (item1, item2) -> item2));
        viewIdToFieldIdList.forEach((viewId, fieldIdList) -> {
            String viewName = viewIdToNameMap.get(viewId);
            List<AggMapping> aggMappingList = Lists.newArrayList();
            fieldIdList.forEach(downFieldId -> {
                String upFieldId = downStreamFieldIdToVirtualFieldIdMap.get(downFieldId);
                AggLocationEnum aggLocationEnum = fieldIdToAggLocationEnumMap.get(downFieldId);
                String aggType = aggLocationEnum.getAggType();
                String downFieldName = fieldIdToNameMap.get(downFieldId);
                String upFieldName = virtualFieldIdToNameMap.get(upFieldId);
                AggMapping aggMapping = AggMapping.of(upFieldId, upFieldName, downFieldId, downFieldName, aggType);
                aggMappingList.add(aggMapping);
            });
            //AggMappingRule aggMappingRule = AggMappingRule.of(viewId, viewName, aggMappingList);
            //aggMappingRuleList.add(aggMappingRule);
        });
        DataSourceEnterprise dataSourceEnterprise = new DataSourceEnterprise();
        dataSourceEnterprise.setId("67209e69e652380001adfc7b");
        dataSourceEnterprise.setType("tg");
        List<DataSourceEnterprise> dataSourceEnterpriseList = Lists.newArrayList(dataSourceEnterprise);
        BiDataSyncPolicyDo biDataSyncPolicyDo = new BiDataSyncPolicyDo();
        biDataSyncPolicyDo.setPolicyId("BI_673737e962399800011b4bbf");
        biDataSyncPolicyDo.setPolicyName("历史1+N同步策略");
        biDataSyncPolicyDo.setPolicyDesc("历史1+N同步策略");
        biDataSyncPolicyDo.setTenantId("779237");
        biDataSyncPolicyDo.setDataSourceEnterprise(JSON.toJSONString(dataSourceEnterpriseList));
        biDataSyncPolicyDo.setSyncStatSchemaId("BI_5bcebcdc3060e20001e79977");
        biDataSyncPolicyDo.setAggMappingRule(JSON.toJSONString(aggMappingRuleList));
        biDataSyncPolicyDo.setStatus(1);
        biDataSyncPolicyDo.setCreateBy("-10000");
        biDataSyncPolicyDo.setCreateTime(new Date().getTime());
        biDataSyncPolicyDo.setLastModifiedBy("-10000");
        biDataSyncPolicyDo.setLastModifiedTime(new Date().getTime());
        biDataSyncPolicyDo.setIsDeleted(0);
        System.out.println(biDataSyncPolicyDo);
    }

    /**
     * 虚拟指标添加策略结构列
     */
    @Test
    public void addDownStreamPolicyStructure() {
        String tenantId = "779237";
        String policyId = "BI_673737e962399800011b4bbf";
        String sqlTemplate = """
          update stat_field set downstream_policy_structure = '%s' where field_id='%s' and tenant_id='%s' and agg_dim_type = 'downstream_agg' and is_deleted=0 and status=1; 
          """;
        statFieldDOList.forEach(statFieldDO -> {
            String fieldId = statFieldDO.getFieldId();
            String downstreamViewId = statFieldDO.getDownstreamViewId();
            List<DownStreamPolicyStructure> list = Lists.newArrayList(new DownStreamPolicyStructure(policyId, downstreamViewId));
            System.out.println(String.format(sqlTemplate, JSON.toJSONString(list), fieldId, tenantId));
        });
    }

    @Test
    public void testSql() {
        String sql = "INSERT INTO bi_data_sync_policy (policy_id, policy_name, policy_desc, tenant_id, data_source_enterprise,sync_stat_schema_id, agg_mapping_rule, status, create_by,create_time, last_modified_by, last_modified_time, is_deleted) VALUES ('BI_673737e962399800011b4bbf', '历史1+N同步策略', '历史1+N同步策略', '779237', '[{\"id\":\"67209e69e652380001adfc7b\",\"type\":\"tg\"}]', 'BI_5bcebcdc3060e20001e79977', '[{\"aggMappingList\":[{\"aggType\":\"count\",\"downFieldId\":\"BI_666946b38f516a0001f9e851\",\"downFieldName\":\"客户数量（N端--主属性）\",\"upFieldId\":\"BI_6669bf164a6a0c3c7d48c0cd\",\"upFieldName\":\"客户数量（N端--主属性）\"},{\"aggType\":\"count\",\"downFieldId\":\"BI_664b0b1c7fd4ec0001e80405\",\"downFieldName\":\"客户数量（总数N段指标）\",\"upFieldId\":\"BI_664db366270d5e1ddd31c4a2\",\"upFieldName\":\"客户数量（总数）\"},{\"aggType\":\"count\",\"downFieldId\":\"BI_664b0c377fd4ec0001e80685\",\"downFieldName\":\"客户新增数量\",\"upFieldId\":\"BI_664db2ebd159cf06bfe4a8a7\",\"upFieldName\":\"客户新增数量\"}],\"viewId\":\"BI_664b0d2406ca80000157e0ae\",\"viewName\":\"系统应用-客户(CH互联)\"},{\"aggMappingList\":[{\"aggType\":\"uniq\",\"downFieldId\":\"BI_66bb0199d88e650001f43037\",\"downFieldName\":\"我品照片家数\",\"upFieldId\":\"BI_66bd76eb3e639c14f0cedf51\",\"upFieldName\":\"我品照片家数\"},{\"aggType\":\"uniq\",\"downFieldId\":\"BI_66bb01cdd88e650001f430c1\",\"downFieldName\":\"竞品照片家数\",\"upFieldId\":\"BI_66bd7711a633de5391af55b7\",\"upFieldName\":\"竞品照片家数\"},{\"aggType\":\"uniq\",\"downFieldId\":\"BI_66bb01f9d88e650001f43150\",\"downFieldName\":\"广宣元素照片家数\",\"upFieldId\":\"BI_66bd774c05bc796783e55d7d\",\"upFieldName\":\"广宣元素照片家数\"},{\"aggType\":\"uniq\",\"downFieldId\":\"BI_66bb0244d88e650001f431c9\",\"downFieldName\":\"陈列卡位照片家数\",\"upFieldId\":\"BI_66bd7790598a780646a5eafd\",\"upFieldName\":\"陈列卡位照片家数\"},{\"aggType\":\"uniq\",\"downFieldId\":\"BI_66bb026bd88e650001f43241\",\"downFieldName\":\"陈列升级照片家数\",\"upFieldId\":\"BI_66bd77cac40daa482904bd04\",\"upFieldName\":\"陈列升级照片家数\"},{\"aggType\":\"uniq\",\"downFieldId\":\"BI_66bb02a4d88e650001f432d7\",\"downFieldName\":\"多点陈列照片家数\",\"upFieldId\":\"BI_66bd780da269364f99eeda99\",\"upFieldName\":\"多点陈列照片家数\"},{\"aggType\":\"uniq\",\"downFieldId\":\"BI_66bb02cbd88e650001f43354\",\"downFieldName\":\"专属陈列照片家数\",\"upFieldId\":\"BI_66bd785385c2117205f2ff36\",\"upFieldName\":\"专属陈列照片家数\"}],\"viewId\":\"BI_66bc1c2299a1d600016d4b9c\",\"viewName\":\"寸土必争(CH报表)\"},{\"aggMappingList\":[{\"aggType\":\"count\",\"downFieldId\":\"BI_664b1019674b7500016214cf\",\"downFieldName\":\"业务员人数\",\"upFieldId\":\"BI_664db42b4e15461495359ded\",\"upFieldName\":\"业务员人数\"},{\"aggType\":\"count\",\"downFieldId\":\"BI_664b1032674b7500016214f0\",\"downFieldName\":\"督导人数\",\"upFieldId\":\"BI_664db4752f83e05b4e296076\",\"upFieldName\":\"督导人数\"},{\"aggType\":\"count\",\"downFieldId\":\"BI_664b1074674b750001621562\",\"downFieldName\":\"执行经理\",\"upFieldId\":\"BI_664db4ef307f531a0a149704\",\"upFieldName\":\"执行经理\"}],\"viewId\":\"BI_664b113706ca80000157e171\",\"viewName\":\"系统应用-人员(CH互联)\"},{\"aggMappingList\":[{\"aggType\":\"uniq\",\"downFieldId\":\"BI_664c51fa7fd4ec0001e90e5e\",\"downFieldName\":\"门店拜访使用人数\",\"upFieldId\":\"BI_664daf363a821e496c2a6348\",\"upFieldName\":\"门店拜访使用人数\"},{\"aggType\":\"uniq\",\"downFieldId\":\"BI_664c5190674b75000163197b\",\"downFieldName\":\"拜访门店数\",\"upFieldId\":\"BI_664dafcde0504a55e4fbf3eb\",\"upFieldName\":\"拜访门店数\"},{\"aggType\":\"uniq\",\"downFieldId\":\"BI_664c525b674b750001631b38\",\"downFieldName\":\"督导使用人数\",\"upFieldId\":\"BI_664db24cc1b9b9599575d85b\",\"upFieldName\":\"督导使用人数\"},{\"aggType\":\"uniq\",\"downFieldId\":\"BI_664c52a07fd4ec0001e90f7d\",\"downFieldName\":\"稽查客户数\",\"upFieldId\":\"BI_664db2ca8edf0134d01b2a29\",\"upFieldName\":\"稽查客户数\"}],\"viewId\":\"BI_664b08ec2310d300014f6759\",\"viewName\":\"系统应用-拜访(CH互联)\"},{\"aggMappingList\":[{\"aggType\":\"uniq\",\"downFieldId\":\"BI_672087ab59ff8b0001e51aa1\",\"downFieldName\":\"陈列架投放照片家数\",\"upFieldId\":\"BI_6721fc6420f99c756df3da31\",\"upFieldName\":\"陈列架投放照片家数\"}],\"viewId\":\"BI_67208b1cc44c93000168ce10\",\"viewName\":\"“冬季大进攻”（ch）统计表\"}]',1, '-10000', 1732006387247, '-10000', 1732006387247, 0);\n";
    }

    private Map<String, AggLocationEnum> getFieldIdToAggLocationEnumMap(List<TopologyTable> topologyTableList) {
        Map<String, AggLocationEnum> fieldIdToAggLocationEnumMap = Maps.newHashMap();
        topologyTableList.forEach(topologyTable -> {
            Map<String, String> statFieldLocationMap = JSON.parseObject(topologyTable.getStatFieldLocation(), new TypeReference<Map<String, String>>() {});
            statFieldLocationMap.forEach((fieldId, aggFieldLocation) -> {
                AggLocationEnum aggLocationEnum = AggLocationEnum.getAggLocationEnumByLocation(aggFieldLocation);
                fieldIdToAggLocationEnumMap.put(fieldId, aggLocationEnum);
            });
        });
        return fieldIdToAggLocationEnumMap;
    }

    private AggRuleDO getSyncAggRule(StatFieldDO statFieldDO) {
        AggRuleDO aggRule = new AggRuleDO();
        aggRule.setRuleId("BI_" + IdGenerator.get());
        aggRule.setFieldId(statFieldDO.getFieldId());
        aggRule.setThemeApiName(statFieldDO.getObjectDescribeApiName());
        aggRule.setTenantId(statFieldDO.getTenantId());
        aggRule.setDisplayName(statFieldDO.getFieldName());
        aggRule.setCreateTime(statFieldDO.getCreateTime());
        aggRule.setCreator(String.valueOf(statFieldDO.getCreator()));
        aggRule.setLastModifiedTime(statFieldDO.getLastModifiedTime());
        aggRule.setLastModifier(statFieldDO.getLastModifier());
        aggRule.setIsDeleted(statFieldDO.getIsDeleted());
        aggRule.setType(statFieldDO.getAggDimType());
        aggRule.setDescription("");
        return aggRule;
    }
}
