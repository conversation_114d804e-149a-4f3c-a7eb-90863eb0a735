package com.fxiaoke.bi.warehouse.dws.compute.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.dag.DAGBean;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyTableDao;
import com.fxiaoke.bi.warehouse.dws.db.entity.TopologyTableDO;
import com.fxiaoke.bi.warehouse.dws.model.*;
import com.fxiaoke.bi.warehouse.dws.service.TopologyTableService;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.StatViewDao;
import com.fxiaoke.bi.warehouse.dws.transform.impl.OldStatViewTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.model.MergeStatusEnum;
import com.fxiaoke.bi.warehouse.dws.utils.Constants;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.FileWriter;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Stream;

/**
 * @Author:jief
 * @Date:2023/12/23
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TopologyTableServiceTests {
  @Resource
  TopologyTableService topologyTableService;
  @Resource
  public TopologyTableDao topologyTableDao;
  @Resource
  OldStatViewTopologyTransformer oldStatViewTopologyTransformer;
  @Resource
  StatViewDao statViewDao;

  @Test
  public void testUpdateTopologyTableMergeStatus(){
    String tenantId="85145";
    String uniqueKey="0002bf06f86105d33d79428d9747c7ae";
    int mergeSize = topologyTableService.updateTopologyTableMergeStatus(tenantId, uniqueKey, MergeStatusEnum.CAL.getStatus(), 100L, null);
    System.out.println(mergeSize);
  }

  @Test
  public void testTopologyAggEffect(){
    String tenantId="82958";
    String sourceId="BI_676aa24d030e110001c3c81b";
    TopologyTable topologyTable = topologyTableDao.findByTenantIdAndSourceId(tenantId, sourceId);
    System.out.println(JSON.toJSON(topologyTable.findAggEffectApiNameMapper()));
  }

  @Test
  public void testTopologyMonitor2(){
    String tenantId="90970";
    String sourceId="BI_67b54cecce2f1600019c117f";
    TopologyTable topologyTable = topologyTableDao.findByTenantIdAndSourceId(tenantId, sourceId);
    if (topologyTable.getAggDownStream() != null) {
      topologyTableService.addDownStreamRuleWhereSql(topologyTable);
    }
    topologyTable.setStatus(1);
    Map<String, Boolean> addOrDeleteCacheMap = Maps.newHashMap();
    Map<String, List<String>> changedColumnsMap = Maps.newHashMap();
    boolean isDSCalculate = topologyTableService.downStreamAggIsCalculate(topologyTable);
    Set<String> fieldIds = topologyTable.findNeedComputeField(Sets.newHashSet("object_report_ch_all_fields__c","object_report_ch_all_fields_lang"));
    for (TopologyTableAggRule statRule : topologyTable.getStatRuleList()) {
      if(fieldIds == null || !fieldIds.contains(statRule.getFieldId())){
        continue;
      }
      long finalBatchNum = 9999L;
      //计算当前批次变更数据涉及的列，如果指标中的table以及column在当前批次没有发生变更，则不需要增量计算
      boolean result= topologyTableService.isNeedCalculateByTopology(topologyTable.getTenantId(),statRule, topologyTable.getViewId(), finalBatchNum, topologyTable.getDatabase(), addOrDeleteCacheMap, changedColumnsMap);
      if(!result){
        fieldIds.remove(statRule.getFieldId());
      }
      }
  }

  @Test
  public void testUpdateMergeByStatus(){
    String tenantId="85145";
    String uniqueKey="d911459afc32bbe1a5d345e8eaf7eccc";
    int status=MergeStatusEnum.CAL.getStatus();
    long batchNum=10L;
    long maxModifiedTime=System.currentTimeMillis();
    int mergeSize = topologyTableService.updateTopologyTableMergeByStatus(tenantId, uniqueKey, MergeStatusEnum.CAL.getStatus(), batchNum, maxModifiedTime);
    System.out.println(mergeSize);
  }
  @Test
  public void testUpdateStatus(){
    String tenantId="85145";
    long batchNum= 10L;
    int uSize = topologyTableService.updateTopologyTableMergeStatusAndVersion(tenantId,
      "", MergeStatusEnum.PREPARE.getStatus(), batchNum, null);
  }
  @Test
  public void testDeleteTopologyTableBySourceId(){
    String tenantId="85145";
    String sourceId="BI_64f9c216e8a01a00015659aa";
    boolean ok = topologyTableService.deleteTopologyTableBySourceId(tenantId, sourceId);
    System.out.println(ok);
  }

  public void testUpdateTopologyTableMergeStatus2(){
    String tenantId="85145";
//    int uSize = topologyTableService.updateTopologyTableMergeStatusAndVersion(tenantId,
//      statViewMonitor.getStatViewUniqueKey(), MergeStatusEnum.PREPARE.getStatus(), batchNum, null);
  }
  @Test
  public void testUpdateStatusTopologyTableBySourceId(){
    String tenantId="85145";
    int result=topologyTableService.updateTopologyTableStatusBySourceId(tenantId, Lists.newArrayList("BI_58acfc2537aa1badf31d169a"), TopologyTableStatus.failSkip.getValue());
    System.out.println(result);
  }
  @Test
  public void testBatchInitTopologyTableMerge(){
    String tenantId="82313";
    String sourceId="BI_659d2941e180160001ee03cb";
    StatViewBatchArg statViewBatchArg = new StatViewBatchArg();
    statViewBatchArg.setTenantId(tenantId);
//    StatViewPreArg statViewPreArg = new StatViewPreArg();
//    statViewPreArg.setSourceId(sourceId);
//    statViewPreArg.setSourceType(0);
//    List<StatViewPreArg> statViewPreArgList = Lists.newArrayList(statViewPreArg);;
//    statViewPreArgList.add(statViewPreArg);
//    statViewBatchArg.setStatViewArgList(statViewPreArgList);
//    topologyTableService.batchInitTopologyTableMerge(statViewBatchArg);
    topologyTableService.batchInitAllAggFields(statViewBatchArg);
  }

  @Test
  public void testTopologyTable(){
    String fieldId="BI_5bceda90dedd2c0001c2f54e";
    TopologyTableDO topologyTableDO=topologyTableDao.queryTopologyBySourceId("85145","BI_6570352d2009810001705e5d");
    Optional<TopologyTableAggRule> aggRuleOptional = topologyTableDO.getStatRuleList()
                                                                     .stream()
                                                                     .filter(statRule -> Objects.equals(fieldId, statRule.getFieldId()))
                                                                     .findFirst();
    if(aggRuleOptional.isPresent()){
      aggRuleOptional.get().setStatus(TopologyTableStatus.UnUsed.getValue());
      System.out.println(JSON.toJSONString(topologyTableDO.getStatRuleList()));
    }

  }

  @Test
  public void testFindViewMonitorByTenantId(){
    String tenantId="85145";
    Set<String> changedObjectDescribeApiNameSet= Sets.newHashSet("biz_account");
    Set<String> updateTables=Sets.newHashSet("biz_account");
    List<TopologyTableMonitor> monitors= topologyTableService.findViewMonitorByTenantId( tenantId,
      changedObjectDescribeApiNameSet, 0, 0L, null);
    System.out.println(monitors.size());
  }
  @Test
  public void testHasAddOrDelete(){
    String tenantId="85145";
    String viewId="v1";
    String ruleId="r1";
    String dbTable="fsbidb044003002.biz_account";
    long batchNum=1000L;
    String apiName="biz_account";
    Map<String, Boolean> addOrDeleteCacheMap = Maps.newHashMap();
    topologyTableService.hasAddOrDelete( tenantId,  viewId,  ruleId,  dbTable,  batchNum,  apiName,  addOrDeleteCacheMap);
  }

  @Test
  public void testCreateMonitorBySourceId(){
    String tenantId="82958";
    String sourceId="BI_660d0b5c35b5d6000167a6d7";
    TopologyTable topologyTable=topologyTableDao.findByTenantIdAndSourceId(tenantId, sourceId);
    Set<String> changedObjectDescribeApiNameSet= Sets.newHashSet("biz_account");
    Set<String> updateTables = Sets.newHashSet("biz_account");
    Map<String, Boolean> addOrDeleteCacheMap = Maps.newHashMap();
    Map<String, List<String>> changedColumnsMap = Maps.newHashMap();
    TopologyTableMonitor monitors= topologyTableService.trans2MonitorByTopologyTable(topologyTable,
      changedObjectDescribeApiNameSet, addOrDeleteCacheMap, changedColumnsMap, 0, 0L, null, Sets.newHashSet());
    monitors.getStatRuleMonitorList().forEach(monitor->{
      long num = Constants.AGG_DOWNSTREAM_DATA.equals(monitor.getRootTableName()) ? 5L : 10L;
      System.out.println(monitor.computeSQL(num).get(0));
    });
  }

  @Test
  public void testTopologyTableMd5() {
    String tenantId = "85145";
    String sourceId = "BI_64f1518f69223600010e9056";
    TopologyTable topologyTable = topologyTableDao.findByTenantIdAndSourceId(tenantId, sourceId);
    System.out.println(topologyTable.createMd5Key());
    String sourceId2 = "BI_656992f6598a990001eeaca4";
    TopologyTable topologyTable2 = topologyTableDao.findByTenantIdAndSourceId(tenantId, sourceId2);
    System.out.println(topologyTable2.createMd5Key());
    String sourceId3 = "BI_656992ff598a990001eeacf0";
    TopologyTable topologyTable3 = topologyTableDao.findByTenantIdAndSourceId(tenantId, sourceId3);
    System.out.println(topologyTable3.createMd5Key());
  }

  @Test
  public void testWideTables() throws Exception {
    try (FileWriter writer = new FileWriter("D:\\常用脚本\\widetable\\biz_account.json", Charset.defaultCharset())) {
      String tenantId = "82958";
      List<TopologyTable> tableDOS = topologyTableService.findCombineTopologyTable(tenantId, new int[] {0, 1, 2});
      String checkObj = "biz_account";
      System.out.println("tableDOS size:" + tableDOS.size());
      List<TopologyTableAggRule> topologyTableAggRules = tableDOS.stream().flatMap(tb -> {
        if (CollectionUtils.isNotEmpty(tb.getStatRuleList())) {
          return tb.getStatRuleList().stream();
        }
        return Stream.empty();
      }).filter(statRule -> Objects.equals(checkObj, statRule.getRootNodeTable().getName())).toList();
      DAGBean dagBean = new DAGBean();
      System.out.println("topologyTableAggRules size=" + topologyTableAggRules.size());
      topologyTableAggRules.forEach(ttr -> ttr.selectTablesBFS(dagBean));
      String dagJSON = JSON.toJSONString(dagBean);
      writer.write(dagJSON);
      System.out.println(dagJSON);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }



  @Test
  public void testSelectTableColumns(){
    String tenantId = "74745";
    String viewId = "BI_5dd60bceb60ace0001916cf7";
    TopologyTable topologyTable=topologyTableDao.findByTenantIdAndSourceId(tenantId, viewId);
    List<TopologyTableAggRule> statRules= topologyTable.getStatRuleList();
    statRules.forEach(statRule->{
      System.out.println("fieldId==============="+statRule.getFieldId());
      System.out.println(JSON.toJSONString(statRule.selectTableAndColumn()));
    });
  }

  @Test
  public void testDownStreamAggIsCalculate() {
    String tenantId = "82958";
    String viewId = "BI_663b20e978676e0001223cc5";
    TopologyTable topologyTable=topologyTableDao.findByTenantIdAndSourceId(tenantId, viewId);
    boolean b = topologyTableService.downStreamAggIsCalculate(topologyTable);
    System.out.println("sssssssss:" + b);
  }
  @Test
  public void testDeleteTopologyTables(){
    ArrayList<String> viewIds = Lists.newArrayList("BI_66e2858a46d8fb00015db076", "BI_66e265985fcc3f0001efad8d", "BI_66e2cf604205c50001f168d2", "BI_66e2d5814205c50001f16a52", "BI_66e2b8525fcc3f0001efcfe2", "BI_66e3dc580633c90001882be2", "BI_66d2f67353f40e00015962c8", "BI_66d2ecac53f40e000159612c", "BI_66e166bb5fcc3f0001eee56b", "BI_66e2aeb302381c00018839d4", "BI_66e2d3ed6bf52700011fb9a0", "BI_66dfb3151a77910001c342b0", "BI_66e1864a5fcc3f0001eeeb40", "BI_66e28cd55fcc3f0001efb7c7", "BI_66d2dbf553f40e0001595e5f", "BI_66d2ddff53f40e0001595eea", "BI_66e52a777d297e0001f1aaae", "BI_66ebc18738069300015b9170", "BI_66e404f50633c90001883833", "BI_66e2b82c5fcc3f0001efcf8d", "BI_66e543cb7d297e0001f1b463", "BI_66e54f2b7d297e0001f1b63f", "BI_66d689b253f40e00015a8e50");
    topologyTableService.deleteTopologyTables("84439", viewIds, 0);
  }
  @Test
  public void testChangeColumn(){
    Map<String, List<String>> changedColumnsMap= Maps.newHashMap();
    changedColumnsMap.put("sale_action_stage|sale_action_stage",Lists.newArrayList("value33"));
    topologyTableService.getChangedColumns("85145","BI_65d1bf63f5da360001e43065", "BI_63733a2452a9e40001e9fd30", "fsbidb044003001.sale_action_stage",7278L, "sale_action_stage", changedColumnsMap);

  }
  @Test
  public void testChangeTopologyTable2PreparedByKey(){
    String tenantId="85145";
    String statViewUniqueKey="014859300e3da6fb8b3ea91af66d311a";
    topologyTableService.changeTopologyTable2PreparedByKey(tenantId,statViewUniqueKey);
//    topologyTableService.changeTopologyTable2NoNeedCalByKey(tenantId,statViewUniqueKey);
  }

  @Test
  public void testGetTableKeys(){
    String tenantId="85145";
    String viewId="BI_656443d1d84c250001838912";
    TopologyTable topologyTable=topologyTableDao.findByTenantIdAndSourceId(tenantId, viewId);
    Map<String, List<String>> tableKeys = topologyTableService.getTableKeys(topologyTable);
    for(String table : tableKeys.keySet()){
      System.out.println(String.format("%s: %s", table, Joiner.on(",").join(tableKeys.get(table))));
    }
  }
  @Test
  public void testToViewSQL(){
    String tenantId="71570";
    String viewId="BI_6661641298857f0001816062";
    TopologyTable topologyTable=topologyTableDao.findByTenantIdAndSourceId(tenantId, viewId);
    System.out.println(topologyTable.toViewSQL(-1,-1,null,null));
  }

  @Test
  public void testStopThemeApiName() {
    String tenantId = "71570";
    String themeName = "biz_account";
    List<TopologyTableDO> topologyTableDOS = topologyTableService.queryTopologyTableByApiName(tenantId, TopologyTableStatus.allStatus(), themeName, 0);
    System.out.println(topologyTableDOS);
  }


}
