package com.fxiaoke.bi.warehouse.dws.compute.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.model.StatViewBatchArg;
import com.fxiaoke.bi.warehouse.dws.model.StatViewPreArg;
import com.fxiaoke.bi.warehouse.dws.model.StatViewPreSQL;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableStatus;
import com.fxiaoke.bi.warehouse.dws.service.StatTopologyService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author:jief
 * @Date:2023/11/21
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class StatTopologyServiceTest {
  @Resource
  StatTopologyService statTopologyService;

  @Test
  public void testCreateTopology() {
    String tenantId = "71570";
    ArrayList<String> viewIds = Lists.newArrayList("BI_6762b6b025c19000012f5e3d");
    for (String viewId : viewIds) {
      try {
        statTopologyService.doCreateTopology(tenantId, viewId, 0, TopologyTableStatus.Prepared.getValue());
      } catch (Exception e) {
        log.error("testCreateTopology",e);
      }
    }
  }

  @Test
  public void testReCreateTopologyByEI(){
    String tenantId = "82958";
    statTopologyService.reCreateTopologyByEI(tenantId,TopologyTableStatus.Prepared.getValue());
  }

  @Test
  public void testRest(){
    StatViewBatchArg statViewBatchArg= new StatViewBatchArg();
    statViewBatchArg.setTenantId("85145");
    List<StatViewPreArg> statViewArgList= Lists.newArrayList();
    StatViewPreArg statViewPreArg= new StatViewPreArg();
    statViewPreArg.setSourceId("BI_654b0055d8b366000168a409");
    statViewArgList.add(statViewPreArg);
    statViewBatchArg.setStatViewArgList(statViewArgList);
    statViewBatchArg.setStatus(1);
    statTopologyService.resetTopologyByEi(statViewBatchArg);
  }

  @Test
  public void testMaxTime(){
    StatViewPreArg statViewPreArg = new StatViewPreArg();
    statViewPreArg.setTenantId("85145");
    statViewPreArg.setSourceId("BI_6597752e730a380001f4501c");
    Map<String, Long> result= statTopologyService.queryDBLatestSyncTimeByEI(statViewPreArg);
    System.out.println(JSON.toJSONString(result));
  }

  @Test
  public void testBatchCreateTopology() {
    String tenantId = "90250";
    statTopologyService.batchCreateTopologyByEi(tenantId, 0, "1970-01-01 00:00:00",true);
  }
  @Test
  public void testCalLatestAggTimeByTables(){
    String tenantId="85145";
    String effectApiNames="{\"BI_5bebdd6b56fc110a30566eaa\":[\"new_opportunity\",\"org_employee_user\"]}";
    Optional<Long> a=  statTopologyService.calLatestAggTimeByTables(tenantId,effectApiNames);

    System.out.println(a.orElse(0L));
  }

  @Test
  public void testCreatePreViewSQL() throws IOException {
    File file = ResourceUtils.getFile("classpath:stat/StatView_1.json");
    String json = Files.readString(file.toPath());
    StatViewPreArg statViewPreArg=JSON.parseObject(json,StatViewPreArg.class);
    StatViewPreSQL statViewPreSQL= statTopologyService.createStatViewPreSQL(statViewPreArg);
    System.out.println(statViewPreSQL.getPreViewSQL());
    System.out.println(statViewPreSQL.getMaxModifiedTime());
  }
}
