package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.db.dao.DBUpdateEventDao;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.service.StatTopologyService;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.OldGoalRuleDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.GoalRuleDO;
import com.fxiaoke.bi.warehouse.dws.transform.model.ColumnDefinition;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2023/7/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class OldGoalRuleDaoTest {

  @Resource
  private OldGoalRuleDao oldGoalRuleDao;
  @Resource
  private MappingService mappingService;
  @Resource
  private StatTopologyService statTopologyService;
  @Resource
  private DBUpdateEventDao dbUpdateEventDao;


  @Test
  public void testFindMaxModifiedTime(){
    String tenantId="85145";
    String pgDb="*****************************************";
    String pgSchema="public";
    Set<String> tableNames = Sets.newHashSet("biz_account","feed_relation","org_employee_user");
    long result = dbUpdateEventDao.findMaxModifiedTime( pgDb,pgSchema,"6412b13f5cd44942982c91b7",  tableNames);
    System.out.println(result);
  }

  @Test
  public void testOldGoal() {
    String tenantId = "71570";
    String aggApiName = mappingService.biApiName("ProductObj");
    String ruleId = "5fcdcb621194be0001220dbf";
    GoalRuleDO goalRuleDO = oldGoalRuleDao.getRuleEntityById(tenantId, ruleId);
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    //    SubGoalDimRule subGoalDimRule = oldGoalRuleDao.createSubGoalDimRule(tenantId, false, aggApiName, goalRuleDO,
    //      cachedTableDefinitions);
        System.out.println(JSON.toJSONString(goalRuleDO));
  }

  @Test
  public void testRemoveFilter() {
    String tenantId = "71570";
    String aggRuleId = "5fcdcb621194be0001220dbf|5fcdcb721194be00012210bf";
    Map<String, Object> aggRuleInfoMapper = oldGoalRuleDao.findAggRuleMapFromDB(tenantId, aggRuleId);
    JSONObject aggRuleInfo = new JSONObject(aggRuleInfoMapper);
    GoalRuleDO goalRuleDO = oldGoalRuleDao.getRuleEntityById(tenantId, "5fcdcb621194be0001220dbf");
    oldGoalRuleDao.removeSubGoalFilter(tenantId, aggRuleInfo, goalRuleDO);
    System.out.println(aggRuleInfo.getString("wheres"));
  }

}
