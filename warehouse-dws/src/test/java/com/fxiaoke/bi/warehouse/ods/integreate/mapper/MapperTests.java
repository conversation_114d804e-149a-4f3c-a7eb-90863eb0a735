package com.fxiaoke.bi.warehouse.ods.integreate.mapper;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.core.db.mapper.BiAggSyncInfoMapper;
import com.fxiaoke.bi.warehouse.ods.args.BIAggSyncInfoArg;
import com.fxiaoke.bi.warehouse.ods.integrate.ch.dao.mapper.AggDataSyncInfoMapper;
import com.fxiaoke.bi.warehouse.ods.integrate.pg.dao.mapper.BizEnterpriseRelationMapper;
import com.google.common.collect.Sets;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.assertj.core.util.Lists;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Author:jief
 * @Date:2024/4/2
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class MapperTests {
  @Autowired
  AggDataSyncInfoMapper aggDataSyncInfoMapper;
  @Autowired
  BizEnterpriseRelationMapper bizEnterpriseRelationMapper;
  @Autowired
  BiAggSyncInfoMapper biAggSyncInfoMapper;

  @Test
  public void testQueryAggDataSyncInfo() {
    List<Map<String, Object>> result = aggDataSyncInfoMapper.setTenantId("82958").queryAggDataSyncInfo();
    System.out.println("aaaaaa"+JSON.toJSONString(result));
  }

  @Test
  public void test22(){
   List<Map<String,String>> result= bizEnterpriseRelationMapper.setTenantId("82958").queryBizEnterpriseRelationMap("82958");
    System.out.println(result.stream().collect(Collectors.toMap(r -> r.get("object_id"), r2 -> r2.get("ea"))));
  }

  @Test
  public void testAddBiAggSyncInfo(){
    List<BIAggSyncInfoDO> biAggSyncInfoDO= Lists.newArrayList();
    BIAggSyncInfoArg biAggSyncInfoArg= new BIAggSyncInfoArg();
    biAggSyncInfoArg.setId(ObjectId.get().toString());
    biAggSyncInfoArg.setLastSyncTime(new Date().getTime());
    biAggSyncInfoArg.setBatchNum(0L);
    biAggSyncInfoArg.setStatus(-2);
    biAggSyncInfoArg.setVersion(0);
    biAggSyncInfoArg.setDownstreamEis("");
    biAggSyncInfoArg.setTenantId("71570");
    biAggSyncInfoArg.setLastModifiedTime(new Date().getTime());
    biAggSyncInfoDO.add(biAggSyncInfoArg.toBiAggSyncInfoDO());
    biAggSyncInfoMapper.setTenantId("-1").upsertSyncDBInfo(biAggSyncInfoDO, Sets.newHashSet("id", "tenant_id"));
  }
}
