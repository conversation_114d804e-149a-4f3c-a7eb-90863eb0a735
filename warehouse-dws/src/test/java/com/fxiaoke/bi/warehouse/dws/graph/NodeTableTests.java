package com.fxiaoke.bi.warehouse.dws.graph;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.common.db.er.ColumnType;
import com.fxiaoke.bi.warehouse.common.db.er.NodeColumn;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import junit.framework.TestCase;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2023/9/17
 */
public class NodeTableTests extends TestCase {

  @Test
  public void testArrayJoin() {
    NodeColumn nodeColumn = new NodeColumn();
    nodeColumn.setName("name");
    nodeColumn.setType(ColumnType._String);
    NodeTable nodeTable = NodeTable.of("stage_task", "stage_task_1", Lists.newArrayList(nodeColumn),
      Sets.newTreeSet(), "stage_task", "stage_id", false, "node123",null);
    nodeTable.appendColumns("candidate_ids");
    nodeTable.appendColumns("value3");
    nodeTable.appendColumns("locked_ids");
    nodeTable.addArrayJoinColumn("candidate_ids",ColumnType._ARRAY);
    nodeTable.addArrayJoinColumn("value3",ColumnType._String);
    nodeTable.addArrayJoinColumn("locked_ids",ColumnType._ARRAY);
    Map<String, List<String>> tableKeys = Maps.newHashMap();
    tableKeys.put("stage_task", Arrays.asList("tenant_id","bi_sys_flag","id"));
    Map<String,String> mostRightLargeTable = Maps.newHashMap();
    mostRightLargeTable.put("stage_task_1","10000");
    System.out.println(nodeTable.createFromTableSQL("bi_112","85145",500,true, mostRightLargeTable,false, tableKeys, false, false,null));
    System.out.println(nodeTable.createFromTableSQL("bi_112","85145",-1,true, null,false, tableKeys, false, false,null));

  }

  @Test
  public void testToView() throws Exception{
    BufferedReader reader=new BufferedReader(new FileReader(new File("C:\\Users\\<USER>\\Downloads\\782557_topology.txt")));
    String json=  reader.readLine();
    TopologyTable topologyTable= JSON.parseObject(json,TopologyTable.class);
    System.out.println(topologyTable.getStatRuleList().size());
  }
}
