package com.fxiaoke.bi.warehouse.dws.model;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.arg.AggRequestContext;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.er.ColumnType;
import com.fxiaoke.bi.warehouse.common.db.er.NodeColumn;
import com.fxiaoke.bi.warehouse.common.goal.BITopology;
import com.fxiaoke.bi.warehouse.common.goal.DisplayField;
import com.fxiaoke.bi.warehouse.common.goal.Edge;
import com.fxiaoke.bi.warehouse.common.mq.message.ViewChangeMessage;
import com.fxiaoke.bi.warehouse.common.util.*;
import com.fxiaoke.bi.warehouse.dws.agg.bean.AggCalResult;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.AggRuleDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.StatViewDO;
import com.fxiaoke.bi.warehouse.dws.transform.model.BiMtRule;
import com.fxiaoke.bi.warehouse.dws.utils.Utils;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.helper.CollectionHelper;
import com.fxiaoke.helper.JoinHelper;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import com.google.common.util.concurrent.Uninterruptibles;
import junit.framework.TestCase;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Date;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class CommonTest extends TestCase {

  @Test
  public void testConfig(){
    System.setProperty("process.profile","fstest");
    boolean isAllow1 = GrayManager.isAllowByRule("skip_explain_unique_key", "807313");
    boolean isAllow2 = GrayManager.isAllowByRule("skip_explain_unique_key", "1^9f380b72107a746f9c32ede48936325b");
    System.out.println(isAllow1);
    System.out.println(isAllow2);
    Assert.equals(isAllow1,true);
    Assert.equals(isAllow2,true);
  }
  @Test
  public void testAA(){
    System.out.println(10%10);
    System.out.println(3%10);
    System.out.println(192873%10);
    System.out.println(888%10);
    //    0-9
  }
  @Test
  public void testLong(){
    Object a = 1741079327473L/1000L;
    System.out.println(a.getClass());
    System.out.println(a);
  }

  @Test
  public void testListAdd2(){
    List<String> orderByColumns = Lists.newArrayList("tenant_id", "view_id", "view_version", "object_id","bi_sys_flag" ,"hash_code");
    orderByColumns.add(1,"policy_id");
    System.out.println(JSON.toJSONString(orderByColumns));
  }
  @Test
  public void testSubtract(){
    List<Set<String>> pushDownFieldsList = Lists.newArrayList();
    Set<String> a = Sets.newHashSet("a","b","c");
    Set<String> b = Sets.newHashSet("a","f","c");
    Set<String> c = Sets.newHashSet();
    pushDownFieldsList.add(a);
    pushDownFieldsList.add(b);
    pushDownFieldsList.add(c);
    Set<String> pushDownFields = Sets.newHashSet();
    if(pushDownFieldsList.size()>0){
      pushDownFields.addAll(pushDownFieldsList.getFirst());
      for(int i=1;i<pushDownFieldsList.size();i++){
        pushDownFields = CommonUtils.findIntersection(pushDownFields, pushDownFieldsList.get(i));
      }
    }
    System.out.println(JSON.toJSONString(pushDownFields));
  }

  @Test
  public void testListAdd(){
    List<AggCalResult.CalPrepare> calPrepares = Lists.newArrayList();
    AggCalResult aggCalResult = new AggCalResult();
    aggCalResult.setCalPrepares(calPrepares);
//    AggCalResult.CalPrepare calPrepare = new AggCalResult.CalPrepare();
//    calPrepare.setInc2CalAfter("heh");
//    calPrepare.setCal2StockBefore("1111");
//    calPrepares.add(calPrepare);
//    System.out.println(JSON.toJSONString(aggCalResult));

    List<List<AggCalResult.CalPrepare>> calPrepareParts = Lists.partition(calPrepares, (int) Math.ceil(calPrepares.size() / WarehouseConfig.insertThreadRate));
    System.out.println(calPrepareParts.size());
  }
  @Test
  public void testEscapeSql(){
    Assert.equals(CommonUtils.escapeSql("McHale's Navy"),"McHale''s Navy");
  }

  @Test
  public void testSets(){
    String useGrayPgDb = "";
    Set<String> grayPgDBs = Sets.newHashSet(Splitter.on(CharMatcher.anyOf(",|"))
                                                    .omitEmptyStrings()
                                                    .splitToList(useGrayPgDb));
    System.out.println(grayPgDBs);
  }
  @Test
  public void testEnumEquals(){
    System.out.println(SyncStatusEnum.createFromStatus(2) == SyncStatusEnum.AGG_ED);
  }

  @Test
  public void testRequestContext(){
    AggRequestContext aggRequestContext = AggRequestContext.getInstance();
    Map<String,List<String>> a = Maps.newHashMap();
    a.put("aa",Lists.newArrayList("1","2"));
    aggRequestContext.putRequestArg("arg",a);
    Map<String,List<String>> b = aggRequestContext.getRequestArg("arg",new TypeReference<Map<String,List<String>>>(){});
    System.out.println(JSON.toJSONString(b));
//    TypeReference<Map<String,List<String>>> type= new TypeReference<Map<String,List<String>>>(){};
//    System.out.println(type.getType());
  }
  @Test
  public void testArraySort(){
    DBUpdatedEvent dbUpdatedEvent= new DBUpdatedEvent();
    dbUpdatedEvent.setBatchNums(new Long[]{6L,5L,7L,8L,3L});
    System.out.println(JSON.toJSONString(dbUpdatedEvent.sortedBatchNums()));
  }
  @Test
  public void testCeil()throws Exception{
//    System.out.println((int) Math.ceil(1/10d));
//    System.out.println(String.join(",", Lists.newArrayList()));
    Long[] batchNums = Splitter.on(",")
                               .splitToList(String.join(",", Lists.newArrayList()))
                               .stream()
                               .filter(StringUtils::isNotBlank)
                               .map(Long::parseLong)
                               .toArray(Long[]::new);
    Arrays.sort(batchNums);
    System.out.println(batchNums.length);
  }
  @Test
  public void testArrays(){
    List<Long> syncFlowBatchNums = Lists.newArrayList();
    Long[] a = new Long[]{1L,2L,3L,4L,5L};
    syncFlowBatchNums.addAll(Arrays.stream(a).toList());
    System.out.println(JSON.toJSONString(syncFlowBatchNums));

    String inBatchIds = Lists.newArrayList(a).stream().map(String::valueOf).collect(Collectors.joining(","));
    System.out.println(inBatchIds);
  }
  @Test
  public void testSort2(){
    long[] batchNums = new long[]{5L,1L,2L,3L};
    Arrays.sort(batchNums);
    System.out.println(batchNums[batchNums.length-1]);
    System.out.println(JSON.toJSONString(batchNums));
  }

  @Test
  public void testChar(){
    char a= 91;
    char b= 93;
    System.out.println(""+a);
    System.out.println(""+b);
  }
  @Test
  public void testEuqueAny(){
    System.out.println(CommonUtils.equalsAny(1,1,2));
    System.out.println(CommonUtils.equalsAny(3,1,2));
  }
  @Test
  public void testDateCompare(){
    StatViewDO statViewDO= new StatViewDO();
    statViewDO.setUpdateTime(new Date(1714492800000L));
    System.out.println(statViewDO.activeInDays(35));
  }
  @Test
  public void testJSON2(){
    String json2="{\"tenantId\":\"714667\"}";
    StatViewBatchArg sta= JSON.parseObject(json2, StatViewBatchArg.class);
    System.out.println(JSON.toJSONString(sta));
  }
  @Test
  public void testDelayTime(){
    for(int rt=0;rt<50;rt++){
      System.out.println(Utils.calculateWaitTime(rt,10,25));
    }
  }
  @Test
  public void testJson2Map() {
    Map<String, Float> tenantThreadRate = JSON.parseObject("{}", new TypeReference<>() {
    });
    System.out.println(tenantThreadRate.getOrDefault("71570", 4.0f));
  }

  @Test
  public void testRandom(){
    Random random = new Random();
    for(int i=0;i<10;i++){
      System.out.println(random.nextInt(100));
    }
  }
  @Test
  public void testColumnType(){
    DimConfig dimConfig= DimConfig.parse("biz_leads_1:is_overtime:dim_boolean_1:_Boolean:true_or_false","");
    System.out.println(dimConfig.getDstColumnType());
  }

  @Test
  public void testNumeric() {
    //    System.out.println(StringUtils.isNumericSpace("-100"));
    //    System.out.println(StringUtils.isNumericSpace("100"));
    //    System.out.println(StringUtils.isNumericSpace("+100"));

    Pattern a = Pattern.compile("^[1-9]\\d*$");
    System.out.println(a.matcher("0123").matches());
    System.out.println(a.matcher("1").matches());
    System.out.println(a.matcher("1203").matches());
    System.out.println(a.matcher("12 ").matches());
    System.out.println(a.matcher(" 12").matches());
    System.out.println(a.matcher(" !12 ").matches());
    System.out.println(a.matcher("12ab").matches());
    System.out.println(a.matcher("").matches());
  }

  public void testStringFormats(){
    DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    String tenantId="721787";
    LocalDateTime localDateTime = LocalDateTime.now();
    localDateTime=localDateTime.plusDays(-180);
    String fromDate = localDateTime.format(DATE_TIME_FORMATTER);
    String sql = String.format(
      "select viewId from biz_log.biz_log_bi_agg_local where tenantId='%s' and day >= '%s' " +
        " and appName like '%s' and viewId <> '' group by viewId", tenantId, fromDate,"fs-bi-stat%");
    System.out.println(sql);
  }

  @Test
  public void testRateLimit() {
    RateLimiter rateLimiter = RateLimiter.create(2.0D);
    for (int i = 0; i < 100; i++) {
      System.out.println(rateLimiter.tryAcquire(Duration.ZERO));
      Uninterruptibles.sleepUninterruptibly(100, TimeUnit.MILLISECONDS);
    }
  }

  @Test
  public void testEnum2() {
    String a = "fstest-gray,foneshare-gray";
    String[] profiles = a.split(",");
    if (StringUtils.equalsAny(ConfigHelper.getProcessInfo().getProfile(), profiles)) {
      System.out.println("skip");
    }
  }

  @Test
  public void testCount() {
    Set<String> tenantIdSet= Sets.newHashSet("1","2","3","4","5");
//    System.out.println(JSON.toJSONString(tenantIdSet));
    Set<String> black=Sets.newHashSet("1","2","3");
    String eiInFilterStr = tenantIdSet.stream()
                                    .filter(ei -> !black.contains(ei))
                                    .map(ei -> "'" + ei + "'")
                                    .collect(Collectors.joining(","));
    System.out.println(eiInFilterStr);
  }

  @Test
  public void testForEach2() {
    Map<String,List<String>> effectApiNameMap= Maps.newHashMap();
    effectApiNameMap.put("a",Lists.newArrayList("1","2"));
    effectApiNameMap.put("b",Lists.newArrayList("5","4"));
    Map<String,Integer> allAggFieldMap = effectApiNameMap.keySet().stream().collect(Collectors.toMap(k->k,v->1));
    System.out.println(JSON.toJSONString(allAggFieldMap));

  }

  @Test
  public void testMapper() {
    DBUpdatedEvent dbUpdatedEvent = new DBUpdatedEvent();
    Map<String, Set<String>> tableTenantIdSetMap = new HashMap<>();
    tableTenantIdSetMap.put("biz_account", Sets.newHashSet("71570", "85145"));
    tableTenantIdSetMap.put("object_data", Sets.newHashSet("85145"));
    tableTenantIdSetMap.put("org_employee_user", Sets.newHashSet("78060"));
    dbUpdatedEvent.setTableTenantIdSetMap(tableTenantIdSetMap);
    System.out.println(dbUpdatedEvent.buildTenantIdTableMapper());
  }

  @Test
  public void testArrayCompare() {
    List<String> changeApiNames = Lists.newArrayList("a", "b");
    List<String> effectApiNames = Lists.newArrayList("d");
    System.out.println(CollectionHelper.hasIntersection(changeApiNames, effectApiNames));
  }

  @Test
  public void testCompare() {
    List<String> a = Lists.newArrayList("new_opportunity_1.order:sum:agg_sum_2", "new_opportunity_1" +
      ".amount:sum:agg_sum_1", "new_opportunity_1.num:sum:agg_sum_3");
    Collections.sort(a);
    List<String> b = Lists.newArrayList("new_opportunity_1.amount:sum:agg_sum_1", "new_opportunity_1" +
      ".order:sum:agg_sum_2");
    Collections.sort(b);
    System.out.println(String.join(",", a));
    System.out.println(String.join(",", b));
    System.out.println(String.join(",", a).equals(String.join(",", b)));
  }

  @Test
  public void testBoolean() {
    //    System.out.println(BooleanUtils.toBoolean("true"));
    //    System.out.println(BooleanUtils.toBoolean("false"));
    //    System.out.println(BooleanUtils.toBoolean("1"));
    //    System.out.println(BooleanUtils.toBoolean("0"));
    DBUpdatedEvent result = new DBUpdatedEvent();
    result.setPgJdbcUrl("***********************************************");
    System.out.println(result.pgDbName());
  }



  @Test
  public void testStopWatch() {
    StopWatch stopWatch = StopWatch.createStarted("dbUpdateEvent:1");
    stopWatch.start("A");
    Uninterruptibles.sleepUninterruptibly(2, TimeUnit.SECONDS);
    stopWatch.stop();
    stopWatch.start("B");
    Uninterruptibles.sleepUninterruptibly(2, TimeUnit.SECONDS);
    stopWatch.stop();
    //    stopWatch.stop();
    System.out.println(stopWatch.prettyPrint());
  }

  @Test
  public void testUrlEncode() {
    String encoder = URLEncoder.encode("jALTER TABLE agg_data on cluster '{cluster}' ADD COLUMN IF NOT EXISTS " +
      "`dim_boolean_1` Nullable(Bool) AFTER dim_decimal_30", StandardCharsets.UTF_8);
    System.out.println(encoder);
    System.out.println(URLDecoder.decode(encoder, StandardCharsets.UTF_8));
  }

  @Test
  public void testMd5() {
    System.out.println(Utils.md5("aaaaa"));
  }

  @Test
  public void testSplitter() {
    String customerSampleLimit = "1^100,2^300";
    Map<String, Integer> customerSampleLimitMap = Splitter.on(CharMatcher.anyOf(",|"))
                                                          .omitEmptyStrings()
                                                          .splitToList(customerSampleLimit)
                                                          .stream()
                                                          .collect(Collectors.toMap(limit -> limit.split("\\^")[0],
                                                            limit -> {
                                                            String num = limit.split("\\^")[1];
                                                            if (StringUtils.isNumeric(num)) {
                                                              return Integer.parseInt(num);
                                                            }
                                                            return 100000;
                                                          }));
    System.out.println(JSON.toJSONString(customerSampleLimitMap));
  }

  @Test
  public void testTimeStamp() {
    DateTimeFormatter dateTimeFormatterWithZone = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    int fromYear = 2020;
    int toYear = 2025;
    String year = "2023";
    ZonedDateTime fromZonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(1694534400000L),
                                                     ZoneId.systemDefault())
                                                   .plusYears(fromYear - Integer.parseInt(year));
    ZonedDateTime toZonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(1694534400000L),
                                                   ZoneId.systemDefault())
                                                 .plusYears(toYear - Integer.parseInt(year));

    System.out.println(fromZonedDateTime.format(dateTimeFormatterWithZone));
    System.out.println(toZonedDateTime.format(dateTimeFormatterWithZone));

    String start = String.format("%s%02d%02d", fromZonedDateTime.getYear(), fromZonedDateTime.getMonth()
                                                                                             .getValue(),
      fromZonedDateTime.getDayOfMonth());
    String end = String.format("%s%02d%02d", toZonedDateTime.getYear(), toZonedDateTime.getMonth()
                                                                                       .getValue(),
      toZonedDateTime.getDayOfMonth());
    System.out.println(start);
    System.out.println(end);

  }

  @Test
  public void testZone() {
    List<String> a = Lists.newArrayList("a", "b", "c");
    a.forEach(ei -> {
      if (ei.equals("b")) {
        return;
      }
      System.out.println(ei);
    });
    //    DateTimeUtil.covertYYYYMMDD2Timestamp("20220801", 1L,);
  }

  @Test
  public void testSetAdd() {
    System.out.println(ObjectId.get().toString());
    System.out.println(ObjectId.get().toString());
    System.out.println(ObjectId.get().toString());
    //    Set<DisplayField>
    //    DisplayField displayField= DisplayField.builder().dbFieldName("a").statFieldId("1").lookupName(false)
    //    .isSingle(1).disPlayType(DisplayField.DisplayType.group).build();
    //    DisplayField displayField2= DisplayField.builder().dbFieldName("a").statFieldId("1").lookupName(false)
    //    .isSingle(1).disPlayType(DisplayField.DisplayType.group).build();

  }

  @Test
  public void testRegex2() {
    Pattern regx = Pattern.compile("bi_mt_data_tag_v\\(tenantId='(\\d+)',apiName='(.+)'\\)");
    Matcher matcher = regx.matcher("bi_mt_data_tag_v(tenantId='85145',apiName='AccountObj')");
    if (matcher.matches()) {
      String tenantId = matcher.group(1);
      String apiName = matcher.group(2);
      System.out.printf(String.format("bi_mt_data_tag_v_%s_%s", tenantId, apiName));
    }
  }

  @Test
  public void testMtTagSQL() {
    //    System.out.println(MtTagViewTable.createSubQuerySQL("fsbidb044003001","85145","AccountObj"));

    //    System.out.println(Constants.aggUniqColumnRex.matcher("agg_uniq_1").matches());
    String dstColumn = "agg_uniq_1";
    dstColumn = "agg_sum_1";
    //    dstColumn="agg_count_1";
    System.out.println(dstColumn.substring(4, dstColumn.indexOf("_", 4)));
  }

  @Test
  public void testListRemove() {
    List<String> l = Lists.newArrayList("1", "2", "3", "action_date");
    List<String> b = l.stream().filter(name -> !Objects.equals(name, "action_date")).toList();
    StringBuilder sb = new StringBuilder();
    sb.append(String.join(",", b));
    sb.append("----->");
    b.remove("2");
    sb.append(String.join(",", b));
    System.out.println(sb.toString());
  }

  @Test
  public void testToMap() {
    List<Pair<String, String>> tableApiNamePair = Lists.newArrayList();
    tableApiNamePair.add(Pair.build("1", "b"));
    tableApiNamePair.add(Pair.build("1", "c"));
    tableApiNamePair.add(Pair.build("2", "d"));
    tableApiNamePair.add(Pair.build("3", "e"));
    Map<String, Set<String>> result = tableApiNamePair.stream()
                                                      .collect(Collectors.groupingBy(Pair::getKey,
                                                        Collectors.mapping(Pair::getValue, Collectors.toSet())));
    System.out.println(JSON.toJSONString(result));
  }

  @Test
  public void testForEach() {
    List<String> a = Lists.newArrayList("1", "2", "3", "4");
    a.forEach(item -> {
      if (item.equals("3")) {
        return;
      }
      System.out.println(item);
    });
  }

  @Test
  public void testSort() {
//    List<Integer> a = Lists.newArrayList(2, 4, 1, 3, 5, 6, 10, 7);
//    System.out.println(a.stream().max(Comparator.naturalOrder()).get());

    List<AggRuleDO> aggRules = Lists.newArrayList();
    AggRuleDO aggRuleDO1= new AggRuleDO();
    aggRuleDO1.setFieldId("BI_600acc7801d003000167012d");
    AggRuleDO aggRuleDO2= new AggRuleDO();
    aggRuleDO2.setFieldId("BI_600acc7801d003000167012b");
    aggRules.add(aggRuleDO1);
    aggRules.add(aggRuleDO2);
//    aggRules.add(null);
    //指标作为筛选条件暂时也作为agg指标计算，后面想通了再做处理
    //排序方便图合并生成的stat_json_list 相似度更高
    aggRules = aggRules.stream().distinct().collect(Collectors.toList());
    aggRules.sort(Comparator.comparing(AggRuleDO::getFieldId));
//    System.out.println(JSON.toJSONString(aggRules));

//    aggRules.addAll(statViewEntity.getAggRuleFilterIds());
//
  }

  @Test
  public void testDBName() {
    String url = "";
    int pos = url.lastIndexOf(63);
    if (pos != -1) {
      url = url.substring(0, pos);
    }
    int begin = url.lastIndexOf(47);
    if (begin == -1) {
      System.out.println("unknown");
    } else {
      String db = url.substring(begin + 1);
      Pattern pattern = Pattern.compile("(fsdb|fsbidb)(\\d+)(\\d{3})");
      Matcher matcher = pattern.matcher(db);
      System.out.println(matcher.matches() ? matcher.group(1) + matcher.group(2) : db);
    }
  }

  @Test
  public void testSQL() {
    SQL sql = new SQL();
    sql.SELECT("a,b").SELECT("c").FROM("goal_value").WHERE("tenant_id='71570'", "goal_rule_id='aaaa'").WHERE("bb=1");
    sql.SELECT("d");
    System.out.println(sql.toString());
  }

  @Test
  public void testJSONs() {
    StatViewBatchArg statViewBatchArg = JSON.parseObject("{\"tenantId\":\"71570\"," +
      "\"statViewArgList\":[{\"sourceType\":0,\"sourceId\":\"64f82817685f98000120d8a7\"}]}", StatViewBatchArg.class);
    System.out.println(statViewBatchArg.statViewArgList.size());
    System.out.println(JSON.toJSONString(statViewBatchArg.statViewArgList));
  }

  @Test
  public void testDecode() {
    //bi pg
    //        System.out.println(PasswordUtil.decode
    //        ("DE0FB1A3552C81D4D4D09A7C516BBE3ECB11B446906A1389645FE72FA8A39E8A"));
    //        System.out.println(PasswordUtil.encode("VT@wbf5t"));
    //bi ch
    //    System.out.println(PasswordUtil.decode("2220EC6040679DEAC6A3D0FAD3D4D1FD07DBDF114A075151E061D962392A1357"));
    //bi mongo agg_dispatch
    //    System.out.println(PasswordUtil.decode
    //    ("2CB2B4897BEDBE39AD14088EC7978DDCB75C8729B557ECC938F31A4CB315C479F6404720DC6C6DCA"));
    //agg mq dispatch
    //    System.out.println(PasswordUtil.decode("5A983E63364FDE2C69F59FE0DAA797F850DB7EEA81C64406BC1DA44DAC855F1C"));
    //    List<String> a = Lists.newArrayList("1","2","3","3");
    //    a= a.stream().distinct().toList();
    //    System.out.println(JSON.toJSONString(a));
    Pair<String, Long> a = Pair.build("71570", 10000000L);
    System.out.println(JSON.toJSONString(a));
  }

  @Test
  public void testSwith() {
    String dbFieldType = "formula";
    switch (dbFieldType) {
      case "formula" -> {
        dbFieldType = "aaaa";
      }
    }
    System.out.println(dbFieldType);
  }

  @Test
  public void testMath() {
    Pattern scientificNotation = Pattern.compile("^[+-]?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$");
    if (scientificNotation.matcher("1.684944E12").matches()) {
      BigDecimal bd = new BigDecimal("1.684944E12");
      System.out.println(bd.longValue());
      //      return formatActionDate(new Date(bd.longValueExact()), timeZone);
    }
    BigDecimal bd2 = new BigDecimal("-1.45");
    //    bd2.compareTo(BigDecimal.valueOf())
    System.out.println(bd2.longValue());
    //    System.out.println(bd2.longValueExact());

  }

  @Test
  public void testDimSQLJoin() {
    List<String> fiscalYears = Lists.newArrayList("2");

    List<Integer> orderFiscalYears = fiscalYears.stream()
                                                .map(Integer::parseInt)
                                                .sorted(Comparator.naturalOrder())
                                                .toList();
    System.out.println(orderFiscalYears.get(0));
    System.out.println(orderFiscalYears.get(orderFiscalYears.size() - 1));
  }

  @Test
  public void testProvider() {
    //    TopologyProvider topologyProvider = new TopologyProvider();
    //   String sql= topologyProvider.updateWithVersion(10,new TopologyTableDO());
    //    System.out.println(JSON.toJSONString("aaa#bb$cc".split("[#\\$]")));
    String a = "aaaa,bbbbb:cccccc,dddddd#1,eeeeeee$2";
    Set<String> fields = Splitter.on(",")
                                 .splitToList(a)
                                 .stream()
                                 .filter(path -> !Objects.equals("-1", path))
                                 .flatMap(dim -> {
                                   if (dim.contains(":")) {
                                     return Stream.of(dim.split(":"));
                                   }
                                   return Stream.of(dim.split("[#\\$]")[0]);
                                 })
                                 .collect(Collectors.toSet());
    System.out.println(JSON.toJSONString(fields));
  }

  @Test
  public void testJSON() {
    String json = "[{\"dbFieldApiName\":\"data_auth_code\",\"fieldId\":\"data_auth_code\"," +
      "\"dstColumnName\":\"data_auth_code\"},{\"dbFieldApiName\":\"owner\",\"fieldId\":\"owner\"," +
      "\"dstColumnName\":\"owner\"},{\"dbFieldApiName\":\"out_tenant_id\",\"fieldId\":\"out_tenant_id\"}," +
      "{\"dbFieldApiName\":\"out_owner\",\"fieldId\":\"out_owner\"},{\"dbFieldApiName\":\"data_own_department\"," +
      "\"fieldId\":\"data_own_department\"},{\"dbFieldApiName\":\"out_data_auth_code\"," +
      "\"fieldId\":\"out_data_auth_code\"},{\"dbFieldApiName\":\"out_data_auth_id\",\"fieldId\":\"out_data_auth_id\"}]";
    List<DisplayField> displayFields = JSON.parseObject(json, new TypeReference<>() {
    });
    displayFields.forEach(df -> {
      System.out.println(JSON.toJSONString(df));
    });
  }

  @Test
  public void testRegex() {
    //    Assert.assertTrue(Constants.slotRegex.matcher("value1").matches());
    //    Assert.assertTrue(Constants.slotRegex.matcher("value0").matches());
    //    Assert.assertTrue(Constants.slotRegex.matcher("value100").matches());
    //    Assert.assertTrue(Constants.slotRegex.matcher("value555").matches());
    //    Map<String,String> a = JSON.parseObject("{\"a\":\"1\"}", new TypeReference<>() {
    //    });
    //    System.out.println(a.get("a"));
    //    String cids="783874083,3018988550";
    //    List<String> b = Splitter.on(CharMatcher.anyOf(";,")).trimResults().omitEmptyStrings().splitToList(cids);
    System.out.println(ZoneId.of("Africa/Algiers"));
    System.out.println(DateTimeUtil.covertYYYYMMDD2Timestamp("20230928", 0L, ZoneId.of("America/New_York")));
    System.out.println(DateTimeUtil.covertYYYYMMDD2Timestamp("20230928", 0L, ZoneId.of("Asia/Tokyo")));
  }

  @Test
  public void testDateTime() {
    long actionDate = -2209017600000L;

    Pattern pattern = Pattern.compile("^[-]?[0-9]+$");
    String time = "2209017600000";
    System.out.println(pattern.matcher(time).matches());
    time = "-2209017600000";
    System.out.println(pattern.matcher(time).matches());
    time = "-0";
    System.out.println(pattern.matcher(time).matches());
    time = "0";
    System.out.println(pattern.matcher(time).matches());
    //    String timeZone= ZoneId.systemDefault().getId();
    //    SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMdd");
    //    if(StringUtils.isNotBlank(timeZone)){
    //      simpleDateFormat.setTimeZone(TimeZone.getTimeZone(ZoneId.of(timeZone)));
    //    }
    //    Date date=new Date(actionDate);
    //    System.out.println(simpleDateFormat.format(date));
  }

  @Test
  public void testFormat() {
    //    String sqlTemplate=" has(splitByChar(',',replaceRegexpAll({0}.value13, '\\\\[|\\\\]|\\\\{|\\\\}|\\\\\"',
    //    '')),{1}.user_id)";
    //    System.out.println(MessageFormat.format(sqlTemplate,"biz_account_1","org_employee_user_1"));
    //    String sqlTemplate1=" has(splitByChar(',',replaceRegexpAll({0}.value13, '\\\\[|\\\\]|\\\\{|\\\\}|\\\\\"',
    //    '')),user_id)";
    //    System.out.println(MessageFormat.format(sqlTemplate1,"biz_account_1","org_employee_user_1"));

    //    System.out.println(MessageFormat.format("{1}.is_deleted=''0''", "a", "b"));
    ViewChangeMessage viewChangeMessage = new ViewChangeMessage();
    viewChangeMessage.setTenantId("71570");
    viewChangeMessage.setSourceType(0);
    viewChangeMessage.setSourceId("BI_6466eca700584b0001260b2c");
    System.out.println(JSON.toJSONString(viewChangeMessage));
  }

  @Test
  public void testStringFormat() {
    //    Number a =NumberUtils.createNumber("1.6185024E12");
    //    System.out.println(NumberUtils.isNumber("1.6185024E12"));
    //    BigDecimal()
    //    long a = Long.parseLong("1.6185024E12");
    //    System.out.println(a.longValue());
    //    BIMtTopologyDesDO statViewFieldDO = new BIMtTopologyDesDO();
    //    statViewFieldDO.setDeleted(false);
    //    System.out.println(JSON.toJSONString(statViewFieldDO));
    //    String whereCondition="AND (((biz_sales_order_1.life_status IS NULL OR biz_sales_order_1.life_status NOT IN
    //    ('ineffective','under_review','invalid'))) ) ";
    //    String b=String.format("a %s",whereCondition);
    //    System.out.println(b);
    String a = "{\"a\":126653771,\"b\":1}";
    JSONObject jsonObject = JSON.parseObject(a);

    int b = jsonObject.getInnerMap()
                      .values()
                      .stream()
                      .map(v -> Integer.parseInt(v.toString()))
                      .mapToInt(Integer::intValue)
                      .sum();
    System.out.println(b);
  }

  @Test
  public void testLookup() {
    BITopology biTopology = new BITopology();
    List<Edge> allEdges = Lists.newArrayList();
    allEdges.add(Edge.builder().from("1").to("2").build());
    allEdges.add(Edge.builder().from("2").to("3").build());
    allEdges.add(Edge.builder().from("3").to("4").build());
    allEdges.add(Edge.builder().from("4").to("5").build());
    allEdges.add(Edge.builder().from("5").to("6").build());
    allEdges.add(Edge.builder().from("6").to("7").build());
    allEdges.add(Edge.builder().from("7").to("8").build());
    allEdges.add(Edge.builder().from("8").to("9").build());
    //  allEdges.add(Edge.builder().from("10").to("12").build());
    //  allEdges.add(Edge.builder().from("6").to("8").build());
    //  allEdges.add(Edge.builder().from("6").to("9").build());
    biTopology.setEdges(allEdges);
    List<Edge> subEdges = Lists.newArrayList();
    BiMtRule biMtRule = BiMtRule.builder().biTopology(biTopology).build();
    //  biTopology.findLookupList(new AtomicInteger(0), subEdges, "1","9");
    //  for(int i=subEdges.size()-1;i>=0;i--){
    //    System.out.println(subEdges.get(i).getFrom()+"---->"+subEdges.get(i).getTo());
    //  }
    List<Edge> l = biMtRule.findLookupList("2", "2");
    l.forEach(e -> {
      System.out.println(e.getFrom() + "--->" + e.getTo());
    });
  }

  @Test
  public void testJson() {
    String json = "{\"nodes\":[{\"describeApiName\":\"SalesOrderObj\",\"id\":\"be4a0b7ed64f\"}," +
      "{\"describeApiName\":\"SalesOrderProductObj\",\"id\":\"ce4a0b7ed64f\"},{\"describeApiName\":\"AccountObj\"," +
      "\"id\":\"ae4a0b7ed64f\"}],\"edges\":[{\"from\":\"be4a0b7ed64f\",\"joinType\":\"LEFT_JOIN\"," +
      "\"onFields\":[{\"fromField\":\"account_id\",\"toField\":\"id\"}],\"to\":\"ae4a0b7ed64f\"}," +
      "{\"from\":\"ce4a0b7ed64f\",\"joinType\":\"LEFT_JOIN\",\"onFields\":[{\"fromField\":\"order_id\"," +
      "\"toField\":\"id\"}],\"to\":\"be4a0b7ed64f\"}]}";
    BITopology biTopology = JSON.parseObject(json, BITopology.class);
    List<Edge> subEdges = Lists.newArrayList();
    biTopology.findSubEdgesFromId(new AtomicInteger(0), subEdges, "ce4a0b7ed64f");
    System.out.println(JSON.toJSONString(subEdges));
  }

  @Test
  public void testRecursion() {
    List<Edge> edges = Lists.newArrayList();
    for (int i = 0; i <= 10; i++) {
      Edge edge1 = new Edge();
      edge1.setFrom("" + i);
      edge1.setTo("" + (i + 1));
      edges.add(edge1);
    }
    System.out.println(JSON.toJSONString(edges));
    BITopology biTopology = new BITopology();
    biTopology.setEdges(edges);
    List<Edge> subEdges = Lists.newArrayList();
    biTopology.findSubEdgesFromId(new AtomicInteger(0), subEdges, "5");
    System.out.println(JSON.toJSONString(subEdges));
  }

  @Test
  public void testPair() {
    List<Pair<String, String>> a = Lists.newArrayList();
    a.add(Pair.build("123455", "dim_string_1"));
    a.add(Pair.build("10987654", "dim_string_2"));
    System.out.println(JSON.toJSONString(a));
  }

  @Test
  public void testEnum() {
    String a = "";
    String b = "113";
    switch (b) {
      case "1124" -> {
        a = "112";
      }
      case "113" -> {
        a = "123";
      }
      default -> {
        a = "default";
      }
    }
    System.out.println(a);

    System.out.println(SyncStatusEnum.createFromStatus(3)==SyncStatusEnum.AGG_ED);
  }

  @Test
  public void testLongValue() {
    String a = "1.68526542E+12";
    Pattern pattern = Pattern.compile("^[+-]?\\d+(\\.\\d+)?([eE][+-]?\\d+)?$");
    System.out.println(pattern.matcher(a).matches());
    BigDecimal bd = new BigDecimal(a);
    long result = bd.longValueExact();
    System.out.println("Result: " + result);
  }

  @Test
  public void testFunction() {
    String text = "SUM($base_order_amount$)";
    String functionName = "SUM";
    String regex = functionName + "\\(([^)]+)\\)";
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(text);
    if (matcher.find()) {
      String parameter = matcher.group(1);
      String parameterName = parameter.replaceAll("\\$", "");
      System.out.println("Parameter name: " + parameterName);
    }
  }

  @Test
  public void testDimConfig() {
    //    for(int i=0;i<=500;i++){
    //      System.out.println(String.format("value%d=EXCLUDED.value%d,",i,i));
    //    }
    String a = "0.90元";
    Pattern pattern = Pattern.compile("(\\d+(?:\\.\\d+)?)\\s*([\\u4e00-\\u9fa5]*)");
    Matcher matcher = pattern.matcher(a);
    if (matcher.matches()) {
      System.out.println("金额" + matcher.group(1));
      System.out.println("单位：" + matcher.group(2));
    }
    //    System.out.println(PGColumnType.Int8.createDimDistType("date"));
    //    String config="approval_task_1:dealed_persons:dim_array_string_1:_ARRAY:employee";
    //    System.out.println(JSON.toJSONString(DimConfig.parse(config,"Asia/Shanghai")));
  }

  @Test
  public void testHashCode() {
    NodeColumn nodeColumn = new NodeColumn();
    nodeColumn.setName("employees");
    nodeColumn.setType(ColumnType._ARRAY);
    nodeColumn.setItemType(ColumnType._int);
    //    System.out.println(getNName(NodeColumn::getName));
    //    System.out.println(getNName(nodeColumn::getName));
    exeResult(NodeColumn::getName);
  }

  public String getNName(INodeFactory nodeFactory) {
    NodeColumn nodeColumn = new NodeColumn();
    nodeColumn.setName("employees");
    return nodeFactory.getN(nodeColumn);
  }

  public void exeResult(IResultFactory resultFactory) {
    NodeColumn nodeColumn = new NodeColumn();
    nodeColumn.setName("employees");
    resultFactory.getB(nodeColumn);
  }

  @FunctionalInterface
  public interface INodeFactory {
    public String getN(NodeColumn nodeColumn);
  }


  @FunctionalInterface
  public interface IResultFactory {
    public void getB(NodeColumn nodeColumn);
  }

  @Test
  public void testAggDataPartition() {
    String historyPart = "CREATE TABLE %s_history PARTITION OF %s FOR VALUES FROM ('00000000') TO ('%s');";
    String otherPart = "CREATE TABLE %s_other PARTITION OF %s default;";
    String addPartition = "CREATE TABLE %s PARTITION OF %s FOR VALUES FROM ('%s') TO ('%s');";
    String midPartition = "CREATE TABLE %s_mid PARTITION OF %s FOR VALUES FROM ('%s') TO ('%s');";
    String latePartition = "CREATE TABLE %s_late PARTITION OF %s FOR VALUES FROM ('%s') TO ('%s');";
    String grayTableName = "sch_735454.agg_data_biz_sales_order_product_gray";
    String partitionTablePre = "sch_735454.agg_data_biz_sales_order_product";
    int beginYear = 2023;
    int beginMonth = 3;
    String early = "_early";
    String mid = "_mid";
    String late = "_late";
    int month = 15;
    LocalDateTime beginActionDate = LocalDateTime.of(beginYear, beginMonth, 1, 0, 0);
    int index = 0;
    LocalDateTime historyDateTime = beginActionDate.plus(-1, ChronoUnit.DAYS);
    String historyPartition = String.format(historyPart, partitionTablePre, grayTableName, String.format("%d%02d%02d"
      , historyDateTime.getYear(), historyDateTime.getMonthValue(), historyDateTime.getDayOfMonth()));
    System.out.println(historyPartition);
    do {
      String _earlyTable = String.format("%s_%d%02d_early", partitionTablePre, beginActionDate.getYear(),
        beginActionDate.getMonthValue());
      String _earlyFrom = String.format("%d%02d%s", beginActionDate.getYear(), beginActionDate.getMonthValue(), "01");
      String _earlyTo = String.format("%d%02d%s", beginActionDate.getYear(), beginActionDate.getMonthValue(), "10");
      String _earlyPartition = String.format(addPartition, _earlyTable, grayTableName, _earlyFrom, _earlyTo);
      System.out.println(_earlyPartition);
      String _midTable = String.format("%s_%d%02d_mid", partitionTablePre, beginActionDate.getYear(),
        beginActionDate.getMonthValue());
      String _midFrom = String.format("%d%02d%s", beginActionDate.getYear(), beginActionDate.getMonthValue(), "11");
      String _midTo = String.format("%d%02d%s", beginActionDate.getYear(), beginActionDate.getMonthValue(), "20");
      String _midPartition = String.format(addPartition, _midTable, grayTableName, _midFrom, _midTo);
      System.out.println(_midPartition);
      LocalDateTime layeLocalDateTime = beginActionDate.plus(1, ChronoUnit.MONTHS).plus(-1, ChronoUnit.DAYS);
      String _lateTable = String.format("%s_%d%02d_late", partitionTablePre, layeLocalDateTime.getYear(), layeLocalDateTime.getMonthValue());
      String _lateFrom = String.format("%d%02d%s", layeLocalDateTime.getYear(), layeLocalDateTime.getMonthValue(), "21");
      String _lateTo = String.format("%d%02d%02d", layeLocalDateTime.getYear(), layeLocalDateTime.getMonthValue(), layeLocalDateTime.getDayOfMonth());
      String _latePartition = String.format(addPartition, _lateTable, grayTableName, _lateFrom, _lateTo);
      System.out.println(_latePartition);
      beginActionDate = beginActionDate.plus(1, ChronoUnit.MONTHS);
      index++;
    } while (index <= month);
    String otherPartition = String.format(otherPart, partitionTablePre, grayTableName);
    System.out.println(otherPartition);
  }

  @Test
  public void testTreeSet(){
    String pb="741832|797119|779718|740369|797556|743759|737593|737406|771468|798020|737754|759376|741016|772047|785781|797337|797781|742475|784099|742316|740370|798016|797552|738076|797103|797336|775214|742155|752941|752736|797777|761285|758744|737407|774818|754753|797123|771466|740532|741995|740692|797339|737756|741347|741018|741187|742477|742317|774439|785594|749127|797786|738077|797561|737408|797255|797043|757897|770304|753946|797918|774799|738078|774997|775196|797683|743943|742639|797466|740693|760650|740853|758742|741019|742157|741996|740694|761260|742158|780917|797794|797352|797567|798039|760853|742479|797135|737410|777933|741997|758741|740373|797053|748938|780493|797778|740695|761701|797263|798017|778145|797553|798002|797104|740696|741511|741674|781683|776114|738791|742160|797764|742481|797325|797539|776792|741352|737921|797945|741675|740212|797275|771839|737761|738447|797065|740537|796417|797702|795066|741839|797485|760210|782335|737762|797954|797698|740213|797482|740698|797271|797062|737601|781121|777951|776978|751240|754358|740214|742484|781258|797277|740539|742002|797946|737763|741194|797068|750653|780429|741354|797703|758736|778274|786117|797486|783367|771877|742116|738037|798236|777206|784128|741307|741467|742437|780326|750129|797783|768734|740653|797343|770563|741631|798030|741794|752781|773866|741308|738038|774463|742438|797125|740332|742117|762521|744295|777395|797558|778175|741148|781357|738039|741632|786360|737554|741957|742118|742439|780323|797346|742599|740333|737718|739648|798031|759427|757565|768508|741469|757174|784979|742440|762396|761805|741150|787315|737555|740815|741470|741310|795822|772354|742600|797564|740335|769848|781845|742120|742441|780206|737720|740171|775125|741634|750123|741151|794562|741959|741311|741797|737880|753579|798073|740817|738042|760690|753785|786165|797348|742121|740336|757170|740172|769637|741798|781844|739651|741635|797128|741472|783027|780205|770231|741152|780832|742603|740984|761594|747069|741153|783432|774918|783816|741961|772940|741313|786426|737722|770230|742283|760975|742443|757744|779807|737723|737883|740174|741154|741314|741474|741637|742444|746357|747618|761592|778424|797200|798094|753381|778237|740175|742605|779417|779024|759845|741801|776704|741315|737724|758157|798071|742445|741155|762190|737884|772742|738045|770228|777849|740500|740340|737725|756774|761930|797416|774461|740821|740987|749545|762320|741156|741316|741476|742446|740662|737726|752583|742126|760479|741317|796782|759416|741477|746496|787580|740341|753178|777391|797648|781771|775632|738406|741318|737887|742448|740342|795445|776816|740663|738217|752769|756395|787201|752399|741641|758358|769120|758993|747613|798123|797424|787541|762585|754192|773740|747611|777848|779414|742449|741967|784202|785226|741806|741479|794777|740990|740504|740179|755545|797448|742450|741968|737729|780828|756966|738938|742610|785079|768804|740180|751281|747421|771742|742290|741160|782834|760047|741320|741644|777655|769840|741481|761797|737890|770225|776701|797660|783223|740506|772932|742451|773327|740666|740992|785888|741161|776878|742612|781424|742131|741970|784884|777845|744110|767806|766935|737731|746686|741162|740667|752972|740346|738052|775234|742453|780318|741810|741163|737732|781562|780521|770740|742613|740347|753371|798397|741323|741971|778955|797922|750891|740829|785729|741811|740509|740348|782555|762515|742614|742133|737893|776199|756958|779345|778954|750889|771866|775430|740185|746346|747604|737385|738596|755754|741325|737734|740510|741812|740830|787445|741973|758143|740349|742455|737643|741395|753477|776335|781470|754098|742044|743626|772198|742204|739029|737804|783270|742525|740740|738125|741720|786363";
    String sch="750076|775276|734678|734675|683661|590172|753923|782557|715669|707281|1|448555|652848|247390|732375|692099|548489|399260|154113|729957|734249|713549|718299|718741|721104|719424|148999|605599|672397|630074|500529|358226|704115|721787|736684|703638|322246|472252|689436|730173|702081|687779|650993|457482|704833|773625|760132|683639|770410|720375|747119|679449|754125|785893|40163027|40163028|40163461|772026|770352|787529|797684|590270|754565|794884|753054|716476|739651|782892|747926|781620|777933|757088|781121|781684|790735|793455|771058|755470|744964|767157|782892|756682|756309|757097|744379|767156|768595|767151|757088|780439|769897|771797|784217|744958|775710|777907|767148|784053|754483|744198|758482|774532|741056|796234|760388|769469|756409|743063|746614|779067|781276|775533|797947|773992|750023|771991|756321|749824|743993|772948|758691|779463|779472|760170|742196|737799|783077|741550|753888|795575|739017|737459|770275|774776|740250|742041|742201|766633|742522|737952|741875|742200|738828|742192|737796|760592|741386|774964|738035|797836|737953|740732|740572|740412|741879|769857|737795|742518|774570|755894|744378|776717|761231|737798|758687|740576|782851|778291|741382|740568|741871|740569|742521|740737|741392|741393|741377|742355|740251|737274|757079|740577|746973|753442|778599|780493|768062|781121|747125|776114|758744|737126|771466|744805|747213|750366|762159|758736|777933|760210|748653|737406|737754|743759|742069|771839|760650|742550|740605|737593|780499|777951|784099|753946|772518|742390|740369|758329|761285|784387|757032|785781|737989|737756|774311|742475|772047|797068|797161|761129|738791|750653|781258|741100|766912|761701|738447|774443|758741|748743|741347|743847|742157|797683|737407|775214|752736|760751|780429|797352|797275|740931|737994|752466|737830|797486|783101|797255|797336|775482|754358|740766|786943|758427|737408|778274|780281|742229|742391|773302|742551|741421|737829|740529|770304|739774|747879|779720|754753|797271|741184|779783|794123|742392|798017|736732|741997|747975|768546|774799|744040|741354|781620|772026|738156|740532|756844|742477|779304|742231|778145|750176|785592|740214|740116|767010|774439|796417|737752|797575|741194|742002|785594|749224|782413|746561|741908|797148|740689|785213|786117|741748|738872|750174|741675|741839|741996|747878|740692|749127|750175|784175|742484|774818|751240|742317|742235|741832|751160|741666|779718|740212|754653|741352|752941|740370|797335|782335|775196|797361|774997|798047|797277|797801|797118|798029|741587|797945|797384|797698|797794|796996|741747|798046|797645|797360|777959|780917|752652|797140|797918|797123|776978|798039|797777|797778|796953|797555|797018|741183|797877|797567|779329|741187|797135|797104|742481|797449|741995|797574|797561|797166|797780|740698|797124|797805|797043|737921|797946|797053|797814|797553|782994|797367|748938|741586|797582|797588|797703|740368|797539|737763|749610|797556|797799|738078|797557|741830|797119|756456|737410|796942|797341|742072|797598|797337|740770|740367|797823|797362|778804|797702|797065|740853|798045|797787|740213|742639|740112|797781|751890|797103|741426|797482|741991|797263|741909|781683|741019|742232|737761|740695|742073|797764|738077|737601|740537|738151|741103|760853|768460|751159|771653|743943|742394|797466|768083|797325|776792|797576|740771|737762|797890|757897|741746|741259|797485|771468|740934|797654|742552|758736|760121|742160|740694|740203|759376|739062|740767|768160|797800|741018|761260|738076|742389|798019|782406|741419|797786|758742|741014|798016|741674|797142|740693|795066|798002|797552|797062|757223|797213|741262|742158|740696|797141|797233|761564|742479|741422|740539|742155|798020|797374|742316|797954|744240|741016|796960|797339|740373|783401|741511|723797|754122|726368|769602|781542|726694|724968|743621|700854|475601|680306|589227|746745|711466|753215|757485|598075|749545|741476|740821|742446|797416|740340|774461|741316|761930|737725|762320|741156|756774|740987|750891|741811|737893|756958|740829|778954|740348|776199|779345|742614|782555|797922|785729|740509|762515|742133|730173|798397|741810|780521|775234|742453|741323|778955|780318|737732|740347|742613|781562|770740|741971|753371|741163|738052|746686|737731|767806|740346|777845|776878|781424|742612|766935|740667|744110|741970|752972|784884|742131|741162|783223|785888|737890|770225|777655|742451|741481|797660|741644|772932|740666|773327|776701|761797|769840|740506|740992|741161|785079|755545|751281|742450|756966|768804|782834|737729|738938|747421|741320|771742|760047|742610|797448|742290|741968|780828|740180|741160|741806|773740|784202|742449|741479|754192|777848|785226|762585|787541|747611|741967|794777|779414|740179|740504|740990|738406|752399|795445|769120|758358|742448|797648|756395|737887|740342|797424|781771|741318|747613|775632|758993|740663|741641|787201|752769|776816|738217|798123|796782|746496|741477|752583|759416|737726|740341|741317|777391|753178|740662|760479|787580|742126|741801|762190|740175|770228|742445|779024|738045|772742|798071|737884|741315|777849|737724|778237|759845|742605|779417|776704|741155|758157|753381|740500|740174|742444|741474|746357|779807|737883|798094|797200|741314|737723|741637|747618|761592|741154|778424|753477|776335|742204|772198|743626|754098|739029|737643|783270|742525|738125|737804|742044|741720|786363|781470|741395|740740|770230|742443|783816|774918|786426|760975|741313|772940|737722|742283|747069|742603|761594|741961|780832|741153|757744|783432|740984|786165|760690|740172|770231|740817|781844|741472|738042|783027|740336|753785|741798|780205|769637|797348|741635|797128|739651|757170|741152|742121|740171|775125|781845|742441|753579|798073|737880|740335|741311|741797|780206|737720|750123|741634|769848|794562|742120|741151|741959|737555|740815|742440|741470|762396|741310|797564|795822|761805|787315|742600|772354|784979|757174|741150|737554|798031|742439|741469|759427|740333|780323|738039|737718|757565|797346|741632|786360|768508|742118|742599|741957|739648|768734|798030|773866|742438|778175|770563|797783|744295|741308|741794|774463|797558|738038|752781|777395|797343|740332|740653|797125|741631|781357|762521|742117|741148|742437|741467|783367|741307|771877|784128|780326|738037|750129|798236|742116|777206|740830|741812|750889|755754|742455|741325|746346|737734|775430|740349|771866|747604|738596|787445|737385|740510|741973|758143|740185|785823|706169|751061";
    Set<String> pbSets= Sets.newHashSet(Splitter.on("|").splitToList(pb));

    List<String> schList=Splitter.on("|").splitToList(sch);
    List<String> result= Lists.newArrayList();
    schList.forEach(ei->{
      if(!pbSets.contains(ei)){
        result.add(ei);
      }
    });
    System.out.println(JoinHelper.joinSkipNullOrBlank("|",result));
  }
  @Test
  public void testFilter(){
    String value1="[null]";
    List<String> options = Lists.newArrayList();
    JSONArray objects = JSON.parseArray(value1);
    for (Object object : objects) {
      if (object == null) {
        System.out.println("object is null");
      }
    }
  }

  @Test
  public void testExtObj() {
    String bizSalesOrderProduct = ObjectConfigManager.getExtendObjName("biz_sales_order_product");
    System.out.println(bizSalesOrderProduct);
  }
  
  @Test
  public void testFilterType(){
    String value1="123;456;789";
    String b= "(" + Arrays.stream(value1.split(";"))
    .filter(StringUtils::isNotBlank)
    .map(it -> "'" + it + "'")
    .collect(Collectors.joining(",")) + ")";
    System.out.println(b);
  }
}
