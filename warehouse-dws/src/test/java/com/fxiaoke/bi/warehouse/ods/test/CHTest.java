package com.fxiaoke.bi.warehouse.ods.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.component.MybatisBITenantPolicy;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.ods.bean.BatchUpdateDbTableSyncInfo;
import com.fxiaoke.bi.warehouse.ods.bean.DBColumnType;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.bean.PGSchema;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.dao.mapper.CommonMapper;
import com.fxiaoke.bi.warehouse.ods.entity.*;
import com.fxiaoke.bi.warehouse.ods.integrate.service.CHDataToCHService;
import com.fxiaoke.bi.warehouse.ods.mq.RouterChangeConsumer;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.fxiaoke.bi.warehouse.ods.service.*;
import com.fxiaoke.bi.warehouse.ods.utils.InitSQL;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.component.ClickHouseUtilService;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.PreparedStatementExecutor;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import com.google.common.util.concurrent.Uninterruptibles;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class CHTest {

  @Resource
  private CHDBService chdbService;
  @Autowired
  private CommonMapper commonMapper;
  @Resource
  private PGMetadataService pgMetadataService;
  @Resource
  private CHMetadataService chMetadataService;
  @Resource
  private CHDataSource chDataSource;
  @Resource
  private DBTransferService dbTransferService;
  @Resource
  private PgDataSource pgDataSource;
  @Resource
  CHClientService chClientService;
  @Resource
  private CHNodeService chNodeService;
  @Resource
  private MybatisBITenantPolicy mybatisBITenantPolicy;
  @Resource
  private PgCommonDao pgCommonDao;

  @Resource
  private CHRouterPolicy chRouterPolicy;

  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;

  @Resource
  private RouterChangeConsumer routerChangeConsumer;

  @Resource
  private DbRouterClient dbRouterClient;

  @Resource
  private ClickHouseUtilService clickHouseUtilService;
  @Resource
  private DbTableSyncInfoService dbTableSyncInfoService;



  @Resource
  private CHDataToCHService chDataToCHService;

  @Test
  public void testClickHouseUtil() {
    Map<Integer, String> eiToEaMap = clickHouseUtilService.getEiToEaMap(Lists.newArrayList("-1"));
    clickHouseUtilService.createChRoute("-1", "***********************************************", false, eiToEaMap);
  }

  @Test
  public void testReadChRoute(){
    String chRoute = chRouterPolicy.getCHJdbcURL("84883");
    System.out.println(chRoute);
  }
  
  @Test
  public void testRedis() {
    jedisCmd.del("**************************************************");
  }

  @Test
  public void testSaveDBSyncInfo() {
    routerChangeConsumer.saveDBSyncInfo("82958");
  }

  @Test
  public void testGrayInfo(){
//    #跳过同步的租户
//      use_ch_black=black:-1000
//#跳过计算的租户
//      use_ch_agg_black=black:-1000
    System.out.println(GrayManager.isAllowByRule("use_ch_black", "-1000"));
    System.out.println(GrayManager.isAllowByRule("use_ch_agg_black", "-1000"));
  }

  //按照库同步部分表到ch
  @Test
  public void testCHTransfer() {
    jedisCmd.del("sch_bi_112^sch_82958");
    TransferEvent transferEvent = new TransferEvent();
    transferEvent.setSchema("sch_82958");
    transferEvent.setDbURL("***********************************************");
    transferEvent.setChDbURL("***************************************************");
    dbTransferService.doTransfer2CH(transferEvent);
  }

  //测试CH数据同步到CH
  @Test
  public void testCHTransferToCH() {
    System.out.println(chDataToCHService.transferTenantLoginData("78060"));
    System.out.println(chDataToCHService.transferTenantApiNameData("78060"));


  }

  @Test
  public void testQueryDBSyncInfo() {
    //    List<DBSyncInfo>  dbSyncInfos= pgCommonDao.queryDBSyncInfos("*******************************************",
    //    "public");
    //    System.out.println(dbSyncInfos !=null);
    //    List<DbTableSyncInfo> dbTableSyncInfos = pgCommonDao.queryDbTableSyncInfos("11111111", null);
    //    System.out.println(dbTableSyncInfos != null);
    System.out.println(chRouterPolicy.getCHJdbcURL("82958"));
    //    System.out.println(chRouterPolicy.getCHJdbcURL("78060"));
    //    System.out.println(chRouterPolicy.getCHJdbcURL("85145"));
  }

  @Test
  public void testDB() throws Exception {
    //    chdbService.getSimpleName();
    //
    System.out.println(PasswordUtil.encode("IrHOgJLM7Yi8DO24"));
    //    System.out.println(com.facishare.paas.pod.util.PasswordUtil.encode("IrHOgJLM7Yi8DO24","fs-bi-clickhouse"));
    //    System.out.println(com.facishare.paas.pod.util.PasswordUtil.decode
    //    ("9910D0B072FCE82C255EE0A2824DC320E9F7CABA6272561CA2A1533AD00B7959","fs-bi-clickhouse"));
    //    System.out.println(chdbService.getSimpleName());

    //    Uninterruptibles.sleepUninterruptibly(10, TimeUnit.SECONDS);
  }

  @Test
  public void testCustomTables() {
    String dbURL = "***********************************************";
    String schema = "sch_82958";
    Set<String> customTables = pgMetadataService.findAllCustomTablesBySchema(dbURL, schema);
    System.out.println(JSON.toJSONString(customTables));
  }

  @Test
  public void testRouter() {
    RouterInfo routerInfo = mybatisBITenantPolicy.getRouterInfo("82313");
    System.out.println(routerInfo.getJdbcUrl());
  }

  @Test
  public void testCommonSQL() {
    String table = "new_opportunity";
    String schema = "public";
    List<DBColumnType> pgTypes = commonMapper.setTenantId("71570").findAllPgTypeByTable(table, schema);
    if (CollectionUtils.isNotEmpty(pgTypes)) {
      pgTypes.forEach(pgType -> {
        System.out.println(JSON.toJSONString(pgType));
      });
    }
  }

  @Test
  public void testDataType() {
    //    chdbService.getSimpleName();
    DBColumnType dbColumnType = new DBColumnType();
    dbColumnType.setUdtName("bpchar");
    dbColumnType.setColumnName("id");
    dbColumnType.setIsNullable("TRUE");
    dbColumnType.trans2CHType(ZoneId.systemDefault(), null);
    System.out.println(JSON.toJSONString(dbColumnType));
  }

  @Test
  public void testZone() {
    System.out.println(ZoneId.systemDefault().getId());
  }

  @Test
  public void testField() {
    String table = "biz_sales_order_product";
    String schema = "public";
    List<DBColumnType> pgTypes = commonMapper.setTenantId("71570").findAllPgTypeByTable(table, schema);
    if (CollectionUtils.isNotEmpty(pgTypes)) {
      for (DBColumnType pgType : pgTypes) {
        pgType.trans2CHType(ZoneId.systemDefault(), null);
        System.out.println(pgType.getColumnName() + "------>" + pgType.getUdtName());
      }
    }
  }

  @Test
  public void testSchema() throws Exception {
    String table = "biz_account_addr";
    PGSchema schema = pgMetadataService.loadSchema("***********************************************", "sch_82958." + table);
    System.out.println(schema.findTableName());
    System.out.println(JSON.toJSONString(schema.getPointColumns()));
//    System.out.println(JSON.toJSONString(schema));
    //    String sql = schema.createQuerySQL(Lists.newArrayList("71570", "74745"), "ASC", Pair.build(0L, 10L), 500);
    //    String sql=schema.createSQLByEiAndModifiedTime("71570",0L);
    //    System.out.println(sql);
    //    StopWatch stopWatch=StopWatch.createUnStarted("create sql");
    //    List<Pair<String, Long>>  results= metadataService.findMaxSysModifiedTime(schema,"71570",0L,stopWatch);
    //    System.out.println(JSON.toJSONString(results));
  }

  @Test
  public void testTimeZone() throws Exception {
    //   System.out.println(chMetadataService.getSysTimeZone());

    ZoneId zoneId = ZoneId.of("UTC");
    System.out.println(zoneId.getId());
  }

  //创建ch表
  @Test
  public void testMetadataService() throws Exception {
    List<String> tables = Lists.newArrayList("biz_sales_order_product");
    List<String> errorTables = Lists.newArrayList();
    String tenantId = "82958";
    String tableEngin = CHContext.REPLICATED_REPLACING_MERGE_TREE;
    String chUrl = chRouterPolicy.getCHJdbcURL(tenantId);
    String db = CHContext.getDBName(chUrl);
    String cluster = "{cluster}";
    for (String table : tables) {
      String tableName = table.toLowerCase();
      String sql1 = "drop table if exists " + db + "." + tableName;
      if (StringUtils.isNotBlank(cluster)) {
        sql1 = "drop table if exists " + db + "." + tableName + " on cluster '" + cluster + "'";
      }
//      chdbService.executeUpsertOnCH(chUrl, new String[] {sql1});
      String sql = pgMetadataService.createCHTableFromPg(tenantId, tableName, db, cluster, tableEngin);
      System.out.println("sql:" + sql);
      if (StringUtils.isNotBlank(sql)) {
//        chdbService.executeUpsertOnCH(chUrl, new String[] {sql});
      } else {
        errorTables.add(table);
      }
    }
    if (CollectionUtils.isNotEmpty(errorTables)) {
      System.out.println("create table error:" + JSON.toJSONString(errorTables));
    }
  }

  @Test
  public void testCreateChTableSQL() throws Exception {
    TransferEvent transferEvent = new TransferEvent("", "*****************************************", "public");
    List<String> tableList = Lists.newArrayList(pgMetadataService.findNeededToSyncTables(transferEvent));
    FileWriter writer = new FileWriter("D:\\clickhouse_workspace\\public_table_on_ch_20231108.sql", StandardCharsets.UTF_8);
    List<String> errorTables = Lists.newArrayList();
    String tenantId = "85145";
    int tables = 0;
    String cluster = "{cluster}";
    for (String table : tableList) {
      String tableName = table.toLowerCase();
      String sql = pgMetadataService.createCHTableFromPg(tenantId, tableName, null, cluster, CHContext.REPLICATED_REPLACING_MERGE_TREE);
      if (StringUtils.isNotBlank(sql)) {
        writer.write(sql + ";");
        writer.write("\n\n");
      } else {
        errorTables.add(table);
      }
      System.out.println(String.format("create sql ok table:%s,total:%d ", tableName, ++tables));
    }
    writer.flush();
    writer.close();
    if (CollectionUtils.isNotEmpty(errorTables)) {
      System.out.println("create table error:" + JSON.toJSONString(errorTables));
    }
  }

  /**
   * 全量建表
   *
   * @throws Exception
   */
  @Test
  public void testCreateChTableByDB() throws Exception {
    String chDbUrl = "***************************************************";
    String pgDbUrl = "*****************************************";
    String pgDbUrlSch = "***********************************************";
    String tenantId = "82958";
    String schemaName = "sch_82958";
    String dbName = "fsbidb044003002";
    String cluster = "{cluster}";
    TransferEvent transferEvent = new TransferEvent(chDbUrl, pgDbUrlSch, schemaName);
    String chUrl = transferEvent.getChDbURL();
    List<String> allPgTableExcludeStatTable = pgMetadataService.findAllPgTableExcludeStatTable(transferEvent);
    List<String> tableList = new ArrayList<>(allPgTableExcludeStatTable);
    List<String> errorTables = Lists.newArrayList();
    String tableEngine = CHContext.REPLICATED_REPLACING_MERGE_TREE;
//    tableList.clear();
//    tableList.add("ew_bi_ens_used_info_daily");
//    tableList.add("en_drr_daily");
    int tables = 0;
    for (String table : tableList) {
      String tableName = table.toLowerCase();
//      String sql1 = "drop table if exists " + dbName + "." + tableName;
//      if (StringUtils.isNotBlank(cluster)) {
//        sql1 = "drop table if exists " + dbName + "." + tableName + " on cluster '" + cluster + "'";
//      }
//      chdbService.executeUpsertOnCH(chUrl, new String[] {sql1});
        try{
          String sql = pgMetadataService.createCHTableFromPg(tenantId, tableName, dbName, cluster, tableEngine);
          if (StringUtils.isNotBlank(sql)) {
            chdbService.executeUpsertOnCH(chUrl, new String[] {sql});
          } else {
            errorTables.add(table);
          }
        }catch (Exception e){
          errorTables.add(table);
        }
      System.out.printf("create sql ok table:%s,total:%d %n", tableName, ++tables);
    }
    if (CollectionUtils.isNotEmpty(errorTables)) {
      System.out.println("create table error:" + JSON.toJSONString(errorTables));
    }
  }

  //按照表同步数据
  @Test
  public void testSyncByTable() throws Exception {
    String tableName = "biz_account_addr";
    String tenantId = "82958";
    JdbcConnection jdbcConnection = pgDataSource.getJdbcConnection(tenantId);
    String chDb="***************************************************";
    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo(chDb,
      pgDataSource.getRouterInfo("82958").getJdbcUrl(),
      mybatisBITenantPolicy.isStandalone(tenantId) ?
        "sch_" + tenantId :
        "public");
    dbSyncInfo.setBatchNum(39755L);
    long timeTag = System.currentTimeMillis();
    DBSyncInfoBO dbSyncInfoBO = DBSyncInfoBO.createInstanceOf(dbSyncInfo);
    String partitionName= WarehouseConfig.STOCK_PARTITION_NAME;
    BatchUpdateDbTableSyncInfo batchUpdateDbTableSyncInfo = BatchUpdateDbTableSyncInfo.createInstance( dbSyncInfoBO, dbTableSyncInfoService,30);
    dbTransferService.doTransferByTable(jdbcConnection, dbSyncInfoBO, tableName, Lists.newArrayList(tenantId),Maps.newHashMap(),partitionName,batchUpdateDbTableSyncInfo,System.currentTimeMillis());
    batchUpdateDbTableSyncInfo.flush();
    //    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo("**************************************************",
    //    "***********************************************", "sch_82958");
    //    pgCommonDao.sendCalculateEvent(dbSyncInfo);
  }

  /**
   * 全量同步租户数据
   * @throws Exception
   */
  @Test
  public void testSyncDataOffline() throws Exception {
    String tableName = "price_book_product";
    String routTenantId = "85145";
    String syncTenantId="85145";
    JdbcConnection jdbcConnection = pgDataSource.getJdbcConnection(routTenantId);
    String chJdbcURL = chRouterPolicy.getCHJdbcURL(routTenantId);
    String pgJbdcURL = pgDataSource.getRouterInfo(routTenantId).getJdbcUrl();
    String route = mybatisBITenantPolicy.isStandalone(routTenantId) ? "sch_" + routTenantId : "public";
    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo(chJdbcURL, pgJbdcURL, route);
    PGSchema pgSchema = pgMetadataService.loadSchema(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema() + "." + tableName);
    Optional<ClickhouseTable> clickhouseTable = chMetadataService.loadTable(route,dbSyncInfo.getChDb(), tableName);
//    ClickhouseTable clickhouseTable = chMetadataService.loadTable(route,dbSyncInfo.getChDb(), "biz_account");
    Biz2CHConsumer biz2CHConsumer = Biz2CHConsumer.getInstance(chClientService, clickhouseTable.get(), 10, 1L,
      chNodeService, chdbService, pgSchema.createOrderByList(), 500);
    Pair<Long, Long> sysTimeRange= Pair.build(0L,1713888000000000L);
    String partitionName="";
    BITransferPageTag biTransferPageTag = dbTransferService.transfer2CHByTenantId(jdbcConnection, pgSchema, Lists.newArrayList(syncTenantId), biz2CHConsumer, sysTimeRange, false,partitionName,null, dbSyncInfo.getPgDb());
    biz2CHConsumer.save();
    System.out.println("result="+JSON.toJSONString(biTransferPageTag));
  }

  @Test
  public void testLoadColumns() throws Exception {
    List<ClickhouseColumn> clickhouseColumns = null;//chMetadataService.loadColumns(
//      "***************************************************", "fsbidb044003001", "biz_account");
    clickhouseColumns=chMetadataService.loadColumnsPlus("***************************************************",
      "fsbidb044003001", "biz_account");
    if (CollectionUtils.isNotEmpty(clickhouseColumns)) {
      System.out.println(JSON.toJSONString(clickhouseColumns));
//      clickhouseColumns.forEach(cc -> {
//        System.out.println(JSON.toJSONString(cc));
//      });
    }
  }

  @Test
  public void testOpenMerge() throws Exception {
    Set<String> set = Sets.newConcurrentHashSet();
    set.add("*");
    System.out.println(CHContext.openMerge(set, "***************************************************"));
    System.out.println(CHContext.openMerge(set, "***************************************************"));
    System.out.println(CHContext.openMerge(set, "***************************************************"));
  }

  @Test
  public void testBatchInsert() {
    String dbURL = "************************************************************************************";
    String sql =
      "insert into bi_112.test_type2 (id,test_int,test_long,array_str,array_str_lc,array_int,test_bool,test_num," +
        "test_date,test_date_time,test_date_time_z) " + "values(?,?,?,?,?,?,?,?,?,?,?)";

    //    values('3','3','5900',['ccc'],['eee'],[],'{"api_name":"biz_account"}','false','12.4','2023-03-01');
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("api_name", "biz_account");
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(dbURL)) {
      jdbcConnection.setFetchSize(10000).prepareUpdateBatch(sql, statement -> {
        for (int i = 30000; i < 50000; i++) {
          statement.setObject(1, "" + i);
          statement.setObject(2, 4);
          statement.setObject(3, 500L);
          statement.setObject(4, new String[] {"dddd"});
          statement.setObject(5, new String[] {"eeee"});
          statement.setObject(6, null);
          statement.setObject(7, "false");
          statement.setObject(8, "500.12");
          statement.setObject(9, "2023-01-01");
          statement.setObject(10, "2023-01-01 08:08:00");
          statement.setObject(11, "2023-01-01 08:08:00");
          statement.addBatch();
        }
      });
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }


  @Test
  public void testLTree() throws Exception {
    String sql = "select id,tenant_id,account_path from biz_account where tenant_id='71570' and " +
      "id='5f3cce80d208dc000102a179'";
    JdbcConnection jdbcConnection = pgDataSource.getConnectionByPgbouncerURL(
      "***************************" + ".251:5432/bi_112");
    jdbcConnection.query(sql, resultSet -> {
      while (resultSet.next()) {
        Object accountPath = resultSet.getObject("account_path");
        System.out.println(accountPath.getClass().getSimpleName());
        System.out.println(accountPath.getClass().getName());
        System.out.println(accountPath.getClass());
        System.out.println(String.valueOf(accountPath));
      }
    });
    jdbcConnection.close();
  }

  /**
   * 按照企业和表名同步数据
   */
  @Test
  public void testTransfer() throws Exception {
    String tenantId = "85145";
    List<String> tableNames = Lists.newArrayList("checkins_data");
    List<DBSyncInfo> dbSyncInfos=pgCommonDao.queryDBSyncInfoById(Lists.newArrayList("6412b13f5cd44942982c91b7"));
    DBSyncInfo dbSyncInfo = dbSyncInfos.get(0);
    for (String tableName : tableNames) {
      JdbcConnection jdbcConnection = pgDataSource.getJdbcConnection(tenantId);
      PGSchema schema = pgMetadataService.loadSchema(dbSyncInfo.getPgDb(), dbSyncInfo.getPgSchema()+"."+tableName);
      Pair<Long, Long> sysTimeRange = null;//Pair.build(1658825032136398L,1677842029755297L);
      String chDbUrl = dbSyncInfo.getChDb();
      int savePointThreshold = 5;
      long batchNum = 1;
      Optional<ClickhouseTable> clickhouseTable = chMetadataService.loadTable(dbSyncInfo.getPgSchema(),chDbUrl, tableName);
      Biz2CHConsumer biz2CHConsumer = Biz2CHConsumer.getInstance(chClientService, clickhouseTable.get(), savePointThreshold, batchNum, chNodeService, chdbService, schema.createOrderByList(), 10);
      String partitionName="";
      dbTransferService.transfer2CHByTenantId(jdbcConnection, schema, Lists.newArrayList(tenantId), biz2CHConsumer, sysTimeRange, false,partitionName,null,
        dbSyncInfo.getPgDb());
      biz2CHConsumer.save();
    }
  }

  @Test
  public void testBizConsumer() throws Exception {
    String chDbUrl = "***************************************************";
    String db = "bi_112";
    String table = "batch";
    int savePointThreshold = 5000;
    long batchNum = 1;
    PGSchema schema = pgMetadataService.loadSchema("*****************************************", "public." + table);
    Optional<ClickhouseTable> clickhouseTable = chMetadataService.loadTable("public",chDbUrl, table);
    Biz2CHConsumer biz2CHConsumer = Biz2CHConsumer.getInstance(chClientService, clickhouseTable.get(), savePointThreshold,
      batchNum, chNodeService, chdbService, schema.createOrderByList(), 2);
    for(int i=0;i<20;i++){
      Map<String, Object> recordMap = Maps.newHashMap();
      recordMap.put("tenant_id", "71570");
      recordMap.put("id", "2222222222222"+i);
      recordMap.put("name", "该函数的作用是将给定的ByteBuffer对象b添加到AdaptiveQueue队列中，如果在指定的超时时间t内无法添加成功，则抛出IOException异常。函数首先获取当前的AdaptiveQueue队列和超时时间t，然后记录当前时间作为开始时间（如果t小于1则开始时间为0），之后通过循环尝试将b添加到队列中，如果添加成功则直接返回。如果在超时时间内无法添加成功，则抛出异常，异常信息中包含超时时间t的值。");
      recordMap.put("bi_sys_flag", "1");
      recordMap.put("bi_sys_batch_id", String.valueOf(batchNum));
      recordMap.put("bi_sys_is_deleted","0");
      biz2CHConsumer.queue(new BizLog(recordMap), true, false);
//      System.out.println("queue success+++++++++++++++++++++++++++++++++++");
      Uninterruptibles.sleepUninterruptibly(100, TimeUnit.MICROSECONDS);
    }
    biz2CHConsumer.save();
    System.out.println("save success+++++++++++++++++++++++++++++++++++");

  }

  @Test
  public void testDBExists() {
    String chDbUrl = "**************************************************";
    String db = "bi_116";
    //    System.out.println(chMetadataService.checkCHDBExists(chDbUrl,db));
    chMetadataService.createDatabase(chDbUrl, null, null);
  }

  //删除ch数据
  @Test
  public void testDeleteCHData() throws Exception {
    String tableName = "biz_account_addr";
//    PGSchema schema = pgMetadataService.loadSchema("***********************************************",
//      "public." + tableName);
    String chDbUrl = "***************************************************";
    long batchNum = 4L;
    String schemaName ="sch_82958";
    Optional<ClickhouseTable> clickhouseTable = chMetadataService.loadTable(schemaName,chDbUrl, tableName);
    clickhouseTable.ifPresent(table -> System.out.println(JSON.toJSONString(table.getPointColumns())));
//    StopWatch stopWatch = StopWatch.createStarted(tableName);
    //    chdbService.insertBeforeData(schema.getEiName(), schema.findPrimaryKeyId(), clickhouseTable, batchNum,
    //    stopWatch) ;
//    chdbService.deleteBeforeData(schema.getEiName(), schema.findPrimaryKeyId(), clickhouseTable.get(), batchNum, stopWatch);
  }


  @Test
  public void testCHStreamWrite() {
    String line =
      "1000,71570,研发部门,1001,,1,,,*************,-10000,*************,0,1000,6038d63f3fc10f0001133f76,CRM,," +
        "DepartmentObj,32,default__c,,,\"['999999','1001','1000']\",,e1903bf62d99a5ad,,,c866a9b83734885b,,5916122," +
        "9157," + ",\"['999999','1001']\",,normal,,0,,,-10000,研发部门,,,,,,1658398330656221,,,,,,,,1,1";
    String tenantId = "71570";
    Pair<Long, Long> sysTimeRange = null;//Pair.build(1658825032136398L,1677842029755297L);
    String chDbUrl = "***************************************************";
    String db = "bi_112";
    String table = "org_dept";
    int savePointThreshold = 2;
    long batchNum = 1;
    Optional<ClickhouseTable> clickhouseTable = chMetadataService.loadTable("public",chDbUrl, table);
    System.out.println(JSON.toJSONString(clickhouseTable.get().getColumnList()));
    System.out.println(JSON.toJSONString(clickhouseTable.get().getColumnMap()));
    //    Biz2CHConsumer biz2CHConsumer=new Biz2CHConsumer(chClientService,clickhouseTable,savePointThreshold,
    //    batchNum,chNodeService);

  }

  @Test
  public void testTableExists() {
    String chDbUrl = "***************************************************";
    boolean a = chMetadataService.checkCHTableExists(chDbUrl, "org_employee_user_downstream");
    //    Assert.assertTrue(a);
    System.out.println(a);
  }

  @Test
  public void testModifiedTimeSQL() throws Exception {
    String tableName = "checkins_data";
    JdbcConnection jdbcConnection = pgDataSource.getConnectionByPgbouncerURL(
      "***************************" + ".251:5432/bi_112");
    PGSchema schema = pgMetadataService.loadSchema("*******************************************",
      "public." + tableName);
//    StopWatch stopWatch = StopWatch.createStarted(tableName);
//    PgSysModifiedTimeInfo pgSysModifiedTimeInfos = pgCommonDao.findMaxSysModifiedTimeByTable(jdbcConnection, schema,
//      1683717779172118l, stopWatch);
//    System.out.println(JSON.toJSONString(pgSysModifiedTimeInfos));
//    System.out.println(stopWatch.prettyPrint());
    String sql=schema.createQuerySQL(Lists.newArrayList("85145"),"ASC",Pair.build(1L,2L),100);
    System.out.println(sql);
  }

  @Test
  public void testTimezone() throws Exception {
    System.out.println(chMetadataService.getSysTimeZone("85145"));
  }

  @Test
  public void testChConnection() throws Exception {
    RouterInfo routerInfo = chRouterPolicy.getRouterInfo("90052");
    System.out.println(JSON.toJSONString(routerInfo));
    System.out.println(routerInfo.getMasterProxyUrl());
    String sql = "select count() from biz_account where tenant_id='82958'";

    //    String sql = "insert into  test_rm (key,ts,is_deleted) values('111',*************,0)";
    //    JdbcConnection jdbcConnection= chDataSource.getJdbcConnection(chRouterPolicy.getCHJdbcURL("78060"));
    //    JdbcConnection jdbcConnection = new JdbcConnection("*****************************************************",
    //      "fs_chdb_b_u_bi_aggcalc_fsbidb044003", "Vc4NoOd25iEUZendRP6");
    //    jdbcConnection.query(sql, resultSet -> {
    //      if (resultSet.next()) {
    //        System.out.println("============" + resultSet.getInt(1));
    //      }
    //    });
    //    jdbcConnection.executeUpdate(sql);
  }

  @Test
  public void testCHTableSize(){
    String chdb="***************************************************";
    List<String> tables=Lists.newArrayList("agg_data");
    Map<String, Long> tableSize=chMetadataService.queryTableSize(chdb,tables);
    System.out.println(JSON.toJSONString(tableSize));
  }

  @Test
  public void testAggFunction() {
    String dbURL = "***************************************************";
    String sql = "select hash_code FROM fsbidb044003001.agg_data WHERE tenant_id in('85145')" +
        " and view_id='BI_5dce2e8e988d3300014bc719' and view_version=3 limit 10";
    try (JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(dbURL)) {
      jdbcConnection.query(sql, resultSet -> {
        while (resultSet.next()) {
           BigDecimal b  =resultSet.getBigDecimal(1);
          System.out.println(b);
        }
      });
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Test
  public void testPreparedStatement()throws Exception{
    String dbURL = "***************************************************";
    JdbcConnection jdbcConnection = chDataSource.getJdbcConnection(dbURL);
    String sql="insert into  fsbidb044003001.agg_downstream_data(tenant_id,view_id,value_slot,view_version,ds_agg_uniq_1)"+
    "select ?,?,?,?,cast(unhex(?),' AggregateFunction(uniqExact, Nullable(String))')";
    Connection connection= jdbcConnection.connection();
    Object[] values=new Object[]{"85145","v2","ds_agg_uniq_1",1,"0101D28BC7A548FB38109D60ECBCC221AAB3"};
    Object[] values2=new Object[]{"85145","v3","ds_agg_uniq_1",1,"0101D28BC7A548FB38109D60ECBCC221AAB3"};
    PreparedStatementExecutor preparedStatementExecutor= new PreparedStatementExecutor("ch",connection,sql,10, RateLimiter.create(1.0d));
    preparedStatementExecutor.accept(values);
    preparedStatementExecutor.accept(values2);
    preparedStatementExecutor.flush();
    preparedStatementExecutor.close();
  }

  @Test
  public void testCreateChTable() {
    String chDbUrl = "***************************************************";
    String database = chDbUrl.substring(chDbUrl.lastIndexOf("/") + 1);
    chdbService.executeSQLWithJdbcUrl(chDbUrl, String.format(InitSQL.BI_ERP_DATA_SCREEN, database),30 * 60 * 1000L);
    chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA,30 * 60 * 1000L);
    chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V1,30 * 60 * 1000L);
    chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V2,30 * 60 * 1000L);
    chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V3,30 * 60 * 1000L);
    chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V4,30 * 60 * 1000L);
    chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V5,30 * 60 * 1000L);
    chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V6,30 * 60 * 1000L);
    chdbService.executeSQLWithJdbcUrl(chDbUrl,InitSQL.AGG_LOG_DATA_V7,30 * 60 * 1000L);
  }
}
