package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import junit.framework.TestCase;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/9/18
 */
public class FlushTool extends TestCase {

  @Test
  public void testRepairCrmRelation() throws Exception{
    File rmPgDB = ResourceUtils.getFile("classpath:stat/base_crm_feedrelation_bug.json");
    String pgs = Files.readString(rmPgDB.toPath());
    JSONArray jsonArray=JSON.parseArray(pgs);
    Map<String, Set<String>> tenantIdAndViewId= Maps.newHashMap();
    jsonArray.forEach(item->{
      JSONObject e = (JSONObject) item;
      tenantIdAndViewId.computeIfAbsent(e.getString("tenant_id"),key-> Sets.newHashSet()).add(e.getString("source_id"));
    });
    List<StatViewBatchArg> statViewBatchArgs = Lists.newArrayList();
    tenantIdAndViewId.forEach((ei,views)->{
      StatViewBatchArg  statViewBatchArg = new StatViewBatchArg();
      statViewBatchArg.setTenantId(ei);
      statViewBatchArg.setSourceType(0);
      List<StatViewPreArg> statViewPreArgList = Lists.newArrayList();
      views.forEach(sourceId->{
        StatViewPreArg statViewPreArg= StatViewPreArg.builder().sourceId(sourceId).sourceType(0).build();
        statViewPreArgList.add(statViewPreArg);
      });
      statViewBatchArg.setStatViewArgList(statViewPreArgList);
      statViewBatchArgs.add(statViewBatchArg);
    });
    String url="curl -X POST 'http://localhost/warehouse-dws/batchCreateAggTopology' -H 'Cache-Control: no-cache' -H 'Content-Type: application/json' -d '%s'";
    FileWriter writer = new FileWriter("D:\\常用脚本\\clickhouse帮助目录\\CH上线脚本\\base_crm_feed_relation2.sh", StandardCharsets.UTF_8);
    statViewBatchArgs.forEach(arg->{
      try{
        writer.write(String.format(url,JSON.toJSONString(arg)));
        writer.write("\n");
      }catch (Exception e){
        e.printStackTrace();
      }
    });
    writer.flush();
    writer.close();
  }
}
