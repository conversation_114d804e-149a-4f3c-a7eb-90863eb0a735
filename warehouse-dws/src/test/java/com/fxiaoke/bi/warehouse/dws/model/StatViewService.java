package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.db.dao.TopologyTableDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.StatViewDao;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2023/7/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class StatViewService {

  @Resource
  private TopologyTableDao topologyTableDao;
  @Resource
  private StatViewDao statViewDao;

  @Test
  public void testViewSQL() {
    String tenantId = "71570";
    String sourceId = "BI_646def82090c3300016e23b0";
    TopologyTable statView = topologyTableDao.findByTenantIdAndSourceId(tenantId, sourceId);
    Map<String, Map<String,String>> rightLargeTables = Maps.newHashMap();
    Map<String,String> tableSamples = Maps.newHashMap();
    tableSamples.put("object_data_1","1000");
    rightLargeTables.put("BI_5ee1a136ee6d0000015fcf73",tableSamples);
    Map<String, List<String>> tableKeys = Maps.newHashMap();
    tableKeys.put("object_data", Arrays.asList("tenant_id","object_describe_api_name","bi_sys_flag","_id"));
    tableKeys.put("biz_sales_order_product", Arrays.asList("tenant_id","bi_sys_flag","id"));
    tableKeys.put("biz_sales_order", Arrays.asList("tenant_id","bi_sys_flag","id"));
    tableKeys.put("org_employee_user", Arrays.asList("tenant_id","bi_sys_flag","id"));
    System.out.println(statView.toViewSQL(100*10000,2000, rightLargeTables, tableKeys));
  }

  @Test
  public void test2(){
    String tenantId="71570";
    String viewId="BI_64ccab80f74efd00015eb376";
    List<String> fields = Lists.newArrayList();
    statViewDao.transObjDetailStatViewDimFields(tenantId,viewId, fields);
    System.out.println(JSON.toJSONString(fields));
  }

  @Test
  public void testSpecFields(){
    List<String> statFields= Lists.newArrayList("BI_spc_cases_field_xcfwry__c_dim","BI_spc_device_field_DLcb2__c_dim","Bi_12345");
    statViewDao.replaceStatFieldId("71570",statFields);
    System.out.println(JSON.toJSONString(statFields));
  }
  @Test
  public void testProperty(){
    String tenantId="85145";
    String viewId="BI_6673d4a7dcc6600001879540";
    List<String> results = statViewDao.fillFieldIdsFromProperty(tenantId, viewId);
    System.out.println(JSON.toJSONString(results));
  }
}
