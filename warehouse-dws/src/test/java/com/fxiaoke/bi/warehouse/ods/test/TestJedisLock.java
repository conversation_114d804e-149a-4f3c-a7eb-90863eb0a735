package com.fxiaoke.bi.warehouse.ods.test;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.core.db.mapper.UdfObjMapper;
import com.fxiaoke.bi.warehouse.ods.service.PGMetadataService;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.JedisCmd;
import com.google.common.util.concurrent.Uninterruptibles;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * @Author:jief
 * @Date:2023/5/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class TestJedisLock {

  @Resource
  PGMetadataService pgMetadataService;
  @Resource
  PgCommonDao pgCommonDao;
  @Autowired
  UdfObjMapper udfObjMapper;

  @Resource(name = "jedisFactory")
  private JedisCmd jedisCmd;


  @Test
  public void testRedisLoc(){
    String dbUrl="bi_112";
    String schemaName="public";
    String transferKey = String.format("%s^%s", dbUrl, schemaName);
//    JedisLock jedisLock = new JedisLock(jedisCmd, transferKey, 1000 * 60 * 60 * 3);
//    jedisLock.tryLock();
    jedisCmd.del(transferKey);
//    System.out.println(jedisCmd.get(transferKey));
  }
  @Test
  public void testThreadLock() {
    DateTimeFormatter dateTimeFormatter= DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    String lock = "ceshi_ch_lock";
    for (int i = 0; i < 5; i++) {
      new Thread(() -> {
        while (true) {
          JedisLock jedisLock = new JedisLock(jedisCmd, lock, 1000 * 60);
          if (jedisLock.tryLock()) {
            System.out.println(Thread.currentThread().getName() + "获取到锁！开始执行任务！"+ dateTimeFormatter.format(LocalDateTime.now()));
            Uninterruptibles.sleepUninterruptibly(60, TimeUnit.SECONDS);
          } else {
            System.out.println(Thread.currentThread().getName() + "没有获取到锁！等待一会儿再尝试！"+ dateTimeFormatter.format(LocalDateTime.now()));
            Uninterruptibles.sleepUninterruptibly(30, TimeUnit.SECONDS);
          }
        }
      }).start();
      Uninterruptibles.sleepUninterruptibly(5, TimeUnit.SECONDS);
    }

    Uninterruptibles.sleepUninterruptibly(30, TimeUnit.HOURS);
  }
}
