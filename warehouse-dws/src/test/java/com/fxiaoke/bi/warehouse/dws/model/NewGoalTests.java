package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.fxiaoke.bi.warehouse.common.goal.BITopology;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.service.MappingService;
import com.fxiaoke.bi.warehouse.dws.service.StatTopologyService;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.GoalRuleDao;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.BIMtTopologyDesDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.entity.GoalRuleDO;
import com.fxiaoke.bi.warehouse.dws.transform.db.paas.mapper.GoalRuleDetailMapper;
import com.fxiaoke.bi.warehouse.dws.transform.impl.NewGoalTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.model.*;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author:jief
 * @Date:2023/5/22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class NewGoalTests {
  @Resource
  private GoalRuleDao goalRuleDao;
  @Resource
  private MappingService mappingService;
  @Resource
  private StatTopologyService statTopologyService;
  @Resource
  private NewGoalTopologyTransformer newGoalTopologyTransformer;
  @Autowired
  private GoalRuleDetailMapper goalRuleDetailMapper;

  @Resource(name = "orgAdapterApiEnterpriseConfigService")
  private EnterpriseConfigService enterpriseConfigService;

  @Test
  public void testTopology() {
    BIMtTopologyDesDO biMtTopologyDesDO = goalRuleDao.findBiMtTopologyDes("71570", "BI_6433e901376a0c0001bf627b", 0);
    System.out.println(JSON.toJSONString(biMtTopologyDesDO));
  }

  @Test
  public void testGoalRule(){
    GoalRuleDO goalRuleDO = goalRuleDao.findGoalRuleById("71570", "63578a750b835f0001f0857b");
    System.out.println("==========="+JSON.toJSONString(goalRuleDO));
    System.out.println("==========="+JSON.toJSONString(goalRuleDO.getCountFiscalYear()));
  }

  @Test
  public void testPaasFiscalConfig(){
    GetConfigDto.Argument argument = new GetConfigDto.Argument();
    argument.setKey("FISCAL_MONTH_SETTING");
    final Integer employeeId = -1000;
    argument.setEmployeeId(employeeId);
    argument.setCurrentEmployeeId(employeeId);
    argument.setEnterpriseId(Integer.valueOf("71570"));
    final GetConfigDto.Result config = this.enterpriseConfigService.getConfig(argument);
    System.out.println(JSON.toJSONString(config));
  }

  @Test
  public void testMtGoalRule() {
    String tenantId = "85529";
    String sourceId = "64d4d6775e40f50001c6efa3";
    BiMtRule BIMtRule = goalRuleDao.createBiMtRule(tenantId, sourceId, 1);
    System.out.println(JSON.toJSONString(BIMtRule.getBiTopology().getEdges()));
  }

  @Test
  public void testCreateTopologyTable(){
    String tenantId = "85145";
//    String sourceId = "65040b6fdfdedc000111cdf4";
//    String sourceId = "64f82817685f98000120d8a7";
//    String sourceId = "6577fe14be9f170001cb8474";
//    String sourceId = "66b03d0bfaf8ce000185cdbc";
    String sourceId = "67ab36be6f549900012df871";
    int source = 1;
    GoalRuleDO goalRuleDO = goalRuleDao.findGoalRuleById(tenantId, sourceId);
    Pair<List<String>, Map<String, String>> goalIdsAndDimMapper = newGoalTopologyTransformer.findGoalIdsAndDimMapper(
      goalRuleDO, tenantId, source, sourceId.contains("|") ? sourceId : "");
    for (String goalId : goalIdsAndDimMapper.first) {
      goalRuleDO.setId(goalId);
      TopologyTable topologyTable = newGoalTopologyTransformer.createTopologyTable(tenantId, goalRuleDO, source,
        goalIdsAndDimMapper.second);
      Map<String, List<String>> tableKeys = Maps.newHashMap();
      tableKeys.put("object_data", Arrays.asList("tenant_id","object_describe_api_name","bi_sys_flag","id"));
      Map<String, Set<String>> aggEffectApiNameMapper = topologyTable.findAggEffectApiNameMapper();
      System.out.println("aggEffectApiNameMapper:" + aggEffectApiNameMapper);
      System.out.println("goalRuleId::::::" + goalId + ": " +topologyTable.toViewSQL(-1,-1, null, tableKeys));
//      System.out.println(goalId + ": " +topologyTable.toDetailViewSql(false));
    }
  }

  @Test
  public void testInsertTopologyTable() throws Exception {
    String tenantId = "85529";
//    String sourceId = "65040b6fdfdedc000111cdf4";
//    String sourceId = "64f82817685f98000120d8a7";
//    String sourceId = "6577fe14be9f170001cb8474";
    String sourceId = "676bb94024409f00011ac690";
    int sourceType = 1;
    newGoalTopologyTransformer.doCreateGoalTopology(tenantId, sourceId, sourceType, TopologyTableStatus.Prepared.getValue());
  }

  @Test
  public void testInsertDetailSql() throws Exception {
    StatViewPreArg statViewPreArg = new StatViewPreArg();
    statViewPreArg.setTenantId("85529");
    statViewPreArg.setSourceId("65dc614e4e12590001f0580b");
    statViewPreArg.setSourceType(1);
    newGoalTopologyTransformer.createStatViewDetailSQL(statViewPreArg);
  }

  @Test
  public void testTopologyTableRule() {
    String tenantId = "85529";
    String sourceId = "650133bbc741340001ecba18";
    //缓存列类型
    Map<String, AtomicInteger> aggAliasMapper = Maps.newHashMap();
    Map<String, AtomicInteger> dimsMapper = Maps.newHashMap();
    Map<String, String/*dim字段别名*/> dimFieldAliasMapper = Maps.newHashMap();
    BiMtRule BIMtRule = goalRuleDao.createBiMtRule(tenantId, sourceId, 1);
    List<TopologyTableAggRule> topologyTableAggRule = goalRuleDao.parseFromMap(tenantId, BIMtRule, aggAliasMapper,
      dimsMapper, dimFieldAliasMapper, "0", null);
    System.out.println(JSON.toJSONString(topologyTableAggRule.get(0)));
  }


  @Test
  public void testTimeRule() {
    String tenantId = "85529";
    String sourceId = "64f1820d942a5200014d490b";
    //缓存列类型
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    BiMtRule biMtRule = goalRuleDao.createBiMtRule(tenantId, sourceId, 1);
    Map<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> mapper =
      biMtRule.createMeasureRuleMapper();
    for (Map.Entry<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> measureEntry :
      mapper.entrySet()) {
      TimeRule timeRule = goalRuleDao.buildTimeRule(tenantId, false, measureEntry.getKey(), biMtRule,
        cachedTableDefinitions);
      System.out.println(JSON.toJSONString(timeRule));
    }

  }
@Test
  public void testWhere(){
  String tenantId = "85529";
  String sourceId = "64f6a105453c440001ec8186";
  boolean standalone=false;
  //缓存列类型
  Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
  BiMtRule BIMtRule = goalRuleDao.createBiMtRule(tenantId, sourceId, 1);
  WhereRules whereRules=goalRuleDao.buildWheres( tenantId, standalone, BIMtRule, cachedTableDefinitions);
  System.out.println(JSON.toJSONString(whereRules));
  }

  @Test
  public void testValue() {
    String tenantId = "85529";
    String sourceId = "64f6a105453c440001ec8186";
    //缓存列类型
    Map<String, Map<String, ColumnDefinition>> cachedTableDefinitions = Maps.newHashMap();
    BiMtRule biMtRule = goalRuleDao.createBiMtRule(tenantId, sourceId, 1);
    Map<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> multiDimMeasureRuleMapper =
      biMtRule.createMeasureRuleMapper();
    for (Map.Entry<Pair<String/*nodeId*/, String/*actionDate*/>, List<BiMtMeasureRule>> measureEntry :
      multiDimMeasureRuleMapper.entrySet()) {
      measureEntry.getValue().forEach(multiDimMeasureRule -> {
        ValueRule valueRule = goalRuleDao.buildValueRule(tenantId, false, biMtRule, multiDimMeasureRule,
          cachedTableDefinitions);
        System.out.println("================="+JSON.toJSONString(valueRule));
      });
    }
  }

  @Test
  public void testMtTopology() {
    BIMtTopologyDesDO biMtTopologyDesDO = goalRuleDao.findBiMtTopologyDesById("85529", "64d4c81d5e40f50001c6e886");
    //    System.out.println(biMtTopologyDesDO.getTopologyModel());
    BITopology biTopology = BITopology.parseFromJson(biMtTopologyDesDO.getTopologyModel());

  }

  @Test
  public void testPaas2biApiName() {
    System.out.println(mappingService.biApiName("SPUObj"));
  }

  @Test
  public void testCreatePreViewSQL() throws IOException {
    StatViewPreArg statViewPreArg = new StatViewPreArg();
    statViewPreArg.setTenantId("85145");
    statViewPreArg.setSourceId("66d6ded80906310001f979b3");
    statViewPreArg.setSourceType(1);
    statViewPreArg.setTimeZone("Asia/Shanghai");
    StatViewPreSQL statViewPreSQL= statTopologyService.createStatViewPreSQL(statViewPreArg);
    System.out.println(statViewPreSQL.getPreViewSQL());
    System.out.println(statViewPreSQL.getMaxModifiedTime());
  }

  @Test
  public void testBatchRepairGoalRuleTopology(){
    newGoalTopologyTransformer.batchRepairGoalRuleTopology("", true);
  }

  @Test
  public void testCreateBatchPreViewSQL() throws IOException {
    File file = ResourceUtils.getFile("classpath:stat/StatView_5.json");
    String json = Files.readString(file.toPath());
    StatViewBatchArg statViewBatchArg = JSON.parseObject(json, StatViewBatchArg.class);
    StatViewBatchPreSQL batchPreViewSQL = statTopologyService.createBatchPreViewSQL(statViewBatchArg);
    batchPreViewSQL.getPreViewSQLList().forEach(statViewPreSQL -> {
      System.out.println("statViewPreSQL:" + statViewPreSQL.getPreViewSQL());
    });
  }



}
