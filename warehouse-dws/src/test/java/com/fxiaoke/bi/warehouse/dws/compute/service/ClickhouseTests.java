package com.fxiaoke.bi.warehouse.dws.compute.service;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.service.CHAggDataService;
import com.fxiaoke.bi.warehouse.dws.service.ClickHouseService;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Author:jief
 * @Date:2023/9/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class ClickhouseTests {
  @Resource
  private ClickHouseService clickHouseService;
  @Resource
  private CHAggDataService chAggDataService;

  @Test
  public void testSystemTable() {
    Map<String, Integer> result = clickHouseService.queryTableSize("85145", Lists.newArrayList("biz_account", "org_employee_user"));
    System.out.println(JSON.toJSONString(result));
  }

  @Test
  public void testDeleteAgg(){
    chAggDataService.batchDeleteAggData("85145", Lists.newArrayList("63872fee085feb0001075d2c","6645a283ed789b0001fcf3b5"));
  }

  @Autowired
  private ResourceLoader resourceLoader;

  @Test
  public void diff() {
    StringBuilder stringBuilder = new StringBuilder();
    try {
      org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:jsonTmp.json");
      try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
        String line;
        while ((line = reader.readLine()) != null) {
          stringBuilder.append(line);
        }
      }
    } catch (IOException e) {
      System.err.println("Error reading file from jar: " + e.getMessage());
    }
    String string = stringBuilder.toString();
    string = "683661,803216,781542,799035,769602,434439,776186,724968,743621,716315,687979,780043,598075,785823,774399,701884,674400,685571,748879,757485,646380,565710,706169,684738,531457,682722,726694,626246,640242,684811,726805,619318,589227,49148,700146,700854,712368,753215,746745,475601,650993,680306,625684,727748,719279,751061,792729,687779,612295,488989,627115,704833,702081,726368,730707,789630,457482,770352,689436,678424,322246";
    getStrings(string);
  }

  @Test
  public void testRemoveSplitChEi() {
    String chGray = "fsbidb007006|fsbidb006037|fsbidb007008|fsbidb007007|fsbidb054072006|fsbidb006030|fsbidb007002|fsbidb014002001|fsbidb006033|fsbidb007004|fsbidb007003|fsbidb006035|fsbidb006029|fsbidb054072011|fsbidb054072017|fsbidb054072019|fsbidb005050|fsbidb006022|fsbidb005048|fsbidb006016|fsbidb054071013|fsbidb006015|fsbidb006018|fsbidb054071015|fsbidb005049|fsbidb054071011|fsbidb054071016|fsbidb054071017|fsbidb054071019|fsbidb005040|fsbidb005044|fsbidb006012|fsbidb005043|fsbidb006011|fsbidb006014|fsbidb005045|fsbidb005036|fsbidb006007|fsbidb005038|fsbidb006009|fsbidb006008|fsbidb005034|fsbidb007042|fsbidb007044|fsbidb007043|fsbidb007046|fsbidb008016|fsbidb008014|fsbidb008008|fsbidb007039|fsbidb054071002|fsbidb054071003|fsbidb054071004|fsbidb054071009|fsbidb054071005|fsbidb054071006|fsbidb054071007|fsbidb054071008|fsbidb007030|fsbidb007033|fsbidb008004|fsbidb007034|fsbidb007037|fsbidb008003|fsbidb007029|fsbidb054070003|fsbidb054070002|fsbidb054070007|fsbidb054070008|fsbidb006050|fsbidb007020|fsbidb007023|fsbidb007025|fsbidb006049|fsbidb007018|fsbidb054070014|fsbidb054070013|fsbidb054070017|fsbidb054070016|fsbidb054070015|fsbidb006043|fsbidb007011|fsbidb006042|fsbidb006044|fsbidb010013|fsbidb009020|fsbidb009023|fsbidb009022|fsbidb009025|fsbidb010009|fsbidb010006|fsbidb009012|fsbidb009013|fsbidb009010|fsbidb008043|fsbidb009016|fsbidb009014|fsbidb009008|fsbidb054079019|fsbidb008030|fsbidb008033|fsbidb009002|fsbidb008034|fsbidb008031|fsbidb009006|fsbidb054079020|:fsbidb001005|fsbidb008022|fsbidb008020|fsbidb008021|fsbidb011024|fsbidb011023|fsbidb011022|fsbidb010050|fsbidb054079004|fsbidb054079003|fsbidb054079009|fsbidb054079008|fsbidb054079006|fsbidb011019|fsbidb011016|fsbidb010047|fsbidb011015|fsbidb011010|fsbidb011011|fsbidb010040|fsbidb054079010|fsbidb054078009|fsbidb054078008|fsbidb011007|fsbidb010039|fsbidb054078005|fsbidb054078004|fsbidb011008|fsbidb011005|fsbidb011001|fsbidb054078014|fsbidb009044|fsbidb009047|fsbidb010025|fsbidb009031|fsbidb009039|fsbidb009036|fsbidb001020|fsbidb054077017|fsbidb001029|fsbidb001025|fsbidb001023|fsbidb001022|fsbidb011050|fsbidb001019|fsbidb001018|fsbidb001017|fsbidb054078002|fsbidb001016|fsbidb001013|fsbidb001012|fsbidb001010|fsbidb011047|fsbidb001006|fsbidb054077001|fsbidb001004|fsbidb001003|fsbidb054077008|fsbidb054077005|fsbidb011039|fsbidb054077006|fsbidb054077004|fsbidb011035|fsbidb011031|fsbidb011034|fsbidb011033|fsbidb054076008|fsbidb002032|fsbidb054076006|fsbidb002030|fsbidb003004|fsbidb054076004|fsbidb003006|fsbidb003007|fsbidb003008|fsbidb003009|fsbidb002036|fsbidb002034|fsbidb002021|fsbidb054076016|fsbidb054076013|fsbidb002029|fsbidb002028|fsbidb002026|fsbidb002024|fsbidb054075017|fsbidb002010|fsbidb001042|fsbidb001041|fsbidb054075019|fsbidb054075014|fsbidb002019|fsbidb054075020|fsbidb002017|fsbidb001049|fsbidb001047|fsbidb002014|fsbidb002013|fsbidb001044|fsbidb001043|fsbidb001030|fsbidb002008|fsbidb001039|fsbidb002007|fsbidb054076001|fsbidb002004|fsbidb001036|fsbidb002003|fsbidb002002|fsbidb001033|fsbidb004013|fsbidb004014|fsbidb003047|fsbidb004015|fsbidb004016|fsbidb004017|fsbidb004019|fsbidb003041|fsbidb004010|fsbidb004011|fsbidb003043|fsbidb004012|fsbidb004002|fsbidb003034|fsbidb054075006|fsbidb004003|fsbidb004004|fsbidb004005|fsbidb003037|fsbidb004007|fsbidb003039|fsbidb054075001|fsbidb004008|fsbidb054075009|fsbidb054075011|fsbidb003030|fsbidb003031|fsbidb003024|fsbidb003027|fsbidb054074001|fsbidb002050|fsbidb054074003|fsbidb054074009|fsbidb003020|fsbidb003021|fsbidb003022|fsbidb054074016|fsbidb002043|fsbidb002041|fsbidb003014|fsbidb003016|fsbidb003017|fsbidb054074014|fsbidb054074013|fsbidb017002|fsbidb017005|fsbidb054074020|fsbidb002046|fsbidb003010|fsbidb002045|fsbidb003011|fsbidb002044|fsbidb054073017|fsbidb005027|fsbidb014082001|fsbidb054073013|fsbidb054073012|fsbidb054073019|fsbidb054073018|fsbidb054073020|fsbidb004046|fsbidb004048|fsbidb005019|fsbidb004041|fsbidb005010|fsbidb004043|fsbidb005013|fsbidb004044|fsbidb004045|fsbidb005004|fsbidb004037|fsbidb005005|fsbidb005008|fsbidb054072020|fsbidb004030|fsbidb004031|fsbidb004034|fsbidb054073004|fsbidb004025|fsbidb054073003|fsbidb054073006|fsbidb004026|fsbidb054073002|fsbidb054073008|fsbidb054073007|fsbidb054073009|fsbidb003050|fsbidb004020|fsbidb004022|fsbidb054076018|fsbidb054074005|fsbidb054078016|fsbidb054076019|fsbidb054079015|fsbidb054076005|fsbidb054075002|fsbidb004006|fsbidb054075005|fsbidb010022|fsbidb010027|fsbidb010035|fsbidb011045|fsbidb001011|fsbidb004009|fsbidb006032|fsbidb054070010|fsbidb054070018|fsbidb054072002|fsbidb054072014|fsbidb054072016|fsbidb054075008|fsbidb054076010|fsbidb054076012|fsbidb054076014|fsbidb054077011|fsbidb054077015|fsbidb054078013|fsbidb054078015|fsbidb054078020|fsbidb008029|fsbidb002035|fsbidb054072004|fsbidb054070009|fsbidb054078017|fsbidb054078003|fsbidb054077012|fsbidb054077019|fsbidb054078012|fsbidb054078010|fsbidb001031|fsbidb054073016|fsbidb005028|fsbidb001050|fsbidb054078011|fsbidb001046|fsbidb009042|fsbidb054072018|fsbidb054077003|fsbidb001005|fsbidb001008|fsbidb001015|fsbidb001021|fsbidb001026|fsbidb001040|fsbidb002027|fsbidb003045|fsbidb004039|fsbidb005032|fsbidb005037|fsbidb054070005|fsbidb054070011|fsbidb054072012|fsbidb054072013|fsbidb054073005|fsbidb054073011|fsbidb054074015|fsbidb054077010|fsbidb054078001|fsbidb054078019|fsbidb008006|fsbidb054076003|fsbidb054079007|fsbidb054076011|fsbidb054079013|fsbidb054078018|fsbidb054078006|fsbidb054074012|fsbidb054077014|fsbidb003028|fsbidb054074019|fsbidb054076017|fsbidb054079012|fsbidb054079018|fsbidb003025|fsbidb054074017|fsbidb009027|fsbidb003019|fsbidb054076002|fsbidb008039|fsbidb001032|fsbidb010010|fsbidb009024|fsbidb002025|fsbidb010033|fsbidb010020|fsbidb007045|fsbidb006045|fsbidb003029|fsbidb011029|fsbidb003002|fsbidb054074007|fsbidb004029|fsbidb009007|fsbidb007019|fsbidb004042|fsbidb010036|fsbidb006005|fsbidb006019|fsbidb011013|fsbidb008017|fsbidb005009|fsbidb005017|fsbidb004023|fsbidb002012|fsbidb011025|fsbidb005001|fsbidb054072007|fsbidb054072010|fsbidb010003|fsbidb010005|fsbidb010011|fsbidb010015|fsbidb010016|fsbidb010023|fsbidb010031|fsbidb010034|fsbidb010046|fsbidb011009|fsbidb011014|fsbidb011018|fsbidb011026|fsbidb011028|fsbidb011030|fsbidb011036|fsbidb011037|fsbidb011038|fsbidb011040|fsbidb011042|fsbidb011044|fsbidb001009|fsbidb001014|fsbidb001027|fsbidb001028|fsbidb001037|fsbidb001045|fsbidb001048|fsbidb002015|fsbidb002016|fsbidb002022|fsbidb002040|fsbidb002042|fsbidb002048|fsbidb003001|fsbidb003005|fsbidb003012|fsbidb003026|fsbidb003033|fsbidb003036|fsbidb003044|fsbidb003048|fsbidb004001|fsbidb004021|fsbidb004027|fsbidb004033|fsbidb004035|fsbidb004040|fsbidb004047|fsbidb004050|fsbidb005002|fsbidb005003|fsbidb005007|fsbidb005012|fsbidb005015|fsbidb005020|fsbidb005024|fsbidb005025|fsbidb005029|fsbidb005039|fsbidb005041|fsbidb005046|fsbidb005047|fsbidb006001|fsbidb006010|fsbidb006013|fsbidb006020|fsbidb006021|fsbidb006023|fsbidb006028|fsbidb006031|fsbidb006034|fsbidb006036|fsbidb006038|fsbidb054070004|fsbidb054072001|fsbidb054072009|fsbidb054073001|fsbidb054073010|fsbidb054074006|fsbidb054074018|fsbidb007001|fsbidb007005|fsbidb007010|fsbidb007017|fsbidb007021|fsbidb007031|fsbidb007038|fsbidb007050|fsbidb054075010|fsbidb054075013|fsbidb054076007|fsbidb054077007|fsbidb054077009|fsbidb054077013|fsbidb054078007|fsbidb054079001|fsbidb008001|fsbidb008010|fsbidb008028|fsbidb008038|fsbidb008045|fsbidb008047|fsbidb008048|fsbidb009004|fsbidb009011|fsbidb009019|fsbidb009026|fsbidb009032|fsbidb009034|fsbidb009035|fsbidb009040|fsbidb009043|fsbidb009038|fsbidb009030|fsbidb009018|fsbidb009045|fsbidb009046|fsbidb004032|fsbidb009001|fsbidb003018|fsbidb009048|fsbidb004038|fsbidb003013|fsbidb003035|fsbidb005016|fsbidb006039|fsbidb008007|fsbidb008023|fsbidb005026|fsbidb006024|fsbidb008013|fsbidb008009|fsbidb005011|fsbidb008037|fsbidb008005|fsbidb011046|fsbidb005035|fsbidb008027|fsbidb011027|fsbidb008019|fsbidb008044|fsbidb005006|fsbidb008050|fsbidb006006|fsbidb011006|fsbidb006002|fsbidb008040|fsbidb008025|fsbidb006040|fsbidb005022|fsbidb006025|fsbidb008018|fsbidb011017|fsbidb005018|fsbidb006027|fsbidb008011|fsbidb005014|fsbidb008035|fsbidb006041|fsbidb011002|fsbidb011020|fsbidb005023|fsbidb006047|fsbidb008012|fsbidb006003|fsbidb008046|fsbidb011021|fsbidb005030|fsbidb008049|fsbidb008032|fsbidb008036|fsbidb008026|fsbidb011012|fsbidb011003|fsbidb005021|fsbidb011041|fsbidb006026|fsbidb006046|fsbidb005031|fsbidb008002|fsbidb011043|fsbidb008041|fsbidb006048|fsbidb008015|fsbidb011032|fsbidb005033|fsbidb011048|fsbidb008024|fsbidb011049|fsbidb005042|fsbidb011004|fsbidb008042|fsbidb006004|fsbidb054070012|fsbidb054073014|fsbidb054076015|fsbidb010001|fsbidb010004|fsbidb010007|fsbidb010008|fsbidb010012|fsbidb010014|fsbidb010017|fsbidb010018|fsbidb010019|fsbidb010021|fsbidb010024|fsbidb010026|fsbidb010028|fsbidb010029|fsbidb010030|fsbidb010032|fsbidb010037|fsbidb010038|fsbidb010041|fsbidb010042|fsbidb010043|fsbidb010044|fsbidb010045|fsbidb010048|fsbidb010049|fsbidb001034|fsbidb002001|fsbidb002037|fsbidb002039|fsbidb003003|fsbidb003015|fsbidb003023|fsbidb003032|fsbidb003038|fsbidb003040|fsbidb003042|fsbidb003046|fsbidb003049|fsbidb004018|fsbidb004024|fsbidb054074011|fsbidb007014|fsbidb007016|fsbidb007047|fsbidb054075004|fsbidb054075012|fsbidb054075015|fsbidb054075016|fsbidb054075018|fsbidb054076009|fsbidb054076020|fsbidb054077002|fsbidb054077018|fsbidb054077020|fsbidb054079002|fsbidb054079011|fsbidb054079016|fsbidb054079017|fsbidb009003|fsbidb009005|fsbidb009009|fsbidb009015|fsbidb009017|fsbidb009021|fsbidb009028|fsbidb009029|fsbidb009033|fsbidb009037|fsbidb009041|fsbidb009049|fsbidb009050|fsbidb054072005|fsbidb054075003|fsbidb054075007|fsbidb054070001|fsbidb054070020|fsbidb054077016|fsbidb054079005|fsbidb054079014|fsbidb007036|fsbidb007048";
    String splitChEi = "fsbidb003002|fsbidb011029|fsbidb003029|fsbidb006045|fsbidb007045|fsbidb010020|fsbidb010033|fsbidb002025|fsbidb008006|fsbidb009024|fsbidb010010|fsbidb001032|fsbidb008039|fsbidb054076002|fsbidb003019|fsbidb004009|fsbidb009027|fsbidb054074017|fsbidb003025|fsbidb054079018|fsbidb054079012|fsbidb054078017|fsbidb054076017|fsbidb054072010|fsbidb054077012|fsbidb054078015|fsbidb054072007|fsbidb005001|fsbidb011025|fsbidb002012|fsbidb004023|fsbidb005017|fsbidb005009|fsbidb008017|fsbidb011013|fsbidb006019|fsbidb054070018|fsbidb006005|fsbidb010036|fsbidb004042|fsbidb054076003|fsbidb007019|fsbidb009007|fsbidb004029|fsbidb054076018|fsbidb054074007|fsbidb004006|fsbidb054070009|fsbidb009004|fsbidb003012|fsbidb004040|fsbidb011040|fsbidb054073010|fsbidb005002|fsbidb011036|fsbidb004001|fsbidb007001|fsbidb007050|fsbidb010023|fsbidb010046|fsbidb003005|fsbidb009040|fsbidb009034|fsbidb010016|fsbidb001037|fsbidb009019|fsbidb054078012|fsbidb007010|fsbidb005047|fsbidb009032|fsbidb054074018|fsbidb004035|fsbidb001009|fsbidb001021|fsbidb009026|fsbidb054079007|fsbidb007038|fsbidb002022|fsbidb011038|fsbidb010015|fsbidb007005|fsbidb011026|fsbidb002042|fsbidb004047|fsbidb005039|fsbidb006001|fsbidb008028|fsbidb054070004|fsbidb006036|fsbidb054077013|fsbidb006021|fsbidb011009|fsbidb005020|fsbidb001028|fsbidb009035|fsbidb010031|fsbidb005003|fsbidb054079015|fsbidb054077009|fsbidb005012|fsbidb054072009|fsbidb054075008|fsbidb054075013|fsbidb006010|fsbidb008001|fsbidb011030|fsbidb002048|fsbidb004050|fsbidb005041|fsbidb006020|fsbidb008029|fsbidb054070005|fsbidb007017|fsbidb001027|fsbidb006034|fsbidb054072012|fsbidb005028|fsbidb001045|fsbidb010005|fsbidb054072001|fsbidb005024|fsbidb002040|fsbidb054078011|fsbidb005015|fsbidb054072018|fsbidb054076012|fsbidb054074006|fsbidb054074005|fsbidb054075010|fsbidb054078007|fsbidb008045|fsbidb011037|fsbidb003026|fsbidb005007|fsbidb005046|fsbidb006031|fsbidb010003|fsbidb001014|fsbidb007021|fsbidb002016|fsbidb006038|fsbidb001048|fsbidb006028|fsbidb002035|fsbidb010034|fsbidb054078020|fsbidb005029|fsbidb008048|fsbidb003048|fsbidb008047|fsbidb054077007|fsbidb054079001|fsbidb054076019|fsbidb054076007|fsbidb002015|fsbidb054073001|fsbidb001024|fsbidb009029|fsbidb011042|fsbidb003032|fsbidb005016|fsbidb006023|fsbidb006039|fsbidb010008|fsbidb002033|fsbidb008007|fsbidb003046|fsbidb008023|fsbidb002011|fsbidb007026|fsbidb007009|fsbidb010035|fsbidb054079017|fsbidb007028|fsbidb009028|fsbidb004028|fsbidb009015|fsbidb003038|fsbidb054079002|fsbidb009037|fsbidb054078003|fsbidb054078006|fsbidb003023|fsbidb054074015|fsbidb010026|fsbidb011045|fsbidb003040|fsbidb005026|fsbidb006024|fsbidb008013|fsbidb010024|fsbidb002038|fsbidb008009|fsbidb005011|fsbidb008037|fsbidb003042|fsbidb010004|fsbidb009003|fsbidb010038|fsbidb001007|fsbidb008005|fsbidb054073005|fsbidb007032|fsbidb009017|fsbidb004018|fsbidb054078018|fsbidb054074012|fsbidb003003|fsbidb054078013|fsbidb001031|fsbidb010042|fsbidb011046|fsbidb003049|fsbidb005035|fsbidb006032|fsbidb008027|fsbidb011027|fsbidb002047|fsbidb008019|fsbidb005032|fsbidb008044|fsbidb005006|fsbidb010043|fsbidb054070010|fsbidb054072002|fsbidb001026|fsbidb010048|fsbidb054075012|fsbidb008050|fsbidb006006|fsbidb002027|fsbidb054076014|fsbidb007036|fsbidb003045|fsbidb054079011|fsbidb011006|fsbidb001005|fsbidb004039|fsbidb006002|fsbidb007013|fsbidb008040|fsbidb054070012|fsbidb003015|fsbidb008025|fsbidb006040|fsbidb009005|fsbidb005022|fsbidb010044|fsbidb054073014|fsbidb054072014|fsbidb002031|fsbidb010049|fsbidb054076015|fsbidb010018|fsbidb006025|fsbidb008018|fsbidb054074002|fsbidb009042|fsbidb054073016|fsbidb011017|fsbidb001035|fsbidb005018|fsbidb006027|fsbidb008011|fsbidb009050|fsbidb001015|fsbidb005014|fsbidb008035|fsbidb007022|fsbidb009009|fsbidb006041|fsbidb011002|fsbidb054077016|fsbidb054075015|fsbidb004036|fsbidb054070019|fsbidb054077002|fsbidb054076020|fsbidb054074011|fsbidb054072005|fsbidb010014|fsbidb054076010|fsbidb010027|fsbidb054075002|fsbidb004024|fsbidb011020|fsbidb002018|fsbidb005023|fsbidb006047|fsbidb008012|fsbidb010017|fsbidb001040|fsbidb006003|fsbidb008046|fsbidb007024|fsbidb054075003|fsbidb007041|fsbidb054072004|fsbidb054078001|fsbidb054078010|fsbidb007049|fsbidb054072003|fsbidb054077011|fsbidb054077020|fsbidb054077003|fsbidb054072015|fsbidb010022|fsbidb002005|fsbidb054072016|fsbidb005037|fsbidb054072013|fsbidb011021|fsbidb002049|fsbidb005030|fsbidb007015|fsbidb009041|fsbidb010021|fsbidb002009|fsbidb007040|fsbidb009021|fsbidb008049|fsbidb054077010|fsbidb008032|fsbidb054073015|fsbidb054078019|fsbidb054079005|fsbidb008036|fsbidb054074008|fsbidb054079014|fsbidb054078016|fsbidb054077018|fsbidb054077014|fsbidb054075018|fsbidb054076011|fsbidb004049|fsbidb003028|fsbidb003001|fsbidb011044|fsbidb004021|fsbidb007031|fsbidb008026|fsbidb011012|fsbidb011003|fsbidb005021|fsbidb009049|fsbidb010019|fsbidb011041|fsbidb002006|fsbidb009033|fsbidb001050|fsbidb007027|fsbidb006026|fsbidb009038|fsbidb054075007|fsbidb006046|fsbidb001038|fsbidb005031|fsbidb009030|fsbidb054079013|fsbidb009018|fsbidb010045|fsbidb054074004|fsbidb054075005|fsbidb008002|fsbidb003033|fsbidb004027|fsbidb008010|fsbidb009011|fsbidb011028|fsbidb011014|fsbidb005025|fsbidb010029|fsbidb010030|fsbidb011043|fsbidb002023|fsbidb009045|fsbidb002020|fsbidb008041|fsbidb007012|fsbidb009046|fsbidb054076009|fsbidb006048|fsbidb004032|fsbidb007048|fsbidb010037|fsbidb054079016|fsbidb008015|fsbidb054074010|fsbidb009001|fsbidb003036|fsbidb004033|fsbidb008038|fsbidb009043|fsbidb011032|fsbidb054070020|fsbidb005033|fsbidb054070001|fsbidb010032|fsbidb011048|fsbidb003018|fsbidb010007|fsbidb002037|fsbidb010041|fsbidb007047|fsbidb009048|fsbidb001011|fsbidb007016|fsbidb004038|fsbidb008024|fsbidb054075016|fsbidb001034|fsbidb003013|fsbidb054076005|fsbidb001008|fsbidb010001|fsbidb003044|fsbidb006013|fsbidb010011|fsbidb011018|fsbidb011049|fsbidb002001|fsbidb005042|fsbidb054070011|fsbidb011004|fsbidb054070006|fsbidb003035|fsbidb010028|fsbidb002039|fsbidb054073011|fsbidb008042|fsbidb054075004|fsbidb001046|fsbidb054072008|fsbidb007014|fsbidb010012|fsbidb054077015|fsbidb006004|fsbidb054077019|fsbidb054074019";
    String[] chGrayArr = chGray.split("\\|");
    String[] splitChEiArr = splitChEi.split("\\|");
    List<String> chGraySet = new ArrayList<>(List.of(chGrayArr));
    List<String> splitChEiSet = new ArrayList<>(List.of(splitChEiArr));
    splitChEiSet.removeAll(chGraySet);
    System.out.println("splitChEiSet = " + String.join("|", splitChEiSet));
  }

  private static void getStrings(String CH445EIS) {
    // 灰度多维度目标的企业
    String multiDimGoal = "781684|590225|790735|797684|795360|720614|793374|793663|790467|797205|789822|788341|798192|799499|795436|799300|800634|797838|795100|794579|770698|800831|800568|799214|800827|785027|782096|778251|769577|800161|774942|761231|769577|778251|782096|796440|796440|791317|801408|759469|703504|579905|768484|802214|780493|802287|796407|802760|769612|533382|802905|798102|802798|796473|800345|800847|776337|776770|69582|723797|739651|795366|736684|701877|804389|804257|801632|804405|754125|753054|779466|787007|761797|749018|747119|728437|787529|716476|800056|767166|590270|796874|767322|798238|753923|673284|780161|753803|715669|779735|757088|800613|794153|762417|722523|726820|792246|800663|742992|793455|735987|734733|801471|721345|781263|797089|702767";
    String chGray = "778656|626246|700146|727748|751061|803707|358226|781715|803952|1|795436|684811|800568|770352|712368|472252|627115|792729|692099|799499|774399|434439|651585|756283|803126|687979|682722|789456|802393|736684|680306|756974|750076|795100|40163461|685571|803136|612295|753215|803497|322246|756269|702081|796440|767835|798192|756277|757485|640242|756276|756274|704833|756271|769577|757917|684738|475601|779366|687779|790598|793986|779237|713549|625684|590172|748879|743621|801793|531457|650993|781542|789822|778251|619318|780579|40163028|40163027|797205|726805|756338|776186|154113|49148|646380|701884|726368|719279|801496|678424|782096|802100|781684|800927|457482|786109|448555|40200077|683661|803895|803410|548489|734675|734678|726694|674400|785027|729607|785823|746745|706169|789630|788782|700854|730707|760132|803228|488989|721787|730173|720375|769602|724968|598075|689436|782581|716315|565710|589227|775276|780043|69582|723797|739651|795366|785292|775723|804139|707988|774570|747119|787529|741377|769785|796874|737798|737796|771058|737795|772026|737799|737953|672397|748175|740569|701885|704115|742355|740572|718741|741386|715474|785001|802348|742196|794626|708845|548405|782892|753054|788757|706026|783868|742201|741871|716476|742200|791881|742041|782660|777933|715669|798318|741879|726820|703719|794884|711974|732375|247390|585375|754125|754483|778291|746614|793601|794777|673284|754358|734249|797260|652848|785718|798994|729957|757088|794153|722523|758172|793455|740250|770275|399260|740737|758300|781121|761797|740576|721104|740732|780832|740577|719424|742521|741550|747926|741392|761285|741393|753923|714776|780161|737459|798133|742522|716387|738035|707281|703638|770410|804140|678424|782729|803216|804381|799035|748810|767835";
    HashSet<String> multiDimGoalObjects = Sets.newHashSet();
    HashSet<String> chGrayObjects = Sets.newHashSet();
    String[] multiDimGoalSplit = multiDimGoal.split("\\|");
    for (String string : multiDimGoalSplit) {
      boolean multiDimGoalContains = CH445EIS.contains(string);
      if (multiDimGoalContains) {
        multiDimGoalObjects.add(string);
      }
    }
    System.out.println("multiDimGoalSplit = " + (multiDimGoalObjects));
    String[] chGraySplit = chGray.split("\\|");
    for (String string : chGraySplit) {
      boolean chGrayContains = CH445EIS.contains(string);
      if (chGrayContains) {
        chGrayObjects.add(string);
      }
    }
    System.out.println("chGrayObjects = " + (chGrayObjects));
  }
}
