package com.fxiaoke.bi.warehouse.ods.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.bean.PreAggFilter;
import com.fxiaoke.bi.warehouse.common.dag.DAGBean;
import com.fxiaoke.bi.warehouse.common.db.er.NodeTable;
import com.fxiaoke.bi.warehouse.common.entity.DbSyncInfoFlowDO;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.DateTimeUtil;
import com.fxiaoke.bi.warehouse.dws.model.TopologyTableAggRule;
import com.fxiaoke.bi.warehouse.ods.args.DBSyncInfoArg;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.ChangedObjectAndFieldMessage;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.helper.JoinHelper;
import com.github.mybatis.util.EntityUtil;
import com.github.mybatis.util.PersistMeta;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import junit.framework.TestCase;
import org.apache.commons.collections.CollectionUtils;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.util.ResourceUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2023/6/27
 */
public class CommonTests extends TestCase {

  private static final ThreadLocal<Random> RANDOM = ThreadLocal.withInitial(Random::new);
  @Test
  public void testPair(){
    Map<String, BIPair<Long,Long>> map = Maps.newHashMap();
    BIPair<Long,Long> pair = BIPair.of(1L,2L);
    System.out.println(pair.getFirst());
    System.out.println(pair.getSecond());
    map.put("a",pair);
  
    System.out.println(JSON.toJSONString(map));
    String json=JSON.toJSONString(map);
    Map<String,BIPair<Long,Long>> map2=JSON.parseObject(json, new TypeReference<>() {
    });
    System.out.println(map2.get("a").getFirst());
    System.out.println(map2.get("a").getSecond());
  }

  @Test
  public void testThread1(){
    new Thread(()->{
      System.out.println("-------");
    }).start();
    Uninterruptibles.sleepUninterruptibly(20,TimeUnit.SECONDS);
  }
  @Test
  public void testPointRegx(){
    System.out.println(CommonUtils.geographyRex.matcher("POINT(116.29628973366323 40.14542047632642)").matches());
    System.out.println(CommonUtils.geographyRex.matcher("POINT(-116.0 40.14542047632642)").matches());
    System.out.println(CommonUtils.geographyRex.matcher("POINT(116.29628973366323 -40.0)").matches());
    System.out.println(CommonUtils.geographyRex.matcher("POINT(-116.29628973366323 -40.14542047632642)").matches());

    System.out.println(CommonUtils.point.matcher("(-116.29628973366323,40.14542047632642)").matches());
    System.out.println(CommonUtils.point.matcher("(116.29628973366323,-40.14542047632642)").matches());
    System.out.println(CommonUtils.point.matcher("(-116.29628973366323,-40.14542047632642)").matches());
    System.out.println(CommonUtils.point.matcher("(116.1,40.0)").matches());

   Matcher matcher=  CommonUtils.geographyRex.matcher("POINT(116.29628973366323 40.14542047632642)");
   if(matcher.matches()){
     System.out.println(matcher.group(1)+","+matcher.group(2));
   }
    matcher= CommonUtils.point.matcher("(-116.29628973366323,-40.14542047632642)");
    if(matcher.matches()){
      System.out.println(matcher.group(1)+","+matcher.group(2));
    }
  }

  @Test
  public void testWideTables2() throws Exception {
    try (FileWriter writer = new FileWriter("D:\\常用脚本\\clickhouse帮助目录\\widetable\\biz_sales_order.json", Charset.defaultCharset())) {
      BufferedReader reader = new BufferedReader(new FileReader("D:\\常用脚本\\clickhouse帮助目录\\logAnalys\\fs_biz_sales_order_dag.js"));
      String checkObj = "biz_sales_order";
      List<TopologyTableAggRule> allStatRuleList = Lists.newArrayList();
      String line = reader.readLine();
      int lineSize=1;
      while (StringUtils.isNotBlank(line)) {
        try{
          List<TopologyTableAggRule> statRuleList = JSON.parseObject(line, new TypeReference<List<TopologyTableAggRule>>() {
          });
          if (CollectionUtils.isNotEmpty(statRuleList)) {
            statRuleList.stream()
                        .filter(statRule -> Objects.equals(checkObj, statRule.getRootNodeTable().getName()))
                        .forEach(allStatRuleList::add);
          }
        }catch (Exception e){
          System.out.println("lineNum:"+lineSize+",json="+line);
          throw new RuntimeException(e);
        }
        line = reader.readLine();
        lineSize++;
      }
      reader.close();
      DAGBean dagBean = new DAGBean();
      System.out.println("topologyTableAggRules size=" + allStatRuleList.size());
      allStatRuleList.forEach(ttr -> ttr.selectTablesBFS(dagBean));
      String dagJSON = JSON.toJSONString(dagBean);
      writer.write(dagJSON);
      System.out.println(dagJSON);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }
  @Test
  public void testFindAllNodeTables()throws Exception{
    BufferedReader reader = new BufferedReader(new FileReader("D:\\常用脚本\\clickhouse帮助目录\\logAnalys\\fs_biz_sales_order_dag.js"));
    List<TopologyTableAggRule> allStatRuleList = Lists.newArrayList();
    String line = reader.readLine();
    int lineSize=1;
    while (StringUtils.isNotBlank(line)) {
      try{
        List<TopologyTableAggRule> statRuleList = JSON.parseObject(line, new TypeReference<List<TopologyTableAggRule>>() {
        });
        if (CollectionUtils.isNotEmpty(statRuleList)) {
          statRuleList
                      .forEach(allStatRuleList::add);
        }
      }catch (Exception e){
        System.out.println("lineNum:"+lineSize+",json="+line);
        throw new RuntimeException(e);
      }
      line = reader.readLine();
      lineSize++;
    }
    reader.close();
    System.out.println("topologyTableAggRules size=" + allStatRuleList.size());
    allStatRuleList.forEach(ttr -> {
     Map<String, NodeTable> nodeTableMap = ttr.findAllNodeTables();
      System.out.println(nodeTableMap.size());
    });
  }

  @Test
  public void testBuildJoinLink()throws Exception{
    BufferedReader reader = new BufferedReader(new FileReader("D:\\常用脚本\\clickhouse帮助目录\\logAnalys\\fs_biz_sales_order_dag_v2.js"));
    String checkObj = "biz_sales_order";
    List<TopologyTableAggRule> allStatRuleList = Lists.newArrayList();
    String line = reader.readLine();
    int lineSize=1;
    while (StringUtils.isNotBlank(line)) {
      try{
        List<TopologyTableAggRule> statRuleList = JSON.parseObject(line, new TypeReference<List<TopologyTableAggRule>>() {
        });
        if (CollectionUtils.isNotEmpty(statRuleList)) {
          allStatRuleList.addAll(statRuleList);
        }
      }catch (Exception e){
        System.out.println("lineNum:"+lineSize+",json="+line);
        throw new RuntimeException(e);
      }
      line = reader.readLine();
      lineSize++;
    }
    reader.close();
    System.out.println("topologyTableAggRules size=" + allStatRuleList.size());
    allStatRuleList.forEach(ttr -> {
      String joins = ttr.buildJoinLink();
      System.out.println(joins);
    });
  }

  @Test
  public void testMergeAggRule()throws Exception{
    BufferedReader reader = new BufferedReader(new FileReader("D:\\常用脚本\\clickhouse帮助目录\\logAnalys\\fs_biz_sales_order_dag_v3.js"));
    List<TopologyTableAggRule> allStatRuleList = Lists.newArrayList();
    String line = reader.readLine();
    int lineSize=1;
    while (StringUtils.isNotBlank(line)) {
      try{
        List<TopologyTableAggRule> statRuleList = JSON.parseObject(line, new TypeReference<List<TopologyTableAggRule>>() {
        });
        if (CollectionUtils.isNotEmpty(statRuleList)) {
          allStatRuleList.addAll(statRuleList);
        }
      }catch (Exception e){
        System.out.println("lineNum:"+lineSize+",json="+line);
        throw new RuntimeException(e);
      }
      line = reader.readLine();
      lineSize++;
    }
    reader.close();
    System.out.println("topologyTableAggRules size=" + allStatRuleList.size());
    TopologyTableAggRule topologyTableAggRule=allStatRuleList.removeFirst();
    Map<String, PreAggFilter> aggFilters = topologyTableAggRule.mergeOtherTopologyRule(allStatRuleList);
    System.out.println(JSON.toJSONString(topologyTableAggRule));
    System.out.println(JSON.toJSONString(aggFilters));
  }

  @Test
  public void testMetadata(){
//    DbSyncInfoFlowDO dbSyncInfoFlowDO = new DbSyncInfoFlowDO();
//    dbSyncInfoFlowDO.setId("66a8bdfcc9902f4b466093a1");
//    dbSyncInfoFlowDO.setTenantId("-1");
//    dbSyncInfoFlowDO.setBatchNums(new int[]{1,2,3,4});
//    dbSyncInfoFlowDO.setStatus(0);
//    dbSyncInfoFlowDO.setCreateTime(new Date().getTime());
//    dbSyncInfoFlowDO.setIsDeleted(0);
//    dbSyncInfoFlowDO.setVersion(1);
//    dbSyncInfoFlowDO.setApiNameEiMap("{\"biz_account\":[\"85145\"],\"org_employee_user\":[\"85145\"],\"opportunity\":[\"85145\"]}");
//    dbSyncInfoFlowDO.setLastModifiedTime(new Date().getTime());
//    dbSyncInfoFlowDO.setDbSyncId("64ee0f5dbfbe303944dd65b6");
//    dbSyncInfoFlowDO.setPartitionName("i");
    Class<?> clazz = DbSyncInfoFlowDO.class;
    PersistMeta meta = EntityUtil.getMeta(clazz);
    Iterator<Map.Entry<String,Field>> var5 = meta.getColumns().entrySet().iterator();
    while(var5.hasNext()) {
      Map.Entry<String, Field> kv = (Map.Entry)var5.next();
      Field field = (Field)kv.getValue();

    }

  }
  @Test
  public void testShutdown(){
    ExecutorService fixedThreadPools = Executors.newFixedThreadPool(1);
    fixedThreadPools.submit(()->{
      Uninterruptibles.sleepUninterruptibly(10,TimeUnit.SECONDS);
    });
    fixedThreadPools.shutdown();
    System.out.println("shutdown....");
  }
  @Test
  public void testLocalDate2(){
    LocalDate lastMergeDate = new Timestamp(0L).toLocalDateTime().toLocalDate();
    System.out.println(lastMergeDate);
  }
  @Test
  public void testReplace(){
    String b= CommonUtils.chDb2Topic("****************************************************");
    System.out.println(b);
  }
  @Test
  public void testLocalTimeCompare(){
    long lastSyncTime = 0L;
    LocalDate lastMergeDate = new Timestamp(lastSyncTime).toLocalDateTime().toLocalDate();
    System.out.println(lastMergeDate);
    LocalDate localDate = LocalDate.now();
    boolean bAfter = localDate.isAfter(lastMergeDate);
    System.out.println(bAfter);
  }
  @Test
  public void testSplit(){
    List<String> fieldValues = Lists.newArrayList("a^b^c","1^2^ ");

    List<String> primaryKeyValues1 = fieldValues.stream()
                                               .map(value -> JoinHelper.joinSkipNullOrBlank(",", "'",
                                                 Arrays.asList(value.split("\\^"))))
                                               .map(v -> "(" + v + ")")
                                               .toList();
    List<String> primaryKeyValues2 = fieldValues.stream().map(value -> {
      String tuples = Arrays.stream(value.split("\\^")).map(v -> "'" + v + "'").collect(Collectors.joining(","));
      if (value.endsWith("^")) {
        tuples = tuples + ",''";
      }
      return tuples;
    }).map(v -> "(" + v + ")").toList();

    System.out.println(JSON.toJSONString(primaryKeyValues1));
    System.out.println(JSON.toJSONString(primaryKeyValues2));
  }

  @Test
  public void testCreateCustomThreshold(){
    String config= "a^1^3,b^33^55";
    Map<String, com.fxiaoke.common.Pair<Integer, Integer>> b= CHContext.createCustomThreshold(config);
    System.out.println(JSON.toJSONString(b));
  }

  @Test
  public void testSubString() {
    System.out.println(MessageFormat.format(
      "can not find theme node table aggApiName:{0}," + "themeApiName:{1}", "a", "b"));
    System.out.println(ObjectId.get().toString());
    System.out.println(ObjectId.get().toString());
    System.out.println(ObjectId.get().toString());
    System.out.println(ObjectId.get().toString());
    System.out.println(ObjectId.get().toString());
  }

  @Test
  public void testRegx() {
    Pattern extTableRegx = Pattern.compile("(.*)_ext_[0-9]+$");
    System.out.println(extTableRegx.matcher("discount_coupon__cext_3").matches());
  }

  @Test
  public void testRegx2(){
    Pattern ttlRegx = Pattern.compile("(?i).*bi_sys_ods_part\\s+IN\\s*\\(\\s*'i'\\s*,\\s*'c'\\s*\\).*",Pattern.DOTALL);
    String ttlStr1="ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version," +
      "             bi_sys_is_deleted)\n" + "        PARTITION BY bi_sys_ods_part\n" +
      "        ORDER BY (tenant_id, bi_sys_flag, id)" +
      "        TTL bi_sys_version + INTERVAL 1 MONTH DELETE WHERE bi_sys_ods_part = 's' AND  (bi_sys_flag = 0 OR is_deleted IN (-1, -2))," +
      "bi_sys_version + INTERVAL 3 DAY DELETE WHERE bi_sys_ods_part in ('i','c')\n" +
      "SETTINGS index_granularity = 8192;";
      String ttlStr2="ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/fsbidb044090001/{uuid}/', '{replica}', bi_sys_version, bi_sys_is_deleted) PARTITION BY bi_sys_ods_part ORDER BY (tenant_id, bi_sys_flag, id) TTL bi_sys_version + toIntervalMonth(1) WHERE (bi_sys_ods_part = 's') AND ((bi_sys_flag = 0) OR (is_deleted IN (-1, -2))), bi_sys_version + toIntervalDay(3) WHERE bi_sys_ods_part IN ('i', 'c') SETTINGS index_granularity = 8192";
    System.out.println(ttlRegx.matcher(ttlStr1).matches());
    System.out.println(ttlRegx.matcher(ttlStr2).matches());
  }

  @Test
  public void testLength() {
    System.out.println(String.valueOf(Integer.MAX_VALUE));
    System.out.println(Long.parseLong("13801328208") > 2147483647L);
  }

  @Test
  public void testHashCode() {
    //    Map<String,String> a= new HashMap<>();
    String chDB = "****************************************************";
    int hash = Math.abs(chDB.hashCode() == Integer.MIN_VALUE ? 0 : chDB.hashCode());
    for (int j = 0; j < 10; j++) {
      for (int i = 0; i < 20; i++) {
        String pgDB = "***************************." + i + ":5432/fsbidb006032" + i;
        int hash1 = Math.abs(pgDB.hashCode() == Integer.MIN_VALUE ? 0 : pgDB.hashCode());
        System.out.println("hash1====" + (hash ^ (hash1 % 5)));
        //        (h = key.hashCode()) ^ (h >>> 16)
        //        System.out.println((hash ^ (hash1 % 5)) % 12);
      }
    }
  }

  @Test
  public void testJSONAndEqu() {
    String msg1 = "{\"handleChangedObjectAndFieldArg\":{\"cleanslot\":0,\"objname\":\"object_O1Zf3__c\",\"status\":0," +
      "\"tenantid\":\"766852\",\"type\":\"text\"}}";
    String msg2 = "{\"handleChangedObjectAndFieldArg\":{\"cleanslot\":0,\"fieldname\":\"field_Zdp0g__c\"," +
      "\"objname\":\"object_O1Zf3__c\",\"status\":1,\"tenantid\":\"766852\",\"type\":\"employee\"}}";

    List<String> msgs = Lists.newArrayList(msg1, msg2);
    List<ChangedObjectAndFieldMessage> msgList = Lists.newArrayList();
    int i = 0;
    for (String msg : msgs) {
      ChangedObjectAndFieldMessage changedObjectAndFieldMessage = JSON.parseObject(msg,
        ChangedObjectAndFieldMessage.class);
      msgList.add(changedObjectAndFieldMessage);
      changedObjectAndFieldMessage.setMsgId("" + i++);
    }
    List<ChangedObjectAndFieldMessage> msgList2 = msgList.stream().distinct().toList();
    System.out.println(msgList2.size());
  }

  @Test
  public void testList() {
    //    System.out.println(String.join(",",Lists.newArrayList()));
    //    String sysEiTables = "";
    //    System.out.println(Sets.newHashSet(Splitter.on(",").splitToList(sysEiTables)));
    //    System.out.println(JSON.toJSONString(Sets.newHashSet(Splitter.on(",").splitToList(sysEiTables))));

    //    List<String> a= Lists.newArrayList("1","2","3","4");
    //    List<List<String>> b = Lists.partition(a,Math.round(a.size() / 1.0f));
    //    System.out.println(JSON.toJSONString(b));

    //    String[] eiAndApiName= "1^2".split("\\^");
    //    Map<String,Set<String>> a= Maps.newHashMap();
    //    a.computeIfAbsent("b",key->Sets.newHashSet()).add("cccc");
    //    System.out.println(JSON.toJSONString(a));
    //    System.out.println(JSON.toJSONString("sch_71570".substring(4)));
    String a = "[进入'比对确认']";
    System.out.println(a.replaceAll("'", "\\'"));
  }

  @Test
  public void testStringUtil() {

    BigDecimal bigDecimal = BigDecimal.valueOf((double) 1 / 180).setScale(3, RoundingMode.HALF_UP);
    System.out.println(bigDecimal.floatValue());
    for (int i = 1; i <= 999; i++) {
      BigDecimal level = bigDecimal.multiply(BigDecimal.valueOf(i));
      if (level.compareTo(BigDecimal.valueOf(0.999f)) > 0) {
        break;
      }
      System.out.println("index:" + i + ",level:" + level.setScale(3, RoundingMode.HALF_UP));
    }


    //    BigDecimal bigDecimal= BigDecimal.valueOf((double) 1 / 80).setScale(3, RoundingMode.HALF_UP);
    //    System.out.println(bigDecimal.floatValue());
    //    for(int i=1;i<=99;i++){
    //      BigDecimal level= bigDecimal.multiply(BigDecimal.valueOf(i));
    //     if(level.compareTo(BigDecimal.valueOf(0.999f))>0){
    //       break;
    //     }
    //     System.out.println("index:"+i+",level:"+level.setScale(3, RoundingMode.HALF_UP));
    //    }
  }

  @Test
  public void testPartitions() {
    System.out.println(JSON.toJSONString(CHContext.createQuantilesLevel(100)));
  }

  @Test
  public void testQuantiles() {
    List<BigDecimal> floats = CHContext.calQuantiles(300000000, 10000000L);
    System.out.println("size:" + floats.size() + ",levels:" + JSON.toJSONString(floats));
    //    List<BigDecimal> floats2 = CHContext.calQuantiles(180000000L, 10000000L);
    //    System.out.println("size:"+floats2.size()+",levels:"+JSON.toJSONString(floats2));
    //    List<BigDecimal> floats3 = CHContext.calQuantiles(3000000000L, 10000000L);
    //    System.out.println("size:"+floats3.size()+",levels:"+JSON.toJSONString(floats3));
  }

  @Test
  public void testChar() {
    char separator = File.separatorChar;
    System.out.println(separator);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    System.out.println(LocalDateTime.now().format(formatter));
  }

  @Test
  public void testListPartition() {
    //    List<String> tableList = Lists.newArrayList("t1", "t2", "t3", "t4", "t5", "t6");
    //    List<List<String>> tablePartition = Lists.partition(tableList, 3);
    //
    int multiple = 4;
    List<Float> tenLevels = Arrays.asList(CHContext.createQuantilesLevel(100));
    int partSize = Math.round(tenLevels.size() / (float) multiple);
    List<List<Float>> a = Lists.newArrayList();
    int from = 0;
    for (int i = 0; i < multiple - 1; i++) {
      if (i == (multiple - 2)) {
        a.add(tenLevels.subList(from, tenLevels.size()));
        break;
      }
      a.add(tenLevels.subList(from, from += partSize));
    }
    System.out.println(JSON.toJSONString(a));
  }


  @Test
  public void testListPartition2() {
    //    List<String> tableList = Lists.newArrayList("t1", "t2", "t3", "t4", "t5", "t6");
    //    List<List<String>> tablePartition = Lists.partition(tableList, 3);
    //
    int multiple = 4;
    List<Float> tenLevels = Arrays.asList(CHContext.createQuantilesLevel(100));
    int partSize = Math.round(tenLevels.size() / (float) multiple);
    List<List<Float>> a = Lists.newArrayList();
    int from = 0;
    for (int i = 0; i < multiple - 1; i++) {
      if (i == (multiple - 2)) {
        a.add(tenLevels.subList(from, tenLevels.size()));
        break;
      }
      a.add(tenLevels.subList(from, from += partSize));
    }
    System.out.println(JSON.toJSONString(a));
  }

  @Test
  public void testWriter() {
    String sqlFieldPath = "D:\\daima\\a.txt";
    try (FileWriter writer = new FileWriter(sqlFieldPath, StandardCharsets.UTF_8)) {
      writer.write("12121212121" + ";");
      writer.write("\n\n");
      writer.flush();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void testSort() {
    DBSyncInfo dbSyncInfos1 = new DBSyncInfo();
    dbSyncInfos1.setLastMergeAggTime(null);
    DBSyncInfo dbSyncInfos2 = new DBSyncInfo();
    dbSyncInfos2.setLastMergeAggTime(1L);
    DBSyncInfo dbSyncInfos3 = new DBSyncInfo();
    dbSyncInfos3.setLastMergeAggTime(2L);
    List<DBSyncInfo> dbSyncInfos = Lists.newArrayList(dbSyncInfos1, dbSyncInfos2, dbSyncInfos3);
    Optional<Long> lastMergeTime = dbSyncInfos.stream()
                                              .map(DBSyncInfo::getLastMergeAggTime)
                                              .filter(Objects::nonNull)
                                              .max(Comparator.naturalOrder());
    System.out.println(lastMergeTime.get());
  }

  @Test
  public void testLocalDate() {
    Timestamp timestamp = new Timestamp(1695225600000L);
    LocalDateTime localDateTime = LocalDateTime.now();
    LocalDate localDate = LocalDate.now();
    boolean a = localDate.isAfter(timestamp.toLocalDateTime().toLocalDate());
    boolean b = localDate.isAfter(localDateTime.toLocalDate());
    System.out.println(a);
    System.out.println(b);
  }

  @Test
  public void testSQL() {
    //    SQL sql = new SQL();
    //    sql.SELECT("a").FROM("biz_account").WHERE(" bi_sys_batch_id < 3").AND().WHERE("(b,c,d) in before_data");
    //    System.out.println(sql.toString());
    //    System.out.println(URLEncoder.encode("****************************************************",
    //      StandardCharsets.UTF_8));\
    //    System.out.println(Integer.MAX_VALUE);
    String churl = "fsbidb044090001";
    //    String churl="fsbidb044001001";
    String pgurl = "***************************************************";
    int chDbHash = Math.abs(churl.hashCode() == Integer.MIN_VALUE ? 0 : churl.hashCode());
    int pgDBHash = Math.abs(pgurl.hashCode() == Integer.MIN_VALUE ? 0 : pgurl.hashCode());
    //    System.out.println(pgDBHash%8);
    int hash = chDbHash + 0;//再根据pgdb做一个取模过程增加一个有限的散列度
    System.out.println(hash % 8);
    int hash1 = chDbHash + 1;//再根据pgdb做一个取模过程增加一个有限的散列度
    System.out.println(hash1 % 8);
    int hash2 = chDbHash + 2;//再根据pgdb做一个取模过程增加一个有限的散列度
    System.out.println(hash2 % 8);
  }

  @Test
  public void testPWD() {
    //    PasswordUtil.decode();
    //    System.out.println(PasswordUtil.encode("kGer$sW"));
    //    System.out.println(PasswordUtil.encode("GlkvlqU3232jKHUB"));
    //    System.out.println(PasswordUtil.encode("$O8E%&yt"));
    //    System.out.println(PasswordUtil.encode("1qaz#EDC5tgb"));
    System.out.println(PasswordUtil.encode("Ht4Vh9Wvs1U2Rh8Br8N"));
    System.out.println(PasswordUtil.encode("T8GpvHoZyxCSWVLqo6R"));
  }

  @Test
  public void testIPAndDB() {
    //    String a="*******************************************";
    //    System.out.println(a.substring(18));
    //    String dbName="fsbidb044003001";
    //    System.out.println(dbName.substring(0,dbName.length()-3));
    //    for (int i = 0; i < 6; i++) {
    //      System.out.println(ObjectId.get().toString());
    //    }
    //    String chdb="****************************************************";
    String chdb = "****************************************************";
    int hash = Math.abs(chdb.hashCode() == Integer.MIN_VALUE ? 0 : chdb.hashCode());
    System.out.println(hash % 8);
  }

  //线程工厂
  ThreadFactory factory = new ThreadFactoryBuilder().setDaemon(true).setNameFormat("bi2ch-fetch-%d").build();
  //线程池
  ExecutorService executor = Executors.newFixedThreadPool(200, factory);

  @Test
  public void testThreads() throws Exception {
    List<String> tablePartition = Lists.newArrayList("a", "b", "c");
    CompletableFuture[] cfs = tablePartition.stream().map(key -> CompletableFuture.runAsync(() -> {
      try {
        if (key.equals("a")) {
          Uninterruptibles.sleepUninterruptibly(20, TimeUnit.SECONDS);
        } else if (key.equals("b")) {
          Uninterruptibles.sleepUninterruptibly(3, TimeUnit.SECONDS);
        } else {
          Uninterruptibles.sleepUninterruptibly(3, TimeUnit.SECONDS);
          throw new RuntimeException("throw exception!");
        }
        System.out.println(Thread.currentThread().getName() + ":finish ======>" + key);
      } catch (Exception e) {
        //        e.printStackTrace();
        throw new RuntimeException(e);
      }
    }, executor)).toArray(CompletableFuture[]::new);

    CompletableFuture.allOf(cfs).join();
    System.out.println("finish");
    //    Uninterruptibles.sleepUninterruptibly(500, TimeUnit.SECONDS);
  }

  @Test
  public void testInitDbSyncInfo() {
    List<DBSyncInfoArg> dbSyncInfoArgs = Lists.newArrayList();
    String okEis = "";
    String chDb = "****************************************************";
    Set<String> okEiList = Sets.newHashSet(Splitter.on(",").splitToList(okEis));
    String grayEis = "";
    List<String> grayEiList = Splitter.on("|").splitToList(grayEis);

    System.out.println(grayEiList.stream().filter(ei -> !okEiList.contains(ei)).toList());
  }

  @Test
  public void testAddColumn() throws Exception {
    FileWriter writer = new FileWriter("D:\\clickhouse_workspace\\public_table_add_column_20231113.sql",
      StandardCharsets.UTF_8);
    String out_data_own_department =
      "ALTER TABLE %s on cluster '{cluster}' ADD COLUMN IF NOT EXISTS  " + "`out_data_own_department` String;";
    String out_data_own_organization =
      "ALTER TABLE %s on cluster '{cluster}' ADD COLUMN IF NOT EXISTS  " + "`out_data_own_organization` String;";
    BufferedReader reader = new BufferedReader(new FileReader(new File(
      "D:\\clickhouse_workspace" + "\\public_tables20231113.txt")));
    String tableName = null;
    while ((tableName = reader.readLine()) != null) {
      writer.write(String.format(out_data_own_department, tableName));
      writer.write("\n");
      writer.write(String.format(out_data_own_organization, tableName));
      writer.write("\n");
    }
    writer.flush();
    writer.close();
    reader.close();
  }

  @Test
  public void testThreadCall() throws Exception {
    List<Future<Integer>> taskFuture = new ArrayList<>();
    ExecutorService fixedThreadPools = Executors.newFixedThreadPool(3);
    for (int j = 0; j < 3; j++) {
      taskFuture.add(fixedThreadPools.submit(() -> {
        Uninterruptibles.sleepUninterruptibly(2, TimeUnit.SECONDS);
        return 10;
      }));
    }
    for (int i = 0; i < 3; i++) {
      System.out.println(taskFuture.get(i).get());
    }
    fixedThreadPools.shutdown();
  }

  @Test
  public void testDecimal2() {
    BigDecimal bigDecimal = new BigDecimal("-1602999992367186000");
    System.out.println(String.valueOf(bigDecimal));
    double doubleValue = bigDecimal.doubleValue();
    System.out.println(bigDecimal.toPlainString());
    if (doubleValue < (-1 * 10 ^ (38 - 20)) || doubleValue > (1 * 10 ^ (38 - 20))) {
      System.out.println("out of range");
    }
  }

  @Test
  public void testDecimal3() {
    BigDecimal bigDecimal = new BigDecimal("2037392526");
    double doubleValue = bigDecimal.doubleValue();
    System.out.println(doubleValue);
    System.out.println(Math.pow(10, (38 - 20)));
    System.out.println(-1 * Math.pow(10, (38 - 20)));
    if (doubleValue < -1 * Math.pow(10, (38 - 20)) || doubleValue > Math.pow(10, (38 - 20))) {
      System.out.println("numeric format error value:{},etlTarget:{},typeName:{}");
    }
  }

  @Test
  public void testBatchDBSyncInfo() {
    String pgDbs = "***************************.2:5432/%s";
    String chDb = "****************************************************";
    String pgSchema = "public";
    //    List<String> pgDBlist = Splitter.on(",").splitToList(pgDbs);
    List<String> pgDBlist = Lists.newArrayList();
    for (int i = 1; i <= 50; i++) {
      String pgDB = String.format(pgDbs, "fsbidb0020" + String.format("%02d", i));
      pgDBlist.add(pgDB);
    }
    List<DBSyncInfoArg> dbSyncInfoArgs = Lists.newArrayList();
    for (String pgDb : pgDBlist) {
      DBSyncInfoArg dbSyncInfoArg = new DBSyncInfoArg();
      dbSyncInfoArg.setId(ObjectId.get().toString());
      dbSyncInfoArg.setPgDb(pgDb);
      dbSyncInfoArg.setChDb(chDb);
      dbSyncInfoArg.setCreateTime(new Date().getTime());
      dbSyncInfoArg.setStatus(-2);
      dbSyncInfoArg.setPgSchema(pgSchema);
      dbSyncInfoArg.setIsDeleted(0);
      dbSyncInfoArg.setBatchNum(0L);
      dbSyncInfoArgs.add(dbSyncInfoArg);
    }
    System.out.println(JSON.toJSONString(dbSyncInfoArgs));
  }


  @Test
  public void testDBSyncInfo() {
    String pgDbs = "***************************************************";
    String chDb = "****************************************************";
    String pgSchema = "sch_768595";
    List<String> pgDBlist = Splitter.on(",").splitToList(pgDbs);
    //    List<String> pgDBlist = Lists.newArrayList();
    //    for(int i=1;i<=50;i++){
    //      String pgDB=String.format(pgDbs,"fsbidb0020"+String.format("%02d",i));
    //      pgDBlist.add(pgDB);
    //    }
    List<DBSyncInfoArg> dbSyncInfoArgs = Lists.newArrayList();
    for (String pgDb : pgDBlist) {
      DBSyncInfoArg dbSyncInfoArg = new DBSyncInfoArg();
      dbSyncInfoArg.setId(ObjectId.get().toString());
      dbSyncInfoArg.setPgDb(pgDb);
      dbSyncInfoArg.setChDb(chDb);
      dbSyncInfoArg.setCreateTime(new Date().getTime());
      dbSyncInfoArg.setStatus(-2);
      dbSyncInfoArg.setPgSchema(pgSchema);
      dbSyncInfoArg.setIsDeleted(0);
      dbSyncInfoArg.setBatchNum(0L);
      dbSyncInfoArg.setAllowIncPartition(1);
      dbSyncInfoArgs.add(dbSyncInfoArg);
    }
    System.out.println(JSON.toJSONString(dbSyncInfoArgs));
  }

  @Test
  public void testDBSyncInfoPlus() {
    String pgDbFormatStr = "***************************.10:5432/fsbidb0100%s";
    String chDb = "****************************************************";
    String pgSchema = "public";
    int dbCount = 50;
    List<String> pgDBlist = Lists.newArrayList();
    for (int i = 1; i <= dbCount; i++) {
      String format;
      if (i < 10) {
        format = String.format(pgDbFormatStr, "0" + i);
      } else {
        format = String.format(pgDbFormatStr, i);
      }
      pgDBlist.add(format);
    }
    List<DBSyncInfoArg> dbSyncInfoArgs = Lists.newArrayList();
    for (String pgDb : pgDBlist) {
      DBSyncInfoArg dbSyncInfoArg = new DBSyncInfoArg();
      dbSyncInfoArg.setId(ObjectId.get().toString());
      dbSyncInfoArg.setPgDb(pgDb);
      dbSyncInfoArg.setChDb(chDb);
      dbSyncInfoArg.setCreateTime(new Date().getTime());
      dbSyncInfoArg.setStatus(-2);
      dbSyncInfoArg.setPgSchema(pgSchema);
      dbSyncInfoArg.setIsDeleted(0);
      dbSyncInfoArg.setBatchNum(0L);
      dbSyncInfoArgs.add(dbSyncInfoArg);
    }
    System.out.println(JSON.toJSONString(dbSyncInfoArgs));
    System.out.println(dbSyncInfoArgs.stream().map(DBSyncInfoArg::getPgDb).collect(Collectors.toList()));
  }

  @Test
  public void testCreateDbSyncInfo(){

  }
  @Test
  public void testJSONETL()throws Exception{
    File rmPgDB = ResourceUtils.getFile("classpath:stat/base_crm_feedrelation_bug.json");
    String pgs = Files.readString(rmPgDB.toPath());
    JSONArray jsonArray=JSON.parseArray(pgs);
    Map<String, Set<String>> tenantIdAndViewId= Maps.newHashMap();
    jsonArray.forEach(item->{
      JSONObject e = (JSONObject) item;
      tenantIdAndViewId.computeIfAbsent(String.valueOf(e.getInteger("ei")),key-> Sets.newHashSet()).add(e.getString("field_id"));
    });
    String sql="select * from agg_rule where tenant_id='%s' and field_id in(%s)";
    tenantIdAndViewId.forEach((k,v)->{
      String fieldIds=v.stream().map(f->"'"+f+"'").collect(Collectors.joining(","));
      System.out.println(String.format(sql,k,fieldIds));
    });
  }
  @Test
  public void testCreatePgJdbcUrl() {
    List<DBSyncInfoArg> dbSyncInfoArgs = Lists.newArrayList();
    DBSyncInfoArg dbSyncInfoArg1 = new DBSyncInfoArg();
    //    dbSyncInfoArg1.setId(ObjectId.get().toString());
    dbSyncInfoArg1.setPgDb("a");
    dbSyncInfoArg1.setChDb("b");
    dbSyncInfoArg1.setCreateTime(new Date().getTime());
    dbSyncInfoArg1.setStatus(-2);
    dbSyncInfoArg1.setIsDeleted(0);
    dbSyncInfoArg1.setBatchNum(0L);
    dbSyncInfoArgs.add(dbSyncInfoArg1);
    //
    //    DBSyncInfoArg dbSyncInfoArg2 = new DBSyncInfoArg();
    //    dbSyncInfoArg2.setPgDb("a");
    //    dbSyncInfoArg2.setChDb("b");
    //    dbSyncInfoArg2.setCreateTime(new Date().getTime());
    //    dbSyncInfoArgs.add(dbSyncInfoArg2);

    System.out.println(JSON.toJSONString(dbSyncInfoArg1.toDBSyncInfo()));
  }

  @Test
  public void testCompletableFuture()throws Exception{
    CompletableFuture<String> completedFuture = CompletableFuture.completedFuture("Hello, World!");
    // 获取并打印结果
//    completedFuture.thenAccept(result -> System.out.println("Result: " + result));

    System.out.println(completedFuture.get());
  }
  @Test
  public void testCompletableFuture2()throws Exception{
    // 创建一个自定义的 Executor
    ExecutorService executor = Executors.newFixedThreadPool(2);
    // 创建一个 Supplier
    Supplier<String> supplier = () -> {
      // 模拟耗时操作
      try {
        System.out.println("start...");
        Thread.sleep(2000);
        System.out.println("end...");
        throw new RuntimeException("throw RuntimeException");
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
      return "Hello, World!";
    };
    // 使用 supplyAsync 方法异步执行 Supplier
    CompletableFuture<String> future = CompletableFuture.supplyAsync(supplier, executor);
    Uninterruptibles.sleepUninterruptibly(10,TimeUnit.SECONDS);
    System.out.println(future.get());
    // 处理结果
//    future.thenAccept(result -> System.out.println("Result: " + result)).get();
//    System.out.println(future.get());
    // 关闭 Executor
    executor.shutdown();
  }

  @Test
  public void testEquals(){
    int a=1;
    Integer b = null;
    System.out.println(Objects.equals(a,b));
    System.out.println(a==b);
//    System.out.println(b.equals(a));
  }

  @Test
  public void testRetry() {
    long time = 10;
    long cap = 20;
    for(int i=1;i<=50;i++){
      int retries = i;
      long baseTime = Math.min(time * (1L << retries), cap);
      System.out.println((long) (baseTime * (RANDOM.get().nextDouble() + 0.5)));
    }
  }

  @Test
  public void testFunction() {
    helloName(B::getName);
  }

  public void helloName(Bfactory bfactory) {
    B b = new B();
    bfactory.getName(b, "world");
  }

  static class B {
    public String getName(String name) {
      return "hello" + name;
    }
  }


  @FunctionalInterface
  interface Bfactory {
    void getName(B b, String name);
  }

  @Test
  public void testSplit2(){
   List<String> a = Lists.newArrayList("1","2","3");
   a.clear();
    System.out.println(a.size());
//    System.out.println(JSON.toJSON(a.subList(1,a.size())));
  }

  @Test
  public void testZoneDateTime(){
   ZonedDateTime a= DateTimeUtil.toZonedDateTimeOfBeginDate(1745722555125L, ZoneId.of("Asia/Shanghai"));
   System.out.println(a.toInstant().toEpochMilli());
   ZonedDateTime b= DateTimeUtil.toZonedDateTimeOfBeginDate(1745722555125L, ZoneId.of("UTC+9"));
    System.out.println(b.toInstant().toEpochMilli());

  }
}
