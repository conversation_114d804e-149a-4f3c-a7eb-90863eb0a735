package com.fxiaoke.bi.warehouse.ods.service;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.component.MybatisBITenantPolicy;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.entity.DbSyncInfoFlowDO;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.ods.bean.BatchUpdateDbTableSyncInfo;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.integrate.service.CHDataToCHService;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.IntegrateServiceImpl;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2024/4/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class DBTransferTest {
  @Resource
  private DBTransferService dbTransferService;
  @Resource
  private PgCommonDao pgCommonDao;
  @Resource
  private PgDataSource pgDataSource;
  @Resource
  private MybatisBITenantPolicy mybatisBITenantPolicy;
  @Autowired
  private CHDataToCHService chDataToCHService;
  @Resource
  private DbTableSyncInfoService dbTableSyncInfoService;
  @Resource
  private IntegrateServiceImpl integrateService;
  @Test
  public void testInterval(){
    String pgDBName="bi_112";
    Long syncInterval = WarehouseConfig.customSyncInterval.getOrDefault(pgDBName, WarehouseConfig.defaultSyncInterval);
    System.out.println(syncInterval);
  }
  @Test
  public void testCH2CH(){
    String chDB="***************************************************";
    String pgDB="*****************************************";
    String schema="public";
    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo(chDB,pgDB,schema);
    DBSyncInfoBO dbSyncInfoCopy = DBSyncInfoBO.createInstanceOf(dbSyncInfo);
    AtomicLong nextBatchNum= new AtomicLong(dbSyncInfo.getBatchNum()+1);
    List<String> tenantIdList= Lists.newArrayList(dbSyncInfoCopy.getLastSyncEis());
    Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = Maps.newHashMap();
    String partitionValue="s";
    chDataToCHService.transferOperationDataToCH(nextBatchNum, dbSyncInfoCopy, tenantIdList, dbTableSyncInfoMap, partitionValue);
  }

  @Test
  public void testIntegratePlus(){
    String chDB="***************************************************";
    String pgDB="***********************************************";
    String schema="sch_82958";
    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo(chDB,pgDB,schema);
    DBSyncInfoBO dbSyncInfoCopy = DBSyncInfoBO.createInstanceOf(dbSyncInfo);
    AtomicLong nextBatchNum= new AtomicLong(dbSyncInfo.getBatchNum()+1);
    Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap= Maps.newHashMap();
    integrateService.executeIntegrateDataPlus(nextBatchNum, dbSyncInfoCopy, dbTableSyncInfoMap, "i");
  }

  @Test
  public void testSyncBizDownstreamNew() {
    String chDB = "***************************************************";
    String pgDB = "***********************************************";
    String schema = "sch_82958";
    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo(chDB, pgDB, schema);
    DBSyncInfoBO dbSyncInfoCopy = DBSyncInfoBO.createInstanceOf(dbSyncInfo);
    AtomicLong nextBatchNum = new AtomicLong(dbSyncInfo.getBatchNum() + 1);
    Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = Maps.newHashMap();
    String tableName = "org_employee_user";
    String toTable = String.format(tableName + "_%s", CHContext.DOWNSTREAM);
    integrateService.syncBizDownstreamNew(nextBatchNum, "82958", tableName, toTable,dbTableSyncInfoMap,"i",dbSyncInfoCopy);
  }

  @Test
  public void testDbSyncFlow(){
    String chDB="***************************************************";
    String pgDB="*****************************************";
    String schema="public";
    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo(chDB,pgDB,schema);
    DbSyncInfoFlowDO dbSyncInfoFlowDO = dbTransferService.findDbSyncingFlow(dbSyncInfo);
    List<DbTableSyncInfoDO> dbTableSyncInfos = Lists.newArrayList();
    DbTableSyncInfoDO dbTableSyncInfoDO= new DbTableSyncInfoDO();
    dbTableSyncInfoDO.setDbSyncId("6412b13f5cd44942982c91b7");
    dbTableSyncInfoDO.setTableName("biz_account");
    dbTableSyncInfoDO.setApiNameEiMap("{\"biz_account\":[\"82313\"]}");
    dbTableSyncInfos.add(dbTableSyncInfoDO);

    DbTableSyncInfoDO dbTableSyncInfoDO2= new DbTableSyncInfoDO();
    dbTableSyncInfoDO2.setDbSyncId("6412b13f5cd44942982c91b7");
    dbTableSyncInfoDO2.setTableName("object_data");
    dbTableSyncInfoDO2.setApiNameEiMap("{\"contact_udef\":[\"82313\"],\"customer_udef\":[\"82313\"]}");
    dbTableSyncInfos.add(dbTableSyncInfoDO2);
    Map<String,DbTableSyncInfoDO> tableSyncInfoDOMap = dbTableSyncInfos.stream().collect(Collectors.toMap(DbTableSyncInfoDO::getTableName, Function.identity()));
    dbTransferService.updateDbSyncInfoFlow(dbSyncInfo, new AtomicLong(10L), tableSyncInfoDOMap, dbSyncInfoFlowDO);
  }

  @Test
  public void testDoTransferByTable() throws Exception {
    String tableName="biz_account";
    String dbSyncId="6412b13f5cd44942982c91b7";
    List<String> pks = Splitter.on(",").omitEmptyStrings().splitToList("66c43b2632a8ac0006ff624e");
    String partition= WarehouseConfig.STOCK_PARTITION_NAME;
    dbTransferService.syncDataByTenantIdPrimaryKey(dbSyncId, tableName, "90970", pks,true,partition);
  }

  //按照表同步数据
  @Test
  public void testSyncByTable() throws Exception {
    String tableName = "cases";
    String tenantId = "71570";
    JdbcConnection jdbcConnection = pgDataSource.getJdbcConnection(tenantId);
    List<DBSyncInfo> dbSyncInfos = pgCommonDao.queryDBSyncInfoById(Lists.newArrayList("6412b13f5cd44942982c91b7"));
    DBSyncInfo dbSyncInfo= dbSyncInfos.get(0);
    dbSyncInfo.setBatchNum(98679L);
    long timeTag = System.currentTimeMillis();
    DBSyncInfoBO dbSyncInfoBO = DBSyncInfoBO.createInstanceOf(dbSyncInfo);
    String partitionName= WarehouseConfig.STOCK_PARTITION_NAME;
    BatchUpdateDbTableSyncInfo batchUpdateDbTableSyncInfo = BatchUpdateDbTableSyncInfo.createInstance( dbSyncInfoBO, dbTableSyncInfoService,30);
    dbTransferService.doTransferByTable(jdbcConnection, dbSyncInfoBO, tableName, Lists.newArrayList(tenantId), Maps.newHashMap(),partitionName,batchUpdateDbTableSyncInfo,System.currentTimeMillis());
    batchUpdateDbTableSyncInfo.flush();
    //    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo("**************************************************",
    //    "***********************************************", "sch_82958");
    //    pgCommonDao.sendCalculateEvent(dbSyncInfo);
  }

  @Test
  public void testIntegrate(){
    String ch="***************************************************";
    String pg="***********************************************";
    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo(ch, pg,"sch_82958");
//    BIAggSyncInfoDO a= dbTransferService.executeIntegrateData(dbSyncInfo);
//    System.out.println(JSON.toJSONString(a));
  }
  @Test
  public void testExchange(){
    String ch="aaaaa";
    String pg="dddd";
    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo(ch, pg,"sch_111");
//    dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo);
    dbTransferService.wait4IncrementMergeAggData("","","");
  }

  @Test
  public void testTransfer() {
    AtomicLong nextBatchNum = new AtomicLong(9999999L);
    DBSyncInfo dbSyncInfo = pgCommonDao.queryDBSyncInfo("***************************************************", "***********************************************", "sch_82958");
    Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = dbTableSyncInfoService.queryDbTableSyncInfoMap(dbSyncInfo);
    TransferEvent transferEvent = TransferEvent.builder()
                                               .chDbURL("***************************************************")
                                               .dbURL("***********************************************")
                                               .schema("sch_82958")
                                               .build();
    dbTransferService.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, System.currentTimeMillis());
  }
}
