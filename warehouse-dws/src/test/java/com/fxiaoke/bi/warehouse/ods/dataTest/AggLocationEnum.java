package com.fxiaoke.bi.warehouse.ods.dataTest;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 指标槽位类型
 */
@Getter
@AllArgsConstructor
public enum AggLocationEnum {

    COUNT("count", "Number", Pattern.compile("agg_count_\\d+"), "指标字段落count槽位列", "ds_agg_count_"),
    SUM("sum", "Number", Pattern.compile("agg_sum_\\d+"), "指标字段落sum槽位列", "ds_agg_sum_"),
    UNIQ("uniq", "AggFunc", Pattern.compile("agg_uniq_\\d+"), "指标字段落uniq槽位列", "ds_agg_uniq_");

    /**
     * 槽位类型
     */
    private String aggType;

    /**
     * 槽位field_type类型
     */
    private String FieldType;

    /**
     * 槽位映射规则
     */
    private Pattern pattern;

    /**
     * 枚举描述
     */
    private String desc;

    /**
     * 计算类型前缀
     */
    private String prefix;

    /**
     * 根据槽位获取指标计算枚举
     */
    public static AggLocationEnum getAggLocationEnumByLocation(String aggFieldLocation) {
        for (AggLocationEnum aggLocationEnum : values()) {
            if (aggLocationEnum.getPattern().matcher(aggFieldLocation).matches()) {
                return aggLocationEnum;
            }
        }
        return null;
    }

    /**
     * 根据计算类型获取计算枚举
     */
    public static AggLocationEnum getAggLocationEnumByAggType(String aggType) {
        for (AggLocationEnum aggLocationEnum : values()) {
            if (StringUtils.equals(aggLocationEnum.getAggType(), aggType)) {
                return aggLocationEnum;
            }
        }
        return null;
    }
}
