package com.fxiaoke.bi.warehouse.ods.dao;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.db.dao.DbSyncInfoFlowDao;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.entity.DbSyncInfoFlowDO;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.core.db.AggMergeDao;
import com.fxiaoke.bi.warehouse.core.db.entity.DBMergeInfoDO;
import com.fxiaoke.bi.warehouse.ods.args.ResetMaxModifiedTimeArg;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.dao.mapper.BISystemMapper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.nio.file.Files;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * @Author:jief
 * @Date:2023/12/8
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class pgCommonDaoTest {
  @Resource
  private PgCommonDao pgCommonDao;
  @Autowired
  private BISystemMapper biSystemMapper;
  @Resource
  private MetaDataDao metaDataDao;
  @Resource
  private DbSyncInfoFlowDao dbSyncInfoFlowDao;
  @Resource
  private AggMergeDao aggMergeDao;

  @Test
  public void testIdGenerate() {
    String string = ObjectId.get().toString();
    System.out.println("string = " + string);
  }

  @Test
  public void testQueryAggMerge(){
    List<DBMergeInfoDO> dbMergeInfoDOS=aggMergeDao.queryDBMergeInfoByChUrl("***************************************************");
    System.out.println("dbMergeInfoDOS size is "+dbMergeInfoDOS.size());
    System.out.println(JSON.toJSONString(dbMergeInfoDOS));
  }

  @Test
  public void testTopologyTableByVersion(){
    String tenantId="85145";
    String viewId="b5af5bdb1c4a475a326ae2bcddd14c98";
    Integer version=18;
   String viewSQL= pgCommonDao.getViewSqlByViewId( tenantId,  viewId,  version);
    System.out.println(viewSQL);
  }

  @Test
  public void testQueryDbSyncInfoFlow(){
    List<DbSyncInfoFlowDO> dbSyncInfoFlowDO = dbSyncInfoFlowDao.queryDbSyncFlow("*****************************************"
      , "public", "6412b13f5cd44942982c91b7", SyncStatusEnum.SYNC_ING.getStatus(),true,100);
    System.out.println(JSON.toJSONString(dbSyncInfoFlowDO));
  }

  @Test
  public void testQueryPartition(){
    List<String> a = dbSyncInfoFlowDao.queryPartitions("*****************************************", "public", "6412b13f5cd44942982c91b7");
    System.out.println(JSON.toJSONString(a));
  }

  @Test
  public void testInsertDbSyncInfoFlow(){
    DbSyncInfoFlowDO dbSyncInfoFlowDO = new DbSyncInfoFlowDO();
    dbSyncInfoFlowDO.setId("66a8bdfcc9902f4b466093a1");
    dbSyncInfoFlowDO.setTenantId("-1");
    dbSyncInfoFlowDO.setBatchNums(new Long[]{1L,2L,3L,4L});
    dbSyncInfoFlowDO.setStatus(0);
    dbSyncInfoFlowDO.setCreateTime(new Date().getTime());
    dbSyncInfoFlowDO.setIsDeleted(0);
    dbSyncInfoFlowDO.setVersion(1);
    dbSyncInfoFlowDO.setApiNameEiMap("{\"biz_account\":[\"85145\"],\"org_employee_user\":[\"85145\"],\"opportunity\":[\"85145\"]}");
    dbSyncInfoFlowDO.setLastModifiedTime(new Date().getTime());
    dbSyncInfoFlowDO.setDbSyncId("64ee0f5dbfbe303944dd65b6");
    dbSyncInfoFlowDO.setPartitionName("i");

    DbSyncInfoFlowDO dbSyncInfoFlowDO1 = new DbSyncInfoFlowDO();
    dbSyncInfoFlowDO1.setId("66a8c1c754c424020ae59e9c");
    dbSyncInfoFlowDO1.setTenantId("-1");
    dbSyncInfoFlowDO1.setBatchNums(new Long[]{5L,6L,7L,8L});
    dbSyncInfoFlowDO1.setStatus(0);
    dbSyncInfoFlowDO1.setCreateTime(new Date().getTime());
    dbSyncInfoFlowDO1.setIsDeleted(0);
    dbSyncInfoFlowDO1.setVersion(1);
    dbSyncInfoFlowDO1.setApiNameEiMap("{\"biz_account\":[\"85145\"],\"org_employee_user\":[\"85145\"]}");
    dbSyncInfoFlowDO1.setLastModifiedTime(new Date().getTime());
    dbSyncInfoFlowDO1.setDbSyncId("64ee0f5dbfbe303944dd65b6");
    dbSyncInfoFlowDO1.setPartitionName("i");
    List<DbSyncInfoFlowDO> a = Lists.newArrayList(dbSyncInfoFlowDO,dbSyncInfoFlowDO1);
    int result = dbSyncInfoFlowDao.upsertDbSyncInfoFlow("*****************************************","public",a);
    System.out.println("update size==="+result);
  }

  @Test
  public  void testTableNames()throws Exception{
    List<String> tables = metaDataDao.findAllPgTableWithSmt("*****************************************","public");
    Set<String> biPgTables= Sets.newHashSet(tables);
    File aea = ResourceUtils.getFile("classpath:ods/tableNames.txt");
    String tableStr = Files.readString(aea.toPath());
    List<String> notHave= Splitter.on(",").omitEmptyStrings().splitToList(tableStr).stream().filter(table->!biPgTables.contains(table)).toList();
    System.out.println(JSON.toJSONString(notHave));
  }

  @Test
  public void testUpdateStatus(){
    List<String> ids= Lists.newArrayList("1111");
    int status= 4;
    List<Integer> casStatus = Lists.newArrayList(3,-1,-2);
    int result = pgCommonDao.updateDbSyncInfoByIdStatusCAS(status, ids, casStatus);
    System.out.println("result==========="+result);
  }
  @Test
  public void testUpdateStatus2(){
   int size= pgCommonDao.updateTopologyStatusByChDbUrl(-2, "aaaaa");
    System.out.println(size);
  }
  @Test
  public void testRest(){
//    pgCommonDao.resetCHDbSyncInfo("aaaa");
//    List<DBSyncInfo> a= biSystemMapper.setTenantId("-1").queryDBSyncInfoByChDB("***************************************************");
//    System.out.println("queryDBSyncInfoByChDB size:"+a.size());
   int result= biSystemMapper.setTenantId("-1").resetCHDbTableSyncInfo("6492e157400f5929c5bd1cd7");
    System.out.println("resetCHDbTableSyncInfo size :"+result);
  }

  @Test
  public void testResetMaxModifiedTime(){
    ResetMaxModifiedTimeArg resetMaxModifiedTimeArg=new ResetMaxModifiedTimeArg();
    resetMaxModifiedTimeArg.setDbSyncId("6412b13f5cd44942982c91b7");
//    resetMaxModifiedTimeArg.setTables(Lists.newArrayList("dt_auth_simple"));
    resetMaxModifiedTimeArg.setMaxModifiedTime(1707116460000L);
    pgCommonDao.updateDbTableMaxModifiedTime(resetMaxModifiedTimeArg);
  }
  @Test
  public void testQuery2Map(){
    Map<String, DbTableSyncInfoDO> a= pgCommonDao.queryDbTableSyncInfoMap("db_001");
//    System.out.println(JSON.toJSONString(a));
    List< DbTableSyncInfoDO > dbTableSyncInfos= a.values().stream().peek(b-> b.setLastSyncTime(new Date().getTime())).toList();
//    null;
    pgCommonDao.batchUpdateDbTableSyncInfo(dbTableSyncInfos);
  }

  @Test
  public void allowIncPartition() {
    int status = 1;
    List<String> ids = Lists.newArrayList("64ee0f5dbfbe303944dd65b6");
    String partitionName = WarehouseConfig.PAAS2BI_FLAG;
    int a= pgCommonDao.allowIncPartition(status, ids, partitionName);
    System.out.println(a);
  }

  @Test
  public void testQueryDBSyncInfo() {
    // Given
    String chDB = "***************************************************";
    String pgDB = "***********************************************";
    String pgSchema = "sch_82958";

    // When
    DBSyncInfo result = pgCommonDao.queryDBSyncInfo(chDB, pgDB, pgSchema);

    // Then
    assertNotNull(result);
    assertEquals(chDB, result.getChDb());
    assertEquals(pgDB, result.getPgDb());
    assertEquals(pgSchema, result.getPgSchema());
    
    // Log the result for debugging
    log.info("QueryDBSyncInfo result: {}", JSON.toJSONString(result));
  }
}
