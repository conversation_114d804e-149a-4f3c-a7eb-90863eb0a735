package com.fxiaoke.bi.warehouse.dws.model;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.metadata.context.dto.dw.RptViewDwContext;
import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.dws.service.StatTopologyService;
import com.fxiaoke.bi.warehouse.dws.transform.db.dao.RptRuleDao;
import com.fxiaoke.bi.warehouse.dws.transform.impl.NewRptTopologyTransformer;
import com.fxiaoke.bi.warehouse.dws.transform.model.BiMtRule;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/4
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class RptViewTest {

  @Resource
  NewRptTopologyTransformer newRptTopologyTransformer;
  @Resource
  private RptRuleDao rptRuleDao;
  @Resource
  StatTopologyService statTopologyService;

  @Test
  public void createRptViewSql() {
    String tenantId = "79682";
//    String viewId = "BI_668e559a5a381c0001573cfd";
    String viewId = "BI_683ece6efb2ba50001efadd9";
//    String viewId = "BI_6668123f9081c20001153297";
//    String viewId = "BI_664f2e6a10a9550001b1fd36";
    int source = 3;
    String timeZone = "Asia/Shanghai";
    BiMtRule biMtRule = rptRuleDao.createRptViewMtRule(tenantId, viewId, source);
    TopologyTable topologyTable = newRptTopologyTransformer.createTopologyTable(tenantId, viewId, source, timeZone, biMtRule, null);
    Map<String, List<String>> tableKeys = Maps.newHashMap();
    tableKeys.put("object_data", Arrays.asList("tenant_id","object_describe_api_name","bi_sys_flag","id"));
    String viewSql = topologyTable.toViewSQL(-1, -1, null, tableKeys);
    System.out.println(viewSql);
  }

  @Test
  public void saveRptTopologyTable() throws Exception {
    String tenantId = "85145";
    //    String viewId = "BI_664eebb524219b000188f69a";
    //    String viewId = "BI_664f2e6a10a9550001b1fd36";
    String viewId = "BI_668e57fa5a381c0001573dc6";
    int source = 3;
    newRptTopologyTransformer.doCreateRptTopology(tenantId, viewId, source);
  }

  @Test
  public void testCreatefsbidb044003002() throws IOException {
    File file = ResourceUtils.getFile("classpath:stat/RptView_1.json");
    String json = Files.readString(file.toPath());
    RptViewDwContext rptViewDwContext= JSON.parseObject(json, RptViewDwContext.class);
    StatViewPreArg rptViewPreArg= new StatViewPreArg();
    rptViewPreArg.setRptViewDwContext(rptViewDwContext);
    rptViewPreArg.setTenantId(rptViewDwContext.getTenantId());
    rptViewPreArg.setTimeZone("Asia/Shanghai");
    rptViewPreArg.setSourceType(3);
    rptViewPreArg.setSourceId(rptViewDwContext.getBiMtTopologyDescribeDO().sourceId);
    rptViewPreArg.setGoalRuleActionDateMtDetailId("BI_6751270534766b35545557c4");
    StatViewPreSQL statViewPreSQL= statTopologyService.createStatViewPreSQL(rptViewPreArg);
    System.out.println(statViewPreSQL.getPreViewSQL());
  }

  @Test
  public void testCreatePreViewSQL2() throws IOException {
    File file = ResourceUtils.getFile("classpath:stat/RptView_1.json");
    String json = Files.readString(file.toPath());
    StatViewPreArg rptViewPreArg = JSON.parseObject(json, StatViewPreArg.class);
    rptViewPreArg.setGoalDetailMatchLevel(0);
    StatViewPreSQL statViewPreSQL= statTopologyService.createStatViewPreSQL(rptViewPreArg);
    System.out.println(statViewPreSQL.getPreViewSQL());
  }




}