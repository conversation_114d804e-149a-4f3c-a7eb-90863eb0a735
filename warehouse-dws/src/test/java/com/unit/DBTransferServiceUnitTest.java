package com.unit;

import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.service.DBTransferService;
import com.fxiaoke.bi.warehouse.ods.service.PGMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.CHNodeService;
import com.fxiaoke.bi.warehouse.ods.service.CHClientService;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.fxiaoke.bi.warehouse.ods.service.MergeTaskService;
import com.fxiaoke.bi.warehouse.ods.service.DbTableSyncInfoService;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.IntegrateServiceImpl;
import com.fxiaoke.bi.warehouse.ods.integrate.service.CHDataToCHService;
import com.fxiaoke.bi.warehouse.common.component.ClickHouseUtilService;
import com.fxiaoke.bi.warehouse.common.db.dao.DbSyncInfoFlowDao;
import com.fxiaoke.bi.warehouse.core.db.AggMergeDao;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.ods.dao.UdfObjFieldDao;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.facishare.converter.EIEAConverter;
import com.github.jedis.support.JedisCmd;
import com.github.jedis.lock.JedisLock;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DBTransferService 单元测试类
 * 专门针对 doTransfer2CH 方法的测试覆盖率
 */
@Slf4j
public class DBTransferServiceUnitTest {

    @InjectMocks
    private DBTransferService dbTransferService;

    @Mock
    private PGMetadataService pgMetadataService;

    @Mock
    private PgCommonDao pgCommonDao;

    @Mock
    private CHMetadataService chMetadataService;

    @Mock
    private CHNodeService chNodeService;

    @Mock
    private CHClientService chClientService;

    @Mock
    private CHDBService chdbService;

    @Mock
    private MergeTaskService mergeTaskService;

    @Mock
    private DbTableSyncInfoService dbTableSyncInfoService;

    @Mock
    private IntegrateServiceImpl integrateService;

    @Mock
    private CHDataToCHService chDataToCHService;

    @Mock
    private ClickHouseUtilService clickHouseUtilService;

    @Mock
    private DbSyncInfoFlowDao dbSyncInfoFlowDao;

    @Mock
    private AggMergeDao aggMergeDao;

    @Mock
    private PgDataSource pgDataSource;

    @Mock
    private UdfObjFieldDao udfObjFieldDao;

    @Mock
    private CHRouterPolicy chRouterPolicy;

    @Mock
    private EIEAConverter eieaConverter;

    @Mock
    private JedisCmd jedisCmd;

    @Mock
    private JedisLock jedisLock;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置DBTransferService的私有字段
        ReflectionTestUtils.setField(dbTransferService, "pgCommonDao", pgCommonDao);
        ReflectionTestUtils.setField(dbTransferService, "pgMetadataService", pgMetadataService);
        ReflectionTestUtils.setField(dbTransferService, "chMetadataService", chMetadataService);
        ReflectionTestUtils.setField(dbTransferService, "chNodeService", chNodeService);
        ReflectionTestUtils.setField(dbTransferService, "chClientService", chClientService);
        ReflectionTestUtils.setField(dbTransferService, "chdbService", chdbService);
        ReflectionTestUtils.setField(dbTransferService, "mergeTaskService", mergeTaskService);
        ReflectionTestUtils.setField(dbTransferService, "dbTableSyncInfoService", dbTableSyncInfoService);
        ReflectionTestUtils.setField(dbTransferService, "integrateService", integrateService);
        ReflectionTestUtils.setField(dbTransferService, "chDataToCHService", chDataToCHService);
        ReflectionTestUtils.setField(dbTransferService, "clickHouseUtilService", clickHouseUtilService);
        ReflectionTestUtils.setField(dbTransferService, "dbSyncInfoFlowDao", dbSyncInfoFlowDao);
        ReflectionTestUtils.setField(dbTransferService, "aggMergeDao", aggMergeDao);
        ReflectionTestUtils.setField(dbTransferService, "pgDataSource", pgDataSource);
        ReflectionTestUtils.setField(dbTransferService, "udfObjFieldDao", udfObjFieldDao);
        ReflectionTestUtils.setField(dbTransferService, "chRouterPolicy", chRouterPolicy);
        ReflectionTestUtils.setField(dbTransferService, "eieaConverter", eieaConverter);
        ReflectionTestUtils.setField(dbTransferService, "jedisCmd", jedisCmd);
        
        // 设置一些默认配置值
        ReflectionTestUtils.setField(dbTransferService, "batchSize", 800);
        ReflectionTestUtils.setField(dbTransferService, "savePointSize", 100000);
        ReflectionTestUtils.setField(dbTransferService, "batchQueryBeforeSize", 500);
        ReflectionTestUtils.setField(dbTransferService, "syncDelayThreshold", 1800000L); // 30分钟
        ReflectionTestUtils.setField(dbTransferService, "waitForCalDelayThreshold", 900000L); // 15分钟
        ReflectionTestUtils.setField(dbTransferService, "sendCalHashSalt", 1);
    }

    /**
     * 测试doTransfer2CH - 正常流程，状态为SYNC_ABLE
     */
    @Test
    public void testDoTransfer2CH_NormalFlow_SyncAble() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ABLE);

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // Mock 更新状态操作 - 这些是需要Mock的数据库变更操作
        when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);
        when(pgCommonDao.updateDbSyncInfo(any(DBSyncInfo.class))).thenReturn(1);
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());

        // Mock doTranslate方法，避免执行复杂的同步逻辑
        DBTransferService spyService = spy(dbTransferService);
        doNothing().when(spyService).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        // 执行测试
        spyService.doTransfer2CH(transferEvent);

        // 验证关键方法调用
        verify(pgCommonDao, times(1)).queryDBSyncInfo(transferEvent.getChDbURL(), transferEvent.getDbURL(), transferEvent.getSchema());
        verify(pgCommonDao, times(1)).updateDBSyncInfoStatus(any(DBSyncInfo.class));
        verify(spyService, times(1)).doTranslate(any(AtomicLong.class), eq(transferEvent), eq(dbSyncInfo), anyLong());

        log.info("testDoTransfer2CH_NormalFlow_SyncAble 测试通过");
    }

    /**
     * 测试doTransfer2CH - 状态为AGG_ING，等待计算
     */
    @Test
    public void testDoTransfer2CH_AggIng_WaitingForCalculation() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ING);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis() - 600000L); // 10分钟前

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // 执行测试
        DBTransferService spyService = spy(dbTransferService);
        spyService.doTransfer2CH(transferEvent);

        // 验证不会执行doTranslate
        verify(spyService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());
        
        // 验证查询了同步信息
        verify(pgCommonDao, times(1)).queryDBSyncInfo(transferEvent.getChDbURL(), transferEvent.getDbURL(), transferEvent.getSchema());

        log.info("testDoTransfer2CH_AggIng_WaitingForCalculation 测试通过");
    }

    /**
     * 测试doTransfer2CH - 状态为AGG_ING，超时需要重发计算消息
     */
    @Test
    public void testDoTransfer2CH_AggIng_TimeoutResendMessage() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ING);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis() - 1000000L); // 超过等待阈值

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // Mock 发送计算消息 - 这是需要Mock的数据库变更操作
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());

        // 执行测试
        DBTransferService spyService = spy(dbTransferService);
        spyService.doTransfer2CH(transferEvent);

        // 验证发送了计算消息
        verify(pgCommonDao, times(1)).sendCalculateEvent(eq(dbSyncInfo), anyInt(), isNull());
        
        // 验证不会执行doTranslate
        verify(spyService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        log.info("testDoTransfer2CH_AggIng_TimeoutResendMessage 测试通过");
    }

    /**
     * 测试doTransfer2CH - 找不到同步信息
     */
    @Test
    public void testDoTransfer2CH_DBSyncInfoNotFound() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息 - 返回null
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(null);

        // 执行测试
        DBTransferService spyService = spy(dbTransferService);
        spyService.doTransfer2CH(transferEvent);

        // 验证查询了同步信息
        verify(pgCommonDao, times(1)).queryDBSyncInfo(transferEvent.getChDbURL(), transferEvent.getDbURL(), transferEvent.getSchema());
        
        // 验证不会执行doTranslate
        verify(spyService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        log.info("testDoTransfer2CH_DBSyncInfoNotFound 测试通过");
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的TransferEvent
     */
    private TransferEvent createTransferEvent() {
        TransferEvent transferEvent = new TransferEvent();
        transferEvent.setChDbURL("***************************************************");
        transferEvent.setDbURL("*****************************************");
        transferEvent.setSchema("public");
        return transferEvent;
    }

    /**
     * 创建测试用的DBSyncInfo
     */
    private DBSyncInfo createDBSyncInfo(SyncStatusEnum status) {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setPgDb("*****************************************");
        dbSyncInfo.setPgSchema("public");
        dbSyncInfo.setChDb("***************************************************");
        dbSyncInfo.setStatus(status.getStatus());
        dbSyncInfo.setBatchNum(1000L);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());
        dbSyncInfo.setLastSyncTime(System.currentTimeMillis());
        dbSyncInfo.setCreateTime(System.currentTimeMillis());
        dbSyncInfo.setIsDeleted(0);
        dbSyncInfo.setAllowIncPartition(0);
        dbSyncInfo.setAllowPaas2biStatus(0);
        return dbSyncInfo;
    }

    /**
     * 测试shouldWait4IncrementMergeMergeAggData - 正常情况返回true
     */
    @Test
    public void testShouldWait4IncrementMergeMergeAggData_ReturnTrue() {
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ED);
        
        // Mock CAS更新返回1，表示更新成功
        when(pgCommonDao.updateDbSyncInfoByIdStatusCAS(anyInt(), anyList(), anyList())).thenReturn(1);

        boolean result = dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo);

        Assert.assertTrue("应该返回true，表示需要等待merge", result);
        verify(pgCommonDao, times(1)).updateDbSyncInfoByIdStatusCAS(
            eq(SyncStatusEnum.EXCHANGE_AGG.getStatus()),
            eq(Lists.newArrayList(dbSyncInfo.getId())),
            anyList()
        );

        log.info("testShouldWait4IncrementMergeMergeAggData_ReturnTrue 测试通过");
    }

    /**
     * 测试createCHNodeInfo - 正常情况
     */
    @Test
    public void testCreateCHNodeInfo_Success() {
        String dbSyncId = "6412b13f5cd44942982c91b7";
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ABLE);
        
        when(pgCommonDao.queryDBSyncInfoById(anyList())).thenReturn(Lists.newArrayList(dbSyncInfo));

        var result = dbTransferService.createCHNodeInfo(dbSyncId);

        Assert.assertNotNull("应该返回ClickhouseNodeInfo对象", result);
        verify(pgCommonDao, times(1)).queryDBSyncInfoById(eq(Lists.newArrayList(dbSyncId)));

        log.info("testCreateCHNodeInfo_Success 测试通过");
    }
}
