package com.unit;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.compare.util.CompareUtil;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.service.DBTransferService;
import com.fxiaoke.bi.warehouse.ods.service.PGMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.CHNodeService;
import com.fxiaoke.bi.warehouse.ods.service.CHClientService;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.fxiaoke.bi.warehouse.ods.service.MergeTaskService;
import com.fxiaoke.bi.warehouse.ods.service.DbTableSyncInfoService;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.IntegrateServiceImpl;
import com.fxiaoke.bi.warehouse.ods.integrate.service.CHDataToCHService;
import com.fxiaoke.bi.warehouse.common.component.ClickHouseUtilService;
import com.fxiaoke.bi.warehouse.common.db.dao.DbSyncInfoFlowDao;
import com.fxiaoke.bi.warehouse.core.db.AggMergeDao;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.ods.dao.UdfObjFieldDao;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.facishare.converter.EIEAConverter;
import com.github.jedis.support.JedisCmd;
import com.github.jedis.support.MergeJedisCmd;
import com.github.jedis.lock.JedisLock;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class UnitTest {

    @SpyBean
    private DBTransferService dbTransferService;

    @MockBean
    private PGMetadataService pgMetadataService;

    @MockBean
    private PgCommonDao pgCommonDao;

    @MockBean
    private CHMetadataService chMetadataService;

    @MockBean
    private CHNodeService chNodeService;

    @MockBean
    private CHClientService chClientService;

    @MockBean
    private CHDBService chdbService;

    @MockBean
    private MergeTaskService mergeTaskService;

    @MockBean
    private DbTableSyncInfoService dbTableSyncInfoService;

    @MockBean
    private IntegrateServiceImpl integrateService;

    @MockBean
    private CHDataToCHService chDataToCHService;

    @MockBean
    private ClickHouseUtilService clickHouseUtilService;

    @MockBean
    private DbSyncInfoFlowDao dbSyncInfoFlowDao;

    @MockBean
    private AggMergeDao aggMergeDao;

    @MockBean
    private PgDataSource pgDataSource;

    @MockBean
    private UdfObjFieldDao udfObjFieldDao;

    @MockBean
    private CHRouterPolicy chRouterPolicy;

    @MockBean
    private EIEAConverter eieaConverter;

    @MockBean(name = "jedisFactory")
    private JedisCmd jedisCmd;

    @MockBean
    private JedisLock jedisLock;

    @BeforeEach
    public void setUp() {
        // 设置DBTransferService的私有字段，模拟@PostConstruct初始化
        ReflectionTestUtils.setField(dbTransferService, "pgCommonDao", pgCommonDao);
        ReflectionTestUtils.setField(dbTransferService, "pgMetadataService", pgMetadataService);
        ReflectionTestUtils.setField(dbTransferService, "chMetadataService", chMetadataService);
        ReflectionTestUtils.setField(dbTransferService, "chNodeService", chNodeService);
        ReflectionTestUtils.setField(dbTransferService, "chClientService", chClientService);
        ReflectionTestUtils.setField(dbTransferService, "chdbService", chdbService);
        ReflectionTestUtils.setField(dbTransferService, "mergeTaskService", mergeTaskService);
        ReflectionTestUtils.setField(dbTransferService, "dbTableSyncInfoService", dbTableSyncInfoService);
        ReflectionTestUtils.setField(dbTransferService, "integrateService", integrateService);
        ReflectionTestUtils.setField(dbTransferService, "chDataToCHService", chDataToCHService);
        ReflectionTestUtils.setField(dbTransferService, "clickHouseUtilService", clickHouseUtilService);
        ReflectionTestUtils.setField(dbTransferService, "dbSyncInfoFlowDao", dbSyncInfoFlowDao);
        ReflectionTestUtils.setField(dbTransferService, "aggMergeDao", aggMergeDao);
        ReflectionTestUtils.setField(dbTransferService, "pgDataSource", pgDataSource);
        ReflectionTestUtils.setField(dbTransferService, "udfObjFieldDao", udfObjFieldDao);
        ReflectionTestUtils.setField(dbTransferService, "chRouterPolicy", chRouterPolicy);
        ReflectionTestUtils.setField(dbTransferService, "eieaConverter", eieaConverter);
        ReflectionTestUtils.setField(dbTransferService, "jedisCmd", jedisCmd);

        // 设置一些默认配置值
        ReflectionTestUtils.setField(dbTransferService, "batchSize", 800);
        ReflectionTestUtils.setField(dbTransferService, "savePointSize", 100000);
        ReflectionTestUtils.setField(dbTransferService, "batchQueryBeforeSize", 500);
        ReflectionTestUtils.setField(dbTransferService, "syncDelayThreshold", 1800000L); // 30分钟
        ReflectionTestUtils.setField(dbTransferService, "waitForCalDelayThreshold", 900000L); // 15分钟
        ReflectionTestUtils.setField(dbTransferService, "sendCalHashSalt", 1);
    }

    @Test
    public void testClickhouseDataDao() {
        boolean approximatelyEqual = CompareUtil.isApproximatelyEqual(1000L, 1213L, 10);
        System.out.println(approximatelyEqual);
    }

    @Test
    public void testMockito() {
        TransferEvent transferEvent = new TransferEvent("***************************************************", "*****************************************", "public");
        when(pgMetadataService.findValidaTenantId(transferEvent)).thenReturn(Lists.newArrayList("1", "2", "3"));
        List<String> validaTenantId = pgMetadataService.findValidaTenantId(transferEvent);
        Assert.assertArrayEquals(new String[]{"1", "2", "3"}, validaTenantId.toArray());
    }

    @Test
    public void testDoTranslate_SimpleCase() {
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        TransferEvent transferEvent = new TransferEvent();
        transferEvent.setChDbURL("***************************************************");
        transferEvent.setDbURL("*****************************************");
        transferEvent.setSchema("public");

        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setPgDb("*****************************************");
        dbSyncInfo.setPgSchema("public");
        dbSyncInfo.setChDb("***************************************************");
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
        dbSyncInfo.setBatchNum(1000L);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());
        dbSyncInfo.setLastSyncTime(System.currentTimeMillis());

        long startTime = System.currentTimeMillis();
        doNothing().when(dbTransferService).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());
        dbTransferService.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, startTime);
        verify(dbTransferService, times(1)).doTranslate(eq(nextBatchNum), eq(transferEvent), eq(dbSyncInfo), eq(startTime));

        log.info("testDoTranslate_SimpleCase 测试通过");
    }

    @Test
    public void testShouldWait4IncrementMergeMergeAggData() {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setPgDb("*****************************************");
        dbSyncInfo.setChDb("***************************************************");
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
        when(dbTransferService.shouldWait4IncrementMergeMergeAggData(any(DBSyncInfo.class))).thenReturn(false);
        boolean result = dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo);
        Assert.assertFalse("应该返回false，表示不需要等待", result);
        verify(dbTransferService, times(1)).shouldWait4IncrementMergeMergeAggData(eq(dbSyncInfo));

        log.info("testShouldWait4IncrementMergeMergeAggData 测试通过");
    }

    @Test
    public void testQueryDBSyncInfo() {
        String chDbURL = "***************************************************";
        String dbURL = "*****************************************";
        String schema = "public";

        DBSyncInfo expectedSyncInfo = new DBSyncInfo();
        expectedSyncInfo.setId("6412b13f5cd44942982c91b7");
        expectedSyncInfo.setPgDb(dbURL);
        expectedSyncInfo.setPgSchema(schema);
        expectedSyncInfo.setChDb(chDbURL);
        expectedSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());

        when(pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema)).thenReturn(expectedSyncInfo);

        DBSyncInfo actualSyncInfo = pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema);

        Assert.assertNotNull("查询结果不应为空", actualSyncInfo);
        Assert.assertEquals("同步信息ID应该匹配", expectedSyncInfo.getId(), actualSyncInfo.getId());
        Assert.assertEquals("PG数据库URL应该匹配", expectedSyncInfo.getPgDb(), actualSyncInfo.getPgDb());
        Assert.assertEquals("CH数据库URL应该匹配", expectedSyncInfo.getChDb(), actualSyncInfo.getChDb());

        verify(pgCommonDao, times(1)).queryDBSyncInfo(eq(chDbURL), eq(dbURL), eq(schema));

        log.info("testQueryDBSyncInfo 测试通过");
    }

    @Test
    public void testQueryDBSyncInfo_NotFound() {
        String chDbURL = "***************************************************";
        String dbURL = "*****************************************";
        String schema = "public";

        when(pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema)).thenReturn(null);

        DBSyncInfo actualSyncInfo = pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema);

        Assert.assertNull("查询结果应为空", actualSyncInfo);
        verify(pgCommonDao, times(1)).queryDBSyncInfo(eq(chDbURL), eq(dbURL), eq(schema));

        log.info("testQueryDBSyncInfo_NotFound 测试通过");
    }

    @Test
    public void testUpdateDBSyncInfoStatus() {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ING.getStatus());
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());

        when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);

        pgCommonDao.updateDBSyncInfoStatus(dbSyncInfo);

        verify(pgCommonDao, times(1)).updateDBSyncInfoStatus(eq(dbSyncInfo));
        log.info("testUpdateDBSyncInfoStatus 测试通过");
    }

    // ==================== doTransfer2CH 核心测试用例 ====================

    /**
     * 测试doTransfer2CH - 正常流程，状态为SYNC_ABLE
     */
    @Test
    public void testDoTransfer2CH_NormalFlow_SyncAble() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ABLE);

        // Mock JedisLock - 使用构造函数创建，然后Mock tryLock方法
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // Mock 更新状态操作 - 这些是需要Mock的数据库变更操作
        when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);
        when(pgCommonDao.updateDbSyncInfo(any(DBSyncInfo.class))).thenReturn(1);
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());

        // Mock doTranslate方法，避免执行复杂的同步逻辑
        doNothing().when(dbTransferService).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证关键方法调用
        verify(pgCommonDao, times(1)).queryDBSyncInfo(transferEvent.getChDbURL(), transferEvent.getDbURL(), transferEvent.getSchema());
        verify(pgCommonDao, times(1)).updateDBSyncInfoStatus(any(DBSyncInfo.class));
        verify(dbTransferService, times(1)).doTranslate(any(AtomicLong.class), eq(transferEvent), eq(dbSyncInfo), anyLong());

        log.info("testDoTransfer2CH_NormalFlow_SyncAble 测试通过");
    }

    /**
     * 测试doTransfer2CH - 状态为AGG_ING，等待计算
     */
    @Test
    public void testDoTransfer2CH_AggIng_WaitingForCalculation() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ING);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis() - 600000L); // 10分钟前

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证不会执行doTranslate
        verify(dbTransferService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        // 验证查询了同步信息
        verify(pgCommonDao, times(1)).queryDBSyncInfo(transferEvent.getChDbURL(), transferEvent.getDbURL(), transferEvent.getSchema());

        log.info("testDoTransfer2CH_AggIng_WaitingForCalculation 测试通过");
    }

    /**
     * 测试doTransfer2CH - 状态为AGG_ING，超时需要重发计算消息
     */
    @Test
    public void testDoTransfer2CH_AggIng_TimeoutResendMessage() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ING);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis() - 1000000L); // 超过等待阈值

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // Mock 发送计算消息 - 这是需要Mock的数据库变更操作
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证发送了计算消息
        verify(pgCommonDao, times(1)).sendCalculateEvent(eq(dbSyncInfo), anyInt(), isNull());

        // 验证不会执行doTranslate
        verify(dbTransferService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        log.info("testDoTransfer2CH_AggIng_TimeoutResendMessage 测试通过");
    }

    /**
     * 测试doTransfer2CH - 状态为SYNC_ED，等待计算
     */
    @Test
    public void testDoTransfer2CH_SyncEd_WaitingForCalculation() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ED);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis() - 600000L); // 10分钟前

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证不会执行doTranslate
        verify(dbTransferService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        log.info("testDoTransfer2CH_SyncEd_WaitingForCalculation 测试通过");
    }

    /**
     * 测试doTransfer2CH - 状态为SYNC_ING，等待同步
     */
    @Test
    public void testDoTransfer2CH_SyncIng_WaitingForSync() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ING);
        dbSyncInfo.setLastSyncTime(System.currentTimeMillis() - 600000L); // 10分钟前

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证不会执行doTranslate
        verify(dbTransferService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        log.info("testDoTransfer2CH_SyncIng_WaitingForSync 测试通过");
    }

    /**
     * 测试doTransfer2CH - 无法获取Redis锁
     */
    @Test
    public void testDoTransfer2CH_CannotAcquireLock() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();

        // Mock JedisLock - 无法获取锁
        when(jedisLock.tryLock()).thenReturn(false);

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证没有查询同步信息
        verify(pgCommonDao, never()).queryDBSyncInfo(anyString(), anyString(), anyString());

        // 验证不会执行doTranslate
        verify(dbTransferService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        log.info("testDoTransfer2CH_CannotAcquireLock 测试通过");
    }

    /**
     * 测试doTransfer2CH - 找不到同步信息
     */
    @Test
    public void testDoTransfer2CH_DBSyncInfoNotFound() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息 - 返回null
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(null);

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证查询了同步信息
        verify(pgCommonDao, times(1)).queryDBSyncInfo(transferEvent.getChDbURL(), transferEvent.getDbURL(), transferEvent.getSchema());

        // 验证不会执行doTranslate
        verify(dbTransferService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        log.info("testDoTransfer2CH_DBSyncInfoNotFound 测试通过");
    }

    /**
     * 测试doTransfer2CH - 异常处理
     */
    @Test
    public void testDoTransfer2CH_ExceptionHandling() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ABLE);

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // Mock 更新状态操作
        when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);

        // Mock doTranslate抛出异常
        doThrow(new RuntimeException("模拟同步异常")).when(dbTransferService).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        // Mock 异常处理中的状态更新 - 这是需要Mock的数据库变更操作
        when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);

        // 执行测试，期望抛出异常
        try {
            dbTransferService.doTransfer2CH(transferEvent);
            Assert.fail("应该抛出RuntimeException");
        } catch (RuntimeException e) {
            Assert.assertEquals("模拟同步异常", e.getMessage());
        }

        // 验证异常处理逻辑
        verify(pgCommonDao, atLeast(1)).updateDBSyncInfoStatus(any(DBSyncInfo.class));

        log.info("testDoTransfer2CH_ExceptionHandling 测试通过");
    }

    /**
     * 测试doTransfer2CH - 状态为EXCHANGE_AGG
     */
    @Test
    public void testDoTransfer2CH_ExchangeAgg() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.EXCHANGE_AGG);

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // Mock dealWithExchangeStatus方法
        doNothing().when(dbTransferService).dealWithExchangeStatus(anyString(), anyString(), anyString(), anyString(), any());

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证调用了dealWithExchangeStatus
        verify(dbTransferService, times(1)).dealWithExchangeStatus(
            eq(dbSyncInfo.getId()),
            eq(dbSyncInfo.getPgDb()),
            eq(dbSyncInfo.getPgSchema()),
            eq(dbSyncInfo.getChDb()),
            eq(dbSyncInfo.getAllowIncPartition())
        );

        // 验证不会执行doTranslate
        verify(dbTransferService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        log.info("testDoTransfer2CH_ExchangeAgg 测试通过");
    }

    /**
     * 测试doTransfer2CH - 状态为AGG_ED，需要等待增量merge
     */
    @Test
    public void testDoTransfer2CH_AggEd_ShouldWaitForMerge() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ED);

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // Mock shouldWait4IncrementMergeMergeAggData返回true，表示需要等待
        when(dbTransferService.shouldWait4IncrementMergeMergeAggData(any(DBSyncInfo.class))).thenReturn(true);

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证调用了shouldWait4IncrementMergeMergeAggData
        verify(dbTransferService, times(1)).shouldWait4IncrementMergeMergeAggData(eq(dbSyncInfo));

        // 验证不会执行doTranslate
        verify(dbTransferService, never()).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        log.info("testDoTransfer2CH_AggEd_ShouldWaitForMerge 测试通过");
    }

    /**
     * 测试doTransfer2CH - 状态为SYNC_ERROR，可以继续同步
     */
    @Test
    public void testDoTransfer2CH_SyncError_CanContinue() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ERROR);

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // Mock shouldWait4IncrementMergeMergeAggData返回false，表示不需要等待
        when(dbTransferService.shouldWait4IncrementMergeMergeAggData(any(DBSyncInfo.class))).thenReturn(false);

        // Mock 更新状态操作 - 这些是需要Mock的数据库变更操作
        when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);
        when(pgCommonDao.updateDbSyncInfo(any(DBSyncInfo.class))).thenReturn(1);
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());

        // Mock doTranslate方法
        doNothing().when(dbTransferService).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证更新了状态为SYNC_ING
        verify(pgCommonDao, times(1)).updateDBSyncInfoStatus(argThat(info ->
            info.getStatus().equals(SyncStatusEnum.SYNC_ING.getStatus())
        ));

        // 验证执行了doTranslate
        verify(dbTransferService, times(1)).doTranslate(any(AtomicLong.class), eq(transferEvent), eq(dbSyncInfo), anyLong());

        log.info("testDoTransfer2CH_SyncError_CanContinue 测试通过");
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的TransferEvent
     */
    private TransferEvent createTransferEvent() {
        TransferEvent transferEvent = new TransferEvent();
        transferEvent.setChDbURL("***************************************************");
        transferEvent.setDbURL("*****************************************");
        transferEvent.setSchema("public");
        return transferEvent;
    }

    /**
     * 创建测试用的DBSyncInfo
     */
    private DBSyncInfo createDBSyncInfo(SyncStatusEnum status) {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setPgDb("*****************************************");
        dbSyncInfo.setPgSchema("public");
        dbSyncInfo.setChDb("***************************************************");
        dbSyncInfo.setStatus(status.getStatus());
        dbSyncInfo.setBatchNum(1000L);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());
        dbSyncInfo.setLastSyncTime(System.currentTimeMillis());
        dbSyncInfo.setCreateTime(System.currentTimeMillis());
        dbSyncInfo.setIsDeleted(0);
        dbSyncInfo.setAllowIncPartition(0);
        dbSyncInfo.setAllowPaas2biStatus(0);
        return dbSyncInfo;
    }

    // ==================== 边界条件和特殊场景测试 ====================

    /**
     * 测试shouldWait4IncrementMergeMergeAggData - 正常情况返回true
     */
    @Test
    public void testShouldWait4IncrementMergeMergeAggData_ReturnTrue() {
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ED);

        // Mock CAS更新返回1，表示更新成功
        when(pgCommonDao.updateDbSyncInfoByIdStatusCAS(anyInt(), anyList(), anyList())).thenReturn(1);

        boolean result = dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo);

        Assert.assertTrue("应该返回true，表示需要等待merge", result);
        verify(pgCommonDao, times(1)).updateDbSyncInfoByIdStatusCAS(
            eq(SyncStatusEnum.EXCHANGE_AGG.getStatus()),
            eq(Lists.newArrayList(dbSyncInfo.getId())),
            anyList()
        );

        log.info("testShouldWait4IncrementMergeMergeAggData_ReturnTrue 测试通过");
    }

    /**
     * 测试shouldWait4IncrementMergeMergeAggData - CAS更新失败返回true
     */
    @Test
    public void testShouldWait4IncrementMergeMergeAggData_CASFailed() {
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ED);

        // Mock CAS更新返回0，表示更新失败
        when(pgCommonDao.updateDbSyncInfoByIdStatusCAS(anyInt(), anyList(), anyList())).thenReturn(0);

        boolean result = dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo);

        Assert.assertTrue("即使CAS失败也应该返回true", result);
        verify(pgCommonDao, times(1)).updateDbSyncInfoByIdStatusCAS(anyInt(), anyList(), anyList());

        log.info("testShouldWait4IncrementMergeMergeAggData_CASFailed 测试通过");
    }

    /**
     * 测试createCHNodeInfo - 正常情况
     */
    @Test
    public void testCreateCHNodeInfo_Success() {
        String dbSyncId = "6412b13f5cd44942982c91b7";
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ABLE);

        when(pgCommonDao.queryDBSyncInfoById(anyList())).thenReturn(Lists.newArrayList(dbSyncInfo));

        var result = dbTransferService.createCHNodeInfo(dbSyncId);

        Assert.assertNotNull("应该返回ClickhouseNodeInfo对象", result);
        verify(pgCommonDao, times(1)).queryDBSyncInfoById(eq(Lists.newArrayList(dbSyncId)));

        log.info("testCreateCHNodeInfo_Success 测试通过");
    }

    /**
     * 测试createCHNodeInfo - 找不到同步信息
     */
    @Test
    public void testCreateCHNodeInfo_NotFound() {
        String dbSyncId = "nonexistent";

        when(pgCommonDao.queryDBSyncInfoById(anyList())).thenReturn(Lists.newArrayList());

        var result = dbTransferService.createCHNodeInfo(dbSyncId);

        Assert.assertNull("应该返回null", result);
        verify(pgCommonDao, times(1)).queryDBSyncInfoById(eq(Lists.newArrayList(dbSyncId)));

        log.info("testCreateCHNodeInfo_NotFound 测试通过");
    }

    /**
     * 测试doTransfer2CH - 灰度控制停止传输
     */
    @Test
    public void testDoTransfer2CH_GrayControlStopTransfer() {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();

        // 这个测试需要模拟GrayManager.isAllowByRule返回true的情况
        // 由于GrayManager是静态方法，这里主要测试方法的调用流程

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 由于灰度控制的逻辑，可能会提前返回，不会执行后续逻辑
        // 这里主要验证方法能正常执行而不抛出异常

        log.info("testDoTransfer2CH_GrayControlStopTransfer 测试通过");
    }

    /**
     * 测试doTransfer2CH - 多种状态组合测试
     */
    @Test
    public void testDoTransfer2CH_MultipleStatusCombinations() throws Exception {
        // 测试多种状态的组合情况
        SyncStatusEnum[] statuses = {
            SyncStatusEnum.SYNC_ABLE,
            SyncStatusEnum.AGG_ED,
            SyncStatusEnum.SYNC_ERROR
        };

        for (SyncStatusEnum status : statuses) {
            // 重置Mock
            reset(pgCommonDao, jedisLock, jedisCmd);

            TransferEvent transferEvent = createTransferEvent();
            DBSyncInfo dbSyncInfo = createDBSyncInfo(status);

            // Mock JedisLock
            when(jedisLock.tryLock()).thenReturn(true);

            // Mock 查询同步信息
            when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

            // Mock shouldWait4IncrementMergeMergeAggData返回false
            when(dbTransferService.shouldWait4IncrementMergeMergeAggData(any(DBSyncInfo.class))).thenReturn(false);

            // Mock 数据库变更操作
            when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);
            when(pgCommonDao.updateDbSyncInfo(any(DBSyncInfo.class))).thenReturn(1);
            doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());

            // Mock doTranslate方法
            doNothing().when(dbTransferService).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

            // 执行测试
            dbTransferService.doTransfer2CH(transferEvent);

            // 验证基本调用
            verify(pgCommonDao, times(1)).queryDBSyncInfo(anyString(), anyString(), anyString());

            log.info("testDoTransfer2CH_MultipleStatusCombinations 状态 {} 测试通过", status);
        }
    }

    /**
     * 测试doTransfer2CH - 并发场景模拟
     */
    @Test
    public void testDoTransfer2CH_ConcurrentScenario() throws Exception {
        // 准备测试数据
        TransferEvent transferEvent = createTransferEvent();
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ABLE);

        // Mock JedisLock - 模拟并发场景下的锁竞争
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(dbSyncInfo);

        // Mock 数据库变更操作
        when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);
        when(pgCommonDao.updateDbSyncInfo(any(DBSyncInfo.class))).thenReturn(1);
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());

        // Mock doTranslate方法
        doNothing().when(dbTransferService).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());

        // 执行测试
        dbTransferService.doTransfer2CH(transferEvent);

        // 验证锁的使用
        verify(jedisLock, times(1)).tryLock();

        log.info("testDoTransfer2CH_ConcurrentScenario 测试通过");
    }

    /**
     * 测试updateDbSyncInfo和updateDbSyncInfoFlow方法
     */
    @Test
    public void testUpdateDbSyncInfo() {
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ED);
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        List<String> tenantIdList = Lists.newArrayList("123", "456");

        // Mock 数据库变更操作
        when(pgCommonDao.updateDbSyncInfo(any(DBSyncInfo.class))).thenReturn(1);

        // 执行测试
        dbTransferService.updateDbSyncInfo(dbSyncInfo, nextBatchNum, tenantIdList);

        // 验证调用
        verify(pgCommonDao, times(1)).updateDbSyncInfo(any(DBSyncInfo.class));

        log.info("testUpdateDbSyncInfo 测试通过");
    }

    /**
     * 测试边界值和特殊参数
     */
    @Test
    public void testDoTransfer2CH_BoundaryValues() throws Exception {
        // 测试空的TransferEvent
        TransferEvent emptyEvent = new TransferEvent();
        emptyEvent.setChDbURL("");
        emptyEvent.setDbURL("");
        emptyEvent.setSchema("");

        // Mock JedisLock
        when(jedisLock.tryLock()).thenReturn(true);

        // Mock 查询同步信息返回null
        when(pgCommonDao.queryDBSyncInfo(anyString(), anyString(), anyString())).thenReturn(null);

        // 执行测试
        dbTransferService.doTransfer2CH(emptyEvent);

        // 验证查询了同步信息
        verify(pgCommonDao, times(1)).queryDBSyncInfo(anyString(), anyString(), anyString());

        log.info("testDoTransfer2CH_BoundaryValues 测试通过");
    }

    /**
     * 测试性能相关的配置参数
     */
    @Test
    public void testPerformanceConfiguration() {
        // 验证配置参数是否正确设置
        Integer batchSize = (Integer) ReflectionTestUtils.getField(dbTransferService, "batchSize");
        Integer savePointSize = (Integer) ReflectionTestUtils.getField(dbTransferService, "savePointSize");
        Integer batchQueryBeforeSize = (Integer) ReflectionTestUtils.getField(dbTransferService, "batchQueryBeforeSize");

        Assert.assertEquals("batchSize应该为800", Integer.valueOf(800), batchSize);
        Assert.assertEquals("savePointSize应该为100000", Integer.valueOf(100000), savePointSize);
        Assert.assertEquals("batchQueryBeforeSize应该为500", Integer.valueOf(500), batchQueryBeforeSize);

        log.info("testPerformanceConfiguration 测试通过");
    }
}
