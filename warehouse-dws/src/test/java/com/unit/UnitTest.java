package com.unit;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.compare.util.CompareUtil;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.service.DBTransferService;
import com.fxiaoke.bi.warehouse.ods.service.PGMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class UnitTest {

    @MockBean
    private PGMetadataService pgMetadataServiceMock;

    @MockBean
    private DBTransferService dbTransferService;

    @MockBean
    private PgCommonDao pgCommonDao;

    @Test
    public void testClickhouseDataDao() {
        boolean approximatelyEqual = CompareUtil.isApproximatelyEqual(1000L, 1213L, 10);
        System.out.println(approximatelyEqual);
    }

    @Test
    public void testMockito() {
        TransferEvent transferEvent = new TransferEvent("***************************************************", "*****************************************", "public");
        when(pgMetadataServiceMock.findValidaTenantId(transferEvent)).thenReturn(Lists.newArrayList("1", "2", "3"));
        List<String> validaTenantId = pgMetadataServiceMock.findValidaTenantId(transferEvent);
        Assert.assertArrayEquals(new String[]{"1", "2", "3"}, validaTenantId.toArray());
    }

    @Test
    public void testDoTranslate_SimpleCase() {
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        TransferEvent transferEvent = new TransferEvent();
        transferEvent.setChDbURL("***************************************************");
        transferEvent.setDbURL("*****************************************");
        transferEvent.setSchema("public");

        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setPgDb("*****************************************");
        dbSyncInfo.setPgSchema("public");
        dbSyncInfo.setChDb("***************************************************");
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
        dbSyncInfo.setBatchNum(1000L);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());
        dbSyncInfo.setLastSyncTime(System.currentTimeMillis());

        long startTime = System.currentTimeMillis();
        doNothing().when(dbTransferService).doTranslate(any(AtomicLong.class), any(TransferEvent.class), any(DBSyncInfo.class), anyLong());
        dbTransferService.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, startTime);
        verify(dbTransferService, times(1)).doTranslate(eq(nextBatchNum), eq(transferEvent), eq(dbSyncInfo), eq(startTime));

        log.info("testDoTranslate_SimpleCase 测试通过");
    }

    @Test
    public void testShouldWait4IncrementMergeMergeAggData() {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setPgDb("*****************************************");
        dbSyncInfo.setChDb("***************************************************");
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
        when(dbTransferService.shouldWait4IncrementMergeMergeAggData(any(DBSyncInfo.class))).thenReturn(false);
        boolean result = dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo);
        Assert.assertFalse("应该返回false，表示不需要等待", result);
        verify(dbTransferService, times(1)).shouldWait4IncrementMergeMergeAggData(eq(dbSyncInfo));

        log.info("testShouldWait4IncrementMergeMergeAggData 测试通过");
    }

    @Test
    public void testQueryDBSyncInfo() {
        String chDbURL = "***************************************************";
        String dbURL = "*****************************************";
        String schema = "public";

        DBSyncInfo expectedSyncInfo = new DBSyncInfo();
        expectedSyncInfo.setId("6412b13f5cd44942982c91b7");
        expectedSyncInfo.setPgDb(dbURL);
        expectedSyncInfo.setPgSchema(schema);
        expectedSyncInfo.setChDb(chDbURL);
        expectedSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());

        when(pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema)).thenReturn(expectedSyncInfo);

        DBSyncInfo actualSyncInfo = pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema);

        Assert.assertNotNull("查询结果不应为空", actualSyncInfo);
        Assert.assertEquals("同步信息ID应该匹配", expectedSyncInfo.getId(), actualSyncInfo.getId());
        Assert.assertEquals("PG数据库URL应该匹配", expectedSyncInfo.getPgDb(), actualSyncInfo.getPgDb());
        Assert.assertEquals("CH数据库URL应该匹配", expectedSyncInfo.getChDb(), actualSyncInfo.getChDb());

        verify(pgCommonDao, times(1)).queryDBSyncInfo(eq(chDbURL), eq(dbURL), eq(schema));

        log.info("testQueryDBSyncInfo 测试通过");
    }

    @Test
    public void testQueryDBSyncInfo_NotFound() {
        String chDbURL = "***************************************************";
        String dbURL = "*****************************************";
        String schema = "public";

        when(pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema)).thenReturn(null);

        DBSyncInfo actualSyncInfo = pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema);

        Assert.assertNull("查询结果应为空", actualSyncInfo);
        verify(pgCommonDao, times(1)).queryDBSyncInfo(eq(chDbURL), eq(dbURL), eq(schema));

        log.info("testQueryDBSyncInfo_NotFound 测试通过");
    }

    @Test
    public void testUpdateDBSyncInfoStatus() {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ING.getStatus());
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());

        when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);

        pgCommonDao.updateDBSyncInfoStatus(dbSyncInfo);

        verify(pgCommonDao, times(1)).updateDBSyncInfoStatus(eq(dbSyncInfo));
        log.info("testUpdateDBSyncInfoStatus 测试通过");
    }
}
