
#请求chProxy 执行命令
echo "DROP TABLE IF EXISTS fsbidb044091002.agg_data ON CLUSTER '{cluster}';" | curl '*********************************************************************************/' --data-binary @-

# 批量创建topology
curl -X POST 'http://localhost/warehouse-dws/batchCreateAggTopology' \
 -H 'Cache-Control: no-cache'\
 -H 'Content-Type: application/json'\
 -d '{"sourceType":0,"statViewArgList":[{"sourceId":"BI_654da60943c81200018409c8"}],"tenantId":"689436"}'

#批量更新拓扑图状态
 curl -X POST http://localhost/warehouse-dws/batchInitAggTopologyStatus \
  -H 'Cache-Control: no-cache'\
  -H 'Content-Type: application/json'\
  -d '{"tenantId":"1","status":0,"sourceType":0,"statViewArgList":[{"sourceId":"BI_64354b0d60e8fc000171af12","sourceType":0}]}'

export HOMEBREW_BREW_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/brew.git"
export HOMEBREW_CORE_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/homebrew-core.git"
export HOMEBREW_INSTALL_FROM_API=1

export HOMEBREW_API_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles/api"
export HOMEBREW_BOTTLE_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles"


echo 'export HOMEBREW_API_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles/api"' >> ~/.bash_profile
echo 'export HOMEBREW_BOTTLE_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles"' >> ~/.bash_profile
export HOMEBREW_API_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles/api"
export HOMEBREW_BOTTLE_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles"


# 从镜像下载安装脚本并安装 Homebrew / Linuxbrew
git clone --depth=1 https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/install.git brew-install
/bin/bash brew-install/install.sh
rm -rf brew-install

# 也可从 GitHub 获取官方安装脚本安装 Homebrew / Linuxbrew
/bin/bash -c "$(curl -fsSL https://github.com/Homebrew/install/raw/master/install.sh)"



- Run these commands in your terminal to add Homebrew to your PATH:
    echo >> /Users/<USER>/.zprofile
    echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> /Users/<USER>/.zprofile
    eval "$(/opt/homebrew/bin/brew shellenv)"
- Run these commands in your terminal to add the non-default Git remotes for Homebrew/brew and Homebrew/homebrew-core:
    echo '# Set non-default Git remotes for Homebrew/brew and Homebrew/homebrew-core.' >> /Users/<USER>/.zprofile
    echo 'export HOMEBREW_BREW_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/brew.git"' >> /Users/<USER>/.zprofile
    echo 'export HOMEBREW_CORE_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/homebrew-core.git"' >> /Users/<USER>/.zprofile
    export HOMEBREW_BREW_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/brew.git"
    export HOMEBREW_CORE_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/homebrew-core.git"
- Run brew help to get started
- Further documentation:
    https://docs.brew.sh


sh -c "$(curl -fsSL https://raw.githubusercontent.com/robbyrussell/oh-my-zsh/master/tools/install.sh)"
sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"


Host jumpbox  # 自定义跳板机别名（如 jumpbox）
  HostName ***********
  Port 50022
  User jief
  HostKeyAlgorithms +ssh-rsa,ssh-dss  # 允许 ssh-rsa
  # 如果需同时允许 ssh-dss，改为： +ssh-rsa,ssh-dss

ssh jumpbox


,707988^BI_66a75405e21ee200016fd03e
 707988^BI_5bcee91cb8e0e7000101619e

 744964^BI_66050fb3388e630001cf0c45

705221^BI_67be7fe23542c90001a8756d

clickhouse-client --host ***********                   --port 9000                   --user admin                   --password IrHOgJLM7Yi8DO24

set search_path = "sch_714776"

DROP TRIGGER IF EXISTS x_system_changes ON dt_auth_simple;
CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON dt_auth_simple FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();


DROP TRIGGER IF EXISTS x_system_changes ON dt_auth_out_simple;
CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON dt_auth_out_simple FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();


"java.jdt.ls.java.home": "/Users/<USER>/soft/jdk-21.0.6.jdk/Contents/Home",
"java.jdt.ls.vmargs": "--add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED",

东莞市万塑成塑料有限公司wellp88[660533/wellp88]
***********:5432/fsbidb004024
能多洁（中国）环境科技有限公司[718797/rentokil]
***********:5432/fsbidb009010
北新建材（天津）有限公司[798530/bnbmg2024]
************:5432/fsbidb054078020
西安冰峰饮料股份有限公司[759383/bf888888]
************:5432/fsbidb054073017
北京度友科技有限公司[786644/dykj888]
************:5432/fsbidb054075011

curl -X POST --location "http://localhost/warehouse-dws/createChTableSQL"\
    -H "Content-Type: application/json" \
    -d '{"tenantId": "660533","pgDbUrl":"***********************************************","chCluster":"{cluster}","tableEngine":"ReplicatedReplacingMergeTree","schemaName":"public","chDBName":"****************************************************","needCreate":false,"tables":["fmcg_points_exchange_record"]}'

curl -X POST --location "http://localhost/warehouse-dws/createChTableSQL"\
    -H "Content-Type: application/json" \
    -d '{"tenantId": "718797","pgDbUrl":"***********************************************","chCluster":"{cluster}","tableEngine":"ReplicatedReplacingMergeTree","schemaName":"public","chDBName":"****************************************************","needCreate":false}'

curl -X POST --location "http://localhost/warehouse-dws/createChTableSQL"\
    -H "Content-Type: application/json" \
    -d '{"tenantId": "798530","pgDbUrl":"***************************************************","chCluster":"{cluster}","tableEngine":"ReplicatedReplacingMergeTree","schemaName":"public","chDBName":"****************************************************","needCreate":false}'

curl -X POST --location "http://localhost/warehouse-dws/createChTableSQL"\
    -H "Content-Type: application/json" \
    -d '{"tenantId": "759383","pgDbUrl":"***************************************************","chCluster":"{cluster}","tableEngine":"ReplicatedReplacingMergeTree","schemaName":"public","chDBName":"****************************************************","needCreate":false}'

curl -X POST --location "http://localhost/warehouse-dws/createChTableSQL"\
    -H "Content-Type: application/json" \
    -d '{"tenantId": "786644","pgDbUrl":"***************************************************","chCluster":"{cluster}","tableEngine":"ReplicatedReplacingMergeTree","schemaName":"public","chDBName":"****************************************************","needCreate":false}'

660533,592647,268107,684536,662057,691241,666926,617603,718797,89285,252931,587262,498685,727516,486266,684319,89285,718797,729976,657231,568300,663870,320052,105404,713421,800502,796781,798530,806531,776773,759383,783188,784344,786644,728052

curl -X POST --location "http://localhost/warehouse-dws/createChTableSQL"\
    -H "Content-Type: application/json" \
    -d '{"tenantId": "85145","pgDbUrl":"****************************************************","chCluster":"{cluster}","tableEngine":"ReplicatedReplacingMergeTree","schemaName":"public","chDBName":"****************************************************","needCreate":true,"tables":["object_data"]}'


# 批量删除同步信息
curl -X POST --location "http://localhost/warehouse-dws/batchDeletedDbSyncInfo" -H "Content-Type: application/json" -d '["66dac18611c1c51bfeea80ca","66dac18611c1c51bfeea7fb1","66c854191b3d2c3980b22bff","66dac18611c1c51bfeea7f95","66dac18611c1c51bfeea7fcf","66c854191b3d2c3980b22c0b","66dac18611c1c51bfeea7fe2","66dac18611c1c51bfeea80e5","66dac18611c1c51bfeea80fe","66d73a8e9137b82438c25d1d"
]'
# 批量删除表同步信息
curl -X POST --location "http://localhost/warehouse-dws/batchDelDbTableSyncInfo" -H "Content-Type: application/json" -d '{"dbSyncIds":["66e1b4990e1cea2419c14212"],"tables":["dt_auth_simple","dt_auth_out_simple"]}'


