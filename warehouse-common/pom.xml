<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-bi-warehouse</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <artifactId>warehouse-common</artifactId>
  <version>1.6.2-SNAPSHOT</version>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>jdbc-support</artifactId>
    </dependency>
  </dependencies>
</project>