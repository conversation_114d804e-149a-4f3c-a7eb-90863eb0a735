package com.fxiaoke.bi.warehouse.common.bean;

import com.google.common.collect.Sets;

import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聚合函数内过滤器
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PreAggFilter {
    /**
     * 过滤器sql
     */
    private String filterSql;
    /**
     * 过滤器对应的表别名
     */
    private Set<String> tableAliasSet;

    /**
     * 添加表别名
     * @param tableAlias
     */
    public void appendTableAlias(String tableAlias) {
        if (this.tableAliasSet == null) {
            this.tableAliasSet = Sets.newHashSet();
        }
        this.tableAliasSet.add(tableAlias);
    }

    /**
     * 批量添加表别名
     * @param tableAliasSet
     */
    public void batchAppendTableAlias(Set<String> tableAliasSet) {
        if (this.tableAliasSet == null) {
            this.tableAliasSet = Sets.newHashSet();
        }
        this.tableAliasSet.addAll(tableAliasSet);
    }
}
