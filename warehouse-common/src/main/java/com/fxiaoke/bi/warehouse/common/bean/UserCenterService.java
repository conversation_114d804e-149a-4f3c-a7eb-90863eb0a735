package com.fxiaoke.bi.warehouse.common.bean;

import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.facishare.uc.api.service.EnterpriseEditionService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author:jief
 * @Date:2024/5/27
 */
@Slf4j
public class UserCenterService {
  @Setter
  private EnterpriseEditionService enterpriseEditionService;

  /**
   * 验证租户是否合法
   *
   * @param tenantId 租户id
   * @return
   */
  public boolean isValidateStatus(String tenantId) {
    GetSimpleEnterpriseDataArg arg = new GetSimpleEnterpriseDataArg();
    try {
      arg.setEnterpriseId(Integer.parseInt(tenantId));
      GetSimpleEnterpriseDataResult simpleEnterpriseDataResult = enterpriseEditionService.getSimpleEnterpriseData(arg);
      if (null != simpleEnterpriseDataResult && null != simpleEnterpriseDataResult.getEnterpriseData()) {
        int runStatus = simpleEnterpriseDataResult.getEnterpriseData().getRunStatus();
        if (runStatus == RunStatus.RUN_STATUS_STOP || runStatus == RunStatus.RUN_STATUS_INVALIDATE ||
          runStatus == RunStatus.RUN_STATUS_DELETE) {
          log.warn("get tenantId:{} run status is {}", tenantId, runStatus);
          return false;
        }
      }
    } catch (Exception e) {
      log.error("check tenant validate error: tenantId:{}", tenantId, e);
    }
    return true;
  }
}
