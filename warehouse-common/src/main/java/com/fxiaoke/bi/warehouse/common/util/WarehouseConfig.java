package com.fxiaoke.bi.warehouse.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2024/7/27
 */
public class WarehouseConfig {
  public static Set<String> skipBeforeTableSet = Sets.newHashSet();
  public static Set<String> noNeedPartitionTableSet = Sets.newHashSet();
  public static Set<String> hasSysEiTables = Sets.newHashSet();
  public static Set<String> mustInsertSysModifiedTime = Sets.newHashSet();
  public static Set<String> chAggDataFixSlot = Sets.newHashSet();
  public static Set<String> forceDbFieldNames = Sets.newHashSet();
  public static int chReadTimeOut = 1000 * 60 * 30;
  /**
   * 同步获取当前最大的时间标尺的延迟时间
   */
  public static volatile int timeDelay = 10000;
  /**
   *数据同步批次集合
   */
  public static volatile int dbSyncFlowDelay = 15 * 60 * 1000;
  /**
   * 分区键
   */
  public static final String ODS_PARTITION_KEY = "bi_sys_ods_part";
  /**
   * 开启增量partition
   */
  public static final int OPEN_INC_PARTITION = 1;
  /**
   * 开启计算
   */
  public static final int OPEN_CAL_PARTITION = 1;
  /**
   * 关闭计算分区
   */
  public static final int CLOSE_CAL_PARTITION = 0;
  /**
   * 关闭增量partition
   */
  public static final int CLOSE_INC_PARTITION = 0;
  /**
   * 增量partition名称
   */
  public static final String INC_PARTITION_NAME = "i";
  /**
   * 存量分区partition名称
   */
  public static final String STOCK_PARTITION_NAME = "s";
  /**
   * 计算分区名称
   */
  public static final String CAL_PARTITION_NAME = "c";
  /**
   * paas2bi标记
   */
  public static final String PAAS2BI_FLAG = "paas2bi";
  /**
   * 租户id是ei的表集合
   */
  public static Set<String> eiTables = Sets.newHashSet();
  /**
   *
   */
  public static volatile double insertThreadRate = 3.0D;
  public static volatile int partitionCopyRetryTimes = 3;
  /**
   * dbSyncFlow 保留时长ms
   */
  public static volatile long dbSyncFlowRetentionMs = 7*24*60*60*1000L;
  public static final String finalsetting = "SETTINGS final = 1, do_not_merge_across_partitions_select_final = 1, " +
    "optimize_move_to_prewhere_if_final = 1, join_use_nulls = 1";
  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
  public static Map<String,String> ch2chTablePair = Maps.newHashMap();
  public static Set<String> ch2chTables = Sets.newHashSet();
  public static long integrateBeginTime;
  public static long integrateEndTime;
  public static double calcFlowStatCostQuantile;
  /**
   * 是否跳过explain
   */
  private static Set<String> skipExplainUniqueKeys = Sets.newHashSet();
  /**
   * 是否允许下游数据同步
   */
  public static volatile boolean allowIntegrate;
  public static volatile int querySyncFlowLimit;
  public static volatile long mergeBeginTime;
  public static volatile long mergeEndTime;
  public static Set<String> allowMergeCHDB = Sets.newHashSet();
  public static volatile String skipProfiles;
  public static Set<String> grayPgDBs = Sets.newHashSet();
  public static String biWarehouseDelayMsgReceiver;
  public static String biWarehouseDelayMsgReceiverName;
  public static String industryAddress;
  public static String alarmNotifyAppId = "FSAID_5f5e501";
  public static final String DWS_AGG_EVENT = "DWS_AGG_EVENT";
  public static final String ODS_EVENT = "ODS_EVENT";
  /**
   * 按照pgdb自定义同步频率
   */
  public static Map<String, Long> customSyncInterval = Maps.newHashMap();
  public static volatile long defaultSyncInterval = 10 * 60 * 1000L;
  public static Set<String> blackTableList = Sets.newHashSet();
  public static int distributed_ddl_task_timeout=7200;
  public static String[] authFields = new String[] {"data_auth_code", "out_tenant_id", "out_data_auth_code", "out_owner", "out_data_own_organization", "out_data_own_department", "data_own_department", "data_own_organization"};
  public static Set<String> allowBiPgCHTables = Sets.newHashSet();
  /**
   * 是否开启paas2bi同步0:否,1:是
   */
  public static int ALLOW_PAAS2BI_STATUS = 1;
  /**
   * 是否开启paas2bi同步0:否,1:是
   */
  public static int CLOSE_PAAS2BI_STATUS = 0;


  public static String dimEnterpriseInfo = "dim_enterprise_info";
  public static Set<String> noNeedDeleteTables = Sets.newHashSet();
  public static Map<String, List<String>> orderByTableColumns = Maps.newHashMap();

  public static List<String> initChTables = Lists.newArrayList();
  /**
   * 多值描述
   */
  public static List<String> convertArrayType = List.of("select_many", "tag", "department", "employee", "department_many", "employee_many", "object_reference_many", "dimension", "out_employee");

  /**
   * 特殊表指定 ORDER BY 字段
   */
  public static Map<String, List<String>> tableOrderByColumnsMap = Maps.newHashMap();

  static {
    ConfigFactory.getConfig("fs-bi-warehouse", "common", config -> {
      chReadTimeOut = config.getInt("ch_socket_time_out", 1000 * 60 * 30);
      String sysEiTables = config.get("have_sys_ei_tables", "");
      hasSysEiTables = Sets.newHashSet(Splitter.on(",").splitToList(sysEiTables));
      String skipBeforeTableStr = config.get("no_need_before_tables", "");
      skipBeforeTableSet = Sets.newHashSet(Splitter.on(",").splitToList(skipBeforeTableStr));
      timeDelay= config.getInt("sync_max_time_delay",10000);
      dbSyncFlowDelay = config.getInt("db_sync_flow_delay_ms", 15 * 60 * 1000);
      String eiTablesStr = config.get("ei_tables","dim_sys_area_gray,dim_sys_date,sale_action_stage");
      eiTables = Sets.newHashSet(Splitter.on(CharMatcher.anyOf(",|")).splitToList(eiTablesStr));
      insertThreadRate = config.getDouble("partition_copy_thread_rate",3.0D);
      partitionCopyRetryTimes = config.getInt("partition_copy_retry_times",3);
      String noNeedPartition = config.get("no_need_partition_tables","");
      noNeedPartitionTableSet= Sets.newHashSet(Splitter.on(",").splitToList(noNeedPartition));
      dbSyncFlowRetentionMs= config.getLong("db_sync_flow_retention_ms",7*24*60*60*1000L);
      String tablePair = config.get("ch2ch_table_pair","");
      Map<String,String> ch2chTablePairTMp= Maps.newHashMap();
      Set<String> ch2chTablesTmp = Sets.newHashSet();
      Splitter.on(",").splitToList(tablePair).forEach(tp -> {
        String[] tables = tp.split("\\^");
        if (tables.length == 2) {
          ch2chTablePairTMp.put(tables[0], tables[1]);
          ch2chTablesTmp.add(tables[0]);
          ch2chTablesTmp.add(tables[1]);
        }
      });
      ch2chTables = ch2chTablesTmp;
      ch2chTablePair = ch2chTablePairTMp;
      //下游数据集成到上游时间
      integrateBeginTime = LocalTime.parse(config.get("integrateBeginTime", "00:01"), formatter)
                                    .getLong(ChronoField.MINUTE_OF_DAY);
      integrateEndTime = LocalTime.parse(config.get("integrateEndTime", "23:59"), formatter).getLong(ChronoField.MINUTE_OF_DAY);
      allowIntegrate = config.getBool("allow_integrate",false);
      String insertSysModifiedTime = config.get("must_insert_sys_modified_time","");
      mustInsertSysModifiedTime=Sets.newHashSet(Splitter.on(",").splitToList(insertSysModifiedTime));
      String chAggFixSlot = config.get("ch.agg.fix.slot", "owner,data_auth_code,out_tenant_id,out_data_auth_code");
      chAggDataFixSlot = Sets.newHashSet(Splitter.on(",").splitToList(chAggFixSlot));
      String dimFixField = config.get("new_agg_data_fix_dim", "");
      forceDbFieldNames = Splitter.on(",").splitToStream(dimFixField).collect(Collectors.toSet());
      //跳过explain
      skipExplainUniqueKeys = Sets.newHashSet(Splitter.on(",").splitToList(config.get("skip_explain_unique_key","")));
      querySyncFlowLimit = config.getInt("db_table_sync_flow_limit", 100);
      mergeBeginTime = LocalTime.parse(config.get("mergeBeginTime", "01:00"), formatter)
                                .getLong(ChronoField.MINUTE_OF_DAY);
      mergeEndTime = LocalTime.parse(config.get("mergeEndTime", "06:00"), formatter).getLong(ChronoField.MINUTE_OF_DAY);
      //放开agg_data merge的chDB
      String allMergeCHTmp = config.get("all_merge_chDB", "");
      if (StringUtils.isNotBlank(allMergeCHTmp)) {
        allowMergeCHDB = Sets.newHashSet(Splitter.on(CharMatcher.anyOf(",|"))
                                                 .omitEmptyStrings()
                                                 .splitToList(allMergeCHTmp));
      }
      skipProfiles = config.get("skipProfiles", "fstest-gray,foneshare-gray");
      //灰度pg db^schema
      String useGrayPgDb = config.get("use_gray_pg_db", "");
      if (StringUtils.isNotBlank(useGrayPgDb)) {
        grayPgDBs = Sets.newHashSet(Splitter.on(CharMatcher.anyOf(",|")).omitEmptyStrings().splitToList(useGrayPgDb));
      }
      biWarehouseDelayMsgReceiver = config.get("biWarehouseDelayMsgReceiver", "7153,6021,8776,9253,9408");
      biWarehouseDelayMsgReceiverName = config.get("biWarehouseDelayMsgReceiverName", "郑子阳,纪二飞,赵孟华,肖扬,赵正豪");//IgnoreI18n
      alarmNotifyAppId = config.get("alarmNotifyAppId", "FSAID_5f5e501");
      customSyncInterval = JSON.parseObject(config.get("custom_sync_interval_ms", "{}"), new TypeReference<>() {
      });
      defaultSyncInterval = config.getLong("default_sync_interval_ms",10 * 60 * 1000);
      industryAddress = config.get("industryAddress", "http://************:39414/fs-bi-industry-interface");
      calcFlowStatCostQuantile = config.getDouble("calc_flow_stat_cost_quantile", 0.9);
      String blackTables = config.get("sysnc_table_black_list", "");
      blackTableList = Splitter.on(",").omitEmptyStrings().splitToStream(blackTables).collect(Collectors.toSet());
      distributed_ddl_task_timeout = config.getInt("distributed_ddl_task_timeout", 3600);
      String allowBiPgCHTablesStr = config.get("bipg_ch_tables", "");
      allowBiPgCHTables = Sets.newHashSet(Splitter.on(",").omitEmptyStrings().splitToList(allowBiPgCHTablesStr));
      String noNeedDeleteTablesStr = config.get("no_need_deleted_tables","sale_action_stage,v_saleactionstage,en_drr_daily,ew_bi_ens_used_info_daily");
      noNeedDeleteTables = Splitter.on(",").omitEmptyStrings().splitToStream(noNeedDeleteTablesStr).collect(Collectors.toSet());
      orderByTableColumns = JSON.parseObject(config.get("shence_order_by_table_columns", "{}"), new TypeReference<>() {
      });
      String convertArrayTypeStr = config.get("convert_array_type", "select_many,tag,department,employee,department_many,employee_many,object_reference_many,dimension,out_employee");
      convertArrayType = Splitter.on(",").omitEmptyStrings().splitToList(convertArrayTypeStr);
      //每次初始化CHdb的时候需要初始化的表
      String initCHTables = config.get("init_ch_tables", "agg_data,agg_data_history,bi_mt_data_tag_v,v_saleactionstage,goal_value_obj_snapshot,biz_account_main_data,biz_user_login_online_operation_ods,biz_user_login_online_operation,biz_user_api_name_operation_ods,biz_user_api_name_operation,biz_user_bi_operation_ods,biz_user_bi_operation,dim_name_data,bi_agg_log,agg_downstream_data,bi_data_sync_policy_log,stage_runtime_task_new_opportunity,stage_runtime_new_opportunity,agg_data_sync_info");
      initChTables = Splitter.on(",").omitEmptyStrings().splitToList(initCHTables);
      // 解析tableOrderByColumns配置并填充到tableOrderByColumnsMap中
      String tableOrderByColumns = config.get("tableOrderByColumns", "goal_value_obj|tenant_id;bi_sys_flag;rule_id;id,goal_value_obj_week|tenant_id;bi_sys_flag;rule_id;id,goal_value_obj_quarter|tenant_id;bi_sys_flag;rule_id;id,goal_value_obj_year|tenant_id;bi_sys_flag;rule_id;id");
      if (StringUtils.isNotBlank(tableOrderByColumns)) {
        // 按逗号分隔不同表的配置
        List<String> tableConfigs = Splitter.on(",").omitEmptyStrings().splitToList(tableOrderByColumns);
        for (String tableConfig : tableConfigs) {
          // 按|分隔表名和列名列表
          List<String> parts = Splitter.on("|").limit(2).splitToList(tableConfig);
          if (parts.size() == 2) {
            String tableName = parts.getFirst();
            // 按;分隔列名
            List<String> columns = Splitter.on(";").splitToList(parts.get(1));
            tableOrderByColumnsMap.put(tableName, columns);
          }
        }
      }
    });
  }

  /**
   * 判断是否跳过执行计划校验
   * @param tenantId
   * @param uniqueKey
   * @return
   */
  public static boolean skipExplain(String tenantId, String uniqueKey) {
   return GrayManager.isAllowByRule("skip_explain_unique_key",tenantId) || GrayManager.isAllowByRule("skip_explain_unique_key",tenantId + "^" + uniqueKey);
//    return skipExplainUniqueKeys.contains(tenantId) || skipExplainUniqueKeys.contains(tenantId + "^" + uniqueKey);
  }

  public static boolean grayPGDB(String pgDBName, String pgSchema) {
    return grayPgDBs.contains(String.format("%s^%s", pgDBName, pgDBName));
  }
}
