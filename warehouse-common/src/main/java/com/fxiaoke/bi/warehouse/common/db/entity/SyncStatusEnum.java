package com.fxiaoke.bi.warehouse.common.db.entity;

import lombok.Getter;

/**
 * 同步计算状态<br>
 * db_sync_info.status
 */
@Getter
public enum SyncStatusEnum {

  SYNC_ABLE(-2, "sync_able"),
  SYNC_ERROR(-1, "sync_error"),
  SYNC_ING(0, "sync_ing"),
  SYNC_ED(1, "sync_ed"),
  AGG_ING(2, "agg_ing"),
  AGG_ED(3, "agg_ed"),
  EXCHANGE_AGG(4, "exchange_agg"),
  COPY_BEFORE_ING(5, "copy_before_ing"),//增量分区到存量分区生成before数据
  INC_2_CAL_AFTER_ING(6,"inc_2_cal_after_ing"), //增量分区after数据拷贝到计算分区
  COPY_AFTER_ING(7, "copy_after_ing"),//增量分区拷贝after数据到存量分区
  CAL_2_STOCK_BEFORE_ING(8,"cal_to_stock_before_ing"),//通过计算分区生成before数据到存量分区
  CAL_2_STOCK_AFTER_ING(9,"cal_to_stock_after_ing");//计算分区的after数据拷贝到存量分区

  private final Integer status;
  private final String desc;

  public static SyncStatusEnum createFromStatus(Integer status) {
    switch (status) {
      case -2 -> {
        return SyncStatusEnum.SYNC_ABLE;
      }
      case -1 -> {
        return SyncStatusEnum.SYNC_ERROR;
      }
      case 0 -> {
        return SyncStatusEnum.SYNC_ING;
      }
      case 1 -> {
        return SyncStatusEnum.SYNC_ED;
      }
      case 2 -> {
        return SyncStatusEnum.AGG_ING;
      }
      case 3 -> {
        return SyncStatusEnum.AGG_ED;
      }
      case 4 -> {
        return EXCHANGE_AGG;
      }
      case 5 -> {
        return COPY_BEFORE_ING;
      }
      case 7 -> {
        return COPY_AFTER_ING;
      }
      case 6 -> {
        return INC_2_CAL_AFTER_ING;
      }
      case 8 -> {
        return CAL_2_STOCK_BEFORE_ING;
      }
      case 9 -> {
        return CAL_2_STOCK_AFTER_ING;
      }
    }
    return SyncStatusEnum.SYNC_ABLE;
  }

  SyncStatusEnum(Integer status, String desc) {
    this.status = status;
    this.desc = desc;
  }
}
