package com.fxiaoke.bi.warehouse.common.bean;

import java.io.Serializable;


public class BIPair<F, S> implements Serializable {
        private static final long serialVersionUID = 1L;
        public final F first;
        public final S second;
      
        public BIPair(F first, S second) {
          this.first = first;
          this.second = second;
        }
        
        private static boolean eq(Object a, Object b) {
          return (a == b) || (a != null && a.equals(b));
        }
        
        public F getFirst() {
          return first;
        }

        public S getSecond() {
          return second;
        }

        /**
         * 通过值创建值对
         *
         * @param f 第一个值
         * @param s 第二个值
         * @return 值对
         */
        public static <F, S> BIPair<F, S> build(F f, S s) {
          return new BIPair<>(f, s);
        }
      
        /**
         * 通过值创建值对
         *
         * @param f 第一个值
         * @param s 第二个值
         * @return 值对
         */
        public static <F, S> BIPair<F, S> of(F f, S s) {
          return new BIPair<>(f, s);
        }
      
      
        @Override
        public int hashCode() {
          return 17 * ((first != null) ? first.hashCode() : 0) + 17 * ((second != null) ? second.hashCode() : 0);
        }
      
        @Override
        public boolean equals(Object o) {
          if (o == this) {
            return true;
          }
          if (!(o instanceof BIPair<?, ?>)) {
            return false;
          }
          BIPair<?, ?> that = (BIPair<?, ?>) o;
          return eq(this.first, that.first) && eq(this.second, that.second);
        }
      
        @Override
        public String toString() {
          return String.format("(%s,%s)", first, second);
        }
}
