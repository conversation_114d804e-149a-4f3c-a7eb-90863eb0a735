package com.fxiaoke.bi.warehouse.common.db.entity;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> zzh
 * @createTime : [2024/8/23 14:31]
 */
@Getter
public enum BiMtDimensionStatusEnum {

  enable(1), unable(0), system_unable(3), init(2);

  private final int status;

  BiMtDimensionStatusEnum(int status) {
    this.status = status;
  }

  public static Integer[] allStatus() {
    return Arrays.stream(BiMtDimensionStatusEnum.values())
                 .map(BiMtDimensionStatusEnum::getStatus)
                 .toArray(Integer[]::new);
  }

  public static BiMtDimensionStatusEnum fromStatus(Integer status) {
    return switch (status) {
      case 1 -> BiMtDimensionStatusEnum.enable;
      case 2 -> BiMtDimensionStatusEnum.init;
      case 3 -> BiMtDimensionStatusEnum.system_unable;
      default -> BiMtDimensionStatusEnum.unable;
    };
  }
}
