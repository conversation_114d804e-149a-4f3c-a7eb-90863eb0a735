package com.fxiaoke.bi.warehouse.common.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

/**
 * @Author:jief
 * @Date:2024/8/7
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CHColumn implements Comparable<CHColumn> {
  private String table;
  private String name;
  private int order;
  private String typeName;

  @Override
  public int compareTo(@NotNull CHColumn o) {
    return this.order-o.getOrder();
  }
}
