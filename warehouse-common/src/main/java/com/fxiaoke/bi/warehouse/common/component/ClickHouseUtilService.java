package com.fxiaoke.bi.warehouse.common.component;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.bi.warehouse.common.bean.ApiResult;
import com.fxiaoke.bi.warehouse.common.bean.EnterPriseLevelEnum;
import com.fxiaoke.bi.warehouse.common.bean.InitChDbRouteArg;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

@Slf4j
public class ClickHouseUtilService {

    private RpcPaasService rpcPaasService;

    private EIEAConverter eieaConverter;

    /**
     * 初始化企业ch路由
     *
     * @param tenantId   企业id
     * @param chDb       ch db
     * @param standalone 是否是专属库
     * @param eiToEaMap  eiToEaMap
     */
    public void createChRoute(String tenantId, String chDb, boolean standalone, Map<Integer, String> eiToEaMap) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(chDb)) {
            return;
        }
        String initChDb = CommonUtils.getDBName(chDb);
        if (StringUtils.isBlank(initChDb)) {
            return;
        }
        String ea = eiToEaMap.get(Integer.valueOf(tenantId));
        EnterPriseLevelEnum enterPriseLevelEnum = EnterPriseLevelEnum.getEnterPriseLevelEnum(standalone, ea);
        InitChDbRouteArg initChDbRouteArg = InitChDbRouteArg.builder()
                                                            .dbName(initChDb)
                                                            .activityLevel(enterPriseLevelEnum.getStatus())
                                                            .checkActivityLevel(false)
                                                            .podRouter(InitChDbRouteArg.PodRouter.builder()
                                                                                                 .biz("BI")
                                                                                                 .tenantId(tenantId)
                                                                                                 .dialect("clickhouse")
                                                                                                 .standalone(standalone)
                                                                                                 .build())
                                                            .build();
        ApiResult apiResult = rpcPaasService.initChDbRoute(initChDbRouteArg);
        if (!ApiResult.IsSuccess(apiResult)) {
            log.error("init ch route error, json:{}", JSON.toJSON(initChDbRouteArg));
        }
    }

    /**
     * 批量获取企业ea
     *
     * @param tenantIdList 企业ei
     * @return eiToEaMap
     */
    public Map<Integer, String> getEiToEaMap(List<String> tenantIdList) {
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return Maps.newHashMap();
        }
        try {
            return eieaConverter.enterpriseIdToAccount(tenantIdList.stream().map(Integer::valueOf).toList());
        } catch (Exception e) {
            log.error("enterpriseIdToAccount error:{}", tenantIdList,e);
        }
        return Maps.newHashMap();
    }

    public void setRpcPaasService(RpcPaasService rpcPaasService) {
        this.rpcPaasService = rpcPaasService;
    }

    public void setEIEAConverter(EIEAConverter eieaConverter) {
        this.eieaConverter = eieaConverter;
    }
}
