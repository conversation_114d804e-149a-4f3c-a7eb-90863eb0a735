package com.fxiaoke.bi.warehouse.common.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.bean.ApiResult;
import com.fxiaoke.bi.warehouse.common.bean.InitChDbRouteArg;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import java.util.Objects;

/**
 * @Author:jief
 * @Date:2024/5/29
 */
@Slf4j
public class RpcPaasService implements InitializingBean {
  private String paasApiHost;
  private OkHttpSupport client;

  private void init() {
    ConfigFactory.getInstance().getConfig("variables_endpoint", config -> {
      paasApiHost = config.get("svc_pod_route", StringUtils.EMPTY);
    });
  }

  public ApiResult initChDbRoute(InitChDbRouteArg initChDbRouteArg) {
    String jsonString = JSON.toJSONString(initChDbRouteArg);
    String postUrl = "http://" + paasApiHost + "/init/by/dbName";
    MediaType MEDIA_JSON = MediaType.parse("application/json; charset=utf-8");
    okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(MEDIA_JSON, jsonString);
    Request request = new Request.Builder().url(postUrl).post(requestBody).build();
    Object result = client.syncExecute(request, new SyncCallback() {
      @Override
      public String response(Response response) throws Exception {
        if (response.isSuccessful()) {
          okhttp3.ResponseBody body = response.body();
          if (body != null) {
            return body.string();
          }
        }
        log.error("init/by/dbName error params:{},response:{}", jsonString, response);
        return null;
      }
    });
    if (result != null) {
      try {
        JSONObject jsonObject = JSONObject.parseObject(String.valueOf(result));
        if (Objects.equals(jsonObject.get("errorCode"), 0)) {
          return ApiResult.success("初始化CH-db路由成功");//IgnoreI18n
        } else {
          return ApiResult.error("初始化CH-db路由失败");//IgnoreI18n
        }
      } catch (Exception e) {
        log.error("initChDbRoute parseObject params:{} result:{}", jsonString, result, e);
      }
    }
    return ApiResult.error("初始化CH-db路由失败");//IgnoreI18n
  }

  public void setClient(OkHttpSupport client) {
    this.client = client;
  }

  @Override
  public void afterPropertiesSet() throws Exception {
    init();
  }

}
