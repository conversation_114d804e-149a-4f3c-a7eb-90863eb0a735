package com.fxiaoke.bi.warehouse.common.db.er;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 对象JOIN关系
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class NodeJoin implements Comparable<NodeJoin>{
  /**
   * join类型
   */
  private JoinType joinType;
  /**
   * join的表
   */
  private NodeTable table;
  /**
   * join的条件
   */
  private NodeOnCondition onCondition;

  /**
   * 目前只能判断单向的join 如果是 inner join 需要判断双向
   *
   * @param otherJoin
   * @return
   */
  public boolean isDuplicateJoin(NodeJoin otherJoin) {
    //    return joinType == otherJoin.joinType && table.createJoinTag().equals(otherJoin.getTable().createJoinTag());
    return table.createJoinTag().equals(otherJoin.getTable().createJoinTag());
  }

  public void mergeOther(NodeJoin o) {
    if (o == null) {
      return;
    }
    NodeTable nodeTable = o.getTable();
    List<NodeColumn> tableColumns = this.getTable().getColumnList();
    nodeTable.getColumnList().forEach(nodeColumn -> {
      if (!tableColumns.contains(nodeColumn)) {
        tableColumns.add(nodeColumn);
      }
    });
    //如果是objectId table 需要设置到table 对象
    if (!this.getTable().isObjectIdTable()) {
      this.getTable().setObjectIdTable(nodeTable.isObjectIdTable());
    }
    //合并列名
    this.getTable().appendColumns(nodeTable.getFilterColumns().toArray(new String[0]));
    //合并arrayJoin
    nodeTable.getArrayJoinColumn().forEach((k, v) -> {
      this.getTable().addArrayJoinColumn(k, v);
    });
    if (this.onCondition == null) {
      this.onCondition = o.onCondition;
    } else if (o.onCondition != null) {
      this.onCondition.appendEqualPairs(o.onCondition.getEqualPairs());
      this.onCondition.appendTableCondition(true, o.onCondition.getLeftTableCondition());
      this.onCondition.appendTableCondition(false, o.onCondition.getRightTableCondition());
    }
    if (this.getTable().isObjectIdTable() && this.onCondition != null &&
      !CollectionUtils.isEmpty(this.onCondition.getRightTableCondition())) {
      this.onCondition.getRightTableCondition().removeIf(rtc -> "is_deleted".equals(rtc.first));
    }
  }

  /**
   * 用于排序
   * @return
   */
  public int findOrderByKey(){
    return Objects.hashCode(table.getName()+"^"+table.getLookupColumn()+"^"+table.isSelectInvalid()+"^"+table.getObjectDescribeApiName());
  }

  /**
   * TreeSet排序
   * @param o the object to be compared.
   * @return
   */
  @Override
  public int compareTo(@NotNull NodeJoin o) {
    if (this.findOrderByKey() > o.findOrderByKey()) {
      return 1;
    } else if (this.findOrderByKey() < o.findOrderByKey()) {
      return -1;
    }
    return 0;
  }
}
