package com.fxiaoke.bi.warehouse.common.component;

import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.common.Pair;
import com.github.mybatis.tenant.TenantContext;
import com.github.mybatis.tenant.TenantPolicy;
import lombok.Setter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/3/17
 */
public class MybatisPaasTenantPolicy implements TenantPolicy {
  @Setter
  private DbRouterClient dbRouterClient;
  public static String BIZ = "CRM";
  public static String APPLICATION = "fs-bi-paas2gp-transfer";
  public static String DIALECT = "postgresql";

  @Override
  public TenantContext get(String tenantId, boolean readOnly) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, GrayManager.isAllowByRule(
      "use-pgbouncer-paas", tenantId));
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (readOnly && GrayManager.isAllowByRule("wh-use-db-slave-paas", tenantId) && routerInfo.getSlaveUrl() != null) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    String schema = BooleanUtils.isTrue(routerInfo.getStandalone()) ? "sch_" + tenantId : "public";
    return TenantContext.builder()
                        .id(tenantId)
                        .url(jdbcUrl)
                        .username(routerInfo.getUserName())
                        .password(routerInfo.getPassWord())
                        .schema(schema)
                        .build();
  }

  public boolean standalone(String tenantId) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, GrayManager.isAllowByRule(
      "use-pgbouncer-paas", tenantId));
    return null != routerInfo.getStandalone() ? routerInfo.getStandalone() : false;
  }

  /**
   * <p>获取路由路由地址</p>
   */
  public Pair<String, Boolean> getDBURLAndSchema(String tenantID) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT);
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (GrayManager.isAllowByRule("wh-use-db-slave-paas", tenantID) && StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    return Pair.build(jdbcUrl, null != routerInfo.getStandalone() ? routerInfo.getStandalone() : false);
  }

  /**
   * <p>获取路由路由地址</p>
   */
  public String getPgBouncerJdbcURL(String tenantId, boolean readOnly) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT,
      GrayManager.isAllowByRule("use-pgbouncer-paas", tenantId));
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (readOnly && GrayManager.isAllowByRule("wh-use-db-slave-paas", tenantId) &&
      StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    return jdbcUrl;
  }

}
