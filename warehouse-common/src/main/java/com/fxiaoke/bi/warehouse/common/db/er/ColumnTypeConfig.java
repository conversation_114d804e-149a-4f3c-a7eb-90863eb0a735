package com.fxiaoke.bi.warehouse.common.db.er;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/3
 */
public class ColumnTypeConfig {

  public interface _ActionDate {
    String TIMEZONE = "timezone";
    String FIELD_TYPE = "fieldType";
  }


  public interface _AGG {
    String AGG_TYPE = "aggType";
  }


  public interface _Array {
    String ITEM_TYPE = "itemType";
  }

  private Map<String, Object> configMap;

  public ColumnTypeConfig(Map<String, Object> configMap) {
    this.configMap = configMap;
  }

  public <T> T get(String key) {
    return (T) configMap.get(key);
  }

  public <T> T getOrDefault(String key, Object value) {
    return (T) configMap.getOrDefault(key, value);
  }

  public static ColumnTypeConfig empty() {
    return new ColumnTypeConfig(Map.of());
  }

  public static ColumnTypeConfig parse(String configString) {
    return new ColumnTypeConfig(Map.of());
  }
}
