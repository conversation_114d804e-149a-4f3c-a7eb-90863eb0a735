package com.fxiaoke.bi.warehouse.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.bi.warehouse.common.bean.DBInfo;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Created by jief on 2020/8/5.
 */
@Slf4j
public class DBInfoService {
  private static final Pattern biSystemRegex = Pattern.compile("^bi_system.*");
  private static final Pattern bidbRegex = Pattern.compile("^fsbidb.*");
  private OkHttpSupport okHttpSupport;

  public DBInfoService(OkHttpSupport okHttpSupport) {
    this.okHttpSupport = okHttpSupport;
  }

  private DBInfoService() {
  }

  /**
   * @param schemaUrl
   * @param dialect
   * @param biz
   * @return
   */
  public Map<String, String> getDBInfoFromRemote(String schemaUrl, String dialect, String biz) {
    HashMap<String, String> headers = new HashMap<>();
    String traceId = TraceContext.get().getTraceId();
    headers.put("X-fs-Trace-Id", traceId == null ? "" : traceId);
    headers.put("Content-Type", "application/json; charset=UTF-8");
    JSONObject jsonObject = null;
    try {
      jsonObject = this.getDBInfoFromRemote(String.format(schemaUrl, dialect, biz), headers);
      return this.parseJSON2Map(jsonObject);
    } catch (Exception e) {
      log.error("get db info map from remote url:{}", schemaUrl, e);
    }
    return null;
  }


  public JSONObject getDBInfoFromRemote(String url, Map<String, String> headers) throws Exception {
    log.info("http get url:{},header:{}", url, headers);
    Request.Builder builder = new Request.Builder();
    if (MapUtils.isNotEmpty(headers)) {
      headers.forEach((k, v) -> builder.addHeader(k, v));
    }
    Request request = builder.url(url).build();
    return (JSONObject) okHttpSupport.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        String res = response.body().string();
        if (response.isSuccessful()) {
          return JSON.parseObject(res);
        } else {
          throw new RuntimeException("response error:" + res);
        }
      }
    });
  }

  /**
   * 获取ip和db映射关系
   */
  public Map<String, String> parseJSON2Map(JSONObject jsonObject) {
    JSONArray jsonArray = jsonObject.getJSONArray("data");
    if (jsonArray == null || jsonArray.isEmpty()) {
      return null;
    }
    Map<String, String> ipDbMap = Maps.newHashMap();
    if ("fstest".equalsIgnoreCase(ConfigHelper.getProcessInfo().getProfile())) {
      jsonArray.stream()
               .filter(jsonObj -> !biSystemRegex.matcher(((JSONObject) jsonObj).getString("dbName")).matches())
               .forEach(jsonObj -> {
                 ipDbMap.computeIfAbsent(((JSONObject) jsonObj).getString("master"),
                   key -> ((JSONObject) jsonObj).getString("masterProxyUrl"));
               });
    } else {
      jsonArray.stream()
               .filter(jsonObj -> bidbRegex.matcher(((JSONObject) jsonObj).getString("dbName")).matches())
               .forEach(jsonObj -> {
                 ipDbMap.computeIfAbsent(((JSONObject) jsonObj).getString("master"),
                   key -> ((JSONObject) jsonObj).getString("masterProxyUrl"));
               });
    }
    return ipDbMap;
  }

  /**
   * 获取ip和dbame
   */
  private DBInfo parse2DBInfo(JSONObject jsonObject) {
    DBInfo dbInfo = new DBInfo();
    dbInfo.setDbName(jsonObject.getString("dbName"));
    dbInfo.setIp(jsonObject.getString("master"));
    dbInfo.setMasterProxyUrl(jsonObject.getString("masterProxyUrl"));
    return dbInfo;
  }

}
