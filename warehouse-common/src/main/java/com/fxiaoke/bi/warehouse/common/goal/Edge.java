package com.fxiaoke.bi.warehouse.common.goal;

import com.fxiaoke.bi.warehouse.common.db.er.JoinType;
import com.fxiaoke.common.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Edge {
  private String from;
  private JoinType joinType;
  private String to;
  private String lookupField;
  private List<OnField> onFields;
  private boolean isLookUp; //正向还是反向查找关联

  public Pair<String, String> findLookupFieldPair() {
    Optional<OnField> onFieldOptional = onFields.stream().filter(onField -> {
      return
        !StringUtils.equalsAny(onField.getFromField(), "ei", "tenant_id", "object_describe_api_name", "is_deleted") &&
          !StringUtils.equalsAny(onField.getToField(), "ei", "tenant_id", "object_describe_api_name", "is_deleted");
    }).findFirst();
    return onFieldOptional.map(OnField::toPair).orElse(null);
  }
}
