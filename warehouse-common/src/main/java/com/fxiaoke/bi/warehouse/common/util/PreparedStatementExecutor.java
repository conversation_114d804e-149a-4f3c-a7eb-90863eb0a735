package com.fxiaoke.bi.warehouse.common.util;

import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

/**
 * prepare statement batch 提交
 *
 * <AUTHOR>
 * @since 2021/5/20
 */
@Slf4j
public class PreparedStatementExecutor {
  private String name;
  private final Connection connection;
  private String sql;
  private PreparedStatement statement;
  private int counter = 0;
  private int batchSize;
  private int total = 0;
  private RateLimiter rateLimiter;

  public PreparedStatementExecutor(String name, Connection connection, String sql, int batchSize, RateLimiter rateLimiter) {
    this.name = name;
    this.connection = connection;
    this.sql = sql;
    this.batchSize = batchSize;
    this.rateLimiter=rateLimiter;
  }

  public void accept(Object[] values) throws SQLException {
    init();
    if (rateLimiter != null) {
      rateLimiter.acquire();
    }
    for (int i = 0; i < values.length; i++) {
      statement.setObject(i + 1, values[i]);
    }
    statement.addBatch();
    counter++;
    total++;
    if (counter == batchSize) {
      flush();
    }
  }

  public void flush() throws SQLException {
    if (null != statement) {
      if (counter > 0) {
        statement.executeBatch();
        log.info("{} execute batch {} items, total {} items", name, counter, total);
        counter = 0;
      }
    }
  }
  public void close() throws SQLException {
    if(null != statement){
      statement.close();
    }
  }

  private void init() throws SQLException {
    if (null == statement) {
      connection.setAutoCommit(true);
      statement = connection.prepareStatement(sql);
    }
  }
}
