package com.fxiaoke.bi.warehouse.common.component;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.bi.warehouse.common.util.CommonUtils;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.common.Pair;
import com.github.mybatis.tenant.TenantContext;
import com.github.mybatis.tenant.TenantPolicy;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/8/6
 */
@Slf4j
public class MybatisBITenantPolicy implements TenantPolicy {
  @Setter
  private DbRouterClient dbRouterClient;
  public static String BIZ = "BI";
  public static String APPLICATION = "fs-bi-custom-statistic-schedule";
  public static String DIALECT = "postgresql";

  @Override
  public TenantContext get(String tenantId, boolean readOnly) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, GrayManager.isAllowByRule("use-pgbouncer", tenantId));
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (readOnly && GrayManager.isAllowByRule("use-db-slave", tenantId) && routerInfo.getSlaveUrl() != null) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    if (BooleanUtils.isTrue(routerInfo.getStandalone())) {
      return TenantContext.builder()
                          .id(tenantId)
                          .url(jdbcUrl)
                          .username(routerInfo.getUserName())
                          .password(routerInfo.getPassWord())
                          .schema("sch_" + tenantId)
                          .build();
    }
    return TenantContext.builder()
                        .id(tenantId)
                        .url(jdbcUrl)
                        .username(routerInfo.getUserName())
                        .password(routerInfo.getPassWord())
                        .build();
  }

  public String getPgbouncerURL(String dbURL, boolean master) {
    return dbRouterClient.convertPgBouncerUrl(BIZ, dbURL, master);
  }

  public boolean standalone(String tenantId) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, GrayManager.isAllowByRule(
      "use" + "-pgbouncer", tenantId));
    return null != routerInfo.getStandalone() ? routerInfo.getStandalone() : false;
  }

  /**
   * <p>获取非pgbouncer路由地址</p>
   */
  public Pair<String, Boolean> getDBURLAndSchema(String tenantID) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT);
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (GrayManager.isAllowByRule("use-db-slave", tenantID) && StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    return Pair.build(jdbcUrl, null != routerInfo.getStandalone() ? routerInfo.getStandalone() : false);
  }

  /**
   * <p>获取路由路由地址</p>
   */
  public String getPgBouncerJdbcURL(String tenantID, boolean readOnly) {
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT, GrayManager.isAllowByRule("use" +
        "-pgbouncer", tenantID));
      String jdbcUrl = routerInfo.getJdbcUrl();
      if (readOnly && GrayManager.isAllowByRule("use-db-slave", tenantID) && StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
        jdbcUrl = routerInfo.getSlaveUrl();
      }
      return jdbcUrl;
    } catch (Exception e) {
      log.error("getPgBouncerJdbcURL error,tenantId:{}", tenantID, e);
    }
    return null;
  }
  /**
   * <p>获取路由路由地址</p>
   */
  public String getPgBouncerJdbcURL(String tenantID) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT,
      GrayManager.isAllowByRule("use-pgbouncer", tenantID));
    String jdbcUrl = routerInfo.getJdbcUrl();
    if (GrayManager.isAllowByRule("use-db-slave", tenantID) && StringUtils.isNotBlank(routerInfo.getSlaveUrl())) {
      jdbcUrl = routerInfo.getSlaveUrl();
    }
    return jdbcUrl;
  }

  /**
   * <p>获取pg bouncer url</p>
   */
  public String getPgBouncerURLByURL(String jdbcURL, boolean isMaster) {
    return dbRouterClient.convertPgBouncerUrl(BIZ, jdbcURL, isMaster);
  }
  /**
   * <p>获取router info 并且加上了灰度控件</p>
   */
  public RouterInfo getRouterInfo(String tenantID) {
    if (StringUtils.isEmpty(tenantID)) {
      return null;
    }
    RouterInfo routerInfo = null;
    try {
      routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT);
      boolean isBIStandalone = routerInfo.getStandalone();
      routerInfo.setStandalone(isBIStandalone);
    } catch (Exception e) {
      log.warn("getRouterInfo error tenantID:{},errormsg:{}", tenantID, e.getMessage());
    }
    return routerInfo;
  }

  /**
   * 返回pg db名称
   *
   * @param tenantId 租户id
   * @return
   */
  public String getPgDbNameByTenantId(String tenantId) {
    RouterInfo routerInfo = this.getRouterInfo(tenantId);
    if (routerInfo != null) {
      return CommonUtils.getDBName(routerInfo.getJdbcUrl());
    }
    return null;
  }

  /**
   * 批量反查路由信息
   * @param tenantIds
   * @return
   */
  public Map<String, RouterInfo> batchQueryRouterInfo(List<String> tenantIds){
    try{
      return dbRouterClient.batchQueryRouterInfo(tenantIds,BIZ, APPLICATION, DIALECT);
    }catch (Exception e){
      log.error("batchQueryRouterInfo error {}", JSON.toJSONString(tenantIds),e);
    }
    return null;
  }

  public String getPgMasterIp(String tenantId) {
    if (StringUtils.isEmpty(tenantId)) {
      return null;
    }
    String ip = null;
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT);
      String jdbcUrl = routerInfo.getJdbcUrl();
      ip = jdbcUrl.substring(18, jdbcUrl.lastIndexOf(":"));
    } catch (Exception e) {
      log.error("getPgMasterIp error tenantID:{},errormsg:{}", tenantId, e.getMessage());
    }
    return ip;
  }

  /**
   * <p>获取从库的jdbc</p>
   *
   * @param tenantId 企业id
   * @return
   */
  public String getSlaveJdbcUrl(String tenantId) {
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT,
        GrayManager.isAllowByRule("use-pgbouncer", tenantId));
      return routerInfo.getSlaveUrl();
    } catch (Exception e) {
      log.warn("get slave jdbcUrl error tenant_id:{}", tenantId, e);
    }
    return null;
  }

  /**
   * <p>判断是否schema隔离</p>
   */
  public boolean isStandalone(String tenantID) {
    boolean isBIStandalone = false;
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantID, BIZ, APPLICATION, DIALECT);
      isBIStandalone = routerInfo.getStandalone();
    } catch (Exception e) {
      log.error("isStandalone error tenantID:{},errormsg:{}", tenantID, e.getMessage());
    }
    return isBIStandalone;
  }


}
