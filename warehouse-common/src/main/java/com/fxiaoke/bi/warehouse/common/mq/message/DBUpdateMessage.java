package com.fxiaoke.bi.warehouse.common.mq.message;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.rocketmq.common.message.MessageExt;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DBUpdateMessage {
  private String id;
  private String pgDB;
  private String chDB;
  private Long batchNum;
  private String schema;
  /**
   * 下游同步信息id
   */
  private String aggSyncId;
  /**
   * 下游聚合数据批次号
   */
  private Long dsBatchNum;

  public static DBUpdateMessage parseFromMsg(MessageExt messageExt) {
    String json = new String(messageExt.getBody(), Charsets.UTF_8);
    return JSON.parseObject(json, DBUpdateMessage.class);
  }
}
