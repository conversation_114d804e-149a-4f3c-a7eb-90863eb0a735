package com.fxiaoke.bi.warehouse.common.db.er;

import com.fxiaoke.bi.warehouse.common.util.FieldType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public enum PGColumnType {
  Number, Int2, Int4, Int8, String, Date, Boolean, ARRAY_Int16, ARRAY_Int32, ARRAY_Int64, ARRAY_String, LTree, AggFunc;

  public static PGColumnType parse(String name) {
    try {
      if ("bigint".equalsIgnoreCase(name)) {
        return PGColumnType.Int8;
      }
      return PGColumnType.valueOf(name);
    } catch (IllegalArgumentException e) {
      log.error("PGColumnType error {}", name);
    }
    return PGColumnType.String;
  }

  public static PGColumnType parseStatFieldType(String type) {
    switch (type) {
      case "count" -> {
        return PGColumnType.Int8;
      }
      case "sum" -> {
        return PGColumnType.Number;
      }
      case "uniq" -> {
        return PGColumnType.AggFunc;
      }
      default -> {
        return null;
      }
    }
  }

  public ColumnType trans2CHType() {
    String name = this.name();
    switch (name) { //bi业务数据日期没有date类型
      case "Date", "Int2", "Int4", "Int8" -> {
        return ColumnType._int;
      }
      case "Number" -> {
        return ColumnType._Decimal;
      }
      case "String" -> {
        return ColumnType._String;
      }
      case "Boolean" -> {
        return ColumnType._Boolean;
      }
      case "LTree", "ARRAY_Int16", "ARRAY_Int32", "ARRAY_Int64", "ARRAY_String" -> {
        return ColumnType._ARRAY;
      }
      case "AggFunc" -> {
        return ColumnType._AggFunc;
      }
    }
    return null;
  }

  /**
   * 获取数组元素类型类型
   *
   * @return
   */
  public String findItemTypeAlias() {
    switch (this) {
      case ARRAY_Int16 -> {
        return "array_int16";
      }
      case ARRAY_Int32 -> {
        return "array_int32";
      }
      case ARRAY_Int64 -> {
        return "array_int64";
      }
      case ARRAY_String, LTree -> {
        return "array_string";
      }
    }
    return "array_string";
  }

  /**
   * 获取数组元素类型
   *
   * @return
   */
  public ColumnType findItemType() {
    switch (this) {
      case Number, Int2, Int4, Int8, String, Date, Boolean -> {
        return null;
      }
      case ARRAY_Int16, ARRAY_Int32, ARRAY_Int64 -> {
        return ColumnType._int;
      }
      case ARRAY_String, LTree -> {
        return ColumnType._String;
      }
    }
    return null;
  }

  /**
   * 获取维度目标类型
   *
   * @return
   */
  public ColumnType createDimDistType(String fieldType, boolean isSingle) {
    switch (this) {
      case String -> {
        if (StringUtils.equalsAny(fieldType, "date", "date_time","Date", "time")) {
          return ColumnType._ActionDate;
        }
        if (StringUtils.equalsAny(fieldType, "number", "currency", "count", "percentile", "image")) {
          return ColumnType._Decimal;
        }
        if (!isSingle) {
          return ColumnType._ARRAY;
        }
        return ColumnType._String;
      }
      case Int2, Int4 -> {
        if (StringUtils.equalsAny(fieldType, FieldType.TRUE_OR_FALSE, FieldType.SELECT_ONE)) {
          return ColumnType._String;
        }
        return ColumnType._int;
      }
      case Int8 -> {
        if (StringUtils.equalsAny(fieldType, "date", "date_time", "time")) {
          return ColumnType._ActionDate;
        }
        return ColumnType._int;
      }
      case ARRAY_String, LTree, ARRAY_Int16, ARRAY_Int32, ARRAY_Int64 -> {
        return ColumnType._ARRAY;
      }
      case Number -> {
        if (StringUtils.equalsAny(fieldType, "date", "date_time","Date", "time")) {
          return ColumnType._ActionDate;
        }
        return ColumnType._Decimal;
      }
//      case Boolean -> {
//        return ColumnType._Boolean;
//      }
      case Date -> {
        return ColumnType._ActionDate;
      }
    }
    return ColumnType._String;
  }

  /**
   * 创建ch 基本类型强转函数名称
   *
   * @return
   */
  public String createCHCastFunc() {
    switch (this) {
      case Number -> {
        return "toDecimal128";
      }
      case Int2, ARRAY_Int16 -> {
        return "toInt16";
      }
      case Int4, ARRAY_Int32 -> {
        return "toInt32";
      }
      case Int8, ARRAY_Int64 -> {
        return "toInt64";
      }
      case String, ARRAY_String, LTree -> {
        return "toString";
      }
      case Date -> {
        return "toDateTime64";
      }
      case Boolean -> {
        return "toBool";
      }
    }
    return null;
  }
}
