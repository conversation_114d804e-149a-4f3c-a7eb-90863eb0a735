package com.fxiaoke.bi.warehouse.common.util;

import com.facishare.converter.EIEAConverter;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

/**
 * 给本次调用添加trace
 */
@Slf4j
public class TraceUtils {

  private static final String traceFormat = "E-E.%s.%s-%s";

  public static void createTrace(String tenantId, String ea, String traceId, String uid) {
    if (StringUtils.isBlank(traceId)) {
      traceId = String.format(traceFormat, ea, uid, ObjectId.get());
    }
    TraceContext.get().setEi(tenantId);
    TraceContext.get().setEa(ea);
    uid = StringUtils.isBlank(uid) ? "-1000" : uid;
    TraceContext.get().setUid(uid);
    TraceContext.get().setTraceId(traceId);
  }

  public static void createTrace(String tenantId, EIEAConverter eieaConverter, String traceId, String uid) {
    String ea = tenantId;
    try {
      ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
    } catch (Exception e) {
      log.warn("enterpriseIdToAccount error tenantId:{}!", tenantId, e);
    }
    uid = StringUtils.isBlank(uid) ? "-1000" : uid;
    traceId = StringUtils.isBlank(traceId) ? ObjectId.get().toString() : traceId;
    createTrace(tenantId, ea, String.format(traceFormat, ea, uid, traceId), uid);
  }

  /**
   * 根据ei转ea
   * @param tenantId
   * @param eieaConverter
   * @return
   */
  public static String ei2EA(String tenantId, EIEAConverter eieaConverter) {
    String ea = tenantId;
    try {
      int ei = Integer.parseInt(tenantId);
      if (ei > 0) {
        ea = eieaConverter.enterpriseIdToAccount(ei);
      }
    } catch (Exception e) {
      log.warn("enterpriseIdToAccount error tenantId:{}!", tenantId, e);
    }
    return ea;
  }

  public static void removeContext() {
    TraceContext.remove();
  }
}
