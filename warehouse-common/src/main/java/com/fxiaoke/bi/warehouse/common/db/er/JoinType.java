package com.fxiaoke.bi.warehouse.common.db.er;

/**
 * join类型
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public enum JoinType {
  /**
   * left join
   */
  LEFT_JOIN("LEFT JOIN"),
  /**
   * right join
   */
  RIGHT_JOIN("RIGHT JOIN"),
  /**
   * inner join
   */
  INNER_JOIN("INNER JOIN"),
  /**
   * full join
   */
  FULL_JOIN("JOIN");

  private String joinSql;

  JoinType(String joinSql) {
    this.joinSql = joinSql;
  }

  public String getJoinSql() {
    return joinSql;
  }
}
