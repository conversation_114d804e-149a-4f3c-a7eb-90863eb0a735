package com.fxiaoke.bi.warehouse.common.db.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.Map;
import java.util.Set;

@Table(name = "db_table_sync_info")
@Data
public class DbTableSyncInfoDO {
  @Id
  @Column(name = "id")
  private String id;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "db_sync_id")
  private String dbSyncId;
  @Column(name = "table_name")
  private String tableName;
  @Column(name = "max_sys_modified_time")
  private Long maxSysModifiedTime;
  @Column(name = "last_sync_time")
  private Long lastSyncTime;
  @Column(name = "api_name_ei_map")
  private String apiNameEiMap;
  @Column(name = "batch_num")
  private Long batchNum;
  @Column(name = "create_time")
  private Long createTime;
  @Column(name = "last_modified_time")
  private Long lastModifiedTime;
  @Column(name = "is_deleted")
  private Integer isDeleted;
  @Column(name="status")
  private Integer status;
  @Column(name="target_table")
  private String targetTable;
  @Column(name = "src_dialect")
  private String srcDialect;
  /**
   * paas2bi同步增量分区最大变更时间
   */
  @Column(name = "inc_sys_modified_time_from")
  private long incSysModifiedTimeFrom;
  @Column(name = "inc_sys_modified_time_to")
  private long incSysModifiedTimeTo;
  @Column(name = "inc_api_name_ei_map")
  private String incApiNameEiMap;

 public static DbTableSyncInfoDO createFrom(SyncInfo dbSyncInfo, String tableName){
   DbTableSyncInfoDO dbTableSyncInfo= new DbTableSyncInfoDO();
   dbTableSyncInfo.setId(ObjectId.get().toString());
   dbTableSyncInfo.setTenantId("-1");
   dbTableSyncInfo.setDbSyncId(dbSyncInfo.getId());
   dbTableSyncInfo.setTableName(tableName);
   dbTableSyncInfo.setMaxSysModifiedTime(null);
   dbTableSyncInfo.setLastSyncTime(new Date().getTime());
   dbTableSyncInfo.setBatchNum(dbSyncInfo.getBatchNum());
   dbTableSyncInfo.setCreateTime(new Date().getTime());
   dbTableSyncInfo.setLastModifiedTime(new Date().getTime());
   dbTableSyncInfo.setIsDeleted(0);
   dbTableSyncInfo.setTargetTable(tableName);
   dbTableSyncInfo.setSrcDialect("postgresql");
   dbTableSyncInfo.setIncSysModifiedTimeFrom(0);
   dbTableSyncInfo.setIncSysModifiedTimeTo(0);
   dbTableSyncInfo.setIncApiNameEiMap(null);
   return dbTableSyncInfo;
 }

  public long findMaxSysModifiedTime() {
    return this.maxSysModifiedTime == null ? 0L : this.maxSysModifiedTime;
  }

  public static Map<String, Set<String>> parseApiNameEiMap(String apiNameEiMapString) {
    if (StringUtils.isBlank(apiNameEiMapString)) {
      return Maps.newHashMap();
    }
    return JSON.parseObject(apiNameEiMapString, new TypeReference<>() {
    });
  }
}
