package com.fxiaoke.bi.warehouse.common.dag;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @Author:jief
 * @Date:2024/10/26
 */
@Data
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
public class DagEdge {
  private String from;
  private String to;
  private String description;
  private Integer weight;

  public void incWeight(int w) {
    if (weight == null) {
      weight = w;
    }
    this.weight = this.weight + w;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DagEdge dagEdge = (DagEdge) o;
    return from.equals(dagEdge.from) && to.equals(dagEdge.to) && description.equals(dagEdge.description);
  }

  @Override
  public int hashCode() {
    return Objects.hash(from, to, description);
  }
}
