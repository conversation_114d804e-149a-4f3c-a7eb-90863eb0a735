package com.fxiaoke.bi.warehouse.common.db.er;

/**
 * <AUTHOR>
 * @since 2023/4/3
 */
public class TableAliasNaming {
  public static String alias(String table, int index) {
    return table + "_" + index;
  }

  public static String tableName(String alias) {
    int splitIdx = alias.lastIndexOf("_");
    if (splitIdx == -1) {
      return alias;
    }
    return alias.substring(0, splitIdx);
  }

  public static int index(String alias) {
    int splitIdx = alias.lastIndexOf("_");
    if (splitIdx == -1) {
      return 0;
    }
    return Integer.parseInt(alias.substring(splitIdx + 1));
  }
}

