package com.fxiaoke.bi.warehouse.common.db.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.bi.warehouse.common.bean.BIPair;
import com.fxiaoke.bi.warehouse.common.component.BIPgDataSource;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.entity.DbSyncInfoFlowDO;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Array;
import java.sql.ResultSet;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * @Author:jief
 * @Date:2024/7/30
 */
@Slf4j
public class DbSyncInfoFlowDao {
  @Setter
  private BIPgDataSource biPgDataSource;

  /**
   * 插入或更新db sync info flow
   * @param pgDbURL
   * @param pgSchema
   * @param dbSyncInfoFlowDOs
   * @return
   */
  public int upsertDbSyncInfoFlow(String pgDbURL, String pgSchema, List<DbSyncInfoFlowDO> dbSyncInfoFlowDOs){
    String upsertSQL= String.format("""
      insert into %s.db_sync_info_flow (id, tenant_id, db_sync_id, batch_nums, partition_name, api_name_ei_map, create_time,
                                     last_modified_time, status, version,is_deleted,last_sync_time,max_sys_modified_time,inc_sys_modified_time_range)
      values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)
      ON CONFLICT(id,tenant_id)
      DO UPDATE set batch_nums=EXCLUDED.batch_nums,
                    partition_name=EXCLUDED.partition_name,
                    api_name_ei_map=EXCLUDED.api_name_ei_map,
                    last_modified_time=EXCLUDED.last_modified_time,
                    status=EXCLUDED.status,
                    version=db_sync_info_flow.version+1,
                    is_deleted=EXCLUDED.is_deleted,
                    last_sync_time=EXCLUDED.last_sync_time,
                    max_sys_modified_time=EXCLUDED.max_sys_modified_time,
                    inc_sys_modified_time_range=EXCLUDED.inc_sys_modified_time_range
      where db_sync_info_flow.version=?
      """,pgSchema);
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      return jdbcConnection.prepareUpdateBatch(upsertSQL, preparedStatement -> {
        for(DbSyncInfoFlowDO dbSyncInfoFlowDO : dbSyncInfoFlowDOs){
          preparedStatement.setString(1, dbSyncInfoFlowDO.getId());
          preparedStatement.setString(2, dbSyncInfoFlowDO.getTenantId());
          preparedStatement.setString(3, dbSyncInfoFlowDO.getDbSyncId());
          preparedStatement.setObject(4, dbSyncInfoFlowDO.getBatchNums());
          preparedStatement.setString(5, dbSyncInfoFlowDO.getPartitionName());
          preparedStatement.setString(6, dbSyncInfoFlowDO.getApiNameEiMap());
          preparedStatement.setLong(7, dbSyncInfoFlowDO.getCreateTime());
          preparedStatement.setLong(8, new Date().getTime());
          preparedStatement.setInt(9, dbSyncInfoFlowDO.getStatus());
          preparedStatement.setInt(10, dbSyncInfoFlowDO.getVersion());
          preparedStatement.setInt(11, dbSyncInfoFlowDO.getIsDeleted());
          preparedStatement.setLong(12, dbSyncInfoFlowDO.getLastSyncTime());
          preparedStatement.setString(13, dbSyncInfoFlowDO.getMaxSysModifiedTime());
          preparedStatement.setString(14, dbSyncInfoFlowDO.getIncSysModifiedTimeRange());
          preparedStatement.setInt(15, dbSyncInfoFlowDO.getVersion());
          preparedStatement.addBatch();
        }
      });
    } catch (Exception e) {
      log.error("upsertDbSyncInfoFlow error", e);
    }
    return 0;
  }

  /**
   * 处理
   */
  private final Function<ResultSet, DbSyncInfoFlowDO> createDbSyncInfoFlowFunction = (ResultSet r) -> {
    try {
      DbSyncInfoFlowDO dbSyncInfoFlowDO = new DbSyncInfoFlowDO();
      dbSyncInfoFlowDO.setId(r.getString("id"));
      dbSyncInfoFlowDO.setTenantId(r.getString("tenant_id"));
      dbSyncInfoFlowDO.setDbSyncId(r.getString("db_sync_id"));
      Array batchNums = r.getArray("batch_nums");
      if (batchNums != null) {
        dbSyncInfoFlowDO.setBatchNums((Long[]) batchNums.getArray());
      }
      dbSyncInfoFlowDO.setPartitionName(r.getString("partition_name"));
      dbSyncInfoFlowDO.setApiNameEiMap(r.getString("api_name_ei_map"));
      dbSyncInfoFlowDO.setCreateTime(r.getLong("create_time"));
      dbSyncInfoFlowDO.setLastModifiedTime(r.getLong("last_modified_time"));
      Object status = r.getObject("status");
      if (status != null) {
        dbSyncInfoFlowDO.setStatus((int) status);
      }
      dbSyncInfoFlowDO.setVersion(r.getInt("version"));
      dbSyncInfoFlowDO.setIsDeleted(r.getInt("is_deleted"));
      dbSyncInfoFlowDO.setMaxSysModifiedTime(r.getString("max_sys_modified_time"));
      dbSyncInfoFlowDO.setLastSyncTime(r.getLong("last_sync_time"));
      dbSyncInfoFlowDO.setIncSysModifiedTimeRange(r.getString("inc_sys_modified_time_range"));
      return dbSyncInfoFlowDO;
    } catch (Exception e) {
      throw new RuntimeException("build DbSyncInfoFlowDO error", e);
    }
  };

  /**
   * 创建同步流 非线程安全
   * @param pgDbURL
   * @param pgSchema
   * @param dbSyncId
   * @return
   */
  public DbSyncInfoFlowDO queryDbSyncFlowAsSyncIng(String pgDbURL, String pgSchema, String dbSyncId,boolean orderBy, int limit) {
    List<DbSyncInfoFlowDO> syncingInfoFlow = this.queryDbSyncFlow(pgDbURL, pgSchema, dbSyncId, SyncStatusEnum.SYNC_ING.getStatus(),orderBy,limit);
    if (CollectionUtils.isEmpty(syncingInfoFlow)) {
      DbSyncInfoFlowDO dbSyncInfoFlowDO = DbSyncInfoFlowDO.of(dbSyncId, SyncStatusEnum.SYNC_ING.getStatus(), WarehouseConfig.INC_PARTITION_NAME);
      this.upsertDbSyncInfoFlow(pgDbURL, pgSchema, Lists.newArrayList(dbSyncInfoFlowDO));
      return dbSyncInfoFlowDO;
    } else if (syncingInfoFlow.size() > 1) {
      throw new RuntimeException(String.format("queryDbSyncIngFlow result more than one pgDbURL:%s,pgSchema:%s,dbSyncId:%s", pgDbURL, pgSchema, dbSyncId));
    }
    return syncingInfoFlow.getFirst();
  }

  /**
   * 查询同步中的flow正常情况下最多有一个
   * @param pgDbURL
   * @param pgSchema
   * @param dbSyncId
   * @return
   */
  public List<DbSyncInfoFlowDO> queryDbSyncFlow(String pgDbURL, String pgSchema, String dbSyncId, int status,boolean orderBy, int limit) {
    String sql = String.format("select * from %s.db_sync_info_flow where db_sync_id='%s' and status=%d and is_deleted=0 %s %s", pgSchema, dbSyncId, status,orderBy? " order by  create_time asc ":"",limit >0?" limit "+limit:"");
    List<DbSyncInfoFlowDO> syncInfoFlowDOS = Lists.newArrayList();
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      jdbcConnection.query(sql, resultSet -> {
        while (resultSet.next()) {
          DbSyncInfoFlowDO dbSyncInfoFlowDO = createDbSyncInfoFlowFunction.apply(resultSet);
          if (dbSyncInfoFlowDO != null) {
            syncInfoFlowDOS.add(dbSyncInfoFlowDO);
          }
        }
      });
    } catch (Exception e) {
      throw new RuntimeException(String.format("querySyncingFlow error dbSyncId:%s,pgBdURL:%s", dbSyncId, pgDbURL), e);
    }
    return syncInfoFlowDOS;
  }

  /**
   * 汇总所有变更的对象和租户变更信息
   *  获取变更表的最大更新时间
   * @param dbSyncInfoFlowDO
   * @param dbTableSyncInfos
   */
  public void mergeApiNameEiMap(DbSyncInfoFlowDO dbSyncInfoFlowDO,
                                List<DbTableSyncInfoDO> dbTableSyncInfos,
                                AtomicLong nextBatchNum) {
    if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
      String allApiNameEiMap = dbSyncInfoFlowDO.getApiNameEiMap();
      String maxSysModifiedTime = dbSyncInfoFlowDO.getMaxSysModifiedTime();
      String incSysModifiedTimeRange = dbSyncInfoFlowDO.getIncSysModifiedTimeRange();
      Map<String, Map<String,Set<String>>> apiNameEiJSONObj=this.parseJsonOrDefault(allApiNameEiMap,new TypeReference<>() {},Maps::newHashMap);
      Map<String,Long> maxSysModifiedTimeMap=this.parseJsonOrDefault(maxSysModifiedTime,new TypeReference<>() {},Maps::newHashMap);
      Map<String,BIPair<Long,Long>> incSysModifiedTimeRangeMap=this.parseJsonOrDefault(incSysModifiedTimeRange,new TypeReference<>() {},Maps::newHashMap);
      dbTableSyncInfos.forEach(tableSyncInfoDO -> {
        String tableName = tableSyncInfoDO.getTableName();
        if (StringUtils.isNotBlank(tableSyncInfoDO.getApiNameEiMap()) && !Objects.equals("{}", tableSyncInfoDO.getApiNameEiMap())) {
          Map<String, Set<String>> tableApiEiMap = JSON.parseObject(tableSyncInfoDO.getApiNameEiMap(), new TypeReference<>() {});
          if (tableApiEiMap != null) {
            this.mergeApiNameEi(apiNameEiJSONObj, tableApiEiMap, tableName);
            maxSysModifiedTimeMap.compute(tableName, (k, v) -> { //获取变更表的最大更新时间
              long maxSysModifiedTime1 = tableSyncInfoDO.getMaxSysModifiedTime() == null ? 0L : tableSyncInfoDO.getMaxSysModifiedTime();
              if (v != null) {
                return Math.max(v, maxSysModifiedTime1);
              }
              return maxSysModifiedTime1;
            });
          }
        }
        //处理增量区间的变更信息，包含apiName ei 变更信息，以及增量区间
        if(StringUtils.isNotBlank(tableSyncInfoDO.getIncApiNameEiMap()) && !Objects.equals("{}", tableSyncInfoDO.getIncApiNameEiMap())) {
          Map<String, Set<String>> incTableApiEiMap = JSON.parseObject(tableSyncInfoDO.getIncApiNameEiMap(), new TypeReference<>() {});
          if (incTableApiEiMap != null) {
            this.mergeApiNameEi(apiNameEiJSONObj, incTableApiEiMap, tableName);
            maxSysModifiedTimeMap.compute(tableName, (k, v) -> { //获取变更表的最大更新时间
              long maxSysModifiedTime1 = tableSyncInfoDO.getIncSysModifiedTimeTo();
              if (v != null) {
                return Math.max(v, maxSysModifiedTime1);
              }
              return maxSysModifiedTime1;
            });
            incSysModifiedTimeRangeMap.compute(tableName, (k, v) -> {
              long incSysModifiedTimeFrom = tableSyncInfoDO.getIncSysModifiedTimeFrom();
              long incSysModifiedTimeTo = tableSyncInfoDO.getIncSysModifiedTimeTo();
              if (v != null) {
                return BIPair.of(Math.min(v.first, incSysModifiedTimeFrom), Math.max(v.second, incSysModifiedTimeTo));
              } else {
                return BIPair.of(incSysModifiedTimeFrom, incSysModifiedTimeTo);
              }
            });
          }
        }
      });
      dbSyncInfoFlowDO.setApiNameEiMap(JSON.toJSONString(apiNameEiJSONObj));
      dbSyncInfoFlowDO.setMaxSysModifiedTime(JSON.toJSONString(maxSysModifiedTimeMap));
      dbSyncInfoFlowDO.setIncSysModifiedTimeRange(JSON.toJSONString(incSysModifiedTimeRangeMap));
    } else {
      log.warn("mergeApiNameEiMap dbTableSyncInfos is empty dbSyncId:{},batchNums:{}", dbSyncInfoFlowDO.getDbSyncId(), nextBatchNum.get());
    }
    Long[] batchNums = dbSyncInfoFlowDO.getBatchNums();
    if (batchNums == null) {
      batchNums = new Long[] {nextBatchNum.get()};
      dbSyncInfoFlowDO.setBatchNums(batchNums);
    } else {
      Long[] newArray = new Long[batchNums.length + 1];
      System.arraycopy(batchNums, 0, newArray, 0, batchNums.length);
      newArray[newArray.length - 1] = nextBatchNum.get();
      dbSyncInfoFlowDO.setBatchNums(newArray);
    }
    log.info("mergeApiNameEiMap dbSyncId:{},batchNums:{}",dbSyncInfoFlowDO.getDbSyncId(),nextBatchNum.get());
  }

  private void mergeApiNameEi(Map<String, Map<String,Set<String>>> apiNameEiJSONObj,Map<String, Set<String>> tableApiEiMap,String tableName){
    if (WarehouseConfig.ch2chTablePair.containsKey(tableName)) { //ch 2 ch 真正变更的表是被同步的表，并且是被变更的表触发计算。
      tableName = WarehouseConfig.ch2chTablePair.get(tableName);
    }
    apiNameEiJSONObj.compute(tableName, (key, value) -> {
      if (value == null) {
        return tableApiEiMap;
      } else {
        tableApiEiMap.forEach((k, v) -> value.computeIfAbsent(k, k1 -> new HashSet<>()).addAll(v));
        return value;
      }
    });
  }

  /**
   * 删除执行过的增量记录
   * @param pgDbURL
   * @param pgSchema
   * @param ids
   * @return
   */
  public int deleteDbSyncInfoFlow(String pgDbURL, String pgSchema, List<String> dbSyncIds,List<String> ids,long batchNum) {
    long createTimeThreshold = System.currentTimeMillis() - WarehouseConfig.dbSyncFlowRetentionMs;
    String inDbSyncIds = JoinHelper.joinSkipNullOrBlank(',', '\'', dbSyncIds);
    String dSql = String.format("delete from %s.db_sync_info_flow where db_sync_id in(%s) and is_deleted=1 and create_time <= %d ", pgSchema, inDbSyncIds, createTimeThreshold);
    String inIds = JoinHelper.joinSkipNullOrBlank(',', '\'', ids);
    String uSql = String.format("update %s.db_sync_info_flow set is_deleted=1,version=version+1,cal_batch_num=%d,last_modified_time=%d where id in (%s)", pgSchema, batchNum, new Date().getTime(),inIds);
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      return jdbcConnection.executeUpdate(uSql,dSql);
    } catch (Exception e) {
      log.error("deleteDbSyncInfoFlow error pgDbURL:{},pgSchema:{},deleteSQL:{},updateSQL{}", pgDbURL, pgSchema,dSql, uSql, e);
    }
    return 0;
  }

  /**
   * 查询所有增量的分区名称，目前默认都是i
   * @param pgDbURL jdbcUrl
   * @param pgSchema pgSchema
   * @param dbSyncId db sync id
   * @return
   */
  public List<String> queryPartitions(String pgDbURL, String pgSchema, String dbSyncId){
    List<String> partitionNames = Lists.newArrayList();
    String sql = String.format("select DISTINCT partition_name from %s.db_sync_info_flow where db_sync_id='%s' and is_deleted=0", pgSchema, dbSyncId);
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      jdbcConnection.query(sql, resultSet -> {
        while (resultSet.next()) {
          partitionNames.add(resultSet.getString("partition_name"));
        }
      });
    } catch (Exception e) {
      throw new RuntimeException(String.format("queryPartitions error dbSyncId:%s,pgBdURL:%s", dbSyncId, pgDbURL), e);
    }
    return partitionNames;
  }

  private <T> T parseJsonOrDefault(String jsonStr, TypeReference<T> typeRef, Supplier<T> defaultValue) {
    if (StringUtils.isNotBlank(jsonStr) && !Objects.equals("{}", jsonStr)) {
      return JSON.parseObject(jsonStr, typeRef);
    }
    return defaultValue.get();
  }
}
