package com.fxiaoke.bi.warehouse.common.db.er;

import lombok.Data;

import java.util.Objects;

/**
 * 对象列
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Data
public class NodeColumn {
  /**
   * 列名
   */
  private String name;
  /**
   * 列名别名用于嵌套查询使用
   */
  private String aliasName;
  /**
   * 列类型
   */
  private ColumnType type;

  /**
   * 数组元素类型
   */
  private ColumnType itemType;
  /**
   * 元数据描述
   */
  private String fieldType;
  /**
   * 是否是单值，多值
   */
  private Integer isSingle;
  /**
   * 是否开启多语
   */
  private boolean enableMultiLang;


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NodeColumn that = (NodeColumn) o;
    return name.equals(that.name) && Objects.equals(aliasName, that.aliasName) && type == that.type &&
      itemType == that.itemType && Objects.equals(fieldType, that.fieldType) && Objects.equals(isSingle, that.isSingle);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, aliasName, type, itemType, fieldType, isSingle);
  }
}
