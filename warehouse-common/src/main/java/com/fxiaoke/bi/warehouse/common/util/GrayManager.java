package com.fxiaoke.bi.warehouse.common.util;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import lombok.experimental.UtilityClass;


@UtilityClass
public class GrayManager {
  private static final FsGrayReleaseBiz onlineGray = FsGrayRelease.getInstance("bi-statistic-online");
  /**
   * 是否支持多时区
   *
   * @param tenantId 企业id
   * @return boolean
   */
  public static boolean supportMultipleTimeZone(String tenantId) {
    return onlineGray.isAllow("multipleTimeZonePhase2", tenantId);
  }

  public static boolean isAllowByRule(String rule, String tenantId) {
    return onlineGray.isAllow(rule, tenantId);
  }
}
