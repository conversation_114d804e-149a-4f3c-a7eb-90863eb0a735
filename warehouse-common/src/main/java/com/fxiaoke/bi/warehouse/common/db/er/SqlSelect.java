package com.fxiaoke.bi.warehouse.common.db.er;

import com.fxiaoke.bi.warehouse.common.util.TemplateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @param order              顺序
 * @param select             select表达式
 * @param defaultStringValue 默认String值
 * @param alias              别名
 * @param groupBy            是否group by
 * <AUTHOR>
 * @since 2023/3/21
 */
@Slf4j
public record SqlSelect(int order, String select, String defaultStringValue, String alias, boolean groupBy,
                        String defaultValueFunTemplate) {

  /**
   * 构建select item sql
   *
   * @return
   */
  public String buildSelectSql() {
    return select + " AS " + alias;
  }

  public String buildSelectSqlWithDefault() {
    Map<String, Object> parseMap = Map.of("checkValue", select, "defaultValue", defaultStringValue);
    return TemplateUtil.replace(defaultValueFunTemplate, parseMap) + " AS " + alias;
  }

  public String buildSelectSqlWithDefaultWithoutAS() {
    return buildSelectSQLWithDefault(defaultValueFunTemplate, select, defaultStringValue);
  }

  public String buildSelectSqlAliasWithDefaultWithoutAS() {
    return buildSelectSQLWithDefault(defaultValueFunTemplate, alias, defaultStringValue);
  }

  public static String buildSelectSQLWithDefault(String functionName, String checkValue, String defaultValue) {
    Map<String, Object> parseMap = Map.of("checkValue", checkValue, "defaultValue", defaultValue);
    return TemplateUtil.replace(functionName, parseMap);
  }
}
