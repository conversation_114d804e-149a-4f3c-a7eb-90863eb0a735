package com.fxiaoke.bi.warehouse.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fxiaoke.common.http.handler.AsyncCallback;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/4/9
 */
@Slf4j
@Component
public class OkHttpClient {

  @Autowired
  @Qualifier("erOkHttpSupport")
  private OkHttpSupport client;

  public OkHttpClient(OkHttpSupport client) {
    this.client = client;
  }

  private OkHttpClient() {
  }

  public JSONObject executeCallBack(String url, Map<String, Object> headers) {
    log.info("http get url:{},header:{}", url, headers);
    Request.Builder builder = new Request.Builder();
    if (MapUtils.isNotEmpty(headers)) {
      headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
    }
    Request request = builder.url(url).build();
    return (JSONObject) client.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (response != null) {
          if (response.isSuccessful()) {
            String res = response.body().string();
            JSONObject jsonObject = (JSONObject) JSON.parse(res, Feature.config(0, Feature.UseBigDecimal, false));
            log.info("http get url:{},code: {} result:{}", url, response.code(), JSON.toJSONString(jsonObject));
            return jsonObject;
          } else {
            log.info("http get url:{}, code: {}, message:{}", url, response.code(), response.message());
          }
        } else {
          log.info("http get url:{}, response is null", url);
        }
        return new JSONObject();
      }
    });
  }

  public JSONObject get(String url, Map<String, Object> headers) {
    try {
      return executeCallBack(url, headers);
    } catch (Exception e) {
      log.error("OkHttpClient.get() Error, url:{}, headers:{}", url, headers);
      log.error("OkHttpClient.get() Error", e);
    }
    return new JSONObject();
  }

  public JSONObject getException(String url, Map<String, Object> headers) throws Exception {
    return executeCallBack(url, headers);
  }

  public JSONObject post(String url, Map<String, String> headers, String entity, String type) throws Exception {
    MediaType mediaType = MediaType.parse(type);
    RequestBody body = RequestBody.create(mediaType, entity);
    Request.Builder builder = new Request.Builder();
    if (MapUtils.isNotEmpty(headers)) {
      headers.forEach((k, v) -> builder.addHeader(k, v));
    }
    Request request = builder.url(url).post(body).build();
    return (JSONObject) client.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        String res = response.body().string();
        if (response.isSuccessful()) {
          return JSONObject.parseObject(res);
        } else {
          throw new RuntimeException("response is unSuccessful ! response is :" + res);
        }
      }
    });
  }

  public JSONObject post(String url, Map<String, Object> headers, Object entity, String type) {
    try {
      MediaType mediaType = MediaType.parse(type);
      RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(entity,
        SerializerFeature.WriteMapNullValue));
      Request.Builder builder = new Request.Builder();
      if (MapUtils.isNotEmpty(headers)) {
        headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
      }
      Request request = builder.url(url).post(body).build();
      return (JSONObject) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response.isSuccessful()) {
            String res = response.body().string();
            JSONObject jsonObject = JSONObject.parseObject(res);
            return jsonObject;
          } else {
            log.error("http post url:{}，header:{},request:{}, code: {}, response:{}", url, JSON.toJSONString(headers)
              , JSON.toJSONString(entity), response.code(), response.body()
                                                                                                                                                                           .string());
            return new JSONObject();
          }
        }
      });
    } catch (Exception e) {
      log.error("OkHttpClient.post() Error, url:{}, headers:{}, entity:{}, mediaType:{}", url, headers,
        JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue), type, e);
    }
    return new JSONObject();
  }

  /**
   * 异步执行 post请求
   */
  public void asyncPost(String url, Map<String, Object> headers, Object entity, String type) {
    log.info("post url:{}, headers:{}, entity:{}", url, JSON.toJSON(headers), JSON.toJSON(entity));
    try {
      Request.Builder builder = new Request.Builder();
      if (MapUtils.isNotEmpty(headers)) {
        headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
      }
      RequestBody body = RequestBody.create(MediaType.parse(type), JSONObject.toJSONString(entity));
      Request request = builder.url(url).post(body).build();
      client.asyncExecute(request, new AsyncCallback() {
        @Override
        public void response(Response response) throws IOException {
          if (response != null) {
            if (response.isSuccessful()) {
              String res = response.body().string();
              JSONObject jsonObject = JSONObject.parseObject(res);
              log.info("async http post url:{}, code: {}, result:{}", url, response.code(),
                JSON.toJSONString(jsonObject));
            } else {
              log.info("async http post url:{}, code: {}, message:{}", url, response.code(), response.message());
            }
          } else {
            log.info("async http post url:{}, response is null", url);
          }
        }
      });
    } catch (Exception e) {
      log.error("OkHttpClient.post() Error, url:{}, headers:{}, entity:{}, mediaType:{}", url, headers, entity, type);
      log.error("OkHttpClient.post() Error", e);
    }
  }

  public JSONObject post(String url, Map<String, Object> headers, Object entity, String type, String ruleId) {
    try {
      MediaType mediaType = MediaType.parse(type);
      RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(entity,
        SerializerFeature.WriteMapNullValue));
      Request.Builder builder = new Request.Builder();
      if (MapUtils.isNotEmpty(headers)) {
        headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
      }
      Request request = builder.url(url).post(body).build();

      return (JSONObject) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response != null) {
            String res = response.body().string();
            if (response.isSuccessful()) {
              JSONObject jsonObject = JSONObject.parseObject(res);
              //              log.info("ruleId:{} http post url:{}，header:{},request:{}, code: {}, response:{}",
              //              ruleId, url, JSON.toJSONString(headers), JSON.toJSONString(entity), response.code(),
              //              JSONObject.parseObject(res));
              return jsonObject;
            } else {
              log.error("errorRuleId:{},http post url:{},header:{},request:{},code:{},response:{}", ruleId, url,
                JSON.toJSONString(headers), JSON.toJSONString(entity), response.code(), JSONObject.parseObject(res));
            }
          } else {
            log.error("errorRuleId:{},http post url:{},header:{},body:{},code:{}", ruleId, url,
              JSON.toJSONString(headers), JSON.toJSONString(entity), response.code());
          }
          return new JSONObject();
        }
      });
    } catch (Exception e) {
      log.error("errorRuleId:{},OkHttpClient.post()Error,url:{},headers:{},entity:{},mediaType:{}", ruleId, url,
        headers, JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue), type);
    }
    return new JSONObject();
  }


  public JSONObject post(String url, Map<String, Object> headers, String entity, String type, String ruleId) {
    try {
      MediaType mediaType = MediaType.parse(type);
      RequestBody body = RequestBody.create(mediaType, entity);

      Request.Builder builder = new Request.Builder();


      if (MapUtils.isNotEmpty(headers)) {
        headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
      }
      Request request = builder.url(url).post(body).build();

      return (JSONObject) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response != null) {
            String res = response.body().string();
            if (response.isSuccessful()) {
              return JSONObject.parseObject(res);
            } else {
              log.error("errorRuleId:{},http post url:{},header:{},request:{},code:{},response:{}", ruleId, url,
                JSON.toJSONString(headers), JSON.toJSONString(entity), response.code(), JSONObject.parseObject(res));
            }
          } else {
            log.error("errorRuleId:{},http post url:{},header:{},body:{},code:{}", ruleId, url,
              JSON.toJSONString(headers), JSON.toJSONString(entity), response.code());
          }
          return new JSONObject();
        }
      });
    } catch (Exception e) {
      log.error("errorRuleId:{},OkHttpClient.post()Error,url:{},headers:{},entity:{},mediaType:{}", ruleId, url,
        headers, JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue), type);
    }
    return new JSONObject();
  }

}
