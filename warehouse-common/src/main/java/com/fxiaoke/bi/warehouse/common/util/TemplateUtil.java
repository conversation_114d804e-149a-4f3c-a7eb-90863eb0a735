package com.fxiaoke.bi.warehouse.common.util;

import com.google.common.base.Preconditions;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateExceptionHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;

import java.io.StringReader;
import java.io.Writer;
import java.util.Map;

/**
 * s
 * <p>模板引擎</p>
 * Created by jief on 2016/7/29.
 *
 * <AUTHOR>
 */
public class TemplateUtil {

  private static final Configuration cfg = new Configuration(Configuration.VERSION_2_3_21);

  static {
    cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
    cfg.setDefaultEncoding("UTF-8");
    cfg.setDateFormat("yyyy-MM-dd HH:mm:ss");
    cfg.setDateFormat("yyyy-MM-dd");
    cfg.setTimeFormat("HH:mm:ss");
  }

  /**
   * <p>解析模板 此处不负责关闭输出流</p>
   *
   * @param template template 模板
   * @param root     root  根对象
   * @param writer   writer 输出流
   */
  public static void parseTemplate(String template, Object root, Writer writer) throws Exception {
    Preconditions.checkArgument(StringUtils.isNotBlank(template), "template can not  be null！");
    Preconditions.checkArgument(root != null, "obj is null");
    Template t = new Template("field_Temple", new StringReader(template), cfg);
    t.process(root, writer);
  }

  public static String replace(String source,
                               Map<String, Object> parameter,
                               String prefix,
                               String suffix,
                               boolean enableSubstitutionInVariables) {
    //StrSubstitutor不是线程安全的类
    StringSubstitutor stringSubstitutor = new StringSubstitutor(parameter, prefix, suffix);
    //是否在变量名称中进行替换
    stringSubstitutor.setEnableSubstitutionInVariables(enableSubstitutionInVariables);
    return stringSubstitutor.replace(source);
  }

  public static String replace(String source, Map<String, Object> parameter) {
    Preconditions.checkArgument(StringUtils.isNotBlank(source), "source can not  be null！");
    Preconditions.checkArgument(parameter != null, "parameter is null");
    return replace(source, parameter, "${", "}", false);
  }

}
