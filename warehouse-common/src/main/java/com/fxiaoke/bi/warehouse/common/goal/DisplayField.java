package com.fxiaoke.bi.warehouse.common.goal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @Author:jief
 * @Date:2023/7/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisplayField {
  /**
   * 对象apiName
   */
  private String describeApiName;
  /**
   * udf_obj_field db_field_name
   */
  private String dbFieldName;
  /**
   * 作为统计分析时候维度字段的唯一标识，如:stat_field 表中的field_id<br>
   * 目标中check_level_code,比如固定 object_id,action_date<br>
   */
  private String statFieldId;
  /**
   * 自定义维度的dimension_id
   */
  private String dimensionId;
  /**
   * 自定义维度配置信息
   */
  private String dimensionConfig;
  /**
   * 自定义维度分组类型
   */
  private String customType;
  /**
   * ch对象槽位列名称
   */
  private String dstColumnName;
  /**
   * 是否是考核维度，用于判断是否需要联查goal_value表
   */
  private boolean checkLevelField;
  /**
   * 是否需要联查name字段，只有查找关联字段和employee department类型需要
   */
  private boolean lookupName;
  /**
   * 是否是多值
   */
  private int isSingle;
  /**
   * 图表展示类型
   */
  private DisplayType disPlayType;
  /**
   * 自定义维度引用字段 stat_field.field_id
   */
  private String relationFieldId;
  /**
   * stat_field.field_id 枚举类型的自定义维度使用
   */
  private String sourceDimensionId;

  public enum DisplayType{
    group,filter,agg,aggFilter
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DisplayField that = (DisplayField) o;
    return statFieldId.equals(that.statFieldId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(statFieldId);
  }
}
