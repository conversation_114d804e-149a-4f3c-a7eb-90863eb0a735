package com.fxiaoke.bi.warehouse.common.util;

import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.experimental.UtilityClass;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author:jief
 * @Date:2023/4/12
 */
@UtilityClass
public class ObjectConfigManager {
  private static final String UDEF_SUFFIX = "_udef";
  private static Map<String, String> BI_EXTEND_OBJ_MAPP = Maps.newHashMap();
  private static Map<String, String> BI_EXTEND_OBJ_REVERT_MAPP = Maps.newHashMap();
  static {
    ConfigFactory.getConfig("fs-bi-statistic-offline", config -> {
      Map<String, String> BI_EXTEND_OBJ_MAPP_TMP = Splitter.on(',')
                                                           .trimResults()
                                                           .omitEmptyStrings()
                                                           .withKeyValueSeparator(":")
                                                           .split(config.get("biExtendObjMapp", ""));
      BI_EXTEND_OBJ_MAPP = BI_EXTEND_OBJ_MAPP_TMP;
      BI_EXTEND_OBJ_REVERT_MAPP = BI_EXTEND_OBJ_MAPP.entrySet()
                                                    .stream()
                                                    .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
    });
  }

  public static String getExtendObjName(String biObjName) {
    if (biObjName.contains(UDEF_SUFFIX)) {
      return biObjName;
    }
    return BI_EXTEND_OBJ_MAPP.getOrDefault(biObjName, biObjName + UDEF_SUFFIX);
  }

  public static String getPreObjName(String biObjName) {
    if (biObjName.contains(UDEF_SUFFIX)) {
      return BI_EXTEND_OBJ_REVERT_MAPP.getOrDefault(biObjName, biObjName).replaceAll(UDEF_SUFFIX, "");
    }
    return biObjName;
  }
}
