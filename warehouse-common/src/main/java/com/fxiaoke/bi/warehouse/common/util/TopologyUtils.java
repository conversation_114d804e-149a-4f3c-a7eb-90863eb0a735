package com.fxiaoke.bi.warehouse.common.util;

import com.fxiaoke.bi.warehouse.common.db.er.CHFunction;
import com.fxiaoke.helper.StringHelper;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * @Author:jief
 * @Date:2023/4/15
 */
@UtilityClass
public class TopologyUtils {
  private static final Pattern regx = Pattern.compile("bi_mt_data_tag_v\\(tenantId='(\\d+)',apiName='(.+)'\\)");

  public static String createAlias(Map<String, AtomicInteger> aliasMapper, String tableName) {
    if (aliasMapper == null) {
      return null;
    }
    if (StringUtils.isBlank(tableName)) {
      throw new RuntimeException("tableName is empty");
    }
    Matcher matcher = regx.matcher(tableName);
    if (matcher.matches()) {
      tableName = String.format("bi_mt_data_tag_v_%s_%s", matcher.group(1), matcher.group(2));
    }
    int aliasNum = aliasMapper.computeIfAbsent(tableName, key -> new AtomicInteger(0)).incrementAndGet();
    return tableName + "_" + aliasNum;
  }

  public static String createDimAlias(Map<String, AtomicInteger> aliasMapper, String dimField, String pre) {
    if (aliasMapper == null) {
      return null;
    }
    if (StringUtils.isBlank(dimField)) {
      throw new RuntimeException("dimField is empty");
    }
    int aliasNum = aliasMapper.computeIfAbsent(dimField, key -> new AtomicInteger(0)).incrementAndGet();
    return pre + "_" + aliasNum;
  }

  public static String createAggAlias(Map<String, AtomicInteger> aliasMapper, String aggField, String pre) {
    if (aliasMapper == null) {
      return null;
    }
    if (StringUtils.isBlank(aggField)) {
      throw new RuntimeException("aggField is empty");
    }
    int aliasNum = aliasMapper.computeIfAbsent(aggField, key -> new AtomicInteger(0)).incrementAndGet();
    return pre + "_" + aliasNum;
  }

  /**
   * 如果包含大写字符需要加上双引号
   *
   * @param column 列名
   * @return
   */
  public static String formatUpperColumn(String column) {
    if (StringHelper.containUpperChar(column) && !column.startsWith("\"") && !column.endsWith("\"")) {
      return "\"" + column + "\"";
    }
    return column;
  }

  /**
   * 获取所有表的flag字段
   *
   * @param allTableAlias 所有表的别名
   * @return
   */
  public static String[] createAllFlag(Set<String> allTableAlias) {
    return allTableAlias.stream().flatMap(alias -> {
      if (alias.startsWith("bi_mt_data_tag_v") && !GrayManager.isAllowByRule("bi_mt_data_tag_v_eis", alias.split("_")[5])) {
        return Stream.of(CHFunction.ifNull.build(String.format("%s.mdtu_bi_sys_flag", alias), "1"),
          CHFunction.ifNull.build(String.format("%s.mst_bi_sys_flag", alias), "1"),
          CHFunction.ifNull.build(String.format("%s.mtt_bi_sys_flag", alias), "1"));
      }
      return Stream.of(CHFunction.ifNull.build(String.format("%s.bi_sys_flag", alias), "1"));
    }).toArray(String[]::new);
  }
}
