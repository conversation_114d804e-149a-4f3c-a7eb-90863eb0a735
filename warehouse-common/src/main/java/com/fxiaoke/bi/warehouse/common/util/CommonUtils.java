package com.fxiaoke.bi.warehouse.common.util;


import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author:jief
 * @Date:2024/5/15
 */

public class CommonUtils {
  public static String defaultTopic = "bi-warehouse-event";
  public static final Pattern geographyRex = Pattern.compile("POINT\\(\\s*(-?\\d+(\\.\\d+)*)\\s+(-?\\d+(\\.\\d+)*)\\s*\\)");
  public static final Pattern point = Pattern.compile("\\(\\s*(-?\\d+(\\.\\d+)*)?,\\s*(-?\\d+(\\.\\d+)*)?\\s*\\)");

  public static final String finalSetting = "SETTINGS final = 1, do_not_merge_across_partitions_select_final = 1, " +
          "optimize_move_to_prewhere_if_final = 1";
  public static final String aliasSuffix = "_n";
  public static final String noFinalSetting = "SETTINGS final = 0";

  public static String getDBName(String dbURL) {
    String db;
    int index = dbURL.lastIndexOf("/");
    if (dbURL.contains("?")) {
      db = dbURL.substring(index + 1, dbURL.indexOf("?"));
    } else {
      db = dbURL.substring(index + 1);
    }
    if (StringUtils.isNotBlank(db)) {
      return db;
    }
    return "default";
  }

  public static String extractIpFromJdbcUrl(String jdbcUrl) {
    // 定义匹配 JDBC URL 中 IP 地址的正则表达式
    String regex = "jdbc:\\w+://(.*):\\d+.*";
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(jdbcUrl);
    if (matcher.find()) {
      return matcher.group(1); // 返回匹配的 IP 地址
    } else {
      throw new IllegalArgumentException("Invalid JDBC URL or IP address not found.");
    }
  }

  /**
   *
   * @param chDbURL
   * @return
   */
  public static String chDb2Topic(String chDbURL) {
    String dbName = getDBName(chDbURL);
    if (dbName.startsWith("fsbidb")) {
      return String.format("bi-warehouse-event-vInx%s", dbName.substring(6, dbName.length() - 3));
    }
    return defaultTopic;
  }

  /**
   * equals any
   * @param obj
   * @param searchObjs
   * @return
   */
  public static boolean equalsAny(Object obj, Object... searchObjs) {
    if (ArrayUtils.isNotEmpty(searchObjs)) {
      for (final Object next : searchObjs) {
        if (Objects.equals(obj, next)) {
          return true;
        }
      }
    }
    return false;
  }

  //-----------------------------------------------------------------------
  /**
   * <p>Escapes the characters in a <code>String</code> to be suitable to pass to
   * an SQL query.</p>
   *
   * <p>For example,
   * <pre>statement.executeQuery("SELECT * FROM MOVIES WHERE TITLE='" +
   *   StringEscapeUtils.escapeSql("McHale's Navy") +
   *   "'");</pre>
   * </p>
   *
   * <p>At present, this method only turns single-quotes into doubled single-quotes
   * (<code>"McHale's Navy"</code> => <code>"McHale''s Navy"</code>). It does not
   * handle the cases of percent (%) or underscore (_) for use in LIKE clauses.</p>
   *
   * see http://www.jguru.com/faq/view.jsp?EID=8881
   * @param str  the string to escape, may be null
   * @return a new String, escaped for SQL, <code>null</code> if null string input
   */
  public static String escapeSql(String str) {
    if (str == null) {
      return null;
    }
    return StringUtils.replace(str, "'", "''");
  }

  public static long cost(long start) {
    return System.currentTimeMillis() - start;
  }

  /**
   * 找到两个集合的交集
   * @param collection1
   * @param collection2
   * @param <T>
   * @return
   */
  public static <T> Set<T> findIntersection(Collection<T> collection1, Collection<T> collection2) {
    // 创建一个新的HashSet，避免修改原始集合
    Set<T> intersection = new HashSet<>(collection1);
    // retainAll 会保留在 collection2 中存在的元素
    intersection.retainAll(collection2);
    return intersection;
}
}
