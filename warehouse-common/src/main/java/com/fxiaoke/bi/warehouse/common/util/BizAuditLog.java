package com.fxiaoke.bi.warehouse.common.util;

import com.fxiaoke.log.BiAuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.BiAuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BizAuditLog {
    private String tenantId;
    private String ea;
    private String userId;
    private String uri;
    private String viewId;
    private String module;
    private String viewName;
    private String viewType;
    private String from;
    private String fromId;
    private long num;
    private long cost;
    private String errorCode;
    private String message;
    private String error;
    private long startTime;
    private String extra;
    private String msgId;
    private String reqId;
    private String queryType;

    public void log() {
        BiAuditLogDTO auditLogDTO = BiAuditLogDTO.builder()
                .appName("fs-bi-warehouse")
                .traceId(TraceContext.get().getTraceId())
                .tenantId(tenantId)
                .ea(ea)
                .userId(userId)
                .viewId(viewId)
                .viewName(viewName)
                .module(module)
                .num(num)
                .createTime(startTime)
                .cost(cost)
                .message(message)
                .error(error)
                .extra(extra)
                .queryType(queryType)
                .profile(ConfigHelper.getProcessInfo().getProfile())
                .serverIp(ConfigHelper.getProcessInfo().getIp())
                .errorCode(errorCode)
                .from(from)
                .fromId(fromId)
                .uri(uri)
                .msgId(msgId)
                .build();
        BizLogClient.send("biz-log-bi", Pojo2Protobuf.toMessage(auditLogDTO, BiAuditLog.class).toByteArray());
    }
}
