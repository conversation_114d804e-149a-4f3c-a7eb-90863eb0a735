package com.fxiaoke.bi.warehouse.common.goal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class BITopology {
  private List<Node> nodes;
  private List<Edge> edges;
  private JSONArray wheres;

  public static BITopology parseFromJson(String jsonStr) {
    return JSON.parseObject(jsonStr, BITopology.class);
  }

  /**
   * id->node之间生成map关系
   *
   * @return
   */
  public Map<String, Node> createNodeMap() {
    if (CollectionUtils.isEmpty(nodes)) {
      return null;
    }
    return nodes.stream().collect(Collectors.toMap(Node::getId, Function.identity()));
  }

  /**
   * 根据id集合获取所有node集合
   *
   * @param ids node id集合
   * @return 顶点 集合
   */
  public List<Node> findNodeByIds(List<String> ids) {
    List<Node> nodeList = List.of();
    Map<String, Node> nodeMap = this.createNodeMap();
    ids.forEach(id -> {
      Node node = nodeMap.get(id);
      if (node != null) {
        nodeList.add(node);
      }
    });
    return nodeList;
  }

  /**
   * 获取根节点
   *
   * @return
   */
  public Optional<Node> findNodeById(String id) {
    if (CollectionUtils.isEmpty(nodes)) {
      return Optional.empty();
    }
    return nodes.stream().filter(node -> Objects.equals(id, node.getId())).findFirst();
  }

  /**
   * 获取所有起点是${id}的边的集合
   *
   * @param recursionDepth 控制递归深度，最大深度也不会超过边数
   * @param aubEdges       子图集合
   * @param id             起点id
   * @return 边的集合
   */
  public void findSubEdgesFromId(AtomicInteger recursionDepth, List<Edge> aubEdges, String id) {
    List<Edge> subEdges = this.edges.stream().filter(edge -> Objects.equals(id, edge.getFrom())).toList();
    if (CollectionUtils.isEmpty(subEdges)) {
      return;
    }
    if (recursionDepth.incrementAndGet() > this.edges.size()) {
      throw new RuntimeException("recursion depth is more than list size ,graph may be cycled");
    }
    subEdges.forEach(edge -> {
      aubEdges.add(edge);
      findSubEdgesFromId(recursionDepth, aubEdges, edge.getTo());
    });
  }

  /**
   * 根据起始id找到一条查找关联路线
   *
   * @param recursionDepth 计数
   * @param lookupEdges    存储关联路径集合
   * @param fromId         起始id
   * @param toId           结束id
   */
  public void findLookupList(AtomicInteger recursionDepth, List<Edge> lookupEdges, String fromId, String toId) {
    if (recursionDepth.incrementAndGet() > this.edges.size() && !this.edges.isEmpty()) {
      throw new RuntimeException("recursion depth is more than list size ,graph may be cycled");
    }
    if (Objects.equals(fromId, toId)) {
      return;
    }
    Optional<Edge> lookupEdge = this.edges.stream().filter(edge -> Objects.equals(toId, edge.getTo())).findFirst();
    lookupEdge.ifPresent(edge -> {
      lookupEdges.add(edge);
      if (!fromId.equals(edge.getFrom())) {
        findLookupList(recursionDepth, lookupEdges, fromId, edge.getFrom());
      }
    });
  }

}
