package com.fxiaoke.bi.warehouse.common.constants;

import java.util.Arrays;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/6/12.
 */
public enum DateRangeEnum {

  CURRENTYEAR(6, DateRanges.CURRENTYEAR, "本年度", "YEAR", "bi.dataRange.6.label"),//本年度
  LASTYEAR(7, DateRanges.LASTYEAR, "上一年度", "YEAR", "bi.dataRange.7.label"),//上一年度
  NEXTYEAR(10, DateRanges.NEXTYEAR, "下一年度", "YEAR", "bi.dataRange.10.label"),//"下一年度";

  CURRENTSEASON(13, DateRanges.CURRENTSEASON, "本季度", "SEASON", "bi.dataRange.13.label"),//"本季度";
  LASTSEASON(14, DateRanges.LASTSEASON, "上一季度", "SEASON", "bi.dataRange.14.label"),//"上一季度";
  NEXTSEASON(15, DateRanges.NEXTSEASON, "下一季度", "SEASON", "bi.dataRange.15.label"),//"下一季度";

  CURRENTMONTH(4, DateRanges.CURRENTMONTH, "本月", "MONTH", "bi.dataRange.4.label"),//"本月"
  LASTMONTH(5, DateRanges.LASTMONTH, "上月", "MONTH", "bi.dataRange.5.label"),//上月
  LAST2MONTH(38, DateRanges.LAST2MONTH, "上上月", "MONTH", "bi.dataRange.38.label"),//上上月
  NEXTMONTH(8, DateRanges.NEXTMONTH, "下月", "MONTH", "bi.dataRange.8.label"),//下月;

  CURRENTWEEK(2, DateRanges.CURRENTWEEK, "本周", "WEEK", "bi.dataRange.2.label"),//本周
  LASTWEEK(3, DateRanges.LASTWEEK, "上周", "WEEK", "bi.dataRange.3.label"),//上周
  NEXTWEEK(9, DateRanges.NEXTWEEK, "下周", "WEEK", "bi.dataRange.9.label"),//下周;

  TODAY(11, DateRanges.TODAY, "今天", "DAY", "bi.dataRange.11.label"),//"今天";
  YESTERDAY(1, DateRanges.YESTERDAY, "昨天", "DAY", "bi.dataRange.1.label"),//昨天  默认值，ID不要变
  TOMORROW(12, DateRanges.TOMORROW, "明天", "DAY", "bi.dataRange.12.label"),//"明天";

  CUSTOM(16, DateRanges.CUSTOM, "自定义", "CUSTOM", "bi.dataRange.16.label"),//"自定义";

  LASTHALFYEAR(17, DateRanges.LASTHALFYEAR, "上半年", "LASTHALFYEAR", "bi.dataRange.17.label"),//"上半年";
  NEXTHALFYEAR(18, DateRanges.NEXTHALFYEAR, "下半年", "NEXTHALFYEAR", "bi.dataRange.18.label"),//"下半年";

  CURRENTFISCALYEAR(20, DateRanges.CURRENTFISCALYEAR, "本财年", "CURRENTFISCALYEAR", "bi.dataRange.20.label"),//"本财年"
  LASTFISCALYEAR(21, DateRanges.LASTFISCALYEAR, "上一财年", "LASTFISCALYEAR", "bi.dataRange.21.label"),//"上一财年"
  NEXTFISCALYEAR(22, DateRanges.NEXTFISCALYEAR, "下一财年", "NEXTFISCALYEAR", "bi.dataRange.22.label"),//"下一财年"
  LASTHALFFISCALYEAR(23, DateRanges.LASTHALFFISCALYEAR, "上半一财年", "LASTHALFFISCALYEAR", "bi.dataRange.23.label"),//"上半一财年"
  NEXTHALFFISCALYEAR(24, DateRanges.NEXTHALFFISCALYEAR, "下半一财年", "NEXTHALFFISCALYEAR", "bi.dataRange.24.label"),//"下半一财年"
  CURRENTFISCALMONTH(28, DateRanges.CURRENTFISCALMONTH, "本财月", "CURRENTFISCALMONTH", "bi.dataRange.28.label"),//"本财月"
  LASTFISCALMONTH(29, DateRanges.LASTFISCALMONTH, "上一财月", "LASTFISCALMONTH", "bi.dataRange.29.label"),//"上一财月"
  NEXTFISCALMONTH(30, DateRanges.NEXTFISCALMONTH, "下一财月", "NEXTFISCALMONTH", "bi.dataRange.30.label"),//"下一财月"

  THEMONTHOFLASTYEAR(31,DateRanges.THEMONTHOFLASTYEAR,"去年本月","THEMONTHOFLASTYEAR", "bi.dataRange.31.label"),//去年本月
  THESEASONOFLASTYEAR(32,DateRanges.THESEASONOFLASTYEAR,"去年本季度","THESEASONOFLASTYEAR", "bi.dataRange.32.label"),//去年本季度
  THEDATEFROMLASTYEARTOTHISMONTH(33,DateRanges.THEDATEFROMLASTYEARTOTHISMONTH,"去年年初至去年当月","THEDATEFROMLASTYEARTOTHISMONTH", "bi.dataRange.33.label"),//去年年初至去年当月
  THEMONTHOFLASTYEARAFTER1MONTH(34,DateRanges.THEMONTHOFLASTYEARAFTER1MONTH,"去年当月后第1个月","THEMONTHOFLASTYEARAFTER1MONTH", "bi.dataRange.34.label"),//去年当月后第1个月
  THEMONTHOFLASTYEARAFTER2MONTH(35,DateRanges.THEMONTHOFLASTYEARAFTER2MONTH,"去年当月后第2个月","THEMONTHOFLASTYEARAFTER2MONTH", "bi.dataRange.35.label"),//去年当月后第2个月
  THEMONTHOFLASTYEARAFTER3MONTH(36,DateRanges.THEMONTHOFLASTYEARAFTER3MONTH,"去年当月后第3个月","THEMONTHOFLASTYEARAFTER3MONTH", "bi.dataRange.36.label"),//去年当月后第3个月
  THEDATEOFLASTYEARAFTERRANGE3MONTH(37,DateRanges.THEDATEOFLASTYEARAFTERRANGE3MONTH,"去年当月后累计3个月","THEDATEOFLASTYEARAFTERRANGE3MONTH", "bi.dataRange.37.label");//去年当月后累计3个月



  private int id;
  private String item;
  private String name;
  private String group;
  private String i18nKey;

  DateRangeEnum(int id, String item, String name, String group, String i18nKey) {
    this.id = id;
    this.item = item;
    this.name = name;
    this.group = group;
    this.i18nKey = i18nKey;
  }

  public static DateRangeEnum fromId(int id) {
    return Arrays.stream(values()).filter(dateRangeEnum -> dateRangeEnum.id == id).findFirst().orElse(null);
  }

  public static String getItemById(int id) {
    for (DateRangeEnum dateRangeEnum : DateRangeEnum.values()) {
      if (dateRangeEnum.getId() == id) {
        return dateRangeEnum.getItem();
      }
    }
    return null;
  }

  public static String getNameById(int id) {
    for (DateRangeEnum dateRangeEnum : DateRangeEnum.values()) {
      if (dateRangeEnum.getId() == id) {
        return dateRangeEnum.getName();
      }
    }
    return null;
  }

  public static String getGroupById(int id) {
    for (DateRangeEnum dateRangeEnum : DateRangeEnum.values()) {
      if (dateRangeEnum.getId() == id) {
        return dateRangeEnum.getGroup();
      }
    }
    return null;
  }

  @Override
  public String toString() {
    return this.item;
  }

  public String getGroup() {
    return group;
  }

  public int getId() {
    return id;
  }

  public String getItem() {
    return item;
  }

  public String getName() {
    return name;
  }

  public String getI18nKey() {
    return i18nKey;
  }

  public static DateRangeEnum getById(int id){
    for (DateRangeEnum dateRangeEnum : DateRangeEnum.values()) {
      if (dateRangeEnum.getId() == id) {
        return dateRangeEnum;
      }
    }
    return DateRangeEnum.CURRENTMONTH;
  }

  public static void main(String[] args) {
    for (DateRangeEnum value : DateRangeEnum.values()) {
      System.out.println(value.getI18nKey() + "-" + value.getName());
    }
  }

}
