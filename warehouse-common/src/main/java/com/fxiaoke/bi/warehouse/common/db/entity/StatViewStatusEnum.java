package com.fxiaoke.bi.warehouse.common.db.entity;

/**
 * '1-启用；2-初始化；0-用户行为导致禁用；-1-近3个月访问禁用'<br>
 * bi_mt_topology_status.status
 */
public enum StatViewStatusEnum {
  disabled4Idle(-1, "近3个月访问禁用"),
  disabled(0, "用户行为导致禁用"),
  used(1, "已启用"),
  init(2, "初始化中");
  private final Integer status;
  private final String desc;

  private StatViewStatusEnum(Integer status, String desc) {
    this.status = status;
    this.desc = desc;
  }
  public int getStatus() {
    return this.status;
  }
}
