package com.fxiaoke.bi.warehouse.common.constants;

import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * operator枚举类
 * Created by liupeng on 2016/6/12.
 * edit by zhangyh on 2016/07/26
 */
public enum FilterOperatorCodeEnum {
  //NOTSUPPORT(0, "~", "不支持", "","bi.filter.operator.0.label"),
  CONTAIN(1, "LIKE", "包含", "String","bi.filter.operator.1.label"),
  NOTCONTAIN(2, "NOT LIKE", "不包含", "String","bi.filter.operator.2.label"),
  IS(3, "=", "等于", "String","bi.filter.operator.3.label"),
  NOTIS(4, "!=", "不等于", "String","bi.filter.operator.4.label"),
  BEGINWITH(5, "LIKE", "开始于", "String","bi.filter.operator.5.label"),//生成sql时值为value+%
  ENDWITH(6, "LIKE", "结束于", "String","bi.filter.operator.6.label"),
  ISNULL(7, "IS NULL", "为空", "String","bi.filter.operator.7.label"),
  ISNOTNULL(8, "IS NOT NULL", "不为空", "String","bi.filter.operator.8.label"),
  EQUAL(9, "=", "等于", "Number","bi.filter.operator.9.label"),
  NOTEQUAL(10, "!=", "不等于", "Number","bi.filter.operator.10.label"),
  GREATERTHAN(11, ">", "大于", "Number","bi.filter.operator.11.label"),
  LOWERTHAN(12, "<", "小于", "Number","bi.filter.operator.12.label"),
  GREATEREQUALTHAN(13, ">=", "大于等于", "Number","bi.filter.operator.13.label"),
  LOWEREQUALTHAN(14, "<=", "小于等于", "Number","bi.filter.operator.14.label"),
  ISNULL1(15, "IS NULL", "为空", "Number","bi.filter.operator.15.label"),
  ISNOTNULL1(16, "IS NOT NULL", "不为空", "Number","bi.filter.operator.16.label"),
  EQUAL1(17, "=", "等于", "Date","bi.filter.operator.17.label"),
  NOTEQUAL1(18, "!=", "不等于", "Date","bi.filter.operator.18.label"),
  SOONERTHAN(19, "<", "早于", "Date","bi.filter.operator.19.label"),
  SOONEREQUALTHAN(38, "<=", "早于等于", "Date","bi.filter.operator.38.label"),
  LATERTHAN(20, ">=", "晚于等于", "Date","bi.filter.operator.20.label"),
  ISNULL2(21, "IS NULL", "为空", "Date","bi.filter.operator.21.label"),
  ISNOTNULL2(22, "IS NOT NULL", "不为空", "Date","bi.filter.operator.22.label"),
  OTHER(23, "DATERANGE", "时间段", "Date","bi.filter.operator.23.label"),// 此ID前端已经指定，不要变化
  BETWEEN(24, "BETWEEN", "介于", "Number","bi.filter.operator.24.label"),
  CUSTOM(25, "CUSTOM", "自定义", "Date","bi.filter.operator.25.label"),
  IN(26, "IN", "属于", "Number","bi.filter.operator.26.label"),
  NOTIN(27, "NOT IN", "不属于", "Number","bi.filter.operator.27.label"),
  DAYBEFOR(28,"BETWEEN", "过去N天内（含当天）", "Date","bi.filter.operator.28.label"),
  DAYAFTER(29,"BETWEEN", "未来N天内（含当天）", "Date","bi.filter.operator.29.label"),
  MONTHBEFOR(30,"BETWEEN", "过去N月内（含当月）", "Date","bi.filter.operator.30.label"),
  MONTHAFTER(31,"BETWEEN", "未来N月内（含当月）", "Date","bi.filter.operator.31.label"),
  BEFORDAY(32, "BETWEEN", "N天前", "Date","bi.filter.operator.32.label"),
  AFTERDAY(33, "BETWEEN", "N天后", "Date","bi.filter.operator.33.label"),
  WEEKBEFOR(34, "BETWEEN", "过去N周内（含当周）", "Date","bi.filter.operator.34.label"),
  WEEKAFTER(35, "BETWEEN", "未来N周内（含当周）", "Date","bi.filter.operator.35.label"),
  BEFORWEEK(36, "BETWEEN", "N周前", "Date","bi.filter.operator.36.label"),
  AFTERWEEK(37, "BETWEEN", "N周后", "Date","bi.filter.operator.37.label"),

  LATERTHAN2(39, ">", "晚于", "Date","bi.filter.operator.39.label"),
  DAYBEFOR2(40, "BETWEEN", "过去N天内（不含当天）", "Date","bi.filter.operator.40.label"),
  DAYAFTER2(41, "BETWEEN", "未来N天内（不含当天）", "Date","bi.filter.operator.41.label"),
  WEEKBEFOR2(42, "BETWEEN", "过去N周内（不含当周）", "Date","bi.filter.operator.42.label"),
  WEEKAFTER2(43, "BETWEEN", "未来N周内（不含当周）", "Date","bi.filter.operator.43.label"),
  MONTHBEFOR2(44, "BETWEEN", "过去N月内（不含当月）", "Date","bi.filter.operator.44.label"),
  MONTHAFTER2(45, "BETWEEN", "未来N月内（不含当月）", "Date","bi.filter.operator.45.label"),
  SQL_IN(999, "SQL_IN", "in(sql片段)", "String","bi.filter.operator.999.label"),

  HOURBEFOR2(46, "BETWEEN", "过去N小时内（含当前小时）", "Date","bi.filter.operator.46.label"),
  HOURAFTER2(47,"BETWEEN", "未来N小时内（含当前小时）", "Date","bi.filter.operator.47.label"),
  MINUTEBEFOR2(48, "BETWEEN", "过去N分钟内（含当前分钟）", "Date","bi.filter.operator.48.label"),
  MINUTEAFTER2(49, "BETWEEN", "未来N分钟内（含当前分钟）", "Date","bi.filter.operator.49.label"),

  BEFORHOUR(50, "BETWEEN", "N小时前（含当前小时）", "Date","bi.filter.operator.50.label"),
  AFTERHOUR(51, "BETWEEN", "N小时后（含当前小时）", "Date","bi.filter.operator.51.label"),
  BEFORMINUTE(52, "BETWEEN", "N分钟前（含当前分钟）", "Date","bi.filter.operator.52.label"),
  AFTERMINUTE(53, "BETWEEN", "N分钟后（含当前分钟）", "Date", "bi.filter.operator.53.label"),

  HOURBEFOR(54, "BETWEEN", "过去N小时内（不含当前小时）", "Date","bi.filter.operator.54.label"),
  HOURAFTER(55, "BETWEEN", "未来N小时内（不含当前小时）", "Date","bi.filter.operator.55.label"),
  MINUTEBEFOR(56, "BETWEEN", "过去N分钟内（不含当前分钟）", "Date","bi.filter.operator.56.label"),
  MINUTEAFTER(57, "BETWEEN", "未来N分钟内（不含当前分钟）", "Date", "bi.filter.operator.57.label");





  private int id;
  private String operator;
  private String label;
  private String type;
  private String i18nKey;

  public String getI18nKey() {
    return i18nKey;
  }


  FilterOperatorCodeEnum(int id, String operator, String label, String type, String i18nKey) {
    this.id = id;
    this.operator = operator;
    this.label = label;
    this.type = type;
    this.i18nKey = i18nKey;
  }

  /**
   * 支持基础同环比数据范围
   */
  public boolean isSupportAsBaseRatioDateFilter(){
    if(!this.getType().equals("Date")){return false;}
    switch (this){
      case CUSTOM:
      case OTHER:
      case EQUAL1:
        return true;
      default:
        return Objects.equals(this.getOperator(),"BETWEEN");
    }
  }

  /**
   * 支持同环比数据范围(组合使用)
   */
  public boolean isSupportAsRatioDateFilter(){
    if(!this.getType().equals("Date")){return false;}
    switch (this){
      case EQUAL1:
      case NOTEQUAL1:
      case SOONERTHAN:
      case SOONEREQUALTHAN:
      case LATERTHAN:
      case LATERTHAN2:
      case CUSTOM:
      case OTHER:
        return true;
      default:
        return Objects.equals(this.getOperator(),"BETWEEN");
    }
  }


  public static String getOperatorLabelById(int id) {
    String operatorLabel = "";
    for (FilterOperatorCodeEnum fe : FilterOperatorCodeEnum.values()) {
      if (fe.getId() == id) {
        operatorLabel = fe.getLabel();
      }
    }
    return operatorLabel;
  }

  public static FilterOperatorCodeEnum getFilterOperatorById(int id) {
    for(FilterOperatorCodeEnum fe: FilterOperatorCodeEnum.values()) {
      if(fe.getId() == id) {
        return fe;
      }
    }
    return null;
  }

  public static String getOperatorById(int id) {
    String operator = "";
    //// add by zyh on 2017/5/9 11:02 id为0是针对枚举类型数据
    if (id == 0) {
      id = 3;
    }
    // end add
    for (FilterOperatorCodeEnum fe : FilterOperatorCodeEnum.values()) {
      if (fe.getId() == id) {
        operator = fe.getOperator();
      }
    }
    if (StringUtils.equals("", operator)) {
      throw new RuntimeException("invalidate，operator=" + id);
    }
    return operator;
  }

  public static String getOperatorTypeById(int id) {
    String type = "";
    //// add by zyh on 2017/5/9 11:02 id为0是针对枚举类型数据
    if (id == 0) {
      id = 3;
    }
    // end add
    for (FilterOperatorCodeEnum fe : FilterOperatorCodeEnum.values()) {
      if (fe.getId() == id) {
        type = fe.getType();
      }
    }
    if (StringUtils.equals("", type)) {
      throw new RuntimeException("invalidate operator，type=" + id);
    }
    return type;
  }


  public int getId() {
    return id;
  }
  // Modify by fanlh end at 2016/7/29

  public String getOperator() {
    return operator;
  }

  public String getLabel() {
    return label;
  }

  public String getType() {
    return type;
  }

  /**
   * 数据范围是否受全局过滤影响
   */
  public boolean filterValueEffectByGlobalFilter() {
    return Objects.equals(this, IN) || Objects.equals(this, NOTIN);
  }

}
