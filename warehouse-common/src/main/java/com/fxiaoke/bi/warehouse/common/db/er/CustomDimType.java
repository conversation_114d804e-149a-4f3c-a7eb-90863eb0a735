package com.fxiaoke.bi.warehouse.common.db.er;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义维度类型
 */
public enum CustomDimType {

  /**
   * 枚举分组
   */
  enum_group {
    @Override
    public String generateCustomMultiIfClause(ColumnTypeConfig sourceTypeConfig,
                                              String tableAlias,
                                              String column,
                                              boolean withAggFun,
                                              boolean isLang) {
      String customDimConfig = sourceTypeConfig.get(CustomDimType.CUSTOM_DIM_CONFIG);
      JSONObject config = JSON.parseObject(customDimConfig);
      String fullColumnName = tableAlias + "." + column;
      JSONArray groups = config.getJSONArray("groups");
      List<JSONObject> notDefaultGroup = groups.stream()
                                               .map(JSONObject.class::cast)
                                               .filter(data -> Objects.isNull(data.getBoolean("isDefault")) ||
                                                               BooleanUtils.isFalse(data.getBoolean("isDefault")))
                                               .toList();
      String defaultName = groups.stream()
                                 .map(JSONObject.class::cast)
                                 .filter(data -> BooleanUtils.isTrue(data.getBoolean("isDefault")))
                                 .findFirst()
                                 .orElse(new JSONObject(Map.of("name", "未分组")))
                                 .getString("name");
      if (CollectionUtils.isEmpty(notDefaultGroup)) {
        return StringUtils.isBlank(defaultName) ? "'未分组'" : "'%s'".formatted(defaultName);
      }
      List<String> conditions = new ArrayList<>();
      for (JSONObject group : notDefaultGroup) {
        String groupName = group.getString("name");
        JSONArray values = group.getJSONArray("values");
        String valuesStr = values.stream().map("'%s'"::formatted).collect(Collectors.joining(", "));
        String apply = String.format("%s IN (%s), '%s'", fullColumnName, valuesStr, groupName);
        conditions.add(apply);
      }
      String multiIfClause = String.join(", ", conditions) + ", '" + defaultName + "'";
      // 处理 select_many 的情况
      String udfType = sourceTypeConfig.get("udfType");
      String tableName = sourceTypeConfig.get("tableName");
      if ("select_many".equals(udfType)) {
        String replace = multiIfClause.replace(fullColumnName, "x");
        if ("object_data".equals(tableName)) {
          return "arrayDistinct(arrayMap(x -> multiIf(" + replace + "), splitByChar(',', " + fullColumnName + ")))";
        } else {
          return "arrayDistinct(arrayMap(x -> multiIf(" + replace + "), " + fullColumnName + "))";
        }
      }
      return "multiIf(" + multiIfClause + ")";
    }
  },

  /**
   * 日期分组
   */
  date_group {
    @Override
    public String generateCustomMultiIfClause(ColumnTypeConfig sourceTypeConfig,
                                              String tableAlias,
                                              String column,
                                              boolean withAggFun,
                                              boolean isLang) {
      String customDimConfig = sourceTypeConfig.get(CustomDimType.CUSTOM_DIM_CONFIG);
      JSONObject config = JSON.parseObject(customDimConfig);
      JSONArray groups = config.getJSONArray("groups");
      List<JSONObject> notDefaultGroup = groups.stream()
                                               .map(JSONObject.class::cast)
                                               .filter(data -> BooleanUtils.isFalse(data.getBoolean("isDefault")))
                                               .toList();
      String defaultName = groups.stream()
                                 .map(JSONObject.class::cast)
                                 .filter(data -> BooleanUtils.isTrue(data.getBoolean("isDefault")))
                                 .findFirst()
                                 .orElse(new JSONObject(Map.of("name", "未分组")))
                                 .getString("name");
      if (CollectionUtils.isEmpty(notDefaultGroup)) {
        return StringUtils.isBlank(defaultName) ? "'未分组'" : "'%s'".formatted(defaultName);
      }
      String groupType = config.getString("group_type");
      String fullColumnName = tableAlias + "." + column;
      // 使用增强的 switch 表达式来处理不同的 groupType
      List<String> conditions = new ArrayList<>();
      for (JSONObject group : notDefaultGroup) {
        String name = group.getString("name");
        String start = group.getString("start");
        String end = group.getString("end");
        String apply = switch (groupType) {
          case "custom_group" ->
            String.format("(%s >= toDate('%s') AND %s <= toDate('%s'))", fullColumnName, start, fullColumnName, end);
          case "month_group" ->
            String.format("(toDayOfMonth(%s) >= %s AND toDayOfMonth(%s) <= %s)", fullColumnName, start, fullColumnName, end);
          case "week_group" ->
            String.format("(toDayOfWeek(%s) >= %s AND toDayOfWeek(%s) <= %s)", fullColumnName, start, fullColumnName, end);
          case "year_group" -> String.format(
            "(toMonth(%s) > %s OR (toMonth(%s) = %s AND toDayOfMonth(%s) >= %s)) AND " +
            "(toMonth(%s) < %s OR (toMonth(%s) = %s AND toDayOfMonth(%s) <= %s))", fullColumnName, start.substring(0, 2), fullColumnName, start.substring(0, 2), fullColumnName, start.substring(3), fullColumnName, end.substring(0, 2), fullColumnName, end.substring(0, 2), fullColumnName, end.substring(3));
          default -> throw new IllegalArgumentException("Unknown group type: " + groupType);
        } + String.format(", '%s'", name);
        conditions.add(apply);
      }
      // 使用 String.join 来拼接 multiIf 条件
      String multiIfClause = String.join(", ", conditions) + ", '" + defaultName + "'";
      return "multiIf(" + multiIfClause + ")";
    }
  },

  /**
   * 引用类型的自定义维度
   */
  reference {
    @Override
    public String generateCustomMultiIfClause(ColumnTypeConfig sourceTypeConfig,
                                              String tableAlias,
                                              String column,
                                              boolean withAggFun,
                                              boolean isLang) {
      String udfType = sourceTypeConfig.get("udfType");
      ColumnTypeConfig dstColumnTypeConfig = sourceTypeConfig.get("dstColumnTypeConfig");
      boolean isSingle = sourceTypeConfig.get("isSingle");
      String fieldType = sourceTypeConfig.get("fieldType");
      PGColumnType pgColumnType = sourceTypeConfig.get("pgColumnType");
      ColumnType dimDistType = pgColumnType.createDimDistType(fieldType, isSingle);
      ColumnType columnType = Objects.requireNonNullElse(pgColumnType.trans2CHType(), dimDistType);
      return columnType.selectItemSql(tableAlias, column, dimDistType, sourceTypeConfig, dstColumnTypeConfig, withAggFun, udfType, isLang);
    }
  };

  public final static String CUSTOM_DIM_TYPE = "customDimType";
  public final static String CUSTOM_DIM_CONFIG = "customDimConfig";

  public String generateCustomMultiIfClause(ColumnTypeConfig sourceTypeConfig,
                                            String tableAlias,
                                            String column,
                                            boolean withAggFun,
                                            boolean isLang) {
    String customDimConfig = sourceTypeConfig.get(CustomDimType.CUSTOM_DIM_CONFIG).toString();
    JSONObject jsonObject = JSON.parseObject(customDimConfig);
    String fullColumnName = tableAlias + "." + column;
    String defaultName = Optional.ofNullable(jsonObject.getString("default_name")).orElse("'未分组'");
    // 检查 groups 是否为空
    return "multiIf(isNull(%s), '', %s)".formatted(fullColumnName, defaultName);
  }
}
