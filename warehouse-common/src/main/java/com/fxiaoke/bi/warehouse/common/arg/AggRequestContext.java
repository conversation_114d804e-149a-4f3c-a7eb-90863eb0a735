package com.fxiaoke.bi.warehouse.common.arg;

import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.util.TypeUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author:jief
 * @Date:2024/8/8
 */
public class AggRequestContext extends HashMap<String, Object> {
  private AggRequestContext() {
    super();
  }

  public static AggRequestContext getInstance() {
    return new AggRequestContext();
  }

  public static AggRequestContext getInstance(Map<String,Object> initMap) {
    AggRequestContext object = new AggRequestContext();
    if(initMap!=null){
      object.putAll(initMap);
    }
    return object;
  }

  public <T> T getRequestArg(String name, Class<T> classType) throws ClassCastException {
    Object arg = this.get(name);
    if (arg != null) {
      return classType.cast(arg);
    }
    return null;
  }

  public <T> T getRequestArg(String name, TypeReference<T> type) {
    Object arg = this.get(name);
    if (arg != null) {
      return TypeUtils.cast(arg, type.getType(), null);
    }
    return null;
  }

  public Object getRequestArg(String name) {
    return this.get(name);
  }

  public void putRequestArg(String name, Object value) {
    this.put(name, value);
  }

}
