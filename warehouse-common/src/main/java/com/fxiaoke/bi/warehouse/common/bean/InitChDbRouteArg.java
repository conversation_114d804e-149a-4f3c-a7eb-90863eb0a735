package com.fxiaoke.bi.warehouse.common.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Date 20240408
 * <AUTHOR>
 * @Desc 初始化企业CH路由
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InitChDbRouteArg {

    /**
     * 数据库名
     */
    private String dbName;

    /**
     * 需要分配的资源类型
     */
    private int activityLevel;
    /**
     * 是否检查活动级别
     */
    private boolean checkActivityLevel;

    private PodRouter podRouter;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PodRouter {

        /**
         * 业务名称
         */
        private String biz;

        /**
         * 企业id
         */
        private String tenantId;

        /**
         * 数据库类型
         */
        private String dialect;

        /**
         * 是否是独立的schema
         */
        private boolean standalone;
    }
}
