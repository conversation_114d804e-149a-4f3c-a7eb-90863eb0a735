package com.fxiaoke.bi.warehouse.common.db.er;

import com.fxiaoke.bi.warehouse.common.util.TemplateUtil;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/3/22
 */
@Data
public class NodeOnCondition {
  private String[] equalPairs;
  private List<Pair<String, String>> leftTableCondition;
  private List<Pair<String, String>> rightTableCondition;

  public NodeOnCondition(String[] equalPairs,
                         List<Pair<String, String>> leftTableCondition,
                         List<Pair<String, String>> rightTableCondition) {
    this.equalPairs = equalPairs;
    this.leftTableCondition = leftTableCondition;
    this.rightTableCondition = rightTableCondition;
  }

  public void appendEqualPairs(String[] otherPairs) {
    if (this.equalPairs == null) {
      this.equalPairs = otherPairs;
    } else if (otherPairs != null) {
      Set<String> equalPairsSet = Sets.newHashSet(this.equalPairs);
      equalPairsSet.addAll(Sets.newHashSet(otherPairs));
      this.equalPairs = equalPairsSet.toArray(new String[0]);
    }
  }

  public void appendTableCondition(boolean isLeft, List<Pair<String, String>> otherCondition) {
    if (isLeft) {
      if (CollectionUtils.isEmpty(this.leftTableCondition)) {
        this.leftTableCondition = otherCondition;
      } else if (!CollectionUtils.isEmpty(otherCondition)) {
        otherCondition.forEach(condition -> {
          if (!this.leftTableCondition.contains(condition)) {
            this.leftTableCondition.add(condition);
          }
        });
      }
    } else {
      if (CollectionUtils.isEmpty(this.rightTableCondition)) {
        this.rightTableCondition = otherCondition;
      } else if (!CollectionUtils.isEmpty(otherCondition)) {
        otherCondition.forEach(condition -> {
          if (!this.rightTableCondition.contains(condition)) {
            this.rightTableCondition.add(condition);
          }
        });
      }
    }
  }

  /**
   * 拼装on条件sql
   *
   * @param leftTableAlias
   * @param rightTableAlias
   * @return
   */
  public String buildOnConditionSql(String leftTableAlias, String rightTableAlias) {
    Map<String, Object> tableAliasMap = Maps.newHashMap();
    tableAliasMap.put("lt", leftTableAlias);
    tableAliasMap.put("rt", rightTableAlias);
    StringBuilder sb = new StringBuilder();
    for (String equalPair : equalPairs) {
      sb.append(" AND ");
      if (equalPair.contains("${lt}") || equalPair.contains("${rt}")) {
        sb.append(TemplateUtil.replace(equalPair, tableAliasMap));
      } else {
        String[] equals = equalPair.split("=");
        sb.append(leftTableAlias);
        sb.append(".");
        sb.append(equals[0]);
        sb.append("=");
        sb.append(rightTableAlias);
        sb.append(".");
        sb.append(equals[1]);
        sb.append(" ");
      }
    }
    if (this.leftTableCondition != null) {
      leftTableCondition.forEach(p -> {
        sb.append("AND ");
        sb.append(leftTableAlias);
        sb.append(".");
        sb.append(p.first);
        sb.append("='");
        sb.append(p.second);
        sb.append("' ");
      });
    }
    if (this.rightTableCondition != null) {
      rightTableCondition.forEach(p -> {
        sb.append("AND ");
        sb.append(rightTableAlias);
        sb.append(".");
        sb.append(p.first);
        sb.append(" = '");
        sb.append(p.second);
        sb.append("' ");
      });
    }
    return sb.substring(4);
  }
}
