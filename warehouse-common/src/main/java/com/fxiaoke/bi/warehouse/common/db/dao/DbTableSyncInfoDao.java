package com.fxiaoke.bi.warehouse.common.db.dao;

import com.fxiaoke.bi.warehouse.common.component.BIPgDataSource;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.helper.JoinHelper;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.sql.ResultSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * @Author:jief
 * @Date:2024/8/23
 */
@Slf4j
public class DbTableSyncInfoDao {
  @Setter
  private BIPgDataSource biPgDataSource;

  public int batchUpsertDbTableSyncInfo(String pgDbURL, String pgSchema, List<DbTableSyncInfoDO> dbTableSyncInfoDOS) {
    String sql= """
       insert into %s.db_table_sync_info(id,tenant_id,db_sync_id,table_name,max_sys_modified_time,last_sync_time,api_name_ei_map
       ,create_time,last_modified_time,is_deleted,batch_num,status,target_table,src_dialect,inc_sys_modified_time_from,inc_sys_modified_time_to,inc_api_name_ei_map)
       values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
       ON CONFLICT(id,tenant_id)
       DO UPDATE set db_sync_id=EXCLUDED.db_sync_id,
                    table_name=EXCLUDED.table_name,
                    max_sys_modified_time=EXCLUDED.max_sys_modified_time,
                    last_sync_time=EXCLUDED.last_sync_time,
                    api_name_ei_map=EXCLUDED.api_name_ei_map,
                    last_modified_time=EXCLUDED.last_modified_time,
                    is_deleted=EXCLUDED.is_deleted,
                    batch_num=EXCLUDED.batch_num,
                    status=EXCLUDED.status,
                    target_table=EXCLUDED.target_table,
                    src_dialect=EXCLUDED.src_dialect,
                    inc_sys_modified_time_from=EXCLUDED.inc_sys_modified_time_from,
                    inc_sys_modified_time_to=EXCLUDED.inc_sys_modified_time_to,
                    inc_api_name_ei_map=EXCLUDED.inc_api_name_ei_map
      """;
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      return jdbcConnection.prepareUpdateBatch(String.format(sql,pgSchema), preparedStatement -> {
        for(DbTableSyncInfoDO dbTableSyncInfoDO : dbTableSyncInfoDOS){
          preparedStatement.setString(1, dbTableSyncInfoDO.getId());
          preparedStatement.setString(2,
            dbTableSyncInfoDO.getTenantId() == null ? "-1" : dbTableSyncInfoDO.getTenantId());
          preparedStatement.setString(3, dbTableSyncInfoDO.getDbSyncId());
          preparedStatement.setString(4, dbTableSyncInfoDO.getTableName());
          preparedStatement.setLong(5, dbTableSyncInfoDO.findMaxSysModifiedTime());
          preparedStatement.setLong(6, dbTableSyncInfoDO.getLastSyncTime());
          preparedStatement.setString(7, dbTableSyncInfoDO.getApiNameEiMap());
          preparedStatement.setLong(8, dbTableSyncInfoDO.getCreateTime());
          preparedStatement.setLong(9, dbTableSyncInfoDO.getLastModifiedTime());
          preparedStatement.setInt(10, dbTableSyncInfoDO.getIsDeleted());
          preparedStatement.setLong(11, dbTableSyncInfoDO.getBatchNum());
          preparedStatement.setInt(12, dbTableSyncInfoDO.getStatus());
          preparedStatement.setString(13, dbTableSyncInfoDO.getTargetTable());
          preparedStatement.setString(14, dbTableSyncInfoDO.getSrcDialect());
          preparedStatement.setLong(15, dbTableSyncInfoDO.getIncSysModifiedTimeFrom());
          preparedStatement.setLong(16, dbTableSyncInfoDO.getIncSysModifiedTimeTo());
          preparedStatement.setString(17, dbTableSyncInfoDO.getIncApiNameEiMap());
          preparedStatement.addBatch();
        }
      });
    } catch (Exception e) {
      log.error("batchUpsertDbTableSyncInfo error pgDB:{},pgSchema:{}",pgDbURL,pgSchema, e);
    }
    return 0;
  }

  public int batchUpdateDbTableSyncInfo(String pgDbURL, String pgSchema, List<DbTableSyncInfoDO> dbTableSyncInfoDOS) {
    String sql= """
            update %s.db_table_sync_info set max_sys_modified_time = ?,
            last_sync_time = ?,
            api_name_ei_map = ?,
            last_modified_time = ?,
            is_deleted = ?,
            batch_num = ?,
            status = ?
            where id = ?
      """;
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      return jdbcConnection.prepareUpdateBatch(String.format(sql,pgSchema), preparedStatement -> {
        for(DbTableSyncInfoDO dbTableSyncInfoDO : dbTableSyncInfoDOS){
          preparedStatement.setLong(1, dbTableSyncInfoDO.getMaxSysModifiedTime());
          preparedStatement.setLong(2, dbTableSyncInfoDO.getLastSyncTime());
          preparedStatement.setString(3, dbTableSyncInfoDO.getApiNameEiMap());
          preparedStatement.setLong(4, dbTableSyncInfoDO.getLastModifiedTime());
          preparedStatement.setInt(5, dbTableSyncInfoDO.getIsDeleted());
          preparedStatement.setLong(6, dbTableSyncInfoDO.getBatchNum());
          preparedStatement.setInt(7, dbTableSyncInfoDO.getStatus());
          preparedStatement.setString(8, dbTableSyncInfoDO.getId());
          preparedStatement.addBatch();
        }
      });
    } catch (Exception e) {
      log.error("batchUpdateDbTableSyncInfo error pgDB:{},pgSchema:{}",pgDbURL,pgSchema, e);
    }
    return 0;
  }

  /**
   *
   * @param pgDbURL
   * @param pgSchema
   * @param id
   * @param maxSysModifiedTime
   * @return
   */
  public int batchUpdateMaxSysModifiedTime(String pgDbURL, String pgSchema, String id, long maxSysModifiedTime) {
    String sql = """
            update %s.db_table_sync_info set max_sys_modified_time = ?
            where id = ?
            and is_deleted=0
      """;
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      return jdbcConnection.prepareUpdate2(String.format(sql, pgSchema), preparedStatement -> {
        preparedStatement.setLong(1, maxSysModifiedTime);
        preparedStatement.setString(2, id);
      });
    } catch (Exception e) {
      log.error("batchUpdateDbTableSyncInfo error pgDB:{},pgSchema:{}", pgDbURL, pgSchema, e);
    }
    return 0;
  }
 /**
   *
   * @param pgDbURL
   * @param pgSchema
   * @param id
   * @param maxSysModifiedTime
   * @param tableName
   * @return
   */
  public int batchUpdateMaxSysModifiedTimeByTableName(String pgDbURL, String pgSchema, String id, long maxSysModifiedTime,String tableName) {
    String sql = """
            update %s.db_table_sync_info set max_sys_modified_time = ?
            where id = ?
            and table_name = ?
            and is_deleted=0
      """;
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      return jdbcConnection.prepareUpdate2(String.format(sql, pgSchema), preparedStatement -> {
        preparedStatement.setLong(1, maxSysModifiedTime);
        preparedStatement.setString(2, id);
        preparedStatement.setString(3, tableName);
      });
    } catch (Exception e) {
      log.error("batchUpdateDbTableSyncInfo error pgDB:{},pgSchema:{}", pgDbURL, pgSchema, e);
    }
    return 0;
  }

  /**
   * 重置 dbTableSyncInfo
   * @param pgDbURL pg
   * @param pgSchema
   * @param dbSyncId
   * @return
   */
  public int resetCHDbTableSyncInfo(String pgDbURL, String pgSchema, String dbSyncId) {
    String sql = """
            update %s.db_table_sync_info set status=0,max_sys_modified_time =0,batch_num=0,api_name_ei_map=''
            where db_sync_id = ?
            and is_deleted=0
      """;
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      return jdbcConnection.prepareUpdate2(String.format(sql, pgSchema), preparedStatement -> {
        preparedStatement.setString(1, dbSyncId);
      });
    } catch (Exception e) {
      log.error("batchUpdateDbTableSyncInfo error pgDB:{},pgSchema:{}", pgDbURL, pgSchema, e);
    }
    return 0;
  }

  private final Function<ResultSet, DbTableSyncInfoDO> createDbSyncInfoFlowFunction = (ResultSet r) -> {
    try {
      DbTableSyncInfoDO dbTableSyncInfoDO = new DbTableSyncInfoDO();
      dbTableSyncInfoDO.setId(r.getString("id"));
      dbTableSyncInfoDO.setTenantId(r.getString("tenant_id"));
      dbTableSyncInfoDO.setDbSyncId(r.getString("db_sync_id"));
      dbTableSyncInfoDO.setTableName(r.getString("table_name"));
      Object msmt = r.getObject("max_sys_modified_time");
      if (msmt != null) {
        dbTableSyncInfoDO.setMaxSysModifiedTime((long) msmt);
      }
      dbTableSyncInfoDO.setLastSyncTime(r.getLong("last_sync_time"));
      dbTableSyncInfoDO.setBatchNum(r.getLong("batch_num"));
      dbTableSyncInfoDO.setApiNameEiMap(r.getString("api_name_ei_map"));
      dbTableSyncInfoDO.setCreateTime(r.getLong("create_time"));
      dbTableSyncInfoDO.setLastModifiedTime(r.getLong("last_modified_time"));
      Object status = r.getObject("status");
      if (status != null) {
        dbTableSyncInfoDO.setStatus((int) status);
      }
      dbTableSyncInfoDO.setIsDeleted(r.getInt("is_deleted"));
      dbTableSyncInfoDO.setTargetTable(r.getString("target_table"));
      dbTableSyncInfoDO.setSrcDialect(r.getString("src_dialect"));
      dbTableSyncInfoDO.setIncSysModifiedTimeFrom(r.getLong("inc_sys_modified_time_from"));
      dbTableSyncInfoDO.setIncSysModifiedTimeTo(r.getLong("inc_sys_modified_time_to"));
      dbTableSyncInfoDO.setIncApiNameEiMap(r.getString("inc_api_name_ei_map"));
      return dbTableSyncInfoDO;
    } catch (Exception e) {
      throw new RuntimeException("build DbTableSyncInfoDO error", e);
    }
  };
  
  public List<DbTableSyncInfoDO> queryDBSyncInfoBySyncId(String pgDbURL, String pgSchema, String dbSyncId) {
    String sql = String.format("select * from %s.db_table_sync_info where db_sync_id='%s' and is_deleted=0", pgSchema, dbSyncId);
    return this.queryDbTableSyncInfos(pgDbURL, pgSchema, sql);
  }

  public List<DbTableSyncInfoDO> findByDBSyncInfoID(String pgDbURL, String pgSchema, String dbSyncId, long batchNum) {
    String sql = String.format("SELECT * FROM %s.db_table_sync_info where db_sync_id='%s' and batch_num=%d and length" +
      "(api_name_ei_map) > 2 and is_deleted=0", pgSchema, dbSyncId, batchNum);
    return this.queryDbTableSyncInfos(pgDbURL, pgSchema, sql);
  }

  private List<DbTableSyncInfoDO> queryDbTableSyncInfos(String pgDbURL, String pgSchema, String sql) {
    List<DbTableSyncInfoDO> dbTableSyncInfoDOS = Lists.newArrayList();
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      jdbcConnection.query(sql, resultSet -> {
        while (resultSet.next()) {
          DbTableSyncInfoDO dbTableSyncInfoDO = createDbSyncInfoFlowFunction.apply(resultSet);
          if (dbTableSyncInfoDO != null) {
            dbTableSyncInfoDOS.add(dbTableSyncInfoDO);
          }
        }
      });
    } catch (Exception e) {
      throw new RuntimeException(String.format("querySyncingFlow error sql:%s,pgBdURL:%s", sql, pgDbURL), e);
    }
    return dbTableSyncInfoDOS;
  }
  /**
   *
   * @param pgDbURL
   * @param pgSchema
   * @param dbSyncId
   * @return
   */
  public Map<String, DbTableSyncInfoDO> queryDbTableSyncInfoMap(String pgDbURL, String pgSchema, String dbSyncId) {
    List<DbTableSyncInfoDO> dbTableSyncInfos = this.queryDBSyncInfoBySyncId(pgDbURL, pgSchema, dbSyncId);
    Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = Maps.newConcurrentMap();
    if (CollectionUtils.isNotEmpty(dbTableSyncInfos)) {
      dbTableSyncInfos.forEach(dbTableSyncInfo -> dbTableSyncInfoMap.put(dbTableSyncInfo.getTableName(), dbTableSyncInfo));
    }
    return dbTableSyncInfoMap;
  }

  /**
   * 按照tableName反查
   * @param pgDbURL
   * @param pgSchema
   * @param dbSyncId
   * @param tableNames
   * @return
   */
  public List<DbTableSyncInfoDO> queryDbTableSyncInfos(String pgDbURL,
                                                       String pgSchema,
                                                       String dbSyncId,
                                                       List<String> tableNames) {
    if (CollectionUtils.isEmpty(tableNames)) {
      return this.queryDBSyncInfoBySyncId(pgDbURL, pgSchema, dbSyncId);
    }
    String arrays = "any(array["+JoinHelper.joinSkipNullOrBlank(',', '\'', tableNames)+"])";
    String sql = String.format("select * from %s.db_table_sync_info where db_sync_id='%s' and table_name=%s and is_deleted=0", pgSchema,dbSyncId, arrays);
    return this.queryDbTableSyncInfos(pgDbURL, pgSchema, sql);
  }

  public int batchDeleteBySyncId(String pgDbURL, String pgSchema, String dbSyncId, List<String> tables) {
    StringBuilder sqlSB = new StringBuilder(String.format("delete from %s.db_table_sync_info where db_sync_id='%s'", pgSchema, dbSyncId));
    if (CollectionUtils.isNotEmpty(tables)) {
      sqlSB.append(" and table_name in(").append(JoinHelper.joinSkipNullOrBlank(',', '\'', tables)).append(")");
    }
    try (JdbcConnection jdbcConnection = biPgDataSource.getConnectionByPgbouncerURL(pgDbURL, pgSchema)) {
      return jdbcConnection.executeUpdate(sqlSB.toString());
    } catch (Exception e) {
      throw new RuntimeException(String.format("batchDeleteBySyncId error dbSyncId:%s,pgBdURL:%s,pgSchema:%s", dbSyncId, pgDbURL, pgSchema), e);
    }
  }

}
