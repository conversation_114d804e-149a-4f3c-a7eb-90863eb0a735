package com.fxiaoke.bi.warehouse.common.provider;

import com.github.mybatis.annotation.DynamicTypeHandler;
import com.github.mybatis.handler.list.ListTypeHandler;
import com.github.mybatis.handler.set.SetTypeHandler;
import com.github.mybatis.util.EntityUtil;
import com.github.mybatis.util.PersistMeta;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.github.mybatis.provider.CrudProvider.FIELD_LEFT;
import static com.github.mybatis.provider.CrudProvider.FIELD_RIGHT;

@Slf4j
public class CommonProvider {
  public static final String FKey = "value";
  public static final String SKey = "skey";
  private static final String normalSql = new SQL().toString();

  /**
   * upsert provider
   *
   * @param paramsMap
   * @return
   */
  public String batchUpsert(@NonNull final Map<String, Object> paramsMap) {
    List<Object> dataList = (List<Object>) paramsMap.get(FKey);
    if (CollectionUtils.isEmpty(dataList)) {
      return normalSql;
    }
    Set<String> onConflictCols = (Set<String>) paramsMap.get(SKey);
    Object obj = dataList.get(0);
    PersistMeta meta = EntityUtil.getMeta(obj.getClass());
    StringBuilder names = new StringBuilder();
    StringBuilder values = new StringBuilder();
    values.append('(');
    String primaryKeys = Joiner.on(",").join(onConflictCols);
    StringBuilder updateFields = new StringBuilder(" ON CONFLICT (" + primaryKeys + ") DO UPDATE SET ");
    int i = 0;
    Iterator<Map.Entry<String, Field>> columnsEntryIter = meta.getColumns().entrySet().iterator();
    while (columnsEntryIter.hasNext()) {
      Map.Entry<String, Field> kv = columnsEntryIter.next();
      Field field = kv.getValue();
      if (i++ != 0) {
        names.append(',');
        values.append(',');
      }
      names.append(FIELD_LEFT).append(kv.getKey()).append(FIELD_RIGHT);
      DynamicTypeHandler typeHandler = (DynamicTypeHandler) field.getAnnotation(DynamicTypeHandler.class);
      if (typeHandler == null) {
        if (field.getType().isAssignableFrom(List.class)) {
          values.append("#'{'value[{0}].")
                .append(field.getName())
                .append(",typeHandler=")
                .append(ListTypeHandler.class.getName())
                .append("'}'");
        } else if (field.getType().isAssignableFrom(Set.class)) {
          values.append("#'{'value[{0}].")
                .append(field.getName())
                .append(",typeHandler=")
                .append(SetTypeHandler.class.getName())
                .append("'}'");
        } else {
          values.append("#'{'value[{0}].").append(field.getName()).append("'}'");
        }
      } else {
        values.append("#'{'value[{0}].")
              .append(field.getName())
              .append(",typeHandler=")
              .append(typeHandler.value())
              .append("'}'");
      }
      if (!onConflictCols.contains(kv.getKey())) {
        updateFields.append(kv.getKey()).append("=").append("EXCLUDED.").append(kv.getKey()).append(",");
      }
    }
    values.append(')');
    StringBuilder insertSql = new StringBuilder();
    insertSql.append("INSERT INTO ")
             .append(getTableName(meta, obj))
             .append('(')
             .append(names.toString())
             .append(')')
             .append(" VALUES ");
    MessageFormat mf = new MessageFormat(values.toString());
    for (int j = 0; j < dataList.size(); j++) {
      if (j != 0) {
        insertSql.append(',');
      }
      insertSql.append(mf.format(new String[] {String.valueOf(j)}));
    }
    insertSql.append(updateFields.substring(0, updateFields.length() - 1));
    return insertSql.toString();
  }


  @SuppressWarnings("unchecked")
  public String batchUpsertOnConflictDoNothing(final Map<String, Object> map) {
    List dataList = (List) map.get(FKey);
    Set<String> primaryKeys = (Set<String>) map.get(SKey);
    if (null == dataList || dataList.isEmpty()) {
      return normalSql;
    }
    Object obj = dataList.get(0);
    PersistMeta meta = EntityUtil.getMeta(obj.getClass());
    StringBuilder fields = new StringBuilder();
    StringBuilder template = new StringBuilder();
    template.append('(');
    int i = 0;
    Map<String, Field> columnMap = Maps.newHashMap();
    dataList.forEach(row -> {
      for (Map.Entry<String, Field> column : meta.getColumns().entrySet()) {
        if (columnMap.containsKey(column.getKey())) {
          continue;
        }
        if (!isNull(column.getValue(), row)) {
          columnMap.putIfAbsent(column.getKey(), column.getValue());
        }
      }
    });
    for (Map.Entry<String, Field> kv : columnMap.entrySet()) {
      if (i++ != 0) {
        fields.append(',');
        template.append(',');
      }
      fields.append(FIELD_LEFT).append(kv.getKey()).append(FIELD_RIGHT);
      Field field = kv.getValue();
      DynamicTypeHandler typeHandler = field.getAnnotation(DynamicTypeHandler.class);
      if (typeHandler == null) {
        if (field.getType().isAssignableFrom(List.class)) {
          template.append("#'{'value[{0}].")
                  .append(field.getName())
                  .append(",typeHandler=")
                  .append(ListTypeHandler.class.getName())
                  .append("'}'");
        } else if (field.getType().isAssignableFrom(Set.class)) {
          template.append("#'{'value[{0}].")
                  .append(field.getName())
                  .append(",typeHandler=")
                  .append(SetTypeHandler.class.getName())
                  .append("'}'");
        } else {
          template.append("#'{'value[{0}].").append(field.getName()).append("'}'");
        }
      } else {
        template.append("#'{'value[{0}].")
                .append(field.getName())
                .append(",typeHandler=")
                .append(typeHandler.value())
                .append("'}'");
      }
    }
    template.append(')');
    StringBuilder insertSql = new StringBuilder();
    insertSql.append("INSERT INTO ")
             .append(getTableName(meta, obj))
             .append('(')
             .append(fields)
             .append(')')
             .append(" VALUES ");
    MessageFormat mf = new MessageFormat(template.toString());
    for (int j = 0; j < dataList.size(); j++) {
      if (j != 0) {
        insertSql.append(',');
      }
      insertSql.append(mf.format(new String[] {String.valueOf(j)}));
    }
    String primaryKeySqlBuilder = Joiner.on(",").join(primaryKeys);
    insertSql.append(" on conflict(").append(primaryKeySqlBuilder).append(") do nothing");
    return insertSql.toString();
  }


  protected boolean isNull(Field field, Object obj) {
    try {
      return field.get(obj) == null;
    } catch (IllegalAccessException var4) {
      return true;
    }
  }

  protected String getTableName(PersistMeta meta, Object obj) {
    if (meta.getPostfix() != null) {
      try {
        return meta.getTableName() + '_' + meta.getPostfix().invoke(obj);
      } catch (Exception var4) {
        log.error("cannot invoke postfix: {}", meta.getPostfix(), var4);
      }
    }

    return meta.getTableName();
  }
}
