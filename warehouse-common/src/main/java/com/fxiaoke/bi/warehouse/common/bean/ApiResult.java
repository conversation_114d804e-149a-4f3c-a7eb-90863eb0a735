package com.fxiaoke.bi.warehouse.common.bean;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;

import java.util.HashMap;
import java.util.Objects;

public class ApiResult extends HashMap<String, Object> {

    public static ApiResult error(String msg) {
        return getApiResult(HttpStatus.SC_INTERNAL_SERVER_ERROR, msg);
    }

    public static ApiResult success(String msg) {
        return getApiResult(HttpStatus.SC_OK, msg);
    }

    public static ApiResult getApiResult(int code, String msg) {
        ApiResult apiResult = new ApiResult();
        apiResult.put("code", code);
        apiResult.put("msg", msg);
        return apiResult;
    }

    public static ApiResult getApiResult(int code, String msg, Object data) {
        ApiResult apiResult = new ApiResult();
        apiResult.put("code", code);
        apiResult.put("msg", msg);
        apiResult.put("data", data);
        return apiResult;
    }

    public ApiResult put(String key, Object value) {
        super.put(key, value);
        return this;
    }

    public static boolean IsSuccess(ApiResult apiResult) {
        return Objects.equals(apiResult.getOrDefault("code", StringUtils.EMPTY), HttpStatus.SC_OK);
    }
}
