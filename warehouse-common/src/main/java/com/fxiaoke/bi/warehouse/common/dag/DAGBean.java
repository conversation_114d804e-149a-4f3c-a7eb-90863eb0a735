package com.fxiaoke.bi.warehouse.common.dag;

import com.fxiaoke.bi.warehouse.common.goal.Node;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * @Author:jief
 * @Date:2024/10/26
 */
@Data
public class DAGBean {
  private Set<DagNode> dagNodes = Sets.newHashSet();
  private Set<DagEdge> dagEdges = Sets.newHashSet();

  public Optional<DagNode> findRootNode() {
    if (CollectionUtils.isNotEmpty(this.dagNodes)) {
      return this.dagNodes.stream().filter(DagNode::isRoot).findFirst();
    }
    return Optional.empty();
  }

  public boolean appendRoot(DagNode root) {
    Optional<DagNode> nodeOptional = this.findRootNode();
    return nodeOptional.map(node -> Objects.equals(node, root)).orElseGet(() -> this.dagNodes.add(root));
  }

  public void appendDagNode(DagNode dagNode) {
    this.dagNodes.add(dagNode);
  }

  public void appendEdges(DagEdge edge) {
    if (!this.dagEdges.isEmpty()) {
      this.dagEdges.forEach(edge1 -> {
        if (Objects.equals(edge1, edge)) {
          edge1.incWeight(1);
        }
      });
    }
    this.dagEdges.add(edge);
  }
}
