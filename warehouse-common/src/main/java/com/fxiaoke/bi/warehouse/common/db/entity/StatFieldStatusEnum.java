package com.fxiaoke.bi.warehouse.common.db.entity;

/**
 * @Author:jief
 * @Date:2024/1/4
 */
public enum StatFieldStatusEnum {
  enable("启用", 1), Initialize("初始化", 2), unable("禁用", 0), System_unable("系统禁用", 3), Object_unable("对象禁用", 4),
  Reservation("保留", 5), Trial_data("试算数据", 6), Batch_delete("批量删除", 7), Batch_unable_delete("批量停用并删除", 8);

  private String desc;
  private int code;


  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public int getCode() {
    return code;
  }

  public void setCode(int code) {
    this.code = code;
  }

  public static boolean isValidStatus(int statusCode) {
    return StatFieldStatusEnum.enable.getCode() == statusCode ||
      StatFieldStatusEnum.Initialize.getCode() == statusCode || StatFieldStatusEnum.Trial_data.getCode() == statusCode;
  }

  public static boolean isNotifyOfflineCalc(int statusCode) {
    return StatFieldStatusEnum.enable.getCode() == statusCode || StatFieldStatusEnum.Initialize.getCode() == statusCode;
  }


  public static boolean isResetInitialize(int statusCode) {
    return StatFieldStatusEnum.enable.getCode() == statusCode || StatFieldStatusEnum.Initialize.getCode() == statusCode;
  }

  //构造方法
  StatFieldStatusEnum(String desc, int code) {
    this.desc = desc;
    this.code = code;
  }
}
