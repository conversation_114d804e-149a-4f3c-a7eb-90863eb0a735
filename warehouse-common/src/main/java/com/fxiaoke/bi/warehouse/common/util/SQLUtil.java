package com.fxiaoke.bi.warehouse.common.util;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.jdbc.JdbcConnection;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 处理SQL工具类
 * <p>
 * Created by huoqi on 17/3/22.
 */
@Slf4j
public class SQLUtil {

  /**
   * 拼接字符串，用单引号包裹, 逗号隔离
   */
  public static <T> String generateValues(List<T> values) {
    if (values == null) {
      return "''";
    }
    List<T> newIds = values.stream()
                           .filter(Objects::nonNull)
                           .map(t -> (T) StringEscapeUtils.escapeSql(t.toString()))
                           .distinct()
                           .collect(Collectors.toList());
    StringBuilder stringBuilder = new StringBuilder("'");
    Joiner.on("','").skipNulls().appendTo(stringBuilder, newIds);
    stringBuilder.append("'");
    return stringBuilder.toString();
  }


  /**
   * 拼接 in表达式
   */
  public static String generateInExpress(List<String> values) {
    return generateInOrNotInExpress(values, "in");
  }

  public static String generateInOrNotInExpress(List<String> values, String op) {
    StringBuilder stringBuilder = new StringBuilder();
    if (!CollectionUtils.isEmpty(values)) {
      stringBuilder.append(" ").append(op).append(" (");
      String valuesStr = SQLUtil.generateValues(values);
      stringBuilder.append(valuesStr);
      stringBuilder.append(" )");
    } else {
      stringBuilder.append(" = '-999999'"); //表示部门下面没有人员
    }
    return stringBuilder.toString();
  }

  public static String generateInIntExpress(List<Integer> values) {
    return generateInOrNotIntExpress(values, "in");
  }

  public static String generateInOrNotIntExpress(List<Integer> values, String op) {
    log.info("in转换列表：{}", JSON.toJSONString(values));
    List<String> stringList = values.stream().map(String::valueOf).distinct().collect(Collectors.toList());
    StringBuilder stringBuilder = new StringBuilder();
    if (!CollectionUtils.isEmpty(stringList)) {
      stringBuilder.append(" ").append(op).append(" (");
      String valuesStr = SQLUtil.generateValues(stringList);
      stringBuilder.append(valuesStr);
      stringBuilder.append(" )");
    } else {
      stringBuilder.append(" = null");
    }
    return stringBuilder.toString();
  }

  public static String replaceTextInString(String value) {
    if (org.apache.commons.lang3.StringUtils.isEmpty(value)) {
      return value;
    }
    value = value.replaceAll("[\t\n\r]", "");

    return escapeSql(value);
  }

  /**
   * 防止SQL注入
   *
   * @param str value
   * @return escape后的value
   */
  public static String escapeSql(String str) {
    if (str == null) {
      return null;
    }
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < str.length(); i++) {
      char src = str.charAt(i);
      switch (src) {
        case '\'' -> sb.append("''");
        case '\\' -> sb.append("\\\\");
        default -> sb.append(src);
      }
    }
    return sb.toString();
  }

  public static <T> List<T> executeQuerySQLWithJdbc(JdbcConnection connection,
                                             String querySQL,
                                             Function<ResultSet, T> mapper) throws Exception {
    List<T> resultList = new ArrayList<>();
    connection.query(querySQL, res -> {
      while (res.next()) {
        T result = mapper.apply(res);
        resultList.add(result);
      }
    });
    return resultList;
  }
}
