package com.fxiaoke.bi.warehouse.common.entity;

import lombok.Data;
import org.bson.types.ObjectId;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author:jief
 * @Date:2024/7/30
 */
@Data
@Table(name = "db_sync_info_flow")
public class DbSyncInfoFlowDO {
  @Id
  @Column(name="id")
  private String id;
  @Column(name = "tenant_id")
  private String tenantId;
  @Column(name = "db_sync_id")
  private String dbSyncId;
  @Column(name = "batch_nums")
  private Long[] batchNums;
  @Column(name = "partition_name")
  private String partitionName;
  @Column(name = "api_name_ei_map")
  private String apiNameEiMap;
  @Column(name = "create_time")
  private long createTime;
  @Column(name = "last_modified_time")
  private long lastModifiedTime;
  @Column(name = "status")
  private Integer status;
  @Column(name = "version")
  private Integer version;
  @Column(name = "is_deleted")
  private int isDeleted;
  @Column(name = "max_sys_modified_time")
  private String maxSysModifiedTime;
  @Column(name = "last_sync_time")
  private Long lastSyncTime;
  @Column(name = "inc_sys_modified_time_range")
  private String incSysModifiedTimeRange;

  public static DbSyncInfoFlowDO of(String dbSyncId,int status,String partitionName){
    DbSyncInfoFlowDO dbSyncInfoFlowDO= new DbSyncInfoFlowDO();
    dbSyncInfoFlowDO.setId(ObjectId.get().toString());
    dbSyncInfoFlowDO.setTenantId("-1");
    dbSyncInfoFlowDO.setDbSyncId(dbSyncId);
    dbSyncInfoFlowDO.setPartitionName(partitionName);
    dbSyncInfoFlowDO.setCreateTime(new Date().getTime());
    dbSyncInfoFlowDO.setLastModifiedTime(new Date().getTime());
    dbSyncInfoFlowDO.setLastSyncTime(new Date().getTime());
    dbSyncInfoFlowDO.setStatus(status);
    dbSyncInfoFlowDO.setVersion(0);
    dbSyncInfoFlowDO.setIsDeleted(0);
    return dbSyncInfoFlowDO;
  }
}
