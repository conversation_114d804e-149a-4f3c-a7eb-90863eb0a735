package com.fxiaoke.bi.warehouse.common.db.er;

import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import org.apache.commons.lang3.StringUtils;

/**
 * clickhouse 列类型
 *
 * <AUTHOR>
 * @since 2023/3/28
 */
public enum ColumnType {
  /**
   * 字符串列类型
   */
  _String {
    @Override
    public String selectItemSql(String tableAlias,
                                String column,
                                ColumnType dstColumnType,
                                ColumnTypeConfig sourceTypeConfig,
                                ColumnTypeConfig dstTypeConfig,
                                boolean withAggFun,
                                String udfType,
                                boolean isLang) {
      String defaultExpr;
      String castDefaultExpr = String.format("toInt64OrNull(%s.%s)", tableAlias, column);
      switch (dstColumnType) {
        case _String:
          //报表支持多语
          if (isLang) {
            String langTableName = TableAliasNaming.tableName(tableAlias) + "_lang";
            String langTableAlias = langTableName + "_" + TableAliasNaming.index(tableAlias);
            String langColumn = column + "_l";
            return String.format("if(empty(%s.%s) or isNull(%s.%s), %s.%s, %s.%s)", langTableAlias, langColumn,
              langTableAlias, langColumn, tableAlias, column, langTableAlias, langColumn);
          }
          return String.format("%s.%s", tableAlias, column);
        case _int:
          return String.format("toInt64OrNull(%s.%s)", tableAlias, column);
        case _Decimal:
          int precision = 20;
          if (dstTypeConfig != null) {
            precision = dstTypeConfig.getOrDefault("precision", 20);
          }
          defaultExpr = String.format("toDecimal128OrNull(%s.%s, %s)", tableAlias, column, precision);
          return defaultTypeFun(udfType, tableAlias, column, null, defaultExpr, "");
        case _Boolean:
          return String.format("toBoolean(%s.%s AS BOOLEAN)", tableAlias, column);
        case _ActionDate:
          String timezone = dstTypeConfig.getOrDefault("timezone", "Asia/Shanghai");
          String fieldType = sourceTypeConfig.getOrDefault(ColumnTypeConfig._ActionDate.FIELD_TYPE, "");
          defaultExpr = String.format("toString(toYYYYMMDD(fromUnixTimestamp64Milli(toInt64OrNull(%s.%s)), '%s'))",
            tableAlias, column, timezone);
          return this.defaultTypeFun(fieldType, tableAlias, column, timezone, defaultExpr, castDefaultExpr);
        case _Agg:
          String aggType = dstTypeConfig.get("aggType").toString().toUpperCase();
          switch (aggType) {
            case "COUNT":
              return withAggFun ?
                String.format("COUNT(%s.%s)", tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            case "SUM":
              int castPrecision = 20;
              return withAggFun ?
                String.format("SUM(toDecimal128OrNull(%s.%s, %s))", tableAlias, column, castPrecision) :
                String.format("toDecimal128OrNull(%s.%s, %s)", tableAlias, column, castPrecision);
            case "SUM2":
              return String.format("toDecimal128OrNull(%s.%s, %s)", tableAlias, column, 20);
            case "DISCOUNT":
              return withAggFun ?
                String.format("count(DISTINCT %s.%s)", tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            case "UNIQ":
            case "UNIQ2":
              String subDefaultExpr = toSubDefaultExpr(tableAlias, column, udfType, dstTypeConfig, castDefaultExpr);
              //agg计算的时候会将空串的排除掉。
              return withAggFun ?
                String.format("uniqExactState(CAST(if(%s.%s = '',null,%s) AS Nullable(String)))", tableAlias, column, subDefaultExpr) :
                String.format("if(%s.%s = '',null,%s)", tableAlias, column, subDefaultExpr);
            default:
              return withAggFun ?
                String.format("%s(%s.%s)", aggType, tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
          }
        case _ARRAY:
          return String.format("splitByChar(',',ifNull(%s.%s,''))", tableAlias, column);
        default:
          return String.format("%s.%s", tableAlias, column);
      }
    }
  },
  /**
   * int列类型
   */
  _int {
    @Override
    public String selectItemSql(String tableAlias,
                                String column,
                                ColumnType dstColumnType,
                                ColumnTypeConfig sourceTypeConfig,
                                ColumnTypeConfig dstTypeConfig,
                                boolean withAggFun,
                                String udfType,
                                boolean isLang) {
      String defaultExpr;
      String castDefaultExpr = String.format("toInt64(NULLIF(%s.%s, 0))", tableAlias, column);
      switch (dstColumnType) {
        case _String:
          defaultExpr = String.format("toString(%s.%s)", tableAlias, column);
          return defaultTypeFun(udfType, tableAlias, column, null, defaultExpr, "");
        case _int:
          return String.format("%s.%s", tableAlias, column);
        case _Decimal:
          int precision = 20;
          if (dstTypeConfig != null) {
            precision = dstTypeConfig.getOrDefault("precision", 20);
          }
          return String.format("toDecimal128OrNull(%s.%s, %s)", tableAlias, column, precision);
        case _Boolean:
          return String.format("if(%s.%s==0, false, true)", tableAlias, column);
        case _ActionDate:
          String timezone = dstTypeConfig.getOrDefault("timezone", "Asia/Shanghai");
          String fieldType = sourceTypeConfig.getOrDefault(ColumnTypeConfig._ActionDate.FIELD_TYPE, "");
          defaultExpr = String.format("toString(toYYYYMMDD(fromUnixTimestamp64Milli(toInt64(NULLIF(%s.%s, 0))), '%s'))",
            tableAlias, column, timezone);
          return this.defaultTypeFun(fieldType, tableAlias, column, timezone, defaultExpr, castDefaultExpr);
        case _Agg:
          String aggType = dstTypeConfig.get("aggType").toString().toUpperCase();
          switch (aggType) {
            case "COUNT":
              return withAggFun ?
                String.format("COUNT(%s.%s)", tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            case "SUM":
              return withAggFun ?
                String.format("SUM(%s.%s)", tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            case "SUM2":
              return String.format("%s.%s", tableAlias, column);
            case "DISCOUNT":
              return withAggFun ?
                String.format("count(DISTINCT toString(%s.%s))", tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            case "UNIQ":
            case "UNIQ2":
              String subDefaultExpr = toSubDefaultExpr(tableAlias, column, udfType, dstTypeConfig, castDefaultExpr);
              return withAggFun ?
                String.format("uniqExactState(CAST(if(%s.%s is null,null,toString(%s)) AS Nullable(String)))", tableAlias, column, subDefaultExpr) :
                String.format("if(%s.%s is null,null,toString(%s))", tableAlias, column, subDefaultExpr);
            default:
              return withAggFun ?
                String.format("%s(%s.%s)", aggType, tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
          }
        default:
          return String.format("%s.%s", tableAlias, column);
      }
    }
  },
  /**
   * number列类型
   */
  _Decimal {
    @Override
    public String selectItemSql(String tableAlias,
                                String column,
                                ColumnType dstColumnType,
                                ColumnTypeConfig sourceTypeConfig,
                                ColumnTypeConfig dstTypeConfig,
                                boolean withAggFun,
                                String udfType,
                                boolean isLang) {
      String defaultExpr;
      String castDefaultExpr = String.format("toInt64(NULLIF(%s.%s, 0))", tableAlias, column);
      switch (dstColumnType) {
        case _String:
          return String.format("toString(%s.%s)", tableAlias, column);
        case _int:
          return String.format("toInt64OrNull(%s.%s)", tableAlias, column);
        case _Decimal:
          return String.format("%s.%s", tableAlias, column);
        case _Boolean:
          return String.format("if(%s.%s==0, false, true)", tableAlias, column);
        case _ActionDate:
          String timezone = dstTypeConfig.getOrDefault("timezone", "Asia/Shanghai");
          String fieldType = sourceTypeConfig.getOrDefault(ColumnTypeConfig._ActionDate.FIELD_TYPE, "");
          defaultExpr = String.format("toString(toYYYYMMDD(fromUnixTimestamp64Milli(toInt64(NULLIF(%s.%s, 0))), '%s'))",
            tableAlias, column, timezone);
          return this.defaultTypeFun(fieldType, tableAlias, column, timezone, defaultExpr, castDefaultExpr);
        case _Agg:
          String aggType = dstTypeConfig.get("aggType").toString().toUpperCase();
          switch (aggType) {
            case "COUNT":
              return withAggFun ?
                String.format("COUNT(%s.%s)", tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            case "SUM":
              return withAggFun ?
                String.format("SUM(%s.%s)", tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            case "SUM2":
              return String.format("%s.%s", tableAlias, column);
            case "DISCOUNT":
              return withAggFun ?
                String.format("uniq(toString(%s.%s))", tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            case "UNIQ":
            case "UNIQ2":
              String subDefaultExpr = toSubDefaultExpr(tableAlias, column, udfType, dstTypeConfig, castDefaultExpr);
              return withAggFun ?
                String.format("uniqExactState(CAST(if(%s.%s is null,null,toString(%s)) AS Nullable(String)))", tableAlias, column, subDefaultExpr) :
                String.format("if(%s.%s is null,null,toString(%s))", tableAlias, column, subDefaultExpr);
            default:
              return withAggFun ?
                String.format("%s(%s.%s)", aggType, tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
          }
        default:
          return String.format("%s.%s", tableAlias, column);
      }
    }
  },
  /**
   * boolean列类型
   */
  _Boolean {
    public String selectItemSql(String tableAlias,
                                String column,
                                ColumnType dstColumnType,
                                ColumnTypeConfig sourceTypeConfig,
                                ColumnTypeConfig dstTypeConfig,
                                boolean withAggFun,
                                String udfType,
                                boolean isLang) {
      switch (dstColumnType) {
        case _String:
          return String.format("toString(%s.%s)", tableAlias, column);
        case _int:
          return String.format("toInt64OrNull(%s.%s)", tableAlias, column);
        case _Decimal:
          return String.format("toDecimal64OrNull(%s.%s)", tableAlias, column);
        case _Boolean:
          return String.format("%s.%s", tableAlias, column);
        case _ActionDate:
          return String.format("CAST(%s.%s AS DATE)", tableAlias, column);
        case _Agg:
          String aggType = dstTypeConfig.get("aggType").toString().toUpperCase();
          switch (aggType) {
            case "COUNT":
              return withAggFun ?
                String.format("COUNT(%s.%s)", tableAlias, column) :
                String.format("toString(%s.%s)", tableAlias, column);
            case "DISCOUNT":
              return String.format("uniq(toString(%s.%s))", tableAlias, column);
            case "UNIQ":
            case "UNIQ2":
              return withAggFun ?
                String.format("uniqExactState(CAST(if(%s.%s is null,null,toString(%s.%s)) AS Nullable(String)))", tableAlias, column,
                  tableAlias, column) :
                String.format("if(%s.%s is null,null,toString(%s.%s))", tableAlias, column, tableAlias, column);
            default:
              return withAggFun ?
                String.format("%s(%s.%s)", aggType, tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
          }
        default:
          return String.format("%s.%s", tableAlias, column);
      }
    }
  },
  /**
   * action_date列类型
   */
  _ActionDate {
  },
  /**
   * 数组
   */
  _ARRAY {
    public String selectItemSql(String tableAlias,
                                String column,
                                ColumnType dstColumnType,
                                ColumnTypeConfig sourceTypeConfig,
                                ColumnTypeConfig dstTypeConfig,
                                boolean withAggFun,
                                String udfType,
                                boolean isLang) {
      switch (dstColumnType) {
        case _Agg:
          String aggType = dstTypeConfig.get("aggType").toString().toUpperCase();
          switch (aggType) {
            case "COUNT", "DISCOUNT", "UNIQ", "UNIQ2" -> {
              return withAggFun ?
                String.format("uniqExactArrayState(if(%s.%s IS NULL,array(NULL),%s.%s))", tableAlias, column,
                  tableAlias, column) :
                String.format("if(%s.%s is null,array(NULL),%s.%s)", tableAlias, column, tableAlias, column);
            }
            default -> {
              return withAggFun ?
                String.format("%s(%s.%s)", aggType, tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            }
          }
        default:
          return String.format("%s.%s", tableAlias, column);
      }
    }
  },
  /**
   * agg聚合列类型
   */
  _Agg,

  /**
   * agg聚合列类型  AggregateFunction
   */
  _AggFunc {
    public String selectItemSql(String tableAlias,
                                String column,
                                ColumnType dstColumnType,
                                ColumnTypeConfig sourceTypeConfig,
                                ColumnTypeConfig dstTypeConfig,
                                boolean withAggFun,
                                String udfType,
                                boolean isLang) {
      switch (dstColumnType) {
        case _Agg:
          String aggType = dstTypeConfig.get("aggType").toString().toUpperCase();
          switch (aggType) {
            case "UNIQ3" -> {
              return withAggFun ?
                String.format("uniqExactMergeState(%s.%s)", tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            }
            default -> {
              return withAggFun ?
                String.format("%s(%s.%s)", aggType, tableAlias, column) :
                String.format("%s.%s", tableAlias, column);
            }
          }
        default:
          return String.format("%s.%s", tableAlias, column);
      }
    }
  },

  _CustomDim {
    @Override
    public String selectItemSql(String tableAlias,
                                String column,
                                ColumnType dstColumnType,
                                ColumnTypeConfig sourceTypeConfig,
                                ColumnTypeConfig dstTypeConfig,
                                boolean withAggFun,
                                String udfType,
                                boolean isLang) {
      String customDimType = sourceTypeConfig.get(CustomDimType.CUSTOM_DIM_TYPE).toString();
      return CustomDimType.valueOf(customDimType).generateCustomMultiIfClause(sourceTypeConfig, tableAlias, column, withAggFun, isLang);
    }
  };

  /**
   * 获取类型对应的sql select item
   *
   * @param tableAlias
   * @param column
   * @param dstColumnType
   * @param dstTypeConfig
   * @return
   */
  public String selectItemSql(String tableAlias,
                              String column,
                              ColumnType dstColumnType,
                              ColumnTypeConfig sourceTypeConfig,
                              ColumnTypeConfig dstTypeConfig,
                              boolean withAggFun,
                              String udfType,
                              boolean isLang) {
    return switch (dstColumnType) {
      case _String -> String.format("%s.%s", tableAlias, column);
      case _int -> String.format("toInt64OrNull(%s.%s)", tableAlias, column);
      case _Decimal -> String.format("toDecimal64OrNull(%s.%s)", tableAlias, column);
      case _Boolean -> String.format("%s.%s", tableAlias, column);
      case _ActionDate -> String.format("CAST(%s.%s AS DATE)", tableAlias, column);
      case _Agg -> String.format("CAST(%s.%s AS DECIMAL)", tableAlias, column);
      default -> String.format("%s.%s", tableAlias, column);
    };
  }

  public String whereExprSql(String tableAlias, String column, String operator, String value1, String value2) {
    switch (this) {
      case _String:
        return String.format("%s.%s %s '%s'", tableAlias, column, operator, value1);
      case _int:
        return String.format("%s.%s %s %s", tableAlias, column, operator, value1);
      case _Decimal:
        return String.format("%s.%s %s %s", tableAlias, column, operator, value1);
      case _Boolean:
        return String.format("%s.%s %s %s", tableAlias, column, operator, value1);
      case _ActionDate:
        return String.format("%s.%s %s %s", tableAlias, column, operator, value1);
      case _Agg:
        return String.format("%s.%s %s %s", tableAlias, column, operator, value1);
      default:
        return String.format("%s.%s %s %s", tableAlias, column, operator, value1);
    }
  }

  /**
   * 获取类型对应的默认值
   *
   * @return
   */
  public String defaultStringValue(ColumnTypeConfig sourceTypeConfig, ColumnTypeConfig dstTypeConfig) {
    switch (this) {
      case _String:
        return "''";
      case _int:
        return "0";
      case _Decimal:
        return "0";
      case _Boolean:
        return "false";
      case _ActionDate:
        String fieldType = sourceTypeConfig.getOrDefault(ColumnTypeConfig._ActionDate.FIELD_TYPE, "");
        if (StringUtils.isNotBlank(fieldType) && StringUtils.equalsAny(fieldType, "date", "date_time", "time")) {
          return "''";
        }
        return "'00000000'";
      case _Agg:
        return "0";
      case _ARRAY: {
        ColumnType itemColumnType = sourceTypeConfig.getOrDefault(ColumnTypeConfig._Array.ITEM_TYPE,
          ColumnType._String);
        if (itemColumnType == ColumnType._int) {
          return "[NULL]";
        }
        return "['']";
      }
      case _CustomDim: {
        return "''";
      }
      default:
        return "''";
    }
  }

  /**
   * 判断默是否返回默认值函数
   *
   * @return
   */
  public String defaultValueFun(ColumnTypeConfig dstTypeConfig, String fieldType, String tableName, String tenantId) {
    if (this == ColumnType._ARRAY) {
      return "if(notEmpty(${checkValue}),${checkValue},${defaultValue})";
    }
    Object aggType = dstTypeConfig.get("aggType");
    if (aggType != null) {
      String type = aggType.toString().toUpperCase();
      switch (type) {
        case "UNIQ", "UNIQ2", "UNIQ3" -> {
          return "${checkValue}";
        }
      }
    }
//    String checkValue = etlMultiValueFun(tenantId, tableName, "${checkValue}", fieldType);
    return "coalesce(${checkValue},${defaultValue})";
  }

  /**
   * 多值字段处理
   *
   * @param column
   * @return
   */
  public static String etlMultiValueFun(String tenantId, String tableName, String column, String fieldType) {
    if (GrayManager.isAllowByRule("multi_value_replace_all_eis", tenantId) &&
            StringUtils.equalsAny(tableName, "object_data", "object_data_lang") &&
            WarehouseConfig.convertArrayType.contains(fieldType)) {
      return String.format("replaceRegexpAll(replaceRegexpAll(toString(%s),'[\\\\[\\\\]{}\\\"\\']',''), '\\|',',')", column);
    }
    return column;
  }

  /**
   * 根据udf中的Type进行类型转换
   * 此方法用于需要进一步加内置函数强转,
   * 如果只是字段类型强转->走 dstColumnType 判断
   * 即 DimRule.createDimConfig() -> PGColumnType.createDimDistType()
   * @param udfType
   * @castExpr 目前只有日期会用到  dstColumnType = _ActionDate
   * @return
   */
  public String defaultTypeFun(String udfType,
                               String tableAlias,
                               String column,
                               String timezoneTz,
                               String defaultExpr,
                               String castDefaultExpr) {
    if (StringUtils.isBlank(udfType)) {
      return defaultExpr;
    }
    String timezone = StringUtils.isBlank(timezoneTz) ? "Asia/Shanghai" : timezoneTz;
    return switch (udfType) {
      case "date2" -> String.format("substring(%s.%s,1,8)", tableAlias, column);
      case "date" -> String.format("toString(toYYYYMMDD(fromUnixTimestamp64Milli(%s), '%s'))", castDefaultExpr, timezone);
      case "date_time","time" -> String.format("substring(toString(toYYYYMMDDhhmmss(fromUnixTimestamp64Milli(%s), " +
        "'%s')),1,12)", castDefaultExpr, timezone);
      //_String -> _Decimal
      case "image" -> String.format("JSONLength(ifNull(%s.%s, '[]'))", tableAlias, column);
      //_int -> _String
      case "true_or_false" -> String.format("if(%s.%s=0, 'true', 'false')", tableAlias, column);
      default -> defaultExpr;
    };
  }

  /**
   * 构建目标pgsql片段
   *
   * @return
   */
  public String timeSelectSQL(String tableAlias,
                              String column,
                              ColumnTypeConfig dstTypeConfig,
                              String formatStr,
                              String tableName) {
    if ("object_data".equals(tableName) && ("create_time".equals(column) || "last_modified_time".equals(column))) {
      return "to_char(" + tableAlias + "." + column + ", '" + formatStr + "')";
    }
    String atTimeZone = "";
    String timeZone = dstTypeConfig.getOrDefault("timezone", "Asia/Shanghai");
    if (StringUtils.isNotBlank(timeZone)) {
      atTimeZone = String.format(" AT TIME ZONE '%s' ", timeZone);
    }
    return switch (this) {
      case _int, _Decimal ->
        "to_char(to_timestamp(" + tableAlias + "." + column + "/1000) " + atTimeZone + ", '" + formatStr + "')";
      default ->
        "to_char(to_timestamp(" + tableAlias + "." + column + "::BIGINT/1000) " + atTimeZone + ", '" + formatStr + "')";
    };
  }

  /**
   * 指标统计日期字段需要格式化
   * @param tableAlias
   * @param column
   * @param udfType
   * @param dstTypeConfig
   * @return
   */
  public String toSubDefaultExpr(String tableAlias, String column, String udfType, ColumnTypeConfig dstTypeConfig, String castDefaultExpr) {
    String subDefaultExpr = String.format("%s.%s", tableAlias, column);
    if (StringUtils.equalsAny(udfType, "date", "date_time", "time")) {
      String timezone = "date".equals(udfType) ? "Asia/Shanghai" : dstTypeConfig.getOrDefault("timezone", "Asia/Shanghai");
      return this.defaultTypeFun(udfType, tableAlias, column, timezone, subDefaultExpr, castDefaultExpr);
    }
    return subDefaultExpr;
  }

}
