package com.fxiaoke.bi.warehouse.common.dag;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @Author:jief
 * @Date:2024/10/26
 */
@Data
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
public class DagNode {
  private String id;
  private String description;
  private boolean root;

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DagNode dagNode = (DagNode) o;
    return Objects.equals(id, dagNode.id);
  }

  @Override
  public int hashCode() {
    return Objects.hashCode(id);
  }
}
