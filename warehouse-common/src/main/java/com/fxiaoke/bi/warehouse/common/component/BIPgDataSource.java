package com.fxiaoke.bi.warehouse.common.component;

import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Optional;

/**
 * @Author:jief
 * @Date:2024/8/6
 */
@Slf4j
public class BIPgDataSource implements InitializingBean {
  private final MybatisBITenantPolicy mybatisBITenantPolicy;
  private String userName;
  private String passWord;

  public BIPgDataSource(MybatisBITenantPolicy mybatisBITenantPolicy) {
    this.mybatisBITenantPolicy = mybatisBITenantPolicy;
  }
  public void init() {
    ConfigFactory.getConfig("fs-bi-statistic-db", iConfig -> {
      userName = iConfig.get("username");
      try {
        passWord = PasswordUtil.decode(iConfig.get("password"));
      } catch (Exception e) {
        log.error("PasswordUtil decode password = {} error:", passWord, e);
        throw new RuntimeException("数据库连接密码解密失败，数据库连接异常，请稍后重试！");//IgnoreI18n
      }
    });
  }

  /**
   * 根据企业id获取db名称
   * @param tenantId 企业id
   * @return
   */
  private Optional<String> findDBNameByTenantId(String tenantId){
    RouterInfo routerInfo=this.getRouterInfo(tenantId);
    if(routerInfo!=null){
      String jdbcURL=routerInfo.getJdbcUrl();
      if(StringUtils.isNotBlank(jdbcURL)){
        return Optional.of(jdbcURL.substring(jdbcURL.lastIndexOf("/")+1));
      }
    }
    return Optional.empty();
  }

  /**
   * <p>获取router info</p>
   *
   * @param tenantId
   * @return
   */
  public RouterInfo getRouterInfo(String tenantId) {
    return mybatisBITenantPolicy.getRouterInfo(tenantId);
  }

  /**
   * 根据企业id
   * @param tenantId
   * @return
   */
  public String getPgBouncerJdbcUrlByTenantId(String tenantId){
    String jdbcURL = mybatisBITenantPolicy.getPgBouncerJdbcURL(tenantId);
    if (StringUtils.isEmpty(jdbcURL)) {
      return null;
    }
    return jdbcURL;
  }

  public JdbcConnection getJdbcConnection(String tenantId) {
    String jdbcURL = mybatisBITenantPolicy.getPgBouncerJdbcURL(tenantId);
    if (StringUtils.isEmpty(jdbcURL)) {
      return null;
    }
    return new JdbcConnection(jdbcURL, this.userName, this.passWord);
  }

  public JdbcConnection getJdbcConnection(String tenantId, boolean readOnly) {
    String jdbcURL = mybatisBITenantPolicy.getPgBouncerJdbcURL(tenantId);
    if (StringUtils.isEmpty(jdbcURL)) {
      return null;
    }
    return new JdbcConnection(jdbcURL, this.userName, this.passWord);
  }

  /**
   * <p>获取pg连接</p>
   *
   * @param tenantID 74745
   * @return JdbcConnection
   */
  @Deprecated
  public JdbcConnection getJdbcConnectionByTenantId(String tenantID) {
    String jdbcURL = mybatisBITenantPolicy.getPgBouncerJdbcURL(tenantID);
    if (StringUtils.isEmpty(jdbcURL)) {
      return null;
    }
    return new JdbcConnection(jdbcURL, this.userName, this.passWord);
  }

  /**
   * 获取slaveDB connection
   *
   * @param tenantId 企业id
   * @return jdbcConnection
   */
  public JdbcConnection getSlaveConnection(String tenantId) {
    String jdbcURL = mybatisBITenantPolicy.getSlaveJdbcUrl(tenantId);
    if (StringUtils.isEmpty(jdbcURL)) {
      return null;
    }
    return new JdbcConnection(jdbcURL, this.userName, this.passWord);
  }

  /**
   * <p>判断某个企业是否做了schema隔离</p>
   *
   * @param tenantId
   * @return
   */
  public boolean isStandalone(String tenantId) {
    return mybatisBITenantPolicy.isStandalone(tenantId);
  }

  /**
   * 获取pgbouncer jdbc 连接
   * @param jdbcURL pgbouncer url
   * @return JdbcConnection
   */
  public JdbcConnection getConnectionByPgbouncerURL(String jdbcURL){
    JdbcConnection jdbcConnection = null;
    try {
      String pgBouncerURL = mybatisBITenantPolicy.getPgBouncerURLByURL(jdbcURL,true);
      jdbcConnection = new JdbcConnection(pgBouncerURL, this.userName, this.passWord);
    } catch (Exception e) {
      log.error("get connection error.", e);
    }
    return jdbcConnection;
  }

  /**
   * 获取pgbouncer jdbc 连接
   * @param jdbcURL pgbouncer url
   * @return JdbcConnection
   */
  public JdbcConnection getConnectionByPgbouncerURL(String jdbcURL, String schemaName) {
    JdbcConnection jdbcConnection = null;
    try {
      String pgBouncerURL;
      if (schemaName.startsWith("sch_")) {
        pgBouncerURL = mybatisBITenantPolicy.getPgBouncerJdbcURL(schemaName.substring(4));
      } else {
        pgBouncerURL = mybatisBITenantPolicy.getPgBouncerURLByURL(jdbcURL, true);
      }
      jdbcConnection = new JdbcConnection(pgBouncerURL, this.userName, this.passWord);
    } catch (Exception e) {
      log.error("get connection error.", e);
    }
    return jdbcConnection;
  }

  /**
   * <p>根据url获取jdbc连接</p>
   *
   * @param dbServerUrl
   * @param isMaster
   * @return
   */
  public JdbcConnection getPgBouncerConnectionByJDBCURL(String dbServerUrl, boolean isMaster) {
    String pgBouncerURL = mybatisBITenantPolicy.getPgBouncerURLByURL(dbServerUrl, isMaster);
    JdbcConnection jdbcConnection = null;
    try {
      jdbcConnection = new JdbcConnection(pgBouncerURL, this.userName, this.passWord);
    } catch (Exception e) {
      log.error("get connection error.", e);
    }
    return jdbcConnection;
  }

  public String getMasterIp(String tenantId) {
    return mybatisBITenantPolicy.getPgMasterIp(tenantId);
  }

  /**
   * <p>关闭连接</p>
   */
  public void close(Connection connection) {
    if (connection != null) {
      try {
        connection.close();
      } catch (SQLException e) {
        log.error("close connection error.", e);
      }
    }
  }
  @Override
  public void afterPropertiesSet() throws Exception {
    this.init();
  }
}
