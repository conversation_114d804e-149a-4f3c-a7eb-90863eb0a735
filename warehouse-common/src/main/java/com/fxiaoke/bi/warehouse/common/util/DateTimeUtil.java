package com.fxiaoke.bi.warehouse.common.util;

import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by jief on 2021/4/21.
 */
public class DateTimeUtil {
  public static final DateTimeFormatter formatterYYYYMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
  private static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
  public static final DateTimeFormatter dateTimeFormatterWithZone = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss Z");
  public static final ConcurrentHashMap<String, ZoneOffset> timeZoneOffsetMap = new ConcurrentHashMap<>();
  public static final Pattern dateTimeWithZoneRex = Pattern.compile("([0-9]{4})-([0-9]{2})-([0-9]{2})([T]|\\s+)([0-9]{2}):([0-9]{2}):([0-9]{2})(\\.[0-9]+)?(\\s*)([\\-\\+].+)");
  public static final Pattern dateTimeWithOutZoneRex = Pattern.compile("([0-9]{4})-([0-9]{2})-([0-9]{2})([T]|\\s+)([0-9]{2}):([0-9]{2}):([0-9]{2})(\\.[0-9]+)?(\\s*)");
  /**
   * 获取当前时间
   *
   * @param zoneId
   * @return
   */
  public static LocalDateTime getNow(ZoneId zoneId) {
    return LocalDateTime.now(zoneId);
  }

  public static LocalDateTime coverActionDate2DateTime(String actionDate) {
    int year = Integer.parseInt(actionDate.substring(0, 4));
    int month = Integer.parseInt(actionDate.substring(4, 6));
    int day = Integer.parseInt(actionDate.substring(6, 8));
    return LocalDateTime.of(year, month, day, 0, 0, 0);
  }

  /**
   * @param dateTime
   * @param days
   * @return
   */
  public static LocalDateTime someDaysAgo(LocalDateTime dateTime, int days) {
    return dateTime.plusDays(0 - days);
  }

  /**
   * 生成 时分秒都是0的日期对象
   *
   * @param dateTime
   * @return
   */
  public static LocalDateTime getBeginOfTheDay(LocalDateTime dateTime) {
    return dateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
  }


  /**
   * 时间戳获取localDateTime
   *
   * @param timestamp timestamp
   * @return A date-time without a time-zone in the ISO-8601 calendar system,
   */
  public static LocalDateTime timestamp2DateTime(long timestamp) {
    return new Timestamp(timestamp).toLocalDateTime();
  }

  /**
   * 根据 localDateTime
   *
   * @param dateTime A date-time without a time-zone in the ISO-8601 calendar system,
   * @return milliseconds since January 1, 1970, 00:00:00 GMT.
   * A negative number is the number of milliseconds before
   * January 1, 1970, 00:00:00 GMT.
   */
  public static long dateTime2Timestamp(LocalDateTime dateTime) {
    return Timestamp.valueOf(dateTime).getTime();
  }

  /**
   * 毫秒值转转日期格式化 YYYYMMdd
   */
  public static String bigIntConvertYYYYMMdd(long timestamp) {
    Timestamp t = new Timestamp(timestamp);
    return formatterYYYYMMdd.format(t.toLocalDateTime());
  }

  /**
   * 时间戳按照时区格式化
   *
   * @param timestamp bigint
   * @param zoneId    eg:Asia/Shanghai
   * @param formatter eg:yyyy-MM-dd HH:mm:ss Z
   * @return
   */
  public static String toChar(long timestamp, ZoneId zoneId, DateTimeFormatter formatter) {
    ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timestamp), zoneId);
    return zonedDateTime.format(formatter);
  }

  /**
   * 时间戳按照时区格式化
   *
   * @param timestamp bigint
   * @param zoneId    eg:Asia/Shanghai
   * @return
   */
  public static ZonedDateTime toZonedDateTimeOfBeginDate(long timestamp, ZoneId zoneId) {
    ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timestamp), zoneId);
    return zonedDateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
  }

  public static long toEpochMilli(ZonedDateTime zonedDateTime) {
    return zonedDateTime.toInstant().toEpochMilli();
  }

  /**
   * 20220801转成对应时区的时间戳
   *
   * @param actionDate 20220801
   * @param zoneId     Asia/Shanghai
   * @return 1659283200000
   */
  public static long covertYYYYMMDD2Timestamp(String actionDate, ZoneId zoneId) {
    int year = Integer.parseInt(actionDate.substring(0, 4));
    int month = Integer.parseInt(actionDate.substring(4, 6));
    int day = Integer.parseInt(actionDate.substring(6, 8));
    return ZonedDateTime.of(LocalDateTime.of(year, month, day, 0, 0, 0), zoneId).toInstant().toEpochMilli();
  }

  public static long covertYYYYMMDD2Timestamp(String actionDate,long plusDays, ZoneId zoneId) {
    int year = Integer.parseInt(actionDate.substring(0, 4));
    int month = Integer.parseInt(actionDate.substring(4, 6));
    int day = Integer.parseInt(actionDate.substring(6, 8));
    return ZonedDateTime.of(LocalDateTime.of(year, month, day, 0, 0, 0).plusDays(plusDays), zoneId).toInstant().toEpochMilli();
  }

  /**
   * 20220801转成对应时区的时间戳
   *
   * @param localDateTime 20220801
   * @param zoneId        Asia/Shanghai
   * @return 1659283200000
   */
  public static long covertYYYYMMDD2Timestamp(LocalDateTime localDateTime, ZoneId zoneId) {
    return ZonedDateTime.of(localDateTime, zoneId).toInstant().toEpochMilli();
  }

  /**
   * 将时间戳由一个时区转到另一个时区
   *
   * @param dateTime  2022-02-08 18:26:46
   * @param withZone  Asia/Shanghai
   * @param atZone    America/New_York
   * @param formatter yyyy-MM-dd HH:mm:ss Z
   * @return
   */
  public static String covertTimestampToOtherZone(String dateTime,
                                                  ZoneId withZone,
                                                  ZoneId atZone,
                                                  DateTimeFormatter formatter) {
    Timestamp timestamp = Timestamp.valueOf(dateTime);
    long before = ZonedDateTime.of(timestamp.toLocalDateTime(), withZone).toInstant().toEpochMilli();
    ZonedDateTime after = ZonedDateTime.ofInstant(Instant.ofEpochMilli(before), atZone);
    return after.format(formatter);
  }

  /**
   * 将时间戳由一个时区转到另一个时区
   *
   * @param dateTime  2022-02-08 18:26:46
   * @param offset    +08:00
   * @param atZone    America/New_York
   * @param formatter yyyy-MM-dd HH:mm:ss Z
   * @return
   */
  public static String covertDateTimeWithOffset2OtherZone(String dateTime,
                                                  ZoneOffset offset,
                                                  ZoneId atZone,
                                                  DateTimeFormatter formatter) {
    int year=Integer.parseInt(dateTime.substring(0,4));
    int mm=Integer.parseInt(dateTime.substring(5,7));
    int dd=Integer.parseInt(dateTime.substring(8,10));
    int hr=Integer.parseInt(dateTime.substring(11,13));
    int mi=Integer.parseInt(dateTime.substring(14,16));
    int ss=Integer.parseInt(dateTime.substring(17,19));
    long t1= ZonedDateTime.ofInstant(LocalDateTime.of(year,mm,dd,hr,mi,ss),offset,offset).toInstant().toEpochMilli();
    ZonedDateTime after = ZonedDateTime.ofInstant(Instant.ofEpochMilli(t1), atZone);
    return after.format(formatter);
  }

  /**
   * 将日期格式的数据转换成对应时区的时间戳
   *
   * @param dateTime 2022-02-08 18:26:46
   * @param zoneId   Asia/Shanghai
   * @return
   */
  public static long extract(String dateTime, ZoneId zoneId) {
    Timestamp timestamp = Timestamp.valueOf(dateTime);
    return ZonedDateTime.of(timestamp.toLocalDateTime(), zoneId).toInstant().toEpochMilli();
  }

  /**
   * 日期对象转日期格式化 YYYYMMdd
   */
  public static String dateTimeConvertYYYYMMdd(LocalDateTime localDateTime) {
    Timestamp t = Timestamp.valueOf(localDateTime);
    return formatterYYYYMMdd.format(t.toLocalDateTime());
  }

  public static ZoneOffset timeZoneOffset(String zoneId) {
    return timeZoneOffsetMap.computeIfAbsent(zoneId, key -> ZonedDateTime.now(ZoneId.of(key)).getOffset());
  }

  /**
   * 获取{@link ZoneOffset}
   * @param dateTime 2022-09-01 11:00:12.999999 +0800
   * @return {@link ZoneOffset}
   */
  public static ZoneOffset getZoneOffsetFromDateTime(String dateTime) {
    Matcher matcher = dateTimeWithZoneRex.matcher(dateTime);
    if (matcher.matches()) {
      String offset = matcher.group(matcher.groupCount());
      if (StringUtils.isNotBlank(offset)) {
        return ZoneOffset.of(offset);
      }
    }
    return null;
  }

  /**
   * 获取{@link ZoneOffset}
   * @param dateTime 2022-09-01 11:00:12.999999 +0800
   * @param dateTimeFormatter yyyy-MM-dd HH:mm:ss.SSSSSS Z
   * @return {@link ZoneOffset}
   */
  public static ZoneOffset getZoneOffsetFromDateTime(String dateTime,DateTimeFormatter dateTimeFormatter)throws DateTimeParseException {
   return ZonedDateTime.parse(dateTime,dateTimeFormatter).getOffset();
  }

  public static long convertToTimestamp(String dateString) {
    return convertToTimestamp(dateString, DEFAULT_PATTERN);
  }

  public static long convertToTimestamp(String dateString, String pattern) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
    LocalDateTime dateTime = LocalDateTime.parse(dateString, formatter);
    return dateTime.toInstant(ZoneOffset.UTC).toEpochMilli();
  }
}
