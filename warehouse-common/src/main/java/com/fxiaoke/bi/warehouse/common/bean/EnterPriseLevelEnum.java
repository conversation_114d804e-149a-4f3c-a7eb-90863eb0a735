package com.fxiaoke.bi.warehouse.common.bean;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum EnterPriseLevelEnum {

    /**
     * 普通企业
     */
    COMMON__ENTERPRISE(1),

    /**
     * 沙盒企业
     */
    SANDBOX_ENTERPRISE(2),

    /**
     * 沙盒隔离企业
     */
    SANDBOX_SCHEMA_ENTERPRISE(3),

    /**
     * schema隔离企业
     */
    SCHEMA_ENTERPRISE(4);

    private int status;

    EnterPriseLevelEnum(int status) {
        this.status = status;
    }

    public static EnterPriseLevelEnum getEnterPriseLevelEnum(boolean standalone, String ea) {
        if (StringUtils.isBlank(ea)) {
            return COMMON__ENTERPRISE;
        }
        return ea.endsWith("_sandbox") ?
          standalone ? SANDBOX_SCHEMA_ENTERPRISE : SANDBOX_ENTERPRISE :
          standalone ? SCHEMA_ENTERPRISE : COMMON__ENTERPRISE;
    }
}
