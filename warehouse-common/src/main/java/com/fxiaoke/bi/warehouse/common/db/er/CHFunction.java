package com.fxiaoke.bi.warehouse.common.db.er;

import com.fxiaoke.bi.warehouse.common.exceptions.ArgsException;

/**
 * ch 函数
 *
 * @Author:jief
 * @Date:2024/3/14
 */
public enum CHFunction {
  coalesce {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("coalesce函数参数不足"));
      }
      String argsStr = String.join(",", args);
      return String.format("coalesce(%s)", argsStr);
    }
  }, greatest {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("greatest函数参数不足"));
      }
      String argsStr = String.join(",", args);
      return String.format("greatest(%s)", argsStr);
    }
  }, least {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("least函数参数不足"));
      }
      String argsStr = String.join(",", args);
      return String.format("least(%s)", argsStr);
    }
  }, MAX {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("MAX函数参数不足"));
      }
      String argsStr = String.join(",", args);
      return String.format("max(%s)", argsStr);
    }
  }, SUM {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("sum函数参数不足"));
      }
      return String.format("SUM(%s)", args[0]);
    }
  }, COUNT {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("count函数参数不足"));
      }
      return String.format("count(%s)", args[0]);
    }
  }, DISCOUNT {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("count DISTINCT函数参数不足"));
      }
      return String.format("count(DISTINCT %s)", args[0]);
    }
  }, uniqExactState {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("uniqExactState 函数参数不足"));
      }
      return String.format("uniqExactState(%s)", args[0]);
    }
  }, uniqExactMergeState {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("uniqExactStateMerge 函数参数不足"));
      }
      return String.format("uniqExactStateMerge(%s)", args[0]);
    }
  }, uniqExactArrayState {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("uniqExactArrayState 函数参数不足"));
      }
      return String.format("uniqExactArrayState(%s)", args[0]);
    }
  }, IF {
    @Override
    public String build(String... args) {
      if (args.length < 3) {
        throw new RuntimeException(new ArgsException("if函数参数不足"));
      }
      return String.format("if(%s,%s,%s)", args[0], args[1], args[2]);
    }
  }, ifNull {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("ifNull 函数参数不足"));
      }
      return String.format("ifNull(%s,%s)", args[0], args[1]);
    }
  }, sumIf {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("sumIf 函数参数不足"));
      }
      return String.format("sumIf(%s,%s)", args[0], args[1]);
    }
  }, countIf {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("countIf 函数参数不足"));
      }
      return String.format("countIf(%s,%s)", args[0], args[1]);
    }
  }, DISCOUNTIf {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("countIf DISTINCT函数参数不足"));
      }
      return String.format("countIf(DISTINCT %s,%s)", args[0], args[1]);
    }
  }, uniqExactStateIf {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("uniqExactStateIf 函数参数不足"));
      }
      return String.format("uniqExactStateIf(%s,%s)", args[0], args[1]);
    }
  }, uniqExactMergeStateIf {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("uniqExactMergeStateIf 函数参数不足"));
      }
      return String.format("uniqExactMergeStateIf(%s,%s)", args[0], args[1]);
    }
  }, toUInt8 {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("toUInt8函数参数不足"));
      }
      return String.format("toUInt8(%s)", args[0]);
    }
  }, toInt64OrNull {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("toInt64OrNull 函数参数不足"));
      }
      return String.format("toInt64OrNull(%s)", args[0]);
    }
  }, toDecimal64OrNull {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("toDecimal64OrNull 函数参数不足"));
      }
      return String.format("toDecimal64OrNull(%s)", args[0]);
    }
  }, toDecimal128OrZero {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("toDecimal128OrZero 函数参数不足"));
      }
      return String.format("toDecimal128OrZero(%s,%s)", args[0], args[1]);
    }
  }, splitByChar {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("splitByChar 函数参数不足"));
      }
      return String.format("splitByChar(%s,%s)", args[0], args[1]);
    }
  }, notEmpty {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("notEmpty 函数参数不足"));
      }
      return String.format("notEmpty(%s)", args[0]);
    }
  }, CAST {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("CAST 函数参数不足"));
      }
      return String.format("CAST(%s AS %s)", args[0], args[1]);
    }
  }, any {
    @Override
    public String build(String... args) {
      if (args.length < 1) {
        throw new RuntimeException(new ArgsException("any 函数参数不足"));
      }
      return String.format("any(%s)", args[0]);
    }
  }, argMaxIf {
    @Override
    public String build(String... args) {
      if (args.length < 3) {
        throw new RuntimeException(new ArgsException("argMaxIf 函数参数不足"));
      }
      return String.format("argMaxIf(%s,%s,%s)", args[0],args[1],args[2]);
    }
  }, argMax {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("argMax 函数参数不足"));
      }
      return String.format("argMax(%s,%s)", args[0],args[1]);
    }
  },anyIf {
    @Override
    public String build(String... args) {
      if (args.length < 2) {
        throw new RuntimeException(new ArgsException("anyIf 函数参数不足"));
      }
      return String.format("anyIf(%s,%s)", args[0],args[1]);
    }
  };

  public abstract String build(String... args);
}
