package com.fxiaoke.bi.warehouse.common.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.Objects;

/**
 * @Author: zhaomh
 * @Description:
 * @Date: Created in 2024/4/11
 * @Modified By:
 */
@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TopologyTableAggDownStream {
  private List<DownStreamInfo> downInfoList;
  private Set<String> upTables;

  @Data
  public static class DownStreamInfo {
    private String downViewId;
    private List<DownStreamField> downFieldList;

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      DownStreamInfo that = (DownStreamInfo) o;
      return downViewId.equals(that.downViewId);
    }

    @Override
    public int hashCode() {
      return Objects.hash(downViewId);
    }
  }

  @Data
  public static class DownStreamField {
    private String downFieldId; //下游指标field_id
    private String downCHColumnType; //下游指标列类型CH
    private String upFieldId; //上游槽位field_id
    private String upDdFieldName; //上游槽位列
  }
}
