package com.fxiaoke.bi.warehouse.common.util;

import com.google.common.collect.Sets;

import java.util.Set;

public interface FieldType {

  String SELECT_ONE = "select_one";
  String CURRENCY = "currency";
  String DATE = "date";
  String DATE_TIME = "date_time";
  String EMAIL = "email";
  String EMBEDDED_OBJECT = "embedded_object";
  String FILE_ATTACHMENT = "file_attachment";
  String IMAGE = "image";
  String LONG_TEXT = "long_text";
  String NUMBER = "number";
  String OBJECT_REFERENCE = "object_reference";
  String PERCENTILE = "percentile";
  String PHONE_NUMBER = "phone_number";
  String SELECT_MANY = "select_many";
  String TEXT = "text";
  String TIME = "time";
  String TRUE_OR_FALSE = "true_or_false";
  String URL = "url";
  String TAG = "tag";
  String EMBEDDED_OBJECT_LIST = "embedded_object_list";
  String FORMULA = "formula";
  String ARRAY = "array";
  String COMPOSITE_ARRAY = "composite_array";
  String AUTO_NUMBER = "auto_number";
  String EMPLOYEE = "employee";
  String OUT_EMPLOYEE = "out_employee";
  String EMPLOYEE_MANY = "employee_many";
  String DEPARTMENT = "department";
  String DEPARTMENT_MANY = "department_many";
  String LOCATION = "location";
  String MULTI_LEVEL_SELECT_ONE = "multi_level_select_one";
  String RECORD_TYPE = "record_type";
  String SUMMARY = "summary";
  String COUNTRY = "country";
  String PROVINCE = "province";
  String CITY = "city";
  String DISTRICT = "district";
  String TOWN = "town";
  String QUOTE = "quote";
  String GROUP = "group";
  String COUNT = "count"; //统计字段
  //String AREA = "area"; // 地区组件
  String SIGNATURE = "signature";  //签字
  String MASTER_DETAIL = "master_detail";  //主从字段
  String LOCK_RULE = "lock_rule";  //锁定规则
  String UseScope = "use_scope"; //对象使用范围
  String UseRange = "use_range"; //对象使用范围2  兼容之前的
  String RICH_TEXT = "rich_text";
  String HTML_RICH_TEXT = "html_rich_text";
  String GEO_POINT = "geo_point";
  String TREE_PATH = "tree_path"; //树形
  String WHAT_LIST_DATA = "what_list_data";
  // 维度类型字段
  String DIMENSION = "dimension";
  // 查找关联支持多选
  String OBJECT_REFERENCE_MANY = "object_reference_many";
  //判定组件value1的存储格式
  String UI_SELECTION = "UI_Selection";

  Set<String> NUMBER_FIELD_TYPES = Sets.newHashSet(NUMBER, CURRENCY, COUNT, PERCENTILE, DATE, DATE_TIME, TIME);

  Set<String> EMP_DEPT = Sets.newHashSet(EMPLOYEE, DEPARTMENT);
  Set<Integer> IN_NOT_IN = Sets.newHashSet(26, 27);

  Set<String> MULTI_VALUE_FIELD_TYPES = Sets.newHashSet(SELECT_MANY, TAG, EMPLOYEE_MANY, ARRAY, DEPARTMENT_MANY, OBJECT_REFERENCE_MANY);
}
