package com.fxiaoke.bi.warehouse.common.mq.message;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.rocketmq.common.message.MessageExt;

/**
 * @Author:jief
 * @Date:2023/5/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ViewChangeMessage {
  private String tenantId;//租户id
  private String sourceId;//图id
  private Integer sourceType; //'数据源类型：0表示统计图，1表示新目标，2老目标，3表示报表，-1表示所有'
  private String policyId;//1+N 策略id

  public static ViewChangeMessage parseFromMsg(MessageExt messageExt) {
    String json = new String(messageExt.getBody(), Charsets.UTF_8);
    return JSON.parseObject(json, ViewChangeMessage.class);
  }
}
